#!/usr/bin/env python3
# SPDX-FileCopyrightText: 2021-2025 <PERSON>tine+ Contributors
# SPDX-License-Identifier: GPL-3.0-or-later

import sys

def load_module():

    try:
        import pynicotine

    except ImportError:
        print("""Cannot find the pynicotine module.
Perhaps it is installed in a folder which is not in Python's module search
path. There could be a version mismatch between the Python version that was
used to build the application binary package and the version you try to run
the application with.""")
        return 1

    return pynicotine.run()


if __name__ == "__main__":
    sys.exit(load_module())
