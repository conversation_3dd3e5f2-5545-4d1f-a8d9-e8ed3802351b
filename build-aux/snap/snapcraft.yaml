# SPDX-FileCopyrightText: 2023-2025 <PERSON><PERSON>+ Contributors
# SPDX-License-Identifier: GPL-3.0-or-later

name: nicotine-plus
adopt-info: nicotine-plus
license: GPL-3.0-or-later

base: core24
confinement: strict
compression: lzo
grade: stable

plugs:
  # Required for Python's multiprocessing module
  shared-memory:
    private: true

apps:
  nicotine-plus:
    command: bin/nicotine
    common-id: org.nicotine_plus.Nicotine
    desktop: share/applications/org.nicotine_plus.Nicotine.desktop
    extensions: [gnome]
    plugs:
      - desktop-legacy
      - home
      - network
      - network-bind
      - removable-media
      - unity7

parts:
  nicotine-plus:
    plugin: python
    source: .
    python-packages: []
    parse-info: [share/metainfo/org.nicotine_plus.Nicotine.appdata.xml]
    override-pull: |
      craftctl default
      sed -E 's|Icon=.*|Icon=snap.$CRAFT_PROJECT_NAME.org.nicotine_plus.Nicotine|' -i data/org.nicotine_plus.Nicotine.desktop.in
    override-build: |
      craftctl default
      mkdir -p $CRAFT_PART_INSTALL/meta/gui/
      cp -r $CRAFT_PART_INSTALL/share/icons $CRAFT_PART_INSTALL/meta/gui/
      for i in `find $CRAFT_PART_INSTALL/meta/gui/icons -name "*.svg" -o -name "*.png"`; do
        mv $i "`dirname $i`/snap.$CRAFT_PROJECT_NAME.`basename $i`"
      done

slots:
  nicotine-plus:
    interface: dbus
    bus: session
    name: org.nicotine_plus.Nicotine
