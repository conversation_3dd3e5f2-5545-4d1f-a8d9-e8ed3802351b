{"id": "org.nicotine_plus.Nicotine", "runtime": "org.gnome.Platform", "runtime-version": "48", "sdk": "org.gnome.Sdk", "command": "nicotine", "separate-locales": false, "finish-args": ["--socket=wayland", "--socket=fallback-x11", "--share=ipc", "--share=network", "--device=dri", "--filesystem=home", "--filesystem=xdg-run/gvfs", "--filesystem=/media", "--filesystem=/mnt", "--filesystem=/run/media", "--talk-name=org.a11y.Bus", "--talk-name=org.kde.StatusNotifierWatcher", "--talk-name=org.mpris.MediaPlayer2.*"], "modules": [{"name": "nicotine-plus", "buildsystem": "simple", "build-commands": ["pip3 install --no-build-isolation --no-deps --prefix=${FLATPAK_DEST} ."], "sources": [{"path": "../../", "type": "dir"}], "run-tests": true, "test-commands": ["python3 -m unittest -v"]}], "build-options": {"env": {}}}