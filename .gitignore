# SPDX-FileCopyrightText: 2016-2025 <PERSON><PERSON>+ Contributors
# SPDX-License-Identifier: GPL-3.0-or-later

# Auto-generated files
*.desktop
*.appdata.xml

# Python byte code files
*.pyc

# Pot backup files
*.po~

# Translation Machine Object files
*.mo
pynicotine/locale/

# Temporary test files
pynicotine/tests/**/*temp_config*
pynicotine/tests/**/*.db
pynicotine/tests/**/*.dbn
pynicotine/tests/**/*.json
pynicotine/tests/**/*.old
pynicotine/tests/**/*.wav

# Ignore venv files
venv/*
.env/

# Ignore packaging directories and files
*.json~
install.txt
MANIFEST
.flatpak-builder/
build/
dist/
debian/files
nicotine_plus.egg-info/
build-aux/flatpak/build-dir/
build-aux/flatpak/.flatpak-builder/
build-aux/macos/build/
build-aux/windows/build/
