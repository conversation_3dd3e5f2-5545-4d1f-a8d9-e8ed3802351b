# SPDX-FileCopyrightText: 2023-2025 <PERSON><PERSON>+ Contributors
# SPDX-License-Identifier: GPL-3.0-or-later

[metadata]
name = nicotine-plus
version = attr: pynicotine.__version__
license = GPLv3+
description = Graphical client for the Soulseek peer-to-peer network
long_description =
    <PERSON><PERSON>+ is a graphical client for the Soulseek peer-to-peer
    network.

    Nicotine+ aims to be a lightweight, pleasant, free and open
    source (FOSS) alternative to the official Soulseek client, while
    also providing a comprehensive set of features.
author = <PERSON><PERSON>+ Team
author_email = <EMAIL>
url = https://nicotine-plus.org
classifiers =
    Development Status :: 5 - Production/Stable
    Environment :: X11 Applications :: GTK
    Intended Audience :: End Users/Desktop
    License :: OSI Approved :: GNU General Public License v3 or later (GPLv3+)
    Operating System :: OS Independent
    Programming Language :: Python
    Topic :: Communications :: Chat
    Topic :: Communications :: File Sharing
    Topic :: Internet
    Topic :: System :: Networking

[options]
scripts = nicotine
packages = find:
python_requires = >=3.9
install_requires = PyGObject>=3.38

[options.packages.find]
include = pynicotine*
exclude = pynicotine.plugins.examplars*, pynicotine.tests*

[options.package_data]
* = *.css, *.csv, *.md, *.mo, *.svg, *.ui, PLUGININFO

[options.data_files]
share/applications = data/org.nicotine_plus.Nicotine.desktop
share/metainfo = data/org.nicotine_plus.Nicotine.appdata.xml
share/man/man1 = data/nicotine.1
share/icons/hicolor/16x16/apps = pynicotine/gtkgui/icons/hicolor/16x16/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/16x16@2/apps = pynicotine/gtkgui/icons/hicolor/16x16@2/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/24x24/apps = pynicotine/gtkgui/icons/hicolor/24x24/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/24x24@2/apps = pynicotine/gtkgui/icons/hicolor/24x24@2/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/32x32/apps = pynicotine/gtkgui/icons/hicolor/32x32/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/32x32@2/apps = pynicotine/gtkgui/icons/hicolor/32x32@2/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/48x48/apps = pynicotine/gtkgui/icons/hicolor/48x48/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/48x48@2/apps = pynicotine/gtkgui/icons/hicolor/48x48@2/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/64x64/apps = pynicotine/gtkgui/icons/hicolor/64x64/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/64x64@2/apps = pynicotine/gtkgui/icons/hicolor/64x64@2/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/128x128/apps = pynicotine/gtkgui/icons/hicolor/128x128/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/128x128@2/apps = pynicotine/gtkgui/icons/hicolor/128x128@2/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/256x256/apps = pynicotine/gtkgui/icons/hicolor/256x256/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/256x256@2/apps = pynicotine/gtkgui/icons/hicolor/256x256@2/apps/org.nicotine_plus.Nicotine.png
share/icons/hicolor/symbolic/apps = pynicotine/gtkgui/icons/hicolor/symbolic/apps/org.nicotine_plus.Nicotine-symbolic.svg
share/icons/hicolor/scalable/apps =
    pynicotine/gtkgui/icons/hicolor/scalable/apps/org.nicotine_plus.Nicotine.svg
    pynicotine/gtkgui/icons/hicolor/scalable/apps/org.nicotine_plus.Nicotine-away.svg
    pynicotine/gtkgui/icons/hicolor/scalable/apps/org.nicotine_plus.Nicotine-connect.svg
    pynicotine/gtkgui/icons/hicolor/scalable/apps/org.nicotine_plus.Nicotine-disconnect.svg
    pynicotine/gtkgui/icons/hicolor/scalable/apps/org.nicotine_plus.Nicotine-msg.svg

[options.extras_require]
packaging = cx_Freeze
tests = pycodestyle; pylint

[pycodestyle]
max-line-length = 120
exclude = build/, venv/

[pylint]
disable =
    broad-except,
    consider-using-assignment-expr,
    cyclic-import,
    duplicate-code,
    fixme,
    import-outside-toplevel,
    missing-class-docstring,
    missing-function-docstring,
    missing-module-docstring,
    no-value-for-parameter,
    superfluous-parens
enable =
    consider-using-augmented-assign,
    use-implicit-booleaness-not-comparison-to-string,
    use-implicit-booleaness-not-comparison-to-zero
ignore-paths =
    build,
    pynicotine/locale,
    venv
load-plugins =
    pylint.extensions.bad_builtin,
    pylint.extensions.check_elif,
    pylint.extensions.code_style,
    pylint.extensions.comparison_placement,
    pylint.extensions.consider_refactoring_into_while_condition,
    pylint.extensions.for_any_all,
    pylint.extensions.dict_init_mutate,
    pylint.extensions.dunder,
    pylint.extensions.eq_without_hash,
    pylint.extensions.overlapping_exceptions,
    pylint.extensions.private_import,
    pylint.extensions.set_membership,
    pylint.extensions.typing
py-version = 3.9

[pylint.design]
max-args = 18
max-attributes = 125
max-bool-expr = 6
max-branches = 40
max-locals = 40
max-positional-arguments = 15
max-public-methods = 85
max-returns = 12
max-statements = 125
min-public-methods = 0

[pylint.format]
max-line-length = 120
max-module-lines = 4500

[pylint.refactoring]
max-nested-blocks = 6

[pylint.similarities]
min-similarity-lines = 15

[pylint.variables]
additional-builtins = _
