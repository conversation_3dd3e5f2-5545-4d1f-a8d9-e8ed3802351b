# SPDX-FileCopyrightText: 2021-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2024-08-07 19:33+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Turkish <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/tr/>\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.7-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr "Soulseek İstemcisi"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Soulseek eşler arası ağı için grafiksel istemci"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr ""
"Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;paylaşım,"
"sohbet;mesajlaşma;eşler-arası;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
msgid "Browse the Soulseek network"
msgstr "Soulseek ağına göz atın"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+, Soulseek eşler arası ağı için grafiksel bir istemcidir."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+, resmi Soulseek istemcisine hafif, kullanımı kolay, özgür ve açık "
"kaynaklı (FOSS) bir alternatif olmayı ve aynı zamanda kapsamlı bir dizi "
"özellik sunmayı amaçlamaktadır."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
msgid "Search Files"
msgstr "Dosyaları Ara"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "İndirilenler"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
msgid "Browse Shares"
msgstr "Paylaşımlara Göz At"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
msgid "Private Chat"
msgstr "Özel Sohbet"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Nicotine+ Ekibi"

#: pynicotine/__init__.py:50
#, python-format
msgid "Website: %s"
msgstr "Web sitesi: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "bu yardım mesajını göster ve çık"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "dosya"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr "öntanımlı olmayan yapılandırma dosyası kullan"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "dizin"

#: pynicotine/__init__.py:64
msgid "alternative directory for user data and plugins"
msgstr "kullanıcı verileri ve eklentiler için diğer dizin"

#: pynicotine/__init__.py:68
msgid "start the program without showing window"
msgstr "programı pencereyi göstermeden başlat"

#: pynicotine/__init__.py:71
msgid "ip"
msgstr "IP"

#: pynicotine/__init__.py:72
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "soketleri verilen IP'ye bağla (VPN için kullanışlıdır)"

#: pynicotine/__init__.py:75
msgid "port"
msgstr "bağlantı noktası"

#: pynicotine/__init__.py:76
msgid "listen on the given port"
msgstr "verilen bağlantı noktasında dinle"

#: pynicotine/__init__.py:80
msgid "rescan shared files"
msgstr "paylaşılan dosyaları yeniden tara"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr "programı başsız modda başlat (grafiksel kullanıcı arayüzü olmadan)"

#: pynicotine/__init__.py:88
msgid "display version and exit"
msgstr "sürümü göster ve çık"

#: pynicotine/__init__.py:121
#, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Desteklenmeyen bir Python (%(old_version)s) sürümü kullanıyorsunuz.\n"
"Python %(min_version)s veya daha yenisini kurmalısınız."

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Paylaşımlar taranamadı. Lütfen diğer Nicotine+ örneklerini kapatın ve tekrar "
"deneyin."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, python-format
msgid "%(user)s is away"
msgstr "%(user)s kullanıcısı uzakta"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, python-format
msgid "%(user)s is online"
msgstr "%(user)s kullanıcısı çevrim içi"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, python-format
msgid "%(user)s is offline"
msgstr "%(user)s kullanıcısı çevrim dışı"

#: pynicotine/buddies.py:316
msgid "Buddy Status"
msgstr "Arkadaş Durumu"

#: pynicotine/chatrooms.py:383
#, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Özel bir odaya eklendiniz: %(room)s"

#: pynicotine/chatrooms.py:478
#, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr ""
"'%(room)s' odasındaki '%(user)s' kullanıcısından sohbet mesajı: %(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr "'%(path)s' dizini oluşturulamıyor, hata bildirildi: %(error)s"

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr "Yapılandırma yedeklenirken hata oluştu: %s"

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr "Yapılandırma yedeklendi: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, python-format
msgid "Loading %(program)s %(version)s"
msgstr "%(program)s %(version)s yükleniyor"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Çıkılıyor: %(program)s %(version)s, %(status)s…"

#: pynicotine/core.py:246
msgid "terminating"
msgstr "sonlandırılıyor"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "uygulama kapatılıyor"

#: pynicotine/core.py:259
#, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "%(program)s %(version)s'tan çık!"

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr "Bağlanmadan önce bir kullanıcı adı ve parola belirtmeniz gerekir…"

#: pynicotine/downloads.py:239
#, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""
"Hata: İndirme filtresi başarısız oldu! Filtrelerinizi doğrulayın. Neden: %s"

#: pynicotine/downloads.py:254
#, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Hata: %(num)d indirme filtresi başarısız oldu! %(error)s "

#: pynicotine/downloads.py:366
#, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s, %(user)s kullanıcısından indirildi"

#: pynicotine/downloads.py:370
msgid "File Downloaded"
msgstr "Dosya İndirildi"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Çalıştırıldı: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "'%(command)s' çalıştırılamadı: %(error)s"

#: pynicotine/downloads.py:407
#, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s, %(user)s kullanıcısından indirildi"

#: pynicotine/downloads.py:411
msgid "Folder Downloaded"
msgstr "Klasör İndirildi"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Klasörde çalıştırıldı: %s"

#: pynicotine/downloads.py:443
#, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "'%(tempfile)s', '%(file)s' konumuna taşınamadı: %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
msgid "Download Folder Error"
msgstr "İndirme Klasörü Hatası"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "İndirme tamamlandı: %(user)s kullanıcısı, %(file)s dosyası"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "İndirme iptal edildi: %(user)s kullanıcısı, %(file)s dosyası"

#: pynicotine/downloads.py:1150
#, python-format
msgid "Download I/O error: %s"
msgstr "İndirme G/Ç hatası: %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Dosya üzerinde özel kilit alınamıyor - G/Ç hatası: %s"

#: pynicotine/downloads.py:1202
#, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Dosya %(folder_path)s içine kaydedilemiyor: %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "İndirme başladı: %(user)s kullanıcısı, %(file)s dosyası"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, python-format
msgid "Cannot find %s, please install it."
msgstr "%s bulunamıyor, lütfen kurun."

#: pynicotine/gtkgui/__init__.py:162
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr ""
"Kullanılabilir grafiksel ortam yok, başsız (GUI olmayan) modu kullanılıyor"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "_Bağlan"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "Bağlantıyı _Kes"

#: pynicotine/gtkgui/application.py:308
msgid "Soulseek _Privileges"
msgstr "Soulseek _Ayrıcalıkları"

#: pynicotine/gtkgui/application.py:314
msgid "_Preferences"
msgstr "_Tercihler"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
msgid "_Quit"
msgstr "_Çıkış"

#: pynicotine/gtkgui/application.py:337
msgid "Browse _Public Shares"
msgstr "_Herkese Açık Paylaşımlara Göz At"

#: pynicotine/gtkgui/application.py:338
msgid "Browse _Buddy Shares"
msgstr "_Arkadaş Paylaşımlarına Göz At"

#: pynicotine/gtkgui/application.py:339
msgid "Browse _Trusted Shares"
msgstr "_Güvenilir Paylaşımlara Göz At"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
msgid "_Rescan Shares"
msgstr "Paylaşımları Yeniden _Tara"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
msgid "Configure _Shares"
msgstr "_Paylaşımları Yapılandır"

#: pynicotine/gtkgui/application.py:371
msgid "_Keyboard Shortcuts"
msgstr "_Klavye Kısayolları"

#: pynicotine/gtkgui/application.py:372
msgid "_Setup Assistant"
msgstr "K_urulum Yardımcısı"

#: pynicotine/gtkgui/application.py:373
msgid "_Transfer Statistics"
msgstr "Aktarım İ_statistikleri"

#: pynicotine/gtkgui/application.py:378
msgid "Report a _Bug"
msgstr "_Hata Bildir"

#: pynicotine/gtkgui/application.py:379
msgid "Improve T_ranslations"
msgstr "Çe_virileri İyileştir"

#: pynicotine/gtkgui/application.py:383
msgid "_About Nicotine+"
msgstr "_Nicotine+ Hakkında"

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "_Dosya"

#: pynicotine/gtkgui/application.py:395
msgid "_Shares"
msgstr "_Paylaşımlar"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
msgid "_Help"
msgstr "_Yardım"

#: pynicotine/gtkgui/application.py:410
msgid "_Browse Shares"
msgstr "Paylaşımlara _Göz At"

#: pynicotine/gtkgui/application.py:465
#, python-format
msgid "Unable to show notification: %s"
msgstr "Bildirim gösterilemiyor: %s"

#: pynicotine/gtkgui/application.py:526
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Hala dosya yüklüyorsunuz. Gerçekten çıkmak istiyor musunuz?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr "Yüklemelerin bitmesini bekle"

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr "Gerçekten çıkmak istiyor musunuz?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
msgid "_No"
msgstr "_Hayır"

#: pynicotine/gtkgui/application.py:535
msgid "_Run in Background"
msgstr "_Arka Planda Çalıştır"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr "Nicotine+'tan çık"

#: pynicotine/gtkgui/application.py:561
msgid "Shares Not Available"
msgstr "Paylaşımlar Kullanılabilir Değil"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Harici disklerin başlandığını ve klasör izinlerinin doğru olduğunu "
"doğrulayın."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
msgid "_Cancel"
msgstr "_İptal"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "_Yeniden Dene"

#: pynicotine/gtkgui/application.py:567
msgid "_Force Rescan"
msgstr "Yeniden Taramayı _Zorla"

#: pynicotine/gtkgui/application.py:742
msgid "Message Downloading Users"
msgstr "İndiren Kullanıcılara Mesaj Gönder"

#: pynicotine/gtkgui/application.py:743
msgid "Send private message to all users who are downloading from you:"
msgstr "Sizden indirme yapan tüm kullanıcılara özel mesaj gönderin:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
msgid "_Send Message"
msgstr "Mesaj _Gönder"

#: pynicotine/gtkgui/application.py:756
msgid "Message Buddies"
msgstr "Mesaj Arkadaşları"

#: pynicotine/gtkgui/application.py:757
msgid "Send private message to all online buddies:"
msgstr "Tüm çevrim içi arkadaşlara özel mesaj gönderin:"

#: pynicotine/gtkgui/application.py:786
msgid "Select a Saved Shares List File"
msgstr "Kaydedilen Paylaşımlar Listesi Dosyası Seç"

#: pynicotine/gtkgui/application.py:877
msgid "Critical Error"
msgstr "Kritik Hata"

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ kritik bir hatayla karşılaştı ve çıkması gerekiyor. Lütfen "
"aşağıdaki mesajı kopyalayın ve hata bildirimine ekleyin:"

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr "Nicotine+'tan _çık"

#: pynicotine/gtkgui/application.py:883
msgid "_Copy & Report Bug"
msgstr "_Kopyala ve Hatayı Bildir"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Durum"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr "Ülke"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Kullanıcı"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Hız"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Dosyalar"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted"
msgstr "Güvenilir"

#: pynicotine/gtkgui/buddies.py:108
msgid "Notify"
msgstr "Bildir"

#: pynicotine/gtkgui/buddies.py:114
msgid "Prioritized"
msgstr "Öncelikli"

#: pynicotine/gtkgui/buddies.py:120
msgid "Last Seen"
msgstr "Son Görüldü"

#: pynicotine/gtkgui/buddies.py:126
msgid "Note"
msgstr "Not"

#: pynicotine/gtkgui/buddies.py:143
msgid "Add User _Note…"
msgstr "Kullanıcı _Notu Ekle…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Kaldır"

#: pynicotine/gtkgui/buddies.py:364
msgid "Never seen"
msgstr "Hiç görülmedi"

#: pynicotine/gtkgui/buddies.py:529
msgid "Add User Note"
msgstr "Kullanıcı Notu Ekle"

#: pynicotine/gtkgui/buddies.py:530
#, python-format
msgid "Add a note about user %s:"
msgstr "%s kullanıcısı hakkında bir not ekleyin:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
msgid "_Add"
msgstr "_Ekle"

#: pynicotine/gtkgui/chatrooms.py:224
msgid "Create New Room?"
msgstr "Yeni Oda Oluşturulsun mu?"

#: pynicotine/gtkgui/chatrooms.py:225
#, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "\"%s\" adlı yeni bir oda oluşturmak istediğinizden emin misiniz?"

#: pynicotine/gtkgui/chatrooms.py:226
msgid "Make room private"
msgstr "Odayı özel yap"

#: pynicotine/gtkgui/chatrooms.py:515
msgid "Search activity log…"
msgstr "Etkinlik günlüğünde ara…"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
msgid "Search chat log…"
msgstr "Sohbet günlüğünde ara…"

#: pynicotine/gtkgui/chatrooms.py:597
msgid "Sear_ch User's Files"
msgstr "Kullanıcının Dosyalarını _Ara"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
msgid "Find…"
msgstr "Bul…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Kopyala"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr "Tümünü Kopyala"

#: pynicotine/gtkgui/chatrooms.py:608
msgid "Clear Activity View"
msgstr "Etkinlik Görünümünü Temizle"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
msgid "_Leave Room"
msgstr "Odadan _Ayrıl"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
msgid "Copy Link"
msgstr "Bağlantıyı Kopyala"

#: pynicotine/gtkgui/chatrooms.py:624
msgid "View Room Log"
msgstr "Oda Günlüğünü Görüntüle"

#: pynicotine/gtkgui/chatrooms.py:627
msgid "Delete Room Log…"
msgstr "Oda Günlüğünü Sil…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
msgid "Clear Message View"
msgstr "Mesaj Görünümünü Temizle"

#: pynicotine/gtkgui/chatrooms.py:832
#, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s, %(room)s odasında sizden bahsetti"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "%(room)s odasında %(user)s tarafından bahsedildi"

#: pynicotine/gtkgui/chatrooms.py:855
#, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "%(room)s odasında %(user)s tarafından mesaj"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s odaya katıldı"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s odadan ayrıldı"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s uzaklaştı"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s geri döndü"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
msgid "Delete Logged Messages?"
msgstr "Kayıtlı Mesajlar Silinsin mi?"

#: pynicotine/gtkgui/chatrooms.py:1197
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"Bu oda için kayıtlı tüm mesajları kalıcı olarak silmek istediğinizden emin "
"misiniz?"

#: pynicotine/gtkgui/dialogs/about.py:405
msgid "About"
msgstr "Hakkında"

#: pynicotine/gtkgui/dialogs/about.py:415
msgid "Website"
msgstr "Web sitesi"

#: pynicotine/gtkgui/dialogs/about.py:457
#, python-format
msgid "Error checking latest version: %s"
msgstr "En son sürüm denetlenirken hata oluştu: %s"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr "Yeni sürüm var: %s"

#: pynicotine/gtkgui/dialogs/about.py:467
msgid "Up to date"
msgstr "Güncel"

#: pynicotine/gtkgui/dialogs/about.py:496
msgid "Checking latest version…"
msgstr "En son sürüm denetleniyor…"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
msgid "Setup Assistant"
msgstr "Kurulum Yardımcısı"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
msgid "Virtual Folder"
msgstr "Sanal Klasör"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
msgid "Folder"
msgstr "Klasör"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
msgid "_Previous"
msgstr "_Önceki"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
msgid "_Finish"
msgstr "_Bitir"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
msgid "_Next"
msgstr "_Sonraki"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
msgid "Add a Shared Folder"
msgstr "Paylaşılan Klasör Ekle"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
msgid "Edit Shared Folder"
msgstr "Paylaşılan Klasörü Düzenle"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "'%(dir)s' için yeni sanal ad girin:"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
msgid "_Edit"
msgstr "_Düzenle"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"%s kullanıcısı zaten var ve girdiğiniz parola geçersiz. İlk kez oturum "
"açıyorsanız lütfen başka bir kullanıcı adı seçin."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
msgid "File Properties"
msgstr "Dosya Özellikleri"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "Dosya Özellikleri (%(num)i / %(total)i  /  %(size)s  /  %(length)s)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Dosya Özellikleri (%(num)i / %(total)i  /  %(size)s)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
msgid "_Apply"
msgstr "_Uygula"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
msgid "Add…"
msgstr "Ekle…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
msgid "Edit…"
msgstr "Düzenle…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, python-format
msgid "%s Settings"
msgstr "%s Ayarları"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
msgid "Add Item"
msgstr "Öge Ekle"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
msgid "Edit Item"
msgstr "Ögeyi Düzenle"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "Bilinmeyen"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
msgid "Check Port Status"
msgstr "Bağlantı Noktası Durumunu Denetle"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, bağlantı noktası %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
msgid "Password Change Rejected"
msgstr "Parola Değişikliği Reddedildi"

#: pynicotine/gtkgui/dialogs/preferences.py:218
msgid "Enter a new password for your Soulseek account:"
msgstr "Soulseek hesabınız için yeni bir parola girin:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"Şu anda Soulseek ağından çıkış yaptınız. Mevcut bir Soulseek hesabının "
"parolasını değiştirmeye çalışıyorsanız, söz konusu hesapta oturum açmanız "
"gerekir."

#: pynicotine/gtkgui/dialogs/preferences.py:223
msgid "Enter password to use when logging in:"
msgstr "Oturum açarken kullanılacak parolayı girin:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
msgid "Change Password"
msgstr "Parola Değiştir"

#: pynicotine/gtkgui/dialogs/preferences.py:230
msgid "_Change"
msgstr "_Değiştir"

#: pynicotine/gtkgui/dialogs/preferences.py:274
msgid "No one"
msgstr "Hiç kimse"

#: pynicotine/gtkgui/dialogs/preferences.py:275
msgid "Everyone"
msgstr "Herkes"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Arkadaşlar"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted buddies"
msgstr "Güvenilir Arkadaşlar"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
msgid "Nothing"
msgstr "Hiçbir Şey Yapma"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
msgid "Open File"
msgstr "Dosya Aç"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
msgid "Open in File Manager"
msgstr "Dosya Yöneticisinde Aç"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Arama"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
msgid "Pause"
msgstr "Duraklat"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
msgid "Resume"
msgstr "Devam Et"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
msgid "Browse Folder"
msgstr "Klasöre Göz At"

#: pynicotine/gtkgui/dialogs/preferences.py:317
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Söz dizimi</b>: Büyük/küçük harfe duyarlı değildir. Etkinleştirilirse "
"Python düzenli ifadeleri kullanılabilir, aksi takdirde yalnızca joker "
"karakter * eşleşmeleri desteklenir."

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr "Filtre"

#: pynicotine/gtkgui/dialogs/preferences.py:334
msgid "Regex"
msgstr "Düzenli ifade"

#: pynicotine/gtkgui/dialogs/preferences.py:468
msgid "Add Download Filter"
msgstr "İndirme Filtresi Ekle"

#: pynicotine/gtkgui/dialogs/preferences.py:469
msgid "Enter a new download filter:"
msgstr "Yeni bir indirme filtresi girin:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
msgid "Enable regular expressions"
msgstr "Düzenli ifadeleri etkinleştir"

#: pynicotine/gtkgui/dialogs/preferences.py:498
msgid "Edit Download Filter"
msgstr "İndirme Filtresini Düzenle"

#: pynicotine/gtkgui/dialogs/preferences.py:499
msgid "Modify the following download filter:"
msgstr "Aşağıdaki indirme filtresini değiştirin:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d Başarısız! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
msgid "Filters Successful"
msgstr "Filtreler Başarılı"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr "Herkese Açık"

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr "Erişilebilir"

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "İptal"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
msgid "Retry"
msgstr "Yeniden Dene"

#: pynicotine/gtkgui/dialogs/preferences.py:828
msgid "Round Robin"
msgstr "Herkes Sırayla"

#: pynicotine/gtkgui/dialogs/preferences.py:829
msgid "First In, First Out"
msgstr "İlk Giren İlk Çıkar"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
msgid "Username"
msgstr "Kullanıcı Adı"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
msgid "IP Address"
msgstr "IP Adresi"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
msgid "Ignore User"
msgstr "Kullanıcıyı Yok Say"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
msgid "Enter the name of the user you want to ignore:"
msgstr "Yok saymak istediğiniz kullanıcının adını girin:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
msgid "Ignore IP Address"
msgstr "IP Adresini Yok Say"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
msgid "Enter an IP address you want to ignore:"
msgstr "Yok saymak istediğiniz bir IP adresi girin:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "* is a wildcard"
msgstr "* bir joker karakterdir"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
msgid "Ban User"
msgstr "Kullanıcıyı Yasakla"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
msgid "Enter the name of the user you want to ban:"
msgstr "Yasaklamak istediğiniz kullanıcının adını girin:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
msgid "Ban IP Address"
msgstr "IP Adresini Yasakla"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "Enter an IP address you want to ban:"
msgstr "Engellemek istediğiniz bir IP adresi girin:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr "Biçim kodları"

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
msgid "Pattern"
msgstr "Kalıp"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
msgid "Replacement"
msgstr "Değişim"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
msgid "Censor Pattern"
msgstr "Sansür Kalıbı"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Sansürlemek istediğiniz bir kalıp girin. Sözcüklerin içindeki dizgeleri "
"eşleştirmek istemiyorsanız, kalıbın etrafına boşluk ekleyin (satırların "
"başında ve sonunda başarısız olabilir)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
msgid "Edit Censored Pattern"
msgstr "Sansürlenen Kalıbı Düzenle"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
msgid "Add Replacement"
msgstr "Değişim Ekle"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
msgid "Enter a text pattern and what to replace it with:"
msgstr "Bir metin kalıbı ve ne ile değiştirileceğini girin:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
msgid "Edit Replacement"
msgstr "Değişimi Düzenle"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
msgid "System default"
msgstr "Sistem öntanımlı değeri"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
msgid "Show confirmation dialog"
msgstr "Onay iletişim kutusunu göster"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
msgid "Run in the background"
msgstr "Arka planda çalıştır"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
msgid "bold"
msgstr "kalın"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
msgid "italic"
msgstr "italik"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
msgid "underline"
msgstr "altı çizili"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
msgid "normal"
msgstr "normal"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
msgid "Separate Buddies tab"
msgstr "Arkadaşlar sekmesini ayır"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
msgid "Sidebar in Chat Rooms tab"
msgstr "Sohbet Odaları sekmesinde kenar çubuğu"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr "Her zaman görünür kenar çubuğu"

#: pynicotine/gtkgui/dialogs/preferences.py:1777
msgid "Top"
msgstr "Üst"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
msgid "Bottom"
msgstr "Alt"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
msgid "Left"
msgstr "Sol"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
msgid "Right"
msgstr "Sağ"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "Çevrim içi"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Uzakta"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Çevrim dışı"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
msgid "Tab Changed"
msgstr "Sekme Değiştirildi"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
msgid "Tab Highlight"
msgstr "Sekme Vurgula"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
msgid "Window"
msgstr "Pencere"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
msgid "Online (Tray)"
msgstr "Çevrim içi (Tepsi)"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
msgid "Away (Tray)"
msgstr "Uzakta (Tepsi)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
msgid "Offline (Tray)"
msgstr "Çevrim dışı (Tepsi)"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
msgid "Message (Tray)"
msgstr "Mesaj (Tepsi)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Protokol"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
msgid "Command"
msgstr "Komut"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
msgid "Add URL Handler"
msgstr "URL İşleyicisi Ekle"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
msgid "Enter the protocol and the command for the URL handler:"
msgstr "URL işleyicisi için protokolü ve komutu girin:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
msgid "Edit Command"
msgstr "Komutu Düzenle"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, python-format
msgid "Enter a new command for protocol %s:"
msgstr "%s protokolü için yeni bir komut girin:"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
msgid "Username;APIKEY"
msgstr "Kullanıcı adı;APIKEY"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr ""
"Müzik çalar (örn. amarok, audacious, exaile); otomatik algılamak için boş "
"bırakın:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
msgid "Username: "
msgstr "Kullanıcı adı: "

#: pynicotine/gtkgui/dialogs/preferences.py:2767
msgid "Command:"
msgstr "Komut:"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
msgid "Title"
msgstr "Başlık"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Şimdi Oynatılıyor (genellikle \"%(artist)s - %(title)s\")"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
msgid "Artist"
msgstr "Sanatçı"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
msgid "Duration"
msgstr "Süre"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "Bit Hızı"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
msgid "Comment"
msgstr "Yorum"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
msgid "Album"
msgstr "Albüm"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
msgid "Track Number"
msgstr "Parça Numarası"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
msgid "Year"
msgstr "Yıl"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
msgid "Filename (URI)"
msgstr "Dosya Adı (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
msgid "Program"
msgstr "Program"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
msgid "Enabled"
msgstr "Etkin"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
msgid "Plugin"
msgstr "Eklenti"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
msgid "No Plugin Selected"
msgstr "Eklenti Seçilmedi"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
msgid "Preferences"
msgstr "Tercihler"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
msgid "Network"
msgstr "Ağ"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
msgid "User Interface"
msgstr "Kullanıcı Arayüzü"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Paylaşımlar"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Yüklenenler"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Aramalar"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
msgid "User Profile"
msgstr "Kullanıcı Profili"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
msgid "Chats"
msgstr "Sohbetler"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
msgid "Now Playing"
msgstr "Şimdi Oynatılıyor"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Günlük kaydı"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
msgid "Banned Users"
msgstr "Yasaklanan Kullanıcılar"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
msgid "Ignored Users"
msgstr "Yok Sayılan Kullanıcılar"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
msgid "URL Handlers"
msgstr "URL İşleyicileri"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr "Eklentiler"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
msgid "Pick a File Name for Config Backup"
msgstr "Yapılandırma Yedeklemesi için bir Dosya Adı Seç"

#: pynicotine/gtkgui/dialogs/statistics.py:75
msgid "Transfer Statistics"
msgstr "Aktarım İstatistikleri"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, python-format
msgid "Total Since %(date)s"
msgstr "%(date)s tarihinden beri toplam"

#: pynicotine/gtkgui/dialogs/statistics.py:123
msgid "Reset Transfer Statistics?"
msgstr "Aktarım İstatistikleri Sıfırlansın mı?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
msgid "Do you really want to reset transfer statistics?"
msgstr "Aktarım istatistiklerini gerçekten sıfırlamak istiyor musunuz?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
msgid "Wishlist"
msgstr "Dilek Listesi"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
msgid "Wish"
msgstr "Dilek"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
msgid "_Search for Item"
msgstr "Ögeyi _Ara"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
msgid "Edit Wish"
msgstr "Dileği Düzenle"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, python-format
msgid "Enter new value for wish '%s':"
msgstr "'%s' dileği için yeni bir değer girin:"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
msgid "Clear Wishlist?"
msgstr "Dilek Listesi Temizlensin mi?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
msgid "Do you really want to clear your wishlist?"
msgstr "Dilek listenizi gerçekten temizlemek istiyor musunuz?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Yol"

#: pynicotine/gtkgui/downloads.py:47
msgid "_Resume"
msgstr "_Devam et"

#: pynicotine/gtkgui/downloads.py:48
msgid "P_ause"
msgstr "D_uraklat"

#: pynicotine/gtkgui/downloads.py:72
msgid "Finished / Filtered"
msgstr "Tamamlandı / Filtrelendi"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Tamamlandı"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
msgid "Paused"
msgstr "Duraklatıldı"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
msgid "Filtered"
msgstr "Filtrelendi"

#: pynicotine/gtkgui/downloads.py:77
msgid "Deleted"
msgstr "Silindi"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
msgid "Queued…"
msgstr "Kuyruğa alındı…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
msgid "Everything…"
msgstr "Her şey…"

#: pynicotine/gtkgui/downloads.py:132
#, python-format
msgid "Downloads: %(speed)s"
msgstr "İndirilenler: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
msgid "Clear Queued Downloads"
msgstr "Kuyruğa Alınan İndirmeleri Temizle"

#: pynicotine/gtkgui/downloads.py:139
msgid "Do you really want to clear all queued downloads?"
msgstr "Kuyruğa alınan tüm indirmeleri temizlemek istediğinizden emin misiniz?"

#: pynicotine/gtkgui/downloads.py:151
msgid "Clear All Downloads"
msgstr "Tüm İndirmeleri Temizle"

#: pynicotine/gtkgui/downloads.py:152
msgid "Do you really want to clear all downloads?"
msgstr "Tüm indirmeleri temizlemek istediğinizden emin misiniz?"

#: pynicotine/gtkgui/downloads.py:169
#, python-format
msgid "Download %(num)i files?"
msgstr "%(num)i dosya indirilsin mi?"

#: pynicotine/gtkgui/downloads.py:170
#, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"%(user)s kullanıcısının %(folder)s klasöründen %(num)i dosyayı indirmek "
"istediğinizden emin misiniz?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
msgid "_Download Folder"
msgstr "_Klasörü İndir"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
msgid "Likes"
msgstr "Seviyor"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
msgid "Dislikes"
msgstr "Sevmiyor"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Değerlendirme"

#: pynicotine/gtkgui/interests.py:111
msgid "Item"
msgstr "Öge"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
msgid "_Recommendations for Item"
msgstr "Öge için _Tavsiyeler"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
msgid "I _Like This"
msgstr "Bunu _Seviyorum"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
msgid "I _Dislike This"
msgstr "Bunu Sev_miyorum"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
msgid "Recommendations"
msgstr "Tavsiyeler"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
msgid "Similar Users"
msgstr "Benzer Kullanıcılar"

#: pynicotine/gtkgui/interests.py:427
#, python-format
msgid "Recommendations (%s)"
msgstr "Tavsiyeler (%s)"

#: pynicotine/gtkgui/interests.py:451
#, python-format
msgid "Similar Users (%s)"
msgstr "Benzer Kullanıcılar (%s)"

#: pynicotine/gtkgui/mainwindow.py:238
msgid "Search log…"
msgstr "Günlükte ara…"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, python-format
msgid "Private Message from %(user)s"
msgstr "%(user)s Kullanıcısından Özel Mesaj"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
msgid "Wishlist Results Found"
msgstr "İstek Listesi Sonuçları Bulundu"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
msgid "User Profiles"
msgstr "Kullanıcı Profilleri"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
msgid "Chat Rooms"
msgstr "Sohbet Odaları"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "İlgi Alanları"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
msgid "Chat"
msgstr "Sohbet"

#: pynicotine/gtkgui/mainwindow.py:1111
msgid "[Debug] Connections"
msgstr "[Hata ayıklama] Bağlantılar"

#: pynicotine/gtkgui/mainwindow.py:1112
msgid "[Debug] Messages"
msgstr "[Hata ayıklama] Mesajlar"

#: pynicotine/gtkgui/mainwindow.py:1113
msgid "[Debug] Transfers"
msgstr "[Hata ayıklama] Aktarımlar"

#: pynicotine/gtkgui/mainwindow.py:1114
msgid "[Debug] Miscellaneous"
msgstr "[Hata ayıklama] Çeşitli"

#: pynicotine/gtkgui/mainwindow.py:1119
msgid "_Find…"
msgstr "_Bul…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
msgid "_Copy"
msgstr "_Kopyala"

#: pynicotine/gtkgui/mainwindow.py:1122
msgid "Copy _All"
msgstr "_Tümünü Kopyala"

#: pynicotine/gtkgui/mainwindow.py:1127
msgid "View _Debug Logs"
msgstr "_Hata Ayıklama Günlüklerini Görüntüle"

#: pynicotine/gtkgui/mainwindow.py:1128
msgid "View _Transfer Logs"
msgstr "_Aktarım Günlüklerini Görüntüle"

#: pynicotine/gtkgui/mainwindow.py:1132
msgid "_Log Categories"
msgstr "Gü_nlük Kategorileri"

#: pynicotine/gtkgui/mainwindow.py:1134
msgid "Clear Log View"
msgstr "Günlük Görünümünü Temizle"

#: pynicotine/gtkgui/mainwindow.py:1199
msgid "Preparing Shares"
msgstr "Paylaşımlar Hazırlanıyor"

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr "Paylaşımlar Taranıyor"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
msgid "Shared Folders"
msgstr "Paylaşılan Klasörler"

#: pynicotine/gtkgui/popovers/chathistory.py:77
msgid "Latest Message"
msgstr "Son Mesaj"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Oda"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Kullanıcılar"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
msgid "Join Room"
msgstr "Odaya Katıl"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
msgid "Leave Room"
msgstr "Odadan Ayrıl"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
msgid "Disown Private Room"
msgstr "Özel Odanın Sahipliğini Bırak"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
msgid "Cancel Room Membership"
msgstr "Oda Üyeliğini İptal Et"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
msgid "Close All Tabs…"
msgstr "Tüm Sekmeleri Kapat…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
msgid "_Close Tab"
msgstr "Sekmeyi _Kapat"

#: pynicotine/gtkgui/privatechat.py:379
msgid "View Chat Log"
msgstr "Sohbet Günlüğünü Görüntüle"

#: pynicotine/gtkgui/privatechat.py:382
msgid "Delete Chat Log…"
msgstr "Sohbet Günlüğünü Sil…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
msgid "User Actions"
msgstr "Kullanıcı Eylemleri"

#: pynicotine/gtkgui/privatechat.py:477
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Bu kullanıcı için kayıtlı tüm mesajları kalıcı olarak silmek istediğinizden "
"emin misiniz?"

#: pynicotine/gtkgui/privatechat.py:528
msgid "* Messages sent while you were offline"
msgstr "* Siz çevrim dışıyken gönderilen mesajlar"

#: pynicotine/gtkgui/search.py:90
msgid "_Global"
msgstr "_Genel"

#: pynicotine/gtkgui/search.py:91
msgid "_Buddies"
msgstr "_Arkadaşlar"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
msgid "_Rooms"
msgstr "_Odalar"

#: pynicotine/gtkgui/search.py:93
msgid "_User"
msgstr "_Kullanıcı"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
msgid "In Queue"
msgstr "Kuyrukta"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
msgid "File Type"
msgstr "Dosya Türü"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Dosya Adı"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Boyut"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
msgid "Quality"
msgstr "Kalite"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
msgid "Copy _File Path"
msgstr "_Dosya Yolunu Kopyala"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "_URL'yi Kopyala"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
msgid "Copy Folder U_RL"
msgstr "Klasör U_RL'sini Kopyala"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
msgid "_Download File(s)"
msgstr "Dosya(lar)ı _İndir"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
msgid "Download File(s) _To…"
msgstr "Dosya(lar)ı _Şuraya İndir…"

#: pynicotine/gtkgui/search.py:615
msgid "Download _Folder(s)"
msgstr "_Klasör(ler)i İndir"

#: pynicotine/gtkgui/search.py:616
msgid "Download F_older(s) To…"
msgstr "K_lasör(ler)ı Şuraya İndir…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
msgid "View User _Profile"
msgstr "Kullanıcı _Profilini Görüntüle"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
msgid "_Browse Folder"
msgstr "Klasöre _Göz At"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
msgid "F_ile Properties"
msgstr "Do_sya Özellikleri"

#: pynicotine/gtkgui/search.py:629
msgid "Copy Search Term"
msgstr "Arama Terimini Kopyala"

#: pynicotine/gtkgui/search.py:631
msgid "Clear All Results"
msgstr "Tüm Sonuçları Temizle"

#: pynicotine/gtkgui/search.py:718
msgid "Clear Filters"
msgstr "Filtreleri Temizle"

#: pynicotine/gtkgui/search.py:721
msgid "Restore Filters"
msgstr "Filtreleri Geri Yükle"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, python-format
msgid "[PRIVATE]  %s"
msgstr "[ÖZEL]  %s"

#: pynicotine/gtkgui/search.py:1273
#, python-format
msgid "_Result Filters [%d]"
msgstr "_Sonuç Filtreleri [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
msgid "_Result Filters"
msgstr "_Sonuç Filtreleri"

#: pynicotine/gtkgui/search.py:1277
#, python-format
msgid "%d active filter(s)"
msgstr "%d etkin filtre"

#: pynicotine/gtkgui/search.py:1329
msgid "Add Wi_sh"
msgstr "_Dilek Ekle"

#: pynicotine/gtkgui/search.py:1332
msgid "Remove Wi_sh"
msgstr "_Dileği Kaldır"

#: pynicotine/gtkgui/search.py:1349
msgid "Select User's Results"
msgstr "Kullanıcının Sonuçlarını Seç"

#: pynicotine/gtkgui/search.py:1472
#, python-format
msgid "Total: %s"
msgstr "Toplam: %s"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
msgid "Results"
msgstr "Sonuçlar"

#: pynicotine/gtkgui/search.py:1590
msgid "Select Destination Folder for File(s)"
msgstr "Dosya(lar) için Hedef Klasörü Seç"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
msgid "Select Destination Folder"
msgstr "Hedef Klasörü Seç"

#: pynicotine/gtkgui/transfers.py:61
msgid "Queued"
msgstr "Kuyruğa alındı"

#: pynicotine/gtkgui/transfers.py:62
msgid "Queued (prioritized)"
msgstr "Kuyruğa alındı (öncelikli)"

#: pynicotine/gtkgui/transfers.py:63
msgid "Queued (privileged)"
msgstr "Kuyruğa alındı (ayrıcalıklı)"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Durum alınıyor"

#: pynicotine/gtkgui/transfers.py:65
msgid "Transferring"
msgstr "Aktarılıyor"

#: pynicotine/gtkgui/transfers.py:66
msgid "Connection closed"
msgstr "Bağlantı kapatıldı"

#: pynicotine/gtkgui/transfers.py:67
msgid "Connection timeout"
msgstr "Bağlantı zaman aşımı"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Kullanıcı oturumu kapattı"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
msgid "Cancelled"
msgstr "İptal edildi"

#: pynicotine/gtkgui/transfers.py:73
msgid "Download folder error"
msgstr "İndirme klasörü hatası"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Yerel dosya hatası"

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr "Yasaklandı"

#: pynicotine/gtkgui/transfers.py:76
msgid "File not shared"
msgstr "Dosya paylaşılmadı"

#: pynicotine/gtkgui/transfers.py:77
msgid "Pending shutdown"
msgstr "Kapatma bekleniyor"

#: pynicotine/gtkgui/transfers.py:78
msgid "File read error"
msgstr "Dosya okuma hatası"

#: pynicotine/gtkgui/transfers.py:182
msgid "Queue"
msgstr "Kuyruk"

#: pynicotine/gtkgui/transfers.py:188
msgid "Percent"
msgstr "Yüzde"

#: pynicotine/gtkgui/transfers.py:208
msgid "Time Elapsed"
msgstr "Geçen Süre"

#: pynicotine/gtkgui/transfers.py:215
msgid "Time Left"
msgstr "Kalan Süre"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
msgid "_Open File"
msgstr "Dosya _Aç"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
msgid "Open in File _Manager"
msgstr "Dosya _Yöneticisinde Aç"

#: pynicotine/gtkgui/transfers.py:286
msgid "_Search"
msgstr "_Ara"

#: pynicotine/gtkgui/transfers.py:289
msgid "Clear All"
msgstr "Tümünü Temizle"

#: pynicotine/gtkgui/transfers.py:953
msgid "Select User's Transfers"
msgstr "Kullanıcının Aktarımlarını Seç"

#: pynicotine/gtkgui/uploads.py:50
msgid "_Abort"
msgstr "_İptal Et"

#: pynicotine/gtkgui/uploads.py:74
msgid "Finished / Cancelled / Failed"
msgstr "Tamamlandı / İptal Edildi / Başarısız Oldu"

#: pynicotine/gtkgui/uploads.py:75
msgid "Finished / Cancelled"
msgstr "Tamamlandı / İptal Edildi"

#: pynicotine/gtkgui/uploads.py:79
msgid "Failed"
msgstr "Başarısız oldu"

#: pynicotine/gtkgui/uploads.py:80
msgid "User Logged Off"
msgstr "Kullanıcı Oturumu Kapattı"

#: pynicotine/gtkgui/uploads.py:142
#, python-format
msgid "Uploads: %(speed)s"
msgstr "Yüklenenler: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr "Çıkılıyor..."

#: pynicotine/gtkgui/uploads.py:164
msgid "Clear Queued Uploads"
msgstr "Kuyruktaki Yüklemeleri Temizle"

#: pynicotine/gtkgui/uploads.py:165
msgid "Do you really want to clear all queued uploads?"
msgstr "Kuyruğa alınan tüm yüklemeleri gerçekten silmek istiyor musunuz?"

#: pynicotine/gtkgui/uploads.py:177
msgid "Clear All Uploads"
msgstr "Tüm Yüklemeleri Temizle"

#: pynicotine/gtkgui/uploads.py:178
msgid "Do you really want to clear all uploads?"
msgstr "Tüm yüklemeleri gerçekten silmek istiyor musunuz?"

#: pynicotine/gtkgui/userbrowse.py:282
msgid "_Save Shares List to Disk"
msgstr "_Paylaşım Listesini Diske Kaydet"

#: pynicotine/gtkgui/userbrowse.py:292
msgid "Upload Folder & Subfolders…"
msgstr "Klasörü ve Alt Klasörleri Yükle…"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
msgid "Copy _Folder Path"
msgstr "Kla_sör Yolunu Kopyala"

#: pynicotine/gtkgui/userbrowse.py:309
msgid "_Download Folder & Subfolders"
msgstr "Klasörü ve Alt Klasörleri İn_dir"

#: pynicotine/gtkgui/userbrowse.py:310
msgid "Download Folder & Subfolders _To…"
msgstr "Klasörü ve Al_t Klasörleri Şuraya İndir…"

#: pynicotine/gtkgui/userbrowse.py:334
msgid "File Name"
msgstr "Dosya Adı"

#: pynicotine/gtkgui/userbrowse.py:373
msgid "Up_load File(s)…"
msgstr "_Dosya(lar)ı Yükle…"

#: pynicotine/gtkgui/userbrowse.py:374
msgid "Upload Folder…"
msgstr "Klasörü Yükle…"

#: pynicotine/gtkgui/userbrowse.py:396
msgid "Download Folder _To…"
msgstr "K_lasör(ler)ı Şuraya İndir…"

#: pynicotine/gtkgui/userbrowse.py:600
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"Kullanıcının paylaşılan dosyalar listesi boş. Ya kullanıcı hiçbir şey "
"paylaşmıyor ya da dosyaları özel olarak paylaşıyor."

#: pynicotine/gtkgui/userbrowse.py:615
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Kullanıcıdan paylaşılan dosyalar istenemiyor. Ya kullanıcı çevrim dışı, "
"ikinizin de dinleme bağlantı noktası kapalı ya da geçici bir bağlantı sorunu "
"var."

#: pynicotine/gtkgui/userbrowse.py:953
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Birden Çok Klasörü İndirmek İçin Hedef Seç"

#: pynicotine/gtkgui/userbrowse.py:997
msgid "Upload Folder (with Subfolders) To User"
msgstr "Klasörü (Alt Klasörlerle) Kullanıcıya Yükle"

#: pynicotine/gtkgui/userbrowse.py:999
msgid "Upload Folder To User"
msgstr "Klasörü Kullanıcıya Yükle"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
msgid "Enter the name of the user you want to upload to:"
msgstr "Yüklemek istediğiniz kullanıcının adını girin:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
msgid "_Upload"
msgstr "_Yükle"

#: pynicotine/gtkgui/userbrowse.py:1139
msgid "Select Destination Folder for Files"
msgstr "Dosyalar için Hedef Klasörü Seç"

#: pynicotine/gtkgui/userbrowse.py:1161
msgid "Upload File(s) To User"
msgstr "Dosya(lar)ı Kullanıcıya Yükle"

#: pynicotine/gtkgui/userinfo.py:376
msgid "Copy Picture"
msgstr "Resmi Kopyala"

#: pynicotine/gtkgui/userinfo.py:377
msgid "Save Picture"
msgstr "Resmi Kaydet"

#: pynicotine/gtkgui/userinfo.py:468
#, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "%(user)s kullanıcısı için resim yüklenemedi: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Kullanıcıdan bilgi istenemiyor. Ya ikinizin de dinleme bağlantı noktası "
"kapalı, kullanıcı çevrim dışı ya da geçici bir bağlantı sorunu var."

#: pynicotine/gtkgui/userinfo.py:577
msgid "Remove _Buddy"
msgstr "Arkadaşı _Kaldır"

#: pynicotine/gtkgui/userinfo.py:577
msgid "Add _Buddy"
msgstr "Arkadaş _Ekle"

#: pynicotine/gtkgui/userinfo.py:581
msgid "Unban User"
msgstr "Kullanıcının Yasağını Kaldır"

#: pynicotine/gtkgui/userinfo.py:585
msgid "Unignore User"
msgstr "Kullanıcıyı Yok Saymayı Kaldır"

#: pynicotine/gtkgui/userinfo.py:613
msgid "Yes"
msgstr "Evet"

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr "Hayır"

#: pynicotine/gtkgui/userinfo.py:773
msgid "Please enter number of days."
msgstr "Lütfen gün sayısını girin."

#: pynicotine/gtkgui/userinfo.py:787
#, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"%(user)s kullanıcısına Soulseek ayrıcalıklarınızın şu kadar gününü hediye "
"edin (%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, python-format
msgid "%(days)s days left"
msgstr "%(days)s gün kaldı"

#: pynicotine/gtkgui/userinfo.py:795
msgid "Gift Privileges"
msgstr "Ayrıcalık Hediye Et"

#: pynicotine/gtkgui/userinfo.py:797
msgid "_Give Privileges"
msgstr "Ayrıcalık _Ver"

#: pynicotine/gtkgui/widgets/dialogs.py:309
msgid "Close"
msgstr "Kapat"

#: pynicotine/gtkgui/widgets/dialogs.py:488
msgid "_Yes"
msgstr "_Evet"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
msgid "_OK"
msgstr "_Tamam"

#: pynicotine/gtkgui/widgets/filechooser.py:39
msgid "Select a File"
msgstr "Dosya Seç"

#: pynicotine/gtkgui/widgets/filechooser.py:174
msgid "Select a Folder"
msgstr "Klasör Seç"

#: pynicotine/gtkgui/widgets/filechooser.py:179
msgid "_Select"
msgstr "_Seç"

#: pynicotine/gtkgui/widgets/filechooser.py:196
msgid "Select an Image"
msgstr "Resim Seç"

#: pynicotine/gtkgui/widgets/filechooser.py:203
msgid "All images"
msgstr "Tüm resimler"

#: pynicotine/gtkgui/widgets/filechooser.py:241
msgid "Save as…"
msgstr "Farklı kaydet…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
msgid "(None)"
msgstr "(Yok)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
msgid "Close Tab"
msgstr "Sekmeyi Kapat"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
msgid "Close All Tabs?"
msgstr "Tüm Sekmeler Kapatılsın mı?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
msgid "Do you really want to close all tabs?"
msgstr "Tüm sekmeleri gerçekten kapatmak istiyor musunuz?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, python-format
msgid "%i Unread Tab(s)"
msgstr "%i Okunmayan Sekme"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
msgid "All Tabs"
msgstr "Tüm Sekmeler"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
msgid "Re_open Closed Tab"
msgstr "Kapatılan Sekmeyi _Yeniden Aç"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, python-format
msgid "%s File(s) Selected"
msgstr "%s Dosya Seçildi"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
msgid "_Browse Files"
msgstr "Dosyalara _Göz At"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
msgid "_Add Buddy"
msgstr "Arkadaş _Ekle"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
msgid "Show IP A_ddress"
msgstr "IP _Adresini Göster"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
msgid "Private Rooms"
msgstr "Özel Odalar"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, python-format
msgid "Remove from Private Room %s"
msgstr "%s Özel Odasından Kaldır"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, python-format
msgid "Add to Private Room %s"
msgstr "%s Özel Odasına Ekle"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, python-format
msgid "Remove as Operator of %s"
msgstr "%s İşleticisinden Kaldır"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, python-format
msgid "Add as Operator of %s"
msgstr "%s İşleticisi Olarak Ekle"

#: pynicotine/gtkgui/widgets/textentry.py:37
msgid "Send message…"
msgstr "Mesaj gönder…"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
msgid "Find Previous Match"
msgstr "Önceki Eşleşmeyi Bul"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
msgid "Find Next Match"
msgstr "Sonraki Eşleşmeyi Bul"

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr "--- eski mesajlar yukarıda ---"

#: pynicotine/gtkgui/widgets/theme.py:243
msgid "Executable"
msgstr "Çalıştırılabilir Dosya"

#: pynicotine/gtkgui/widgets/theme.py:244
msgid "Audio"
msgstr "Ses"

#: pynicotine/gtkgui/widgets/theme.py:245
msgid "Image"
msgstr "Resim"

#: pynicotine/gtkgui/widgets/theme.py:246
msgid "Archive"
msgstr "Arşiv"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
msgid "Miscellaneous"
msgstr "Çeşitli"

#: pynicotine/gtkgui/widgets/theme.py:248
msgid "Video"
msgstr "Video"

#: pynicotine/gtkgui/widgets/theme.py:249
msgid "Document"
msgstr "Belge"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr "Metin"

#: pynicotine/gtkgui/widgets/theme.py:357
#, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "%(path)s özel simgesi yüklenirken hata oluştu: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Hide Nicotine+"
msgstr "Nicotine+'ı Gizle"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Show Nicotine+"
msgstr "Nicotine+'ı Göster"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, python-format
msgid "Column #%i"
msgstr "Sütun #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
msgid "Ungrouped"
msgstr "Gruplandırılmadı"

#: pynicotine/gtkgui/widgets/treeview.py:960
msgid "Group by Folder"
msgstr "Klasöre Göre Gruplandır"

#: pynicotine/gtkgui/widgets/treeview.py:963
msgid "Group by User"
msgstr "Kullanıcıya Göre Gruplandır"

#: pynicotine/headless/application.py:77
#, python-format
msgid "Do you really want to exit? %s"
msgstr "Gerçekten çıkmak istiyor musunuz? %s"

#: pynicotine/headless/application.py:81
#, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr "%s kullanıcısı zaten var ve girdiğiniz parola geçersiz."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr "Başka bir kullanıcı adı veya parola ile oturum açmak için %s yazın."

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
msgid "Password: "
msgstr "Parola: "

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Yeni bir Soulseek hesabı oluşturmak için istediğiniz kullanıcı adını ve "
"parolayı girin. Zaten bir hesabınız varsa, oturum açma bilgilerinizi girin."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr "Şu paylaşımlar kullanılabilir değil:"

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr "Yeniden tarama denensin mi? %s"

#: pynicotine/logfacility.py:181
#, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr "\"%(filename)s\" günlük dosyasına yazılamadı: %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "%(path)s günlük dosyasına erişilemiyor: %(error)s"

#: pynicotine/networkfilter.py:40
msgid "Andorra"
msgstr "Andorra"

#: pynicotine/networkfilter.py:41
msgid "United Arab Emirates"
msgstr "Birleşik Arap Emirlikleri"

#: pynicotine/networkfilter.py:42
msgid "Afghanistan"
msgstr "Afganistan"

#: pynicotine/networkfilter.py:43
msgid "Antigua & Barbuda"
msgstr "Antigua ve Barbuda"

#: pynicotine/networkfilter.py:44
msgid "Anguilla"
msgstr "Anguilla"

#: pynicotine/networkfilter.py:45
msgid "Albania"
msgstr "Arnavutluk"

#: pynicotine/networkfilter.py:46
msgid "Armenia"
msgstr "Ermenistan"

#: pynicotine/networkfilter.py:47
msgid "Angola"
msgstr "Angola"

#: pynicotine/networkfilter.py:48
msgid "Antarctica"
msgstr "Antarktika"

#: pynicotine/networkfilter.py:49
msgid "Argentina"
msgstr "Arjantin"

#: pynicotine/networkfilter.py:50
msgid "American Samoa"
msgstr "Amerikan Samoası"

#: pynicotine/networkfilter.py:51
msgid "Austria"
msgstr "Avusturya"

#: pynicotine/networkfilter.py:52
msgid "Australia"
msgstr "Avustralya"

#: pynicotine/networkfilter.py:53
msgid "Aruba"
msgstr "Aruba"

#: pynicotine/networkfilter.py:54
msgid "Åland Islands"
msgstr "Åland Adaları"

#: pynicotine/networkfilter.py:55
msgid "Azerbaijan"
msgstr "Azerbaycan"

#: pynicotine/networkfilter.py:56
msgid "Bosnia & Herzegovina"
msgstr "Bosna-Hersek"

#: pynicotine/networkfilter.py:57
msgid "Barbados"
msgstr "Barbados"

#: pynicotine/networkfilter.py:58
msgid "Bangladesh"
msgstr "Bangladeş"

#: pynicotine/networkfilter.py:59
msgid "Belgium"
msgstr "Belçika"

#: pynicotine/networkfilter.py:60
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: pynicotine/networkfilter.py:61
msgid "Bulgaria"
msgstr "Bulgaristan"

#: pynicotine/networkfilter.py:62
msgid "Bahrain"
msgstr "Bahreyn"

#: pynicotine/networkfilter.py:63
msgid "Burundi"
msgstr "Burundi"

#: pynicotine/networkfilter.py:64
msgid "Benin"
msgstr "Benin"

#: pynicotine/networkfilter.py:65
msgid "Saint Barthelemy"
msgstr "Saint Barthelemy"

#: pynicotine/networkfilter.py:66
msgid "Bermuda"
msgstr "Bermuda"

#: pynicotine/networkfilter.py:67
msgid "Brunei Darussalam"
msgstr "Brunei Sultanlığı"

#: pynicotine/networkfilter.py:68
msgid "Bolivia"
msgstr "Bolivya"

#: pynicotine/networkfilter.py:69
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius ve Saba"

#: pynicotine/networkfilter.py:70
msgid "Brazil"
msgstr "Brezilya"

#: pynicotine/networkfilter.py:71
msgid "Bahamas"
msgstr "Bahamalar"

#: pynicotine/networkfilter.py:72
msgid "Bhutan"
msgstr "Bhutan"

#: pynicotine/networkfilter.py:73
msgid "Bouvet Island"
msgstr "Bouvet Adası"

#: pynicotine/networkfilter.py:74
msgid "Botswana"
msgstr "Botsvana"

#: pynicotine/networkfilter.py:75
msgid "Belarus"
msgstr "Belarus"

#: pynicotine/networkfilter.py:76
msgid "Belize"
msgstr "Belize"

#: pynicotine/networkfilter.py:77
msgid "Canada"
msgstr "Kanada"

#: pynicotine/networkfilter.py:78
msgid "Cocos (Keeling) Islands"
msgstr "Cocos (Keeling) Adaları"

#: pynicotine/networkfilter.py:79
msgid "Democratic Republic of Congo"
msgstr "Kongo Demokratik Cumhuriyeti"

#: pynicotine/networkfilter.py:80
msgid "Central African Republic"
msgstr "Orta Afrika Cumhuriyeti"

#: pynicotine/networkfilter.py:81
msgid "Congo"
msgstr "Kongo"

#: pynicotine/networkfilter.py:82
msgid "Switzerland"
msgstr "İsviçre"

#: pynicotine/networkfilter.py:83
msgid "Ivory Coast"
msgstr "Fildişi Sahili"

#: pynicotine/networkfilter.py:84
msgid "Cook Islands"
msgstr "Cook Adaları"

#: pynicotine/networkfilter.py:85
msgid "Chile"
msgstr "Şili"

#: pynicotine/networkfilter.py:86
msgid "Cameroon"
msgstr "Kamerun"

#: pynicotine/networkfilter.py:87
msgid "China"
msgstr "Çin"

#: pynicotine/networkfilter.py:88
msgid "Colombia"
msgstr "Kolombiya"

#: pynicotine/networkfilter.py:89
msgid "Costa Rica"
msgstr "Kosta Rika"

#: pynicotine/networkfilter.py:90
msgid "Cuba"
msgstr "Küba"

#: pynicotine/networkfilter.py:91
msgid "Cabo Verde"
msgstr "Yeşil Burun Adaları"

#: pynicotine/networkfilter.py:92
msgid "Curaçao"
msgstr "Curaçao"

#: pynicotine/networkfilter.py:93
msgid "Christmas Island"
msgstr "Christmas Adası"

#: pynicotine/networkfilter.py:94
msgid "Cyprus"
msgstr "Kıbrıs"

#: pynicotine/networkfilter.py:95
msgid "Czechia"
msgstr "Çekya"

#: pynicotine/networkfilter.py:96
msgid "Germany"
msgstr "Almanya"

#: pynicotine/networkfilter.py:97
msgid "Djibouti"
msgstr "Cibuti"

#: pynicotine/networkfilter.py:98
msgid "Denmark"
msgstr "Danimarka"

#: pynicotine/networkfilter.py:99
msgid "Dominica"
msgstr "Dominika"

#: pynicotine/networkfilter.py:100
msgid "Dominican Republic"
msgstr "Dominik Cumhuriyeti"

#: pynicotine/networkfilter.py:101
msgid "Algeria"
msgstr "Cezayir"

#: pynicotine/networkfilter.py:102
msgid "Ecuador"
msgstr "Ekvador"

#: pynicotine/networkfilter.py:103
msgid "Estonia"
msgstr "Estonya"

#: pynicotine/networkfilter.py:104
msgid "Egypt"
msgstr "Mısır"

#: pynicotine/networkfilter.py:105
msgid "Western Sahara"
msgstr "Batı Sahra"

#: pynicotine/networkfilter.py:106
msgid "Eritrea"
msgstr "Eritre"

#: pynicotine/networkfilter.py:107
msgid "Spain"
msgstr "İspanya"

#: pynicotine/networkfilter.py:108
msgid "Ethiopia"
msgstr "Etiyopya"

#: pynicotine/networkfilter.py:109
msgid "Europe"
msgstr "Avrupa"

#: pynicotine/networkfilter.py:110
msgid "Finland"
msgstr "Finlandiya"

#: pynicotine/networkfilter.py:111
msgid "Fiji"
msgstr "Fiji"

#: pynicotine/networkfilter.py:112
msgid "Falkland Islands (Malvinas)"
msgstr "Falkland (Malvina) Adaları"

#: pynicotine/networkfilter.py:113
msgid "Micronesia"
msgstr "Mikronezya"

#: pynicotine/networkfilter.py:114
msgid "Faroe Islands"
msgstr "Faroe Adaları"

#: pynicotine/networkfilter.py:115
msgid "France"
msgstr "Fransa"

#: pynicotine/networkfilter.py:116
msgid "Gabon"
msgstr "Gabon"

#: pynicotine/networkfilter.py:117
msgid "Great Britain"
msgstr "Büyük Britanya"

#: pynicotine/networkfilter.py:118
msgid "Grenada"
msgstr "Grenada"

#: pynicotine/networkfilter.py:119
msgid "Georgia"
msgstr "Gürcistan"

#: pynicotine/networkfilter.py:120
msgid "French Guiana"
msgstr "Fransız Guyanası"

#: pynicotine/networkfilter.py:121
msgid "Guernsey"
msgstr "Guernsey"

#: pynicotine/networkfilter.py:122
msgid "Ghana"
msgstr "Gana"

#: pynicotine/networkfilter.py:123
msgid "Gibraltar"
msgstr "Cebelitarık"

#: pynicotine/networkfilter.py:124
msgid "Greenland"
msgstr "Grönland"

#: pynicotine/networkfilter.py:125
msgid "Gambia"
msgstr "Gambiya"

#: pynicotine/networkfilter.py:126
msgid "Guinea"
msgstr "Gine"

#: pynicotine/networkfilter.py:127
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: pynicotine/networkfilter.py:128
msgid "Equatorial Guinea"
msgstr "Ekvator Ginesi"

#: pynicotine/networkfilter.py:129
msgid "Greece"
msgstr "Yunanistan"

#: pynicotine/networkfilter.py:130
msgid "South Georgia & South Sandwich Islands"
msgstr "Güney Georgia ve Güney Sandwich Adaları"

#: pynicotine/networkfilter.py:131
msgid "Guatemala"
msgstr "Guatemala"

#: pynicotine/networkfilter.py:132
msgid "Guam"
msgstr "Guam"

#: pynicotine/networkfilter.py:133
msgid "Guinea-Bissau"
msgstr "Gine-Bissau"

#: pynicotine/networkfilter.py:134
msgid "Guyana"
msgstr "Guyana"

#: pynicotine/networkfilter.py:135
msgid "Hong Kong"
msgstr "Hong Kong"

#: pynicotine/networkfilter.py:136
msgid "Heard & McDonald Islands"
msgstr "Heard ve McDonald Adaları"

#: pynicotine/networkfilter.py:137
msgid "Honduras"
msgstr "Honduras"

#: pynicotine/networkfilter.py:138
msgid "Croatia"
msgstr "Hırvatistan"

#: pynicotine/networkfilter.py:139
msgid "Haiti"
msgstr "Haiti"

#: pynicotine/networkfilter.py:140
msgid "Hungary"
msgstr "Macaristan"

#: pynicotine/networkfilter.py:141
msgid "Indonesia"
msgstr "Endonezya"

#: pynicotine/networkfilter.py:142
msgid "Ireland"
msgstr "İrlanda"

#: pynicotine/networkfilter.py:143
msgid "Israel"
msgstr "İsrail"

#: pynicotine/networkfilter.py:144
msgid "Isle of Man"
msgstr "Man Adası"

#: pynicotine/networkfilter.py:145
msgid "India"
msgstr "Hindistan"

#: pynicotine/networkfilter.py:146
msgid "British Indian Ocean Territory"
msgstr "Britanya Hint Okyanusu Toprakları"

#: pynicotine/networkfilter.py:147
msgid "Iraq"
msgstr "Irak"

#: pynicotine/networkfilter.py:148
msgid "Iran"
msgstr "İran"

#: pynicotine/networkfilter.py:149
msgid "Iceland"
msgstr "İzlanda"

#: pynicotine/networkfilter.py:150
msgid "Italy"
msgstr "İtalya"

#: pynicotine/networkfilter.py:151
msgid "Jersey"
msgstr "Jersey"

#: pynicotine/networkfilter.py:152
msgid "Jamaica"
msgstr "Jamaika"

#: pynicotine/networkfilter.py:153
msgid "Jordan"
msgstr "Ürdün"

#: pynicotine/networkfilter.py:154
msgid "Japan"
msgstr "Japonya"

#: pynicotine/networkfilter.py:155
msgid "Kenya"
msgstr "Kenya"

#: pynicotine/networkfilter.py:156
msgid "Kyrgyzstan"
msgstr "Kırgızistan"

#: pynicotine/networkfilter.py:157
msgid "Cambodia"
msgstr "Kamboçya"

#: pynicotine/networkfilter.py:158
msgid "Kiribati"
msgstr "Kiribati"

#: pynicotine/networkfilter.py:159
msgid "Comoros"
msgstr "Komorlar"

#: pynicotine/networkfilter.py:160
msgid "Saint Kitts & Nevis"
msgstr "Saint Kitts ve Nevis"

#: pynicotine/networkfilter.py:161
msgid "North Korea"
msgstr "Kuzey Kore"

#: pynicotine/networkfilter.py:162
msgid "South Korea"
msgstr "Güney Kore"

#: pynicotine/networkfilter.py:163
msgid "Kuwait"
msgstr "Kuveyt"

#: pynicotine/networkfilter.py:164
msgid "Cayman Islands"
msgstr "Cayman Adaları"

#: pynicotine/networkfilter.py:165
msgid "Kazakhstan"
msgstr "Kazakistan"

#: pynicotine/networkfilter.py:166
msgid "Laos"
msgstr "Laos"

#: pynicotine/networkfilter.py:167
msgid "Lebanon"
msgstr "Lübnan"

#: pynicotine/networkfilter.py:168
msgid "Saint Lucia"
msgstr "Saint Lucia"

#: pynicotine/networkfilter.py:169
msgid "Liechtenstein"
msgstr "Lihtenştayn"

#: pynicotine/networkfilter.py:170
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: pynicotine/networkfilter.py:171
msgid "Liberia"
msgstr "Liberya"

#: pynicotine/networkfilter.py:172
msgid "Lesotho"
msgstr "Lesotho"

#: pynicotine/networkfilter.py:173
msgid "Lithuania"
msgstr "Litvanya"

#: pynicotine/networkfilter.py:174
msgid "Luxembourg"
msgstr "Lüksemburg"

#: pynicotine/networkfilter.py:175
msgid "Latvia"
msgstr "Letonya"

#: pynicotine/networkfilter.py:176
msgid "Libya"
msgstr "Libya"

#: pynicotine/networkfilter.py:177
msgid "Morocco"
msgstr "Fas"

#: pynicotine/networkfilter.py:178
msgid "Monaco"
msgstr "Monako"

#: pynicotine/networkfilter.py:179
msgid "Moldova"
msgstr "Moldova"

#: pynicotine/networkfilter.py:180
msgid "Montenegro"
msgstr "Karadağ"

#: pynicotine/networkfilter.py:181
msgid "Saint Martin"
msgstr "Saint Martin"

#: pynicotine/networkfilter.py:182
msgid "Madagascar"
msgstr "Madagaskar"

#: pynicotine/networkfilter.py:183
msgid "Marshall Islands"
msgstr "Marshall Adaları"

#: pynicotine/networkfilter.py:184
msgid "North Macedonia"
msgstr "Kuzey Makedonya"

#: pynicotine/networkfilter.py:185
msgid "Mali"
msgstr "Mali"

#: pynicotine/networkfilter.py:186
msgid "Myanmar"
msgstr "Myanmar"

#: pynicotine/networkfilter.py:187
msgid "Mongolia"
msgstr "Moğolistan"

#: pynicotine/networkfilter.py:188
msgid "Macau"
msgstr "Makao"

#: pynicotine/networkfilter.py:189
msgid "Northern Mariana Islands"
msgstr "Kuzey Mariana Adaları"

#: pynicotine/networkfilter.py:190
msgid "Martinique"
msgstr "Martinik"

#: pynicotine/networkfilter.py:191
msgid "Mauritania"
msgstr "Moritanya"

#: pynicotine/networkfilter.py:192
msgid "Montserrat"
msgstr "Montserrat"

#: pynicotine/networkfilter.py:193
msgid "Malta"
msgstr "Malta"

#: pynicotine/networkfilter.py:194
msgid "Mauritius"
msgstr "Mauritius"

#: pynicotine/networkfilter.py:195
msgid "Maldives"
msgstr "Maldivler"

#: pynicotine/networkfilter.py:196
msgid "Malawi"
msgstr "Malavi"

#: pynicotine/networkfilter.py:197
msgid "Mexico"
msgstr "Meksika"

#: pynicotine/networkfilter.py:198
msgid "Malaysia"
msgstr "Malezya"

#: pynicotine/networkfilter.py:199
msgid "Mozambique"
msgstr "Mozambik"

#: pynicotine/networkfilter.py:200
msgid "Namibia"
msgstr "Namibya"

#: pynicotine/networkfilter.py:201
msgid "New Caledonia"
msgstr "Yeni Kaledonya"

#: pynicotine/networkfilter.py:202
msgid "Niger"
msgstr "Nijer"

#: pynicotine/networkfilter.py:203
msgid "Norfolk Island"
msgstr "Norfolk Adası"

#: pynicotine/networkfilter.py:204
msgid "Nigeria"
msgstr "Nijerya"

#: pynicotine/networkfilter.py:205
msgid "Nicaragua"
msgstr "Nikaragua"

#: pynicotine/networkfilter.py:206
msgid "Netherlands"
msgstr "Hollanda"

#: pynicotine/networkfilter.py:207
msgid "Norway"
msgstr "Norveç"

#: pynicotine/networkfilter.py:208
msgid "Nepal"
msgstr "Nepal"

#: pynicotine/networkfilter.py:209
msgid "Nauru"
msgstr "Nauru"

#: pynicotine/networkfilter.py:210
msgid "Niue"
msgstr "Niue"

#: pynicotine/networkfilter.py:211
msgid "New Zealand"
msgstr "Yeni Zelanda"

#: pynicotine/networkfilter.py:212
msgid "Oman"
msgstr "Umman"

#: pynicotine/networkfilter.py:213
msgid "Panama"
msgstr "Panama"

#: pynicotine/networkfilter.py:214
msgid "Peru"
msgstr "Peru"

#: pynicotine/networkfilter.py:215
msgid "French Polynesia"
msgstr "Fransız Polinezyası"

#: pynicotine/networkfilter.py:216
msgid "Papua New Guinea"
msgstr "Papua Yeni Gine"

#: pynicotine/networkfilter.py:217
msgid "Philippines"
msgstr "Filipinler"

#: pynicotine/networkfilter.py:218
msgid "Pakistan"
msgstr "Pakistan"

#: pynicotine/networkfilter.py:219
msgid "Poland"
msgstr "Polonya"

#: pynicotine/networkfilter.py:220
msgid "Saint Pierre & Miquelon"
msgstr "Saint Pierre ve Miquelon"

#: pynicotine/networkfilter.py:221
msgid "Pitcairn"
msgstr "Pitcairn"

#: pynicotine/networkfilter.py:222
msgid "Puerto Rico"
msgstr "Porto Riko"

#: pynicotine/networkfilter.py:223
msgid "State of Palestine"
msgstr "Filistin Devleti"

#: pynicotine/networkfilter.py:224
msgid "Portugal"
msgstr "Portekiz"

#: pynicotine/networkfilter.py:225
msgid "Palau"
msgstr "Palau"

#: pynicotine/networkfilter.py:226
msgid "Paraguay"
msgstr "Paraguay"

#: pynicotine/networkfilter.py:227
msgid "Qatar"
msgstr "Katar"

#: pynicotine/networkfilter.py:228
msgid "Réunion"
msgstr "Réunion"

#: pynicotine/networkfilter.py:229
msgid "Romania"
msgstr "Romanya"

#: pynicotine/networkfilter.py:230
msgid "Serbia"
msgstr "Sırbistan"

#: pynicotine/networkfilter.py:231
msgid "Russia"
msgstr "Rusya"

#: pynicotine/networkfilter.py:232
msgid "Rwanda"
msgstr "Ruanda"

#: pynicotine/networkfilter.py:233
msgid "Saudi Arabia"
msgstr "Suudi Arabistan"

#: pynicotine/networkfilter.py:234
msgid "Solomon Islands"
msgstr "Solomon Adaları"

#: pynicotine/networkfilter.py:235
msgid "Seychelles"
msgstr "Seyşeller"

#: pynicotine/networkfilter.py:236
msgid "Sudan"
msgstr "Sudan"

#: pynicotine/networkfilter.py:237
msgid "Sweden"
msgstr "İsveç"

#: pynicotine/networkfilter.py:238
msgid "Singapore"
msgstr "Singapur"

#: pynicotine/networkfilter.py:239
msgid "Saint Helena"
msgstr "Saint Helena"

#: pynicotine/networkfilter.py:240
msgid "Slovenia"
msgstr "Slovenya"

#: pynicotine/networkfilter.py:241
msgid "Svalbard & Jan Mayen Islands"
msgstr "Svalbard ve Jan Mayen Adaları"

#: pynicotine/networkfilter.py:242
msgid "Slovak Republic"
msgstr "Slovak Cumhuriyeti"

#: pynicotine/networkfilter.py:243
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: pynicotine/networkfilter.py:244
msgid "San Marino"
msgstr "San Marino"

#: pynicotine/networkfilter.py:245
msgid "Senegal"
msgstr "Senegal"

#: pynicotine/networkfilter.py:246
msgid "Somalia"
msgstr "Somali"

#: pynicotine/networkfilter.py:247
msgid "Suriname"
msgstr "Surinam"

#: pynicotine/networkfilter.py:248
msgid "South Sudan"
msgstr "Güney Sudan"

#: pynicotine/networkfilter.py:249
msgid "Sao Tome & Principe"
msgstr "Sao Tome ve Principe"

#: pynicotine/networkfilter.py:250
msgid "El Salvador"
msgstr "El Salvador"

#: pynicotine/networkfilter.py:251
msgid "Sint Maarten"
msgstr "Sint Maarten"

#: pynicotine/networkfilter.py:252
msgid "Syria"
msgstr "Suriye"

#: pynicotine/networkfilter.py:253
msgid "Eswatini"
msgstr "Esvatini"

#: pynicotine/networkfilter.py:254
msgid "Turks & Caicos Islands"
msgstr "Turks ve Caicos Adaları"

#: pynicotine/networkfilter.py:255
msgid "Chad"
msgstr "Çad"

#: pynicotine/networkfilter.py:256
msgid "French Southern Territories"
msgstr "Fransız Güney Toprakları"

#: pynicotine/networkfilter.py:257
msgid "Togo"
msgstr "Togo"

#: pynicotine/networkfilter.py:258
msgid "Thailand"
msgstr "Tayland"

#: pynicotine/networkfilter.py:259
msgid "Tajikistan"
msgstr "Tacikistan"

#: pynicotine/networkfilter.py:260
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
msgid "Timor-Leste"
msgstr "Doğu Timor"

#: pynicotine/networkfilter.py:262
msgid "Turkmenistan"
msgstr "Türkmenistan"

#: pynicotine/networkfilter.py:263
msgid "Tunisia"
msgstr "Tunus"

#: pynicotine/networkfilter.py:264
msgid "Tonga"
msgstr "Tonga"

#: pynicotine/networkfilter.py:265
msgid "Türkiye"
msgstr "Türkiye"

#: pynicotine/networkfilter.py:266
msgid "Trinidad & Tobago"
msgstr "Trinidad ve Tobago"

#: pynicotine/networkfilter.py:267
msgid "Tuvalu"
msgstr "Tuvalu"

#: pynicotine/networkfilter.py:268
msgid "Taiwan"
msgstr "Tayvan"

#: pynicotine/networkfilter.py:269
msgid "Tanzania"
msgstr "Tanzanya"

#: pynicotine/networkfilter.py:270
msgid "Ukraine"
msgstr "Ukrayna"

#: pynicotine/networkfilter.py:271
msgid "Uganda"
msgstr "Uganda"

#: pynicotine/networkfilter.py:272
msgid "U.S. Minor Outlying Islands"
msgstr "Amerika Birleşik Devletleri Küçük Dış Adaları"

#: pynicotine/networkfilter.py:273
msgid "United States"
msgstr "Amerika Birleşik Devletleri"

#: pynicotine/networkfilter.py:274
msgid "Uruguay"
msgstr "Uruguay"

#: pynicotine/networkfilter.py:275
msgid "Uzbekistan"
msgstr "Özbekistan"

#: pynicotine/networkfilter.py:276
msgid "Holy See (Vatican City State)"
msgstr "Holy See (Vatikan Şehir Devleti)"

#: pynicotine/networkfilter.py:277
msgid "Saint Vincent & The Grenadines"
msgstr "Saint Vincent ve Grenadinler"

#: pynicotine/networkfilter.py:278
msgid "Venezuela"
msgstr "Venezuela"

#: pynicotine/networkfilter.py:279
msgid "British Virgin Islands"
msgstr "Britanya Virgin Adaları"

#: pynicotine/networkfilter.py:280
msgid "U.S. Virgin Islands"
msgstr "Amerika Birleşik Devletleri Virgin Adaları"

#: pynicotine/networkfilter.py:281
msgid "Viet Nam"
msgstr "Vietnam"

#: pynicotine/networkfilter.py:282
msgid "Vanuatu"
msgstr "Vanuatu"

#: pynicotine/networkfilter.py:283
msgid "Wallis & Futuna"
msgstr "Wallis ve Futuna"

#: pynicotine/networkfilter.py:284
msgid "Samoa"
msgstr "Samoa"

#: pynicotine/networkfilter.py:285
msgid "Yemen"
msgstr "Yemen"

#: pynicotine/networkfilter.py:286
msgid "Mayotte"
msgstr "Mayotte"

#: pynicotine/networkfilter.py:287
msgid "South Africa"
msgstr "Güney Afrika"

#: pynicotine/networkfilter.py:288
msgid "Zambia"
msgstr "Zambiya"

#: pynicotine/networkfilter.py:289
msgid "Zimbabwe"
msgstr "Zimbabve"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "Mesaj için metinden konuşmaya başarısız oldu: %s"

#: pynicotine/nowplaying.py:130
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr ""
"Last.fm: Lütfen hem Last.fm kullanıcı adınızı hem de API anahtarınızı "
"sağlayın"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
msgid "Now Playing Error"
msgstr "Şimdi Oynatılıyor Hatası"

#: pynicotine/nowplaying.py:140
#, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm: Audioscrobbler'a bağlanılamadı: %(error)s"

#: pynicotine/nowplaying.py:162
#, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr "Last.fm: Audioscrobbler'dan son parça alınamadı: %(error)s"

#: pynicotine/nowplaying.py:196
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: Uygun bir MPRIS oynatıcı bulunamadı"

#: pynicotine/nowplaying.py:201
#, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr ""
"Birden fazla MPRIS oynatıcı bulundu: %(players)s. Kullanılan: %(player)s"

#: pynicotine/nowplaying.py:204
#, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Otomatik algılanan MPRIS oynatıcı: %s"

#: pynicotine/nowplaying.py:219
#, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr "MPRIS: %(player)s sorgulanırken bir şeyler ters gitti: %(exception)s"

#: pynicotine/nowplaying.py:266
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "ListenBrainz: Lütfen ListenBrainz kullanıcı adınızı sağlayın"

#: pynicotine/nowplaying.py:275
#, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "ListenBrainz: ListenBrainz'e bağlanılamadı: %(error)s"

#: pynicotine/nowplaying.py:283
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "ListenBrainz: Şu anda hiçbir şey dinlemiyor gibisiniz"

#: pynicotine/nowplaying.py:297
#, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr "ListenBrainz: ListenBrainz'den şu anki parça alınamadı: %(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
msgid "Network Filters"
msgstr "Ağ Filtreleri"

#: pynicotine/plugins/core_commands/__init__.py:44
msgid "List available commands"
msgstr "Kullanılabilir komutları listele"

#: pynicotine/plugins/core_commands/__init__.py:49
msgid "Connect to the server"
msgstr "Sunucuya bağlan"

#: pynicotine/plugins/core_commands/__init__.py:53
msgid "Disconnect from the server"
msgstr "Sunucuyla bağlantıyı kes"

#: pynicotine/plugins/core_commands/__init__.py:58
msgid "Toggle away status"
msgstr "Uzakta durumunu değiştir"

#: pynicotine/plugins/core_commands/__init__.py:62
msgid "Manage plugins"
msgstr "Eklentileri yönet"

#: pynicotine/plugins/core_commands/__init__.py:74
msgid "Clear chat window"
msgstr "Sohbet penceresini temizle"

#: pynicotine/plugins/core_commands/__init__.py:80
msgid "Say something in the third-person"
msgstr "Üçüncü şahıs ağzından bir şey söyle"

#: pynicotine/plugins/core_commands/__init__.py:87
msgid "Announce the song currently playing"
msgstr "Şu anda oynatılan şarkıyı duyur"

#: pynicotine/plugins/core_commands/__init__.py:94
msgid "Join chat room"
msgstr "Sohbet odasına katıl"

#: pynicotine/plugins/core_commands/__init__.py:102
msgid "Leave chat room"
msgstr "Sohbet odasından ayrıl"

#: pynicotine/plugins/core_commands/__init__.py:110
msgid "Say message in specified chat room"
msgstr "Belirtilen sohbet odasında mesaj yaz"

#: pynicotine/plugins/core_commands/__init__.py:117
msgid "Open private chat"
msgstr "Özel sohbeti aç"

#: pynicotine/plugins/core_commands/__init__.py:125
msgid "Close private chat"
msgstr "Özel sohbeti kapat"

#: pynicotine/plugins/core_commands/__init__.py:133
msgid "Request user's client version"
msgstr "Kullanıcının istemci sürümünü iste"

#: pynicotine/plugins/core_commands/__init__.py:142
msgid "Send private message to user"
msgstr "Kullanıcıya özel mesaj gönder"

#: pynicotine/plugins/core_commands/__init__.py:150
msgid "Add user to buddy list"
msgstr "Arkadaş listesine kullanıcı ekle"

#: pynicotine/plugins/core_commands/__init__.py:158
msgid "Remove buddy from buddy list"
msgstr "Arkadaş listesinden arkadaşı kaldır"

#: pynicotine/plugins/core_commands/__init__.py:166
msgid "Browse files of user"
msgstr "Kullanıcının dosyalarına göz at"

#: pynicotine/plugins/core_commands/__init__.py:175
msgid "Show user profile information"
msgstr "Kullanıcı profili bilgilerini göster"

#: pynicotine/plugins/core_commands/__init__.py:183
msgid "Show IP address or username"
msgstr "IP adresini veya kullanıcı adını göster"

#: pynicotine/plugins/core_commands/__init__.py:190
msgid "Block connections from user or IP address"
msgstr "Kullanıcıdan veya IP adresinden gelen bağlantıları engelle"

#: pynicotine/plugins/core_commands/__init__.py:197
msgid "Remove user or IP address from ban lists"
msgstr "Kullanıcı veya IP adresini yasaklama listelerinden kaldır"

#: pynicotine/plugins/core_commands/__init__.py:204
msgid "Silence messages from user or IP address"
msgstr "Kullanıcıdan veya IP adresinden gelen mesajları sustur"

#: pynicotine/plugins/core_commands/__init__.py:212
msgid "Remove user or IP address from ignore lists"
msgstr "Kullanıcı veya IP adresini yok sayma listelerinden kaldır"

#: pynicotine/plugins/core_commands/__init__.py:220
msgid "Add share"
msgstr "Paylaşım ekle"

#: pynicotine/plugins/core_commands/__init__.py:226
msgid "Remove share"
msgstr "Paylaşımı kaldır"

#: pynicotine/plugins/core_commands/__init__.py:233
msgid "List shares"
msgstr "Paylaşımları listele"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Paylaşımları yeniden tara"

#: pynicotine/plugins/core_commands/__init__.py:246
msgid "Start global file search"
msgstr "Genel dosya aramayı başlat"

#: pynicotine/plugins/core_commands/__init__.py:254
msgid "Search files in joined rooms"
msgstr "Katıldığınız odalarda dosya ara"

#: pynicotine/plugins/core_commands/__init__.py:262
msgid "Search files of all buddies"
msgstr "Tüm arkadaşların dosyalarını ara"

#: pynicotine/plugins/core_commands/__init__.py:270
msgid "Search a user's shared files"
msgstr "Bir kullanıcının paylaşılan dosyalarını ara"

#: pynicotine/plugins/core_commands/__init__.py:296
#, python-format
msgid "Listing %(num)i available commands:"
msgstr "%(num)i kullanılabilir komut listeleniyor:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr "\"%(query)s\" ile eşleşen %(num)i kullanılabilir komut listeleniyor:"

#: pynicotine/plugins/core_commands/__init__.py:311
#, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Benzer komutları listelemek için %(command)s yazın"

#: pynicotine/plugins/core_commands/__init__.py:314
#, python-format
msgid "Type %(command)s to list available commands"
msgstr "Kullanılabilir komutları listelemek için %(command)s yazın"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, python-format
msgid "Not joined in room %s"
msgstr "%s odasına katılmadı"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr "%s kullanıcısıyla mesajlaşılmıyor"

#: pynicotine/plugins/core_commands/__init__.py:408
#, python-format
msgid "Closed private chat of user %s"
msgstr "%s kullanıcısının özel sohbeti kapatıldı"

#: pynicotine/plugins/core_commands/__init__.py:476
#, python-format
msgid "Banned %s"
msgstr "%s yasaklandı"

#: pynicotine/plugins/core_commands/__init__.py:490
#, python-format
msgid "Unbanned %s"
msgstr "%s yasağı kaldırıldı"

#: pynicotine/plugins/core_commands/__init__.py:503
#, python-format
msgid "Ignored %s"
msgstr "%s yok sayıldı"

#: pynicotine/plugins/core_commands/__init__.py:517
#, python-format
msgid "Unignored %s"
msgstr "%s yok sayılması kaldırıldı"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr "%(num_listed)s paylaşım listelendi (%(num_total)s yapılandırıldı)"

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr "Erişilemeyen \"%s\" klasörü paylaşılamıyor"

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""
"%(group_name)s paylaşımı \"%(virtual_name)s\" eklendi (yenidedn tarama "
"gerekli)"

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr "\"%s\" adında paylaşım yok"

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr "\"%s\" paylaşımı kaldırıldı (yeniden tarama gerekli)"

#: pynicotine/pluginsystem.py:413
msgid "Loading plugin system"
msgstr "Eklenti sistemi yükleniyor"

#: pynicotine/pluginsystem.py:516
#, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"%(name)s eklentisi yüklenemiyor. Eklenti klasörü adı geçersiz karakterler "
"içeriyor: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "%(name)s eklentisinde çakışan %(interface)s komutu: %(command)s"

#: pynicotine/pluginsystem.py:575
#, python-format
msgid "Loaded plugin %s"
msgstr "%s eklentisi yüklendi"

#: pynicotine/pluginsystem.py:579
#, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"%(module)s eklentisi yüklenemiyor\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, python-format
msgid "Unloaded plugin %s"
msgstr "%s eklentisi devre dışı bırakıldı"

#: pynicotine/pluginsystem.py:648
#, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"%(module)s eklentisi devre dışı bırakılamıyor\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"%(module)s eklentisi %(errortype)s hatasıyla başarısız oldu: %(error)s.\n"
"Geri izleme: %(trace)s"

#: pynicotine/pluginsystem.py:810
msgid "No description"
msgstr "Açıklama yok"

#: pynicotine/pluginsystem.py:887
#, python-format
msgid "Missing %s argument"
msgstr "%s argümanı eksik"

#: pynicotine/pluginsystem.py:896
#, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Geçersiz argüman, olası seçenekler: %s"

#: pynicotine/pluginsystem.py:901
#, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Kullanım: %(command)s %(parameters)s"

#: pynicotine/pluginsystem.py:940
#, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Bilinmeyen komut: %(command)s. Kullanılabilir komutları listelemek için "
"%(help_command)s yazın."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
msgid "No UPnP devices found"
msgstr "UPnP aygıtı bulunamadı"

#: pynicotine/portmapper.py:633
#, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"%(protocol)s: %(external_port)s dış bağlantı noktası iletilemedi: %(error)s"

#: pynicotine/portmapper.py:647
#, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"%(protocol)s: %(external_port)s dış bağlantı noktası, %(ip_address)s yerel "
"IP adresi %(local_port)s bağlantı noktasına başarıyla iletildi"

#: pynicotine/privatechat.py:220
#, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "'%(user)s' kullanıcısından özel mesaj: %(message)s"

#: pynicotine/search.py:368
#, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "\"%s\" dilek listesi ögesi aranıyor"

#: pynicotine/search.py:434
#, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Dilek listesi bekleme süresi %s saniyeye ayarlandı"

#: pynicotine/search.py:760
#, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr ""
"%(user)s kullanıcısı \"%(query)s\" için arama yapıyor, %(num)i sonuç bulundu"

#: pynicotine/shares.py:302
msgid "Rebuilding shares…"
msgstr "Paylaşımlar yeniden oluşturuluyor…"

#: pynicotine/shares.py:302
msgid "Rescanning shares…"
msgstr "Paylaşımlar yeniden taranıyor…"

#: pynicotine/shares.py:324
#, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Yeniden tarama tamamlandı: %(num)s klasör bulundu"

#: pynicotine/shares.py:334
#, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Paylaşımlar yeniden taranırken ciddi bir hata oluştu. Bu sorun devam ederse, "
"%(dir)s/*.db dosyalarını silin ve tekrar deneyin. Bu işe yaramazsa, lütfen "
"şu yığın geri izlemesini içeren bir hata bildirimi gönderin: %(trace)s"

#: pynicotine/shares.py:582
#, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "%(path)s dosyası taranırken hata oluştu: %(error)s"

#: pynicotine/shares.py:590
#, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "%(path)s klasörü taranırken hata oluştu: %(error)s"

#: pynicotine/shares.py:627
#, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "%(path)s dosyası için üst veriler taranırken hata oluştu: %(error)s"

#: pynicotine/shares.py:1046
#, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Kullanılamayan paylaşımlar nedeniyle yeniden tarama iptal edildi: %s"

#: pynicotine/shares.py:1184
#, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "%(user)s kullanıcısı paylaşılan dosyalar listenize göz atıyor"

#: pynicotine/slskmessages.py:3120
#, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Paylaşım veri tabanı okunamıyor. Lütfen paylaşımlarınızı yeniden tarayın. "
"Hata: %s"

#: pynicotine/slskproto.py:500
#, python-format
msgid "Specified network interface '%s' is not available"
msgstr "Belirtilen ağ arayüzü '%s' kullanılamıyor"

#: pynicotine/slskproto.py:511
#, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"%(port)s bağlantı noktasında dinlenemiyor. Başka bir uygulamanın "
"kullanmadığından emin olun veya farklı bir bağlantı noktası seçin. Hata: "
"%(error)s"

#: pynicotine/slskproto.py:523
#, python-format
msgid "Listening on port: %i"
msgstr "Dinlenen bağlantı noktası: %i"

#: pynicotine/slskproto.py:805
#, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "%(host)s:%(port)s sunucusuna bağlanılamıyor: %(error)s"

#: pynicotine/slskproto.py:1095
#, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Sunucuya %s saniye içinde yeniden bağlanılıyor"

#: pynicotine/slskproto.py:1170
#, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Bağlanılıyor: %(host)s:%(port)s"

#: pynicotine/slskproto.py:1208
#, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "%(host)s:%(port)s sunucusuna bağlanıldı, oturum açılıyor…"

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "%(host)s:%(port)s sunucusuyla bağlantı kesildi"

#: pynicotine/slskproto.py:1499
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Birisi başka bir yerden Soulseek hesabınızda oturum açtı"

#: pynicotine/uploads.py:382
#, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Yükleme tamamlandı: %(user)s kullanıcısı, %(ip)s IP adresi, %(file)s dosyası"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Yükleme iptal edildi: %(user)s kullanıcısı, %(file)s dosyası"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, python-format
msgid "Upload I/O error: %s"
msgstr "Yükleme G/Ç hatası: %s"

#: pynicotine/uploads.py:1103
#, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Yükleme başladı: %(user)s kullanıcısı, %(ip)s IP adresi, %(file)s dosyası"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr "'%(folder)s' dizini oluşturulamıyor, hata bildirildi: %(error)s"

#: pynicotine/userbrowse.py:236
#, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Paylaşımlar diskten yüklenemedi: %(error)s"

#: pynicotine/userbrowse.py:282
#, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr ""
"'%(user)s' kullanıcısı için paylaşılan dosyaların listesi %(dir)s konumuna "
"kaydedildi"

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr "Paylaşımlar kaydedilemiyor, '%(user)s', hata bildirildi: %(error)s"

#: pynicotine/userinfo.py:160
#, python-format
msgid "Picture saved to %s"
msgstr "Resim %s konumuna kaydedildi"

#: pynicotine/userinfo.py:163
#, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Resim %(filename)s konumuna kaydedilemiyor: %(error)s"

#: pynicotine/userinfo.py:190
#, python-format
msgid "User %(user)s is viewing your profile"
msgstr "%(user)s kullanıcısı profilinizi görüntülüyor"

#: pynicotine/users.py:272
#, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Sunucuya bağlanılamıyor. Neden: %s"

#: pynicotine/users.py:272
msgid "Cannot Connect"
msgstr "Bağlanamıyor"

#: pynicotine/users.py:306
#, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr ""
"Bu kullanıcı çevrim dışı olduğundan %s kullanıcısının IP adresi alınamıyor"

#: pynicotine/users.py:315
#, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr ""
"%(user)s kullanıcısının IP adresi: %(ip)s, bağlantı noktası "
"%(port)i%(country)s"

#: pynicotine/users.py:433
msgid "Soulseek Announcement"
msgstr "Soulseek Duyurusu"

#: pynicotine/users.py:449
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"Soulseek ayrıcalığınız yok. Ayrıcalıklar etkinken, indirmeleriniz "
"ayrıcalıklı olmayan kullanıcılarınkinden önce sıraya alınır."

#: pynicotine/users.py:455
#, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"%(days)i gün, %(hours)i saat, %(minutes)i dakika, %(seconds)i saniye "
"Soulseek ayrıcalığı kaldı"

#: pynicotine/users.py:473
msgid "Your password has been changed"
msgstr "Parolanız değiştirildi"

#: pynicotine/users.py:473
msgid "Password Changed"
msgstr "Parola Değiştirildi"

#: pynicotine/utils.py:574
#, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "%(path)s dosya yolu açılamıyor: %(error)s"

#: pynicotine/utils.py:621
#, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "%(url)s URL'si açılamıyor: %(error)s"

#: pynicotine/utils.py:646
#, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr "%(filename)s dosyası okunurken bir şeyler ters gitti: %(error)s"

#: pynicotine/utils.py:651
#, python-format
msgid "Attempting to load backup of file %s"
msgstr "%s dosyasının yedeği yüklenmeye çalışılıyor"

#: pynicotine/utils.py:673
#, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "%(path)s dosyası yedeklenemiyor: %(error)s"

#: pynicotine/utils.py:694
#, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "%(path)s dosyası kaydedilemiyor: %(error)s"

#: pynicotine/utils.py:705
#, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Önceki dosya %(path)s geri yüklenemiyor: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
msgid "Add buddy…"
msgstr "Arkadaş ekle…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
msgid "Toggle Text-to-Speech"
msgstr "Metinden Konuşmayı Aç/Kapat"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
msgid "Chat Room Command Help"
msgstr "Sohbet Odası Komut Yardımı"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
msgid "_Log"
msgstr "Gün_lük"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
msgid "Room Wall"
msgstr "Oda Duvarı"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
msgid "R_oom Wall"
msgstr "_Oda duvarı"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
msgid "Created by"
msgstr "Oluşturan"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
msgid "Translated by"
msgstr "Çeviren"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
msgid "License"
msgstr "Lisans"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
msgid "Welcome to Nicotine+"
msgstr "Nicotine+'a Hoş Geldiniz"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr "İstediğiniz kullanıcı adı zaten alınmışsa, değiştirmeniz istenecektir."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""
"Diğer Soulseek eşleriyle bağlantı kurmak için, yönlendiricinizdeki dinleyen "
"bir bağlantı noktasının bilgisayarınıza yönlendirilmesi gerekir."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Dinleme bağlantı noktanız kapalıysa, yalnızca dinleme bağlantı noktaları "
"açık olan kullanıcılara bağlanabilirsiniz."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"Gerekirse aşağıdan farklı bir dinleme bağlantı noktası seçin. Bu, daha sonra "
"tercihlerde de yapılabilir."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
msgid "Download Files to Folder"
msgstr "Dosyaları Klasöre İndir"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
msgid "Share Folders"
msgstr "Klasörleri Paylaş"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Soulseek kullanıcıları paylaşımlarınızdan indirebilecekler. Kendi "
"dosyalarınızı paylaşarak ve diğer kullanıcılardan indirdiklerinizi yeniden "
"paylaşarak Soulseek ağına katkıda bulunun."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
msgid "You are ready to use Nicotine+!"
msgstr "Nicotine+ kullanmaya hazırsınız!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""
"Soulseek güvenli iletişim için tasarlanmamış şifreli olmayan bir protokoldür."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Soulseek'e bağış yapmak size belirli bir süre için ayrıcalıklar verir. "
"Ayrıcalıklarınız varsa, indirmeleriniz ayrıcalıklı olmayan kullanıcılardan "
"önce kuyruğa alınacaktır."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
msgid "Previous File"
msgstr "Önceki Dosya"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
msgid "Next File"
msgstr "Sonraki Dosya"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
msgid "Name"
msgstr "Ad"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
msgid "Last Speed"
msgstr "Son Hız"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
msgid "_Export…"
msgstr "_Dışa Aktar…"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
msgid "Keyboard Shortcuts"
msgstr "Klavye Kısayolları"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
msgid "General"
msgstr "Genel"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr "Bağlan"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr "Bağlantıyı Kes"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
msgid "Rescan Shares"
msgstr "Paylaşımları Yeniden Tara"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
msgid "Show Log Pane"
msgstr "Günlük Bölmesini Göster"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
msgid "Confirm Quit"
msgstr "Çıkmayı Onayla"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
msgid "Quit"
msgstr "Çıkış"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
msgid "Menus"
msgstr "Menüler"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
msgid "Open Main Menu"
msgstr "Ana Menüyü Aç"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
msgid "Open Context Menu"
msgstr "Bağlam Menüsünü Aç"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
msgid "Tabs"
msgstr "Sekmeler"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
msgid "Change Main Tab"
msgstr "Ana Sekmeyi Değiştir"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
msgid "Go to Previous Secondary Tab"
msgstr "Önceki İkincil Sekmeye Git"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
msgid "Go to Next Secondary Tab"
msgstr "Sonraki İkincil Sekmeye Git"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
msgid "Reopen Closed Secondary Tab"
msgstr "Kapatılan İkincil Sekmeyi Yeniden Aç"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
msgid "Close Secondary Tab"
msgstr "İkincil Sekmeyi Kapat"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
msgid "Lists"
msgstr "Listeler"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
msgid "Copy Selected Cell"
msgstr "Seçili Hücreyi Kopyala"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
msgid "Select All"
msgstr "Tümünü Seç"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
msgid "Find"
msgstr "Bul"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
msgid "Remove Selected Row"
msgstr "Seçili Satırı Kaldır"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
msgid "Editing"
msgstr "Düzenleme"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
msgid "Cut"
msgstr "Kes"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
msgid "Paste"
msgstr "Yapıştır"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
msgid "Insert Emoji"
msgstr "Emoji Ekle"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
msgid "File Transfers"
msgstr "Dosya Aktarımları"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
msgid "Resume / Retry Transfer"
msgstr "Aktarımı Devam Ettir / Yeniden Dene"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
msgid "Pause / Abort Transfer"
msgstr "Aktarımı Duraklat / İptal Et"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
msgid "Download / Upload To"
msgstr "Şuraya İndir / Yükle"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
msgid "Save List to Disk"
msgstr "Listeyi Diske Kaydet"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
msgid "Refresh"
msgstr "Yenile"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
msgid "Expand / Collapse All"
msgstr "Tümünü Genişlet / Daralt"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
msgid "Back to Parent Folder"
msgstr "Üst Klasöre Geri Dön"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
msgid "File Search"
msgstr "Dosya Arama"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
msgid "Result Filters"
msgstr "Sonuç Filtreleri"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
msgid "Current Session"
msgstr "Geçerli Oturum"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
msgid "Completed Downloads"
msgstr "Tamamlanan İndirmeler"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
msgid "Downloaded Size"
msgstr "İndirmelerin Boyutu"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
msgid "Completed Uploads"
msgstr "Tamamlanan Yüklemeler"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
msgid "Uploaded Size"
msgstr "Yüklemelerin Boyutu"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
msgid "Total"
msgstr "Toplam"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
msgid "_Reset…"
msgstr "_Sıfırla…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Dilek listesi ögeleri, olağan dışı dosyaları keşfetmek için düzenli "
"aralıklarla otomatik olarak aranır."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
msgid "Add Wish…"
msgstr "Dilek Ekle…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
msgid "Clear All…"
msgstr "Tümünü Temizle…"

#: pynicotine/gtkgui/ui/downloads.ui:138
msgid "Clear All Finished/Filtered Downloads"
msgstr "Tüm tamamlanan ve filtrelenen indirmeleri temizle"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
msgid "Clear Finished"
msgstr "Tamamlananları Temizle"

#: pynicotine/gtkgui/ui/downloads.ui:169
msgid "Clear Specific Downloads"
msgstr "Belirli İndirmeleri Temizle"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
msgid "Clear _All…"
msgstr "Tümünü _Temizle…"

#: pynicotine/gtkgui/ui/interests.ui:21
msgid "Personal Interests"
msgstr "Kişisel İlgi Alanları"

#: pynicotine/gtkgui/ui/interests.ui:40
msgid "Add something you like…"
msgstr "Sevdiğiniz bir şey ekleyin…"

#: pynicotine/gtkgui/ui/interests.ui:62
msgid "Personal Dislikes"
msgstr "Kişisel Hoşnutsuzluklar"

#: pynicotine/gtkgui/ui/interests.ui:80
msgid "Add something you dislike…"
msgstr "Sevmediğiniz bir şey ekleyin…"

#: pynicotine/gtkgui/ui/interests.ui:143
msgid "Refresh Recommendations"
msgstr "Tavsiyeler Listesini Yenile"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
msgid "Main Menu"
msgstr "Ana Menü"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
msgid "Room…"
msgstr "Oda…"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
msgid "Username…"
msgstr "Kullanıcı adı…"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
msgid "Search term…"
msgstr "Arama terimi…"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Temizle"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Arama kalıpları: bir sözcük ile = terim, bir sözcük olmadan = -terim, kısmi "
"sözcük = *erim"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
msgid "Search Scope"
msgstr "Arama Kapsamı"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
msgid "_Wishlist"
msgstr "_Dilek Listesi"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
msgid "Configure Searches"
msgstr "Aramaları Yapılandır"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Soulseek ağındaki diğer kullanıcılar tarafından paylaşılan dosyaları aramak "
"için bir arama terimi girin"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
msgid "File Grouping Mode"
msgstr "Dosya Gruplama Modu"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
msgid "Configure Downloads"
msgstr "İndirmeleri Yapılandır"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Diğer kullanıcılardan indirdiğiniz dosyalar burada kuyruğa alınır ve "
"istendiğinde duraklatılıp devam ettirilebilir"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
msgid "Configure Uploads"
msgstr "Yüklemeleri Yapılandır"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Kullanıcıların paylaşılan dosyalarınızı indirme girişimleri burada kuyruğa "
"alınır ve yönetilir"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
msgid "_Open List"
msgstr "Listeyi _Aç"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Daha önce diske kaydedilen paylaşılan dosyaların yerel bir listesini açar"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
msgid "Configure Shares"
msgstr "Paylaşımları Yapılandır"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Paylaşılan dosyalarına göz atmak istediğiniz kullanıcının adını girin. "
"Ayrıca listeyi diske kaydedebilir ve daha sonra inceleyebilirsiniz."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
msgid "_Personal Profile"
msgstr "Kişisel _Profil"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
msgid "Configure Account"
msgstr "Hesabı Yapılandır"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Kullanıcı açıklamasını, bilgilerini ve kişisel resmini görüntülemek için bir "
"kullanıcının adını girin"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
msgid "Chat _History"
msgstr "Sohbet _Geçmişi"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
msgid "Configure Chats"
msgstr "Sohbetleri Yapılandır"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr ""
"Onlarla özel olarak bir yazılı konuşma başlatmak için bir kullanıcının adını "
"girin"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
msgid "_Message All"
msgstr "Tümüne _Mesaj Gönder"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
msgid "Configure Ignored Users"
msgstr "Yok Sayılan Kullanıcıları Yapılandır"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Belirli klasörleri onlarla paylaşmak ve çevrim içi olduklarında bildirim "
"almak için kullanıcıları arkadaş olarak ekleyin"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
msgid "Join or create room…"
msgstr "Katılın veya oda oluşturun…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Soulseek ağındaki diğer kullanıcılarla sohbet etmek için mevcut bir sohbet "
"odasına katılın veya yeni bir oda oluşturun"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
msgid "Configure User Profile"
msgstr "Kullanıcı Profilini Yapılandır"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
msgid "Connections"
msgstr "Bağlantılar"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
msgid "Downloading (Speed / Active Users)"
msgstr "İndirme (Hız / Etkin Kullanıcılar)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
msgid "Uploading (Speed / Active Users)"
msgstr "Yükleme (Hız / Etkin Kullanıcılar)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
msgid "Search chat history…"
msgstr "Sohbet geçmişinde ara…"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
msgid "Download Speed Limits"
msgstr "İndirme Hızı Sınırları"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
msgid "Unlimited download speed"
msgstr "Sınırsız indirme hızı"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
msgid "Use download speed limit (KiB/s):"
msgstr "İndirme hızı sınırı kullan (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Alternatif indirme hızı sınırı kullan (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
msgid "Search rooms…"
msgstr "Odalarda ara…"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
msgid "Refresh Rooms"
msgstr "Odaları Yenile"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
msgid "_Show feed of public chat room messages"
msgstr "Herkese açık sohbet odası mesajlarının akışını _göster"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
msgid "_Accept private room invitations"
msgstr "Özel oda davetlerini _kabul et"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Diğer oda kullanıcılarının daha sonra okuyabileceği bir mesaj yazın. Son "
"mesajlar en üstte gösterilir."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
msgid "Set wall message…"
msgstr "Duvar mesajı ayarla…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
msgid "Search Result Filters"
msgstr "Arama Sonucu Filtreleri"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Arama sonucu filtreleri, hangi arama sonuçlarının görüntüleneceğini "
"belirlemek için kullanılır."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Her arama sonucu listesinin, Sonuç Filtreleri düğmesi değiştirilerek "
"gösterilebilen kendi filtresi vardır. Bir filtre, birden çok alandan oluşur "
"ve bunların tümü, alanlarından herhangi birinde Enter tuşuna basıldığında "
"uygulanır. Filtreleme, zaten alınmış olan sonuçlara ve henüz alınmayan "
"sonuçlara hemen uygulanır."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Adından da anlaşılacağı gibi, bir arama sonucu filtresi orijinal aramanızı "
"genişletemez, yalnızca daraltabilir. Arama terimlerinizi genişletmek veya "
"değiştirmek için yeni bir arama yapın."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
msgid "Result Filter Usage"
msgstr "Sonuç Filtresi Kullanımı"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
msgid "Include Text"
msgstr "Metni Dahil Et"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
msgid "Files, folders and usernames containing this text will be shown."
msgstr ""
"Bu metni içeren dosyalar, klasörler ve kullanıcı adları gösterilecektir."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Büyük/küçük harfe duyarlı değildir, ancak sözcük sırası önemlidir: "
"'Instrumental Remix' herhangi bir 'Remix Instrumental' göstermeyecektir"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Birkaç tam ifadeyi ayırmak için | (veya boru karakteri) kullanın. Örnek:\n"
"    Remix|Dub Mix|Instrumental"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
msgid "Exclude Text"
msgstr "Metni Hariç Tut"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"Yukarıdaki gibi, ancak metin eşleşirse dosyalar, klasörler ve kullanıcı "
"adları filtrelenir."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
msgid "Filters files based upon their file extension."
msgstr "Dosyaları dosya uzantılarına göre filtreler."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Birden fazla dosya uzantısı belirtilebilir, bu da sonuç listesinden daha "
"fazlasını gösterecektir. Örnek:\n"
"    flac wav ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"Sonuçlarınızda istemediğiniz dosya uzantılarını bir ünlem işaretiyle "
"belirterek filtreyi tersine çevirmek de mümkündür! Örnek:\n"
"    !mp3 !jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
msgid "File Size"
msgstr "Dosya Boyutu"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
msgid "Filters files based upon their file size."
msgstr "Dosyaları, dosya boyutlarına göre filtreler."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"Öntanımlı olarak, kullanılan birim bayttır (B) ve değere eşit veya daha "
"büyük (>=) dosyalar eşleşecektir."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Bayt, kibibayt, mebibayt veya gibibayt birimlerini belirtmek için b, k, m "
"veya g (alternatif olarak kib, mib veya gib) ekleyin: \n"
"    20 MiB (mebibayt)'tan daha büyük dosyaları göstermek için 20m girin."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Tam bir eşleşme belirtmek için bir değerin başına = ekleyin:\n"
"    =1024, yalnızca boyutu 1 KiB (kibibayt) olan dosyalarla eşleşir."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Belirli bir boyuttaki dosyaları hariç tutmak için bir değerin başına ! "
"ekleyin: \n"
"    !30.5m, boyutu 30.5 MiB (mebibayt) olan dosyaları hariç tutacaktır."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Verilen değerden daha küçük/büyük dosyaları bulmak için < veya > ekleyin. "
"Bir aralık eklemek için her koşul arasında bir boşluk kullanın:\n"
"    >10.5m <1g, 10,5 MiB'den büyük ancak 1 GiB'den küçük dosyaları gösterir."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Daha iyi bilinen kb, mb ve gb çeşitleri kilobayt, megabayt ve gigabayt "
"birimleri için de kullanılabilir."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
msgid "Filters files based upon their bitrate."
msgstr "Dosyaları bit hızlarına göre filtreler."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Değerler yalnızca sayısal basamak olarak girilmelidir. Birim her zaman Kb/"
"s'dir (Kilobit/saniye)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Dosya boyutunda (yukarıda) olduğu gibi =, !, <, >, <= veya >= operatörleri "
"kullanılabilir ve örneğin en fazla 1411 Kb/s bit hızıyla en az 256 Kb/s bit "
"hızına sahip dosyaları göstermek için birden fazla koşul belirtilebilir:\n"
"    256 <=1411"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
msgid "Filters files based upon their duration."
msgstr "Dosyaları sürelerine göre filtreler."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"Öntanımlı olarak, bir operatör (=, !, < veya >) kullanılmadığı sürece, "
"girilen süreye eşit veya daha uzun (>=) dosyalar eşleştirilecektir."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Saniye cinsinden ham bir değer girin veya DD:SS ve SS:DD:SS zaman "
"biçimlerini kullanın:\n"
"    =53 yaklaşık 53 saniye uzunluğundaki dosyaları gösterir.\n"
"    >5:30 5 buçuk dakikadan uzun dosyaları gösterir.\n"
"    <5:30:00 5 buçuk saatten kısa dosyaları gösterir."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Birden fazla koşul belirtilebilir:\n"
"    >6:00 <12:00 6 ila 12 dakika uzunluğundaki dosyaları gösterir.\n"
"    !9:54 !8:43 !7:32 belirtilen dosyaları sonuçlardan gizler.\n"
"    =5:34 =4:23 =3:05 belirtilen sürelere sahip dosyaları dahil eder."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"ISO 3166-2 tarafından tanımlanan ülke kodlarına göre kullanıcıların coğrafi "
"konumlarına göre dosyaları filtreler:\n"
"    US yalnızca Amerika Birleşik Devletleri'ndeki IP adreslerine sahip "
"kullanıcılardan gelen sonuçları gösterecektir.\n"
"    !GB Büyük Britanya'daki kullanıcılardan gelen sonuçları gizleyecektir."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Birden fazla ülke virgül veya boşluk kullanılarak belirtilebilir."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
msgid "Free Slot"
msgstr "Boş Yuva"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Yalnızca en az bir yükleme yuvası boş olan kullanıcılardan gelen sonuçları, "
"yani hemen kullanılabilir olan dosyaları göster."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
msgid "Upload Speed Limits"
msgstr "Yükleme Hızı Sınırları"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
msgid "Unlimited upload speed"
msgstr "Sınırsız yükleme hızı"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
msgid "Use upload speed limit (KiB/s):"
msgstr "Yükleme hızı sınırı kullan (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Alternatif yükleme hızı sınırı kullan (KiB/s):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
msgid "Private Chat Command Help"
msgstr "Özel Sohbet Komutu Yardımı"

#: pynicotine/gtkgui/ui/search.ui:7
msgid "Include text…"
msgstr "Metni dahil et…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Dosya yolları belirtilen metni içeren sonuçları göster. Birden fazla ifade "
"ve sözcük belirtilebilir, örn. tam ifade|müzik|terim|ikinci tam ifade"

#: pynicotine/gtkgui/ui/search.ui:18
msgid "Exclude text…"
msgstr "Metni hariç tut…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Dosya yolları belirtilen metni içeren sonuçları gösterme. Birden fazla ifade "
"ve sözcük belirtilebilir, örn. tam ifade|müzik|terim|ikinci tam ifade"

#: pynicotine/gtkgui/ui/search.ui:29
msgid "File type…"
msgstr "Dosya türü…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Dosya türü, örneğin flac wav veya !mp3 !m4a"

#: pynicotine/gtkgui/ui/search.ui:40
msgid "File size…"
msgstr "Dosya boyutu…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
msgid "File size, e.g. >10.5m <1g"
msgstr "Dosya boyutu, örneğin >10.5m <1g"

#: pynicotine/gtkgui/ui/search.ui:51
msgid "Bitrate…"
msgstr "Bit hızı…"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
msgid "Bitrate, e.g. 256 <1412"
msgstr "Bit hızı, örneğin 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
msgid "Duration…"
msgstr "Süre…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Süre, örneğin >6:00 <12:00 !6:54"

#: pynicotine/gtkgui/ui/search.ui:73
msgid "Country code…"
msgstr "Ülke kodu…"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Ülke kodu, örneğin US ES veya !DE !GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Kullanıcı adı, IP adresi veya ülkeye göre kullanıcıların paylaşılan "
"dosyalarınıza erişmesini yasaklayın."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Engellenecek ülke kodları (virgülle ayrılmış):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
msgid "Codes must be in ISO 3166-2 format."
msgstr "Kodlar ISO 3166-2 biçiminde olmalıdır."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
msgid "Use custom geo block message:"
msgstr "Özel coğrafi engelleme mesajı kullan:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Özel yasaklama mesajı kullan:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
msgid "IP Addresses"
msgstr "IP Adresleri"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
msgid "Restore previously open private chats on startup"
msgstr "Önceden açık olan özel sohbetleri başlangıçta geri yükle"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
msgid "Enable spell checker"
msgstr "Yazım denetleyicisini etkinleştir"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
msgid "Enable CTCP-like private message responses (client version)"
msgstr "CTCP benzeri özel mesaj yanıtlarını etkinleştir (istemci sürümü)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
msgid "Number of recent private chat messages to show:"
msgstr "Gösterilecek son özel sohbet mesajlarının sayısı:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
msgid "Number of recent chat room messages to show:"
msgstr "Gösterilecek son sohbet odası mesajlarının sayısı:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
msgid "Chat Completion"
msgstr "Sohbeti Tamamla"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
msgid "Enable tab-key completion"
msgstr "Tab tuşuyla tamamlamayı etkinleştir"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
msgid "Enable completion drop-down list"
msgstr "Tamamlama açılır listesini etkinleştir"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
msgid "Minimum characters required to display drop-down:"
msgstr "Açılır menüyü görüntülemek için gereken en az karakter:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
msgid "Allowed chat completions:"
msgstr "İzin verilen sohbet tamamlamaları:"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
msgid "Buddy names"
msgstr "Arkadaş adları"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
msgid "Chat room usernames"
msgstr "Sohbet odası kullanıcı adları"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
msgid "Room names"
msgstr "Oda adları"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
msgid "Commands"
msgstr "Komutlar"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
msgid "Timestamps"
msgstr "Zaman Damgaları"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
msgid "Private chat format:"
msgstr "Özel sohbet biçimi:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "Öntanımlı"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
msgid "Chat room format:"
msgstr "Sohbet odası biçimi:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
msgid "Text-to-Speech"
msgstr "Metinden Konuşmaya"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
msgid "Enable Text-to-Speech"
msgstr "Metinden Konuşmayayı Etkinleştir"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
msgid "Text-to-Speech command:"
msgstr "Metinden konuşmaya komutu:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
msgid "Private chat message:"
msgstr "Özel sohbet mesajı:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
msgid "Chat room message:"
msgstr "Sohbet odası mesajı:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
msgid "Censor"
msgstr "Sansürle"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
msgid "Enable censoring of text patterns"
msgstr "Metin kalıplarının sansürlenmesini etkinleştir"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
msgid "Auto-Replace"
msgstr "Otomatik Değiştir"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
msgid "Enable automatic replacement of words"
msgstr "Sözcüklerin otomatik olarak değiştirilmesini etkinleştir"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr ""
"Aktarım listesinden tamamlanan/filtrelenen indirmeleri otomatik olarak "
"temizle"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
msgid "Store completed downloads in username subfolders"
msgstr "Tamamlanan indirmeleri kullanıcı adı alt klasörlerinde depola"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
msgid "Double-click action for downloads:"
msgstr "İndirmeler için çift tıklama eylemi:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
msgid "Allow users to send you any files:"
msgstr "Kullanıcıların size herhangi bir dosya göndermesine izin verin:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
msgid "Folders"
msgstr "Klasörler"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
msgid "Finished downloads:"
msgstr "Tamamlanan indirmeler:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
msgid "Incomplete downloads:"
msgstr "Tamamlanmayan indirmeler:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
msgid "Received files:"
msgstr "Alınan dosyalar:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "Olaylar"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
msgid "Run command after file download finishes ($ for file path):"
msgstr ""
"Dosya indirme işlemi tamamlandıktan sonra komutu çalıştır (dosya yolu için "
"$):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"Klasör indirme işlemi tamamlandıktan sonra komutu çalıştır (klasör yolu için "
"$):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
msgid "Download Filters"
msgstr "İndirme Filtreleri"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
msgid "Enable download filters"
msgstr "İndirme filtrelerini etkinleştir"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Ekle"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Öntanımlıları Yükle"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
msgid "Verify Filters"
msgstr "Filtreleri Doğrula"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
msgid "Unverified"
msgstr "Doğrulanmadı"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Kullanıcı adı veya IP adresine göre kullanıcılardan gelen sohbet mesajlarını "
"ve arama sonuçlarını yok say."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Öntanımlı olarak sohbet odalarını günlüğe kaydet"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Öntanımlı olarak özel sohbeti günlüğe kaydet"

#: pynicotine/gtkgui/ui/settings/log.ui:142
msgid "Log transfers to file"
msgstr "Aktarımların günlüğünü dosyaya kaydet"

#: pynicotine/gtkgui/ui/settings/log.ui:166
msgid "Log debug messages to file"
msgstr "Hata ayıklama mesajlarının günlüğünü dosyaya kaydet"

#: pynicotine/gtkgui/ui/settings/log.ui:190
msgid "Log timestamp format:"
msgstr "Günlük zaman damgası biçimi:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
msgid "Folder Locations"
msgstr "Klasör Konumları"

#: pynicotine/gtkgui/ui/settings/log.ui:245
msgid "Chatroom logs folder:"
msgstr "Sohbet odası günlükleri klasörü:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
msgid "Private chat logs folder:"
msgstr "Özel sohbet günlükleri klasörü:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
msgid "Transfer logs folder:"
msgstr "Aktarım günlükleri klasörü:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
msgid "Debug logs folder:"
msgstr "Hata ayıklama günlükleri klasörü:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Mevcut bir Soulseek hesabıyla oturum açın veya yeni bir tane oluşturun. "
"Kullanıcı adları büyük/küçük harfe duyarlıdır ve benzersizdir."

#: pynicotine/gtkgui/ui/settings/network.ui:118
msgid "Public IP address:"
msgstr "Genel IP adresi:"

#: pynicotine/gtkgui/ui/settings/network.ui:159
msgid "Listening port:"
msgstr "Dinleme bağlantı noktası:"

#: pynicotine/gtkgui/ui/settings/network.ui:185
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Dinleme bağlantı noktasını otomatik olarak ilet (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
msgid "Away Status"
msgstr "Uzakta Durumu"

#: pynicotine/gtkgui/ui/settings/network.ui:228
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr ""
"Uzakta olarak belirtilecek dakika cinsinden hareketsizlik süresi (devre dışı "
"bırakmak için 0):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
msgid "Auto-reply message when away:"
msgstr "Uzaktayken otomatik yanıt mesajı:"

#: pynicotine/gtkgui/ui/settings/network.ui:299
msgid "Auto-connect to server on startup"
msgstr "Başlangıçta sunucuya otomatik bağlan"

#: pynicotine/gtkgui/ui/settings/network.ui:323
msgid "Soulseek server:"
msgstr "Soulseek sunucusu:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Bağlantıları belirli bir ağ arayüzüne bağlar, örn. her zaman bir VPN'nin "
"kullanılmasını sağlamak için kullanışlıdır. Kullanılabilir herhangi bir "
"arayüzü kullanmak için boş bırakın. Bu değeri yalnızca ne yaptığınızı "
"biliyorsanız değiştirin."

#: pynicotine/gtkgui/ui/settings/network.ui:351
msgid "Network interface:"
msgstr "Ağ arayüzü:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"Şimdi Oynatılıyor, sohbette /now komutunu kullanarak ortam oynatıcınızın ne "
"çaldığını görüntülemenizi sağlar."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
msgid "Other"
msgstr "Diğer"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
msgid "Now Playing Format"
msgstr "Şimdi Oynatılıyor Biçimi"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
msgid "Now Playing message format:"
msgstr "Şimdi Oynatılıyor mesaj biçimi:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
msgid "Test Configuration"
msgstr "Yapılandırmayı Test Et"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
msgid "Enable plugins"
msgstr "Eklentileri etkinleştir"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
msgid "Add Plugins"
msgstr "Eklenti Ekle"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
msgid "_Add Plugins"
msgstr "_Eklenti Ekle"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
msgid "Settings"
msgstr "Ayarlar"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
msgid "_Settings"
msgstr "_Ayarlar"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
msgid "Version:"
msgstr "Sürüm:"

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
msgid "Created by:"
msgstr "Oluşturan:"

#: pynicotine/gtkgui/ui/settings/search.ui:51
msgid "Enable search history"
msgstr "Arama geçmişini etkinleştir"

#: pynicotine/gtkgui/ui/settings/search.ui:70
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Özel olarak paylaşılan ve herkes tarafından görülebilen dosyaların önüne "
"'[PRIVATE]' eklenir ve yükleyen kişi açık izin verene kadar indirilemez. Bu "
"kişilerden nazikçe isteyin."

#: pynicotine/gtkgui/ui/settings/search.ui:76
msgid "Show privately shared files in search results"
msgstr "Arama sonuçlarında özel olarak paylaşılan dosyaları göster"

#: pynicotine/gtkgui/ui/settings/search.ui:106
msgid "Limit number of results per search:"
msgstr "Arama başına sonuç sayısını sınırla:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
msgid "Result Filter Help"
msgstr "Sonuç Filtresi Yardımı"

#: pynicotine/gtkgui/ui/settings/search.ui:177
msgid "Enable search result filters by default"
msgstr "Öntanımlı olarak arama sonucu filtrelerini etkinleştir"

#: pynicotine/gtkgui/ui/settings/search.ui:211
msgid "Include:"
msgstr "Dahil Et:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
msgid "Exclude:"
msgstr "Hariç Tut:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
msgid "File Type:"
msgstr "Dosya Türü:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "Boyut:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Bit Hızı:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
msgid "Duration:"
msgstr "Süre:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
msgid "Country Code:"
msgstr "Ülke Kodu:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
msgid "Only show results from users with an available upload slot."
msgstr ""
"Yalnızca kullanılabilir bir yükleme yuvasına sahip kullanıcılardan gelen "
"sonuçları göster."

#: pynicotine/gtkgui/ui/settings/search.ui:430
msgid "Network Searches"
msgstr "Ağ Aramaları"

#: pynicotine/gtkgui/ui/settings/search.ui:452
msgid "Respond to search requests from other users"
msgstr "Diğer kullanıcılardan gelen arama isteklerine yanıt ver"

#: pynicotine/gtkgui/ui/settings/search.ui:486
msgid "Searches shorter than this number of characters will be ignored:"
msgstr "Bu karakter sayısından daha kısa aramalar yok sayılacaktır:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
msgid "Maximum search results to send per search request:"
msgstr "Arama isteği başına gönderilecek en fazla arama sonucu:"

#: pynicotine/gtkgui/ui/settings/search.ui:578
msgid "Clear Search History"
msgstr "Arama Geçmişini Temizle"

#: pynicotine/gtkgui/ui/settings/search.ui:626
msgid "Clear Filter History"
msgstr "Filtre Geçmişini Temizle"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Başlangıçta paylaşılan klasörlerinizin içeriğini otomatik olarak yeniden "
"tarar. Devre dışı bırakılırsa, paylaşımlarınız yalnızca elle yeniden tarama "
"başlattığınızda güncellenir."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Başlangıçta paylaşımları yeniden tara"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr "Herkes tarafından görülebilir:"

#: pynicotine/gtkgui/ui/settings/shares.ui:82
msgid "Buddy shares"
msgstr "Arkadaş paylaşımları"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
msgid "Trusted shares"
msgstr "Güvenilir paylaşımlar"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr ""
"Tamamlanan/iptal edilen yüklemeleri aktarım listesinden otomatik olarak "
"temizle"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
msgid "Double-click action for uploads:"
msgstr "Yüklemeler için çift tıklama eylemi:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
msgid "Limit upload speed:"
msgstr "Yükleme hızını sınırla:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
msgid "Per transfer"
msgstr "Aktarım başına"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
msgid "Total transfers"
msgstr "Toplam aktarımlar"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
msgid "Upload Slots"
msgstr "Yükleme Yuvaları"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Herkes Sırayla: Dosyalar, kuyrukta bekleyen kullanıcılara çevrimsel bir "
"sırayla yüklenecektir.\n"
"İlk Giren İlk Çıkar: Dosyalar kuyruğa alındıkları sırayla yüklenecektir."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
msgid "Upload queue type:"
msgstr "Yükleme kuyruğu türü:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr "Toplam hız bu değere ulaşana kadar yükleme yuvaları tahsis et (KiB/s):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
msgid "Fixed number of upload slots:"
msgstr "Sabit yükleme yuvası sayısı:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
msgid "Prioritize all buddies"
msgstr "Tüm arkadaşlara öncelik ver"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
msgid "Queue Limits"
msgstr "Kuyruk Sınırları"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
msgid "Maximum number of queued files per user:"
msgstr "Kullanıcı başına kuyruğa alınan en fazla dosya sayısı:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr "Kullanıcı başına kuyruğa alınan en yüksek toplam dosya boyutu (MiB):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
msgid "Limits do not apply to buddies"
msgstr "Sınırlar arkadaşlar için geçerli değildir"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"$ karakterleri URL ile değiştirilecektir. Bir protokolün yapılandırılmadığı "
"durumlarda öntanımlı sistem uygulamaları kullanılır."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
msgid "File manager command:"
msgstr "Dosya yöneticisi komutu:"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
msgid "Reset Picture"
msgstr "Resmi Sıfırla"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
msgid "Self Description"
msgstr "Kendini Tanıtma"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""
"Kısa bir açıklama, faydalı ipuçları veya paylaşımlarınızı indirmek için "
"talimatlar gibi herkesin görmesini istediğiniz şeyler ekleyin."

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
msgid "Picture:"
msgstr "Resim:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
msgid "Prefer dark mode"
msgstr "Karanlık modu tercih et"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
msgid "Use header bar"
msgstr "Başlık çubuğu kullan"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
msgid "Display tray icon"
msgstr "Tepsi simgesini görüntüle"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
msgid "Minimize to tray on startup"
msgstr "Başlangıçta tepsi simgesine küçült"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
msgid "Language (requires a restart):"
msgstr "Dil (yeniden başlatma gerektirir):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
msgid "When closing window:"
msgstr "Pencereyi kapatırken:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
msgid "Notifications"
msgstr "Bildirimler"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
msgid "Enable sound for notifications"
msgstr "Bildirimler için sesi etkinleştir"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
msgid "Show notification for private chats and mentions in the window title"
msgstr "Pencere başlığında özel sohbetler ve bahsetmeler için bildirim göster"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
msgid "Show notifications for:"
msgstr "Şunlar için bildirimleri göster:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
msgid "Finished file downloads"
msgstr "Biten dosya indirmeleri"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
msgid "Finished folder downloads"
msgstr "Biten klasör indirmeleri"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
msgid "Private messages"
msgstr "Özel mesajlar"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
msgid "Chat room messages"
msgstr "Sohbet odası mesajları"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
msgid "Chat room mentions"
msgstr "Sohbet odasında bahsedilmeler"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
msgid "Wishlist results found"
msgstr "İstek listesi sonuçları bulundu"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
msgid "Restore the previously active main tab at startup"
msgstr "Önceden etkin olan ana sekmeyi başlangıçta geri yükle"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
msgid "Close-buttons on secondary tabs"
msgstr "İkincil sekmelerdeki kapatma düğmeleri"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
msgid "Regular tab label color:"
msgstr "Normal sekme etiketi rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
msgid "Changed tab label color:"
msgstr "Değiştirilen sekme etiketi rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
msgid "Highlighted tab label color:"
msgstr "Vurgulanan sekme etiketi rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr "Arkadaş listesi konumu:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
msgid "Visible main tabs:"
msgstr "Görünür ana sekmeler:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
msgid "Tab bar positions:"
msgstr "Sekme çubuğu konumları:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
msgid "Main tabs"
msgstr "Ana sekmeler"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
msgid "Show reverse file paths (requires a restart)"
msgstr "Ters dosya yollarını göster (yeniden başlatma gerektirir)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
msgid "Show exact file sizes (requires a restart)"
msgstr "Tam dosya boyutlarını göster (yeniden başlatma gerektirir)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
msgid "List text color:"
msgstr "Liste metni rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
msgid "Enable colored usernames"
msgstr "Renkli kullanıcı adlarını etkinleştir"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
msgid "Chat username appearance:"
msgstr "Sohbet kullanıcı adı görünümü:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
msgid "Remote text color:"
msgstr "Uzak metin rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
msgid "Local text color:"
msgstr "Yerel metin rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
msgid "Command output text color:"
msgstr "Komut çıktısı metin rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
msgid "/me action text color:"
msgstr "/me eylem metni rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
msgid "Highlighted text color:"
msgstr "Vurgulanan metin rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
msgid "URL link text color:"
msgstr "URL bağlantı metni rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
msgid "User Statuses"
msgstr "Kullanıcı Durumları"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
msgid "Online color:"
msgstr "Çevrim içi rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
msgid "Away color:"
msgstr "Uzakta rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
msgid "Offline color:"
msgstr "Çevrim dışı rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
msgid "Text Entries"
msgstr "Metin Girdileri"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
msgid "Text entry background color:"
msgstr "Metin girdisi arka plan rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
msgid "Text entry text color:"
msgstr "Metin girdisi metin rengi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
msgid "Fonts"
msgstr "Yazı Tipleri"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
msgid "Global font:"
msgstr "Genel yazı tipi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
msgid "List font:"
msgstr "Liste yazı tipi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
msgid "Text view font:"
msgstr "Metin görünümü yazı tipi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
msgid "Chat font:"
msgstr "Sohbet yazı tipi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
msgid "Transfers font:"
msgstr "Aktarımlar yazı tipi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
msgid "Search font:"
msgstr "Arama yazı tipi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
msgid "Browse font:"
msgstr "Göz atma yazı tipi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
msgid "Icons"
msgstr "Simgeler"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
msgid "Icon theme folder:"
msgstr "Simge teması klasörü:"

#: pynicotine/gtkgui/ui/uploads.ui:86
msgid "Abort User(s)"
msgstr "Kullanıcı(lar)ı İptal Et"

#: pynicotine/gtkgui/ui/uploads.ui:116
msgid "Ban User(s)"
msgstr "Kullanıcı(lar)ı Yasakla"

#: pynicotine/gtkgui/ui/uploads.ui:138
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Tamamlanan ve iptal edilen tüm yüklemeleri temizle"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
msgid "Message All"
msgstr "Tümüne Mesaj Gönder"

#: pynicotine/gtkgui/ui/uploads.ui:199
msgid "Clear Specific Uploads"
msgstr "Belirli Yüklemeleri Temizle"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
msgid "Save Shares List to Disk"
msgstr "Paylaşım Listesini Diske Kaydet"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
msgid "Refresh Files"
msgstr "Dosyaları Yenile"

#: pynicotine/gtkgui/ui/userinfo.ui:101
msgid "Edit Profile"
msgstr "Profili Düzenle"

#: pynicotine/gtkgui/ui/userinfo.ui:149
msgid "Shared Files"
msgstr "Paylaşılan Dosyalar"

#: pynicotine/gtkgui/ui/userinfo.ui:203
msgid "Upload Speed"
msgstr "Yükleme Hızı"

#: pynicotine/gtkgui/ui/userinfo.ui:230
msgid "Free Upload Slots"
msgstr "Boş Yükleme Yuvaları"

#: pynicotine/gtkgui/ui/userinfo.ui:284
msgid "Queued Uploads"
msgstr "Kuyruğa Alınan Yüklemeler"

#: pynicotine/gtkgui/ui/userinfo.ui:354
msgid "Edit Interests"
msgstr "İlgi Alanlarını Düzenle"

#: pynicotine/gtkgui/ui/userinfo.ui:624
msgid "_Gift Privileges…"
msgstr "Ayrıcalık _Hediye Et…"

#: pynicotine/gtkgui/ui/userinfo.ui:663
msgid "_Refresh Profile"
msgstr "Profili _Yenile"

#: pynicotine/plugins/core_commands/PLUGININFO:3
msgid "Nicotine+ Commands"
msgstr "Nicotine+ Komutları"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Nicotine+ Ekibi"

#~ msgid "Listening port (requires a restart):"
#~ msgstr "Dinleme bağlantı noktası (yeniden başlatma gerektirir):"

#~ msgid "Network interface (requires a restart):"
#~ msgstr "Ağ arayüzü (yeniden başlatma gerektirir):"

#~ msgid "Invalid Password"
#~ msgstr "Geçersiz Parola"

#~ msgid "Change _Login Details"
#~ msgstr "_Oturum Açma Bilgilerini Değiştir"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i ayrıcalıklı kullanıcı"

#~ msgid "_Set Up…"
#~ msgstr "_Kurulum…"

#~ msgid "Queued search result text color:"
#~ msgstr "Kuyruğa alınan arama sonucu metin rengi:"

#, python-format
#~ msgid "Failed to fetch the shared folder %(folder)s: %(error)s"
#~ msgstr "%(folder)s paylaşılan klasörü alınamadı: %(error)s"

#~ msgid "_Clear"
#~ msgstr "_Temizle"

#, python-format
#~ msgid "Can't save %(filename)s: %(error)s"
#~ msgstr "%(filename)s kaydedilemiyor: %(error)s"

#~ msgid "Trusted Buddies"
#~ msgstr "Güvenilir Arkadaşlar"

#~ msgid "Quit program"
#~ msgstr "Programdan çık"

#~ msgid "Username:"
#~ msgstr "Kullanıcı adı:"

#~ msgid "Re_commendations for Item"
#~ msgstr "Öge için Ta_vsiyeler"

#~ msgid "_Remove Item"
#~ msgstr "Ögeyi _Kaldır"

#~ msgid "_Remove"
#~ msgstr "_Kaldır"

#~ msgid "Send M_essage"
#~ msgstr "_Mesaj Gönder"

#~ msgid "Send Message"
#~ msgstr "Mesaj Gönder"

#~ msgid "View User Profile"
#~ msgstr "Kullanıcı Profilini Görüntüle"

#~ msgid "Start Messaging"
#~ msgstr "Mesajlaşmaya Başla"

#~ msgid "Enter the name of the user whom you want to send a message:"
#~ msgstr "Mesaj göndermek istediğiniz kullanıcının adını girin:"

#~ msgid "_Message"
#~ msgstr "_Mesaj Gönder"

#~ msgid "Enter the name of the user whose profile you want to see:"
#~ msgstr "Profilini görmek istediğiniz kullanıcının adını girin:"

#~ msgid "_View Profile"
#~ msgstr "Profili _Görüntüle"

#~ msgid "Enter the name of the user whose shares you want to see:"
#~ msgstr "Paylaşımlarını görmek istediğiniz kullanıcının adını girin:"

#~ msgid "_Browse"
#~ msgstr "_Göz At"

#~ msgid "Password"
#~ msgstr "Parola"

#~ msgid "Download File"
#~ msgstr "Dosyayı İndir"

#~ msgid "Refresh Similar Users"
#~ msgstr "Benzer Kullanıcıları Yenile"

#~ msgid "Enter the username of the person whose files you want to see"
#~ msgstr "Dosyalarını görmek istediğiniz kişinin kullanıcı adını girin"

#~ msgid "Enter the username of the person whose information you want to see"
#~ msgstr "Bilgilerini görmek istediğiniz kişinin kullanıcı adını girin"

#~ msgid "Enter the username of the person you want to send a message to"
#~ msgstr "Mesaj göndermek istediğiniz kişinin kullanıcı adını girin"

#~ msgid "Enter the username of the person you want to add to your buddy list"
#~ msgstr "Arkadaş listenize eklemek istediğiniz kişinin kullanıcı adını girin"

#~ msgid ""
#~ "Enter the name of a room you want to join. If the room doesn't exist, it "
#~ "will be created."
#~ msgstr ""
#~ "Katılmak istediğiniz odanın adını girin. Oda yoksa oluşturulacaktır."

#~ msgid "Show Log History Pane"
#~ msgstr "Günlük Geçmişi Bölmesini Göster"

#~ msgid "Save _Picture"
#~ msgstr "Resmi _Kaydet"

#, python-format
#~ msgid ""
#~ "Filtered out incorrect search result %(filepath)s from user %(user)s for "
#~ "search query \"%(query)s\""
#~ msgstr ""
#~ "\"%(query)s\" arama sorgusu için %(user)s kullanıcısından gelen hatalı "
#~ "arama sonucu %(filepath)s filtrelendi"

#~ msgid ""
#~ "Certain clients don't send search results if special characters are "
#~ "included."
#~ msgstr ""
#~ "Özel karakterler dahil edilmişse, bazı istemciler arama sonuçları "
#~ "göndermez."

#~ msgid "Remove special characters from search terms"
#~ msgstr "Arama terimlerinden özel karakterleri kaldır"

#, python-format
#~ msgid "Trouble executing '%s'"
#~ msgstr "'%s' çalıştırılırken sorun oluştu"

#, python-format
#~ msgid "Trouble executing on folder: %s"
#~ msgstr "Klasörde çalıştırılırken sorun oluştu: %s"

#~ msgid "Disallowed extension"
#~ msgstr "İzin verilmeyen uzantı"

#~ msgid "Too many files"
#~ msgstr "Çok fazla dosya"

#~ msgid "Too many megabytes"
#~ msgstr "Çok fazla megabayt"

#~ msgid "Server does not permit performing wishlist searches at this time"
#~ msgstr "Sunucu şu anda dilek listesi aramalarına izin vermiyor"

#~ msgid ""
#~ "File transfer speeds depend on users you are downloading from. Certain "
#~ "users will be faster, while others will be slow."
#~ msgstr ""
#~ "Dosya aktarım hızları indirdiğiniz kullanıcılara bağlıdır. Bazı "
#~ "kullanıcılarda daha hızlı olurken, bazılarında yavaş olacaktır."

#~ msgid "Started Downloads"
#~ msgstr "Başlatılan İndirmeler"

#~ msgid "Started Uploads"
#~ msgstr "Başlatılan Yüklemeler"

#~ msgid "Replace censored letters with:"
#~ msgstr "Sansürlenen harfleri şununla değiştir:"

#~ msgid "Censored Patterns"
#~ msgstr "Sansürlenen Kalıplar"

#~ msgid "Replacements"
#~ msgstr "Değişimler"

#~ msgid ""
#~ "If a user on the Soulseek network searches for a file that exists in your "
#~ "shares, search results will be sent to the user."
#~ msgstr ""
#~ "Soulseek ağındaki bir kullanıcı paylaşımlarınızda bulunan bir dosyayı "
#~ "ararsa, arama sonuçları kullanıcıya gönderilecek."

#~ msgid "Send to Player"
#~ msgstr "Oynatıcıya Gönder"

#~ msgid "Send to _Player"
#~ msgstr "_Oynatıcıya Gönder"

#~ msgid "_Open in File Manager"
#~ msgstr "_Dosya Yöneticisinde Aç"

#~ msgid "Media player command:"
#~ msgstr "Ortam oynatıcısı komutu:"

#, python-format
#~ msgid ""
#~ "Unable to save download to username subfolder, falling back to default "
#~ "download folder. Error: %s"
#~ msgstr ""
#~ "İndirme, kullanıcı adı alt klasörüne kaydedilemiyor, öntanımlı indirme "
#~ "klasörü kullanılıyor. Hata: %s"

#~ msgid "Buddy-only"
#~ msgstr "Yalnızca arkadaş"

#~ msgid "Share with buddies only"
#~ msgstr "Yalnızca arkadaşlarla paylaş"

#~ msgid "_Quit…"
#~ msgstr "_Çıkış…"

#~ msgid "_Configure Shares"
#~ msgstr "Paylaşımları _Yapılandır"

#~ msgid "Remote file error"
#~ msgstr "Uzak dosya hatası"

#, python-format
#~ msgid "Cannot find %(option1)s or %(option2)s, please install either one."
#~ msgstr "%(option1)s veya %(option2)s bulunamıyor, lütfen birini kurun."

#, python-format
#~ msgid "%(num)s folders found before rescan"
#~ msgstr "Yeniden taramadan sonra %(num)s klasör bulundu"

#, python-format
#~ msgid "Failed to process the following databases: %(names)s"
#~ msgstr "Şu veri tabanları işlenemedi: %(names)s"

#, python-format
#~ msgid "Failed to send number of shared files to the server: %s"
#~ msgstr "Paylaşılan dosya sayısı sunucuya gönderilemedi: %s"

#~ msgid "Quit / Run in Background"
#~ msgstr "Çık / Arka Planda Çalıştır"

#~ msgid "Limit buddy-only shares to trusted buddies"
#~ msgstr "Yalnızca arkadaş paylaşımlarını güvenilir arkadaşlarla sınırla"

#~ msgid "Shared"
#~ msgstr "Paylaşılan"

#~ msgid "Search Files and Folders"
#~ msgstr "Dosya ve Klasörleri Ara"

#~ msgid "Out of Date"
#~ msgstr "Güncel Değil"

#, python-format
#~ msgid "Version %(version)s is available, released on %(date)s"
#~ msgstr "Sürüm %(version)s kullanılabilir, %(date)s tarihinde yayınlandı"

#, python-format
#~ msgid "You are using a development version of %s"
#~ msgstr "%s geliştirme sürümünü kullanıyorsunuz"

#, python-format
#~ msgid "You are using the latest version of %s"
#~ msgstr "En son %s sürümünü kullanıyorsunuz"

#~ msgid "Latest Version Unknown"
#~ msgstr "Son Sürüm Bilinmiyor"

#~ msgid "Prefer Dark _Mode"
#~ msgstr "Karanlık _Modu Tercih Et"

#~ msgid "Show _Log History Pane"
#~ msgstr "_Günlük Geçmişi Bölmesini Göster"

#~ msgid "Buddy List in Separate Tab"
#~ msgstr "Ayrı Sekmede Arkadaş Listesi"

#~ msgid "Buddy List Always Visible"
#~ msgstr "Her Zaman Görünür Arkadaş Listesi"

#~ msgid "_View"
#~ msgstr "_Görünüm"

#~ msgid "_Open Log Folder"
#~ msgstr "_Günlük Klasörünü Aç"

#~ msgid "_Browse Folder(s)"
#~ msgstr "Klasör(ler)e _Göz At"

#~ msgid "Kibibytes (2^10 bytes) per second."
#~ msgstr "Kibibayt (2^10 bayt) / saniye."

#~ msgid "Sent to users as the reason for being geo blocked."
#~ msgstr "Coğrafi olarak engellenme nedeni olarak kullanıcılara gönderilir."

#~ msgid "Sent to users as the reason for being banned."
#~ msgstr "Yasaklanma nedeni olarak kullanıcılara gönderilir."

#~ msgid ""
#~ "Once you interact with the application being away, status will be set to "
#~ "online."
#~ msgstr ""
#~ "Uzakta iken uygulama ile etkileşime geçtiğinizde, durum çevrim içi olarak "
#~ "ayarlanacaktır."

#~ msgid "Each user may queue a maximum of either:"
#~ msgstr "Her kullanıcı en fazla şunlardan birini kuyruğa alabilir:"

#~ msgid "Mebibytes (2^20 bytes)."
#~ msgstr "Mebibayt (2^20 bayt)."

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "files"
#~ msgstr "dosya"

#~ msgid "Queue Behavior"
#~ msgstr "Kuyruk Davranışı"

#~ msgid ""
#~ "If disabled, slots will automatically be determined by available "
#~ "bandwidth limitations."
#~ msgstr ""
#~ "Devre dışı bırakılırsa, yuvalar kullanılabilir bant genişliği "
#~ "sınırlamalarına göre otomatik olarak belirlenecektir."

#~ msgid "Note that the operating system's theme may take precedence."
#~ msgstr "İşletim sisteminin temasının öncelikli olabileceğini unutmayın."

#~ msgid "By default the leftmost tab is activated at startup"
#~ msgstr "Öntanımlı olarak, en soldaki sekme başlangıçta etkinleştirilir"

#, python-format
#~ msgid "Quit %(program)s %(version)s, %(status)s!"
#~ msgstr "Çıkış: %(program)s %(version)s, %(status)s!"

#~ msgid "terminated"
#~ msgstr "sonlandırıldı"

#~ msgid "done"
#~ msgstr "bitti"

#~ msgid "Remember choice"
#~ msgstr "Seçimi hatırla"

#~ msgid "Kosovo"
#~ msgstr "Kosova"

#~ msgid "Unknown Network Interface"
#~ msgstr "Bilinmeyen Ağ Arayüzü"

#~ msgid "Listening Port Unavailable"
#~ msgstr "Kullanılabilir Dinleme Bağlantı Noktası Yok"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr ""
#~ "Sunucu çalışmıyor veya yanıt vermiyor gibi görünüyor, %i saniye içinde "
#~ "yeniden deneniyor"

#~ msgid ""
#~ "Nicotine+ uses peer-to-peer networking to connect to other users. In "
#~ "order to allow users to connect to you without trouble, an open listening "
#~ "port is crucial."
#~ msgstr ""
#~ "Nicotine+, diğer kullanıcılara bağlanmak için eşler arası ağ kullanır. "
#~ "Kullanıcıların size sorunsuz bir şekilde bağlanmasını sağlamak için açık "
#~ "bir dinleme bağlantı noktası çok önemlidir."

#~ msgid "--- disconnected ---"
#~ msgstr "--- bağlantı kesildi ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- yeniden bağlandı ---"

#~ msgid "ID"
#~ msgstr "Kimlik"

#~ msgid "Earth"
#~ msgstr "Dünya"

#~ msgid "Czech Republic"
#~ msgstr "Çek Cumhuriyeti"

#~ msgid "Turkey"
#~ msgstr "Türkiye"

#~ msgid "Joined Rooms "
#~ msgstr "Katılınan Odalar "

#~ msgid "_Auto-join Room"
#~ msgstr "Odaya Otomatik K_atıl"

#~ msgid ""
#~ "<b>Syntax</b>: Letters are case-insensitive. All Python regular "
#~ "expressions are supported if escaping is disabled. For simple filters, "
#~ "keeping escaping enabled is recommended."
#~ msgstr ""
#~ "<b>Söz dizimi:</b> Harfler büyük/küçük harfe duyarlı değildir. Kaçırma "
#~ "devre dışı bırakılmışsa, tüm Python düzenli ifadeleri desteklenir. Basit "
#~ "filtreler için kaçırmanın etkin tutulması tavsiye edilir."

#~ msgid "Escaped"
#~ msgstr "Kaçırıldı"

#~ msgid "Escape filter"
#~ msgstr "Filtreyi kaçır"

#~ msgid "Enter a text pattern and what to replace it with"
#~ msgstr "Bir metin kalıbı ve ne ile değiştirileceğini girin"

#, python-format
#~ msgid "%(num)s folders found before rescan, rebuilding…"
#~ msgstr ""
#~ "Yeniden taramadan önce %(num)s klasör bulundu, yeniden oluşturuluyor…"

#, python-format
#~ msgid "No listening port is available in the specified port range %s–%s"
#~ msgstr ""
#~ "Belirtilen bağlantı noktası aralığında kullanılabilir dinleme bağlantı "
#~ "noktası yok %s–%s"

#~ msgid ""
#~ "Choose a range to select a listening port from. The first available port "
#~ "in the range will be used."
#~ msgstr ""
#~ "Bir dinleme bağlantı noktası seçmek için bir aralık seçin. Aralıktaki ilk "
#~ "kullanılabilir bağlantı noktası kullanılacaktır."

#~ msgid "First Port"
#~ msgstr "İlk Bağlantı Noktası"

#~ msgid "to"
#~ msgstr "ile"

#~ msgid "Last Port"
#~ msgstr "Son Bağlantı Noktası"

#, python-format
#~ msgid "Cannot find %s or newer, please install it."
#~ msgstr "%s veya daha yenisi bulunamıyor, lütfen kurun."

#~ msgid ""
#~ "Cannot import the Gtk module. Bad install of the python-gobject module?"
#~ msgstr ""
#~ "Gtk modülü içe aktarılamıyor. python-gobject modülü hatalı mı kuruldu?"

#, python-format
#~ msgid ""
#~ "You are using an unsupported version of GTK %(major_version)s. You should "
#~ "install GTK %(complete_version)s or newer."
#~ msgstr ""
#~ "Desteklenmeyen bir GTK %(major_version)s sürümü kullanıyorsunuz. GTK "
#~ "%(complete_version)s veya daha yenisini kurmalısınız."

#~ msgid "User Info"
#~ msgstr "Kullanıcı Bilgileri"

#~ msgid "Zoom 1:1"
#~ msgstr "1:1 Yakınlaştır"

#~ msgid "Zoom In"
#~ msgstr "Yakınlaştır"

#~ msgid "Zoom Out"
#~ msgstr "Uzaklaştır"

#~ msgid "Show User I_nfo"
#~ msgstr "Kullanıcı _Bilgilerini Göster"

#~ msgid "Request User's Info"
#~ msgstr "Kullanıcı Bilgilerini İste"

#~ msgid "Request User's Shares"
#~ msgstr "Kullanıcının Paylaşımlarını İste"

#~ msgid "Request User Info"
#~ msgstr "Kullanıcı Bilgilerini İste"

#~ msgid "Enter the name of the user whose info you want to see:"
#~ msgstr "Bilgilerini görmek istediğiniz kullanıcının adını girin:"

#~ msgid "Request Shares List"
#~ msgstr "Paylaşım Listesini İste"

#, python-format
#~ msgid "Invalid Soulseek URL: %s"
#~ msgstr "Geçersiz Soulseek URL'si: %s"

#~ msgid ""
#~ "Users on the Soulseek network will be able to download files from folders "
#~ "you share. Sharing files is crucial for the health of the Soulseek "
#~ "network."
#~ msgstr ""
#~ "Soulseek ağındaki kullanıcılar, paylaştığınız klasörlerden dosya "
#~ "indirebilecekler. Dosyaları paylaşmak, Soulseek ağının sağlığı için çok "
#~ "önemlidir."

#~ msgid "Update I_nfo"
#~ msgstr "_Bilgileri Güncelle"

#~ msgid "Chat Room Commands"
#~ msgstr "Sohbet Odası Komutları"

#~ msgid "/join /j 'room'"
#~ msgstr "/join /j 'oda'"

#~ msgid "Join room 'room'"
#~ msgstr "'oda' odasına katıl"

#~ msgid "/me 'message'"
#~ msgstr "/me 'mesaj'"

#~ msgid "Display the Now Playing script's output"
#~ msgstr "Şimdi Oynatılıyor betiğinin çıktısını görüntüle"

#~ msgid "/add /ad 'user'"
#~ msgstr "/add /ad 'kullanıcı'"

#~ msgid "Add user 'user' to your buddy list"
#~ msgstr "'kullanıcı' kullanıcısını arkadaş listenize ekleyin"

#~ msgid "/rem /unbuddy 'user'"
#~ msgstr "/rem /unbuddy 'kullanıcı'"

#~ msgid "Remove user 'user' from your buddy list"
#~ msgstr "'kullanıcı' kullanıcısını arkadaş listenizden kaldırın"

#~ msgid "/ban 'user'"
#~ msgstr "/ban 'kullanıcı'"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "'kullanıcı' kullanıcısını yasaklama listenize ekleyin"

#~ msgid "/unban 'user'"
#~ msgstr "/unban 'kullanıcı'"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "'kullanıcı' kullanıcısını yasaklama listenizden kaldırın"

#~ msgid "/ignore 'user'"
#~ msgstr "/ignore 'kullanıcı'"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "'kullanıcı' kullanıcısını yok sayılan listenize ekleyin"

#~ msgid "/unignore 'user'"
#~ msgstr "/unignore 'kullanıcı'"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "'kullanıcı' kullanıcısını yok sayılan listenizden kaldırın"

#~ msgid "/browse /b 'user'"
#~ msgstr "/browse /b 'kullanıcı'"

#~ msgid "/whois /w 'user'"
#~ msgstr "/whois /w 'kullanıcı'"

#~ msgid "Request info for 'user'"
#~ msgstr "'kullanıcı' için bilgi iste"

#~ msgid "/ip 'user'"
#~ msgstr "/ip 'kullanıcı'"

#~ msgid "Show IP for user 'user'"
#~ msgstr "'kullanıcı' kullanıcısının IP adresini göster"

#~ msgid "/search /s 'query'"
#~ msgstr "/search /s 'sorgu'"

#~ msgid "Start a new search for 'query'"
#~ msgstr "'sorgu' için yeni bir arama başlat"

#~ msgid "/rsearch /rs 'query'"
#~ msgstr "/rsearch /rs 'sorgu'"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "'sorgu' için katılınan odaları ara"

#~ msgid "/bsearch /bs 'query'"
#~ msgstr "/bsearch /bs 'sorgu'"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "'sorgu' için arkadaş listesini ara"

#~ msgid "/usearch /us 'user' 'query'"
#~ msgstr "/usearch /us 'kullanıcı' 'sorgu'"

#~ msgid "/msg 'user' 'message'"
#~ msgstr "/msg 'kullanıcı' 'mesaj'"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "'kullanıcı' kullanıcısına 'mesaj' mesajını gönder"

#~ msgid "/pm 'user'"
#~ msgstr "/pm 'kullanıcı'"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr "'kullanıcı' kullanıcısı için özel sohbet penceresi aç"

#~ msgid "Private Chat Commands"
#~ msgstr "Özel Sohbet Komutları"

#~ msgid "Add user to your ban list"
#~ msgstr "Yasaklama listenize kullanıcı ekleyin"

#~ msgid "Add user to your ignore list"
#~ msgstr "Yok sayılan listenize kullanıcı ekleyin"

#~ msgid "Browse shares of user"
#~ msgstr "Kullanıcının paylaşımlarına göz at"

#~ msgid ""
#~ "For order-insensitive filtering, as well as filtering several exact "
#~ "phrases, vertical bars can be used to separate phrases and words.\n"
#~ "    Example: Remix|Instrumental|Dub Mix"
#~ msgstr ""
#~ "Sıraya duyarsız filtreleme ve aynı zamanda birkaç tam ifadeyi filtrelemek "
#~ "için, ifadeleri ve sözcükleri ayırmak için dikey çubuklar "
#~ "kullanılabilir. \n"
#~ "    Örnek: Remix|Instrumental|Dub Mix"

#~ msgid "To exclude non-audio files use !0 in the duration filter."
#~ msgstr ""
#~ "Ses dosyası olmayan dosyaları hariç tutmak için süre filtresinde !0 "
#~ "kullanın."

#~ msgid "Filters files based upon users' geographical location."
#~ msgstr "Dosyaları kullanıcıların coğrafi konumlarına göre filtreler."

#~ msgid "To view the full results again, simply clear all active filters."
#~ msgstr ""
#~ "Sonuçların tamamını tekrar görüntülemek için tüm etkin filtreleri "
#~ "temizlemeniz yeterlidir."

#~ msgid "See the preferences to set default search result filter options."
#~ msgstr ""
#~ "Öntanımlı arama sonucu filtre seçeneklerini ayarlamak için tercihlere "
#~ "bakın."

#~ msgid "File size"
#~ msgstr "Dosya boyutu"

#~ msgid "Clear Active Filters"
#~ msgstr "Etkin Filtreleri Temizle"

#~ msgid "Cycle through completions when pressing tab-key"
#~ msgstr "Tab tuşuna basıldığında tamamlamalar arasında geçiş yap"

#~ msgid "Hide drop-down when only one matches"
#~ msgstr "Yalnızca bir tane eşleştiğinde açılır menüyü gizle"

#~ msgid "Download folders in reverse alphanumerical order"
#~ msgstr "Klasörleri ters alfabetik sırada indir"

#~ msgid "Incomplete file folder:"
#~ msgstr "Tamamlanmamış dosya klasörü:"

#~ msgid "Download folder:"
#~ msgstr "İndirme klasörü:"

#~ msgid "Save buddies' uploads to:"
#~ msgstr "Arkadaşların yüklemelerini şuraya kaydet:"

#~ msgid "Use UPnP to forward listening port"
#~ msgstr "Dinleme bağlantı noktasını iletmek için UPnP kullan"

#~ msgid ""
#~ "Share folders with every Soulseek user or buddies, allowing contents to "
#~ "be downloaded directly from your device. Hidden files are never shared."
#~ msgstr ""
#~ "İçeriğin doğrudan aygıtınızdan indirilmesine izin vererek klasörleri her "
#~ "Soulseek kullanıcısı veya arkadaşlarınızla paylaşın. Gizli dosyalar asla "
#~ "paylaşılmaz."

#~ msgid "Secondary Tabs"
#~ msgstr "İkincil Sekmeler"

#~ msgid "Chat room tab bar position:"
#~ msgstr "Sohbet odası sekme çubuğu konumu:"

#~ msgid "Private chat tab bar position:"
#~ msgstr "Özel sohbet sekmesi çubuğu konumu:"

#~ msgid "Search tab bar position:"
#~ msgstr "Arama sekmesi çubuğu konumu:"

#~ msgid "User info tab bar position:"
#~ msgstr "Kullanıcı bilgileri sekme çubuğu konumu:"

#~ msgid "User browse tab bar position:"
#~ msgstr "Kullanıcı göz atma sekmesi çubuğu konumu:"

#~ msgid "Tab Labels"
#~ msgstr "Sekme Etiketleri"

#~ msgid "_Refresh Info"
#~ msgstr "Bilgileri _Yenile"

#~ msgid "Block IP Address"
#~ msgstr "IP Adresini Engelle"

#~ msgid "Connected"
#~ msgstr "Bağlandı"

#~ msgid "Disconnected"
#~ msgstr "Bağlantı kesildi"

#~ msgid "Disconnected (Tray)"
#~ msgstr "Bağlantı kesildi (Tepsi)"

#~ msgid "User(s)"
#~ msgstr "Kullanıcı(lar)"

#, python-format
#~ msgid "Alias \"%s\" returned nothing"
#~ msgstr "\"%s\" takma adı hiçbir şey döndürmedi"

#~ msgid "Alternative Speed Limits"
#~ msgstr "Alternatif Hız Sınırları"

#~ msgid "Last played"
#~ msgstr "Son oynatılan"

#~ msgid "Playing now"
#~ msgstr "Şimdi oynatılıyor"

#, python-format
#~ msgid "Cannot download file to %(path)s: %(error)s"
#~ msgstr "Dosya %(path)s konumuna indirilemiyor: %(error)s"

#, python-format
#~ msgid "Error code %(code)s: %(description)s"
#~ msgstr "Hata kodu %(code)s: %(description)s"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "Böyle bir takma ad yok (%s)"

#~ msgid "Aliases:"
#~ msgstr "Takma adlar:"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "%(alias)s: %(action)s takma adı kaldırıldı\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "Böyle bir takma ad yok (%(alias)s)\n"

#~ msgid "Use Alternative Transfer Speed Limits"
#~ msgstr "Alternatif Aktarım Hızı Sınırlarını Kullan"

#~ msgid "Aliases"
#~ msgstr "Takma adlar"

#~ msgid "/alias /al 'command' 'definition'"
#~ msgstr "/alias /al 'komut' 'tanım'"

#~ msgid "Add a new alias"
#~ msgstr "Yeni bir takma ad ekle"

#~ msgid "/unalias /un 'command'"
#~ msgstr "/unalias /un 'komut'"

#~ msgid "Remove an alias"
#~ msgstr "Takma adı kaldır"

#~ msgid "Chat History"
#~ msgstr "Sohbet Geçmişi"

#~ msgid "Command aliases"
#~ msgstr "Komut takma adları"

#~ msgid "Limit download speed to (KiB/s):"
#~ msgstr "İndirme hızını sınırla (KiB/s):"

#~ msgid "Author(s):"
#~ msgstr "Yazar(lar):"

#~ msgid "Limit upload speed to (KiB/s):"
#~ msgstr "Yükleme hızını sınırla (KiB/s):"

#~ msgid "Wishlist item found"
#~ msgstr "Dilek listesi ögesi bulundu"

#~ msgid "Tabs show user status icons instead of status text"
#~ msgstr "Sekmeler, durum metni yerine kullanıcı durumu simgelerini gösterir"

#~ msgid "Show file path tooltips in file list views"
#~ msgstr "Dosya yolu araç ipuçlarını dosya listesi görünümlerinde göster"

#~ msgid "Colored and clickable usernames"
#~ msgstr "Renkli ve tıklanabilir kullanıcı adları"

#~ msgid "Notification changes the tab's text color"
#~ msgstr "Bildirim, sekmenin metin rengini değiştirir"

#~ msgid "Cancel"
#~ msgstr "İptal"

#~ msgid "OK"
#~ msgstr "Tamam"

#~ msgid "_Add to Buddy List"
#~ msgstr "Arkadaş Listesine _Ekle"

#~ msgid "Clear Downloads With Specific Status"
#~ msgstr "Belirli bir duruma sahip tüm indirmeleri temizle"

#~ msgid "Clear Uploads With Specific Status"
#~ msgstr "Belirli bir durumla sahip yüklemeleri temizle"

#~ msgid "use non-default user data directory for e.g. list of downloads"
#~ msgstr ""
#~ "öntanımlı olmayan kullanıcı veri dizini kullan, örn. indirilenler listesi "
#~ "için"

#, python-format
#~ msgid "Unknown config section '%s'"
#~ msgstr "Bilinmeyen yapılandırma bölümü '%s'"

#, python-format
#~ msgid "Unknown config option '%(option)s' in section '%(section)s'"
#~ msgstr ""
#~ "'%(section)s' bölümünde bilinmeyen yapılandırma seçeneği '%(option)s'"

#, python-format
#~ msgid "Version %s is available"
#~ msgstr "%s sürümü kullanılabilir"

#, python-format
#~ msgid "released on %s"
#~ msgstr "%s tarihinde yayınlandı"

#~ msgid "Nicotine+ is running in the background"
#~ msgstr "Nicotine+ arka planda çalışıyor"

#, python-format
#~ msgid "Private message from %s"
#~ msgstr "%s kullanıcısından özel mesaj"

#~ msgid "Aborted"
#~ msgstr "İptal edildi"

#~ msgid "Blocked country"
#~ msgstr "Engellenen ülke"

#~ msgid "Finished / Aborted"
#~ msgstr "Tamamlandı / İptal Edildi"

#~ msgid "Close tab"
#~ msgstr "Sekmeyi kapat"

#, python-format
#~ msgid "You've been mentioned in the %(room)s room"
#~ msgstr "%(room)s odasında sizden bahsedildi"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "%s komutu tanınmadı"

#, python-format
#~ msgid "(Warning: %(realuser)s is attempting to spoof %(fakeuser)s) "
#~ msgstr ""
#~ "(Uyarı: %(realuser)s, %(fakeuser)s kullanıcısını taklit etmeye çalışıyor) "

#, python-format
#~ msgid ""
#~ "The network interface you specified, '%s', does not exist. Change or "
#~ "remove the specified network interface and restart Nicotine+."
#~ msgstr ""
#~ "Belirttiğiniz '%s' ağ arayüzü yok. Belirtilen ağ arayüzünü değiştirin "
#~ "veya kaldırın ve Nicotine+'ı yeniden başlatın."

#~ msgid ""
#~ "The range you specified for client connection ports was {}-{}, but none "
#~ "of these were usable. Increase and/or "
#~ msgstr ""
#~ "İstemci bağlantı noktaları için belirttiğiniz aralık {}-{} idi, ancak "
#~ "bunların hiçbiri kullanılabilir değildi. Arttırın ve/veya "

#~ msgid ""
#~ "Note that part of your range lies below 1024, this is usually not allowed "
#~ "on most operating systems with the exception of Windows."
#~ msgstr ""
#~ "Aralığınızın bir kısmının 1024'ün altında olduğunu unutmayın, buna "
#~ "Windows hariç çoğu işletim sisteminde genellikle izin verilmez."

#, python-format
#~ msgid "Rescan progress: %s"
#~ msgstr "Yeniden tarama ilerlemesi: %s"

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "İşletim sistemi hatası: %s"

#~ msgid "UPnP is not available on this network"
#~ msgstr "UPnP bu ağda kullanılamıyor"

#, python-format
#~ msgid "Failed to map the external WAN port: %(error)s"
#~ msgstr "Dış WAN bağlantı noktası eşleştirilemedi: %(error)s"

#~ msgid "Room wall"
#~ msgstr "Oda duvarı"

#~ msgid ""
#~ "The default listening port '2234' works fine in most cases. If you need "
#~ "to use a different port, you will be able to modify it in the preferences "
#~ "later."
#~ msgstr ""
#~ "Öntanımlı dinleme bağlantı noktası '2234' çoğu durumda sorunsuz çalışır. "
#~ "Farklı bir bağlantı noktası kullanmanız gerekiyorsa, daha sonra "
#~ "tercihlerde değiştirebilirsiniz."

#~ msgid "Show users with similar interests"
#~ msgstr "Benzer ilgi alanlarına sahip kullanıcıları göster"

#~ msgid "Menu"
#~ msgstr "Menü"

#~ msgid "Expand / Collapse all"
#~ msgstr "Tümünü genişlet / daralt"

#~ msgid "Configure shares"
#~ msgstr "Paylaşımları yapılandır"

#~ msgid "Create or join room…"
#~ msgstr "Oda oluştur veya odaya katıl…"

#~ msgid "_Room List"
#~ msgstr "_Oda Listesi"

#~ msgid "Enable alternative download and upload speed limits"
#~ msgstr "Alternatif indirme ve yükleme hız sınırlarını etkinleştirin"

#~ msgid "Show log history"
#~ msgstr "Günlük geçmişini göster"

#~ msgid "Result grouping mode"
#~ msgstr "Sonuç gruplandırma modu"

#~ msgid "Free slot"
#~ msgstr "Boş yuva"

#~ msgid "Save shares list to disk"
#~ msgstr "Paylaşım listesini diske kaydet"

#~ msgid "_Away"
#~ msgstr "_Uzakta"

#, python-format
#~ msgid "Failed to load ui file %(file)s: %(error)s"
#~ msgstr "%(file)s kullanıcı arayüzü dosyası yüklenemedi: %(error)s"

#~ msgid ""
#~ "Attempting to reset index of shared files due to an error. Please rescan "
#~ "your shares."
#~ msgstr ""
#~ "Bir hata nedeniyle paylaşılan dosyaların dizini sıfırlanmaya çalışılıyor. "
#~ "Lütfen paylaşımlarınızı yeniden tarayın."

#~ msgid ""
#~ "File index of shared files could not be accessed. This could occur due to "
#~ "several instances of Nicotine+ being active simultaneously, file "
#~ "permission issues, or another issue in Nicotine+."
#~ msgstr ""
#~ "Paylaşılan dosyaların dosya dizinine erişilemedi. Bu, Nicotine+'ın birkaç "
#~ "örneğinin aynı anda etkin olması, dosya izni sorunları veya "
#~ "Nicotine+'taki başka bir sorun nedeniyle oluşabilir."

#~ msgid "Nicotine+ is a Soulseek client"
#~ msgstr "Nicotine+ bir Soulseek istemcisidir"

#~ msgid "enable the tray icon"
#~ msgstr "tepsi simgesini etkinleştir"

#~ msgid "disable the tray icon"
#~ msgstr "tepsi simgesini devre dışı bırak"

#~ msgid "Get Soulseek Privileges…"
#~ msgstr "Soulseek Ayrıcalıkları Edin…"

#, python-format
#~ msgid ""
#~ "Public IP address is <b>%(ip)s</b> and active listening port is "
#~ "<b>%(port)s</b>"
#~ msgstr ""
#~ "Genel IP adresi <b>%(ip)s</b> ve etkin dinlenen bağlantı noktası "
#~ "<b>%(port)s</b>"

#~ msgid "unknown"
#~ msgstr "bilinmeyen"

#~ msgid "Notification"
#~ msgstr "Bildirim"

#~ msgid "Length"
#~ msgstr "Uzunluk"

#, python-format
#~ msgid "Picture not saved, %s already exists."
#~ msgstr "Resim kaydedilmedi, %s zaten var."

#~ msgid "_Open"
#~ msgstr "_Aç"

#~ msgid "_Save"
#~ msgstr "_Kaydet"

#, python-format
#~ msgid "Failed to load plugin '%s', could not find it."
#~ msgstr "Bulunamadığı için '%s' eklentisi yüklenemedi."

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "G/Ç hatası: %s"

#, python-format
#~ msgid "Failed to open file path: %s"
#~ msgstr "Dosya yolu açılamadı: %s"

#, python-format
#~ msgid "Failed to open URL: %s"
#~ msgstr "URL açılamadı: %s"

#~ msgid "_Log Conversation"
#~ msgstr "Konuşmayı _Günlüğe Kaydet"

#~ msgid "Result Filter List"
#~ msgstr "Sonuç Filtresi Listesi"

#~ msgid "Prepend < or > to find files less/greater than the given value."
#~ msgstr ""
#~ "Verilen değerden küçük/büyük dosyaları bulmak için başına < veya > "
#~ "ekleyin."

#~ msgid ""
#~ "VBR files display their average bitrate and are typically lower in "
#~ "bitrate than a compressed 320 kbps CBR file of the same audio quality."
#~ msgstr ""
#~ "VBR (değişken bit hızı) dosyaları, ortalama bit hızlarını görüntüler ve "
#~ "genellikle aynı ses kalitesine sahip sıkıştırılmış 320 kbps CBR (sabit "
#~ "bit hızı) dosyasından daha düşük bit hızına sahiptir."

#~ msgid "Like Size above, =, <, and > can be used."
#~ msgstr "Yukarıda boyutta olduğu gibi; =, < ve > kullanılabilir."

#~ msgid ""
#~ "Prevent write access by other programs for files being downloaded (turn "
#~ "off for NFS)"
#~ msgstr ""
#~ "İndirilen dosyalar için diğer programların yazma erişimini engelle (NFS "
#~ "için kapatın)"

#~ msgid "Where incomplete downloads are temporarily stored."
#~ msgstr "Tamamlanmamış indirmelerin geçici olarak depolandığı yer."

#~ msgid ""
#~ "Where buddies' uploads will be stored (with a subfolder created for each "
#~ "buddy)."
#~ msgstr ""
#~ "Arkadaşların yüklemelerinin depolanacağı yer (her bir arkadaş için "
#~ "oluşturulan bir alt klasörle)."

#~ msgid "Toggle away status after minutes of inactivity:"
#~ msgstr "Şu kadar dakika hareketsizlikten sonra uzakta durumunu değiştir:"

#~ msgid "Protocol:"
#~ msgstr "Protokol:"

#~ msgid "Icon theme folder (requires restart):"
#~ msgstr "Simge teması klasörü (yeniden başlatma gerektirir):"

#~ msgid "Establishing connection"
#~ msgstr "Bağlantı kuruluyor"

#~ msgid "Clear Groups"
#~ msgstr "Grupları Temizle"

#~ msgid "User List"
#~ msgstr "Kullanıcı Listesi"

#~ msgid "_Reset Statistics…"
#~ msgstr "İstatistikleri _Sıfırla…"

#~ msgid "Clear _Downloads…"
#~ msgstr "İndirmeleri _Temizle…"

#~ msgid "Clear Uploa_ds…"
#~ msgstr "_Yüklemeleri Temizle…"

#~ msgid "Block User's IP Address"
#~ msgstr "Kullanıcının IP Adresini Engelle"

#~ msgid "Ignore User's IP Address"
#~ msgstr "Kullanıcının IP Adresini Yok Say"

#~ msgid ""
#~ "Clear every download that has finished transferring or been caught by a "
#~ "filter."
#~ msgstr ""
#~ "Aktarmayı bitiren veya bir filtre tarafından yakalanan her indirmeyi "
#~ "temizle."

#~ msgid "Usernames"
#~ msgstr "Kullanıcı Adları"

#~ msgid "Display logged chat room messages when a room is rejoined"
#~ msgstr ""
#~ "Bir odaya yeniden katıldığınızda günlüğe kaydedilen sohbet odası "
#~ "mesajlarını görüntüle"

#~ msgid ""
#~ "Clear every upload that has either finished transferring, or been "
#~ "cancelled by the remote user."
#~ msgstr ""
#~ "Aktarımı tamamlanan veya uzak kullanıcı tarafından iptal edilen her "
#~ "yüklemeyi temizle."

#~ msgid "Queue Position"
#~ msgstr "Kuyruk Konumu"

#, python-format
#~ msgid ""
#~ "User %(user)s is directly searching for \"%(query)s\", returning %(num)i "
#~ "results"
#~ msgstr ""
#~ "%(user)s kullanıcısı doğrudan \"%(query)s\" için arama yapıyor ve %(num)i "
#~ "sonuç döndürüyor"

#~ msgid "Room wall (personal message set)"
#~ msgstr "Oda duvarı (kişisel mesaj ayarlandı)"

#~ msgid "Your config file is corrupt"
#~ msgstr "Yapılandırma dosyanız bozuk"

#, python-format
#~ msgid ""
#~ "We're sorry, but it seems your configuration file is corrupt. Please "
#~ "reconfigure Nicotine+.\n"
#~ "\n"
#~ "We renamed your old configuration file to\n"
#~ "%(corrupt)s\n"
#~ "If you open this file with a text editor you might be able to rescue some "
#~ "of your settings."
#~ msgstr ""
#~ "Üzgünüz, ancak görünüşe göre yapılandırma dosyanız bozuk. Lütfen "
#~ "Nicotine+'ı yeniden yapılandırın.\n"
#~ "\n"
#~ "Eski yapılandırma dosyanızı şu şekilde yeniden adlandırdık:\n"
#~ "%(corrupt)s\n"
#~ "Bu dosyayı bir metin düzenleyiciyle açarsanız, bazı ayarlarınızı "
#~ "kurtarabilirsiniz."

#~ msgid "User Description"
#~ msgstr "Kullanıcı Açıklaması"

#~ msgid "User Interests"
#~ msgstr "Kullanıcı İlgi Alanları"

#~ msgid "User Picture"
#~ msgstr "Kullanıcı Resmi"

#~ msgid "Search Wishlist"
#~ msgstr "Dilek Listesini Ara"

#, python-format
#~ msgid "Loading Nicotine+ %(nic_version)s"
#~ msgstr "Nicotine+ %(nic_version)s yükleniyor"

#, python-format
#~ msgid "Using Python %(py_version)s"
#~ msgstr "Python %(py_version)s kullanılıyor"

#, python-format
#~ msgid "Quitting Nicotine+ %(version)s, %(status)s…"
#~ msgstr "Çıkılıyor: Nicotine+ %(version)s, %(status)s…"

#, python-format
#~ msgid "Quit Nicotine+ %(version)s, %(status)s!"
#~ msgstr "Çıkış: Nicotine+ %(version)s, %(status)s!"

#~ msgid "User:"
#~ msgstr "Kullanıcı:"

#, python-format
#~ msgid "All %(ext)s"
#~ msgstr "Tüm %(ext)s"

#, python-format
#~ msgid "%(number)2s files "
#~ msgstr "%(number)2s dosya "

#~ msgid "Allow regular expressions for the filter's include and exclude"
#~ msgstr ""
#~ "Filtrenin dahil etme ve hariç tutma işlemleri için düzenli ifadelere izin "
#~ "ver"

#~ msgid "Quit…"
#~ msgstr "Çıkış…"

#~ msgid "Remember previous primary tab on startup"
#~ msgstr "Başlangıçta önceki birincil sekmeyi hatırla"

#~ msgid "Start with Search Files by default."
#~ msgstr "Öntanımlı olarak Dosyaları Ara sekmesiyle başla."

#~ msgid "Close Nicotine+?"
#~ msgstr "Nicotine+ kapatılsın mı?"

#~ msgid "Do you really want to exit Nicotine+?"
#~ msgstr "Nikotin+'tan gerçekten çıkmak istiyor musunuz?"

#~ msgid "Run in Background"
#~ msgstr "Arka Planda Çalıştır"

#~ msgid "_Online Notify"
#~ msgstr "_Çevrim İçi Bildir"

#~ msgid "_Prioritize User"
#~ msgstr "Kullanıcıya Öncelik _Ver"

#~ msgid "_Trust User"
#~ msgstr "Kullanıcıya _Güven"

#~ msgid "Request User's IP Address"
#~ msgstr "Kullanıcının IP Adresini İste"

#~ msgid "Request IP Address"
#~ msgstr "IP Adresini İste"

#~ msgid "Enter the name of the user whose IP address you want to see:"
#~ msgstr "IP adresini görmek istediğiniz kullanıcının adını girin:"

#~ msgid "Downloaded"
#~ msgstr "İndirildi"

#, python-format
#~ msgid "Failed to add download %(filename)s to shared files: %(error)s"
#~ msgstr "İndirilen %(filename)s, paylaşılan dosyalara eklenemedi: %(error)s"

#~ msgid "Automatically share completed downloads"
#~ msgstr "Tamamlanan indirmeleri otomatik olarak paylaş"

#~ msgid ""
#~ "The equivalent of adding your download folder as a public share, however "
#~ "files downloaded to this folder will be automatically accessible to "
#~ "others (no rescan required)."
#~ msgstr ""
#~ "İndirme klasörünüzü genel bir paylaşım olarak eklemeye eş değerdir, ancak "
#~ "bu klasöre indirilen dosyalara başkaları tarafından otomatik olarak "
#~ "erişilebilir (yeniden tarama gerekmez)."

#~ msgid "Unable to Share Folder"
#~ msgstr "Klasör Paylaşılamıyor"

#~ msgid "The chosen virtual name is empty"
#~ msgstr "Seçilen sanal ad boş"

#~ msgid "The chosen virtual name already exists"
#~ msgstr "Seçilen sanal ad zaten var"

#~ msgid "The chosen folder is already shared"
#~ msgstr "Seçilen klasör zaten paylaşılıyor"

#~ msgid "Set Virtual Name"
#~ msgstr "Sanal Ad Ayarla"

#, python-format
#~ msgid "Enter virtual name for '%(dir)s':"
#~ msgstr "'%(dir)s' için sanal ad girin:"

#~ msgid "The chosen virtual name is either empty or already exists"
#~ msgstr "Seçilen sanal ad ya boş ya da zaten var"

#~ msgid "The chosen folder is already shared."
#~ msgstr "Seçilen klasör zaten paylaşılıyor."

#, python-format
#~ msgid "%s Properties"
#~ msgstr "%s Özellikleri"

#~ msgid "Finished rescanning shares"
#~ msgstr "Paylaşımların yeniden taranması tamamlandı"

#~ msgid "Plugin List"
#~ msgstr "Eklenti Listesi"

#, python-format
#~ msgid "Error while scanning %(path)s: %(error)s"
#~ msgstr "%(path)s taranırken hata oluştu: %(error)s"

#~ msgid "Show _Log Pane"
#~ msgstr "_Günlük Bölmesini Göster"

#~ msgid "Addresses"
#~ msgstr "Adresler"

#~ msgid "Handler"
#~ msgstr "İşleyici"

#~ msgid "Could not enable plugin."
#~ msgstr "Eklenti etkinleştirilemedi."

#~ msgid "Could not disable plugin."
#~ msgstr "Eklenti devre dışı bırakılamadı."

#~ msgid "Transfers"
#~ msgstr "Aktarımlar"

#~ msgid "Ban List"
#~ msgstr "Yasaklama Listesi"

#~ msgid "Ignore List"
#~ msgstr "Yok Sayılan Listesi"

#~ msgid "Censor & Replace"
#~ msgstr "Sansürle ve Değiştir"

#~ msgid "Completion"
#~ msgstr "Tamamlama"

#~ msgid "Categories"
#~ msgstr "Kategoriler"

#~ msgid "Upload Folder To…"
#~ msgstr "Klasörü Şuraya Yükle…"

#~ msgid "Upload Folder Recursive To…"
#~ msgstr "Klasörü Özyinelemeli Şuraya Yükle…"

#~ msgid "Download _Recursive"
#~ msgstr "_Özyinelemeli İndir"

#~ msgid "Download R_ecursive To…"
#~ msgstr "Ö_zyinelemeli Şuraya İndir…"

#~ msgid "Up_load File(s)"
#~ msgstr "_Dosya(lar)ı Yükle"

#, python-format
#~ msgid ""
#~ "Error while attempting to display folder '%(folder)s', reported error: "
#~ "%(error)s"
#~ msgstr ""
#~ "'%(folder)s' klasörü görüntülenmeye çalışılırken hata oluştu, hata "
#~ "bildirildi: %(error)s"

#~ msgid "Select Destination for Downloading Folder with Subfolders from User"
#~ msgstr "Kullanıcıdan Alt Klasörlerle Klasörü İndirmek İçin Hedef Seç"

#~ msgid "Select Destination for Downloading a Folder from User"
#~ msgstr "Kullanıcıdan Klasör İndirmek İçin Hedef Seç"

#~ msgid "Select Destination for Downloading File(s) from User"
#~ msgstr "Kullanıcıdan Dosya(lar) İndirmek İçin Hedef Seç"

#~ msgid "Wishes"
#~ msgstr "Dilekler"

#~ msgid "privileged"
#~ msgstr "ayrıcalıklı"

#~ msgid "prioritized"
#~ msgstr "öncelikli"

#~ msgid "Blocked IP Addresses"
#~ msgstr "Engellenen IP Adresleri"

#~ msgid "Complete buddy names"
#~ msgstr "Arkadaş adlarını tamamla"

#~ msgid "Complete usernames in chat rooms"
#~ msgstr "Sohbet odalarında kullanıcı adlarını tamamla"

#~ msgid "Complete room names"
#~ msgstr "Oda adlarını tamamla"

#~ msgid "Drop-down List"
#~ msgstr "Açılan Liste"

#~ msgid "File Manager command ($ for file path):"
#~ msgstr "Dosya yöneticisi komutu (dosya yolu için $):"

#~ msgid "Media Player command ($ for file path):"
#~ msgstr "Ortam oynatıcı komutu (dosya yolu için $):"

#~ msgid "Ignored IP Addresses"
#~ msgstr "Yok Sayılan IP Adresleri"

#~ msgid "Display timestamps"
#~ msgstr "Zaman damgalarını göster"

#~ msgid "Notification Popups"
#~ msgstr "Bildirim Açılır Pencereleri"

#~ msgid "Show notification popup when a file has finished downloading"
#~ msgstr ""
#~ "Bir dosyanın indirilmesi bittiğinde bildirim açılır penceresini göster"

#~ msgid "Show notification popup when a folder has finished downloading"
#~ msgstr ""
#~ "Bir klasörün indirilmesi bittiğinde bildirim açılır penceresini göster"

#~ msgid "Show notification popup when you receive a private message"
#~ msgstr "Özel bir mesaj aldığınızda bildirim açılır penceresini göster"

#~ msgid "Show notification popup when someone sends a message in a chat room"
#~ msgstr ""
#~ "Birisi sohbet odasında bir mesaj gönderdiğinde bildirim açılır "
#~ "penceresini göster"

#~ msgid "Show notification popup when you are mentioned in a chat room"
#~ msgstr ""
#~ "Bir sohbet odasında sizden bahsedildiğinde bildirim açılır penceresini "
#~ "göster"

#~ msgid "Tray"
#~ msgstr "Tepsi"

#~ msgid ""
#~ "Instances of $ will be replaced by the link. The default web browser of "
#~ "the system will be used in cases where a protocol has not been configured."
#~ msgstr ""
#~ "$ karakterleri bağlantı ile değiştirilecektir. Bir protokolün "
#~ "yapılandırılmadığı durumlarda sistemin öntanımlı internet tarayıcısı "
#~ "kullanılacaktır."

#~ msgid "Handler:"
#~ msgstr "İşleyici:"

#~ msgid "Primary Tabs"
#~ msgstr "Birincil Sekmeler"

#~ msgid "Enable file path tooltips in search and transfer views"
#~ msgstr ""
#~ "Arama ve aktarım görünümlerinde dosya yolu araç ipuçlarını etkinleştir"

#~ msgid ""
#~ "Displays the complete file path of a search result or file transfer when "
#~ "you hover a folder path or file name with your cursor."
#~ msgstr ""
#~ "İmlecinizle bir klasör yolunun veya dosya adının üzerine geldiğinizde, "
#~ "bir arama sonucunun veya dosya aktarımının tam dosya yolunu görüntüler."

#~ msgid "Show privately shared files in user shares"
#~ msgstr "Kullanıcı paylaşımlarında özel olarak paylaşılan dosyaları göster"

#~ msgid ""
#~ "Other clients may offer an option to send privately shared files when you "
#~ "browse their shares. Folders containing such files are prefixed with "
#~ "'[PRIVATE FOLDER]', and are not downloadable unless the uploader gives "
#~ "explicit permission."
#~ msgstr ""
#~ "Diğer istemciler, paylaşımlarına göz atarken özel olarak paylaşılan "
#~ "dosyalar gönderme seçeneği sunabilir. Bu tür dosyaları içeren klasörlerin "
#~ "önüne '[PRIVATE FOLDER]' eklenir ve yükleyen kişi açıkça izin vermedikçe "
#~ "indirilemezler."

#~ msgid "Chat Censor & Replace"
#~ msgstr "Sohbeti Sansürle ve Değiştir"

#~ msgid "Show _Debug Log Controls"
#~ msgstr "_Hata Ayıklama Günlüğü Denetimlerini Göster"

#~ msgid "Debug Logging"
#~ msgstr "Hata Ayıklama Günlüğü"

#~ msgid "Virtual Name"
#~ msgstr "Sanal Ad"

#~ msgid "Edit Virtual Name"
#~ msgstr "Sanal Adı Düzenle"

#~ msgid "Copy Folder URL"
#~ msgstr "Klasör URL'sini Kopyala"

#~ msgid "Download _To…"
#~ msgstr "Şuraya _İndir…"

#~ msgid "Download"
#~ msgstr "İndir"

#~ msgid "Upload Folder's Contents"
#~ msgstr "Klasörün İçeriğini Yükle"

#~ msgid "Rename"
#~ msgstr "Yeniden Adlandır"

#~ msgid "File Lists"
#~ msgstr "Dosya Listeleri"

#~ msgid "Copy File Path"
#~ msgstr "Dosya Yolunu Kopyala"

#~ msgid "R_emove Wish"
#~ msgstr "Dileği _Kaldır"

#, python-format
#~ msgid "It appears '%s' is not a directory, not loading plugins."
#~ msgstr "Görünüşe göre '%s' bir dizin değil, eklentiler yüklenmiyor."

#~ msgid "Rescanning buddy shares…"
#~ msgstr "Arkadaş paylaşımları yeniden taranıyor…"

#~ msgid "Finished rescanning buddy shares"
#~ msgstr "Arkadaş paylaşımlarının yeniden taranması tamamlandı"

#, python-format
#~ msgid "Your buddy, %s, is attempting to upload file(s) to you."
#~ msgstr "Arkadaşınız %s, size dosya(lar) yüklemeye çalışıyor."

#, python-format
#~ msgid ""
#~ "%s is not allowed to send you file(s), but is attempting to, anyway. "
#~ "Warning Sent."
#~ msgstr ""
#~ "%s kullanıcısının size dosya(lar) göndermesine izin verilmiyor, ancak "
#~ "yine de deniyor. Uyarı gönderildi."

#~ msgid "Client Version"
#~ msgstr "İstemci Sürümü"

#, python-format
#~ msgid "How many days of privileges should user %s be gifted?"
#~ msgstr "%s kullanıcısına kaç günlük ayrıcalık hediye edilmeli?"

#~ msgid ""
#~ "Buddies will have higher priority in the queue, the same as globally "
#~ "privileged users."
#~ msgstr ""
#~ "Arkadaşlar, küresel olarak ayrıcalıklı kullanıcılarla aynı şekilde "
#~ "kuyrukta daha yüksek önceliğe sahip olacak."

#~ msgid "Privileged"
#~ msgstr "Ayrıcalıklı"

#~ msgid "_Privileged"
#~ msgstr "_Ayrıcalıklı"

#~ msgid "Comments"
#~ msgstr "Yorumlar"

#~ msgid "Edit _Comments…"
#~ msgstr "_Yorumları Düzenle…"

#~ msgid ""
#~ "Creates subfolders based on the user you are downloading from, and stores "
#~ "the downloaded file / folder there."
#~ msgstr ""
#~ "İndirdiğiniz kullanıcıya göre alt klasörler oluşturur ve indirilen "
#~ "dosyayı / klasörü orada depolar."

#~ msgid "Login Details"
#~ msgstr "Oturum Açma Bilgileri"

#~ msgid "Advanced"
#~ msgstr "Gelişmiş"

#~ msgid "Port mapping renewal interval in hours:"
#~ msgstr "Saat cinsinden bağlantı noktası eşleştirme yenileme aralığı:"

#~ msgid "Enable buddy-only shares"
#~ msgstr "Yalnızca arkadaş paylaşımlarını etkinleştir"

#~ msgid "Files will be uploaded in the order they were queued."
#~ msgstr "Dosyalar kuyruğa alındıkları sıraya göre yüklenecektir."

#~ msgid "Rescanning normal shares…"
#~ msgstr "Normal paylaşımlar yeniden taranıyor…"

#~ msgid "Finished rescanning public shares"
#~ msgstr "Genel paylaşımların yeniden taranması tamamlandı"

#~ msgid "Scanning Buddy Shares"
#~ msgstr "Arkadaş Paylaşımları Taranıyor"

#~ msgid "_Rescan Public Shares"
#~ msgstr "_Genel Paylaşımları Yeniden Tara"

#~ msgid "Rescan B_uddy Shares"
#~ msgstr "_Arkadaş Paylaşımlarını Yeniden Tara"

#~ msgid "Rescan Public Shares"
#~ msgstr "Genel Paylaşımları Yeniden Tara"

#~ msgid "Rescan Buddy Shares"
#~ msgstr "Arkadaş Paylaşımlarını Yeniden Tara"

#~ msgid "Enables buddy shares that only users on your buddy list can access."
#~ msgstr ""
#~ "Yalnızca arkadaş listenizdeki kullanıcıların erişebileceği arkadaş "
#~ "paylaşımlarını etkinleştirir."

#~ msgid "Mark each shared folder as buddy-only"
#~ msgstr "Her paylaşılan klasörü yalnızca arkadaş olarak işaretle"

#~ msgid ""
#~ "Overrides the per-share option, useful if you temporarily need to prevent "
#~ "public access to shares."
#~ msgstr ""
#~ "Paylaşımlara genel erişimi geçici olarak engellemeniz gerektiğinde "
#~ "kullanışlı olan, paylaşım başına seçeneğini geçersiz kılar."

#~ msgid ""
#~ "Only users marked as trusted on your buddy list can access your buddy-"
#~ "only shares."
#~ msgstr ""
#~ "Yalnızca arkadaş listenizde güvenilir olarak işaretlenen kullanıcılar, "
#~ "arkadaşlara özel paylaşımlarınıza erişebilir."

#~ msgid ""
#~ "Nicotine+ allows you to share folders directly from your computer. All "
#~ "the contents of these folders (with the exception of dotfiles) can be "
#~ "downloaded by other users on the Soulseek network. Public shares are "
#~ "available for every user, while users in your buddy list can access buddy-"
#~ "only shares in addition to public shares."
#~ msgstr ""
#~ "Nicotine+, klasörleri doğrudan bilgisayarınızdan paylaşmanıza olanak "
#~ "tanır. Bu klasörlerin tüm içeriği (nokta ile başlayan dosyalar hariç) "
#~ "Soulseek ağındaki diğer kullanıcılar tarafından indirilebilir. Herkese "
#~ "açık paylaşımlar her kullanıcı için kullanılabilirken, arkadaş "
#~ "listenizdeki kullanıcılar genel paylaşımlara ek olarak yalnızca arkadaş "
#~ "paylaşımlarına erişebilir."

#~ msgid "Filtered out excluded search result "
#~ msgstr "Hariç tutulan arama sonucu filtrelendi: "

#~ msgid "Filtered out inexact or incorrect search result "
#~ msgstr "Hatalı veya yanlış arama sonucu filtrelendi: "

#, python-format
#~ msgid ""
#~ "Stored setting '%(key)s' is no longer present in the '%(name)s' plugin"
#~ msgstr "Depolanan '%(key)s' ayarı artık '%(name)s' eklentisinde yok"

#, python-format
#~ msgid "Plugin %(module)s returned something weird, '%(value)s', ignoring"
#~ msgstr ""
#~ "%(module)s eklentisi garip bir şey döndürdü, '%(value)s', yok sayılıyor"

#, python-format
#~ msgid "Inconsistent cache for '%(vdir)s', rebuilding '%(dir)s'"
#~ msgstr "'%(vdir)s' için tutarsız önbellek, '%(dir)s' yeniden oluşturuluyor"

#, python-format
#~ msgid "Dropping missing folder %(dir)s"
#~ msgstr "Eksik klasör %(dir)s bırakılıyor"

#, python-format
#~ msgid "All tickers / wall messages for %(room)s:"
#~ msgstr "%(room)s için tüm durum / duvar mesajları:"

#~ msgid "Set your personal ticker"
#~ msgstr "Kişisel durum mesajınızı ayarlayın"

#~ msgid "Show all the tickers"
#~ msgstr "Tüm durum mesajlarını göster"

#~ msgid ""
#~ "Failed to scan shares. If Nicotine+ is currently running, please close "
#~ "the program before scanning."
#~ msgstr ""
#~ "Paylaşımlar taranamadı. Nicotine+ şu anda çalışıyorsa, lütfen taramadan "
#~ "önce programı kapatın."

#~ msgid "Are you sure you wish to exit Nicotine+ at this time?"
#~ msgstr "Şu anda Nicotine+'tan çıkmak istediğinizden emin misiniz?"

#~ msgid "Receive a User's IP Address"
#~ msgstr "Kullanıcının IP Adresini Al"

#~ msgid "Receive a User's Info"
#~ msgstr "Kullanıcının Bilgilerini Al"

#~ msgid "The server forbid us from doing wishlist searches."
#~ msgstr "Sunucu, dilek listesi araması yapmamızı yasaklıyor."

#, python-format
#~ msgid ""
#~ "Your shares database is corrupted. Please rescan your shares and report "
#~ "any potential scanning issues to the developers. Error: %s"
#~ msgstr ""
#~ "Paylaşım veri tabanınız bozuk. Lütfen paylaşımlarınızı yeniden tarayın ve "
#~ "olası tarama sorunlarını geliştiricilere bildirin. Hata: %s"

#~ msgid ""
#~ "Please keep in mind that certain usernames may be taken. If this is the "
#~ "case, you will be prompted to change your username when connecting to the "
#~ "network."
#~ msgstr ""
#~ "Lütfen belirli kullanıcı adlarının alınmış olabileceğini unutmayın. Bu "
#~ "durumda, ağa bağlanırken kullanıcı adınızı değiştirmeniz istenecektir."

#~ msgid "Enter the username of the person you to receive information about"
#~ msgstr "Hakkında bilgi alacağınız kişinin kullanıcı adını girin"

#~ msgid "Add user 'user' to your user list"
#~ msgstr "'kullanıcı' kullanıcısını kullanıcı listenize ekleyin"

#~ msgid "Remove user 'user' from your user list"
#~ msgstr "'kullanıcı' kullanıcısını kullanıcı listenizden kaldırın"

#~ msgid "Request user info for user 'user'"
#~ msgstr "'kullanıcı' kullanıcısı için kullanıcı bilgilerini iste"

#~ msgid "Add user to your user list"
#~ msgstr "Kullanıcı listenize kullanıcı ekleyin"

#~ msgid "Remove user from your user list"
#~ msgstr "Kullanıcı listenizden kullanıcıyı kaldırın"

#~ msgid "Respond to search requests containing minimum character count:"
#~ msgstr "En az şu kadar karakter içeren arama isteklerine yanıt ver:"

#~ msgid ""
#~ "Queued users will be uploaded one file at a time in a cyclical fashion."
#~ msgstr ""
#~ "Kuyruğa alınan kullanıcılara, döngüsel bir şekilde her seferinde bir "
#~ "dosya yüklenecektir."

#~ msgid ""
#~ "Could not execute now playing code. Are you sure you picked the right "
#~ "player?"
#~ msgstr ""
#~ "Şimdi oynatılıyor kodu çalıştırılamadı. Doğru oynatıcıyı seçtiğinizden "
#~ "emin misiniz?"

#, python-format
#~ msgid "The password you've entered is invalid for user %s"
#~ msgstr "Girdiğiniz parola %s kullanıcısı için geçersiz"

#~ msgid "Find..."
#~ msgstr "Bul..."
