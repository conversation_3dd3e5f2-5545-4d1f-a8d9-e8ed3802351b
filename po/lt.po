# SPDX-FileCopyrightText: 2006-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2023-07-25 18:31+0000\n"
"Last-Translator: Anonymous <<EMAIL>>\n"
"Language-Team: Lithuanian <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/lt/>\n"
"Language: lt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n % 10 == 1 && (n % 100 < 11 || n % 100 > "
"19)) ? 0 : ((n % 10 >= 2 && n % 10 <= 9 && (n % 100 < 11 || n % 100 > 19)) ? "
"1 : 2);\n"
"X-Generator: Weblate 5.0-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
#, fuzzy
msgid "Soulseek Client"
msgstr "Soulseek klientas"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
#, fuzzy
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Grafinis klientas Soulseek peer-to-peer tinklo"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
#, fuzzy
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr "Soulseek;Nicotine;dalijimasis;muzika; P2P;lygiaverčiai; GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
#, fuzzy
msgid "Browse the Soulseek network"
msgstr "Grafinis klientas Soulseek peer-to-peer tinklo"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+ yra grafinis klientas Soulseek peer-to-peer tinklo."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
#, fuzzy
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ siekia būti malonus, nemokamas ir atviro kodo (FOSS) alternatyva "
"oficialiam Soulseek klientui, suteikiant papildomą funkcionalumą, išlaikant "
"naujausią soulseek protokolą."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
#, fuzzy
msgid "Search Files"
msgstr "Ieškos failai"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Atsiuntimai"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
#, fuzzy
msgid "Browse Shares"
msgstr "Bro_wse Buddy Akcijos"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
msgid "Private Chat"
msgstr "Asmeninis pokalbis"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Nicotine+ komanda"

#: pynicotine/__init__.py:50
#, fuzzy, python-format
msgid "Website: %s"
msgstr "Įvykdyta: %s"

#: pynicotine/__init__.py:56
#, fuzzy
msgid "show this help message and exit"
msgstr "rodyti šį žinyno pranešimą ir išeiti"

#: pynicotine/__init__.py:59
#, fuzzy
msgid "file"
msgstr "failas"

#: pynicotine/__init__.py:60
#, fuzzy
msgid "use non-default configuration file"
msgstr "naudoti nenumatytą konfigūracijos failą"

#: pynicotine/__init__.py:63
#, fuzzy
msgid "dir"
msgstr "Dir"

#: pynicotine/__init__.py:64
#, fuzzy
msgid "alternative directory for user data and plugins"
msgstr "naudoti nenumatytąjį priedų katalogą"

#: pynicotine/__init__.py:68
#, fuzzy
msgid "start the program without showing window"
msgstr "paleisti programą nerodant lango"

#: pynicotine/__init__.py:71
#, fuzzy
msgid "ip"
msgstr "Ip"

#: pynicotine/__init__.py:72
#, fuzzy
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "susieti lizdus su pateiktu IP (naudinga VPN)"

#: pynicotine/__init__.py:75
#, fuzzy
msgid "port"
msgstr "uostas"

#: pynicotine/__init__.py:76
#, fuzzy
msgid "listen on the given port"
msgstr "klausytis nurodytame prievade"

#: pynicotine/__init__.py:80
#, fuzzy
msgid "rescan shared files"
msgstr "Perskanuoti viešinius"

#: pynicotine/__init__.py:84
#, fuzzy
msgid "start the program in headless mode (no GUI)"
msgstr "paleisti programą be galvos režimu (be GUI)"

#: pynicotine/__init__.py:88
#, fuzzy
msgid "display version and exit"
msgstr "rodyti versiją ir išeiti"

#: pynicotine/__init__.py:121
#, fuzzy, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Naudojate nepalaikomą Python versiją (%(old_version)s).\n"
"Turėtumėte įdiegti Python %(min_version)s arba naujesnę."

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Nepavyko nuskaityti bendrinimo elementų. Uždarykite kitus Nicotine+ "
"egzempliorius ir bandykite dar kartą."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, fuzzy, python-format
msgid "%(user)s is away"
msgstr "Naudotojo %s nėra"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, fuzzy, python-format
msgid "%(user)s is online"
msgstr "Naudotojas %s prisijungęs"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, fuzzy, python-format
msgid "%(user)s is offline"
msgstr "Naudotojas %s atsijungęs"

#: pynicotine/buddies.py:316
#, fuzzy
msgid "Buddy Status"
msgstr "Bičiulių sąrašas"

#: pynicotine/chatrooms.py:383
#, fuzzy, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Jūs buvote įtrauktas į privatų kambarį: %(room)s"

#: pynicotine/chatrooms.py:478
#, fuzzy, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr ""
"Pokalbio pranešimas iš vartotojo „%(user)s“ kambaryje „%(room)s“: %(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr "Nepavyko sukurti aplanko „%(path)s“, gauta klaida: %(error)s"

#: pynicotine/config.py:816
#, fuzzy, python-format
msgid "Error backing up config: %s"
msgstr "Klaida kuriant atsarginę konfigūracijos kopiją: %s"

#: pynicotine/config.py:819
#, fuzzy, python-format
msgid "Config backed up to: %s"
msgstr "Config atsarginę kopiją: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, fuzzy, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Nicotine+ versija %s"

#: pynicotine/core.py:243
#, fuzzy, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Nicotine+ versija %s"

#: pynicotine/core.py:246
#, fuzzy
msgid "terminating"
msgstr "Nutraukti"

#: pynicotine/core.py:246
#, fuzzy
msgid "application closing"
msgstr "programos uždarymas"

#: pynicotine/core.py:259
#, fuzzy, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Nicotine+ versija %s"

#: pynicotine/core.py:267
#, fuzzy
msgid "You need to specify a username and password before connecting…"
msgstr "Prieš prijungdami turite nurodyti vartotojo vardą ir slaptažodį…"

#: pynicotine/downloads.py:239
#, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""
"Klaida: Atsiuntimų filtravimas nepavyko! Patikrinkite filtrus: Priežastis: %s"

#: pynicotine/downloads.py:254
#, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Klaida: %(num)d Atsiuntimų filtravimas nesėkmingas! %(error)s "

#: pynicotine/downloads.py:366
#, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s atsiųsta iš %(user)s"

#: pynicotine/downloads.py:370
#, fuzzy
msgid "File Downloaded"
msgstr "Atsisiųstas failas"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Įvykdyta: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, fuzzy, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "KLAIDA: nepavyko įvykdyti „%(command)s“: %(error)s"

#: pynicotine/downloads.py:407
#, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s atsiųsta iš %(user)s"

#: pynicotine/downloads.py:411
#, fuzzy
msgid "Folder Downloaded"
msgstr "Aplankas atsisiųstas"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Įvykdyta aplanke: %s"

#: pynicotine/downloads.py:443
#, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "Nepavyko perkelti „%(tempfile)s“ į „%(file)s“: %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
#, fuzzy
msgid "Download Folder Error"
msgstr "Atsiuntimo aplanko klaida"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Atsiųsta: naudotojas %(user)s, failas %(file)s"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Atsiuntimas nutrauktas, naudotojas %(user)s failas %(file)s"

#: pynicotine/downloads.py:1150
#, python-format
msgid "Download I/O error: %s"
msgstr "Atsiuntimo I/O klaida: %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Nepavyksta gauti išskirtinio failo užrakto - I/O klaida: %s"

#: pynicotine/downloads.py:1202
#, fuzzy, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Neįmanoma įrašyti failo %(path)s: %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Pradėtas atsiuntimas: naudotojas %(user)s, failas %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, fuzzy, python-format
msgid "Cannot find %s, please install it."
msgstr "Nepavyksta rasti %s, įdiekite jį."

#: pynicotine/gtkgui/__init__.py:162
#, fuzzy
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr "Nėra grafinės aplinkos, naudojamas be galvutės (be GUI) režimas"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "_Prisijungti"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "_Atsijungti"

#: pynicotine/gtkgui/application.py:308
#, fuzzy
msgid "Soulseek _Privileges"
msgstr "_Privilegijuotas"

#: pynicotine/gtkgui/application.py:314
#, fuzzy
msgid "_Preferences"
msgstr "_Preferences"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
#, fuzzy
msgid "_Quit"
msgstr "_Quit"

#: pynicotine/gtkgui/application.py:337
#, fuzzy
msgid "Browse _Public Shares"
msgstr "_Browse viešosios akcijos"

#: pynicotine/gtkgui/application.py:338
#, fuzzy
msgid "Browse _Buddy Shares"
msgstr "Bro_wse Buddy Akcijos"

#: pynicotine/gtkgui/application.py:339
#, fuzzy
msgid "Browse _Trusted Shares"
msgstr "Bro_wse Buddy Akcijos"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
#, fuzzy
msgid "_Rescan Shares"
msgstr "Perskanuoti viešinius"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
#, fuzzy
msgid "Configure _Shares"
msgstr "_Configure Akcijos"

#: pynicotine/gtkgui/application.py:371
#, fuzzy
msgid "_Keyboard Shortcuts"
msgstr "_Keyboard nuorodos"

#: pynicotine/gtkgui/application.py:372
#, fuzzy
msgid "_Setup Assistant"
msgstr "_Setup asistentas"

#: pynicotine/gtkgui/application.py:373
#, fuzzy
msgid "_Transfer Statistics"
msgstr "_Transfer statistika"

#: pynicotine/gtkgui/application.py:378
#, fuzzy
msgid "Report a _Bug"
msgstr "Pranešti apie _Bug"

#: pynicotine/gtkgui/application.py:379
#, fuzzy
msgid "Improve T_ranslations"
msgstr "Pagerinkite T_vertimus"

#: pynicotine/gtkgui/application.py:383
#, fuzzy
msgid "_About Nicotine+"
msgstr "Apie _Nicotine+\""

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "_Failas"

#: pynicotine/gtkgui/application.py:395
#, fuzzy
msgid "_Shares"
msgstr "_Shares"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
#, fuzzy
msgid "_Help"
msgstr "_Help"

#: pynicotine/gtkgui/application.py:410
#, fuzzy
msgid "_Browse Shares"
msgstr "Bro_wse Buddy Akcijos"

#: pynicotine/gtkgui/application.py:465
#, fuzzy, python-format
msgid "Unable to show notification: %s"
msgstr "Neįmanoma parodyti pranešimo iššokančiojo meniu: %s"

#: pynicotine/gtkgui/application.py:526
#, fuzzy
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Ar tikrai norite išeiti?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr ""

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr "Ar tikrai norite išeiti?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
#, fuzzy
msgid "_No"
msgstr "Ne"

#: pynicotine/gtkgui/application.py:535
#, fuzzy
msgid "_Run in Background"
msgstr "Vykdyti fone"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr "_Uždarykite Nicotine+"

#: pynicotine/gtkgui/application.py:561
#, fuzzy
msgid "Shares Not Available"
msgstr "Akcijų nėra"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
#, fuzzy
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Patikrinkite, ar prijungti išoriniai diskai ir ar teisingi aplanko leidimai."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
#, fuzzy
msgid "_Cancel"
msgstr "_Cancel"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "_Bandyti dar kartą"

#: pynicotine/gtkgui/application.py:567
#, fuzzy
msgid "_Force Rescan"
msgstr "_Priverstinis nuskaitymas iš naujo"

#: pynicotine/gtkgui/application.py:742
#, fuzzy
msgid "Message Downloading Users"
msgstr "Pranešimas atsisiunčiantiems vartotojams"

#: pynicotine/gtkgui/application.py:743
#, fuzzy
msgid "Send private message to all users who are downloading from you:"
msgstr "Siųsti asmeninę žinutę visiems vartotojams, kurie atsisiunčia iš jūsų:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
#, fuzzy
msgid "_Send Message"
msgstr "Siųsti žinutę"

#: pynicotine/gtkgui/application.py:756
#, fuzzy
msgid "Message Buddies"
msgstr "Žinutės"

#: pynicotine/gtkgui/application.py:757
#, fuzzy
msgid "Send private message to all online buddies:"
msgstr "Siųsti asmeninę žinutę visiems prisijungusiems draugams:"

#: pynicotine/gtkgui/application.py:786
#, fuzzy
msgid "Select a Saved Shares List File"
msgstr "Įrašyto bendrai naudojamo sąrašo failo pasirinkimas"

#: pynicotine/gtkgui/application.py:877
#, fuzzy
msgid "Critical Error"
msgstr "Kritinė klaida"

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ susidūrė su kritine klaida ir turi išeiti. Nukopijuokite šį "
"pranešimą ir įtraukite jį į pranešimą apie klaidą:"

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr "_Uždarykite Nicotine+"

#: pynicotine/gtkgui/application.py:883
#, fuzzy
msgid "_Copy & Report Bug"
msgstr "Kopijuoti & pranešti apie klaidą"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Būsena"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr "Valstybė"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Naudotojas"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Greitis"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Failai"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted"
msgstr "Patikimas"

#: pynicotine/gtkgui/buddies.py:108
msgid "Notify"
msgstr "Pranešti"

#: pynicotine/gtkgui/buddies.py:114
#, fuzzy
msgid "Prioritized"
msgstr "Pirmenybė teikiama"

#: pynicotine/gtkgui/buddies.py:120
#, fuzzy
msgid "Last Seen"
msgstr "Vėliausiai matytas"

#: pynicotine/gtkgui/buddies.py:126
#, fuzzy
msgid "Note"
msgstr "Pastaba"

#: pynicotine/gtkgui/buddies.py:143
#, fuzzy
msgid "Add User _Note…"
msgstr "Pridėti naudotoją _Pastaba…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Pašalinti"

#: pynicotine/gtkgui/buddies.py:364
msgid "Never seen"
msgstr "Niekada nematytas"

#: pynicotine/gtkgui/buddies.py:529
#, fuzzy
msgid "Add User Note"
msgstr "Pridėti vartotojo pastabą"

#: pynicotine/gtkgui/buddies.py:530
#, fuzzy, python-format
msgid "Add a note about user %s:"
msgstr "Įtraukite kelias pastabas, susietas su vartotoju %s:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
#, fuzzy
msgid "_Add"
msgstr "_Add…"

#: pynicotine/gtkgui/chatrooms.py:224
#, fuzzy
msgid "Create New Room?"
msgstr "Pokalbių kambariai"

#: pynicotine/gtkgui/chatrooms.py:225
#, fuzzy, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Ar tikrai norite sukurti naują kambarį \"%s\"?"

#: pynicotine/gtkgui/chatrooms.py:226
#, fuzzy
msgid "Make room private"
msgstr "Padarykite kambarį privatų"

#: pynicotine/gtkgui/chatrooms.py:515
#, fuzzy
msgid "Search activity log…"
msgstr "Paieškos"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
#, fuzzy
msgid "Search chat log…"
msgstr "Paieškos"

#: pynicotine/gtkgui/chatrooms.py:597
#, fuzzy
msgid "Sear_ch User's Files"
msgstr "Sear_ch vartotojo failai"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
#, fuzzy
msgid "Find…"
msgstr "Rasti"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Kopijuoti"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr "Kopijuoti viską"

#: pynicotine/gtkgui/chatrooms.py:608
#, fuzzy
msgid "Clear Activity View"
msgstr "Valyti veiklos rodinį"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
#, fuzzy
msgid "_Leave Room"
msgstr "_Leave kambarys"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
#, fuzzy
msgid "Copy Link"
msgstr "Kopijuoti _URL"

#: pynicotine/gtkgui/chatrooms.py:624
#, fuzzy
msgid "View Room Log"
msgstr "Peržiūrėti kambario žurnalą"

#: pynicotine/gtkgui/chatrooms.py:627
#, fuzzy
msgid "Delete Room Log…"
msgstr "Naikinti kambario žurnalą…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
#, fuzzy
msgid "Clear Message View"
msgstr "Valyti pranešimo rodinį"

#: pynicotine/gtkgui/chatrooms.py:832
#, fuzzy, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s paminėjo jus kambaryje %(room)s"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, fuzzy, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "%(user)s paminėjo jus kambaryje %(room)s"

#: pynicotine/gtkgui/chatrooms.py:855
#, fuzzy, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "%(user)s paminėjo jus kambaryje %(room)s"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s prisijungė prie kambario"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s išėjo iš kambario"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s pasitraukė"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s grįžo"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
#, fuzzy
msgid "Delete Logged Messages?"
msgstr "Naikinti užregistruotus pranešimus?"

#: pynicotine/gtkgui/chatrooms.py:1197
#, fuzzy
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"Ar tikrai norite visam laikui panaikinti visus užregistruotus šio kambario "
"pranešimus?"

#: pynicotine/gtkgui/dialogs/about.py:405
#, fuzzy
msgid "About"
msgstr "Džibutis"

#: pynicotine/gtkgui/dialogs/about.py:415
#, fuzzy
msgid "Website"
msgstr "Įvykdyta: %s"

#: pynicotine/gtkgui/dialogs/about.py:457
#, fuzzy, python-format
msgid "Error checking latest version: %s"
msgstr "Nuskaitant naujausią versiją įvyko klaida"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr ""

#: pynicotine/gtkgui/dialogs/about.py:467
#, fuzzy
msgid "Up to date"
msgstr "Atnaujinti"

#: pynicotine/gtkgui/dialogs/about.py:496
#, fuzzy
msgid "Checking latest version…"
msgstr "Tikrinti _Latest versiją"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
#, fuzzy
msgid "Setup Assistant"
msgstr "_Setup asistentas"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
#, fuzzy
msgid "Virtual Folder"
msgstr "Virtualus aplankas"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
#, fuzzy
msgid "Folder"
msgstr "Aplankas"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#, fuzzy
msgid "_Previous"
msgstr "Ankstesnis failas"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#, fuzzy
msgid "_Finish"
msgstr "Baigta"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
#, fuzzy
msgid "_Next"
msgstr "_Kitas"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
#, fuzzy
msgid "Add a Shared Folder"
msgstr "Bendrinamo aplanko įtraukimas"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
#, fuzzy
msgid "Edit Shared Folder"
msgstr "V_iešiniai"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, fuzzy, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Įveskite naują virtualų pavadinimą \"%(dir)s\":"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
#, fuzzy
msgid "_Edit"
msgstr "Įvertinimas"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, fuzzy, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"Vartotojas %s jau yra, o įvestas slaptažodis neleistinas. Pasirinkite kitą "
"naudotojo vardą, jei tai pirmas kartas, kai prisijungiate."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
#, fuzzy
msgid "File Properties"
msgstr "Failo ypatybės"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, fuzzy, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "%(total)i failo ypatybės (%(num)i)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, fuzzy, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "%(total)i failo ypatybės (%(num)i)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
#, fuzzy
msgid "_Apply"
msgstr "Vartoti"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
#, fuzzy
msgid "Add…"
msgstr "Pridėti…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
#, fuzzy
msgid "Edit…"
msgstr "Redaguoti…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, fuzzy, python-format
msgid "%s Settings"
msgstr "%s Nustatymai"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
#, fuzzy
msgid "Add Item"
msgstr "Elementas"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
#, fuzzy
msgid "Edit Item"
msgstr "Pomėgiai"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "Nežinoma"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
#, fuzzy
msgid "Check Port Status"
msgstr "Tikrinti prievado būseną"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, fuzzy, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, prievadas %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
#, fuzzy
msgid "Password Change Rejected"
msgstr "Slaptažodžio keitimas atmestas"

#: pynicotine/gtkgui/dialogs/preferences.py:218
#, fuzzy
msgid "Enter a new password for your Soulseek account:"
msgstr "Įveskite naują \"Soulseek\" paskyros slaptažodį:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
#, fuzzy
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"Šiuo metu esate atsijungę nuo Soulseek tinklo. Jei norite pakeisti esamos "
"Soulseek paskyros slaptažodį, turite būti prisijungę prie tos paskyros."

#: pynicotine/gtkgui/dialogs/preferences.py:223
#, fuzzy
msgid "Enter password to use when logging in:"
msgstr "Įveskite slaptažodį, kuris bus naudojamas prisijungiant:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
#, fuzzy
msgid "Change Password"
msgstr "Slaptažodis:"

#: pynicotine/gtkgui/dialogs/preferences.py:230
#, fuzzy
msgid "_Change"
msgstr "Slaptažodžio keitimas atmestas"

#: pynicotine/gtkgui/dialogs/preferences.py:274
msgid "No one"
msgstr "Niekas"

#: pynicotine/gtkgui/dialogs/preferences.py:275
msgid "Everyone"
msgstr "Visi"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Pas bičiulius"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
#, fuzzy
msgid "Trusted buddies"
msgstr "Patikimi naudotojai"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
#, fuzzy
msgid "Nothing"
msgstr "Nieko"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
#, fuzzy
msgid "Open File"
msgstr "Kitas failas"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
#, fuzzy
msgid "Open in File Manager"
msgstr "failų tvarkytuvo _Open"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Ieškoti"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
#, fuzzy
msgid "Pause"
msgstr "Sulaikyta"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
#, fuzzy
msgid "Resume"
msgstr "Atnaujinti"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
#, fuzzy
msgid "Browse Folder"
msgstr "_Browse aplankas (-ai)"

#: pynicotine/gtkgui/dialogs/preferences.py:317
#, fuzzy
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Sintaksė</b>: neskiriamos didžiosios ir mažosios raidės. Jei įjungta, "
"Python įprastos išraiškos gali būti naudojamos, kitu atveju palaikomos tik "
"pakaitos simbolio * atitiktys."

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr "Filtruoti"

#: pynicotine/gtkgui/dialogs/preferences.py:334
#, fuzzy
msgid "Regex"
msgstr "Regex"

#: pynicotine/gtkgui/dialogs/preferences.py:468
#, fuzzy
msgid "Add Download Filter"
msgstr "Keiskite šį atsiuntimo filtrą:"

#: pynicotine/gtkgui/dialogs/preferences.py:469
msgid "Enter a new download filter:"
msgstr "Įveskite naują atsiuntimo filtrą:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
#, fuzzy
msgid "Enable regular expressions"
msgstr "Įgalinti reguliariąsias išraiškas"

#: pynicotine/gtkgui/dialogs/preferences.py:498
#, fuzzy
msgid "Edit Download Filter"
msgstr "Keisti filtrą"

#: pynicotine/gtkgui/dialogs/preferences.py:499
#, fuzzy
msgid "Modify the following download filter:"
msgstr "Keiskite šį atsiuntimo filtrą:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d Nepavyko! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
#, fuzzy
msgid "Filters Successful"
msgstr "Filtrai sėkmingi"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "Nutraukti"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
msgid "Retry"
msgstr "Bandyti dar kartą"

#: pynicotine/gtkgui/dialogs/preferences.py:828
msgid "Round Robin"
msgstr "Round Robin"

#: pynicotine/gtkgui/dialogs/preferences.py:829
msgid "First In, First Out"
msgstr "First In, First Out"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
msgid "Username"
msgstr "Naudotojo vardas"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
#, fuzzy
msgid "IP Address"
msgstr "Blokuoti IP adresą..."

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
#, fuzzy
msgid "Ignore User"
msgstr "Nepaisyti vartotojo"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
#, fuzzy
msgid "Enter the name of the user you want to ignore:"
msgstr "Nurodkite naudotoją, kuriam norite nusiųsti:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
#, fuzzy
msgid "Ignore IP Address"
msgstr "Nepaisyti IP adreso..."

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#, fuzzy
msgid "Enter an IP address you want to ignore:"
msgstr "Nurodkite naudotoją, kuriam norite nusiųsti:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "* is a wildcard"
msgstr "* yra kaitos simbolis"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
#, fuzzy
msgid "Ban User"
msgstr "Uždrausti vartotoją"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
#, fuzzy
msgid "Enter the name of the user you want to ban:"
msgstr "Nurodkite naudotoją, kuriam norite nusiųsti:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
#, fuzzy
msgid "Ban IP Address"
msgstr "Blokuoti IP adresą..."

#: pynicotine/gtkgui/dialogs/preferences.py:1290
#, fuzzy
msgid "Enter an IP address you want to ban:"
msgstr "Nurodkite naudotoją, kuriam norite nusiųsti:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
msgid "Pattern"
msgstr "Šablonas"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
msgid "Replacement"
msgstr "Pakeitimas"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
#, fuzzy
msgid "Censor Pattern"
msgstr "Šablonas"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
#, fuzzy
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Įveskite modelį, kurį norite cenzūruoti. Pridėkite tarpų aplink modelį, jei "
"nenorite suderinti žodžių eilučių (gali nepavykti eilučių pradžioje ir "
"pabaigoje)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
#, fuzzy
msgid "Edit Censored Pattern"
msgstr "Cenzūruoti modeliai"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
#, fuzzy
msgid "Add Replacement"
msgstr "Pakeitimas"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
#, fuzzy
msgid "Enter a text pattern and what to replace it with:"
msgstr "Įveskite atitinkamai teksto šabloną ir pakeitimą:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
#, fuzzy
msgid "Edit Replacement"
msgstr "Pakeitimas"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
#, fuzzy
msgid "System default"
msgstr "Numatytoji"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
#, fuzzy
msgid "Show confirmation dialog"
msgstr "Užveriant pagrindinį langą, rodyti patvirtinimo dialogą"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
#, fuzzy
msgid "Run in the background"
msgstr "Vykdyti fone"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
#, fuzzy
msgid "bold"
msgstr "drąsus"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
#, fuzzy
msgid "italic"
msgstr "Pasvirasis"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
#, fuzzy
msgid "underline"
msgstr "Pabrėžti"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
#, fuzzy
msgid "normal"
msgstr "įprastas"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
#, fuzzy
msgid "Separate Buddies tab"
msgstr "Žinutės"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
#, fuzzy
msgid "Sidebar in Chat Rooms tab"
msgstr "Bičiulių sąrašas kambariuose"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1777
msgid "Top"
msgstr "Viršuje"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
msgid "Bottom"
msgstr "Apačioje"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
msgid "Left"
msgstr "Kairėje"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
msgid "Right"
msgstr "Dešinėje"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "Prisijungęs"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Pasitraukęs"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Atsijungęs"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
#, fuzzy
msgid "Tab Changed"
msgstr "Slaptažodžio keitimas atmestas"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
#, fuzzy
msgid "Tab Highlight"
msgstr "Pabrėžti"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
#, fuzzy
msgid "Window"
msgstr "Langas:"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
#, fuzzy
msgid "Online (Tray)"
msgstr "Prisijungta:"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
#, fuzzy
msgid "Away (Tray)"
msgstr "Išvykęs (dėklas)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
#, fuzzy
msgid "Offline (Tray)"
msgstr "Atsijungęs"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
#, fuzzy
msgid "Message (Tray)"
msgstr "Žinutės"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Protokolas"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
#, fuzzy
msgid "Command"
msgstr "Komentaras"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
#, fuzzy
msgid "Add URL Handler"
msgstr "Valdiklis"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
#, fuzzy
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Įveskite atitinkamai URL dalyvės protokolą ir komandą:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
#, fuzzy
msgid "Edit Command"
msgstr "Taisyti komentarus"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, fuzzy, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Įveskite naują virtualų pavadinimą \"%(dir)s\":"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
#, fuzzy
msgid "Username;APIKEY"
msgstr "Naudotojo vardas"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
#, fuzzy
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr "Kliento vardas (pvz., amarok, įžūlus, exaile) arba tuščias auto:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
#, fuzzy
msgid "Username: "
msgstr "Vartotojo vardas:"

#: pynicotine/gtkgui/dialogs/preferences.py:2767
#, fuzzy
msgid "Command:"
msgstr "Komentaras"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
msgid "Title"
msgstr "Pavadinimas"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Dabar grojama (paprastai „%(artist)s - %(title)s“)"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
msgid "Artist"
msgstr "Atlikėjas"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
#, fuzzy
msgid "Duration"
msgstr "Trukmė"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "Bitų dažnis"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
msgid "Comment"
msgstr "Komentaras"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
msgid "Album"
msgstr "Albumas"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
msgid "Track Number"
msgstr "Takelio numeris"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
msgid "Year"
msgstr "Metai"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
msgid "Filename (URI)"
msgstr "Failo pavadinimas (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
#, fuzzy
msgid "Program"
msgstr "Programa"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
msgid "Enabled"
msgstr "Įjungta"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
#, fuzzy
msgid "Plugin"
msgstr "Įskiepiai"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
#, fuzzy
msgid "No Plugin Selected"
msgstr "Nepasirinktas priedas"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
#, fuzzy
msgid "Preferences"
msgstr "Nuostatos"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
#, fuzzy
msgid "Network"
msgstr "Tinklas"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
#, fuzzy
msgid "User Interface"
msgstr "Sąsaja"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "V_iešiniai"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Išsiuntimai"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Paieškos"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
#, fuzzy
msgid "User Profile"
msgstr "Vartotojo naršymas"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
#, fuzzy
msgid "Chats"
msgstr "Pokalbis"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
#, fuzzy
msgid "Now Playing"
msgstr "Dabar leidžiama"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Žurnalų vedimas"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
#, fuzzy
msgid "Banned Users"
msgstr "Užblokuoti naudotoją(us)"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
#, fuzzy
msgid "Ignored Users"
msgstr "Nepaisomi naudotojai:"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
#, fuzzy
msgid "URL Handlers"
msgstr "Valdiklis"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr "Įskiepiai"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
#, fuzzy
msgid "Pick a File Name for Config Backup"
msgstr "Pasirinkite konfigūracijos atsarginės kopijos failo vardą"

#: pynicotine/gtkgui/dialogs/statistics.py:75
#, fuzzy
msgid "Transfer Statistics"
msgstr "Perkelti statistiką"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, fuzzy, python-format
msgid "Total Since %(date)s"
msgstr "Iš viso nuo %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
#, fuzzy
msgid "Reset Transfer Statistics?"
msgstr "Iš naujo nustatyti perkėlimo statistiką?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
#, fuzzy
msgid "Do you really want to reset transfer statistics?"
msgstr "Ar tikrai norite iš naujo nustatyti perkėlimo statistiką?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
#, fuzzy
msgid "Wishlist"
msgstr "Ieškoti pageidavimų sąraše"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
#, fuzzy
msgid "Wish"
msgstr "Noras"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
#, fuzzy
msgid "_Search for Item"
msgstr "_Ieškoti failų"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
#, fuzzy
msgid "Edit Wish"
msgstr "Įvertinimas"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, fuzzy, python-format
msgid "Enter new value for wish '%s':"
msgstr "Įveskite naują virtualų pavadinimą \"%(dir)s\":"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
#, fuzzy
msgid "Clear Wishlist?"
msgstr "Išvalyti pageidavimų sąrašą?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
#, fuzzy
msgid "Do you really want to clear your wishlist?"
msgstr "Ar tikrai norite išvalyti savo pageidavimų sąrašą?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Kelias"

#: pynicotine/gtkgui/downloads.py:47
#, fuzzy
msgid "_Resume"
msgstr "_Resume"

#: pynicotine/gtkgui/downloads.py:48
#, fuzzy
msgid "P_ause"
msgstr "Sulaikyta"

#: pynicotine/gtkgui/downloads.py:72
#, fuzzy
msgid "Finished / Filtered"
msgstr "Išvalyti baigtus / nutrauktus"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Baigta"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
msgid "Paused"
msgstr "Sulaikyta"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
msgid "Filtered"
msgstr "Atfiltruota"

#: pynicotine/gtkgui/downloads.py:77
#, fuzzy
msgid "Deleted"
msgstr "Ištrinta"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
#, fuzzy
msgid "Queued…"
msgstr "Eilėje"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
#, fuzzy
msgid "Everything…"
msgstr "Viskas…"

#: pynicotine/gtkgui/downloads.py:132
#, fuzzy, python-format
msgid "Downloads: %(speed)s"
msgstr "Parsisiųsti: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
#, fuzzy
msgid "Clear Queued Downloads"
msgstr "Valyti atsisiuntimus eilėje"

#: pynicotine/gtkgui/downloads.py:139
#, fuzzy
msgid "Do you really want to clear all queued downloads?"
msgstr "Nurodkite naudotoją, kuriam norite nusiųsti:"

#: pynicotine/gtkgui/downloads.py:151
#, fuzzy
msgid "Clear All Downloads"
msgstr "Atsiuntimai"

#: pynicotine/gtkgui/downloads.py:152
#, fuzzy
msgid "Do you really want to clear all downloads?"
msgstr "Nurodkite naudotoją, kuriam norite nusiųsti:"

#: pynicotine/gtkgui/downloads.py:169
#, fuzzy, python-format
msgid "Download %(num)i files?"
msgstr "Atsisiųsti %(num)i failus?"

#: pynicotine/gtkgui/downloads.py:170
#, fuzzy, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"Ar tikrai norite atsisiųsti %(num)i failus iš %(user)s aplanko %(folder)s?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
#, fuzzy
msgid "_Download Folder"
msgstr "_Download aplankas"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
msgid "Likes"
msgstr "Mėgsta"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
#, fuzzy
msgid "Dislikes"
msgstr "Mėgsta"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Įvertinimas"

#: pynicotine/gtkgui/interests.py:111
msgid "Item"
msgstr "Elementas"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
#, fuzzy
msgid "_Recommendations for Item"
msgstr "%s rekomendacijos"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
#, fuzzy
msgid "I _Like This"
msgstr "Aš _Like Tai"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
#, fuzzy
msgid "I _Dislike This"
msgstr "Aš _Dislike Tai"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
#, fuzzy
msgid "Recommendations"
msgstr "Rekomendacijas"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
#, fuzzy
msgid "Similar Users"
msgstr "Panašūs vartotojai"

#: pynicotine/gtkgui/interests.py:427
#, fuzzy, python-format
msgid "Recommendations (%s)"
msgstr "Rekomendacijas"

#: pynicotine/gtkgui/interests.py:451
#, fuzzy, python-format
msgid "Similar Users (%s)"
msgstr "Panašūs vartotojai"

#: pynicotine/gtkgui/mainwindow.py:238
#, fuzzy
msgid "Search log…"
msgstr "Paieškos"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, python-format
msgid "Private Message from %(user)s"
msgstr "Asmeninė žinutė nuo %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
#, fuzzy
msgid "Wishlist Results Found"
msgstr "Rasti pageidavimų sąrašo rezultatai"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
#, fuzzy
msgid "User Profiles"
msgstr "Vartotojo naršymas"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
msgid "Chat Rooms"
msgstr "Pokalbių kambariai"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "Pomėgiai"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
msgid "Chat"
msgstr "Pokalbis"

#: pynicotine/gtkgui/mainwindow.py:1111
#, fuzzy
msgid "[Debug] Connections"
msgstr "Prisijungimai"

#: pynicotine/gtkgui/mainwindow.py:1112
#, fuzzy
msgid "[Debug] Messages"
msgstr "Žinutės"

#: pynicotine/gtkgui/mainwindow.py:1113
#, fuzzy
msgid "[Debug] Transfers"
msgstr "Siuntimai"

#: pynicotine/gtkgui/mainwindow.py:1114
#, fuzzy
msgid "[Debug] Miscellaneous"
msgstr "Įvairūs"

#: pynicotine/gtkgui/mainwindow.py:1119
#, fuzzy
msgid "_Find…"
msgstr "Rasti"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
#, fuzzy
msgid "_Copy"
msgstr "Kopijuoti"

#: pynicotine/gtkgui/mainwindow.py:1122
#, fuzzy
msgid "Copy _All"
msgstr "Kopijuoti viską"

#: pynicotine/gtkgui/mainwindow.py:1127
#, fuzzy
msgid "View _Debug Logs"
msgstr "Peržiūrėti derinimo žurnalus"

#: pynicotine/gtkgui/mainwindow.py:1128
#, fuzzy
msgid "View _Transfer Logs"
msgstr "Peržiūrėti perkėlimo žurnalą"

#: pynicotine/gtkgui/mainwindow.py:1132
#, fuzzy
msgid "_Log Categories"
msgstr "Kategorijos"

#: pynicotine/gtkgui/mainwindow.py:1134
#, fuzzy
msgid "Clear Log View"
msgstr "Valyti žurnalo rodinį"

#: pynicotine/gtkgui/mainwindow.py:1199
#, fuzzy
msgid "Preparing Shares"
msgstr "Skenuojami viešiniai"

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr "Skenuojami viešiniai"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
#, fuzzy
msgid "Shared Folders"
msgstr "V_iešiniai"

#: pynicotine/gtkgui/popovers/chathistory.py:77
#, fuzzy
msgid "Latest Message"
msgstr "Siųsti žinutę"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Kambarys"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Naudotojai"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
#, fuzzy
msgid "Join Room"
msgstr "Prisijungti prie kambario"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
#, fuzzy
msgid "Leave Room"
msgstr "Palikti kambarį"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
msgid "Disown Private Room"
msgstr "Nebeturėti asmeninio kambario"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
#, fuzzy
msgid "Cancel Room Membership"
msgstr "Atšaukti kambario narystę"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
#, fuzzy
msgid "Close All Tabs…"
msgstr "Uždaryti visus skirtukus…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
#, fuzzy
msgid "_Close Tab"
msgstr "skirtukas _Close"

#: pynicotine/gtkgui/privatechat.py:379
#, fuzzy
msgid "View Chat Log"
msgstr "Peržiūrėti pokalbių žurnalą"

#: pynicotine/gtkgui/privatechat.py:382
#, fuzzy
msgid "Delete Chat Log…"
msgstr "Naikinti pokalbių žurnalą…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
#, fuzzy
msgid "User Actions"
msgstr "Siuntimai"

#: pynicotine/gtkgui/privatechat.py:477
#, fuzzy
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Ar tikrai norite visam laikui panaikinti visus šio vartotojo užregistruotus "
"pranešimus?"

#: pynicotine/gtkgui/privatechat.py:528
#, fuzzy
msgid "* Messages sent while you were offline"
msgstr ""
"* Atsiųsta žinučių, kol buvote atsijungę. Laiko įrašus praneša serveris, "
"todėl jie gali būti neteisingi."

#: pynicotine/gtkgui/search.py:90
#, fuzzy
msgid "_Global"
msgstr "Visur"

#: pynicotine/gtkgui/search.py:91
#, fuzzy
msgid "_Buddies"
msgstr "Pas bičiulius"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
#, fuzzy
msgid "_Rooms"
msgstr "Kambariuose"

#: pynicotine/gtkgui/search.py:93
#, fuzzy
msgid "_User"
msgstr "Naudotojas"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
#, fuzzy
msgid "In Queue"
msgstr "Eilėje"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
#, fuzzy
msgid "File Type"
msgstr "Failo tipas"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Failo pavadinimas"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Dydis"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
#, fuzzy
msgid "Quality"
msgstr "Kokybė"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
#, fuzzy
msgid "Copy _File Path"
msgstr "Kopijuoti _File maršrutą"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "Kopijuoti _URL"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
#, fuzzy
msgid "Copy Folder U_RL"
msgstr "Kopijuoti aplanko U_RL"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
#, fuzzy
msgid "_Download File(s)"
msgstr "_Download Failas (-ai)"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
#, fuzzy
msgid "Download File(s) _To…"
msgstr "Atsisiųsti failą (-us) _To…"

#: pynicotine/gtkgui/search.py:615
#, fuzzy
msgid "Download _Folder(s)"
msgstr "Atsisiųsti _Folder (-us)"

#: pynicotine/gtkgui/search.py:616
#, fuzzy
msgid "Download F_older(s) To…"
msgstr "Atsisiųsti F_older (-us) į…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
#, fuzzy
msgid "View User _Profile"
msgstr "Na_ršyti naudotoją"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
#, fuzzy
msgid "_Browse Folder"
msgstr "_Browse aplankas (-ai)"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
#, fuzzy
msgid "F_ile Properties"
msgstr "Savybės"

#: pynicotine/gtkgui/search.py:629
#, fuzzy
msgid "Copy Search Term"
msgstr "Kopijuoti ieškos terminą"

#: pynicotine/gtkgui/search.py:631
#, fuzzy
msgid "Clear All Results"
msgstr "Valyti visus rezultatus"

#: pynicotine/gtkgui/search.py:718
#, fuzzy
msgid "Clear Filters"
msgstr "Valyti filtro retrospektyvą"

#: pynicotine/gtkgui/search.py:721
#, fuzzy
msgid "Restore Filters"
msgstr "Patikrinti filtrus"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, fuzzy, python-format
msgid "[PRIVATE]  %s"
msgstr "[PRIVATUS]"

#: pynicotine/gtkgui/search.py:1273
#, fuzzy, python-format
msgid "_Result Filters [%d]"
msgstr "_Result filtrai [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
#, fuzzy
msgid "_Result Filters"
msgstr "Patikrinti filtrus"

#: pynicotine/gtkgui/search.py:1277
#, fuzzy, python-format
msgid "%d active filter(s)"
msgstr "Valyti visus aktyvius filtrus"

#: pynicotine/gtkgui/search.py:1329
#, fuzzy
msgid "Add Wi_sh"
msgstr "Pridėti Wi_sh"

#: pynicotine/gtkgui/search.py:1332
#, fuzzy
msgid "Remove Wi_sh"
msgstr "Pašalinti alternatyvų vardą"

#: pynicotine/gtkgui/search.py:1349
#, fuzzy
msgid "Select User's Results"
msgstr "Pasirinkite naudotojų siuntimus"

#: pynicotine/gtkgui/search.py:1472
#, fuzzy, python-format
msgid "Total: %s"
msgstr "Bendras"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
#, fuzzy
msgid "Results"
msgstr "Rezultatai"

#: pynicotine/gtkgui/search.py:1590
#, fuzzy
msgid "Select Destination Folder for File(s)"
msgstr "Pasirinkite failo (-ų) atsisiuntimo iš vartotojo paskirties vietą"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
#, fuzzy
msgid "Select Destination Folder"
msgstr "Pasirinkite aplanką"

#: pynicotine/gtkgui/transfers.py:61
msgid "Queued"
msgstr "Eilėje"

#: pynicotine/gtkgui/transfers.py:62
#, fuzzy
msgid "Queued (prioritized)"
msgstr "teikiama pirmenybė"

#: pynicotine/gtkgui/transfers.py:63
#, fuzzy
msgid "Queued (privileged)"
msgstr "(privilegijuotas)"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Gaunama būsena"

#: pynicotine/gtkgui/transfers.py:65
msgid "Transferring"
msgstr "Siunčiama"

#: pynicotine/gtkgui/transfers.py:66
#, fuzzy
msgid "Connection closed"
msgstr "Porininkas nutraukė ryšį"

#: pynicotine/gtkgui/transfers.py:67
#, fuzzy
msgid "Connection timeout"
msgstr "Prisijungimai"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Naudotojas atsijungęs"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
#, fuzzy
msgid "Cancelled"
msgstr "Atšaukti"

#: pynicotine/gtkgui/transfers.py:73
#, fuzzy
msgid "Download folder error"
msgstr "Atsiuntimo aplanko klaida"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Vietinio failo klaida"

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr "Užblokuotas"

#: pynicotine/gtkgui/transfers.py:76
msgid "File not shared"
msgstr "Failas neviešinamas"

#: pynicotine/gtkgui/transfers.py:77
#, fuzzy
msgid "Pending shutdown"
msgstr "Laukiama išjungimo"

#: pynicotine/gtkgui/transfers.py:78
#, fuzzy
msgid "File read error"
msgstr "Siuntimai"

#: pynicotine/gtkgui/transfers.py:182
#, fuzzy
msgid "Queue"
msgstr "Eilėje"

#: pynicotine/gtkgui/transfers.py:188
msgid "Percent"
msgstr "Procentai"

#: pynicotine/gtkgui/transfers.py:208
#, fuzzy
msgid "Time Elapsed"
msgstr "Praėjęs laikas"

#: pynicotine/gtkgui/transfers.py:215
#, fuzzy
msgid "Time Left"
msgstr "Laikas kairėje"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
#, fuzzy
msgid "_Open File"
msgstr "Blokavimo sąrašas"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
#, fuzzy
msgid "Open in File _Manager"
msgstr "Atidaryti naudojant failų _Manager"

#: pynicotine/gtkgui/transfers.py:286
#, fuzzy
msgid "_Search"
msgstr "_Search"

#: pynicotine/gtkgui/transfers.py:289
#, fuzzy
msgid "Clear All"
msgstr "Valyti viską"

#: pynicotine/gtkgui/transfers.py:953
msgid "Select User's Transfers"
msgstr "Pasirinkite naudotojų siuntimus"

#: pynicotine/gtkgui/uploads.py:50
#, fuzzy
msgid "_Abort"
msgstr "Nutraukti"

#: pynicotine/gtkgui/uploads.py:74
#, fuzzy
msgid "Finished / Cancelled / Failed"
msgstr "Išvalyti baigtus / nutrauktus"

#: pynicotine/gtkgui/uploads.py:75
#, fuzzy
msgid "Finished / Cancelled"
msgstr "Išvalyti baigtus / nutrauktus"

#: pynicotine/gtkgui/uploads.py:79
#, fuzzy
msgid "Failed"
msgstr "Nepavyko"

#: pynicotine/gtkgui/uploads.py:80
#, fuzzy
msgid "User Logged Off"
msgstr "Naudotojas atsijungęs"

#: pynicotine/gtkgui/uploads.py:142
#, fuzzy, python-format
msgid "Uploads: %(speed)s"
msgstr "Įkeliama: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr ""

#: pynicotine/gtkgui/uploads.py:164
#, fuzzy
msgid "Clear Queued Uploads"
msgstr "Valyti eilėje įrašytus nusiuntimus"

#: pynicotine/gtkgui/uploads.py:165
#, fuzzy
msgid "Do you really want to clear all queued uploads?"
msgstr "Nurodkite naudotoją, kuriam norite nusiųsti:"

#: pynicotine/gtkgui/uploads.py:177
#, fuzzy
msgid "Clear All Uploads"
msgstr "Valyti visus įkėlimus"

#: pynicotine/gtkgui/uploads.py:178
#, fuzzy
msgid "Do you really want to clear all uploads?"
msgstr "Nurodkite naudotoją, kuriam norite nusiųsti:"

#: pynicotine/gtkgui/userbrowse.py:282
#, fuzzy
msgid "_Save Shares List to Disk"
msgstr "_Save akcijų sąrašą į diską"

#: pynicotine/gtkgui/userbrowse.py:292
#, fuzzy
msgid "Upload Folder & Subfolders…"
msgstr "Įkelti aplanką (su poaplankiais) vartotojui"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
#, fuzzy
msgid "Copy _Folder Path"
msgstr "Kopijuoti _Folder maršrutą"

#: pynicotine/gtkgui/userbrowse.py:309
#, fuzzy
msgid "_Download Folder & Subfolders"
msgstr "Atsisiųsti _Folder (-us)"

#: pynicotine/gtkgui/userbrowse.py:310
#, fuzzy
msgid "Download Folder & Subfolders _To…"
msgstr "Atsisiųsti F_older (-us) į…"

#: pynicotine/gtkgui/userbrowse.py:334
#, fuzzy
msgid "File Name"
msgstr "Failo pavadinimas"

#: pynicotine/gtkgui/userbrowse.py:373
#, fuzzy
msgid "Up_load File(s)…"
msgstr "Up_load Failas (-ai)"

#: pynicotine/gtkgui/userbrowse.py:374
#, fuzzy
msgid "Upload Folder…"
msgstr "Nusiųsti aplanką į…"

#: pynicotine/gtkgui/userbrowse.py:396
#, fuzzy
msgid "Download Folder _To…"
msgstr "Atsiuntimo aplanko klaida"

#: pynicotine/gtkgui/userbrowse.py:600
#, fuzzy
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"Vartotojo bendrinamų failų sąrašas tuščias. Vartotojas nieko nebendrina arba "
"bendrai dalijasi failais privačiai."

#: pynicotine/gtkgui/userbrowse.py:615
#, fuzzy
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Neįmanoma prašyti bendrai naudojamų failų iš vartotojo. Vartotojas yra "
"neprisijungęs, abu turite uždarytą klausymosi prievadą arba yra laikina "
"ryšio problema."

#: pynicotine/gtkgui/userbrowse.py:953
#, fuzzy
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Pasirinkite aplanko atsisiuntimo iš vartotojo paskirties vietą"

#: pynicotine/gtkgui/userbrowse.py:997
#, fuzzy
msgid "Upload Folder (with Subfolders) To User"
msgstr "Įkelti aplanką (su poaplankiais) vartotojui"

#: pynicotine/gtkgui/userbrowse.py:999
#, fuzzy
msgid "Upload Folder To User"
msgstr "Nusiųsti aplanką į…"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
#, fuzzy
msgid "Enter the name of the user you want to upload to:"
msgstr "Nurodkite naudotoją, kuriam norite nusiųsti:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
#, fuzzy
msgid "_Upload"
msgstr "_Išsiuntimai"

#: pynicotine/gtkgui/userbrowse.py:1139
#, fuzzy
msgid "Select Destination Folder for Files"
msgstr "Pasirinkite failo (-ų) atsisiuntimo iš vartotojo paskirties vietą"

#: pynicotine/gtkgui/userbrowse.py:1161
#, fuzzy
msgid "Upload File(s) To User"
msgstr "Nusiųsti failą (-us)"

#: pynicotine/gtkgui/userinfo.py:376
#, fuzzy
msgid "Copy Picture"
msgstr "Nuotraukų:"

#: pynicotine/gtkgui/userinfo.py:377
#, fuzzy
msgid "Save Picture"
msgstr "Įrašyti paveikslėlį"

#: pynicotine/gtkgui/userinfo.py:468
#, fuzzy, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Nepavyko įkelti vartotojo %(user)s: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
#, fuzzy
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Neįmanoma prašyti informacijos iš vartotojo. Abu turite uždarytą klausymosi "
"prievadą, vartotojas yra neprisijungęs arba yra laikina ryšio problema."

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Remove _Buddy"
msgstr "Pašalinti alternatyvų vardą"

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Add _Buddy"
msgstr "Pridėti bičiulį…"

#: pynicotine/gtkgui/userinfo.py:581
#, fuzzy
msgid "Unban User"
msgstr "Uždrausti vartotoją"

#: pynicotine/gtkgui/userinfo.py:585
#, fuzzy
msgid "Unignore User"
msgstr "Nepaisyti vartotojo"

#: pynicotine/gtkgui/userinfo.py:613
msgid "Yes"
msgstr "Taip"

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr "Ne"

#: pynicotine/gtkgui/userinfo.py:773
#, fuzzy
msgid "Please enter number of days."
msgstr "Įveskite sveikąjį skaičių!"

#: pynicotine/gtkgui/userinfo.py:787
#, fuzzy, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Dovanokite savo „Soulseek“ privilegijas vartotojui %(user)s (%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, python-format
msgid "%(days)s days left"
msgstr "liko %(days)s"

#: pynicotine/gtkgui/userinfo.py:795
#, fuzzy
msgid "Gift Privileges"
msgstr "Suteikti privilegijas"

#: pynicotine/gtkgui/userinfo.py:797
#, fuzzy
msgid "_Give Privileges"
msgstr "Suteikti privilegijas"

#: pynicotine/gtkgui/widgets/dialogs.py:309
#, fuzzy
msgid "Close"
msgstr "Užvėrimo mygtukai kortelėse"

#: pynicotine/gtkgui/widgets/dialogs.py:488
#, fuzzy
msgid "_Yes"
msgstr "_ Taip"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
#, fuzzy
msgid "_OK"
msgstr "Gerai"

#: pynicotine/gtkgui/widgets/filechooser.py:39
#, fuzzy
msgid "Select a File"
msgstr "Pasirinkite failą"

#: pynicotine/gtkgui/widgets/filechooser.py:174
#, fuzzy
msgid "Select a Folder"
msgstr "Pasirinkite aplanką"

#: pynicotine/gtkgui/widgets/filechooser.py:179
#, fuzzy
msgid "_Select"
msgstr "Žymėti viską"

#: pynicotine/gtkgui/widgets/filechooser.py:196
#, fuzzy
msgid "Select an Image"
msgstr "Pasirinkite vaizdą"

#: pynicotine/gtkgui/widgets/filechooser.py:203
#, fuzzy
msgid "All images"
msgstr "Visi vaizdai"

#: pynicotine/gtkgui/widgets/filechooser.py:241
#, fuzzy
msgid "Save as…"
msgstr "Įrašyti kaip…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
#, fuzzy
msgid "(None)"
msgstr "(Nėra)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
#, fuzzy
msgid "Close Tab"
msgstr "skirtukas _Close"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
#, fuzzy
msgid "Close All Tabs?"
msgstr "Uždaryti visus skirtukus?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
#, fuzzy
msgid "Do you really want to close all tabs?"
msgstr "Nurodkite naudotoją, kuriam norite nusiųsti:"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, fuzzy, python-format
msgid "%i Unread Tab(s)"
msgstr "Neskaityti skirtukai"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
#, fuzzy
msgid "All Tabs"
msgstr "Uždaryti visus skirtukus?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
#, fuzzy
msgid "Re_open Closed Tab"
msgstr "skirtukas _Close"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, fuzzy, python-format
msgid "%s File(s) Selected"
msgstr "%s Pasirinktas (-i) failas (-ai)"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
#, fuzzy
msgid "_Browse Files"
msgstr "_Ieškoti failų"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
#, fuzzy
msgid "_Add Buddy"
msgstr "Pridėti bičiulį…"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
#, fuzzy
msgid "Show IP A_ddress"
msgstr "Rodyti IP A_ddress"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
#, fuzzy
msgid "Private Rooms"
msgstr "Privatūs kambariai"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, fuzzy, python-format
msgid "Remove from Private Room %s"
msgstr "Pašalinti iš asmeninio kambario %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, fuzzy, python-format
msgid "Add to Private Room %s"
msgstr "Įtraukti į asmeninį kambarį %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, fuzzy, python-format
msgid "Remove as Operator of %s"
msgstr "Pašalinti kaip %s operatorių"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, fuzzy, python-format
msgid "Add as Operator of %s"
msgstr "Įtraukti kaip %s operatorių"

#: pynicotine/gtkgui/widgets/textentry.py:37
#, fuzzy
msgid "Send message…"
msgstr "Siųsti žinutę"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
#, fuzzy
msgid "Find Previous Match"
msgstr "Rasti ankstesnį atitikmenį"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
#, fuzzy
msgid "Find Next Match"
msgstr "Rasti kitą atitikmenį"

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr "--- viršuje seni pranešimai ---"

#: pynicotine/gtkgui/widgets/theme.py:243
#, fuzzy
msgid "Executable"
msgstr "Įvykdyta: %s"

#: pynicotine/gtkgui/widgets/theme.py:244
#, fuzzy
msgid "Audio"
msgstr "Garsas"

#: pynicotine/gtkgui/widgets/theme.py:245
#, fuzzy
msgid "Image"
msgstr "Paveikslėlis:"

#: pynicotine/gtkgui/widgets/theme.py:246
#, fuzzy
msgid "Archive"
msgstr "Archyvas"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
#, fuzzy
msgid "Miscellaneous"
msgstr "Įvairūs"

#: pynicotine/gtkgui/widgets/theme.py:248
#, fuzzy
msgid "Video"
msgstr "Vaizdo įrašas"

#: pynicotine/gtkgui/widgets/theme.py:249
#, fuzzy
msgid "Document"
msgstr "Dokumentas/tekstas"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:357
#, fuzzy, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "Įkeliant pasirinktinę piktogramą %(path)s: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Hide Nicotine+"
msgstr "Paslėpti Nicotine+"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Show Nicotine+"
msgstr "Rodyti Nicotine+"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, python-format
msgid "Column #%i"
msgstr "Stulpelis #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
#, fuzzy
msgid "Ungrouped"
msgstr "Išgrupuota"

#: pynicotine/gtkgui/widgets/treeview.py:960
#, fuzzy
msgid "Group by Folder"
msgstr "Grupuoti pagal aplanką"

#: pynicotine/gtkgui/widgets/treeview.py:963
#, fuzzy
msgid "Group by User"
msgstr "Grupuoti pagal vartotoją"

#: pynicotine/headless/application.py:77
#, fuzzy, python-format
msgid "Do you really want to exit? %s"
msgstr "Ar tikrai norite išeiti?"

#: pynicotine/headless/application.py:81
#, fuzzy, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr ""
"Vartotojas %s jau yra, o įvestas slaptažodis neleistinas. Pasirinkite kitą "
"naudotojo vardą, jei tai pirmas kartas, kai prisijungiate."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr ""

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
#, fuzzy
msgid "Password: "
msgstr "Slaptažodis"

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
#, fuzzy
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Norėdami sukurti naują Soulseek paskyrą, užpildykite norimą vartotojo vardą "
"ir slaptažodį. Jei jau turite paskyrą, užpildykite esamus prisijungimo "
"duomenis."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr ""

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr ""

#: pynicotine/logfacility.py:181
#, fuzzy, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr "Nepavyko perkelti „%(tempfile)s“ į „%(file)s“: %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, fuzzy, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Neįmanoma įrašyti failo %(path)s: %(error)s"

#: pynicotine/networkfilter.py:40
#, fuzzy
msgid "Andorra"
msgstr "Andora"

#: pynicotine/networkfilter.py:41
#, fuzzy
msgid "United Arab Emirates"
msgstr "Jungtiniai Arabų Emyratai"

#: pynicotine/networkfilter.py:42
#, fuzzy
msgid "Afghanistan"
msgstr "Afganistanas"

#: pynicotine/networkfilter.py:43
#, fuzzy
msgid "Antigua & Barbuda"
msgstr "Antigva & Barbuda"

#: pynicotine/networkfilter.py:44
#, fuzzy
msgid "Anguilla"
msgstr "Angilija"

#: pynicotine/networkfilter.py:45
#, fuzzy
msgid "Albania"
msgstr "Albanija"

#: pynicotine/networkfilter.py:46
#, fuzzy
msgid "Armenia"
msgstr "Armėnija"

#: pynicotine/networkfilter.py:47
#, fuzzy
msgid "Angola"
msgstr "Angola"

#: pynicotine/networkfilter.py:48
#, fuzzy
msgid "Antarctica"
msgstr "Antarktida"

#: pynicotine/networkfilter.py:49
#, fuzzy
msgid "Argentina"
msgstr "Argentina"

#: pynicotine/networkfilter.py:50
#, fuzzy
msgid "American Samoa"
msgstr "Amerikos Samoa"

#: pynicotine/networkfilter.py:51
#, fuzzy
msgid "Austria"
msgstr "Austrija"

#: pynicotine/networkfilter.py:52
#, fuzzy
msgid "Australia"
msgstr "Australija"

#: pynicotine/networkfilter.py:53
#, fuzzy
msgid "Aruba"
msgstr "Aruba"

#: pynicotine/networkfilter.py:54
#, fuzzy
msgid "Åland Islands"
msgstr "Alandų salos"

#: pynicotine/networkfilter.py:55
#, fuzzy
msgid "Azerbaijan"
msgstr "Azerbaidžanas"

#: pynicotine/networkfilter.py:56
#, fuzzy
msgid "Bosnia & Herzegovina"
msgstr "Bosnija ir Hercegovina"

#: pynicotine/networkfilter.py:57
#, fuzzy
msgid "Barbados"
msgstr "Barbadosas"

#: pynicotine/networkfilter.py:58
#, fuzzy
msgid "Bangladesh"
msgstr "Bangladešas"

#: pynicotine/networkfilter.py:59
#, fuzzy
msgid "Belgium"
msgstr "Belgija"

#: pynicotine/networkfilter.py:60
#, fuzzy
msgid "Burkina Faso"
msgstr "Burkina Fasas"

#: pynicotine/networkfilter.py:61
#, fuzzy
msgid "Bulgaria"
msgstr "Bulgarija"

#: pynicotine/networkfilter.py:62
#, fuzzy
msgid "Bahrain"
msgstr "Bahreinas"

#: pynicotine/networkfilter.py:63
#, fuzzy
msgid "Burundi"
msgstr "Burundis"

#: pynicotine/networkfilter.py:64
#, fuzzy
msgid "Benin"
msgstr "Beninas"

#: pynicotine/networkfilter.py:65
#, fuzzy
msgid "Saint Barthelemy"
msgstr "Šventoji Barthelemy"

#: pynicotine/networkfilter.py:66
#, fuzzy
msgid "Bermuda"
msgstr "Bermuda"

#: pynicotine/networkfilter.py:67
#, fuzzy
msgid "Brunei Darussalam"
msgstr "Brunėjaus Darusalamas"

#: pynicotine/networkfilter.py:68
#, fuzzy
msgid "Bolivia"
msgstr "Bolivija"

#: pynicotine/networkfilter.py:69
#, fuzzy
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius ir Saba"

#: pynicotine/networkfilter.py:70
#, fuzzy
msgid "Brazil"
msgstr "Brazilija"

#: pynicotine/networkfilter.py:71
#, fuzzy
msgid "Bahamas"
msgstr "Bahamos"

#: pynicotine/networkfilter.py:72
#, fuzzy
msgid "Bhutan"
msgstr "Butanas"

#: pynicotine/networkfilter.py:73
#, fuzzy
msgid "Bouvet Island"
msgstr "Bouvet sala"

#: pynicotine/networkfilter.py:74
#, fuzzy
msgid "Botswana"
msgstr "Botsvana"

#: pynicotine/networkfilter.py:75
#, fuzzy
msgid "Belarus"
msgstr "Baltarusija"

#: pynicotine/networkfilter.py:76
#, fuzzy
msgid "Belize"
msgstr "Belizas"

#: pynicotine/networkfilter.py:77
#, fuzzy
msgid "Canada"
msgstr "Kanada"

#: pynicotine/networkfilter.py:78
#, fuzzy
msgid "Cocos (Keeling) Islands"
msgstr "Kokosų (Keeling) salos"

#: pynicotine/networkfilter.py:79
#, fuzzy
msgid "Democratic Republic of Congo"
msgstr "Kongo Demokratinė Respublika"

#: pynicotine/networkfilter.py:80
#, fuzzy
msgid "Central African Republic"
msgstr "Centrinės Afrikos Respublika"

#: pynicotine/networkfilter.py:81
#, fuzzy
msgid "Congo"
msgstr "Kongas"

#: pynicotine/networkfilter.py:82
#, fuzzy
msgid "Switzerland"
msgstr "Šveicarija"

#: pynicotine/networkfilter.py:83
#, fuzzy
msgid "Ivory Coast"
msgstr "Dramblio Kaulo Krantas"

#: pynicotine/networkfilter.py:84
#, fuzzy
msgid "Cook Islands"
msgstr "Kuko Salos"

#: pynicotine/networkfilter.py:85
#, fuzzy
msgid "Chile"
msgstr "Čilė"

#: pynicotine/networkfilter.py:86
#, fuzzy
msgid "Cameroon"
msgstr "Kamerūnas"

#: pynicotine/networkfilter.py:87
#, fuzzy
msgid "China"
msgstr "Kinija"

#: pynicotine/networkfilter.py:88
#, fuzzy
msgid "Colombia"
msgstr "Kolumbija"

#: pynicotine/networkfilter.py:89
#, fuzzy
msgid "Costa Rica"
msgstr "Kosta Rika"

#: pynicotine/networkfilter.py:90
#, fuzzy
msgid "Cuba"
msgstr "Kuba"

#: pynicotine/networkfilter.py:91
#, fuzzy
msgid "Cabo Verde"
msgstr "Cabo Verdė"

#: pynicotine/networkfilter.py:92
#, fuzzy
msgid "Curaçao"
msgstr "Kiurasao"

#: pynicotine/networkfilter.py:93
#, fuzzy
msgid "Christmas Island"
msgstr "Kalėdų sala"

#: pynicotine/networkfilter.py:94
#, fuzzy
msgid "Cyprus"
msgstr "Kipras"

#: pynicotine/networkfilter.py:95
#, fuzzy
msgid "Czechia"
msgstr "Čekija"

#: pynicotine/networkfilter.py:96
#, fuzzy
msgid "Germany"
msgstr "Vokietija"

#: pynicotine/networkfilter.py:97
#, fuzzy
msgid "Djibouti"
msgstr "Džibutis"

#: pynicotine/networkfilter.py:98
#, fuzzy
msgid "Denmark"
msgstr "Danija"

#: pynicotine/networkfilter.py:99
#, fuzzy
msgid "Dominica"
msgstr "Dominika"

#: pynicotine/networkfilter.py:100
#, fuzzy
msgid "Dominican Republic"
msgstr "Dominikos Respublika"

#: pynicotine/networkfilter.py:101
#, fuzzy
msgid "Algeria"
msgstr "Alžyras"

#: pynicotine/networkfilter.py:102
#, fuzzy
msgid "Ecuador"
msgstr "Ekvadoras"

#: pynicotine/networkfilter.py:103
#, fuzzy
msgid "Estonia"
msgstr "Estija"

#: pynicotine/networkfilter.py:104
#, fuzzy
msgid "Egypt"
msgstr "Egiptas"

#: pynicotine/networkfilter.py:105
#, fuzzy
msgid "Western Sahara"
msgstr "Vakarų Sachara"

#: pynicotine/networkfilter.py:106
#, fuzzy
msgid "Eritrea"
msgstr "Eritrėja"

#: pynicotine/networkfilter.py:107
#, fuzzy
msgid "Spain"
msgstr "Ispanija"

#: pynicotine/networkfilter.py:108
#, fuzzy
msgid "Ethiopia"
msgstr "Etiopija"

#: pynicotine/networkfilter.py:109
#, fuzzy
msgid "Europe"
msgstr "Europa"

#: pynicotine/networkfilter.py:110
#, fuzzy
msgid "Finland"
msgstr "Suomija"

#: pynicotine/networkfilter.py:111
#, fuzzy
msgid "Fiji"
msgstr "Fidžis"

#: pynicotine/networkfilter.py:112
#, fuzzy
msgid "Falkland Islands (Malvinas)"
msgstr "Folklando salos (Malvinai)"

#: pynicotine/networkfilter.py:113
#, fuzzy
msgid "Micronesia"
msgstr "Mikronezija"

#: pynicotine/networkfilter.py:114
#, fuzzy
msgid "Faroe Islands"
msgstr "Farerų salos"

#: pynicotine/networkfilter.py:115
#, fuzzy
msgid "France"
msgstr "Prancūzija"

#: pynicotine/networkfilter.py:116
#, fuzzy
msgid "Gabon"
msgstr "Gabonas"

#: pynicotine/networkfilter.py:117
#, fuzzy
msgid "Great Britain"
msgstr "Didžioji Britanija"

#: pynicotine/networkfilter.py:118
#, fuzzy
msgid "Grenada"
msgstr "Grenada"

#: pynicotine/networkfilter.py:119
#, fuzzy
msgid "Georgia"
msgstr "Gruzija"

#: pynicotine/networkfilter.py:120
#, fuzzy
msgid "French Guiana"
msgstr "Prancūzijos Gviana"

#: pynicotine/networkfilter.py:121
#, fuzzy
msgid "Guernsey"
msgstr "Gernsis"

#: pynicotine/networkfilter.py:122
#, fuzzy
msgid "Ghana"
msgstr "Gana"

#: pynicotine/networkfilter.py:123
#, fuzzy
msgid "Gibraltar"
msgstr "Gibraltaras"

#: pynicotine/networkfilter.py:124
#, fuzzy
msgid "Greenland"
msgstr "Grenlandija"

#: pynicotine/networkfilter.py:125
#, fuzzy
msgid "Gambia"
msgstr "Gambija"

#: pynicotine/networkfilter.py:126
#, fuzzy
msgid "Guinea"
msgstr "Gvinėja"

#: pynicotine/networkfilter.py:127
#, fuzzy
msgid "Guadeloupe"
msgstr "Gvadelupa"

#: pynicotine/networkfilter.py:128
#, fuzzy
msgid "Equatorial Guinea"
msgstr "Pusiaujo Gvinėja"

#: pynicotine/networkfilter.py:129
#, fuzzy
msgid "Greece"
msgstr "Graikija"

#: pynicotine/networkfilter.py:130
#, fuzzy
msgid "South Georgia & South Sandwich Islands"
msgstr "Pietų Džordžija & Pietų Sandvičo salos"

#: pynicotine/networkfilter.py:131
#, fuzzy
msgid "Guatemala"
msgstr "Gvatemala"

#: pynicotine/networkfilter.py:132
#, fuzzy
msgid "Guam"
msgstr "Guamas"

#: pynicotine/networkfilter.py:133
#, fuzzy
msgid "Guinea-Bissau"
msgstr "Bisau Gvinėja"

#: pynicotine/networkfilter.py:134
#, fuzzy
msgid "Guyana"
msgstr "Gajana"

#: pynicotine/networkfilter.py:135
#, fuzzy
msgid "Hong Kong"
msgstr "Honkongas"

#: pynicotine/networkfilter.py:136
#, fuzzy
msgid "Heard & McDonald Islands"
msgstr "Heard & McDonald salos"

#: pynicotine/networkfilter.py:137
#, fuzzy
msgid "Honduras"
msgstr "Hondūras"

#: pynicotine/networkfilter.py:138
#, fuzzy
msgid "Croatia"
msgstr "Kroatija"

#: pynicotine/networkfilter.py:139
#, fuzzy
msgid "Haiti"
msgstr "Haitis"

#: pynicotine/networkfilter.py:140
#, fuzzy
msgid "Hungary"
msgstr "Vengrija"

#: pynicotine/networkfilter.py:141
#, fuzzy
msgid "Indonesia"
msgstr "Indonezija"

#: pynicotine/networkfilter.py:142
#, fuzzy
msgid "Ireland"
msgstr "Airija"

#: pynicotine/networkfilter.py:143
#, fuzzy
msgid "Israel"
msgstr "Izraelis"

#: pynicotine/networkfilter.py:144
#, fuzzy
msgid "Isle of Man"
msgstr "Meno sala"

#: pynicotine/networkfilter.py:145
#, fuzzy
msgid "India"
msgstr "Indija"

#: pynicotine/networkfilter.py:146
#, fuzzy
msgid "British Indian Ocean Territory"
msgstr "Britų Indijos vandenyno teritorija"

#: pynicotine/networkfilter.py:147
#, fuzzy
msgid "Iraq"
msgstr "Irakas"

#: pynicotine/networkfilter.py:148
#, fuzzy
msgid "Iran"
msgstr "Iranas"

#: pynicotine/networkfilter.py:149
#, fuzzy
msgid "Iceland"
msgstr "Islandija"

#: pynicotine/networkfilter.py:150
#, fuzzy
msgid "Italy"
msgstr "Italija"

#: pynicotine/networkfilter.py:151
#, fuzzy
msgid "Jersey"
msgstr "Džersis"

#: pynicotine/networkfilter.py:152
#, fuzzy
msgid "Jamaica"
msgstr "Jamaika"

#: pynicotine/networkfilter.py:153
#, fuzzy
msgid "Jordan"
msgstr "Jordanija"

#: pynicotine/networkfilter.py:154
#, fuzzy
msgid "Japan"
msgstr "Japonija"

#: pynicotine/networkfilter.py:155
#, fuzzy
msgid "Kenya"
msgstr "Kenija"

#: pynicotine/networkfilter.py:156
#, fuzzy
msgid "Kyrgyzstan"
msgstr "Kirgizija"

#: pynicotine/networkfilter.py:157
#, fuzzy
msgid "Cambodia"
msgstr "Kambodža"

#: pynicotine/networkfilter.py:158
#, fuzzy
msgid "Kiribati"
msgstr "Kiribatis"

#: pynicotine/networkfilter.py:159
#, fuzzy
msgid "Comoros"
msgstr "Komorai"

#: pynicotine/networkfilter.py:160
#, fuzzy
msgid "Saint Kitts & Nevis"
msgstr "Sent Kitsas & Nevis"

#: pynicotine/networkfilter.py:161
#, fuzzy
msgid "North Korea"
msgstr "Šiaurės Korėja"

#: pynicotine/networkfilter.py:162
#, fuzzy
msgid "South Korea"
msgstr "Pietų Korėja"

#: pynicotine/networkfilter.py:163
#, fuzzy
msgid "Kuwait"
msgstr "Kuveitas"

#: pynicotine/networkfilter.py:164
#, fuzzy
msgid "Cayman Islands"
msgstr "Kaimanų salos"

#: pynicotine/networkfilter.py:165
#, fuzzy
msgid "Kazakhstan"
msgstr "Kazachstanas"

#: pynicotine/networkfilter.py:166
#, fuzzy
msgid "Laos"
msgstr "Laosas"

#: pynicotine/networkfilter.py:167
#, fuzzy
msgid "Lebanon"
msgstr "Libanas"

#: pynicotine/networkfilter.py:168
#, fuzzy
msgid "Saint Lucia"
msgstr "Sent Lusija"

#: pynicotine/networkfilter.py:169
#, fuzzy
msgid "Liechtenstein"
msgstr "Lichtenšteinas"

#: pynicotine/networkfilter.py:170
#, fuzzy
msgid "Sri Lanka"
msgstr "Šri Lanka"

#: pynicotine/networkfilter.py:171
#, fuzzy
msgid "Liberia"
msgstr "Liberija"

#: pynicotine/networkfilter.py:172
#, fuzzy
msgid "Lesotho"
msgstr "Lesotas"

#: pynicotine/networkfilter.py:173
#, fuzzy
msgid "Lithuania"
msgstr "Lietuva"

#: pynicotine/networkfilter.py:174
#, fuzzy
msgid "Luxembourg"
msgstr "Liuksemburgas"

#: pynicotine/networkfilter.py:175
#, fuzzy
msgid "Latvia"
msgstr "Latvija"

#: pynicotine/networkfilter.py:176
#, fuzzy
msgid "Libya"
msgstr "Libija"

#: pynicotine/networkfilter.py:177
#, fuzzy
msgid "Morocco"
msgstr "Marokas"

#: pynicotine/networkfilter.py:178
#, fuzzy
msgid "Monaco"
msgstr "Monakas"

#: pynicotine/networkfilter.py:179
#, fuzzy
msgid "Moldova"
msgstr "Moldavija"

#: pynicotine/networkfilter.py:180
#, fuzzy
msgid "Montenegro"
msgstr "Juodkalnija"

#: pynicotine/networkfilter.py:181
#, fuzzy
msgid "Saint Martin"
msgstr "Sent Martenas"

#: pynicotine/networkfilter.py:182
#, fuzzy
msgid "Madagascar"
msgstr "Madagaskaras"

#: pynicotine/networkfilter.py:183
#, fuzzy
msgid "Marshall Islands"
msgstr "Maršalo salos"

#: pynicotine/networkfilter.py:184
#, fuzzy
msgid "North Macedonia"
msgstr "Šiaurės Makedonija"

#: pynicotine/networkfilter.py:185
#, fuzzy
msgid "Mali"
msgstr "Malis"

#: pynicotine/networkfilter.py:186
#, fuzzy
msgid "Myanmar"
msgstr "Mianmaras"

#: pynicotine/networkfilter.py:187
#, fuzzy
msgid "Mongolia"
msgstr "Mongolija"

#: pynicotine/networkfilter.py:188
#, fuzzy
msgid "Macau"
msgstr "Makao"

#: pynicotine/networkfilter.py:189
#, fuzzy
msgid "Northern Mariana Islands"
msgstr "Šiaurės Marianų salos"

#: pynicotine/networkfilter.py:190
#, fuzzy
msgid "Martinique"
msgstr "Martinika"

#: pynicotine/networkfilter.py:191
#, fuzzy
msgid "Mauritania"
msgstr "Mauritanija"

#: pynicotine/networkfilter.py:192
#, fuzzy
msgid "Montserrat"
msgstr "Montseratas"

#: pynicotine/networkfilter.py:193
#, fuzzy
msgid "Malta"
msgstr "Malta"

#: pynicotine/networkfilter.py:194
#, fuzzy
msgid "Mauritius"
msgstr "Mauricijus"

#: pynicotine/networkfilter.py:195
#, fuzzy
msgid "Maldives"
msgstr "Maldyvai"

#: pynicotine/networkfilter.py:196
#, fuzzy
msgid "Malawi"
msgstr "Malavis"

#: pynicotine/networkfilter.py:197
#, fuzzy
msgid "Mexico"
msgstr "Meksika"

#: pynicotine/networkfilter.py:198
#, fuzzy
msgid "Malaysia"
msgstr "Malaizija"

#: pynicotine/networkfilter.py:199
#, fuzzy
msgid "Mozambique"
msgstr "Mozambikas"

#: pynicotine/networkfilter.py:200
#, fuzzy
msgid "Namibia"
msgstr "Namibija"

#: pynicotine/networkfilter.py:201
#, fuzzy
msgid "New Caledonia"
msgstr "Naujoji Kaledonija"

#: pynicotine/networkfilter.py:202
#, fuzzy
msgid "Niger"
msgstr "Nigeris"

#: pynicotine/networkfilter.py:203
#, fuzzy
msgid "Norfolk Island"
msgstr "Norfolko sala"

#: pynicotine/networkfilter.py:204
#, fuzzy
msgid "Nigeria"
msgstr "Nigerija"

#: pynicotine/networkfilter.py:205
#, fuzzy
msgid "Nicaragua"
msgstr "Nikaragva"

#: pynicotine/networkfilter.py:206
#, fuzzy
msgid "Netherlands"
msgstr "Nyderlandai"

#: pynicotine/networkfilter.py:207
#, fuzzy
msgid "Norway"
msgstr "Norvegija"

#: pynicotine/networkfilter.py:208
#, fuzzy
msgid "Nepal"
msgstr "Nepalas"

#: pynicotine/networkfilter.py:209
#, fuzzy
msgid "Nauru"
msgstr "Nauru"

#: pynicotine/networkfilter.py:210
#, fuzzy
msgid "Niue"
msgstr "Niujė"

#: pynicotine/networkfilter.py:211
#, fuzzy
msgid "New Zealand"
msgstr "Naujoji Zelandija"

#: pynicotine/networkfilter.py:212
#, fuzzy
msgid "Oman"
msgstr "Omanas"

#: pynicotine/networkfilter.py:213
#, fuzzy
msgid "Panama"
msgstr "Panama"

#: pynicotine/networkfilter.py:214
#, fuzzy
msgid "Peru"
msgstr "Peru"

#: pynicotine/networkfilter.py:215
#, fuzzy
msgid "French Polynesia"
msgstr "Prancūzijos Polinezija"

#: pynicotine/networkfilter.py:216
#, fuzzy
msgid "Papua New Guinea"
msgstr "Papua Naujoji Gvinėja"

#: pynicotine/networkfilter.py:217
#, fuzzy
msgid "Philippines"
msgstr "Filipinai"

#: pynicotine/networkfilter.py:218
#, fuzzy
msgid "Pakistan"
msgstr "Pakistanas"

#: pynicotine/networkfilter.py:219
#, fuzzy
msgid "Poland"
msgstr "Lenkija"

#: pynicotine/networkfilter.py:220
#, fuzzy
msgid "Saint Pierre & Miquelon"
msgstr "Sen Pjeras & Mikelonas"

#: pynicotine/networkfilter.py:221
#, fuzzy
msgid "Pitcairn"
msgstr "Pitkernas"

#: pynicotine/networkfilter.py:222
#, fuzzy
msgid "Puerto Rico"
msgstr "Puerto Rikas"

#: pynicotine/networkfilter.py:223
#, fuzzy
msgid "State of Palestine"
msgstr "Palestinos valstybė"

#: pynicotine/networkfilter.py:224
#, fuzzy
msgid "Portugal"
msgstr "Portugalija"

#: pynicotine/networkfilter.py:225
#, fuzzy
msgid "Palau"
msgstr "Palau"

#: pynicotine/networkfilter.py:226
#, fuzzy
msgid "Paraguay"
msgstr "Paragvajus"

#: pynicotine/networkfilter.py:227
#, fuzzy
msgid "Qatar"
msgstr "Kataras"

#: pynicotine/networkfilter.py:228
#, fuzzy
msgid "Réunion"
msgstr "Reunionas"

#: pynicotine/networkfilter.py:229
#, fuzzy
msgid "Romania"
msgstr "Rumunija"

#: pynicotine/networkfilter.py:230
#, fuzzy
msgid "Serbia"
msgstr "Serbija"

#: pynicotine/networkfilter.py:231
#, fuzzy
msgid "Russia"
msgstr "Rusija"

#: pynicotine/networkfilter.py:232
#, fuzzy
msgid "Rwanda"
msgstr "Ruanda"

#: pynicotine/networkfilter.py:233
#, fuzzy
msgid "Saudi Arabia"
msgstr "Saudo Arabija"

#: pynicotine/networkfilter.py:234
#, fuzzy
msgid "Solomon Islands"
msgstr "Saliamono Salos"

#: pynicotine/networkfilter.py:235
#, fuzzy
msgid "Seychelles"
msgstr "Seišeliai"

#: pynicotine/networkfilter.py:236
#, fuzzy
msgid "Sudan"
msgstr "Sudanas"

#: pynicotine/networkfilter.py:237
#, fuzzy
msgid "Sweden"
msgstr "Švedija"

#: pynicotine/networkfilter.py:238
#, fuzzy
msgid "Singapore"
msgstr "Singapūras"

#: pynicotine/networkfilter.py:239
#, fuzzy
msgid "Saint Helena"
msgstr "Šv. Elenos sala"

#: pynicotine/networkfilter.py:240
#, fuzzy
msgid "Slovenia"
msgstr "Slovėnija"

#: pynicotine/networkfilter.py:241
#, fuzzy
msgid "Svalbard & Jan Mayen Islands"
msgstr "Svalbardas & Jan Majeno salos"

#: pynicotine/networkfilter.py:242
#, fuzzy
msgid "Slovak Republic"
msgstr "Slovakija"

#: pynicotine/networkfilter.py:243
#, fuzzy
msgid "Sierra Leone"
msgstr "Siera Leonė"

#: pynicotine/networkfilter.py:244
#, fuzzy
msgid "San Marino"
msgstr "San Marinas"

#: pynicotine/networkfilter.py:245
#, fuzzy
msgid "Senegal"
msgstr "Senegalas"

#: pynicotine/networkfilter.py:246
#, fuzzy
msgid "Somalia"
msgstr "Somalis"

#: pynicotine/networkfilter.py:247
#, fuzzy
msgid "Suriname"
msgstr "Surinamas"

#: pynicotine/networkfilter.py:248
#, fuzzy
msgid "South Sudan"
msgstr "Pietų Sudanas"

#: pynicotine/networkfilter.py:249
#, fuzzy
msgid "Sao Tome & Principe"
msgstr "San Tomė > Prinsipė"

#: pynicotine/networkfilter.py:250
#, fuzzy
msgid "El Salvador"
msgstr "Salvadoras"

#: pynicotine/networkfilter.py:251
#, fuzzy
msgid "Sint Maarten"
msgstr "Sint Maarten"

#: pynicotine/networkfilter.py:252
#, fuzzy
msgid "Syria"
msgstr "Sirija"

#: pynicotine/networkfilter.py:253
#, fuzzy
msgid "Eswatini"
msgstr "Esvatinis"

#: pynicotine/networkfilter.py:254
#, fuzzy
msgid "Turks & Caicos Islands"
msgstr "Turksas > Kaiicoso salos"

#: pynicotine/networkfilter.py:255
#, fuzzy
msgid "Chad"
msgstr "Čadas"

#: pynicotine/networkfilter.py:256
#, fuzzy
msgid "French Southern Territories"
msgstr "Prancūzijos pietinės teritorijos"

#: pynicotine/networkfilter.py:257
#, fuzzy
msgid "Togo"
msgstr "Togas"

#: pynicotine/networkfilter.py:258
#, fuzzy
msgid "Thailand"
msgstr "Tailandas"

#: pynicotine/networkfilter.py:259
#, fuzzy
msgid "Tajikistan"
msgstr "Tadžikija"

#: pynicotine/networkfilter.py:260
#, fuzzy
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
#, fuzzy
msgid "Timor-Leste"
msgstr "Rytų Timoras"

#: pynicotine/networkfilter.py:262
#, fuzzy
msgid "Turkmenistan"
msgstr "Turkmėnistanas"

#: pynicotine/networkfilter.py:263
#, fuzzy
msgid "Tunisia"
msgstr "Tunisas"

#: pynicotine/networkfilter.py:264
#, fuzzy
msgid "Tonga"
msgstr "Tonga"

#: pynicotine/networkfilter.py:265
#, fuzzy
msgid "Türkiye"
msgstr "Turkija"

#: pynicotine/networkfilter.py:266
#, fuzzy
msgid "Trinidad & Tobago"
msgstr "Trinidadas & Tobagas"

#: pynicotine/networkfilter.py:267
#, fuzzy
msgid "Tuvalu"
msgstr "Tuvalu"

#: pynicotine/networkfilter.py:268
#, fuzzy
msgid "Taiwan"
msgstr "Taivanas"

#: pynicotine/networkfilter.py:269
#, fuzzy
msgid "Tanzania"
msgstr "Tanzanija"

#: pynicotine/networkfilter.py:270
#, fuzzy
msgid "Ukraine"
msgstr "Ukraina"

#: pynicotine/networkfilter.py:271
#, fuzzy
msgid "Uganda"
msgstr "Uganda"

#: pynicotine/networkfilter.py:272
#, fuzzy
msgid "U.S. Minor Outlying Islands"
msgstr "JAV Mažosios alyškios salos"

#: pynicotine/networkfilter.py:273
#, fuzzy
msgid "United States"
msgstr "Jungtinės Amerikos Valstijos"

#: pynicotine/networkfilter.py:274
#, fuzzy
msgid "Uruguay"
msgstr "Urugvajus"

#: pynicotine/networkfilter.py:275
#, fuzzy
msgid "Uzbekistan"
msgstr "Uzbekistanas"

#: pynicotine/networkfilter.py:276
#, fuzzy
msgid "Holy See (Vatican City State)"
msgstr "Šventasis Sostas (Vatikano Miesto Valstybė)"

#: pynicotine/networkfilter.py:277
#, fuzzy
msgid "Saint Vincent & The Grenadines"
msgstr "Sent Vinsentas & Grenadinai"

#: pynicotine/networkfilter.py:278
#, fuzzy
msgid "Venezuela"
msgstr "Venesuela"

#: pynicotine/networkfilter.py:279
#, fuzzy
msgid "British Virgin Islands"
msgstr "Mergelių Salos (Didžioji Britanija)"

#: pynicotine/networkfilter.py:280
#, fuzzy
msgid "U.S. Virgin Islands"
msgstr "Mergelių Salos (JAV)"

#: pynicotine/networkfilter.py:281
#, fuzzy
msgid "Viet Nam"
msgstr "Vietnamas"

#: pynicotine/networkfilter.py:282
#, fuzzy
msgid "Vanuatu"
msgstr "Vanuatu"

#: pynicotine/networkfilter.py:283
#, fuzzy
msgid "Wallis & Futuna"
msgstr "Volisas > Futuna"

#: pynicotine/networkfilter.py:284
#, fuzzy
msgid "Samoa"
msgstr "Samoa"

#: pynicotine/networkfilter.py:285
#, fuzzy
msgid "Yemen"
msgstr "Jemenas"

#: pynicotine/networkfilter.py:286
#, fuzzy
msgid "Mayotte"
msgstr "Majotas"

#: pynicotine/networkfilter.py:287
#, fuzzy
msgid "South Africa"
msgstr "Pietų Afrika"

#: pynicotine/networkfilter.py:288
#, fuzzy
msgid "Zambia"
msgstr "Zambija"

#: pynicotine/networkfilter.py:289
#, fuzzy
msgid "Zimbabwe"
msgstr "Zimbabvė"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, fuzzy, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "Pranešimo tekstas į kalbą nepavyko: %s"

#: pynicotine/nowplaying.py:130
#, fuzzy
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr "Last.fm: pateikite Last.fm naudotojo vardą ir API raktą"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
#, fuzzy
msgid "Now Playing Error"
msgstr "Grojamos dainos rodymo formatas:"

#: pynicotine/nowplaying.py:140
#, fuzzy, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm: Nepavyko prisijungti prie Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:162
#, fuzzy, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr "Last.fm: Nepavyko gauti naujausio takelio iš Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:196
#, fuzzy
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: Nepavyko rasti tinkamo MPRIS grotuvo"

#: pynicotine/nowplaying.py:201
#, fuzzy, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr "Rasti keli MPRIS grotuvai: %(players)s. Naudojimas: %(player)s"

#: pynicotine/nowplaying.py:204
#, fuzzy, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Automatiškai aptiktas MPRIS grotuvas: %s"

#: pynicotine/nowplaying.py:219
#, fuzzy, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr "Išimtis apdorojant %(area)s: %(exception)s"

#: pynicotine/nowplaying.py:266
#, fuzzy
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "ListenBrainz: Prašome pateikti savo ListenBrainz vartotojo vardą"

#: pynicotine/nowplaying.py:275
#, fuzzy, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "ListenBrainz: Nepavyko prisijungti prie ListenBrainz: %(error)s"

#: pynicotine/nowplaying.py:283
#, fuzzy
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "ListenBrainz: Jums neatrodo, kad klausytis nieko dabar"

#: pynicotine/nowplaying.py:297
#, fuzzy, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz: Nepavyko gauti dabartinio takelio iš ListenBrainz: %(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
#, fuzzy
msgid "Network Filters"
msgstr "Tinklo ieškos"

#: pynicotine/plugins/core_commands/__init__.py:44
#, fuzzy
msgid "List available commands"
msgstr "Užbaigti įtaisytųjų komandų"

#: pynicotine/plugins/core_commands/__init__.py:49
#, fuzzy
msgid "Connect to the server"
msgstr "Neįmanoma prisijungti prie serverio. Priežastis: %s"

#: pynicotine/plugins/core_commands/__init__.py:53
#, fuzzy
msgid "Disconnect from the server"
msgstr "Atsijungta nuo serverio %(host)s:%(port)s"

#: pynicotine/plugins/core_commands/__init__.py:58
#, fuzzy
msgid "Toggle away status"
msgstr "Perjungia Jūsų nebuvimo būseną"

#: pynicotine/plugins/core_commands/__init__.py:62
#, fuzzy
msgid "Manage plugins"
msgstr "Įgalinti papildinius"

#: pynicotine/plugins/core_commands/__init__.py:74
#, fuzzy
msgid "Clear chat window"
msgstr "Išvalyti pokalbio langą"

#: pynicotine/plugins/core_commands/__init__.py:80
msgid "Say something in the third-person"
msgstr "Pasakykite ką nors trečiuoju asmeniu"

#: pynicotine/plugins/core_commands/__init__.py:87
#, fuzzy
msgid "Announce the song currently playing"
msgstr "Paskelbkite šiuo metu grojamą dainą"

#: pynicotine/plugins/core_commands/__init__.py:94
#, fuzzy
msgid "Join chat room"
msgstr "Prisijunkite arba sukurkite kambarį…"

#: pynicotine/plugins/core_commands/__init__.py:102
#, fuzzy
msgid "Leave chat room"
msgstr "Išeiti iš kambario „kambarys“"

#: pynicotine/plugins/core_commands/__init__.py:110
#, fuzzy
msgid "Say message in specified chat room"
msgstr "Pasakykite pranešimą nurodytame pokalbių kambaryje"

#: pynicotine/plugins/core_commands/__init__.py:117
#, fuzzy
msgid "Open private chat"
msgstr "Asmeninis pokalbis"

#: pynicotine/plugins/core_commands/__init__.py:125
#, fuzzy
msgid "Close private chat"
msgstr "Užverti esamą privatų pokalbį"

#: pynicotine/plugins/core_commands/__init__.py:133
#, fuzzy
msgid "Request user's client version"
msgstr "Naudotojo i_nformacija"

#: pynicotine/plugins/core_commands/__init__.py:142
#, fuzzy
msgid "Send private message to user"
msgstr "Siųsti asmeninę žinutę visiems prisijungusiems draugams:"

#: pynicotine/plugins/core_commands/__init__.py:150
#, fuzzy
msgid "Add user to buddy list"
msgstr "Įtraukti naudotoją „naudotojas“ į užblokuotų sąrašą"

#: pynicotine/plugins/core_commands/__init__.py:158
#, fuzzy
msgid "Remove buddy from buddy list"
msgstr "Pašalinti naudotoją „naudotojas“ iš užblokuotų sąrašo"

#: pynicotine/plugins/core_commands/__init__.py:166
#, fuzzy
msgid "Browse files of user"
msgstr "Naršyti naudotojo „naudotojas“ failus"

#: pynicotine/plugins/core_commands/__init__.py:175
#, fuzzy
msgid "Show user profile information"
msgstr "Naudotojo i_nformacija"

#: pynicotine/plugins/core_commands/__init__.py:183
#, fuzzy
msgid "Show IP address or username"
msgstr "Parodyti naudotojo „naudotojas“ IP"

#: pynicotine/plugins/core_commands/__init__.py:190
#, fuzzy
msgid "Block connections from user or IP address"
msgstr "Blokuoti ryšius iš vartotojo arba IP adreso"

#: pynicotine/plugins/core_commands/__init__.py:197
#, fuzzy
msgid "Remove user or IP address from ban lists"
msgstr "Pašalinti naudotoją „naudotojas“ iš užblokuotų sąrašo"

#: pynicotine/plugins/core_commands/__init__.py:204
#, fuzzy
msgid "Silence messages from user or IP address"
msgstr "Nutildyti pranešimus iš vartotojo arba IP adreso"

#: pynicotine/plugins/core_commands/__init__.py:212
#, fuzzy
msgid "Remove user or IP address from ignore lists"
msgstr "Pašalinti naudotoją „naudotojas“ iš nepaisomųjų sąrašo"

#: pynicotine/plugins/core_commands/__init__.py:220
#, fuzzy
msgid "Add share"
msgstr "Pridėti Wi_sh"

#: pynicotine/plugins/core_commands/__init__.py:226
#, fuzzy
msgid "Remove share"
msgstr "Pašalinti alternatyvų vardą"

#: pynicotine/plugins/core_commands/__init__.py:233
#, fuzzy
msgid "List shares"
msgstr "Perskanuoti viešinius"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Perskanuoti viešinius"

#: pynicotine/plugins/core_commands/__init__.py:246
#, fuzzy
msgid "Start global file search"
msgstr "Pradėkite visuotinę failų paiešką"

#: pynicotine/plugins/core_commands/__init__.py:254
#, fuzzy
msgid "Search files in joined rooms"
msgstr "Ieškoti failų ir aplankų (tikslus atitikimas)"

#: pynicotine/plugins/core_commands/__init__.py:262
#, fuzzy
msgid "Search files of all buddies"
msgstr "Ieškoti failų ir aplankų (tikslus atitikimas)"

#: pynicotine/plugins/core_commands/__init__.py:270
#, fuzzy
msgid "Search a user's shared files"
msgstr "Ieškoti „užklausa“ naudotojo viešiniuose"

#: pynicotine/plugins/core_commands/__init__.py:296
#, fuzzy, python-format
msgid "Listing %(num)i available commands:"
msgstr "Galimų %(num)i komandų sąrašas:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, fuzzy, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr "Pateikiamas %(num)i galimų komandų, atitinkančių „%(query)s“, sąrašas:"

#: pynicotine/plugins/core_commands/__init__.py:311
#, fuzzy, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Įveskite %(command)s, kad pateiktumėte panašias komandas"

#: pynicotine/plugins/core_commands/__init__.py:314
#, fuzzy, python-format
msgid "Type %(command)s to list available commands"
msgstr "Įveskite %(command)s, kad pateiktumėte galimas komandas"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, fuzzy, python-format
msgid "Not joined in room %s"
msgstr "%s prisijungė prie kambario"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:408
#, fuzzy, python-format
msgid "Closed private chat of user %s"
msgstr "Užverti esamą privatų pokalbį"

#: pynicotine/plugins/core_commands/__init__.py:476
#, fuzzy, python-format
msgid "Banned %s"
msgstr "Užblokuotas (%s)"

#: pynicotine/plugins/core_commands/__init__.py:490
#, fuzzy, python-format
msgid "Unbanned %s"
msgstr "Užblokuotas (%s)"

#: pynicotine/plugins/core_commands/__init__.py:503
#, fuzzy, python-format
msgid "Ignored %s"
msgstr "Nepaisomi IP:"

#: pynicotine/plugins/core_commands/__init__.py:517
#, fuzzy, python-format
msgid "Unignored %s"
msgstr "Nepaisyti vartotojo"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr ""

#: pynicotine/pluginsystem.py:413
#, fuzzy
msgid "Loading plugin system"
msgstr "Nepavyko įjungti įskiepio %s"

#: pynicotine/pluginsystem.py:516
#, fuzzy, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Neįmanoma įkelti priedo %(name)s. Papildinio aplanko pavadinime yra "
"neleistinų simbolių: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, fuzzy, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "Nesuderinama %(interface)s komanda papildinyje %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, fuzzy, python-format
msgid "Loaded plugin %s"
msgstr "Nepavyko įjungti įskiepio %s"

#: pynicotine/pluginsystem.py:579
#, fuzzy, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Neįmanoma įkelti priedo %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, fuzzy, python-format
msgid "Unloaded plugin %s"
msgstr "Nepavyko įjungti įskiepio %s"

#: pynicotine/pluginsystem.py:648
#, fuzzy, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Neįmanoma iškrauti priedo %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, fuzzy, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Įskiepis %(module)s grąžino klaidą %(errortype)s: %(error)s.\n"
"Pėdsakas: %(trace)s\n"
"Klaidos sritis: %(area)s"

#: pynicotine/pluginsystem.py:810
#, fuzzy
msgid "No description"
msgstr "Saviaprašymas:"

#: pynicotine/pluginsystem.py:887
#, fuzzy, python-format
msgid "Missing %s argument"
msgstr "Trūksta %s argumento"

#: pynicotine/pluginsystem.py:896
#, fuzzy, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Netinkamas SoulSeek meta-url: %s"

#: pynicotine/pluginsystem.py:901
#, fuzzy, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Naudojimas: %(command)s %(args)s"

#: pynicotine/pluginsystem.py:940
#, fuzzy, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Nežinoma komanda: %(command)s. Įveskite %(help_command)s, kad pateiktumėte "
"galimas komandas."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
#, fuzzy
msgid "No UPnP devices found"
msgstr "UPnP įrenginių nerasta"

#: pynicotine/portmapper.py:633
#, fuzzy, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"UPnP: Nepavyko persiųsti išorinio prievado %(external_port)s: %(error)s"

#: pynicotine/portmapper.py:647
#, fuzzy, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"UPnP: išorinis prievadas %(external_port)s sėkmingai persiųstas į vietinį IP "
"adresą %(ip_address)s prievadą %(local_port)s"

#: pynicotine/privatechat.py:220
#, fuzzy, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Asmeninė žinutė nuo %(user)s"

#: pynicotine/search.py:368
#, fuzzy, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Ieškoma pageidavimų sąrašo elemento „%s“"

#: pynicotine/search.py:434
#, fuzzy, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Pageidavimų sąrašo laukimo laikotarpis nustatytas kaip %s sekundės"

#: pynicotine/search.py:760
#, fuzzy, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr "Vartotojas %(user)s ieško \"%(query)s\", grąžina %(num)i rezultatus"

#: pynicotine/shares.py:302
#, fuzzy
msgid "Rebuilding shares…"
msgstr "Skenuojami bičiulių viešiniai"

#: pynicotine/shares.py:302
#, fuzzy
msgid "Rescanning shares…"
msgstr "Skenuojami bičiulių viešiniai"

#: pynicotine/shares.py:324
#, fuzzy, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Pakartotinis nuskaitymas baigtas: rasta %(num)s aplankų"

#: pynicotine/shares.py:334
#, fuzzy, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Iš naujo nuskaitant akcijas įvyko rimta klaida. Jei problema kartosis, "
"panaikinkite %(dir)s/*.db ir bandykite dar kartą. Jei tai nepadeda, "
"pateikite klaidos ataskaitą su šiuo rietuvės sekimas įtrauktas: %(trace)s"

#: pynicotine/shares.py:582
#, fuzzy, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Klaida nuskaitant failą %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, fuzzy, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Klaida nuskaitant aplanką %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, fuzzy, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "Klaida nuskaitant failo %(path)s metaduomenis: %(error)s"

#: pynicotine/shares.py:1046
#, fuzzy, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Pakartotinis nuskaitymas nutrauktas dėl nepasiekiamų bendrinimų"

#: pynicotine/shares.py:1184
#, fuzzy, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "Vartotojas %(user)s naršo jūsų bendrinamų failų sąrašą"

#: pynicotine/slskmessages.py:3120
#, fuzzy, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Neįmanoma nuskaityti bendrai naudojamos duomenų bazės. Prašome perskirstyti "
"savo akcijas. Klaida: %s"

#: pynicotine/slskproto.py:500
#, fuzzy, python-format
msgid "Specified network interface '%s' is not available"
msgstr "Nurodytos tinklo sąsajos „%s“ nėra"

#: pynicotine/slskproto.py:511
#, fuzzy, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Negalima klausytis per prievadą %(port)s. Įsitikinkite, kad jokia kita "
"programa jo nenaudoja, arba pasirinkite kitą prievadą. Klaida: %(error)s"

#: pynicotine/slskproto.py:523
#, fuzzy, python-format
msgid "Listening on port: %i"
msgstr "Klausomasi prievado %i"

#: pynicotine/slskproto.py:805
#, fuzzy, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "Nepavyko prisijungti prie serverio %(host)s:%(port)s: %(error)s"

#: pynicotine/slskproto.py:1095
#, fuzzy, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Automatinis prisijungimas prie serverio paleidžiant"

#: pynicotine/slskproto.py:1170
#, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Jungiamasi prie %(host)s:%(port)s"

#: pynicotine/slskproto.py:1208
#, fuzzy, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Prisijungta prie serverio %(host)s:%(port)s, registruojamasi..."

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Atsijungta nuo serverio %(host)s:%(port)s"

#: pynicotine/slskproto.py:1499
#, fuzzy
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Kažkas prisijungęs prie jūsų Soulseek paskyros kitur"

#: pynicotine/uploads.py:382
#, fuzzy, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Įkelti baigtas: vartotojas %(user)s, IP adresas %(ip)s, failas %(file)s"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Išsiuntimas nutrauktas, naudotojas %(user)s failas %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, python-format
msgid "Upload I/O error: %s"
msgstr "Išsiuntimo I/O klaida: %s"

#: pynicotine/uploads.py:1103
#, fuzzy, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Įkelti prasidėjo: vartotojas %(user)s, IP adresas %(ip)s, failas %(file)s"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr "Nepavyko sukurti aplanko „%(folder)s“, gauta klaida: %(error)s"

#: pynicotine/userbrowse.py:236
#, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Nepavyko įkelti viešinių iš disko: %(error)s"

#: pynicotine/userbrowse.py:282
#, fuzzy, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr "Išsaugotas vartotojo %(user)s\"-%(dir)s bendrinamų failų sąrašas"

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr "Nepavyko išsaugoti viešinių, „%(user)s“, gauta klaida: %(error)s"

#: pynicotine/userinfo.py:160
#, fuzzy, python-format
msgid "Picture saved to %s"
msgstr "Paveikslėlis įrašytas į %s"

#: pynicotine/userinfo.py:163
#, fuzzy, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Nepavyko perkelti „%(tempfile)s“ į „%(file)s“: %(error)s"

#: pynicotine/userinfo.py:190
#, fuzzy, python-format
msgid "User %(user)s is viewing your profile"
msgstr "%(user)s daro UserInfo užklausą"

#: pynicotine/users.py:272
#, fuzzy, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Neįmanoma prisijungti prie serverio. Priežastis: %s"

#: pynicotine/users.py:272
#, fuzzy
msgid "Cannot Connect"
msgstr "Nepavyko prisijungti"

#: pynicotine/users.py:306
#, fuzzy, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr ""
"Neįmanoma nuskaityti vartotojo IP %s, nes šis vartotojas yra neprisijungęs"

#: pynicotine/users.py:315
#, fuzzy, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr ""
"Naudotojo %(user)s IP adresas yra %(ip)s, prievadas %(port)i%(country)s"

#: pynicotine/users.py:433
#, fuzzy
msgid "Soulseek Announcement"
msgstr "Soulseek klientas"

#: pynicotine/users.py:449
#, fuzzy
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"Jūs neturite jokių privilegijų. Teisės nereikalingos, tačiau leidžia jūsų "
"atsisiuntimus įtraukti į eilę prieš privilegijuotus vartotojus."

#: pynicotine/users.py:455
#, fuzzy, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"Liko %(days)i dien., %(hours)i val., %(minutes)i min., %(seconds)i sek. "
"atsisiuntimo privilegijų."

#: pynicotine/users.py:473
#, fuzzy
msgid "Your password has been changed"
msgstr "Jūsų slaptažodis buvo pakeistas"

#: pynicotine/users.py:473
#, fuzzy
msgid "Password Changed"
msgstr "Slaptažodžio keitimas atmestas"

#: pynicotine/utils.py:574
#, fuzzy, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Neįmanoma įrašyti failo %(path)s: %(error)s"

#: pynicotine/utils.py:621
#, fuzzy, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "Nepavyko perkelti „%(tempfile)s“ į „%(file)s“: %(error)s"

#: pynicotine/utils.py:646
#, fuzzy, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr "Skaitant failą %(filename)s kažkas negerai: %(error)s"

#: pynicotine/utils.py:651
#, fuzzy, python-format
msgid "Attempting to load backup of file %s"
msgstr "Bandoma įkelti atsarginę failo kopiją %s"

#: pynicotine/utils.py:673
#, fuzzy, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Neįmanoma sukurti atsarginės failo %(path)s nuotraukos: %(error)s"

#: pynicotine/utils.py:694
#, fuzzy, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Neįmanoma įrašyti failo %(path)s: %(error)s"

#: pynicotine/utils.py:705
#, fuzzy, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Neįmanoma atkurti ankstesnio failo %(path)s: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
#, fuzzy
msgid "Add buddy…"
msgstr "Pridėti bičiulį…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
#, fuzzy
msgid "Toggle Text-to-Speech"
msgstr "Perjungti tekstą į kalbą"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
#, fuzzy
msgid "Chat Room Command Help"
msgstr "Pokalbių kambarių komandų žinynas"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
#, fuzzy
msgid "_Log"
msgstr "_Log"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
#, fuzzy
msgid "Room Wall"
msgstr "Kambario siena"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
#, fuzzy
msgid "R_oom Wall"
msgstr "Kambario siena"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
#, fuzzy
msgid "Created by"
msgstr "Sukurta"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
#, fuzzy
msgid "Translated by"
msgstr "Išvertė"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
#, fuzzy
msgid "License"
msgstr "Licencija"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
msgid "Welcome to Nicotine+"
msgstr "Sveiki atvykę į Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
#, fuzzy
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Jei jūsų norimas naudotojo vardas jau paimtas, būsite paraginti jį pakeisti."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
#, fuzzy
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Jei jūsų klausymosi prievadas uždarytas, galėsite prisijungti tik prie "
"vartotojų, kurių klausymosi prievadai atidaryti."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
#, fuzzy
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"Jei reikia, toliau pasirinkite kitą klausymosi prievadą. Tai taip pat galima "
"padaryti vėliau nuostatose."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
#, fuzzy
msgid "Download Files to Folder"
msgstr "Atsiuntimo aplanko klaida"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
#, fuzzy
msgid "Share Folders"
msgstr "Bendrinti aplankus"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
#, fuzzy
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"„Soulseek“ vartotojai galės atsisiųsti iš jūsų akcijų. Prisidėkite prie "
"„Soulseek“ tinklo dalindamiesi savo kolekcija ir dar kartą bendrindami tai, "
"ką atsisiuntėte iš kitų vartotojų."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
msgid "You are ready to use Nicotine+!"
msgstr "Jūs esate pasirengę naudoti Nicotine+!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
#, fuzzy
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Dovanodami Soulseek suteikia jums privilegijas tam tikrą laikotarpį. Jei "
"turite teises, atsisiuntimai bus įtraukti į eilę prieš privilegijuotus "
"vartotojus."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
#, fuzzy
msgid "Previous File"
msgstr "Ankstesnis failas"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
#, fuzzy
msgid "Next File"
msgstr "Kitas failas"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
#, fuzzy
msgid "Name"
msgstr "Failo vardas"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
#, fuzzy
msgid "Last Speed"
msgstr "Paskutinis greitis"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
#, fuzzy
msgid "_Export…"
msgstr "Eksportuoti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
#, fuzzy
msgid "Keyboard Shortcuts"
msgstr "Spartieji klavišai"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
#, fuzzy
msgid "General"
msgstr "Bendra"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr "Prisijungti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr "Atsijungti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
#, fuzzy
msgid "Rescan Shares"
msgstr "Perskanuoti viešinius"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
#, fuzzy
msgid "Show Log Pane"
msgstr "Rodyti žurnalo sritį"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
#, fuzzy
msgid "Confirm Quit"
msgstr "Konfigūruoti registravimą"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
msgid "Quit"
msgstr "Išeiti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
#, fuzzy
msgid "Menus"
msgstr "Meniu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
#, fuzzy
msgid "Open Main Menu"
msgstr "Atidaryti pagrindinį meniu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
#, fuzzy
msgid "Open Context Menu"
msgstr "Atidaryti kontekstinį meniu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
#, fuzzy
msgid "Tabs"
msgstr "Skirtukai"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
#, fuzzy
msgid "Change Main Tab"
msgstr "Keisti pirminį skirtuką"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
#, fuzzy
msgid "Go to Previous Secondary Tab"
msgstr "Eiti į ankstesnį antrinį skirtuką"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
#, fuzzy
msgid "Go to Next Secondary Tab"
msgstr "Eiti į paskesnį antrinį skirtuką"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
#, fuzzy
msgid "Reopen Closed Secondary Tab"
msgstr "Uždaryti antrinį skirtuką"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
#, fuzzy
msgid "Close Secondary Tab"
msgstr "Uždaryti antrinį skirtuką"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
#, fuzzy
msgid "Lists"
msgstr "Blokavimo sąrašas"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
#, fuzzy
msgid "Copy Selected Cell"
msgstr "Žymėti viską"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
#, fuzzy
msgid "Select All"
msgstr "Žymėti viską"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
msgid "Find"
msgstr "Rasti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
#, fuzzy
msgid "Remove Selected Row"
msgstr "Pašalinti pasirinktą eilutę"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
#, fuzzy
msgid "Editing"
msgstr "Įvertinimas"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
#, fuzzy
msgid "Cut"
msgstr "Valstybė"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
#, fuzzy
msgid "Paste"
msgstr "Įklijuoti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
#, fuzzy
msgid "Insert Emoji"
msgstr "Įterpti jaustukus"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
#, fuzzy
msgid "File Transfers"
msgstr "Siuntimai"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
#, fuzzy
msgid "Resume / Retry Transfer"
msgstr "Pasirinkite naudotojų siuntimus"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
#, fuzzy
msgid "Pause / Abort Transfer"
msgstr "Pristabdyti / nutraukti perkėlimą"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
#, fuzzy
msgid "Download / Upload To"
msgstr "Atsiuntimo aplanko klaida"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
#, fuzzy
msgid "Save List to Disk"
msgstr "_Save akcijų sąrašą į diską"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
#, fuzzy
msgid "Refresh"
msgstr "Atnaujinti failus"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
#, fuzzy
msgid "Expand / Collapse All"
msgstr "Išplėsti / sutraukti visus"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
#, fuzzy
msgid "Back to Parent Folder"
msgstr "Neįmanoma bendrai naudoti aplanko"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
#, fuzzy
msgid "File Search"
msgstr "Ieškoti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
#, fuzzy
msgid "Result Filters"
msgstr "Patikrinti filtrus"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
#, fuzzy
msgid "Current Session"
msgstr "Dabartinis seansas"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
#, fuzzy
msgid "Completed Downloads"
msgstr "Baigti atsisiuntimai"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
#, fuzzy
msgid "Downloaded Size"
msgstr "Atsisiųstas dydis"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
#, fuzzy
msgid "Completed Uploads"
msgstr "Baigti nusiuntimai"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
#, fuzzy
msgid "Uploaded Size"
msgstr "Įkeltas dydis"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
#, fuzzy
msgid "Total"
msgstr "Bendras"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
#, fuzzy
msgid "_Reset…"
msgstr "_Resume"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
#, fuzzy
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Pageidavimų sąrašo elementų automatiškai ieškoma reguliariais intervalais, "
"kad būtų galima aptikti nedažnus failus."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
#, fuzzy
msgid "Add Wish…"
msgstr "Pridėti norą…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
#, fuzzy
msgid "Clear All…"
msgstr "Valyti viską…"

#: pynicotine/gtkgui/ui/downloads.ui:138
#, fuzzy
msgid "Clear All Finished/Filtered Downloads"
msgstr "Atsisiųstas failas"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
#, fuzzy
msgid "Clear Finished"
msgstr "Valyti baigtą"

#: pynicotine/gtkgui/ui/downloads.ui:169
#, fuzzy
msgid "Clear Specific Downloads"
msgstr "Atsiuntimai"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
#, fuzzy
msgid "Clear _All…"
msgstr "Valyti viską…"

#: pynicotine/gtkgui/ui/interests.ui:21
#, fuzzy
msgid "Personal Interests"
msgstr "Pomėgiai"

#: pynicotine/gtkgui/ui/interests.ui:40
#, fuzzy
msgid "Add something you like…"
msgstr "Pridėti tai, kas jums patinka…"

#: pynicotine/gtkgui/ui/interests.ui:62
#, fuzzy
msgid "Personal Dislikes"
msgstr "Mėgsta"

#: pynicotine/gtkgui/ui/interests.ui:80
#, fuzzy
msgid "Add something you dislike…"
msgstr "Pridėti tai, kas jums nepatinka…"

#: pynicotine/gtkgui/ui/interests.ui:143
#, fuzzy
msgid "Refresh Recommendations"
msgstr "Rekomendacijas"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
#, fuzzy
msgid "Main Menu"
msgstr "Atidaryti pagrindinį meniu"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
#, fuzzy
msgid "Room…"
msgstr "Kambarys"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
#, fuzzy
msgid "Username…"
msgstr "Naudotojo vardas"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
#, fuzzy
msgid "Search term…"
msgstr "Paieškos"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Išvalyti"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
#, fuzzy
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr "Paieškos šablonai: su žodžiu = žodis, be žodžio = -žodis"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
#, fuzzy
msgid "Search Scope"
msgstr "Ieškos aprėptis"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
#, fuzzy
msgid "_Wishlist"
msgstr "Ieškoti pageidavimų sąraše"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
#, fuzzy
msgid "Configure Searches"
msgstr "Konfigūruoti iešką"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
#, fuzzy
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Įveskite ieškos terminą, kad ieškotumėte failų, kuriuos bendrai naudoja kiti "
"\"Soulseek\" tinklo vartotojai"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
#, fuzzy
msgid "File Grouping Mode"
msgstr "Failų grupavimo režimas"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
#, fuzzy
msgid "Configure Downloads"
msgstr "Konfigūruoti atsisiuntimus"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
#, fuzzy
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Failai, kuriuos atsisiunčiate iš kitų vartotojų, yra eilėje čia ir gali būti "
"pristabdyti ir atnaujinti pagal pareikalavimą"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
#, fuzzy
msgid "Configure Uploads"
msgstr "Konfigūruoti nusiuntimus"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
#, fuzzy
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Vartotojų bandymai atsisiųsti bendrinamus failus yra eilėje ir tvarkomi čia"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
#, fuzzy
msgid "_Open List"
msgstr "Blokavimo sąrašas"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
#, fuzzy
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Atidaro vietinį bendrinamų failų, kurie anksčiau buvo įrašyti į diską, sąrašą"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
#, fuzzy
msgid "Configure Shares"
msgstr "_Configure Akcijos"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
#, fuzzy
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Įveskite vartotojo, kurio bendrinamus failus norite naršyti, vardą. Taip pat "
"galite įrašyti sąrašą į diską ir patikrinti jį vėliau."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
#, fuzzy
msgid "_Personal Profile"
msgstr "Mėgsta"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
#, fuzzy
msgid "Configure Account"
msgstr "Konfigūruoti registravimą"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
#, fuzzy
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Įveskite vartotojo vardą, kad peržiūrėtumėte jo vartotojo aprašą, "
"informaciją ir asmeninį paveikslėlį"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
#, fuzzy
msgid "Chat _History"
msgstr "Pokalbių istorija"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
#, fuzzy
msgid "Configure Chats"
msgstr "Konfigūruoti bendrai naudojamos akcijas"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
#, fuzzy
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr ""
"Įveskite vartotojo vardą, kad pradėtumėte tekstinį pokalbį su jais privačiai"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
#, fuzzy
msgid "_Message All"
msgstr "Žinutės"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
#, fuzzy
msgid "Configure Ignored Users"
msgstr "Nepaisomi naudotojai:"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
#, fuzzy
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Įtraukite vartotojus į savo bičiulių sąrašą, kad galėtumėte su jais "
"bendrinti konkrečius aplankus ir gauti pranešimus, kai jie yra prisijungę"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
#, fuzzy
msgid "Join or create room…"
msgstr "Prisijunkite arba sukurkite kambarį…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
#, fuzzy
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Prisijunkite prie esamo pokalbių kambario arba sukurkite naują kambarį "
"pokalbiams su kitais \"Soulseek\" tinklo vartotojais"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
#, fuzzy
msgid "Configure User Profile"
msgstr "Peržiūrėti vartotojo profilį"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
msgid "Connections"
msgstr "Prisijungimai"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
#, fuzzy
msgid "Downloading (Speed / Active Users)"
msgstr "Atsisiuntimas (greitis / aktyvūs vartotojai)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
#, fuzzy
msgid "Uploading (Speed / Active Users)"
msgstr "Įkėlimas (greitis / aktyvūs vartotojai)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
#, fuzzy
msgid "Search chat history…"
msgstr "Paieškos"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
#, fuzzy
msgid "Download Speed Limits"
msgstr "Atsiuntimai"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
#, fuzzy
msgid "Unlimited download speed"
msgstr "Apriboti įkėlimo greitį:"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
#, fuzzy
msgid "Use download speed limit (KiB/s):"
msgstr "Alternatyvus atsisiuntimo greičio apribojimas (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
#, fuzzy
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Alternatyvus atsisiuntimo greičio apribojimas (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
#, fuzzy
msgid "Search rooms…"
msgstr "Paieškos"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
#, fuzzy
msgid "Refresh Rooms"
msgstr "Atnaujinti kambarių sąrašą"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
#, fuzzy
msgid "_Show feed of public chat room messages"
msgstr "_Show viešų pokalbių kambario pranešimų sklaidos kanalas"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
#, fuzzy
msgid "_Accept private room invitations"
msgstr "Įtraukti į asmeninį kambarį %s"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
#, fuzzy
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Kambario sienos funkcija leidžia kambario vartotojams nurodyti unikalų "
"pranešimą, kuris bus rodomas kitiems. Naujausi pranešimai rodomi viršuje."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
#, fuzzy
msgid "Set wall message…"
msgstr "Nustatyti sienos pranešimą…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
#, fuzzy
msgid "Search Result Filters"
msgstr "Ieškos rezultatų filtrai"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
#, fuzzy
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Ieškos rezultatų filtrai naudojami ieškos rezultatams rodyti patikslinti."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
#, fuzzy
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Kiekvienas ieškos rezultatų sąrašas turi savo filtrą, kurį galima atskleisti "
"perjungus mygtuką Rezultatų filtrai. Filtrą sudaro keli laukai, kurie visi "
"taikomi paspaudus Enter bet kuriame iš jo laukų. Filtravimas nedelsiant "
"taikomas jau gautiems rezultatams, taip pat tiems, kurie dar neatvyks. "
"Norėdami dar kartą peržiūrėti visus rezultatus, tiesiog išvalykite visų "
"terminų filtrą ir taikykite jį iš naujo. Kaip rodo pavadinimas, ieškos "
"rezultatų filtras negali išplėsti pradinės ieškos, jis gali tik susiaurinti. "
"Norėdami išplėsti arba pakeisti paieškos terminus, atlikite naują iešką."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
#, fuzzy
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Kaip rodo pavadinimas, paieškos rezultatų filtras negali išplėsti pradinės "
"paieškos, jis gali ją tik susiaurinti. Norėdami išplėsti arba pakeisti "
"paieškos terminus, atlikite naują paiešką."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
#, fuzzy
msgid "Result Filter Usage"
msgstr "Patikrinti filtrus"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
#, fuzzy
msgid "Include Text"
msgstr "Įtraukti tekstą"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
#, fuzzy
msgid "Files, folders and usernames containing this text will be shown."
msgstr "Bus rodomi failai ir aplankai, kuriuose yra šis tekstas."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
#, fuzzy
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Byla yra nejautri, tačiau žodžio tvarka yra svarbi: 'Instrumental Remix' "
"nerodys jokių 'Remix Instrumental'"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
#, fuzzy
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Naudokite | (arba vamzdžiai), kad atskirtumėte kelias tikslias frazes. "
"Pavyzdys:\n"
"    Remix|Dub Mix|Instrumental"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
#, fuzzy
msgid "Exclude Text"
msgstr "Neįtraukti teksto"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
#, fuzzy
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"Kaip nurodyta pirmiau, bet failai ir aplankai filtruojami, jei tekstas "
"sutampa."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
#, fuzzy
msgid "Filters files based upon their file extension."
msgstr "Filtruoja failus pagal jų failo plėtinį."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
#, fuzzy
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Galima nurodyti kelis failų plėtinius, kurie savo ruožtu išplės rezultatų "
"sąrašą.\n"
"    Pavyzdys: flac|wav|ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
#, fuzzy
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"Taip pat galima apversti filtrą, nurodant failų plėtinius, kurių nenorite "
"savo rezultatuose.\n"
"    Pavyzdys: !mp3|! jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
#, fuzzy
msgid "File Size"
msgstr "Failo dydis"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
#, fuzzy
msgid "Filters files based upon their file size."
msgstr "Filtruoja failus pagal jų failo dydį."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
#, fuzzy
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"Pagal numatytuosius nustatymus naudojamas vienetas yra baitai, o failai, "
"didesni arba lygūs reikšmei, bus sugretinti."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
#, fuzzy
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Pridėti b, k, m arba g (arba kib, mib, arba gib) nurodyti baitų, kibibyte, "
"mebibyte, arba gibibyte vienetų:\n"
"    <1024k rasite failus 1024 kibibytes (ty 1 mebibyte) arba mažesnis."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
#, fuzzy
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Prepend = reikšmei, nurodančią tikslų atitikmenį:\n"
"    =1024 atitinka tik 1024 baitų dydžio failus (t. y. 1 kibibyte)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
#, fuzzy
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Prepend = reikšmei, nurodančią tikslų atitikmenį:\n"
"    =1024 atitinka tik 1024 baitų dydžio failus (t. y. 1 kibibyte)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
#, fuzzy
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Pridėkite < arba >, jei norite rasti failus, mažesnius / didesnius nei "
"nurodyta reikšmė:\n"
"    >10,5m|<1g, kad būtų rodomi didesni nei 10,5 MiB (mebibaitų) failai,\n"
"    bet mažesnis nei 1 GiB (gibibaitas), naudokite a | tarp sąlygų."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
#, fuzzy
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Kad būtų patogiau, taip pat galima naudoti geriau žinomų kilo-, mega-ir "
"gigabaitų vienetų kb, mb ir gb variantus."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
#, fuzzy
msgid "Filters files based upon their bitrate."
msgstr "Filtruoja failus pagal jų bitų dažnį."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
#, fuzzy
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Reikšmes reikia įvesti tik kaip skaitinius skaitmenis. Vienetas visada yra "
"Kb/s (kilobitai per sekundę)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
#, fuzzy
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Kaip ir Failo dydis (aukščiau), galima naudoti operatorius =, !, < ir >, o "
"kelias sąlygas galima nurodyti su | vamzdžiai:\n"
"    >256|<1411, kad būtų rodomi failai, kurių bitų sparta ne mažesnė kaip "
"256 Kb/s\n"
"    su maksimaliu 1411 Kb/s bitų sparta."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
#, fuzzy
msgid "Filters files based upon their duration."
msgstr "Filtruoja failus pagal jų bitų dažnį."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
#, fuzzy
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"Pagal numatytuosius nustatymus failai, ilgesni arba lygūs įvestai, bus "
"suderinti, nebent naudojamas operatorius (=, !, < arba >)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
#, fuzzy
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Įveskite neapdorotą reikšmę sekundėmis arba naudokite MM:SS ir HH:MM:SS "
"laiko formatus:\n"
"    >5:30, kad būtų rodomi bent 5 su puse minutės ilgio failai.\n"
"    <5:30:00 rodomi trumpesni nei 5 su puse valandos failai."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
#, fuzzy
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Su | galima nurodyti kelias sąlygas vamzdžių separatoriai:\n"
"    >6:00|<12:00, kad būtų rodomi 6–12 minučių ilgio failai.\n"
"    !9:54|!8:43|!7:32, kad paslėptumėte kai kuriuos konkrečius failus nuo "
"rezultatų.\n"
"    =5:34|=4:23|=3:05, kad įtrauktumėte konkrečios trukmės failus."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
#, fuzzy
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Naudoja ISO 3166-2 apibrėžtus šalių kodus (žr. Vikipediją):\n"
"    \"JAV\" grąžins failus tik iš naudotojų, prijungtų per Jungtines "
"Amerikos Valstijas. Panašiai \"GB\" grąžina failus iš naudotojų, turinčių IP "
"Jungtinėje Karalystėje."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
#, fuzzy
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Kableliais arba tarpais galima nurodyti kelias šalis."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
#, fuzzy
msgid "Free Slot"
msgstr "Nemokamas lizdas"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
#, fuzzy
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Rodyti tik tuos rezultatus iš vartotojų, kurie turi bent vieną įkėlimo lizdą "
"nemokamai. Šis filtras taikomas nedelsiant."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
#, fuzzy
msgid "Upload Speed Limits"
msgstr "Įkelti greičio apribojimus"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
#, fuzzy
msgid "Unlimited upload speed"
msgstr "Apriboti įkėlimo greitį:"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
#, fuzzy
msgid "Use upload speed limit (KiB/s):"
msgstr "Alternatyvus įkėlimo greičio apribojimas (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
#, fuzzy
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Alternatyvus įkėlimo greičio apribojimas (KiB/s):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
#, fuzzy
msgid "Private Chat Command Help"
msgstr "Privačių pokalbių komandų žinynas"

#: pynicotine/gtkgui/ui/search.ui:7
#, fuzzy
msgid "Include text…"
msgstr "Įtraukti tekstą…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
#, fuzzy
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtruoti rezultatuose, kurių failų maršrutuose yra nurodytas tekstas. "
"Galima nurodyti kelias frazes ir žodžius, pvz., tiksli frazė|music|term|"
"exact frazė du"

#: pynicotine/gtkgui/ui/search.ui:18
#, fuzzy
msgid "Exclude text…"
msgstr "Neįtraukti teksto…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
#, fuzzy
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtruoti rezultatus, kurių failų maršrutuose yra nurodytas tekstas. Galima "
"nurodyti kelias frazes ir žodžius, pvz., tiksli frazė|music|term|exact frazė "
"du"

#: pynicotine/gtkgui/ui/search.ui:29
#, fuzzy
msgid "File type…"
msgstr "Failo tipas…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
#, fuzzy
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Failo tipas, pvz., flac|wav|ape arba !mp3|! m4a"

#: pynicotine/gtkgui/ui/search.ui:40
#, fuzzy
msgid "File size…"
msgstr "Failo dydis…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
#, fuzzy
msgid "File size, e.g. >10.5m <1g"
msgstr "Failo dydis, pvz. >10,5m <1g"

#: pynicotine/gtkgui/ui/search.ui:51
#, fuzzy
msgid "Bitrate…"
msgstr "Bitų dažnis"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
#, fuzzy
msgid "Bitrate, e.g. 256 <1412"
msgstr "Bitrate, pvz. 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
#, fuzzy
msgid "Duration…"
msgstr "Trukmė…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
#, fuzzy
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Trukmė, pvz. 2:20|!3:30|=4:40"

#: pynicotine/gtkgui/ui/search.ui:73
#, fuzzy
msgid "Country code…"
msgstr "Valstybė"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
#, fuzzy
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Šalies kodas, pvz., JAV| GB|ES arba !DE|! GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
#, fuzzy
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Uždrausti vartotojams pasiekti jūsų bendrinamus failus pagal naudotojo "
"vardą, IP adresą ar šalį."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Blokuotinų valstybių kodai (atskirti kableliu):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
#, fuzzy
msgid "Codes must be in ISO 3166-2 format."
msgstr "Kodai turi būti ISO 3166-2 formato."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
#, fuzzy
msgid "Use custom geo block message:"
msgstr "Naudoti pasirinktinę blokavimo žinutę:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Naudoti pasirinktinę blokavimo žinutę:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
#, fuzzy
msgid "IP Addresses"
msgstr "Adresai"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
#, fuzzy
msgid "Restore previously open private chats on startup"
msgstr "Atkurti anksčiau atidarytus privačius pokalbius paleisties metu"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
#, fuzzy
msgid "Enable spell checker"
msgstr "Įjungti rašybos tikrinimą (reikia paleisti programą iš naujo)"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
#, fuzzy
msgid "Enable CTCP-like private message responses (client version)"
msgstr ""
"Įgalinti Į CTCP panašius asmeninius pranešimų atsakymus (kliento versija)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
#, fuzzy
msgid "Number of recent private chat messages to show:"
msgstr "Rodomų naujausių pokalbių eilučių skaičius:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
#, fuzzy
msgid "Number of recent chat room messages to show:"
msgstr "Rodomų naujausių pokalbių eilučių skaičius:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
#, fuzzy
msgid "Chat Completion"
msgstr "Užbaigimas"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
msgid "Enable tab-key completion"
msgstr "Įjungti užbaigimą Tab klavišu"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
msgid "Enable completion drop-down list"
msgstr "Įjungti užbaigimo išskleidžiamą sąrašą"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
msgid "Minimum characters required to display drop-down:"
msgstr "Minimalus simbolių skaičius, kad būtų rodomas išskleidžiamas sąrašas:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
#, fuzzy
msgid "Allowed chat completions:"
msgstr "Leidžiami užbaigimai"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
#, fuzzy
msgid "Buddy names"
msgstr "Bičiulių sąrašas"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
#, fuzzy
msgid "Chat room usernames"
msgstr "Pokalbių kambario pranešimas:"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
#, fuzzy
msgid "Room names"
msgstr "Kambariuose"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
#, fuzzy
msgid "Commands"
msgstr "Komandas"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
#, fuzzy
msgid "Timestamps"
msgstr "Laiko žymos"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
#, fuzzy
msgid "Private chat format:"
msgstr "Privataus kambario formatas:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "Numatytoji"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
msgid "Chat room format:"
msgstr "Pokalbių kambarių formatas:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
#, fuzzy
msgid "Text-to-Speech"
msgstr "Tekstas į kalbą"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
#, fuzzy
msgid "Enable Text-to-Speech"
msgstr "Įgalinti tekstą į kalbą"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
#, fuzzy
msgid "Text-to-Speech command:"
msgstr "Komanda Tekstas į kalbą:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
msgid "Private chat message:"
msgstr "Asmeninio pokalbio pranešimas:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
msgid "Chat room message:"
msgstr "Pokalbių kambario pranešimas:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
#, fuzzy
msgid "Censor"
msgstr "Cenzūros sąrašas"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
#, fuzzy
msgid "Enable censoring of text patterns"
msgstr "Įgalinti teksto šablonų cenzūrą"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
#, fuzzy
msgid "Auto-Replace"
msgstr "Automatinio pakeitimo sąrašas"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
#, fuzzy
msgid "Enable automatic replacement of words"
msgstr "Įgalinti automatinį žodžių keitimą"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
#, fuzzy
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr "Autoclear baigti / filtruoti atsisiuntimai iš perkėlimo sąrašo"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
#, fuzzy
msgid "Store completed downloads in username subfolders"
msgstr "Atsisiuntimų saugojimas vartotojo vardo poaplankiuose"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
#, fuzzy
msgid "Double-click action for downloads:"
msgstr "Laukiama siuntimo"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
#, fuzzy
msgid "Allow users to send you any files:"
msgstr "Leisti šiems naudotojams atsiųsti jums failus:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
#, fuzzy
msgid "Folders"
msgstr "Aplankus"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
#, fuzzy
msgid "Finished downloads:"
msgstr "Atsisiųstas failas"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
#, fuzzy
msgid "Incomplete downloads:"
msgstr "Baigti atsisiuntimai"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
#, fuzzy
msgid "Received files:"
msgstr "Užklausiamas failas"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "Įvykiai"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
#, fuzzy
msgid "Run command after file download finishes ($ for file path):"
msgstr "Vykdyti komandą pasibaigus failo atsisiuntimui ($ už failo kelią):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
#, fuzzy
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"Vykdyti komandą pasibaigus aplanko atsisiuntimui ($ aplanko maršrutui):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
#, fuzzy
msgid "Download Filters"
msgstr "Atsisiųsti filtrus"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
#, fuzzy
msgid "Enable download filters"
msgstr "Įveskite naują atsiuntimo filtrą:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Pridėti"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Įkelti numatytuosius"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
msgid "Verify Filters"
msgstr "Patikrinti filtrus"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
#, fuzzy
msgid "Unverified"
msgstr "Nepatikrintas"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
#, fuzzy
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Nepaisykite vartotojų pokalbių pranešimų ir paieškos rezultatų pagal "
"naudotojo vardą arba IP adresą."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Vesti pokalbių kambarių žurnalus"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Vesti asmeninių pokalbių žurnalus"

#: pynicotine/gtkgui/ui/settings/log.ui:142
#, fuzzy
msgid "Log transfers to file"
msgstr "Registruoti perkėlimus į failą"

#: pynicotine/gtkgui/ui/settings/log.ui:166
#, fuzzy
msgid "Log debug messages to file"
msgstr "Registruoti derinimo pranešimus į failą"

#: pynicotine/gtkgui/ui/settings/log.ui:190
#, fuzzy
msgid "Log timestamp format:"
msgstr "Žurnalų failų formatas:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
#, fuzzy
msgid "Folder Locations"
msgstr "Aplankų vietos"

#: pynicotine/gtkgui/ui/settings/log.ui:245
#, fuzzy
msgid "Chatroom logs folder:"
msgstr "Pokalbių kambario žurnalų aplankas:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
#, fuzzy
msgid "Private chat logs folder:"
msgstr "Privačių pokalbių žurnalų aplankas:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
#, fuzzy
msgid "Transfer logs folder:"
msgstr "Perkėlimo žurnalų aplankas:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
#, fuzzy
msgid "Debug logs folder:"
msgstr "Derinimo žurnalų aplankas:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
#, fuzzy
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Prisijunkite prie esamos Soulseek paskyros arba sukurkite naują. Naudotojų "
"varduose taikomos didžiąsias ir mažąsias raides bei jos yra unikalios."

#: pynicotine/gtkgui/ui/settings/network.ui:118
#, fuzzy
msgid "Public IP address:"
msgstr "Blokuoti IP adresą..."

#: pynicotine/gtkgui/ui/settings/network.ui:159
#, fuzzy
msgid "Listening port:"
msgstr "Klausomasi prievado %i"

#: pynicotine/gtkgui/ui/settings/network.ui:185
#, fuzzy
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Automatiškai persiųsti klausymosi prievadą (UPnP / NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
#, fuzzy
msgid "Away Status"
msgstr "Būsena"

#: pynicotine/gtkgui/ui/settings/network.ui:228
#, fuzzy
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Neaktyvumo minutės prieš išvykstant (0, kad išjungtumėte):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
#, fuzzy
msgid "Auto-reply message when away:"
msgstr "Automatinio atsakymo pranešimas išvykęs:"

#: pynicotine/gtkgui/ui/settings/network.ui:299
#, fuzzy
msgid "Auto-connect to server on startup"
msgstr "Automatinis prisijungimas prie serverio paleidžiant"

#: pynicotine/gtkgui/ui/settings/network.ui:323
#, fuzzy
msgid "Soulseek server:"
msgstr "Soulseek serveris:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
#, fuzzy
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Susieja ryšius su konkrečia tinklo sąsaja, naudinga, pvz., užtikrinant, kad "
"VPN visada būtų naudojamas. Palikite tuščią, jei norite naudoti bet kurią "
"galimą sąsają. Pakeiskite šią reikšmę tik tuo atveju, jei žinote, ką darote."

#: pynicotine/gtkgui/ui/settings/network.ui:351
#, fuzzy
msgid "Network interface:"
msgstr "Tinklo ieškos"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
#, fuzzy
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"Dabar leidžiama leidžia jums parodyti, ką leidžia medijos leistuvas, "
"naudojant komandą /now pokalbyje."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
msgid "Other"
msgstr "Kitas"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
#, fuzzy
msgid "Now Playing Format"
msgstr "Grojamos dainos rodymo formatas:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
#, fuzzy
msgid "Now Playing message format:"
msgstr "Grojamos dainos rodymo formatas:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
#, fuzzy
msgid "Test Configuration"
msgstr "Tikrinti konfigūraciją"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
#, fuzzy
msgid "Enable plugins"
msgstr "Įgalinti papildinius"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
#, fuzzy
msgid "Add Plugins"
msgstr "Įskiepiai"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
#, fuzzy
msgid "_Add Plugins"
msgstr "Įskiepiai"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
#, fuzzy
msgid "Settings"
msgstr "Gaunama būsena"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
#, fuzzy
msgid "_Settings"
msgstr "Gaunama būsena"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
#, fuzzy
msgid "Version:"
msgstr "Versija: "

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
#, fuzzy
msgid "Created by:"
msgstr "Sukurta"

#: pynicotine/gtkgui/ui/settings/search.ui:51
#, fuzzy
msgid "Enable search history"
msgstr "Įgalinti ieškos retrospektyvą"

#: pynicotine/gtkgui/ui/settings/search.ui:70
#, fuzzy
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Kiti klientai gali pasiūlyti galimybę siųsti privačiai bendrinamus failus "
"atsakydami į ieškos užklausas. Tokie failai yra prefiksuoti \"[PRIVATE "
"FILE]\" ir jų negalima atsisiųsti, nebent įkėlėjams būtų duotas aiškus "
"leidimas."

#: pynicotine/gtkgui/ui/settings/search.ui:76
#, fuzzy
msgid "Show privately shared files in search results"
msgstr "Rodyti privačiai bendrinamus failus ieškos rezultatuose"

#: pynicotine/gtkgui/ui/settings/search.ui:106
#, fuzzy
msgid "Limit number of results per search:"
msgstr "Riboti rezultatų skaičių paieškoje:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
#, fuzzy
msgid "Result Filter Help"
msgstr "Rezultatų filtro žinynas"

#: pynicotine/gtkgui/ui/settings/search.ui:177
#, fuzzy
msgid "Enable search result filters by default"
msgstr "Įjungti filtrus iš karto"

#: pynicotine/gtkgui/ui/settings/search.ui:211
#, fuzzy
msgid "Include:"
msgstr "Įtraukti:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
#, fuzzy
msgid "Exclude:"
msgstr "Išskirti:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
#, fuzzy
msgid "File Type:"
msgstr "Failo tipas:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "Dydis:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Bitų dažnis:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
#, fuzzy
msgid "Duration:"
msgstr "Trukmė:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
#, fuzzy
msgid "Country Code:"
msgstr "Šalies kodas:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
#, fuzzy
msgid "Only show results from users with an available upload slot."
msgstr "Rodyti tik naudotojų, turinčių galimą įkėlimo lizdą, rezultatus."

#: pynicotine/gtkgui/ui/settings/search.ui:430
#, fuzzy
msgid "Network Searches"
msgstr "Tinklo ieškos"

#: pynicotine/gtkgui/ui/settings/search.ui:452
#, fuzzy
msgid "Respond to search requests from other users"
msgstr "Atsakyti į kitų vartotojų ieškos užklausas"

#: pynicotine/gtkgui/ui/settings/search.ui:486
#, fuzzy
msgid "Searches shorter than this number of characters will be ignored:"
msgstr "Ieškos, trumpesnės už šį simbolių skaičių, bus ignoruojamos:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
#, fuzzy
msgid "Maximum search results to send per search request:"
msgstr "rezultatų paieškos užklausoje"

#: pynicotine/gtkgui/ui/settings/search.ui:578
#, fuzzy
msgid "Clear Search History"
msgstr "Valyti ieškos retrospektyvą"

#: pynicotine/gtkgui/ui/settings/search.ui:626
#, fuzzy
msgid "Clear Filter History"
msgstr "Valyti filtro retrospektyvą"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
#, fuzzy
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Paleisties metu automatiškai iš naujo užsako bendrinamų aplankų turinį. Jei "
"išjungta, jūsų akcijos atnaujinamos tik tada, kai rankiniu būdu inicijuojate "
"pakartotinį informacijos skadrą."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Paleidus perskanuoti viešinius"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/shares.ui:82
#, fuzzy
msgid "Buddy shares"
msgstr "Bičiulių sąrašas"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
#, fuzzy
msgid "Trusted shares"
msgstr "Perskanuoti viešinius"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
#, fuzzy
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr "Autoclear baigti / atšaukti nusiuntimai iš perkėlimo sąrašo"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
#, fuzzy
msgid "Double-click action for uploads:"
msgstr "Nusiuntimo dukart spustelėkite veiksmą:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
#, fuzzy
msgid "Limit upload speed:"
msgstr "Apriboti įkėlimo greitį:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
#, fuzzy
msgid "Per transfer"
msgstr "Už perkėlimą"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
#, fuzzy
msgid "Total transfers"
msgstr "Iš viso pervedimų"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
#, fuzzy
msgid "Upload Slots"
msgstr "Išsiuntimai"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
#, fuzzy
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr "Failai bus įkeliami cikliškai eilėje laukiantiems vartotojams."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
#, fuzzy
msgid "Upload queue type:"
msgstr "Nusiuntimo eilės tipas:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
#, fuzzy
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr "Eilė įkeliama, jei pasiekiamas bendras perkėlimo greitis (KiB/s):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
#, fuzzy
msgid "Fixed number of upload slots:"
msgstr "Apriboti įkėlimo laiko tarpsnių skaičių iki:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
#, fuzzy
msgid "Prioritize all buddies"
msgstr "Privilegijuoti visus mano draugus"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
#, fuzzy
msgid "Queue Limits"
msgstr "Eilės padėtis"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
#, fuzzy
msgid "Maximum number of queued files per user:"
msgstr "Riboti rezultatų skaičių paieškoje:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
#, fuzzy
msgid "Limits do not apply to buddies"
msgstr "Eilių dydžio limitas netaikomas draugams"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
#, fuzzy
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"$ egzemplioriai bus pakeisti nuoroda. Pridėkite tuščias dorokles, jei norite "
"naudoti numatytąją sistemos žiniatinklio naršyklę.\n"
"Protokolų doroklės: "

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
#, fuzzy
msgid "File manager command:"
msgstr "Failų tvarkytuvo komanda ($ failo maršrutui):"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
#, fuzzy
msgid "Reset Picture"
msgstr "Iš naujo nustatyti paveikslėlį"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
#, fuzzy
msgid "Self Description"
msgstr "Savęs aprašymas"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
#, fuzzy
msgid "Picture:"
msgstr "Nuotraukų:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
#, fuzzy
msgid "Prefer dark mode"
msgstr "Teikti pirmenybę tamsiuoju režimu"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
#, fuzzy
msgid "Use header bar"
msgstr "Naudoti _Header juostą"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
#, fuzzy
msgid "Display tray icon"
msgstr "Ekrano dėklo piktograma"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
#, fuzzy
msgid "Minimize to tray on startup"
msgstr "Minimizuoti iki dėklo paleisties metu"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
#, fuzzy
msgid "Language (requires a restart):"
msgstr "Įjungti rašybos tikrinimą (reikia paleisti programą iš naujo)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
#, fuzzy
msgid "When closing window:"
msgstr "Uždarant Nicotine+:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
#, fuzzy
msgid "Notifications"
msgstr "Pranešimus"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
#, fuzzy
msgid "Enable sound for notifications"
msgstr "Pranešimų iššokančių langų garso įgalinimas"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
#, fuzzy
msgid "Show notification for private chats and mentions in the window title"
msgstr ""
"Rodyti pranešimą apie privačius pokalbius ir paminėjimus lango pavadinime"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
#, fuzzy
msgid "Show notifications for:"
msgstr "Pranešimų piktogramų rodymas skirtukuose"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
#, fuzzy
msgid "Finished file downloads"
msgstr "Atsisiųstas failas"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
#, fuzzy
msgid "Finished folder downloads"
msgstr "Aplankas atsisiųstas"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
#, fuzzy
msgid "Private messages"
msgstr "Privatus pranešimas iš %s"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
#, fuzzy
msgid "Chat room messages"
msgstr "Pokalbių kambario pranešimas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
#, fuzzy
msgid "Chat room mentions"
msgstr "Užbaigimas"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
#, fuzzy
msgid "Wishlist results found"
msgstr "Rasti pageidavimų sąrašo rezultatai"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
#, fuzzy
msgid "Restore the previously active main tab at startup"
msgstr "Atkurti anksčiau atidarytus privačius pokalbius paleisties metu"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
#, fuzzy
msgid "Close-buttons on secondary tabs"
msgstr "Užvėrimo mygtukai kortelėse"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
#, fuzzy
msgid "Regular tab label color:"
msgstr "Įprasta skirtuko etiketės spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
#, fuzzy
msgid "Changed tab label color:"
msgstr "Pakeista skirtuko etiketės spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
#, fuzzy
msgid "Highlighted tab label color:"
msgstr "Paryškinta skirtuko etiketės spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
#, fuzzy
msgid "Visible main tabs:"
msgstr "Matomi pirminiai skirtukai:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
#, fuzzy
msgid "Tab bar positions:"
msgstr "Skirtukų juostos padėtis:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
#, fuzzy
msgid "Main tabs"
msgstr "Pagrindiniai skirtukai"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
#, fuzzy
msgid "Show reverse file paths (requires a restart)"
msgstr ""
"Rodyti atvirkštinius failų kelius ieškos ir perkėlimo rodiniuose (reikia "
"paleisti iš naujo)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
#, fuzzy
msgid "Show exact file sizes (requires a restart)"
msgstr "Įjungti rašybos tikrinimą (reikia paleisti programą iš naujo)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
#, fuzzy
msgid "List text color:"
msgstr "Sąrašo teksto spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
#, fuzzy
msgid "Enable colored usernames"
msgstr "Pokalbių kambario pranešimas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
#, fuzzy
msgid "Chat username appearance:"
msgstr "Pokalbio naudotojo vardo išvaizda:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
#, fuzzy
msgid "Remote text color:"
msgstr "Nuotolinio teksto spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
#, fuzzy
msgid "Local text color:"
msgstr "Vietinio failo klaida"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
#, fuzzy
msgid "Command output text color:"
msgstr "Nuotolinio teksto spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
#, fuzzy
msgid "/me action text color:"
msgstr "/me veiksmo teksto spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
#, fuzzy
msgid "Highlighted text color:"
msgstr "Paryškinta teksto spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
#, fuzzy
msgid "URL link text color:"
msgstr "URL saito teksto spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
#, fuzzy
msgid "User Statuses"
msgstr "Jungtinės Amerikos Valstijos"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
#, fuzzy
msgid "Online color:"
msgstr "Internetinė teksto spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
#, fuzzy
msgid "Away color:"
msgstr "Teksto spalva išvykęs:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
#, fuzzy
msgid "Offline color:"
msgstr "Autonominio teksto spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
#, fuzzy
msgid "Text Entries"
msgstr "Teksto įrašai"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
#, fuzzy
msgid "Text entry background color:"
msgstr "Teksto įrašo fono spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
#, fuzzy
msgid "Text entry text color:"
msgstr "Teksto įrašo teksto spalva:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
#, fuzzy
msgid "Fonts"
msgstr "Šriftai"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
#, fuzzy
msgid "Global font:"
msgstr "Visuotinis šriftas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
#, fuzzy
msgid "List font:"
msgstr "Sąrašo šriftas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
#, fuzzy
msgid "Text view font:"
msgstr "Teksto peržiūros šriftas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
#, fuzzy
msgid "Chat font:"
msgstr "Pokalbio šriftas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
#, fuzzy
msgid "Transfers font:"
msgstr "Perkelia šriftą:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
#, fuzzy
msgid "Search font:"
msgstr "Paieškos šriftas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
#, fuzzy
msgid "Browse font:"
msgstr "Naršyti šriftą:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
msgid "Icons"
msgstr "Piktogramos"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
#, fuzzy
msgid "Icon theme folder:"
msgstr "Nebaigtas failų aplankas:"

#: pynicotine/gtkgui/ui/uploads.ui:86
#, fuzzy
msgid "Abort User(s)"
msgstr "Naudotojas (-ai)"

#: pynicotine/gtkgui/ui/uploads.ui:116
msgid "Ban User(s)"
msgstr "Užblokuoti naudotoją(us)"

#: pynicotine/gtkgui/ui/uploads.ui:138
#, fuzzy
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Autoclear baigti / atšaukti nusiuntimai iš perkėlimo sąrašo"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
#, fuzzy
msgid "Message All"
msgstr "Žinutės"

#: pynicotine/gtkgui/ui/uploads.ui:199
#, fuzzy
msgid "Clear Specific Uploads"
msgstr "Valyti visus įkėlimus"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
#, fuzzy
msgid "Save Shares List to Disk"
msgstr "_Save akcijų sąrašą į diską"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
#, fuzzy
msgid "Refresh Files"
msgstr "Atnaujinti failus"

#: pynicotine/gtkgui/ui/userinfo.ui:101
#, fuzzy
msgid "Edit Profile"
msgstr "V_iešiniai"

#: pynicotine/gtkgui/ui/userinfo.ui:149
#, fuzzy
msgid "Shared Files"
msgstr "_Ieškoti failų"

#: pynicotine/gtkgui/ui/userinfo.ui:203
#, fuzzy
msgid "Upload Speed"
msgstr "Nusiųsti"

#: pynicotine/gtkgui/ui/userinfo.ui:230
#, fuzzy
msgid "Free Upload Slots"
msgstr "Nemokami įkėlimo laiko tarpsniai"

#: pynicotine/gtkgui/ui/userinfo.ui:284
#, fuzzy
msgid "Queued Uploads"
msgstr "Išsiuntimai"

#: pynicotine/gtkgui/ui/userinfo.ui:354
#, fuzzy
msgid "Edit Interests"
msgstr "Pomėgiai"

#: pynicotine/gtkgui/ui/userinfo.ui:624
#, fuzzy
msgid "_Gift Privileges…"
msgstr "Suteikti privilegijas"

#: pynicotine/gtkgui/ui/userinfo.ui:663
#, fuzzy
msgid "_Refresh Profile"
msgstr "Atnaujinti failus"

#: pynicotine/plugins/core_commands/PLUGININFO:3
#, fuzzy
msgid "Nicotine+ Commands"
msgstr "Nicotine+ komanda"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Nicotine+ komanda"

#, fuzzy
#~ msgid "Listening port (requires a restart):"
#~ msgstr "Įjungti rašybos tikrinimą (reikia paleisti programą iš naujo)"

#, fuzzy
#~ msgid "Network interface (requires a restart):"
#~ msgstr "Įjungti rašybos tikrinimą (reikia paleisti programą iš naujo)"

#, fuzzy
#~ msgid "Invalid Password"
#~ msgstr "Slaptažodis:"

#, fuzzy
#~ msgid "Change _Login Details"
#~ msgstr "Keisti prisijungimo duomenis"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i privilegijuoti naudotojai"

#, fuzzy
#~ msgid "_Set Up…"
#~ msgstr "_Set aukštyn…"

#, fuzzy
#~ msgid "Queued search result text color:"
#~ msgstr "Eilėje esančių ieškos rezultatų teksto spalva:"

#~ msgid "_Clear"
#~ msgstr "Iš_valyti"

#~ msgid "_Remove"
#~ msgstr "_Pašalinti"

#~ msgid "Send Message"
#~ msgstr "Siųsti žinutę"

#, python-format
#~ msgid "Trouble executing '%s'"
#~ msgstr "Nesėkmė vykdant „%s“"

#, python-format
#~ msgid "Trouble executing on folder: %s"
#~ msgstr "Klaida vykdant aplanke: %s"

#~ msgid "Replace censored letters with:"
#~ msgstr "Pakeisti cenzūruotas raides:"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr "Arba serveris neveikia, arba neatsako, kitas bandymas po %i sek"

#~ msgid ""
#~ "Nicotine+ uses peer-to-peer networking to connect to other users. In "
#~ "order to allow users to connect to you without trouble, an open listening "
#~ "port is crucial."
#~ msgstr ""
#~ "Nicotine+ naudoja lygiaverčius tinklus, kad prisijungtų prie kitų "
#~ "vartotojų. Kad vartotojai galėtų prisijungti prie jūsų be problemų, labai "
#~ "svarbus atviras klausymosi prievadas."

#~ msgid "--- disconnected ---"
#~ msgstr "--- atsijungta ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- prisijungta iš naujo ---"

#~ msgid "Joined Rooms "
#~ msgstr "Kambariai, kuriuose esate "

#~ msgid "Escaped"
#~ msgstr "Kaitos simbolis"

#~ msgid "to"
#~ msgstr "iki"

#~ msgid "Zoom 1:1"
#~ msgstr "Mastelis 1:1"

#~ msgid "Zoom In"
#~ msgstr "Pritraukti"

#~ msgid "Zoom Out"
#~ msgstr "Atitraukti"

#~ msgid "Join room 'room'"
#~ msgstr "Įeiti į kambarį „kambarys“"

#~ msgid "Display the Now Playing script's output"
#~ msgstr "Rodyti scenarijaus Dabar grojama išvestį"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "Įtraukti naudotoją „naudotojas“ į užblokuotų sąrašą"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "Pašalinti naudotoją „naudotojas“ iš užblokuotų sąrašo"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "Įtraukti naudotoją „naudotojas“ į nepaisomųjų sąrašą"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "Pašalinti naudotoją „naudotojas“ iš nepaisomųjų sąrašo"

#~ msgid "Show IP for user 'user'"
#~ msgstr "Parodyti naudotojo „naudotojas“ IP"

#~ msgid "Start a new search for 'query'"
#~ msgstr "Pradėti naują „užklausa“ paiešką"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "Ieškoti „užklausa“ kambariuose, kuriuose esate"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "Ieškoti „užklausa“ pas bičiulius"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "Siųsti žinutę „žinutė“ naudotojui „naudotojas“"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr "Atverti asmeninio pokalbio su naudotoju „naudotojas“ langą"

#~ msgid "Hide drop-down when only one matches"
#~ msgstr "Paslėpti išskleidžiamą sąrašą, kai yra tik vienas atitikmuo"

#~ msgid "Download folders in reverse alphanumerical order"
#~ msgstr "Atsiųsti aplankus atvirkštine tekstine tvarka"

#~ msgid "User(s)"
#~ msgstr "Naudotojas (-ai)"

#~ msgid "Last played"
#~ msgstr "Vėliausiai grota"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "Nėra tokio alternatyvaus vardo (%s)"

#~ msgid "Aliases:"
#~ msgstr "Alternatyvūs vardai:"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "Pašalintas alternatyvus vardas %(alias)s: %(action)s\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "Nėra tokio alternatyvaus vardo (%(alias)s)\n"

#~ msgid "Aliases"
#~ msgstr "Alternatyvūs vardai"

#~ msgid "Add a new alias"
#~ msgstr "Pridėti naują alternatyvų vardą"

#~ msgid "Remove an alias"
#~ msgstr "Pašalinti alternatyvų vardą"

#~ msgid "Notification changes the tab's text color"
#~ msgstr "Pranešimas keičia kortelės teksto spalvą"

#~ msgid "Cancel"
#~ msgstr "Atšaukti"

#~ msgid "OK"
#~ msgstr "Gerai"

#~ msgid "Aborted"
#~ msgstr "Nutraukta"

#, python-format
#~ msgid "You've been mentioned in the %(room)s room"
#~ msgstr "Jus paminėjo kambaryje %(room)s"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "Komanda %s neatpažinta"

#, python-format
#~ msgid "(Warning: %(realuser)s is attempting to spoof %(fakeuser)s) "
#~ msgstr "(Dėmesio: %(realuser)s bando apsimesti naudotoju %(fakeuser)s) "

#, python-format
#~ msgid ""
#~ "The network interface you specified, '%s', does not exist. Change or "
#~ "remove the specified network interface and restart Nicotine+."
#~ msgstr ""
#~ "Nurodyta tinklo sąsaja '%s' neegzistuoja. Pakeiskite arba pašalinkite "
#~ "nurodytą tinklo sąsają ir iš naujo paleiskite Nicotine+."

#~ msgid ""
#~ "Note that part of your range lies below 1024, this is usually not allowed "
#~ "on most operating systems with the exception of Windows."
#~ msgstr ""
#~ "Pastaba: dalis nurodytojo rėžio yra žemiau 1024. Paprastai tai daugumoje "
#~ "operacinių sistemų, išskyrus Windows, neleidžiama."

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "OS klaida: %s"

#~ msgid "Expand / Collapse all"
#~ msgstr "Išplėsti / sutraukti visus"

#~ msgid ""
#~ "File index of shared files could not be accessed. This could occur due to "
#~ "several instances of Nicotine+ being active simultaneously, file "
#~ "permission issues, or another issue in Nicotine+."
#~ msgstr ""
#~ "Bendrai naudojamų failų rodyklės pasiekti nepavyko. Taip gali atsitikti "
#~ "dėl kelių atvejų, kai Nicotine+ yra aktyvus vienu metu, failų teisių "
#~ "problemos arba kita Nicotine+ problema."

#~ msgid "Nicotine+ is a Soulseek client"
#~ msgstr "Nicotine+ yra Soulseek klientas"

#~ msgid "unknown"
#~ msgstr "nežinoma"

#~ msgid "Length"
#~ msgstr "Ilgis"

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "I/O klaida: %s"

#~ msgid "Protocol:"
#~ msgstr "Protokolas:"

#~ msgid "Establishing connection"
#~ msgstr "Įtvirtinamas ryšys"

#~ msgid "Clear Groups"
#~ msgstr "Išvalymo grupės"

#~ msgid "Your config file is corrupt"
#~ msgstr "Konfigūracijos failas sugadintas"

#~ msgid "User:"
#~ msgstr "Naudotojas:"

#, python-format
#~ msgid "All %(ext)s"
#~ msgstr "Visi %(ext)s"

#, python-format
#~ msgid "%(number)2s files "
#~ msgstr "%(number)2s failų "

#~ msgid "Addresses"
#~ msgstr "Adresai"

#~ msgid "Handler"
#~ msgstr "Valdiklis"

#~ msgid "Could not enable plugin."
#~ msgstr "Nepavyko įjungti įskiepio."

#~ msgid "Could not disable plugin."
#~ msgstr "Nepavyko išjungti įskiepio."

#~ msgid "Transfers"
#~ msgstr "Siuntimai"

#~ msgid "Ban List"
#~ msgstr "Blokavimo sąrašas"

#~ msgid "Ignore List"
#~ msgstr "Nepaisymo sąrašas"

#~ msgid "Completion"
#~ msgstr "Užbaigimas"

#~ msgid "Categories"
#~ msgstr "Kategorijos"

#~ msgid "Complete usernames in chat rooms"
#~ msgstr "Užbaigti naudotojų vardus pokalbių kambariuose"

#~ msgid "Display timestamps"
#~ msgstr "Rodyti laiko žymas"

#~ msgid "Download"
#~ msgstr "Atsiųsti"

#~ msgid "Upload"
#~ msgstr "Nusiųsti"

#, python-format
#~ msgid "Your buddy, %s, is attempting to upload file(s) to you."
#~ msgstr "Jūsų bičiulis, %s, bando atsiųsti jums failą(us)."

#, python-format
#~ msgid ""
#~ "%s is not allowed to send you file(s), but is attempting to, anyway. "
#~ "Warning Sent."
#~ msgstr ""
#~ "%s neleidžiama atsiųsti Jums failų, tačiau jis vistiek bando. Išsiųstas "
#~ "įspėjimas."

#~ msgid "Client Version"
#~ msgstr "Kliento versija"

#~ msgid "Privileged"
#~ msgstr "Privilegijuotas"

#~ msgid "_Privileged"
#~ msgstr "_Privilegijuotas"

#~ msgid "Comments"
#~ msgstr "Komentarai"

#~ msgid "Scanning Buddy Shares"
#~ msgstr "Skenuojami bičiulių viešiniai"

#, python-format
#~ msgid "Plugin %(module)s returned something weird, '%(value)s', ignoring"
#~ msgstr "Įskiepis %(module)s grąžino keistą reikšmę: „%(value)s“; nepaisoma"

#~ msgid "Set your personal ticker"
#~ msgstr "Nurodykite savo asmeninę bėgančią eilutę"

#~ msgid "Add user 'user' to your user list"
#~ msgstr "Įtraukti naudotoją „naudotojas“ į Jūsų naudotojų sąrašą"

#~ msgid "Remove user 'user' from your user list"
#~ msgstr "Pašalinti naudotoją „naudotojas“ iš naudotojų sąrašo"

#~ msgid "Request user info for user 'user'"
#~ msgstr "Užklausti naudotojo „naudotojas“ informacijos"

#~ msgid "Cannot find the pynicotine.utils module."
#~ msgstr "Nepavyko rasti pynicotine.utils modulio."

#~ msgid "Errors occured while trying to change process name:"
#~ msgstr "Bandant pakeisti proceso vardą įvyko klaidų:"

#, python-format
#~ msgid "Unable to fully disable plugin %s"
#~ msgstr "Nepavyko visiškai išjungyi įskiepio %s"

#, python-format
#~ msgid "Can not log in, reason: %s"
#~ msgstr "Nepavyko prisiregistroti, priežastis: %s"

#~ msgid ""
#~ "Someone else is logging in with the same nickname, server is going to "
#~ "disconnect us"
#~ msgstr ""
#~ "Kažkas kitas jungiasi su tokiu pat slapyvardžiu, serveris mus atjungs"

#, python-format
#~ msgid ""
#~ "IP %(ip)s:%(port)s is spoofing user %(user)s with a peer request, "
#~ "blocking because it does not match IP: %(real_ip)s"
#~ msgstr ""
#~ "IP %(ip)s:%(port)s apsimeta naudotoju %(user)s su porininko užklausa, "
#~ "blokuojama, nes jis neatitiktų IP: %(real_ip)s"

#, python-format
#~ msgid ""
#~ "Blocking %(user)s from making a UserInfo request, possible spoofing "
#~ "attempt from IP %(ip)s port %(port)s"
#~ msgstr ""
#~ "%(user)s UserInfo užklausa blokuojama, galimas bandymas apsimesti iš IP "
#~ "%(ip)s prievado %(port)s"

#, python-format
#~ msgid ""
#~ "Blocking %s from making a UserInfo request, possible spoofing attempt "
#~ "from an unknown IP & port"
#~ msgstr ""
#~ "%s UserInfo užklausa blokuojama, galimas bandymas apsimesti iš nežinomo "
#~ "IP ir prievado"

#, python-format
#~ msgid "%(user)s is banned, but is making a UserInfo request"
#~ msgstr "%(user)s užblokuotas, tačiau daro UserInfo užklausą"

#, python-format
#~ msgid ""
#~ "%(user)s is making a BrowseShares request, blocking possible spoofing "
#~ "attempt from IP %(ip)s port %(port)s"
#~ msgstr ""
#~ "%(user)s daro viešinių naršymo užklausą, blokuojamas galimas bandymas "
#~ "apsimesti iš IP %(ip)s prievado %(port)s"

#, python-format
#~ msgid ""
#~ "%(user)s is making a BrowseShares request, blocking possible spoofing "
#~ "attempt from an unknown IP & port"
#~ msgstr ""
#~ "%(user)s daro viešinių naršymo užklausą, blokuojamas galimas bandymas "
#~ "apsimesti iš nežinomo IP ir prievadas"

#, python-format
#~ msgid "%(user)s is making a BrowseShares request"
#~ msgstr "%(user)s daro viešinių naršymo užklausą"

#~ msgid "Shared files database seems to be corrupted, rescan your shares"
#~ msgstr ""
#~ "Atrodo, jog viešinamų failų duomenų bazė sugadinta, perskanuokite savo "
#~ "viešinius"

#~ msgid "Could not bind to a local port, aborting connection"
#~ msgstr "Nepavyko susieti su vietiniu prievadu, nutraukiamas ryšys"

#, python-format
#~ msgid "Retrying failed download: user %(user)s, file %(file)s"
#~ msgstr ""
#~ "Pakartotinas bandymas atsisiųsti nesėkmingas: naudotojas %(user)s, failas "
#~ "%(file)s"

#~ msgid "[Automatic Message] "
#~ msgstr "[Automatinė žinutė] "

#~ msgid "You are not allowed to send me files."
#~ msgstr "Jums neleidžiama atsiųsti man failų."

#~ msgid "(friend)"
#~ msgstr "(draugas)"

#~ msgid "Clear Queued"
#~ msgstr "Išvalyti eilėje"

#~ msgid "Abor_t"
#~ msgstr "Nu_traukti"

#~ msgid "Warning"
#~ msgstr "Dėmesio"

#, python-format
#~ msgid "Hide %(tab)s"
#~ msgstr "Paslėpti %(tab)s"

#~ msgid "Join Public Room"
#~ msgstr "Prisijungti prie viešo kambario"

#~ msgid "Immediate Download"
#~ msgstr "Skubus atsiuntimas"

#, python-format
#~ msgid "Client port is <b>%(port)s</b>"
#~ msgstr "Kliento prievadas yra <b>%(port)s</b>"

#~ msgid "Your IP address has not been retrieved from the server"
#~ msgstr "Jūsų IP adreso gauti iš serverio nepavyko"

#, python-format
#~ msgid "Your IP address is <b>%(ip)s</b>"
#~ msgstr "Jūsų IP adresas yra <b>%(ip)s</b>"

#~ msgid "IP:"
#~ msgstr "IP:"

#~ msgid "Server"
#~ msgstr "Serveris"

#~ msgid "Geo Block"
#~ msgstr "Geoblokavimas"

#~ msgid "URL Catching"
#~ msgstr "URL surinkimas"

#~ msgid "Initializing transfer"
#~ msgstr "Inicializuojamas siuntimas"

#~ msgid "Waiting for peer to connect"
#~ msgstr "Laukiama porininko prisijungimui"

#~ msgid "Connecting"
#~ msgstr "Jungiamasi"

#~ msgid "Getting address"
#~ msgstr "Gaunamas adresas"

#~ msgid "Lookup a User's IP"
#~ msgstr "Gauti naudotojo IP"

#, python-format
#~ msgid "ERROR: tray menu, %(error)s"
#~ msgstr "KLAIDA: skydelio meniu, %(error)s"

#, python-format
#~ msgid "Speed: %s"
#~ msgstr "Greitis: %s"

#, python-format
#~ msgid "Files: %s"
#~ msgstr "Failai: %s"

#, python-format
#~ msgid "Directories: %s"
#~ msgstr "Aplankai: %s"

#~ msgid "Hates"
#~ msgstr "Nemėgsta"

#, python-format
#~ msgid "Total uploads allowed: %i"
#~ msgstr "Iš viso leidžiamų išsiuntimų: %i"

#, python-format
#~ msgid "Queue size: %i"
#~ msgstr "Eilės dydis: %i"

#, python-format
#~ msgid "Slots free: %s"
#~ msgstr "Laisvų vietų: %s"

#~ msgid "Users in list"
#~ msgstr "Sąraše esantys naudotojai"

#, python-format
#~ msgid "%s"
#~ msgstr "%s"

#, python-format
#~ msgid "to %(user)s"
#~ msgstr "%(user)s"

#~ msgid "Warnings"
#~ msgstr "Perspėjimai"

#~ msgid ""
#~ "Send the private message directly to the user (not supported on most "
#~ "clients)"
#~ msgstr ""
#~ "Siųsti asmeninį pranešimą tiesiogiai naudotojui (dauguma klientų to "
#~ "nepalaiko)"

#~ msgid "Log"
#~ msgstr "Žurnalas"

#~ msgid "Total uploads allowed: unknown"
#~ msgstr "Iš viso leidžiama išsiuntimų: nežinoma"

#~ msgid "Slots free: unknown"
#~ msgstr "Laisvi lizdai: nežinoma"

#~ msgid "Queue size: unknown"
#~ msgstr "Eilės dydis: nežinoma"

#~ msgid "Speed: unknown"
#~ msgstr "Greitis: nežinoma"

#~ msgid "Files: unknown"
#~ msgstr "Failai: nežinoma"

#~ msgid "Directories: unknown"
#~ msgstr "Aplankai: nežinoma"

#~ msgid "Accepts Uploads from:"
#~ msgstr "Priima failų įkėlimus iš:"

#~ msgid "_Modes"
#~ msgstr "_Veiksenos"

#~ msgid "_Private Chat"
#~ msgstr "_Pokalbiai"

#~ msgid "Buddy _List"
#~ msgstr "Bičiulių _sąrašas"

#~ msgid "_Chat Rooms"
#~ msgstr "_Kambariai"

#~ msgid "_Interests"
#~ msgstr "P_omėgiai"

#~ msgid "Add..."
#~ msgstr "Pridėti..."

#~ msgid "Away:"
#~ msgstr "Neaktyvus:"

#~ msgid "Offline:"
#~ msgstr "Atsijungęs:"

#~ msgid "Online:"
#~ msgstr "Prisijungęs:"

#~ msgid "Enable geographical blocker"
#~ msgstr "Įjungti geografinį blokavimą"

#~ msgid "Always quit when main window is closed"
#~ msgstr "Užveriant pagrindinį langą, uždaryti programą"

#~ msgid "Notify:"
#~ msgstr "Pranešti:"

#~ msgid "lines"
#~ msgstr "eilutes (-čių)"

#~ msgid "last.fm"
#~ msgstr "last.fm"

#~ msgid "Player Command/Username"
#~ msgstr "Grotuvo komanda / naudotojo vardas"

#~ msgid "Legend:"
#~ msgstr "Legenda:"

#~ msgid "Example:"
#~ msgstr "Pavyzdys:"

#~ msgid "Send out a max of"
#~ msgstr "Išsiųsti daugiausiai"

#~ msgid "Enable URL catching"
#~ msgstr "Įjungti URL surinkimą"

#~ msgid "Search results"
#~ msgstr "Paieškos rezultatai"

#~ msgid "Rescanning Buddy Shares started"
#~ msgstr "Pradėtas pakartotinas bičiulių viešinių skanavimas"

#~ msgid "Rescanning Buddy Shares finished"
#~ msgstr "Baigtas bičiulių viešininų pakartotinis skanavimas"

#~ msgid "Rescanning finished"
#~ msgstr "Baigtas pakartotinis skanavimas"
