# SPDX-FileCopyrightText: 2003-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2023-12-01 21:04+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Danish <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/da/>\n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.3-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
#, fuzzy
msgid "Soulseek Client"
msgstr "Soulseek-klient"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
#, fuzzy
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Grafisk klient til Soulseek peer-to-peer-netværket"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr "Soulseek;Nicotine;deling;chat;musik;P2P;peer-to-peer;GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
#, fuzzy
msgid "Browse the Soulseek network"
msgstr "Grafisk klient til Soulseek peer-to-peer-netværket"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
#, fuzzy
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+ er en grafisk klient til Soulseek peer-to-peer-netværket."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ sigter sig mod at være et letvægts, behageligt, gratis og open "
"source (FOSS) alternativ til den officielle Soulseek-klient, samtidig med at "
"den giver et omfattende sæt funktioner."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
msgid "Search Files"
msgstr "Søg filer"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Downloads"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
msgid "Browse Shares"
msgstr "Søg delinger"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
#, fuzzy
msgid "Private Chat"
msgstr "Privat chat"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Nicotine+-holdet"

#: pynicotine/__init__.py:50
#, fuzzy, python-format
msgid "Website: %s"
msgstr "Udført: %s"

#: pynicotine/__init__.py:56
#, fuzzy
msgid "show this help message and exit"
msgstr "vise denne Hjælp-meddelelse og afslutte"

#: pynicotine/__init__.py:59
#, fuzzy
msgid "file"
msgstr "fil"

#: pynicotine/__init__.py:60
#, fuzzy
msgid "use non-default configuration file"
msgstr "bruge ikke-standardkonfigurationsfil"

#: pynicotine/__init__.py:63
#, fuzzy
msgid "dir"
msgstr "Dir"

#: pynicotine/__init__.py:64
#, fuzzy
msgid "alternative directory for user data and plugins"
msgstr "bruge ikke-standardmappe til plugins"

#: pynicotine/__init__.py:68
#, fuzzy
msgid "start the program without showing window"
msgstr "starte programmet uden at vise vindue"

#: pynicotine/__init__.py:71
#, fuzzy
msgid "ip"
msgstr "ip"

#: pynicotine/__init__.py:72
#, fuzzy
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "binde sockets til den givne IP (nyttig til VPN)"

#: pynicotine/__init__.py:75
#, fuzzy
msgid "port"
msgstr "havn"

#: pynicotine/__init__.py:76
#, fuzzy
msgid "listen on the given port"
msgstr "lytte på den givne port"

#: pynicotine/__init__.py:80
#, fuzzy
msgid "rescan shared files"
msgstr "Omindexere delede filer"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr "start programmet i en hovedløs tilstand (ingen GUI)"

#: pynicotine/__init__.py:88
msgid "display version and exit"
msgstr "vis version og afslut"

#: pynicotine/__init__.py:121
#, fuzzy, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Du bruger en ikke-understøttet version af Python (%(old_version)s).\n"
"Du skal installere Python %(min_version)s eller nyere."

#: pynicotine/__init__.py:192
#, fuzzy
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Aktierne kunne ikke scannes. Luk andre Nicotine+-forekomster, og prøv igen."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, fuzzy, python-format
msgid "%(user)s is away"
msgstr "Brugeren %s er away"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, fuzzy, python-format
msgid "%(user)s is online"
msgstr "Brugeren %s er online"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, fuzzy, python-format
msgid "%(user)s is offline"
msgstr "Brugeren %s er offline"

#: pynicotine/buddies.py:316
#, fuzzy
msgid "Buddy Status"
msgstr "Venneliste"

#: pynicotine/chatrooms.py:383
#, fuzzy, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Du er blevet føjet til et privat rum: %(room)s"

#: pynicotine/chatrooms.py:478
#, fuzzy, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr "Chatbesked fra bruger '%(user)s' i rum '%(room)s': %(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr "Kunne ikke skape biblioteket '%(path)s', rapporteret fejl: %(error)s"

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr "Fejl ved sikkerhedskopiering af konfiguration: %s"

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr "Konfigurationen sikkerhedskopieret til: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Indlæser %(program)s %(version)s"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Afslutter %(program)s %(version)s, %(status)s…"

#: pynicotine/core.py:246
#, fuzzy
msgid "terminating"
msgstr "Afslutning"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "programmet lukker"

#: pynicotine/core.py:259
#, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Afslut %(program)s %(version)s!"

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr ""
"Du skal angive et brugernavn og en adgangskode, før du skaber en forbindelse…"

#: pynicotine/downloads.py:239
#, fuzzy, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr "Fejl: Overførselsfilteret mislykkedes! Kontroller filtrene. Årsag: %s"

#: pynicotine/downloads.py:254
#, fuzzy, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Fejl: %(num)d Overførselsfiltre mislykkedes! %(error)s "

#: pynicotine/downloads.py:366
#, fuzzy, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s hentet fra %(user)s"

#: pynicotine/downloads.py:370
#, fuzzy
msgid "File Downloaded"
msgstr "Fil hentet"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Udført: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, fuzzy, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "Udførelsen af '%(command)s' mislykkedes: %(error)s"

#: pynicotine/downloads.py:407
#, fuzzy, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s hentet fra %(user)s"

#: pynicotine/downloads.py:411
#, fuzzy
msgid "Folder Downloaded"
msgstr "Mappe hentet"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Udført på mappe: %s"

#: pynicotine/downloads.py:443
#, fuzzy, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "'%(tempfile)s' kunne ikke flyttes til '%(file)s': %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
#, fuzzy
msgid "Download Folder Error"
msgstr "Fejl på downloadsbibliotek"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Download færdig: %(user)s, fil %(file)s"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Mislykked download: bruger %(user)s, fil %(file)s"

#: pynicotine/downloads.py:1150
#, fuzzy, python-format
msgid "Download I/O error: %s"
msgstr "Hent I/O-fejl: %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Kunne ikke oprette et exklusivt lås på fil - I/O-fel: %s"

#: pynicotine/downloads.py:1202
#, fuzzy, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Filen %(path)s kan ikke gemmes: %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Mislykked download: bruger %(user)s, fil %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, fuzzy, python-format
msgid "Cannot find %s, please install it."
msgstr "%s blev ikke fundet."

#: pynicotine/gtkgui/__init__.py:162
#, fuzzy
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr "Intet grafisk miljø tilgængeligt, bruger hovedløs (ingen GUI) tilstand"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "_Tilslut"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "Luk ne_d"

#: pynicotine/gtkgui/application.py:308
#, fuzzy
msgid "Soulseek _Privileges"
msgstr "_Priveligiered"

#: pynicotine/gtkgui/application.py:314
#, fuzzy
msgid "_Preferences"
msgstr "_Preferences"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
#, fuzzy
msgid "_Quit"
msgstr "_Quit"

#: pynicotine/gtkgui/application.py:337
#, fuzzy
msgid "Browse _Public Shares"
msgstr "_Browse offentlige aktier"

#: pynicotine/gtkgui/application.py:338
#, fuzzy
msgid "Browse _Buddy Shares"
msgstr "Bro_wse Buddy-aktier"

#: pynicotine/gtkgui/application.py:339
#, fuzzy
msgid "Browse _Trusted Shares"
msgstr "Bro_wse Buddy-aktier"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
#, fuzzy
msgid "_Rescan Shares"
msgstr "Omindexere delede filer"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
#, fuzzy
msgid "Configure _Shares"
msgstr "_Configure Aktier"

#: pynicotine/gtkgui/application.py:371
#, fuzzy
msgid "_Keyboard Shortcuts"
msgstr "_Keyboard Genveje"

#: pynicotine/gtkgui/application.py:372
#, fuzzy
msgid "_Setup Assistant"
msgstr "_Setup Assistent"

#: pynicotine/gtkgui/application.py:373
#, fuzzy
msgid "_Transfer Statistics"
msgstr "Overførsler"

#: pynicotine/gtkgui/application.py:378
#, fuzzy
msgid "Report a _Bug"
msgstr "Rapportér en _Bug"

#: pynicotine/gtkgui/application.py:379
#, fuzzy
msgid "Improve T_ranslations"
msgstr "Forbedre T_oversættelser"

#: pynicotine/gtkgui/application.py:383
#, fuzzy
msgid "_About Nicotine+"
msgstr "Om _Nicotine+"

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "_Arkiv"

#: pynicotine/gtkgui/application.py:395
#, fuzzy
msgid "_Shares"
msgstr "_Shares"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
#, fuzzy
msgid "_Help"
msgstr "_Help"

#: pynicotine/gtkgui/application.py:410
#, fuzzy
msgid "_Browse Shares"
msgstr "Bro_wse Buddy-aktier"

#: pynicotine/gtkgui/application.py:465
#, fuzzy, python-format
msgid "Unable to show notification: %s"
msgstr "Meddelelses-pop op-vinduet kunne ikke vises: %s"

#: pynicotine/gtkgui/application.py:526
#, fuzzy
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Har du virkelig lyst til at afslutte Nicotine+?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr ""

#: pynicotine/gtkgui/application.py:529
#, fuzzy
msgid "Do you really want to exit?"
msgstr "Har du virkelig lyst til at afslutte Nicotine+?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
#, fuzzy
msgid "_No"
msgstr "N"

#: pynicotine/gtkgui/application.py:535
#, fuzzy
msgid "_Run in Background"
msgstr "Kør i baggrunden"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
#, fuzzy
msgid "Quit Nicotine+"
msgstr "Afslut Nicotine+"

#: pynicotine/gtkgui/application.py:561
#, fuzzy
msgid "Shares Not Available"
msgstr "Aktier ikke tilgængelige"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
#, fuzzy
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Kontroller, at eksterne diske er monteret, og at mappetilladelserne er "
"korrekte."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
#, fuzzy
msgid "_Cancel"
msgstr "_Cancel"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "Forsøg igen"

#: pynicotine/gtkgui/application.py:567
#, fuzzy
msgid "_Force Rescan"
msgstr "_Tving genscanning"

#: pynicotine/gtkgui/application.py:742
#, fuzzy
msgid "Message Downloading Users"
msgstr "Brugere, der downloader beskeder"

#: pynicotine/gtkgui/application.py:743
#, fuzzy
msgid "Send private message to all users who are downloading from you:"
msgstr "Send privat besked til alle brugere, der downloader fra dig:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
#, fuzzy
msgid "_Send Message"
msgstr "Send meddelelse"

#: pynicotine/gtkgui/application.py:756
#, fuzzy
msgid "Message Buddies"
msgstr "Meddelelser"

#: pynicotine/gtkgui/application.py:757
#, fuzzy
msgid "Send private message to all online buddies:"
msgstr "Send privat besked til alle online venner:"

#: pynicotine/gtkgui/application.py:786
#, fuzzy
msgid "Select a Saved Shares List File"
msgstr "Vælg en listefil til gemt shares"

#: pynicotine/gtkgui/application.py:877
#, fuzzy
msgid "Critical Error"
msgstr "Kritisk fejl"

#: pynicotine/gtkgui/application.py:878
#, fuzzy
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ er stødt på en kritisk fejl og skal afsluttes. Kopier følgende "
"meddelelse, og medtag den i en fejlrapport:"

#: pynicotine/gtkgui/application.py:882
#, fuzzy
msgid "_Quit Nicotine+"
msgstr "Afslut Nicotine+"

#: pynicotine/gtkgui/application.py:883
#, fuzzy
msgid "_Copy & Report Bug"
msgstr "Fejl i kopiering og rapport"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Status"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
#, fuzzy
msgid "Country"
msgstr "Land"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Bruger"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Hastighet"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Filer"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
#, fuzzy
msgid "Trusted"
msgstr "Betroede"

#: pynicotine/gtkgui/buddies.py:108
#, fuzzy
msgid "Notify"
msgstr "Melde"

#: pynicotine/gtkgui/buddies.py:114
#, fuzzy
msgid "Prioritized"
msgstr "Prioriteret"

#: pynicotine/gtkgui/buddies.py:120
#, fuzzy
msgid "Last Seen"
msgstr "Sidst set"

#: pynicotine/gtkgui/buddies.py:126
#, fuzzy
msgid "Note"
msgstr "Bemærk"

#: pynicotine/gtkgui/buddies.py:143
#, fuzzy
msgid "Add User _Note…"
msgstr "Tilføj brugeren til vennelisten"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Fjern"

#: pynicotine/gtkgui/buddies.py:364
#, fuzzy
msgid "Never seen"
msgstr "Aldrig set"

#: pynicotine/gtkgui/buddies.py:529
#, fuzzy
msgid "Add User Note"
msgstr "Tilføj brugeren til vennelisten"

#: pynicotine/gtkgui/buddies.py:530
#, fuzzy, python-format
msgid "Add a note about user %s:"
msgstr "Tilføj et par noter, der er knyttet til brugeren %s:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
#, fuzzy
msgid "_Add"
msgstr "_Add…"

#: pynicotine/gtkgui/chatrooms.py:224
#, fuzzy
msgid "Create New Room?"
msgstr "Opret nyt rum?"

#: pynicotine/gtkgui/chatrooms.py:225
#, fuzzy, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Vil du virkelig oprette et nyt rum \"%s\"?"

#: pynicotine/gtkgui/chatrooms.py:226
#, fuzzy
msgid "Make room private"
msgstr "Gør plads privat"

#: pynicotine/gtkgui/chatrooms.py:515
#, fuzzy
msgid "Search activity log…"
msgstr "Søgninger"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
#, fuzzy
msgid "Search chat log…"
msgstr "Søgninger"

#: pynicotine/gtkgui/chatrooms.py:597
#, fuzzy
msgid "Sear_ch User's Files"
msgstr "_Søg filer"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
#, fuzzy
msgid "Find…"
msgstr "Finde…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Kopier"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
#, fuzzy
msgid "Copy All"
msgstr "Kopier alle"

#: pynicotine/gtkgui/chatrooms.py:608
#, fuzzy
msgid "Clear Activity View"
msgstr "Ryd aktivitetsvisning"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
#, fuzzy
msgid "_Leave Room"
msgstr "Forlad rummet 'rum'"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
#, fuzzy
msgid "Copy Link"
msgstr "Kopier _URL"

#: pynicotine/gtkgui/chatrooms.py:624
#, fuzzy
msgid "View Room Log"
msgstr "Vis værelseslog"

#: pynicotine/gtkgui/chatrooms.py:627
#, fuzzy
msgid "Delete Room Log…"
msgstr "Slet værelseslog…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
#, fuzzy
msgid "Clear Message View"
msgstr "Ryd meddelelsesvisning"

#: pynicotine/gtkgui/chatrooms.py:832
#, fuzzy, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s ind %(room)s kom ind i rummet"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, fuzzy, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "%(user)s ind %(room)s kom ind i rummet"

#: pynicotine/gtkgui/chatrooms.py:855
#, fuzzy, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "%(user)s ind %(room)s kom ind i rummet"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s kom ind i rummet"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s forlod rummet"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s er away"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s er tilbage"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
#, fuzzy
msgid "Delete Logged Messages?"
msgstr "Skal logførte meddelelser slettes?"

#: pynicotine/gtkgui/chatrooms.py:1197
#, fuzzy
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr "Vil du slette alle logførte meddelelser permanent for dette rum?"

#: pynicotine/gtkgui/dialogs/about.py:405
#, fuzzy
msgid "About"
msgstr "Djibouti"

#: pynicotine/gtkgui/dialogs/about.py:415
#, fuzzy
msgid "Website"
msgstr "Udført: %s"

#: pynicotine/gtkgui/dialogs/about.py:457
#, fuzzy, python-format
msgid "Error checking latest version: %s"
msgstr "Der opstod en fejl under hentning af den nyeste version"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr ""

#: pynicotine/gtkgui/dialogs/about.py:467
#, fuzzy
msgid "Up to date"
msgstr "Opdateret"

#: pynicotine/gtkgui/dialogs/about.py:496
#, fuzzy
msgid "Checking latest version…"
msgstr "Tjek _Latest version"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
#, fuzzy
msgid "Setup Assistant"
msgstr "_Setup Assistent"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
#, fuzzy
msgid "Virtual Folder"
msgstr "Virtuel mappe"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
#, fuzzy
msgid "Folder"
msgstr "Mappe"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#, fuzzy
msgid "_Previous"
msgstr "Forrige fil"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#, fuzzy
msgid "_Finish"
msgstr "Klar"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
#, fuzzy
msgid "_Next"
msgstr "_Næste"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
#, fuzzy
msgid "Add a Shared Folder"
msgstr "Tilføje en delt mappe"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
#, fuzzy
msgid "Edit Shared Folder"
msgstr "Delede mapper"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, fuzzy, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Angiv et nyt virtuelt navn til '%(dir)s':"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
#, fuzzy
msgid "_Edit"
msgstr "Karakter"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, fuzzy, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"Brugeren %s findes allerede, og den indtastede adgangskode er ugyldig. Vælg "
"et andet brugernavn, hvis det er første gang, du logger på."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
#, fuzzy
msgid "File Properties"
msgstr "Egenskaber for fil"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, fuzzy, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "Filegenskaber (%(num)i for %(total)i)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, fuzzy, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Filegenskaber (%(num)i for %(total)i)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
#, fuzzy
msgid "_Apply"
msgstr "Anvende"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
#, fuzzy
msgid "Add…"
msgstr "Tilføje…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
#, fuzzy
msgid "Edit…"
msgstr "Redigere…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, fuzzy, python-format
msgid "%s Settings"
msgstr "Indstillninger"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
#, fuzzy
msgid "Add Item"
msgstr "Vare"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
#, fuzzy
msgid "Edit Item"
msgstr "Interesser"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
#, fuzzy
msgid "Unknown"
msgstr "Ukendt"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
#, fuzzy
msgid "Check Port Status"
msgstr "Kontroller portstatus"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, fuzzy, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, port %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
#, fuzzy
msgid "Password Change Rejected"
msgstr "Ændring af adgangskode afvist"

#: pynicotine/gtkgui/dialogs/preferences.py:218
#, fuzzy
msgid "Enter a new password for your Soulseek account:"
msgstr "Indtast en ny adgangskode til din Soulseek-konto:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
#, fuzzy
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"I er i øjeblikket logget ud af Soulseek-netværket. Hvis du vil ændre "
"adgangskoden til en eksisterende Soulseek-konto, skal du være logget ind på "
"den pågældende konto."

#: pynicotine/gtkgui/dialogs/preferences.py:223
#, fuzzy
msgid "Enter password to use when logging in:"
msgstr "Angiv den adgangskode, der skal bruges, når du logger på:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
#, fuzzy
msgid "Change Password"
msgstr "Password:"

#: pynicotine/gtkgui/dialogs/preferences.py:230
#, fuzzy
msgid "_Change"
msgstr "Ændring af adgangskode afvist"

#: pynicotine/gtkgui/dialogs/preferences.py:274
#, fuzzy
msgid "No one"
msgstr "Ingen"

#: pynicotine/gtkgui/dialogs/preferences.py:275
#, fuzzy
msgid "Everyone"
msgstr "Alle"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Venner"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
#, fuzzy
msgid "Trusted buddies"
msgstr "Venner"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
#, fuzzy
msgid "Nothing"
msgstr "Ikke noget"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
#, fuzzy
msgid "Open File"
msgstr "Næste fil"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
#, fuzzy
msgid "Open in File Manager"
msgstr "_Open i Filhåndtering"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Søg"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
#, fuzzy
msgid "Pause"
msgstr "Pause"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
#, fuzzy
msgid "Resume"
msgstr "Genoptage"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
#, fuzzy
msgid "Browse Folder"
msgstr "Kig i filer"

#: pynicotine/gtkgui/dialogs/preferences.py:317
#, fuzzy
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Syntaks</b>: Ufølsom mellem store og små bogstaver. Hvis det er "
"aktiveret, kan Python regulære udtryk bruges, ellers understøttes kun "
"wildcard * matches."

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr "Filtrere"

#: pynicotine/gtkgui/dialogs/preferences.py:334
#, fuzzy
msgid "Regex"
msgstr "Regex"

#: pynicotine/gtkgui/dialogs/preferences.py:468
#, fuzzy
msgid "Add Download Filter"
msgstr "_Hent fil(er)"

#: pynicotine/gtkgui/dialogs/preferences.py:469
#, fuzzy
msgid "Enter a new download filter:"
msgstr "Angiv et nyt overførselsfilter:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
#, fuzzy
msgid "Enable regular expressions"
msgstr "Aktiver regulære udtryk"

#: pynicotine/gtkgui/dialogs/preferences.py:498
#, fuzzy
msgid "Edit Download Filter"
msgstr "_Hent fil(er)"

#: pynicotine/gtkgui/dialogs/preferences.py:499
#, fuzzy
msgid "Modify the following download filter:"
msgstr "Rediger følgende overførselsfilter:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, fuzzy, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d Mislykkedes! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
#, fuzzy
msgid "Filters Successful"
msgstr "Filtrene lykkedes"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "Afbryd"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
#, fuzzy
msgid "Retry"
msgstr "Forsøg igen"

#: pynicotine/gtkgui/dialogs/preferences.py:828
#, fuzzy
msgid "Round Robin"
msgstr "Runde Robin"

#: pynicotine/gtkgui/dialogs/preferences.py:829
#, fuzzy
msgid "First In, First Out"
msgstr "Først ind, først ud"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
#, fuzzy
msgid "Username"
msgstr "Brugernavn"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
#, fuzzy
msgid "IP Address"
msgstr "Vis IP-a_dresse"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
#, fuzzy
msgid "Ignore User"
msgstr "Ignorer bruger"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
#, fuzzy
msgid "Enter the name of the user you want to ignore:"
msgstr "Angiv navnet på den bruger, du vil ignorere:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
#, fuzzy
msgid "Ignore IP Address"
msgstr "Vis IP-a_dresse"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#, fuzzy
msgid "Enter an IP address you want to ignore:"
msgstr "Angiv en IP-adresse, du vil ignorere:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
#, fuzzy
msgid "* is a wildcard"
msgstr "* er et wildcard"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
#, fuzzy
msgid "Ban User"
msgstr "Forbyd bruger"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
#, fuzzy
msgid "Enter the name of the user you want to ban:"
msgstr "Angiv navnet på den bruger, du vil forbyde:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
#, fuzzy
msgid "Ban IP Address"
msgstr "Vis IP-a_dresse"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
#, fuzzy
msgid "Enter an IP address you want to ban:"
msgstr "Angiv en IP-adresse, du vil blokere:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
#, fuzzy
msgid "Pattern"
msgstr "Mønster"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
#, fuzzy
msgid "Replacement"
msgstr "Ombytning"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
#, fuzzy
msgid "Censor Pattern"
msgstr "Censormønster"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
#, fuzzy
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Angiv et mønster, du vil censurere. Tilføj mellemrum omkring mønsteret, hvis "
"du ikke vil matche strenge i ord (kan mislykkes i begyndelsen og slutningen "
"af linjerne)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
#, fuzzy
msgid "Edit Censored Pattern"
msgstr "Censurerede mønstre"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
#, fuzzy
msgid "Add Replacement"
msgstr "Ombytning"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
#, fuzzy
msgid "Enter a text pattern and what to replace it with:"
msgstr "Indtast henholdsvis tekstmønster og erstatning:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
#, fuzzy
msgid "Edit Replacement"
msgstr "Ombytning"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
#, fuzzy
msgid "System default"
msgstr "Standard"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
#, fuzzy
msgid "Show confirmation dialog"
msgstr "Vis bekræftelsesdialogboks"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
#, fuzzy
msgid "Run in the background"
msgstr "Kør i baggrunden"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
#, fuzzy
msgid "bold"
msgstr "modig"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
#, fuzzy
msgid "italic"
msgstr "Kursiv"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
#, fuzzy
msgid "underline"
msgstr "Online"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
#, fuzzy
msgid "normal"
msgstr "normal"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
#, fuzzy
msgid "Separate Buddies tab"
msgstr "Meddelelser"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
#, fuzzy
msgid "Sidebar in Chat Rooms tab"
msgstr "Buddy Liste i chatrum"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1777
#, fuzzy
msgid "Top"
msgstr "Top"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
#, fuzzy
msgid "Bottom"
msgstr "Bund"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
#, fuzzy
msgid "Left"
msgstr "Venstre"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
#, fuzzy
msgid "Right"
msgstr "Højre"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "Online"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Away"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Offline"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
#, fuzzy
msgid "Tab Changed"
msgstr "Ændring af adgangskode afvist"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
#, fuzzy
msgid "Tab Highlight"
msgstr "Fremhæve"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
#, fuzzy
msgid "Window"
msgstr "Vindue"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
#, fuzzy
msgid "Online (Tray)"
msgstr "Tilslut"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
#, fuzzy
msgid "Away (Tray)"
msgstr "Væk (bakke)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
#, fuzzy
msgid "Offline (Tray)"
msgstr "Offline"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
#, fuzzy
msgid "Message (Tray)"
msgstr "Meddelelse (bakke)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Protokol"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
#, fuzzy
msgid "Command"
msgstr "Kommentarer"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
#, fuzzy
msgid "Add URL Handler"
msgstr "Håndterere"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
#, fuzzy
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Indtast henholdsvis protokollen og kommandoen for URL-handleren:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
#, fuzzy
msgid "Edit Command"
msgstr "Rediger kommentarer"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, fuzzy, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Angiv et nyt virtuelt navn til '%(dir)s':"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
#, fuzzy
msgid "Username;APIKEY"
msgstr "Brugernavn; APIKEY:"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
#, fuzzy
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr "Klientnavn (f.eks. amarok, dristig, eksatilt) eller tomt for auto:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
#, fuzzy
msgid "Username: "
msgstr "Brugernavn:"

#: pynicotine/gtkgui/dialogs/preferences.py:2767
#, fuzzy
msgid "Command:"
msgstr "Kommentarer"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
#, fuzzy
msgid "Title"
msgstr "Titel"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, fuzzy, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Spiller nu (typisk \"%(artist)s - %(title)s\")"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
#, fuzzy
msgid "Artist"
msgstr "Kunstner"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
#, fuzzy
msgid "Duration"
msgstr "Varighed"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "Bitrate"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
#, fuzzy
msgid "Comment"
msgstr "Kommentar"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
#, fuzzy
msgid "Album"
msgstr "Album"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
#, fuzzy
msgid "Track Number"
msgstr "Spor nummer"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
#, fuzzy
msgid "Year"
msgstr "År"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
#, fuzzy
msgid "Filename (URI)"
msgstr "Filnavn (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
#, fuzzy
msgid "Program"
msgstr "Program"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
#, fuzzy
msgid "Enabled"
msgstr "Aktiveret"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
#, fuzzy
msgid "Plugin"
msgstr "Plugins"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
#, fuzzy
msgid "No Plugin Selected"
msgstr "Der er ikke valgt et plugin"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
#, fuzzy
msgid "Preferences"
msgstr "Indstillinger"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
#, fuzzy
msgid "Network"
msgstr "Netværk"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
#, fuzzy
msgid "User Interface"
msgstr "Interface"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Delede mapper"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
#, fuzzy
msgid "Uploads"
msgstr "Uploads"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Søgninger"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
#, fuzzy
msgid "User Profile"
msgstr "Kig igennem filer"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
#, fuzzy
msgid "Chats"
msgstr "Chattrum"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
#, fuzzy
msgid "Now Playing"
msgstr "Spiller nu"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Logger"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
#, fuzzy
msgid "Banned Users"
msgstr "Sortlistede brugere:"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
#, fuzzy
msgid "Ignored Users"
msgstr "Ignorerade brugere:"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
#, fuzzy
msgid "URL Handlers"
msgstr "Håndterere"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
#, fuzzy
msgid "Plugins"
msgstr "Plugins"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
#, fuzzy
msgid "Pick a File Name for Config Backup"
msgstr "Vælg et filnavn til sikkerhedskopiering af Config"

#: pynicotine/gtkgui/dialogs/statistics.py:75
#, fuzzy
msgid "Transfer Statistics"
msgstr "Overførsler"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, fuzzy, python-format
msgid "Total Since %(date)s"
msgstr "I alt siden %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
#, fuzzy
msgid "Reset Transfer Statistics?"
msgstr "Skal overførselsstatistikken nulstilles?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
#, fuzzy
msgid "Do you really want to reset transfer statistics?"
msgstr "Vil du virkelig nulstille overførselsstatistik?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
#, fuzzy
msgid "Wishlist"
msgstr "_Wishlist"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
#, fuzzy
msgid "Wish"
msgstr "Ønske"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
#, fuzzy
msgid "_Search for Item"
msgstr "_Søg efter dette"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
#, fuzzy
msgid "Edit Wish"
msgstr "Karakter"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, fuzzy, python-format
msgid "Enter new value for wish '%s':"
msgstr "Angiv et nyt virtuelt navn til '%(dir)s':"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
#, fuzzy
msgid "Clear Wishlist?"
msgstr "Rens afsluttede"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
#, fuzzy
msgid "Do you really want to clear your wishlist?"
msgstr "Vil du virkelig rydde din ønskeliste?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Søgesti"

#: pynicotine/gtkgui/downloads.py:47
#, fuzzy
msgid "_Resume"
msgstr "_Resume"

#: pynicotine/gtkgui/downloads.py:48
#, fuzzy
msgid "P_ause"
msgstr "P_ause"

#: pynicotine/gtkgui/downloads.py:72
#, fuzzy
msgid "Finished / Filtered"
msgstr "Færdig/filtreret"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Klar"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
#, fuzzy
msgid "Paused"
msgstr "Standsede"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
#, fuzzy
msgid "Filtered"
msgstr "Filtreret"

#: pynicotine/gtkgui/downloads.py:77
#, fuzzy
msgid "Deleted"
msgstr "Slettet"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
#, fuzzy
msgid "Queued…"
msgstr "Kø…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
#, fuzzy
msgid "Everything…"
msgstr "Alt…"

#: pynicotine/gtkgui/downloads.py:132
#, fuzzy, python-format
msgid "Downloads: %(speed)s"
msgstr "Overførsler: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
#, fuzzy
msgid "Clear Queued Downloads"
msgstr "Ryd overførsler i kø"

#: pynicotine/gtkgui/downloads.py:139
#, fuzzy
msgid "Do you really want to clear all queued downloads?"
msgstr "Vil du virkelig rydde alle downloads i kø?"

#: pynicotine/gtkgui/downloads.py:151
#, fuzzy
msgid "Clear All Downloads"
msgstr "Downloads"

#: pynicotine/gtkgui/downloads.py:152
#, fuzzy
msgid "Do you really want to clear all downloads?"
msgstr "Ønsker du virkelig at rydde alle downloads?"

#: pynicotine/gtkgui/downloads.py:169
#, fuzzy, python-format
msgid "Download %(num)i files?"
msgstr "Download %(num)i filer?"

#: pynicotine/gtkgui/downloads.py:170
#, fuzzy, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr "Vil du virkelig hente %(num)i filer fra %(user)ss mappe %(folder)s?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
#, fuzzy
msgid "_Download Folder"
msgstr "_Downloadsmappe"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
#, fuzzy
msgid "Likes"
msgstr "Holder"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
#, fuzzy
msgid "Dislikes"
msgstr "Jeg kan ikke li'"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Karakter"

#: pynicotine/gtkgui/interests.py:111
#, fuzzy
msgid "Item"
msgstr "Vare"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
#, fuzzy
msgid "_Recommendations for Item"
msgstr "_Rekommendationer"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
#, fuzzy
msgid "I _Like This"
msgstr "Jeg kan _li' dette"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
#, fuzzy
msgid "I _Dislike This"
msgstr "Jeg kan _li' dette"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
#, fuzzy
msgid "Recommendations"
msgstr "Rekommendationer for %s"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
#, fuzzy
msgid "Similar Users"
msgstr "Lignende brugere"

#: pynicotine/gtkgui/interests.py:427
#, fuzzy, python-format
msgid "Recommendations (%s)"
msgstr "Rekommendationer for %s"

#: pynicotine/gtkgui/interests.py:451
#, fuzzy, python-format
msgid "Similar Users (%s)"
msgstr "Lignende brugere"

#: pynicotine/gtkgui/mainwindow.py:238
#, fuzzy
msgid "Search log…"
msgstr "Søgninger"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, fuzzy, python-format
msgid "Private Message from %(user)s"
msgstr "Privat meddelelse fra %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
#, fuzzy
msgid "Wishlist Results Found"
msgstr "Ønskelisteresultater fundet"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
#, fuzzy
msgid "User Profiles"
msgstr "Kig igennem filer"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
#, fuzzy
msgid "Chat Rooms"
msgstr "Chatrum"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "Interesser"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
#, fuzzy
msgid "Chat"
msgstr "Chatte"

#: pynicotine/gtkgui/mainwindow.py:1111
#, fuzzy
msgid "[Debug] Connections"
msgstr "Forbindelser"

#: pynicotine/gtkgui/mainwindow.py:1112
#, fuzzy
msgid "[Debug] Messages"
msgstr "Meddelelser"

#: pynicotine/gtkgui/mainwindow.py:1113
#, fuzzy
msgid "[Debug] Transfers"
msgstr "Overførsler"

#: pynicotine/gtkgui/mainwindow.py:1114
#, fuzzy
msgid "[Debug] Miscellaneous"
msgstr "Diverse"

#: pynicotine/gtkgui/mainwindow.py:1119
#, fuzzy
msgid "_Find…"
msgstr "Finde…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
#, fuzzy
msgid "_Copy"
msgstr "Kopier"

#: pynicotine/gtkgui/mainwindow.py:1122
#, fuzzy
msgid "Copy _All"
msgstr "Kopier alle"

#: pynicotine/gtkgui/mainwindow.py:1127
#, fuzzy
msgid "View _Debug Logs"
msgstr "Vis fejlfindingslogfiler"

#: pynicotine/gtkgui/mainwindow.py:1128
#, fuzzy
msgid "View _Transfer Logs"
msgstr "per overforsel"

#: pynicotine/gtkgui/mainwindow.py:1132
#, fuzzy
msgid "_Log Categories"
msgstr "Kategorier"

#: pynicotine/gtkgui/mainwindow.py:1134
#, fuzzy
msgid "Clear Log View"
msgstr "Rens logfil"

#: pynicotine/gtkgui/mainwindow.py:1199
#, fuzzy
msgid "Preparing Shares"
msgstr "Scanning Shares"

#: pynicotine/gtkgui/mainwindow.py:1210
#, fuzzy
msgid "Scanning Shares"
msgstr "Scanning Shares"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
#, fuzzy
msgid "Shared Folders"
msgstr "Delede mapper"

#: pynicotine/gtkgui/popovers/chathistory.py:77
#, fuzzy
msgid "Latest Message"
msgstr "Send meddelelse"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Rum"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Bruger"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
#, fuzzy
msgid "Join Room"
msgstr "Deltag i rummet"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
#, fuzzy
msgid "Leave Room"
msgstr "Forlad plads"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
#, fuzzy
msgid "Disown Private Room"
msgstr "Afslået privat værelse"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
#, fuzzy
msgid "Cancel Room Membership"
msgstr "Annuller værelsesmedlemskab"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
#, fuzzy
msgid "Close All Tabs…"
msgstr "Luk alle faner…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
#, fuzzy
msgid "_Close Tab"
msgstr "Luk"

#: pynicotine/gtkgui/privatechat.py:379
#, fuzzy
msgid "View Chat Log"
msgstr "Vis chatlog"

#: pynicotine/gtkgui/privatechat.py:382
#, fuzzy
msgid "Delete Chat Log…"
msgstr "Slet chatlog…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
#, fuzzy
msgid "User Actions"
msgstr "Overførsler"

#: pynicotine/gtkgui/privatechat.py:477
#, fuzzy
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr "Vil du slette alle gemte meddelelser permanent for denne bruger?"

#: pynicotine/gtkgui/privatechat.py:528
#, fuzzy
msgid "* Messages sent while you were offline"
msgstr ""
"* Meddelelser, der blev sendt, mens du var offline. Tidsstempler rapporteres "
"af serveren og kan være slukket."

#: pynicotine/gtkgui/search.py:90
#, fuzzy
msgid "_Global"
msgstr "Global"

#: pynicotine/gtkgui/search.py:91
#, fuzzy
msgid "_Buddies"
msgstr "Venner"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
#, fuzzy
msgid "_Rooms"
msgstr "Rum"

#: pynicotine/gtkgui/search.py:93
#, fuzzy
msgid "_User"
msgstr "Bruger"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
#, fuzzy
msgid "In Queue"
msgstr "I kø"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
#, fuzzy
msgid "File Type"
msgstr "Filtype"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Filnavn"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Størrelse"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
#, fuzzy
msgid "Quality"
msgstr "Kvalitet"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
#, fuzzy
msgid "Copy _File Path"
msgstr "Kopier _File sti"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "Kopier _URL"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
#, fuzzy
msgid "Copy Folder U_RL"
msgstr "Kopier mappens URL"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
#, fuzzy
msgid "_Download File(s)"
msgstr "_Hent fil(er)"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
#, fuzzy
msgid "Download File(s) _To…"
msgstr "Hent fil(er) _til..."

#: pynicotine/gtkgui/search.py:615
#, fuzzy
msgid "Download _Folder(s)"
msgstr "_Hent fil(er)"

#: pynicotine/gtkgui/search.py:616
#, fuzzy
msgid "Download F_older(s) To…"
msgstr "Hent fil(er) _til..."

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
#, fuzzy
msgid "View User _Profile"
msgstr "Vis bruger _profil"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
#, fuzzy
msgid "_Browse Folder"
msgstr "Kig i filer"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
#, fuzzy
msgid "F_ile Properties"
msgstr "egenskaber for F_ile"

#: pynicotine/gtkgui/search.py:629
#, fuzzy
msgid "Copy Search Term"
msgstr "Kopier søgeord"

#: pynicotine/gtkgui/search.py:631
#, fuzzy
msgid "Clear All Results"
msgstr "Ryd alle resultater"

#: pynicotine/gtkgui/search.py:718
#, fuzzy
msgid "Clear Filters"
msgstr "Ryd filterhistorik"

#: pynicotine/gtkgui/search.py:721
#, fuzzy
msgid "Restore Filters"
msgstr "Aktivere filtere"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, fuzzy, python-format
msgid "[PRIVATE]  %s"
msgstr "[PRIVAT]"

#: pynicotine/gtkgui/search.py:1273
#, fuzzy, python-format
msgid "_Result Filters [%d]"
msgstr "_Result filtre [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
#, fuzzy
msgid "_Result Filters"
msgstr "Aktivere filtere"

#: pynicotine/gtkgui/search.py:1277
#, fuzzy, python-format
msgid "%d active filter(s)"
msgstr "Ryd alle aktive filtre"

#: pynicotine/gtkgui/search.py:1329
#, fuzzy
msgid "Add Wi_sh"
msgstr "Tilføj Wi_sh"

#: pynicotine/gtkgui/search.py:1332
#, fuzzy
msgid "Remove Wi_sh"
msgstr "Fjern et alias"

#: pynicotine/gtkgui/search.py:1349
#, fuzzy
msgid "Select User's Results"
msgstr "Vælg brugeroverførsler"

#: pynicotine/gtkgui/search.py:1472
#, fuzzy, python-format
msgid "Total: %s"
msgstr "Total"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
#, fuzzy
msgid "Results"
msgstr "Resultater"

#: pynicotine/gtkgui/search.py:1590
#, fuzzy
msgid "Select Destination Folder for File(s)"
msgstr "Vælg destination for download af fil(er) fra bruger"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
#, fuzzy
msgid "Select Destination Folder"
msgstr "Vælg en mappe"

#: pynicotine/gtkgui/transfers.py:61
#, fuzzy
msgid "Queued"
msgstr "Kø"

#: pynicotine/gtkgui/transfers.py:62
#, fuzzy
msgid "Queued (prioritized)"
msgstr "prioriteret"

#: pynicotine/gtkgui/transfers.py:63
#, fuzzy
msgid "Queued (privileged)"
msgstr "(priveligieret)"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Henter status"

#: pynicotine/gtkgui/transfers.py:65
#, fuzzy
msgid "Transferring"
msgstr "Overføre"

#: pynicotine/gtkgui/transfers.py:66
#, fuzzy
msgid "Connection closed"
msgstr "Forbindelsen lukket av anden bruger"

#: pynicotine/gtkgui/transfers.py:67
#, fuzzy
msgid "Connection timeout"
msgstr "Tilslutter"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Bruger logget ud"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
#, fuzzy
msgid "Cancelled"
msgstr "Aflyse"

#: pynicotine/gtkgui/transfers.py:73
#, fuzzy
msgid "Download folder error"
msgstr "Fejl på downloadsbibliotek"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Lokalt filfejl"

#: pynicotine/gtkgui/transfers.py:75
#, fuzzy
msgid "Banned"
msgstr "Forbudt"

#: pynicotine/gtkgui/transfers.py:76
#, fuzzy
msgid "File not shared"
msgstr "Filen er ikke delt"

#: pynicotine/gtkgui/transfers.py:77
#, fuzzy
msgid "Pending shutdown"
msgstr "Afventer lukning"

#: pynicotine/gtkgui/transfers.py:78
#, fuzzy
msgid "File read error"
msgstr "Overførsler"

#: pynicotine/gtkgui/transfers.py:182
#, fuzzy
msgid "Queue"
msgstr "Kø"

#: pynicotine/gtkgui/transfers.py:188
#, fuzzy
msgid "Percent"
msgstr "Procent"

#: pynicotine/gtkgui/transfers.py:208
#, fuzzy
msgid "Time Elapsed"
msgstr "Forløben tid"

#: pynicotine/gtkgui/transfers.py:215
#, fuzzy
msgid "Time Left"
msgstr "Tid tilbage"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
#, fuzzy
msgid "_Open File"
msgstr "_Open liste"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
#, fuzzy
msgid "Open in File _Manager"
msgstr "Åbn i _Manager"

#: pynicotine/gtkgui/transfers.py:286
#, fuzzy
msgid "_Search"
msgstr "Søg"

#: pynicotine/gtkgui/transfers.py:289
#, fuzzy
msgid "Clear All"
msgstr "Rens logfil"

#: pynicotine/gtkgui/transfers.py:953
#, fuzzy
msgid "Select User's Transfers"
msgstr "Vælg brugeroverførsler"

#: pynicotine/gtkgui/uploads.py:50
#, fuzzy
msgid "_Abort"
msgstr "Afbryd"

#: pynicotine/gtkgui/uploads.py:74
#, fuzzy
msgid "Finished / Cancelled / Failed"
msgstr "Fuldført / Afbrudt / Mislykkedes"

#: pynicotine/gtkgui/uploads.py:75
#, fuzzy
msgid "Finished / Cancelled"
msgstr "Færdig/filtreret"

#: pynicotine/gtkgui/uploads.py:79
#, fuzzy
msgid "Failed"
msgstr "Mislykkedes"

#: pynicotine/gtkgui/uploads.py:80
#, fuzzy
msgid "User Logged Off"
msgstr "Bruger logget ud"

#: pynicotine/gtkgui/uploads.py:142
#, fuzzy, python-format
msgid "Uploads: %(speed)s"
msgstr "Uploads: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr ""

#: pynicotine/gtkgui/uploads.py:164
#, fuzzy
msgid "Clear Queued Uploads"
msgstr "Ryd uploads i kø"

#: pynicotine/gtkgui/uploads.py:165
#, fuzzy
msgid "Do you really want to clear all queued uploads?"
msgstr "Vil du virkelig rydde alle uploads i kø?"

#: pynicotine/gtkgui/uploads.py:177
#, fuzzy
msgid "Clear All Uploads"
msgstr "Rens logfil"

#: pynicotine/gtkgui/uploads.py:178
#, fuzzy
msgid "Do you really want to clear all uploads?"
msgstr "Vil du virkelig rydde alle uploads?"

#: pynicotine/gtkgui/userbrowse.py:282
#, fuzzy
msgid "_Save Shares List to Disk"
msgstr "_Save-shares-liste til disk"

#: pynicotine/gtkgui/userbrowse.py:292
#, fuzzy
msgid "Upload Folder & Subfolders…"
msgstr "Upload mappe (med undermapper) til bruger"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
#, fuzzy
msgid "Copy _Folder Path"
msgstr "Kopier mappens URL"

#: pynicotine/gtkgui/userbrowse.py:309
#, fuzzy
msgid "_Download Folder & Subfolders"
msgstr "_Hent fil(er)"

#: pynicotine/gtkgui/userbrowse.py:310
#, fuzzy
msgid "Download Folder & Subfolders _To…"
msgstr "Hent fil(er) _til..."

#: pynicotine/gtkgui/userbrowse.py:334
#, fuzzy
msgid "File Name"
msgstr "Filnavn"

#: pynicotine/gtkgui/userbrowse.py:373
#, fuzzy
msgid "Up_load File(s)…"
msgstr "Hent fi_l(er)"

#: pynicotine/gtkgui/userbrowse.py:374
#, fuzzy
msgid "Upload Folder…"
msgstr "Overfør mappe til…"

#: pynicotine/gtkgui/userbrowse.py:396
#, fuzzy
msgid "Download Folder _To…"
msgstr "Hent Mappe _til..."

#: pynicotine/gtkgui/userbrowse.py:600
#, fuzzy
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"Brugerens liste over delte filer er tom. Enten deler brugeren ikke noget, "
"eller de deler filer privat."

#: pynicotine/gtkgui/userbrowse.py:615
#, fuzzy
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Der kan ikke anmodes om delte filer fra brugeren. Enten er brugeren offline, "
"I har begge en lukket lytteport, eller også er der et midlertidigt "
"forbindelsesproblem."

#: pynicotine/gtkgui/userbrowse.py:953
#, fuzzy
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Vælg destination for download af en mappe fra bruger"

#: pynicotine/gtkgui/userbrowse.py:997
#, fuzzy
msgid "Upload Folder (with Subfolders) To User"
msgstr "Upload mappe (med undermapper) til bruger"

#: pynicotine/gtkgui/userbrowse.py:999
#, fuzzy
msgid "Upload Folder To User"
msgstr "Overfør mappe til…"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
#, fuzzy
msgid "Enter the name of the user you want to upload to:"
msgstr "Angiv navnet på den bruger, du vil overføre til:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
#, fuzzy
msgid "_Upload"
msgstr "_Uploads"

#: pynicotine/gtkgui/userbrowse.py:1139
#, fuzzy
msgid "Select Destination Folder for Files"
msgstr "Vælg destination for download af fil(er) fra bruger"

#: pynicotine/gtkgui/userbrowse.py:1161
#, fuzzy
msgid "Upload File(s) To User"
msgstr "Overfør filer"

#: pynicotine/gtkgui/userinfo.py:376
#, fuzzy
msgid "Copy Picture"
msgstr "Billede:"

#: pynicotine/gtkgui/userinfo.py:377
#, fuzzy
msgid "Save Picture"
msgstr "Gem billede"

#: pynicotine/gtkgui/userinfo.py:468
#, fuzzy, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Billedet for brugeren %(user)s kunne ikke indlæses: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
#, fuzzy
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Der kan ikke anmodes om oplysninger fra brugeren. Enten har I begge en "
"lukket lytteport, brugeren er offline, eller også er der et midlertidigt "
"forbindelsesproblem."

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Remove _Buddy"
msgstr "Fjern et alias"

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Add _Buddy"
msgstr "Tilføj ven…"

#: pynicotine/gtkgui/userinfo.py:581
#, fuzzy
msgid "Unban User"
msgstr "Forbyd bruger"

#: pynicotine/gtkgui/userinfo.py:585
#, fuzzy
msgid "Unignore User"
msgstr "Ignorer bruger"

#: pynicotine/gtkgui/userinfo.py:613
#, fuzzy
msgid "Yes"
msgstr "Ja"

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr "N"

#: pynicotine/gtkgui/userinfo.py:773
#, fuzzy
msgid "Please enter number of days."
msgstr "Angiv et helt tal!"

#: pynicotine/gtkgui/userinfo.py:787
#, fuzzy, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Giv dage af dine Soulseek-privilegier til brugeren %(user)s (%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, fuzzy, python-format
msgid "%(days)s days left"
msgstr "%(days)s dage tilbage"

#: pynicotine/gtkgui/userinfo.py:795
#, fuzzy
msgid "Gift Privileges"
msgstr "indrømme privilegier"

#: pynicotine/gtkgui/userinfo.py:797
#, fuzzy
msgid "_Give Privileges"
msgstr "indrømme privilegier"

#: pynicotine/gtkgui/widgets/dialogs.py:309
#, fuzzy
msgid "Close"
msgstr "Luk"

#: pynicotine/gtkgui/widgets/dialogs.py:488
#, fuzzy
msgid "_Yes"
msgstr "_Ja"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
#, fuzzy
msgid "_OK"
msgstr "OK"

#: pynicotine/gtkgui/widgets/filechooser.py:39
#, fuzzy
msgid "Select a File"
msgstr "_Søg filer"

#: pynicotine/gtkgui/widgets/filechooser.py:174
#, fuzzy
msgid "Select a Folder"
msgstr "Vælg en mappe"

#: pynicotine/gtkgui/widgets/filechooser.py:179
#, fuzzy
msgid "_Select"
msgstr "Vælg Alle"

#: pynicotine/gtkgui/widgets/filechooser.py:196
#, fuzzy
msgid "Select an Image"
msgstr "Vælg et billede"

#: pynicotine/gtkgui/widgets/filechooser.py:203
#, fuzzy
msgid "All images"
msgstr "Alle billeder"

#: pynicotine/gtkgui/widgets/filechooser.py:241
#, fuzzy
msgid "Save as…"
msgstr "Gem som…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
#, fuzzy
msgid "(None)"
msgstr "(Ingen)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
#, fuzzy
msgid "Close Tab"
msgstr "Luk"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
#, fuzzy
msgid "Close All Tabs?"
msgstr "Lukke alle faner?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
#, fuzzy
msgid "Do you really want to close all tabs?"
msgstr "Vil du virkelig lukke alle faner?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, fuzzy, python-format
msgid "%i Unread Tab(s)"
msgstr "Ulæste faner"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
#, fuzzy
msgid "All Tabs"
msgstr "Lukke alle faner?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
#, fuzzy
msgid "Re_open Closed Tab"
msgstr "Luk"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, fuzzy, python-format
msgid "%s File(s) Selected"
msgstr "%s Filer er markeret"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
#, fuzzy
msgid "_Browse Files"
msgstr "Kig i filer"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
#, fuzzy
msgid "_Add Buddy"
msgstr "Tilføj ven…"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
#, fuzzy
msgid "Show IP A_ddress"
msgstr "Vis IP-a_dresse"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
#, fuzzy
msgid "Private Rooms"
msgstr "_Chattrum"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, fuzzy, python-format
msgid "Remove from Private Room %s"
msgstr "Fjern fra privat rum %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, fuzzy, python-format
msgid "Add to Private Room %s"
msgstr "Føj til privatrum %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, fuzzy, python-format
msgid "Remove as Operator of %s"
msgstr "Fjern som operatør af %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, fuzzy, python-format
msgid "Add as Operator of %s"
msgstr "Tilføj som operatør af %s"

#: pynicotine/gtkgui/widgets/textentry.py:37
#, fuzzy
msgid "Send message…"
msgstr "Send meddelelse"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
#, fuzzy
msgid "Find Previous Match"
msgstr "Find forrige match"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
#, fuzzy
msgid "Find Next Match"
msgstr "Find næste match"

#: pynicotine/gtkgui/widgets/textview.py:443
#, fuzzy
msgid "--- old messages above ---"
msgstr "--- gamle beskeder over ---"

#: pynicotine/gtkgui/widgets/theme.py:243
#, fuzzy
msgid "Executable"
msgstr "Udført: %s"

#: pynicotine/gtkgui/widgets/theme.py:244
#, fuzzy
msgid "Audio"
msgstr "Lyd"

#: pynicotine/gtkgui/widgets/theme.py:245
#, fuzzy
msgid "Image"
msgstr "Image:"

#: pynicotine/gtkgui/widgets/theme.py:246
#, fuzzy
msgid "Archive"
msgstr "Arkiv"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
#, fuzzy
msgid "Miscellaneous"
msgstr "Diverse"

#: pynicotine/gtkgui/widgets/theme.py:248
#, fuzzy
msgid "Video"
msgstr "Video"

#: pynicotine/gtkgui/widgets/theme.py:249
#, fuzzy
msgid "Document"
msgstr "Dokument/tekst"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:357
#, fuzzy, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr ""
"Der opstod en fejl under indlæsning af det brugerdefinerede ikon %(path)s: "
"%(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
#, fuzzy
msgid "Hide Nicotine+"
msgstr "Nicotine+"

#: pynicotine/gtkgui/widgets/trayicon.py:114
#, fuzzy
msgid "Show Nicotine+"
msgstr "Nicotine+"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, fuzzy, python-format
msgid "Column #%i"
msgstr "Kolonne #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
#, fuzzy
msgid "Ungrouped"
msgstr "Grupperet"

#: pynicotine/gtkgui/widgets/treeview.py:960
#, fuzzy
msgid "Group by Folder"
msgstr "Kopier mappens URL"

#: pynicotine/gtkgui/widgets/treeview.py:963
#, fuzzy
msgid "Group by User"
msgstr "Grupper efter bruger"

#: pynicotine/headless/application.py:77
#, fuzzy, python-format
msgid "Do you really want to exit? %s"
msgstr "Har du virkelig lyst til at afslutte Nicotine+?"

#: pynicotine/headless/application.py:81
#, fuzzy, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr ""
"Brugeren %s findes allerede, og den indtastede adgangskode er ugyldig. Vælg "
"et andet brugernavn, hvis det er første gang, du logger på."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr ""

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
#, fuzzy
msgid "Password: "
msgstr "Kodeord"

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
#, fuzzy
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Hvis du vil oprette en ny Soulseek-konto, skal du udfylde det ønskede "
"brugernavn og din ønskede adgangskode. Hvis du allerede har en konto, skal "
"du udfylde dine eksisterende loginoplysninger."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr ""

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr ""

#: pynicotine/logfacility.py:181
#, fuzzy, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr "Kunne ikke skrive til logfilen \"%(filename)s\": %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, fuzzy, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Filen %(path)s kan ikke gemmes: %(error)s"

#: pynicotine/networkfilter.py:40
#, fuzzy
msgid "Andorra"
msgstr "Andorra"

#: pynicotine/networkfilter.py:41
#, fuzzy
msgid "United Arab Emirates"
msgstr "De Forenede Arabiske Emirater"

#: pynicotine/networkfilter.py:42
#, fuzzy
msgid "Afghanistan"
msgstr "Afghanistan"

#: pynicotine/networkfilter.py:43
#, fuzzy
msgid "Antigua & Barbuda"
msgstr "Antigua &Barbuda"

#: pynicotine/networkfilter.py:44
#, fuzzy
msgid "Anguilla"
msgstr "Anguilla"

#: pynicotine/networkfilter.py:45
#, fuzzy
msgid "Albania"
msgstr "Albanien"

#: pynicotine/networkfilter.py:46
#, fuzzy
msgid "Armenia"
msgstr "Armenien"

#: pynicotine/networkfilter.py:47
#, fuzzy
msgid "Angola"
msgstr "Angola"

#: pynicotine/networkfilter.py:48
#, fuzzy
msgid "Antarctica"
msgstr "Antarktis"

#: pynicotine/networkfilter.py:49
#, fuzzy
msgid "Argentina"
msgstr "Argentina"

#: pynicotine/networkfilter.py:50
#, fuzzy
msgid "American Samoa"
msgstr "Amerikansk Samoa"

#: pynicotine/networkfilter.py:51
#, fuzzy
msgid "Austria"
msgstr "Østrig"

#: pynicotine/networkfilter.py:52
#, fuzzy
msgid "Australia"
msgstr "Australien"

#: pynicotine/networkfilter.py:53
#, fuzzy
msgid "Aruba"
msgstr "Aruba"

#: pynicotine/networkfilter.py:54
#, fuzzy
msgid "Åland Islands"
msgstr "Ålandsøerne"

#: pynicotine/networkfilter.py:55
#, fuzzy
msgid "Azerbaijan"
msgstr "Aserbajdsjan"

#: pynicotine/networkfilter.py:56
#, fuzzy
msgid "Bosnia & Herzegovina"
msgstr "Bosnien -Hercegovina"

#: pynicotine/networkfilter.py:57
#, fuzzy
msgid "Barbados"
msgstr "Barbados"

#: pynicotine/networkfilter.py:58
#, fuzzy
msgid "Bangladesh"
msgstr "Bangladesh"

#: pynicotine/networkfilter.py:59
#, fuzzy
msgid "Belgium"
msgstr "Belgien"

#: pynicotine/networkfilter.py:60
#, fuzzy
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: pynicotine/networkfilter.py:61
#, fuzzy
msgid "Bulgaria"
msgstr "Bulgarien"

#: pynicotine/networkfilter.py:62
#, fuzzy
msgid "Bahrain"
msgstr "Bahrain"

#: pynicotine/networkfilter.py:63
#, fuzzy
msgid "Burundi"
msgstr "Burundi"

#: pynicotine/networkfilter.py:64
#, fuzzy
msgid "Benin"
msgstr "Benin"

#: pynicotine/networkfilter.py:65
#, fuzzy
msgid "Saint Barthelemy"
msgstr "Saint Barthelemy"

#: pynicotine/networkfilter.py:66
#, fuzzy
msgid "Bermuda"
msgstr "Bermuda"

#: pynicotine/networkfilter.py:67
#, fuzzy
msgid "Brunei Darussalam"
msgstr "Brunei Darussalam"

#: pynicotine/networkfilter.py:68
#, fuzzy
msgid "Bolivia"
msgstr "Bolivia"

#: pynicotine/networkfilter.py:69
#, fuzzy
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius og Saba"

#: pynicotine/networkfilter.py:70
#, fuzzy
msgid "Brazil"
msgstr "Brasilien"

#: pynicotine/networkfilter.py:71
#, fuzzy
msgid "Bahamas"
msgstr "Bahamas"

#: pynicotine/networkfilter.py:72
#, fuzzy
msgid "Bhutan"
msgstr "Bhutan"

#: pynicotine/networkfilter.py:73
#, fuzzy
msgid "Bouvet Island"
msgstr "Bouvet Island"

#: pynicotine/networkfilter.py:74
#, fuzzy
msgid "Botswana"
msgstr "Botswana"

#: pynicotine/networkfilter.py:75
#, fuzzy
msgid "Belarus"
msgstr "Belarus"

#: pynicotine/networkfilter.py:76
#, fuzzy
msgid "Belize"
msgstr "Belize"

#: pynicotine/networkfilter.py:77
#, fuzzy
msgid "Canada"
msgstr "Canada"

#: pynicotine/networkfilter.py:78
#, fuzzy
msgid "Cocos (Keeling) Islands"
msgstr "Cocos (Keeling) Øer"

#: pynicotine/networkfilter.py:79
#, fuzzy
msgid "Democratic Republic of Congo"
msgstr "Den Demokratiske Republik Congo"

#: pynicotine/networkfilter.py:80
#, fuzzy
msgid "Central African Republic"
msgstr "Den Centralafrikanske Republik"

#: pynicotine/networkfilter.py:81
#, fuzzy
msgid "Congo"
msgstr "Republikken Congo"

#: pynicotine/networkfilter.py:82
#, fuzzy
msgid "Switzerland"
msgstr "Schweiz"

#: pynicotine/networkfilter.py:83
#, fuzzy
msgid "Ivory Coast"
msgstr "Elfenbenskysten"

#: pynicotine/networkfilter.py:84
#, fuzzy
msgid "Cook Islands"
msgstr "Cookøerne"

#: pynicotine/networkfilter.py:85
#, fuzzy
msgid "Chile"
msgstr "Chile"

#: pynicotine/networkfilter.py:86
#, fuzzy
msgid "Cameroon"
msgstr "Cameroun"

#: pynicotine/networkfilter.py:87
#, fuzzy
msgid "China"
msgstr "Kina"

#: pynicotine/networkfilter.py:88
#, fuzzy
msgid "Colombia"
msgstr "Colombia"

#: pynicotine/networkfilter.py:89
#, fuzzy
msgid "Costa Rica"
msgstr "Costa Rica"

#: pynicotine/networkfilter.py:90
#, fuzzy
msgid "Cuba"
msgstr "Cuba"

#: pynicotine/networkfilter.py:91
#, fuzzy
msgid "Cabo Verde"
msgstr "Cabo Verde"

#: pynicotine/networkfilter.py:92
#, fuzzy
msgid "Curaçao"
msgstr "Curaçao"

#: pynicotine/networkfilter.py:93
#, fuzzy
msgid "Christmas Island"
msgstr "Juleøen"

#: pynicotine/networkfilter.py:94
#, fuzzy
msgid "Cyprus"
msgstr "Cypern"

#: pynicotine/networkfilter.py:95
#, fuzzy
msgid "Czechia"
msgstr "Tjekkiet"

#: pynicotine/networkfilter.py:96
#, fuzzy
msgid "Germany"
msgstr "Tyskland"

#: pynicotine/networkfilter.py:97
#, fuzzy
msgid "Djibouti"
msgstr "Djibouti"

#: pynicotine/networkfilter.py:98
#, fuzzy
msgid "Denmark"
msgstr "Danmark"

#: pynicotine/networkfilter.py:99
#, fuzzy
msgid "Dominica"
msgstr "Dominica"

#: pynicotine/networkfilter.py:100
#, fuzzy
msgid "Dominican Republic"
msgstr "Dominikanske Republik"

#: pynicotine/networkfilter.py:101
#, fuzzy
msgid "Algeria"
msgstr "Algeriet"

#: pynicotine/networkfilter.py:102
#, fuzzy
msgid "Ecuador"
msgstr "Ecuador"

#: pynicotine/networkfilter.py:103
#, fuzzy
msgid "Estonia"
msgstr "Estland"

#: pynicotine/networkfilter.py:104
#, fuzzy
msgid "Egypt"
msgstr "Egypten"

#: pynicotine/networkfilter.py:105
#, fuzzy
msgid "Western Sahara"
msgstr "Vestsahara"

#: pynicotine/networkfilter.py:106
#, fuzzy
msgid "Eritrea"
msgstr "Eritrea"

#: pynicotine/networkfilter.py:107
#, fuzzy
msgid "Spain"
msgstr "Spanien"

#: pynicotine/networkfilter.py:108
#, fuzzy
msgid "Ethiopia"
msgstr "Etiopien"

#: pynicotine/networkfilter.py:109
#, fuzzy
msgid "Europe"
msgstr "Europa"

#: pynicotine/networkfilter.py:110
#, fuzzy
msgid "Finland"
msgstr "Finland"

#: pynicotine/networkfilter.py:111
#, fuzzy
msgid "Fiji"
msgstr "Fiji"

#: pynicotine/networkfilter.py:112
#, fuzzy
msgid "Falkland Islands (Malvinas)"
msgstr "Falklandsøerne (Malvinas)"

#: pynicotine/networkfilter.py:113
#, fuzzy
msgid "Micronesia"
msgstr "Mikronesien"

#: pynicotine/networkfilter.py:114
#, fuzzy
msgid "Faroe Islands"
msgstr "Færøerne"

#: pynicotine/networkfilter.py:115
#, fuzzy
msgid "France"
msgstr "Frankrig"

#: pynicotine/networkfilter.py:116
#, fuzzy
msgid "Gabon"
msgstr "Gabon"

#: pynicotine/networkfilter.py:117
#, fuzzy
msgid "Great Britain"
msgstr "Storbritannien"

#: pynicotine/networkfilter.py:118
#, fuzzy
msgid "Grenada"
msgstr "Grenada"

#: pynicotine/networkfilter.py:119
#, fuzzy
msgid "Georgia"
msgstr "Georgien"

#: pynicotine/networkfilter.py:120
#, fuzzy
msgid "French Guiana"
msgstr "Fransk Guyana"

#: pynicotine/networkfilter.py:121
#, fuzzy
msgid "Guernsey"
msgstr "Guernsey"

#: pynicotine/networkfilter.py:122
#, fuzzy
msgid "Ghana"
msgstr "Ghana"

#: pynicotine/networkfilter.py:123
#, fuzzy
msgid "Gibraltar"
msgstr "Gibraltar"

#: pynicotine/networkfilter.py:124
#, fuzzy
msgid "Greenland"
msgstr "Grønland"

#: pynicotine/networkfilter.py:125
#, fuzzy
msgid "Gambia"
msgstr "Gambia"

#: pynicotine/networkfilter.py:126
#, fuzzy
msgid "Guinea"
msgstr "Guinea"

#: pynicotine/networkfilter.py:127
#, fuzzy
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: pynicotine/networkfilter.py:128
#, fuzzy
msgid "Equatorial Guinea"
msgstr "Ækvatorialguinea"

#: pynicotine/networkfilter.py:129
#, fuzzy
msgid "Greece"
msgstr "Grækenland"

#: pynicotine/networkfilter.py:130
#, fuzzy
msgid "South Georgia & South Sandwich Islands"
msgstr "South Georgia &South Sandwich Islands"

#: pynicotine/networkfilter.py:131
#, fuzzy
msgid "Guatemala"
msgstr "Guatemala"

#: pynicotine/networkfilter.py:132
#, fuzzy
msgid "Guam"
msgstr "Guam"

#: pynicotine/networkfilter.py:133
#, fuzzy
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: pynicotine/networkfilter.py:134
#, fuzzy
msgid "Guyana"
msgstr "Guyana"

#: pynicotine/networkfilter.py:135
#, fuzzy
msgid "Hong Kong"
msgstr "Hongkong"

#: pynicotine/networkfilter.py:136
#, fuzzy
msgid "Heard & McDonald Islands"
msgstr "Heard &McDonald Islands"

#: pynicotine/networkfilter.py:137
#, fuzzy
msgid "Honduras"
msgstr "Honduras"

#: pynicotine/networkfilter.py:138
#, fuzzy
msgid "Croatia"
msgstr "Kroatien"

#: pynicotine/networkfilter.py:139
#, fuzzy
msgid "Haiti"
msgstr "Haiti"

#: pynicotine/networkfilter.py:140
#, fuzzy
msgid "Hungary"
msgstr "Ungarn"

#: pynicotine/networkfilter.py:141
#, fuzzy
msgid "Indonesia"
msgstr "Indonesien"

#: pynicotine/networkfilter.py:142
#, fuzzy
msgid "Ireland"
msgstr "Irland"

#: pynicotine/networkfilter.py:143
#, fuzzy
msgid "Israel"
msgstr "Israel"

#: pynicotine/networkfilter.py:144
#, fuzzy
msgid "Isle of Man"
msgstr "Isle of Man"

#: pynicotine/networkfilter.py:145
#, fuzzy
msgid "India"
msgstr "Indien"

#: pynicotine/networkfilter.py:146
#, fuzzy
msgid "British Indian Ocean Territory"
msgstr "Britisk territorium i Det Indiske Ocean"

#: pynicotine/networkfilter.py:147
#, fuzzy
msgid "Iraq"
msgstr "Irak"

#: pynicotine/networkfilter.py:148
#, fuzzy
msgid "Iran"
msgstr "Iran"

#: pynicotine/networkfilter.py:149
#, fuzzy
msgid "Iceland"
msgstr "Island"

#: pynicotine/networkfilter.py:150
#, fuzzy
msgid "Italy"
msgstr "Italien"

#: pynicotine/networkfilter.py:151
#, fuzzy
msgid "Jersey"
msgstr "Jersey"

#: pynicotine/networkfilter.py:152
#, fuzzy
msgid "Jamaica"
msgstr "Jamaica"

#: pynicotine/networkfilter.py:153
#, fuzzy
msgid "Jordan"
msgstr "Jordan"

#: pynicotine/networkfilter.py:154
#, fuzzy
msgid "Japan"
msgstr "Japan"

#: pynicotine/networkfilter.py:155
#, fuzzy
msgid "Kenya"
msgstr "Kenya"

#: pynicotine/networkfilter.py:156
#, fuzzy
msgid "Kyrgyzstan"
msgstr "Kirgisistan"

#: pynicotine/networkfilter.py:157
#, fuzzy
msgid "Cambodia"
msgstr "Cambodja"

#: pynicotine/networkfilter.py:158
#, fuzzy
msgid "Kiribati"
msgstr "Kiribati"

#: pynicotine/networkfilter.py:159
#, fuzzy
msgid "Comoros"
msgstr "Comorerne"

#: pynicotine/networkfilter.py:160
#, fuzzy
msgid "Saint Kitts & Nevis"
msgstr "Saint Kitts &Nevis"

#: pynicotine/networkfilter.py:161
#, fuzzy
msgid "North Korea"
msgstr "Nordkorea"

#: pynicotine/networkfilter.py:162
#, fuzzy
msgid "South Korea"
msgstr "Sydkorea"

#: pynicotine/networkfilter.py:163
#, fuzzy
msgid "Kuwait"
msgstr "Kuwait"

#: pynicotine/networkfilter.py:164
#, fuzzy
msgid "Cayman Islands"
msgstr "Caymanøerne"

#: pynicotine/networkfilter.py:165
#, fuzzy
msgid "Kazakhstan"
msgstr "Kasakhstan"

#: pynicotine/networkfilter.py:166
#, fuzzy
msgid "Laos"
msgstr "Laos"

#: pynicotine/networkfilter.py:167
#, fuzzy
msgid "Lebanon"
msgstr "Libanon"

#: pynicotine/networkfilter.py:168
#, fuzzy
msgid "Saint Lucia"
msgstr "Saint Lucia"

#: pynicotine/networkfilter.py:169
#, fuzzy
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: pynicotine/networkfilter.py:170
#, fuzzy
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: pynicotine/networkfilter.py:171
#, fuzzy
msgid "Liberia"
msgstr "Liberia"

#: pynicotine/networkfilter.py:172
#, fuzzy
msgid "Lesotho"
msgstr "Lesotho"

#: pynicotine/networkfilter.py:173
#, fuzzy
msgid "Lithuania"
msgstr "Litauen"

#: pynicotine/networkfilter.py:174
#, fuzzy
msgid "Luxembourg"
msgstr "Luxembourg"

#: pynicotine/networkfilter.py:175
#, fuzzy
msgid "Latvia"
msgstr "Letland"

#: pynicotine/networkfilter.py:176
#, fuzzy
msgid "Libya"
msgstr "Libyen"

#: pynicotine/networkfilter.py:177
#, fuzzy
msgid "Morocco"
msgstr "Marokko"

#: pynicotine/networkfilter.py:178
#, fuzzy
msgid "Monaco"
msgstr "Monaco"

#: pynicotine/networkfilter.py:179
#, fuzzy
msgid "Moldova"
msgstr "Moldova"

#: pynicotine/networkfilter.py:180
#, fuzzy
msgid "Montenegro"
msgstr "Montenegro"

#: pynicotine/networkfilter.py:181
#, fuzzy
msgid "Saint Martin"
msgstr "Sankt Martin"

#: pynicotine/networkfilter.py:182
#, fuzzy
msgid "Madagascar"
msgstr "Madagaskar"

#: pynicotine/networkfilter.py:183
#, fuzzy
msgid "Marshall Islands"
msgstr "Marshalløerne"

#: pynicotine/networkfilter.py:184
#, fuzzy
msgid "North Macedonia"
msgstr "Nordmakedonien"

#: pynicotine/networkfilter.py:185
#, fuzzy
msgid "Mali"
msgstr "Mali"

#: pynicotine/networkfilter.py:186
#, fuzzy
msgid "Myanmar"
msgstr "Myanmar"

#: pynicotine/networkfilter.py:187
#, fuzzy
msgid "Mongolia"
msgstr "Mongoliet"

#: pynicotine/networkfilter.py:188
#, fuzzy
msgid "Macau"
msgstr "Macao"

#: pynicotine/networkfilter.py:189
#, fuzzy
msgid "Northern Mariana Islands"
msgstr "Nordmarianerne"

#: pynicotine/networkfilter.py:190
#, fuzzy
msgid "Martinique"
msgstr "Martinique"

#: pynicotine/networkfilter.py:191
#, fuzzy
msgid "Mauritania"
msgstr "Mauretanien"

#: pynicotine/networkfilter.py:192
#, fuzzy
msgid "Montserrat"
msgstr "Montserrat"

#: pynicotine/networkfilter.py:193
#, fuzzy
msgid "Malta"
msgstr "Malta"

#: pynicotine/networkfilter.py:194
#, fuzzy
msgid "Mauritius"
msgstr "Mauritius"

#: pynicotine/networkfilter.py:195
#, fuzzy
msgid "Maldives"
msgstr "Maldiverne"

#: pynicotine/networkfilter.py:196
#, fuzzy
msgid "Malawi"
msgstr "Malawi"

#: pynicotine/networkfilter.py:197
#, fuzzy
msgid "Mexico"
msgstr "Mexico"

#: pynicotine/networkfilter.py:198
#, fuzzy
msgid "Malaysia"
msgstr "Malaysia"

#: pynicotine/networkfilter.py:199
#, fuzzy
msgid "Mozambique"
msgstr "Mozambique"

#: pynicotine/networkfilter.py:200
#, fuzzy
msgid "Namibia"
msgstr "Namibia"

#: pynicotine/networkfilter.py:201
#, fuzzy
msgid "New Caledonia"
msgstr "Ny Kaledonien"

#: pynicotine/networkfilter.py:202
#, fuzzy
msgid "Niger"
msgstr "Niger"

#: pynicotine/networkfilter.py:203
#, fuzzy
msgid "Norfolk Island"
msgstr "Norfolk Island"

#: pynicotine/networkfilter.py:204
#, fuzzy
msgid "Nigeria"
msgstr "Nigeria"

#: pynicotine/networkfilter.py:205
#, fuzzy
msgid "Nicaragua"
msgstr "Nicaragua"

#: pynicotine/networkfilter.py:206
#, fuzzy
msgid "Netherlands"
msgstr "Holland"

#: pynicotine/networkfilter.py:207
#, fuzzy
msgid "Norway"
msgstr "Norge"

#: pynicotine/networkfilter.py:208
#, fuzzy
msgid "Nepal"
msgstr "Nepal"

#: pynicotine/networkfilter.py:209
#, fuzzy
msgid "Nauru"
msgstr "Nauru"

#: pynicotine/networkfilter.py:210
#, fuzzy
msgid "Niue"
msgstr "Niue"

#: pynicotine/networkfilter.py:211
#, fuzzy
msgid "New Zealand"
msgstr "New Zealand"

#: pynicotine/networkfilter.py:212
#, fuzzy
msgid "Oman"
msgstr "Oman"

#: pynicotine/networkfilter.py:213
#, fuzzy
msgid "Panama"
msgstr "Panama"

#: pynicotine/networkfilter.py:214
#, fuzzy
msgid "Peru"
msgstr "Peru"

#: pynicotine/networkfilter.py:215
#, fuzzy
msgid "French Polynesia"
msgstr "Fransk Polynesien"

#: pynicotine/networkfilter.py:216
#, fuzzy
msgid "Papua New Guinea"
msgstr "Papua Ny Guinea"

#: pynicotine/networkfilter.py:217
#, fuzzy
msgid "Philippines"
msgstr "Filippinerne"

#: pynicotine/networkfilter.py:218
#, fuzzy
msgid "Pakistan"
msgstr "Pakistan"

#: pynicotine/networkfilter.py:219
#, fuzzy
msgid "Poland"
msgstr "Polen"

#: pynicotine/networkfilter.py:220
#, fuzzy
msgid "Saint Pierre & Miquelon"
msgstr "Sankt Pierre &Miquelon"

#: pynicotine/networkfilter.py:221
#, fuzzy
msgid "Pitcairn"
msgstr "Pitcairn"

#: pynicotine/networkfilter.py:222
#, fuzzy
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: pynicotine/networkfilter.py:223
#, fuzzy
msgid "State of Palestine"
msgstr "Staten Palæstina"

#: pynicotine/networkfilter.py:224
#, fuzzy
msgid "Portugal"
msgstr "Portugal"

#: pynicotine/networkfilter.py:225
#, fuzzy
msgid "Palau"
msgstr "Palau"

#: pynicotine/networkfilter.py:226
#, fuzzy
msgid "Paraguay"
msgstr "Paraguay"

#: pynicotine/networkfilter.py:227
#, fuzzy
msgid "Qatar"
msgstr "Qatar"

#: pynicotine/networkfilter.py:228
#, fuzzy
msgid "Réunion"
msgstr "Réunion"

#: pynicotine/networkfilter.py:229
#, fuzzy
msgid "Romania"
msgstr "Rumænien"

#: pynicotine/networkfilter.py:230
#, fuzzy
msgid "Serbia"
msgstr "Serbien"

#: pynicotine/networkfilter.py:231
#, fuzzy
msgid "Russia"
msgstr "Rusland"

#: pynicotine/networkfilter.py:232
#, fuzzy
msgid "Rwanda"
msgstr "Rwanda"

#: pynicotine/networkfilter.py:233
#, fuzzy
msgid "Saudi Arabia"
msgstr "Saudi-Arabien"

#: pynicotine/networkfilter.py:234
#, fuzzy
msgid "Solomon Islands"
msgstr "Salomonøerne"

#: pynicotine/networkfilter.py:235
#, fuzzy
msgid "Seychelles"
msgstr "Seychellerne"

#: pynicotine/networkfilter.py:236
#, fuzzy
msgid "Sudan"
msgstr "Sudan"

#: pynicotine/networkfilter.py:237
#, fuzzy
msgid "Sweden"
msgstr "Sverige"

#: pynicotine/networkfilter.py:238
#, fuzzy
msgid "Singapore"
msgstr "Singapore"

#: pynicotine/networkfilter.py:239
#, fuzzy
msgid "Saint Helena"
msgstr "Sankt Helena"

#: pynicotine/networkfilter.py:240
#, fuzzy
msgid "Slovenia"
msgstr "Slovenien"

#: pynicotine/networkfilter.py:241
#, fuzzy
msgid "Svalbard & Jan Mayen Islands"
msgstr "Svalbard & Jan Mayen Islands"

#: pynicotine/networkfilter.py:242
#, fuzzy
msgid "Slovak Republic"
msgstr "Slovakiet"

#: pynicotine/networkfilter.py:243
#, fuzzy
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: pynicotine/networkfilter.py:244
#, fuzzy
msgid "San Marino"
msgstr "San Marino"

#: pynicotine/networkfilter.py:245
#, fuzzy
msgid "Senegal"
msgstr "Senegal"

#: pynicotine/networkfilter.py:246
#, fuzzy
msgid "Somalia"
msgstr "Somalia"

#: pynicotine/networkfilter.py:247
#, fuzzy
msgid "Suriname"
msgstr "Filnavn"

#: pynicotine/networkfilter.py:248
#, fuzzy
msgid "South Sudan"
msgstr "Sydsudan"

#: pynicotine/networkfilter.py:249
#, fuzzy
msgid "Sao Tome & Principe"
msgstr "Sao Tome &Principe"

#: pynicotine/networkfilter.py:250
#, fuzzy
msgid "El Salvador"
msgstr "El Salvador"

#: pynicotine/networkfilter.py:251
#, fuzzy
msgid "Sint Maarten"
msgstr "Sint Maarten"

#: pynicotine/networkfilter.py:252
#, fuzzy
msgid "Syria"
msgstr "Syrien"

#: pynicotine/networkfilter.py:253
#, fuzzy
msgid "Eswatini"
msgstr "Eswatini"

#: pynicotine/networkfilter.py:254
#, fuzzy
msgid "Turks & Caicos Islands"
msgstr "Turks & Caicosøerne"

#: pynicotine/networkfilter.py:255
#, fuzzy
msgid "Chad"
msgstr "Tchad"

#: pynicotine/networkfilter.py:256
#, fuzzy
msgid "French Southern Territories"
msgstr "Franske sydlige territorier"

#: pynicotine/networkfilter.py:257
#, fuzzy
msgid "Togo"
msgstr "Togo"

#: pynicotine/networkfilter.py:258
#, fuzzy
msgid "Thailand"
msgstr "Thailand"

#: pynicotine/networkfilter.py:259
#, fuzzy
msgid "Tajikistan"
msgstr "Tadsjikistan"

#: pynicotine/networkfilter.py:260
#, fuzzy
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
#, fuzzy
msgid "Timor-Leste"
msgstr "Timor-Leste"

#: pynicotine/networkfilter.py:262
#, fuzzy
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: pynicotine/networkfilter.py:263
#, fuzzy
msgid "Tunisia"
msgstr "Tunesien"

#: pynicotine/networkfilter.py:264
#, fuzzy
msgid "Tonga"
msgstr "Tonga"

#: pynicotine/networkfilter.py:265
#, fuzzy
msgid "Türkiye"
msgstr "Türkiye"

#: pynicotine/networkfilter.py:266
#, fuzzy
msgid "Trinidad & Tobago"
msgstr "Trinidad &Tobago"

#: pynicotine/networkfilter.py:267
#, fuzzy
msgid "Tuvalu"
msgstr "Tuvalu"

#: pynicotine/networkfilter.py:268
#, fuzzy
msgid "Taiwan"
msgstr "Taiwan"

#: pynicotine/networkfilter.py:269
#, fuzzy
msgid "Tanzania"
msgstr "Tanzania"

#: pynicotine/networkfilter.py:270
#, fuzzy
msgid "Ukraine"
msgstr "Ukraine"

#: pynicotine/networkfilter.py:271
#, fuzzy
msgid "Uganda"
msgstr "Uganda"

#: pynicotine/networkfilter.py:272
#, fuzzy
msgid "U.S. Minor Outlying Islands"
msgstr "Mindre amerikanske øer i usa"

#: pynicotine/networkfilter.py:273
#, fuzzy
msgid "United States"
msgstr "USA"

#: pynicotine/networkfilter.py:274
#, fuzzy
msgid "Uruguay"
msgstr "Uruguay"

#: pynicotine/networkfilter.py:275
#, fuzzy
msgid "Uzbekistan"
msgstr "Usbekistan"

#: pynicotine/networkfilter.py:276
#, fuzzy
msgid "Holy See (Vatican City State)"
msgstr "Pavestolen (Vatikanstaten)"

#: pynicotine/networkfilter.py:277
#, fuzzy
msgid "Saint Vincent & The Grenadines"
msgstr "Saint Vincent &Grenadinerne"

#: pynicotine/networkfilter.py:278
#, fuzzy
msgid "Venezuela"
msgstr "Venezuela"

#: pynicotine/networkfilter.py:279
#, fuzzy
msgid "British Virgin Islands"
msgstr "De Britiske Jomfruøer"

#: pynicotine/networkfilter.py:280
#, fuzzy
msgid "U.S. Virgin Islands"
msgstr "De Amerikanske Jomfruøer"

#: pynicotine/networkfilter.py:281
#, fuzzy
msgid "Viet Nam"
msgstr "Vietnam"

#: pynicotine/networkfilter.py:282
#, fuzzy
msgid "Vanuatu"
msgstr "Vanuatu"

#: pynicotine/networkfilter.py:283
#, fuzzy
msgid "Wallis & Futuna"
msgstr "Wallis &Futuna"

#: pynicotine/networkfilter.py:284
#, fuzzy
msgid "Samoa"
msgstr "Samoa"

#: pynicotine/networkfilter.py:285
#, fuzzy
msgid "Yemen"
msgstr "Yemen"

#: pynicotine/networkfilter.py:286
#, fuzzy
msgid "Mayotte"
msgstr "Mayotte"

#: pynicotine/networkfilter.py:287
#, fuzzy
msgid "South Africa"
msgstr "Sydafrika"

#: pynicotine/networkfilter.py:288
#, fuzzy
msgid "Zambia"
msgstr "Zambia"

#: pynicotine/networkfilter.py:289
#, fuzzy
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, fuzzy, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "Tekst-til-tale for meddelelsen mislykkedes: %s"

#: pynicotine/nowplaying.py:130
#, fuzzy
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr "Last.fm: Angiv både dit Last.fm brugernavn og API-nøgle"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
#, fuzzy
msgid "Now Playing Error"
msgstr "Spiller nu format"

#: pynicotine/nowplaying.py:140
#, fuzzy, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr ""
"Last.fm: Der kunne ikke oprettes forbindelse til Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:162
#, fuzzy, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr "Last.fm: Kunne ikke få det seneste spor fra Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:196
#, fuzzy
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: Der blev ikke fundet en passende MPRIS-afspiller"

#: pynicotine/nowplaying.py:201
#, fuzzy, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr "Fandt flere MPRIS-afspillere: %(players)s. Bruger: %(player)s"

#: pynicotine/nowplaying.py:204
#, fuzzy, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Automatisk registreret MPRIS-afspiller: %s"

#: pynicotine/nowplaying.py:219
#, fuzzy, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr "MPRIS: Noget gik galt, mens forespørgsel %(player)s: %(exception)s"

#: pynicotine/nowplaying.py:266
#, fuzzy
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "ListenBrainz: Angiv dit ListenBrainz brugernavn"

#: pynicotine/nowplaying.py:275
#, fuzzy, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz: Der kunne ikke oprettes forbindelse til ListenBrainz: %(error)s"

#: pynicotine/nowplaying.py:283
#, fuzzy
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "LytBrainz: Du synes ikke at lytte til noget lige nu"

#: pynicotine/nowplaying.py:297
#, fuzzy, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr "ListenBrainz: Kunne ikke få aktuelle spor fra ListenBrainz: %(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
#, fuzzy
msgid "Network Filters"
msgstr "Søgninger"

#: pynicotine/plugins/core_commands/__init__.py:44
#, fuzzy
msgid "List available commands"
msgstr "Ingen kø"

#: pynicotine/plugins/core_commands/__init__.py:49
#, fuzzy
msgid "Connect to the server"
msgstr "Der kan ikke oprettes forbindelse til serveren. Årsag: %s"

#: pynicotine/plugins/core_commands/__init__.py:53
#, fuzzy
msgid "Disconnect from the server"
msgstr "Frakobled fra server %(host)s:%(port)s"

#: pynicotine/plugins/core_commands/__init__.py:58
#, fuzzy
msgid "Toggle away status"
msgstr "Ændrer din away-status"

#: pynicotine/plugins/core_commands/__init__.py:62
#, fuzzy
msgid "Manage plugins"
msgstr "Aktivere filtere"

#: pynicotine/plugins/core_commands/__init__.py:74
#, fuzzy
msgid "Clear chat window"
msgstr "Rense chatvinduet"

#: pynicotine/plugins/core_commands/__init__.py:80
#, fuzzy
msgid "Say something in the third-person"
msgstr "Sig noget i tredje person"

#: pynicotine/plugins/core_commands/__init__.py:87
#, fuzzy
msgid "Announce the song currently playing"
msgstr "Annoncer den sang, der afspilles i øjeblikket"

#: pynicotine/plugins/core_commands/__init__.py:94
#, fuzzy
msgid "Join chat room"
msgstr "Deltag eller opret rum…"

#: pynicotine/plugins/core_commands/__init__.py:102
#, fuzzy
msgid "Leave chat room"
msgstr "Forlad rummet 'rum'"

#: pynicotine/plugins/core_commands/__init__.py:110
#, fuzzy
msgid "Say message in specified chat room"
msgstr "Sig besked i det angivne chatrum"

#: pynicotine/plugins/core_commands/__init__.py:117
#, fuzzy
msgid "Open private chat"
msgstr "Privat chat"

#: pynicotine/plugins/core_commands/__init__.py:125
#, fuzzy
msgid "Close private chat"
msgstr "Luk den private chat"

#: pynicotine/plugins/core_commands/__init__.py:133
#, fuzzy
msgid "Request user's client version"
msgstr "Brugeri_nfo"

#: pynicotine/plugins/core_commands/__init__.py:142
#, fuzzy
msgid "Send private message to user"
msgstr "Send privat besked til alle online venner:"

#: pynicotine/plugins/core_commands/__init__.py:150
#, fuzzy
msgid "Add user to buddy list"
msgstr "Tilføj brugeren 'bruger' til din ban-liste"

#: pynicotine/plugins/core_commands/__init__.py:158
#, fuzzy
msgid "Remove buddy from buddy list"
msgstr "Fjern brugeren 'bruger' fra din ban-liste"

#: pynicotine/plugins/core_commands/__init__.py:166
#, fuzzy
msgid "Browse files of user"
msgstr "Se brugerens filer 'bruger'"

#: pynicotine/plugins/core_commands/__init__.py:175
#, fuzzy
msgid "Show user profile information"
msgstr "Brugeri_nfo"

#: pynicotine/plugins/core_commands/__init__.py:183
#, fuzzy
msgid "Show IP address or username"
msgstr "Vis IP-adresse for brugeren 'bruger'"

#: pynicotine/plugins/core_commands/__init__.py:190
#, fuzzy
msgid "Block connections from user or IP address"
msgstr "Bloker forbindelser fra bruger eller IP-adresse"

#: pynicotine/plugins/core_commands/__init__.py:197
#, fuzzy
msgid "Remove user or IP address from ban lists"
msgstr "Fjern brugeren 'bruger' fra din ban-liste"

#: pynicotine/plugins/core_commands/__init__.py:204
#, fuzzy
msgid "Silence messages from user or IP address"
msgstr "Lydløs beskeder fra bruger eller IP-adresse"

#: pynicotine/plugins/core_commands/__init__.py:212
#, fuzzy
msgid "Remove user or IP address from ignore lists"
msgstr "Fjern brugeren 'bruger' fra din ignore-liste"

#: pynicotine/plugins/core_commands/__init__.py:220
#, fuzzy
msgid "Add share"
msgstr "Tilføj Wi_sh"

#: pynicotine/plugins/core_commands/__init__.py:226
#, fuzzy
msgid "Remove share"
msgstr "Fjern et alias"

#: pynicotine/plugins/core_commands/__init__.py:233
#, fuzzy
msgid "List shares"
msgstr "Omindexere delede filer"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Omindexere delede filer"

#: pynicotine/plugins/core_commands/__init__.py:246
#, fuzzy
msgid "Start global file search"
msgstr "Start global filsøgning"

#: pynicotine/plugins/core_commands/__init__.py:254
#, fuzzy
msgid "Search files in joined rooms"
msgstr "Søge i filer og mapper (nøjagtigt match)"

#: pynicotine/plugins/core_commands/__init__.py:262
#, fuzzy
msgid "Search files of all buddies"
msgstr "Søge i filer og mapper (nøjagtigt match)"

#: pynicotine/plugins/core_commands/__init__.py:270
#, fuzzy
msgid "Search a user's shared files"
msgstr "Søg i en brugers delede filer efter 'forespørgsel'"

#: pynicotine/plugins/core_commands/__init__.py:296
#, fuzzy, python-format
msgid "Listing %(num)i available commands:"
msgstr "Viser %(num)i tilgængelige kommandoer:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, fuzzy, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr "Viser %(num)i tilgængelige kommandoer, der matcher \"%(query)s\":"

#: pynicotine/plugins/core_commands/__init__.py:311
#, fuzzy, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Skriv %(command)s for at vise lignende kommandoer"

#: pynicotine/plugins/core_commands/__init__.py:314
#, fuzzy, python-format
msgid "Type %(command)s to list available commands"
msgstr "Indtast %(command)s for at få vist tilgængelige kommandoer"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, fuzzy, python-format
msgid "Not joined in room %s"
msgstr "%s kom ind i rummet"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:408
#, fuzzy, python-format
msgid "Closed private chat of user %s"
msgstr "Luk den private chat"

#: pynicotine/plugins/core_commands/__init__.py:476
#, fuzzy, python-format
msgid "Banned %s"
msgstr "Forbudt"

#: pynicotine/plugins/core_commands/__init__.py:490
#, fuzzy, python-format
msgid "Unbanned %s"
msgstr "Forbyd bruger"

#: pynicotine/plugins/core_commands/__init__.py:503
#, fuzzy, python-format
msgid "Ignored %s"
msgstr "Ignorerade brugere:"

#: pynicotine/plugins/core_commands/__init__.py:517
#, fuzzy, python-format
msgid "Unignored %s"
msgstr "Ignorer bruger"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr ""

#: pynicotine/pluginsystem.py:413
#, fuzzy
msgid "Loading plugin system"
msgstr "Indlæst plugin %s"

#: pynicotine/pluginsystem.py:516
#, fuzzy, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Plugin kunne ikke indlæses %(name)s. Navnet på plugin-mappen indeholder "
"ugyldige tegn: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, fuzzy, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "Modstridende %(interface)s kommando i plugin %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, fuzzy, python-format
msgid "Loaded plugin %s"
msgstr "Indlæst plugin %s"

#: pynicotine/pluginsystem.py:579
#, fuzzy, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Plugin kunne ikke indlæses %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, fuzzy, python-format
msgid "Unloaded plugin %s"
msgstr "Fjernet plugin %s"

#: pynicotine/pluginsystem.py:648
#, fuzzy, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Plugin kunne ikke fjernes %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, fuzzy, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Plugin %(module)s mislykkedes med fejlen %(errortype)s: %(error)s.\n"
"Sporing: %(trace)s"

#: pynicotine/pluginsystem.py:810
#, fuzzy
msgid "No description"
msgstr "Beskrivelse:"

#: pynicotine/pluginsystem.py:887
#, fuzzy, python-format
msgid "Missing %s argument"
msgstr "Mangler %s argument"

#: pynicotine/pluginsystem.py:896
#, fuzzy, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Fejlagtig SoulSeek-meta-url: %s"

#: pynicotine/pluginsystem.py:901
#, fuzzy, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Anvendelse: %(command)s %(args)s"

#: pynicotine/pluginsystem.py:940
#, fuzzy, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Ukendt kommando: %(command)s. Indtast %(help_command)s for at få vist "
"tilgængelige kommandoer."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
#, fuzzy
msgid "No UPnP devices found"
msgstr "Ingen UPnP-enheder fundet"

#: pynicotine/portmapper.py:633
#, fuzzy, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"UPnP: Den eksterne port kunne ikke videresendes %(external_port)s: %(error)s"

#: pynicotine/portmapper.py:647
#, fuzzy, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"UPnP: Den eksterne port %(external_port)s blev sendt til den lokale IP-"
"adresse %(ip_address)s port %(local_port)s"

#: pynicotine/privatechat.py:220
#, fuzzy, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Privat meddelelse fra %(user)s"

#: pynicotine/search.py:368
#, fuzzy, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Søger efter ønskeseddel \"%s\""

#: pynicotine/search.py:434
#, fuzzy, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Ønskeliste ventetid indstillet til %s sekunder"

#: pynicotine/search.py:760
#, fuzzy, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr ""
"Brugeren %(user)s søger efter \"%(query)s\", og returnerer %(num)i-resultater"

#: pynicotine/shares.py:302
#, fuzzy
msgid "Rebuilding shares…"
msgstr "Indexering påbegyndt"

#: pynicotine/shares.py:302
#, fuzzy
msgid "Rescanning shares…"
msgstr "Indexering påbegyndt"

#: pynicotine/shares.py:324
#, fuzzy, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Genscanning fuldført: %(num)s mapper fundet"

#: pynicotine/shares.py:334
#, fuzzy, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Der opstod en alvorlig fejl under genscanning af shares. Hvis problemet "
"fortsætter, skal du slette %(dir)s/*.db og prøve igen. Hvis det ikke "
"hjælper, skal du indsende en fejlrapport med denne staksporing inkluderet: "
"%(trace)s"

#: pynicotine/shares.py:582
#, fuzzy, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Der opstod en fejl under scanning af filen %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, fuzzy, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Der opstod en fejl under scanning af mappen %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, fuzzy, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr ""
"Der opstod en fejl under scanning af metadata for filen %(path)s: %(error)s"

#: pynicotine/shares.py:1046
#, fuzzy, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Genscanning afbrudt på grund af utilgængelige delinger"

#: pynicotine/shares.py:1184
#, fuzzy, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "Brugeren %(user)s gennemser listen over delte filer"

#: pynicotine/slskmessages.py:3120
#, fuzzy, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr "Shares-databasen kan ikke læses. Tjek dine aktier igen. Fejl: %s"

#: pynicotine/slskproto.py:500
#, fuzzy, python-format
msgid "Specified network interface '%s' is not available"
msgstr "Den specificerede netværksgrænseflade '%s' findes ikke"

#: pynicotine/slskproto.py:511
#, fuzzy, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Kan ikke lytte på port %(port)s. Sørg for, at ingen andre programmer bruger "
"det, eller vælg en anden port. Fejl: %(error)s"

#: pynicotine/slskproto.py:523
#, fuzzy, python-format
msgid "Listening on port: %i"
msgstr "Lytter på port %i"

#: pynicotine/slskproto.py:805
#, fuzzy, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "Kunne ikke forbinde til serveren %(host)s:%(port)s: %(error)s"

#: pynicotine/slskproto.py:1095
#, fuzzy, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Opret automatisk forbindelse til serveren ved start"

#: pynicotine/slskproto.py:1170
#, fuzzy, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Opretter forbindelse til %(host)s:%(port)s"

#: pynicotine/slskproto.py:1208
#, fuzzy, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Tilsluttet server %(host)s:%(port)s, logger in..."

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Frakobled fra server %(host)s:%(port)s"

#: pynicotine/slskproto.py:1499
#, fuzzy
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Nogen er logget ind på din Soulseek-konto et andet sted"

#: pynicotine/uploads.py:382
#, fuzzy, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr "Download færdig: %(user)s, fil %(file)s"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Mislykked download: bruger %(user)s, fil %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, fuzzy, python-format
msgid "Upload I/O error: %s"
msgstr "Overfør I/O-fejl: %s"

#: pynicotine/uploads.py:1103
#, fuzzy, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr "Mislykked download: bruger %(user)s, fil %(file)s"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr "Kunne ikke skape biblioteket '%(folder)s', rapporteret fejl: %(error)s"

#: pynicotine/userbrowse.py:236
#, fuzzy, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Indlæsning af shares fra disken mislykkedes: %(error)s"

#: pynicotine/userbrowse.py:282
#, fuzzy, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr "Gemt liste over delte filer for brugeren '%(user)s' til %(dir)s"

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr "Kunne ikke skape biblioteket '%(user)s', rapporteret fejl: %(error)s"

#: pynicotine/userinfo.py:160
#, fuzzy, python-format
msgid "Picture saved to %s"
msgstr "Billede gemt i %s"

#: pynicotine/userinfo.py:163
#, fuzzy, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Kunne ikke gemme konfigurationsfil, I/O-fel: %s"

#: pynicotine/userinfo.py:190
#, fuzzy, python-format
msgid "User %(user)s is viewing your profile"
msgstr "Brugeren %(user)s læser dine brugeroplysninger"

#: pynicotine/users.py:272
#, fuzzy, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Der kan ikke oprettes forbindelse til serveren. Årsag: %s"

#: pynicotine/users.py:272
#, fuzzy
msgid "Cannot Connect"
msgstr "Kunne ikke tilslutte"

#: pynicotine/users.py:306
#, fuzzy, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr "IP'en for brugeren %s kan ikke hentes, da denne bruger er offline"

#: pynicotine/users.py:315
#, fuzzy, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr "IP-adressen for %(user)s er %(ip)s, port %(port)i%(country)s"

#: pynicotine/users.py:433
#, fuzzy
msgid "Soulseek Announcement"
msgstr "Soulseek-klient"

#: pynicotine/users.py:449
#, fuzzy
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"Du har ingen privilegier. Rettigheder er ikke påkrævet, men tillad, at dine "
"downloads sættes i kø foran brugere uden rettigheder."

#: pynicotine/users.py:455
#, fuzzy, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"%(days)i dage, %(hours)i timer, %(minutes)i minutter, %(seconds)i sekunder "
"af downloadrettigheder tilbage."

#: pynicotine/users.py:473
#, fuzzy
msgid "Your password has been changed"
msgstr "Din adgangskode er blevet ændret. Adgangskoden er %s"

#: pynicotine/users.py:473
#, fuzzy
msgid "Password Changed"
msgstr "Ændring af adgangskode afvist"

#: pynicotine/utils.py:574
#, fuzzy, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Filen %(path)s kan ikke gemmes: %(error)s"

#: pynicotine/utils.py:621
#, fuzzy, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "Kunne ikke gemme konfigurationsfil, I/O-fel: %s"

#: pynicotine/utils.py:646
#, fuzzy, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr "Noget gik galt, mens du læser filen %(filename)s: %(error)s"

#: pynicotine/utils.py:651
#, fuzzy, python-format
msgid "Attempting to load backup of file %s"
msgstr "Forsøger at indlæse sikkerhedskopiering af filen %s"

#: pynicotine/utils.py:673
#, fuzzy, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Filen %(path)s kan ikke sikkerhedskopiers: %(error)s"

#: pynicotine/utils.py:694
#, fuzzy, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Filen %(path)s kan ikke gemmes: %(error)s"

#: pynicotine/utils.py:705
#, fuzzy, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Den forrige fil %(path)s kunne ikke gendannes: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
#, fuzzy
msgid "Add buddy…"
msgstr "Tilføj ven…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
#, fuzzy
msgid "Toggle Text-to-Speech"
msgstr "Slå tekst-til-tale til/fra"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
#, fuzzy
msgid "Chat Room Command Help"
msgstr "Hjælp til kommandokommando i chatrum"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
#, fuzzy
msgid "_Log"
msgstr "_Log"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
#, fuzzy
msgid "Room Wall"
msgstr "Værelse væg"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
#, fuzzy
msgid "R_oom Wall"
msgstr "Værelse væg"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
#, fuzzy
msgid "Created by"
msgstr "Lavet af"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
#, fuzzy
msgid "Translated by"
msgstr "Oversat af"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
#, fuzzy
msgid "License"
msgstr "Licens"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
#, fuzzy
msgid "Welcome to Nicotine+"
msgstr "Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
#, fuzzy
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Hvis dit ønskede brugernavn allerede er taget, bliver du bedt om at ændre "
"det."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
#, fuzzy
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Hvis lytteporten er lukket, kan du kun oprette forbindelse til brugere, hvis "
"lytteporte er åbne."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
#, fuzzy
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"Vælg om nødvendigt en anden lytteport nedenfor. Dette kan også gøres senere "
"i præferencerne."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
#, fuzzy
msgid "Download Files to Folder"
msgstr "Hent fil(er) _til..."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
#, fuzzy
msgid "Share Folders"
msgstr "Delede mapper"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
#, fuzzy
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Soulseek-brugere vil kunne downloade fra dine aktier. Bidrag til Soulseek-"
"netværket ved at dele din egen samling og ved at videredele det, du har "
"downloadet fra andre brugere."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
#, fuzzy
msgid "You are ready to use Nicotine+!"
msgstr "Du er klar til at bruge Nicotine+!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
#, fuzzy
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Donere til Soulseek giver dig privilegier i en bestemt periode. Hvis du har "
"rettigheder, sættes dine downloads i kø foran brugere uden rettigheder."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
#, fuzzy
msgid "Previous File"
msgstr "Forrige fil"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
#, fuzzy
msgid "Next File"
msgstr "Næste fil"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
#, fuzzy
msgid "Name"
msgstr "Filnavn"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
#, fuzzy
msgid "Last Speed"
msgstr "Sidste hastighed"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
#, fuzzy
msgid "_Export…"
msgstr "Eksport"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
#, fuzzy
msgid "Keyboard Shortcuts"
msgstr "Tastaturgenveje"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
#, fuzzy
msgid "General"
msgstr "Generel"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr "Tilslut"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr "Luk ned"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
#, fuzzy
msgid "Rescan Shares"
msgstr "Omindexere delede filer"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
#, fuzzy
msgid "Show Log Pane"
msgstr "Vis ruden Log"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
#, fuzzy
msgid "Confirm Quit"
msgstr "Konfigurer logføring"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
#, fuzzy
msgid "Quit"
msgstr "Afslutte"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
#, fuzzy
msgid "Menus"
msgstr "Menuer"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
#, fuzzy
msgid "Open Main Menu"
msgstr "Åbn hovedmenu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
#, fuzzy
msgid "Open Context Menu"
msgstr "Menuen Åbn kontekst"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
#, fuzzy
msgid "Tabs"
msgstr "Faner"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
#, fuzzy
msgid "Change Main Tab"
msgstr "Skift primær fane"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
#, fuzzy
msgid "Go to Previous Secondary Tab"
msgstr "Gå til forrige sekundær fane"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
#, fuzzy
msgid "Go to Next Secondary Tab"
msgstr "Gå til næste sekundære fane"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
#, fuzzy
msgid "Reopen Closed Secondary Tab"
msgstr "Luk sekundær fane"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
#, fuzzy
msgid "Close Secondary Tab"
msgstr "Luk sekundær fane"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
#, fuzzy
msgid "Lists"
msgstr "Lister"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
#, fuzzy
msgid "Copy Selected Cell"
msgstr "Vælg Alle"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
#, fuzzy
msgid "Select All"
msgstr "Vælg Alle"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
#, fuzzy
msgid "Find"
msgstr "Finde"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
#, fuzzy
msgid "Remove Selected Row"
msgstr "Fjern den valgte række"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
#, fuzzy
msgid "Editing"
msgstr "Karakter"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
#, fuzzy
msgid "Cut"
msgstr "Skære"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
#, fuzzy
msgid "Paste"
msgstr "Indsætte"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
#, fuzzy
msgid "Insert Emoji"
msgstr "Indsæt emoji"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
#, fuzzy
msgid "File Transfers"
msgstr "Overførsler"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
#, fuzzy
msgid "Resume / Retry Transfer"
msgstr "Fortsæt/ Overfør igen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
#, fuzzy
msgid "Pause / Abort Transfer"
msgstr "Afbryd overførslen midlertidigt/afbryd"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
#, fuzzy
msgid "Download / Upload To"
msgstr "Hent Mappe _til..."

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
#, fuzzy
msgid "Save List to Disk"
msgstr "_Save-shares-liste til disk"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
#, fuzzy
msgid "Refresh"
msgstr "Opdatere filer"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
#, fuzzy
msgid "Expand / Collapse All"
msgstr "Udvid/skjul alle"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
#, fuzzy
msgid "Back to Parent Folder"
msgstr "Mappen kan ikke deles"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
#, fuzzy
msgid "File Search"
msgstr "Søg"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
#, fuzzy
msgid "Result Filters"
msgstr "Aktivere filtere"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
#, fuzzy
msgid "Current Session"
msgstr "Aktuel session"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
#, fuzzy
msgid "Completed Downloads"
msgstr "Ingen kø"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
#, fuzzy
msgid "Downloaded Size"
msgstr "_Hent fil(er)"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
#, fuzzy
msgid "Completed Uploads"
msgstr "Fuldførte overførsler"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
#, fuzzy
msgid "Uploaded Size"
msgstr "Hent fi_l(er)"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
#, fuzzy
msgid "Total"
msgstr "Total"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
#, fuzzy
msgid "_Reset…"
msgstr "_Resume"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
#, fuzzy
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Ønskelisteelementer søges automatisk med jævne mellemrum for at opdage "
"usædvanlige filer."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
#, fuzzy
msgid "Add Wish…"
msgstr "Tilføj ønske…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
#, fuzzy
msgid "Clear All…"
msgstr "Rens logfil"

#: pynicotine/gtkgui/ui/downloads.ui:138
#, fuzzy
msgid "Clear All Finished/Filtered Downloads"
msgstr "Fil hentet"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
#, fuzzy
msgid "Clear Finished"
msgstr "Rens afsluttede"

#: pynicotine/gtkgui/ui/downloads.ui:169
#, fuzzy
msgid "Clear Specific Downloads"
msgstr "Downloads"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
#, fuzzy
msgid "Clear _All…"
msgstr "Rens logfil"

#: pynicotine/gtkgui/ui/interests.ui:21
#, fuzzy
msgid "Personal Interests"
msgstr "Interesser"

#: pynicotine/gtkgui/ui/interests.ui:40
#, fuzzy
msgid "Add something you like…"
msgstr "Tilføj noget, du kan lide…"

#: pynicotine/gtkgui/ui/interests.ui:62
#, fuzzy
msgid "Personal Dislikes"
msgstr "Jeg kan ikke li'"

#: pynicotine/gtkgui/ui/interests.ui:80
#, fuzzy
msgid "Add something you dislike…"
msgstr "Tilføj noget, du ikke kan lide…"

#: pynicotine/gtkgui/ui/interests.ui:143
#, fuzzy
msgid "Refresh Recommendations"
msgstr "Rekommendationer for %s"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
#, fuzzy
msgid "Main Menu"
msgstr "Åbn hovedmenu"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
#, fuzzy
msgid "Room…"
msgstr "Rum"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
#, fuzzy
msgid "Username…"
msgstr "Brugernavn…"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
#, fuzzy
msgid "Search term…"
msgstr "Søgninger"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Rens"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
#, fuzzy
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Søgemønstre: med et ord = udtryk, uden et ord = -term, delvis ord = *erm"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
#, fuzzy
msgid "Search Scope"
msgstr "Søgeområde"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
#, fuzzy
msgid "_Wishlist"
msgstr "_Wishlist"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
#, fuzzy
msgid "Configure Searches"
msgstr "Konfigurer søgninger"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
#, fuzzy
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Angiv et søgeord for at søge efter filer, der deles af andre brugere på "
"Soulseek-netværket"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
#, fuzzy
msgid "File Grouping Mode"
msgstr "Filgrupperingstilstand"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
#, fuzzy
msgid "Configure Downloads"
msgstr "Konfigurer overførsler"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
#, fuzzy
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Filer, du downloader fra andre brugere, sættes i kø her og kan sættes på "
"pause og genoptages efter behov"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
#, fuzzy
msgid "Configure Uploads"
msgstr "Konfigurer overførsler"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
#, fuzzy
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Brugernes forsøg på at downloade dine delte filer sættes i kø og "
"administreres her"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
#, fuzzy
msgid "_Open List"
msgstr "_Open liste"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
#, fuzzy
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Åbner en lokal liste over delte filer, der tidligere blev gemt på disken"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
#, fuzzy
msgid "Configure Shares"
msgstr "_Configure Aktier"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
#, fuzzy
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Angiv navnet på en bruger, hvis delte filer du vil gennemse. Du kan også "
"gemme listen på disken og undersøge den senere."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
#, fuzzy
msgid "_Personal Profile"
msgstr "Jeg kan ikke li'"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
#, fuzzy
msgid "Configure Account"
msgstr "Konfigurer logføring"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
#, fuzzy
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Angiv navnet på en bruger for at få vist vedkommendes brugerbeskrivelse, "
"oplysninger og personlige billede"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
#, fuzzy
msgid "Chat _History"
msgstr "Chathistorik"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
#, fuzzy
msgid "Configure Chats"
msgstr "Konfigurer shares"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
#, fuzzy
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr ""
"Angiv navnet på en bruger for at starte en tekstsamtale med vedkommende "
"privat"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
#, fuzzy
msgid "_Message All"
msgstr "Meddelelser"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
#, fuzzy
msgid "Configure Ignored Users"
msgstr "Ignorerade brugere:"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
#, fuzzy
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Føje brugere til din venneliste for at dele bestemte mapper med dem og "
"modtage meddelelser, når de er online"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
#, fuzzy
msgid "Join or create room…"
msgstr "Deltag eller opret rum…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
#, fuzzy
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Deltag i et eksisterende chatrum, eller opret et nyt rum for at chatte med "
"andre brugere på Soulseek-netværket"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
#, fuzzy
msgid "Configure User Profile"
msgstr "Se brugerprofil"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
#, fuzzy
msgid "Connections"
msgstr "Forbindelser"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
#, fuzzy
msgid "Downloading (Speed / Active Users)"
msgstr "Download (hastighed / aktive brugere)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
#, fuzzy
msgid "Uploading (Speed / Active Users)"
msgstr "Upload (hastighed / aktive brugere)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
#, fuzzy
msgid "Search chat history…"
msgstr "Søgninger"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
#, fuzzy
msgid "Download Speed Limits"
msgstr "Download påbegyndt: %s"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
#, fuzzy
msgid "Unlimited download speed"
msgstr "Begræns uploadhastigheden til"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
#, fuzzy
msgid "Use download speed limit (KiB/s):"
msgstr "Alternativ hastighedsgrænse for download (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
#, fuzzy
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Alternativ hastighedsgrænse for download (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
#, fuzzy
msgid "Search rooms…"
msgstr "Søgninger"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
#, fuzzy
msgid "Refresh Rooms"
msgstr "Opdater værelsesliste"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
#, fuzzy
msgid "_Show feed of public chat room messages"
msgstr "_Show feed af offentlige chatrumsbeskeder"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
#, fuzzy
msgid "_Accept private room invitations"
msgstr "_Accept invitationer til private værelser"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
#, fuzzy
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Værelsesvægsfunktionen giver brugerne i et rum mulighed for at angive en "
"unik meddelelse, der skal vises til andre. De seneste meddelelser vises "
"øverst."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
#, fuzzy
msgid "Set wall message…"
msgstr "Send besked"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
#, fuzzy
msgid "Search Result Filters"
msgstr "_Søg filer"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
#, fuzzy
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Søgeresultatfiltre bruges til at forfine, hvilke søgeresultater der vises."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
#, fuzzy
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Hver liste over søgeresultater har sit eget filter, som kan afsløres ved at "
"skifte knappen Resultatfiltre. Et filter består af flere felter, som alle "
"anvendes, når der trykkes på Enter i et af dets felter. Filtrering anvendes "
"straks på resultater, der allerede er modtaget, og også på dem, der endnu "
"ikke er ankommet. Hvis du vil se de fulde resultater igen, skal du blot "
"rydde filteret for alle termer og anvende det igen. Som navnet antyder, kan "
"et søgeresultatfilter ikke udvide din oprindelige søgning, det kan kun "
"indsnævre det ned. Hvis du vil udvide eller ændre søgeordet, skal du udføre "
"en ny søgning."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
#, fuzzy
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Som navnet antyder, kan et søgeresultatfilter ikke udvide din oprindelige "
"søgning, det kan kun indsnævre det. Foretag en ny søgning for at udvide "
"eller ændre dine søgetermer."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
#, fuzzy
msgid "Result Filter Usage"
msgstr "Aktivere filtere"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
#, fuzzy
msgid "Include Text"
msgstr "Medtag tekst"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
#, fuzzy
msgid "Files, folders and usernames containing this text will be shown."
msgstr "Filer og mapper, der indeholder denne tekst, vises."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
#, fuzzy
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Sagen er ufølsom, men ordrækkefølgen er vigtig: 'Instrumental Remix' vil "
"ikke vise nogen 'Remix Instrumental'"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
#, fuzzy
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Brug | (eller rør) for at adskille flere nøjagtige sætninger. Eksempel:\n"
"    Remix|Dub Mix|Instrumental"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
#, fuzzy
msgid "Exclude Text"
msgstr "Udelad tekst"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
#, fuzzy
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"Som ovenfor, men filer og mapper filtreres fra, hvis teksten stemmer overens."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
#, fuzzy
msgid "Filters files based upon their file extension."
msgstr "Filtrerer filer baseret på deres filtypenavn."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
#, fuzzy
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Der kan angives flere filtypenavne, hvilket igen vil udvide listen over "
"resultater.\n"
"    Eksempel: flac|wav|ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
#, fuzzy
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"Det er også muligt at invertere filteret og angive de filtypenavne, du ikke "
"ønsker i resultaterne.\n"
"    Eksempel: !mp3|! jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
#, fuzzy
msgid "File Size"
msgstr "Filstørrelse"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
#, fuzzy
msgid "Filters files based upon their file size."
msgstr "Filtrerer filer baseret på deres filstørrelse."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
#, fuzzy
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"Den anvendte enhed er som standard byte, og filer, der er større end eller "
"lig med værdien, vil blive matchet."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
#, fuzzy
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Tilføj b, k, m eller g (alternativt kib, mib eller gib) for at angive "
"enheder med byte, kibibyte, mebibyte eller gibibyte:\n"
"    <1024k vil finde filer 1024 kibibytes (dvs. 1 mebibyte) eller mindre."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
#, fuzzy
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Forberedelse = til en værdi for at angive et nøjagtigt match:\n"
"    =1024 svarer kun til filer, der har en størrelse på 1024 byte (dvs. 1 "
"kibibyte)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
#, fuzzy
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Forberedelse = til en værdi for at angive et nøjagtigt match:\n"
"    =1024 svarer kun til filer, der har en størrelse på 1024 byte (dvs. 1 "
"kibibyte)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
#, fuzzy
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Sæt < eller > foran for at finde filer, der er mindre/større end den givne "
"værdi:\n"
"    >10,5m|<1g for at vise filer større end 10,5 MiB (mebibytes),\n"
"    men mindre end 1 GiB (gibibytes), brug en | mellem forhold."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
#, fuzzy
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"For nemheds skyld kan varianterne kb, mb og gb for de bedre kendte kilo-, "
"mega- og gigabyte-enheder også bruges."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
#, fuzzy
msgid "Filters files based upon their bitrate."
msgstr "Filtrerer filer baseret på deres bitrate."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
#, fuzzy
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Værdier skal kun indtastes som numeriske cifre. Enheden er altid Kb/s "
"(kilobit pr. sekund)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
#, fuzzy
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Ligesom filstørrelse (ovenfor) kan operatorer =, !, < og > bruges, og flere "
"betingelser kan angives med | rør:\n"
"    >256|<1411 for at vise filer med en bithastighed på mindst 256 Kb/s\n"
"    med en maksimal bithastighed på 1411 Kb/s."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
#, fuzzy
msgid "Filters files based upon their duration."
msgstr "Filtrerer filer baseret på deres bitrate."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
#, fuzzy
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"Som standard vil filer, der er længere end eller lig med den indtastede "
"varighed, blive matchet, medmindre en operator (=, !, < eller >) bruges."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
#, fuzzy
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Indtast en rå værdi i sekunder, eller brug tidsformaterne MM:SS og TT:MM:"
"SS:\n"
"    >5:30 for at vise filer på mindst 5 et halvt minut lange.\n"
"    <5:30:00 viser filer, der er mindre end 5 og en halv time lange."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
#, fuzzy
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Flere betingelser kan angives med | rørskillere:\n"
"    >6:00|<12:00 for at vise filer på mellem 6 og 12 minutter.\n"
"    !9:54|!8:43|!7:32 for at skjule nogle specifikke filer fra "
"resultaterne.\n"
"    =5:34|=4:23|=3:05 for at inkludere filer med specifik varighed."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
#, fuzzy
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Bruger landekoder defineret af ISO 3166-2 (se Wikipedia):\n"
"    'USA' returnerer kun filer fra brugere, der er tilsluttet via USA. På "
"samme måde returnerer »GB« filer fra brugere med IPs i Det Forenede "
"Kongerige."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
#, fuzzy
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Flere lande kan angives med kommaer eller mellemrum."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
#, fuzzy
msgid "Free Slot"
msgstr "Ledig plads"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
#, fuzzy
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Vis kun de resultater fra brugere, der har mindst én upload slot gratis. "
"Dette filter anvendes med det samme."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
#, fuzzy
msgid "Upload Speed Limits"
msgstr "Overfør hastighedsgrænser"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
#, fuzzy
msgid "Unlimited upload speed"
msgstr "Begræns uploadhastigheden til"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
#, fuzzy
msgid "Use upload speed limit (KiB/s):"
msgstr "Alternativ hastighedsgrænse for upload (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
#, fuzzy
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Alternativ hastighedsgrænse for upload (KiB/s):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
#, fuzzy
msgid "Private Chat Command Help"
msgstr "Hjælp til privat chatkommando"

#: pynicotine/gtkgui/ui/search.ui:7
#, fuzzy
msgid "Include text…"
msgstr "Medtag tekst…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
#, fuzzy
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtrer i resultater, hvis filstier indeholder den angivne tekst. Der kan "
"specificeres flere sætninger og ord, f.eks|||."

#: pynicotine/gtkgui/ui/search.ui:18
#, fuzzy
msgid "Exclude text…"
msgstr "Udelad tekst…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
#, fuzzy
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Bortfiltrere resultater, hvis filstier indeholder den angivne tekst. Der kan "
"specificeres flere sætninger og ord, f.eks|||."

#: pynicotine/gtkgui/ui/search.ui:29
#, fuzzy
msgid "File type…"
msgstr "Filtype…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
#, fuzzy
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Filtype, f.eks flac|wav|ape eller !mp3|! m4a"

#: pynicotine/gtkgui/ui/search.ui:40
#, fuzzy
msgid "File size…"
msgstr "Filstørrelse…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
#, fuzzy
msgid "File size, e.g. >10.5m <1g"
msgstr "Filstørrelse, f.eks. >10,5m <1g"

#: pynicotine/gtkgui/ui/search.ui:51
#, fuzzy
msgid "Bitrate…"
msgstr "Bitrate"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
#, fuzzy
msgid "Bitrate, e.g. 256 <1412"
msgstr "Bitrate, f.eks. 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
#, fuzzy
msgid "Duration…"
msgstr "Varighed…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
#, fuzzy
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Varighed, f.eks. 2:20|!3:30|=4:40"

#: pynicotine/gtkgui/ui/search.ui:73
#, fuzzy
msgid "Country code…"
msgstr "Landekode…"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
#, fuzzy
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Landekode, f.eks. USA| GB|ES eller !DE|! GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
#, fuzzy
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Forbyd brugere at få adgang til dine delte filer baseret på brugernavn, IP-"
"adresse eller land."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Landekoder at blokera (afgræns med komma):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
#, fuzzy
msgid "Codes must be in ISO 3166-2 format."
msgstr "Koderne skal være i ISO 3166-2-format."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
#, fuzzy
msgid "Use custom geo block message:"
msgstr "Brug eget ban-besked:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Brug eget ban-besked:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
#, fuzzy
msgid "IP Addresses"
msgstr "Adresser"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
#, fuzzy
msgid "Restore previously open private chats on startup"
msgstr "Gendan tidligere åbne private chats ved start"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
#, fuzzy
msgid "Enable spell checker"
msgstr "Aktiver stavekontrol (kræver genstart)"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
#, fuzzy
msgid "Enable CTCP-like private message responses (client version)"
msgstr "Aktiver CTCP-lignende svar på private meddelelser (klientversion)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
#, fuzzy
msgid "Number of recent private chat messages to show:"
msgstr "Antal seneste chatlinjer, der skal vises:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
#, fuzzy
msgid "Number of recent chat room messages to show:"
msgstr "Antal seneste chatlinjer, der skal vises:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
#, fuzzy
msgid "Chat Completion"
msgstr "Færdiggørelse"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
#, fuzzy
msgid "Enable tab-key completion"
msgstr "Aktiver fuldførelse af tabulatortast"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
#, fuzzy
msgid "Enable completion drop-down list"
msgstr "Aktiver rullelisten Fuldførelse"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
#, fuzzy
msgid "Minimum characters required to display drop-down:"
msgstr "Der kræves minimumtegn for at få vist rullelisten:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
#, fuzzy
msgid "Allowed chat completions:"
msgstr "Tilladte afslutninger"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
#, fuzzy
msgid "Buddy names"
msgstr "Venneliste"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
#, fuzzy
msgid "Chat room usernames"
msgstr "Chatrumsmeddelelse:"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
#, fuzzy
msgid "Room names"
msgstr "Rum"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
#, fuzzy
msgid "Commands"
msgstr "Kommentarer"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
#, fuzzy
msgid "Timestamps"
msgstr "Forløben tid"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
#, fuzzy
msgid "Private chat format:"
msgstr "Privat chat"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "Standard"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
#, fuzzy
msgid "Chat room format:"
msgstr "Chatrumsformat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
#, fuzzy
msgid "Text-to-Speech"
msgstr "Tekst-til-tale"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
#, fuzzy
msgid "Enable Text-to-Speech"
msgstr "Aktiver tekst-til-tale"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
#, fuzzy
msgid "Text-to-Speech command:"
msgstr "Tekst-til-tale-kommando:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
#, fuzzy
msgid "Private chat message:"
msgstr "Privat chatbesked:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
#, fuzzy
msgid "Chat room message:"
msgstr "Chatrumsmeddelelse:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
#, fuzzy
msgid "Censor"
msgstr "Censor"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
#, fuzzy
msgid "Enable censoring of text patterns"
msgstr "Aktiver censurering af tekstmønstre"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
#, fuzzy
msgid "Auto-Replace"
msgstr "Automatisk erstat"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
#, fuzzy
msgid "Enable automatic replacement of words"
msgstr "Aktiver automatisk udskiftning af ord"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
#, fuzzy
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr "Autoclear færdig / filtreret downloads fra overførselslisten"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
#, fuzzy
msgid "Store completed downloads in username subfolders"
msgstr "Gemme overførsler i undermapper til brugernavne"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
#, fuzzy
msgid "Double-click action for downloads:"
msgstr "Venter på download"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
#, fuzzy
msgid "Allow users to send you any files:"
msgstr "Tillad disse brugere at sende dig filer:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
#, fuzzy
msgid "Folders"
msgstr "Mapper"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
#, fuzzy
msgid "Finished downloads:"
msgstr "Fil hentet"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
#, fuzzy
msgid "Incomplete downloads:"
msgstr "Ingen kø"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
#, fuzzy
msgid "Received files:"
msgstr "Spørg efter fil"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "Hændelser"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
#, fuzzy
msgid "Run command after file download finishes ($ for file path):"
msgstr "Kør kommando når download er færdig ($ for filnavn):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
#, fuzzy
msgid "Run command after folder download finishes ($ for folder path):"
msgstr "Kør kommando når mappe er færdig ($ for mappenavn):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
#, fuzzy
msgid "Download Filters"
msgstr "_Hent fil(er)"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
#, fuzzy
msgid "Enable download filters"
msgstr "Aktivere filtere"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Tilføj"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Standard"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
#, fuzzy
msgid "Verify Filters"
msgstr "Kontroller filtre"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
#, fuzzy
msgid "Unverified"
msgstr "Ubekræftet"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
#, fuzzy
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Ignorer chatbeskeder og søgeresultater fra brugere baseret på brugernavn "
"eller IP-adresse."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Logge chatrum"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Logge privatchat"

#: pynicotine/gtkgui/ui/settings/log.ui:142
#, fuzzy
msgid "Log transfers to file"
msgstr "Logfør overførsler til fil"

#: pynicotine/gtkgui/ui/settings/log.ui:166
#, fuzzy
msgid "Log debug messages to file"
msgstr "Logfør fejlfindingsmeddelelser til fil"

#: pynicotine/gtkgui/ui/settings/log.ui:190
#, fuzzy
msgid "Log timestamp format:"
msgstr "Logfilformat:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
#, fuzzy
msgid "Folder Locations"
msgstr "Mappeplaceringer"

#: pynicotine/gtkgui/ui/settings/log.ui:245
#, fuzzy
msgid "Chatroom logs folder:"
msgstr "Mappen Chatroom logs:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
#, fuzzy
msgid "Private chat logs folder:"
msgstr "Privat chat"

#: pynicotine/gtkgui/ui/settings/log.ui:277
#, fuzzy
msgid "Transfer logs folder:"
msgstr "Overførsler"

#: pynicotine/gtkgui/ui/settings/log.ui:293
#, fuzzy
msgid "Debug logs folder:"
msgstr "Udført på mappe: %s"

#: pynicotine/gtkgui/ui/settings/network.ui:39
#, fuzzy
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Log ind på en eksisterende Soulseek-konto, eller opret en ny. Brugernavne "
"skelner mellem store og små bogstaver og er unikke."

#: pynicotine/gtkgui/ui/settings/network.ui:118
#, fuzzy
msgid "Public IP address:"
msgstr "Vis IP-a_dresse"

#: pynicotine/gtkgui/ui/settings/network.ui:159
#, fuzzy
msgid "Listening port:"
msgstr "Lytter på port %i"

#: pynicotine/gtkgui/ui/settings/network.ui:185
#, fuzzy
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Videresend automatisk lytteport (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
#, fuzzy
msgid "Away Status"
msgstr "Status"

#: pynicotine/gtkgui/ui/settings/network.ui:228
#, fuzzy
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Minutters inaktivitet, før du går væk (0 for at deaktivere):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
#, fuzzy
msgid "Auto-reply message when away:"
msgstr "Auto-svar ved away:  "

#: pynicotine/gtkgui/ui/settings/network.ui:299
#, fuzzy
msgid "Auto-connect to server on startup"
msgstr "Opret automatisk forbindelse til serveren ved start"

#: pynicotine/gtkgui/ui/settings/network.ui:323
#, fuzzy
msgid "Soulseek server:"
msgstr "Soulseek server:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
#, fuzzy
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Binder forbindelser til en bestemt netværksgrænseflade, der er nyttig til f."
"eks. at sikre, at en VPN bruges til enhver tid. Lad det være tomt for at "
"bruge en tilgængelig grænseflade. Kun ændre denne værdi, hvis du ved, hvad "
"du gør."

#: pynicotine/gtkgui/ui/settings/network.ui:351
#, fuzzy
msgid "Network interface:"
msgstr "Søgninger"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
#, fuzzy
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"Nu spiller giver dig mulighed for at vise, hvad din medieafspiller spiller "
"ved hjælp af / nu kommando i chatten."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
#, fuzzy
msgid "Other"
msgstr "Anden"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
#, fuzzy
msgid "Now Playing Format"
msgstr "Spiller nu format"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
#, fuzzy
msgid "Now Playing message format:"
msgstr "Afspiller nu meddelelsesformat:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
#, fuzzy
msgid "Test Configuration"
msgstr "Testkonfiguration"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
#, fuzzy
msgid "Enable plugins"
msgstr "Aktivere filtere"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
#, fuzzy
msgid "Add Plugins"
msgstr "_Add Plugins"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
#, fuzzy
msgid "_Add Plugins"
msgstr "_Add Plugins"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
#, fuzzy
msgid "Settings"
msgstr "Indstillninger"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
#, fuzzy
msgid "_Settings"
msgstr "Indstillninger"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
#, fuzzy
msgid "Version:"
msgstr "Version: "

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
#, fuzzy
msgid "Created by:"
msgstr "Lavet af"

#: pynicotine/gtkgui/ui/settings/search.ui:51
#, fuzzy
msgid "Enable search history"
msgstr "Aktiver søgehistorik"

#: pynicotine/gtkgui/ui/settings/search.ui:70
#, fuzzy
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Andre klienter kan tilbyde en mulighed for at sende privat delte filer som "
"svar på søgeanmodninger. Sådanne filer er præfikset med '[PRIVATE FILE]', og "
"kan ikke downloades, medmindre uploaderen giver udtrykkelig tilladelse."

#: pynicotine/gtkgui/ui/settings/search.ui:76
#, fuzzy
msgid "Show privately shared files in search results"
msgstr "Vise privat delte filer i søgeresultater"

#: pynicotine/gtkgui/ui/settings/search.ui:106
#, fuzzy
msgid "Limit number of results per search:"
msgstr "Begræns antallet af resultater pr. søgning:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
#, fuzzy
msgid "Result Filter Help"
msgstr "Hjælp til resultatfilter"

#: pynicotine/gtkgui/ui/settings/search.ui:177
#, fuzzy
msgid "Enable search result filters by default"
msgstr "Aktivere filtere som standard"

#: pynicotine/gtkgui/ui/settings/search.ui:211
#, fuzzy
msgid "Include:"
msgstr "Omfatte:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
#, fuzzy
msgid "Exclude:"
msgstr "Udelukke:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
#, fuzzy
msgid "File Type:"
msgstr "Filtype:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "størrelse:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Bitrate:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
#, fuzzy
msgid "Duration:"
msgstr "Varighed:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
#, fuzzy
msgid "Country Code:"
msgstr "Landekode:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
#, fuzzy
msgid "Only show results from users with an available upload slot."
msgstr "Vis kun resultater fra brugere med en tilgængelig uploadplads."

#: pynicotine/gtkgui/ui/settings/search.ui:430
#, fuzzy
msgid "Network Searches"
msgstr "Søgninger"

#: pynicotine/gtkgui/ui/settings/search.ui:452
#, fuzzy
msgid "Respond to search requests from other users"
msgstr "Besvar søgeanmodninger fra andre brugere"

#: pynicotine/gtkgui/ui/settings/search.ui:486
#, fuzzy
msgid "Searches shorter than this number of characters will be ignored:"
msgstr "Søgninger, der er kortere end dette antal tegn, ignoreres:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
#, fuzzy
msgid "Maximum search results to send per search request:"
msgstr "søgresultat per søgning"

#: pynicotine/gtkgui/ui/settings/search.ui:578
#, fuzzy
msgid "Clear Search History"
msgstr "Ryd søgehistorik"

#: pynicotine/gtkgui/ui/settings/search.ui:626
#, fuzzy
msgid "Clear Filter History"
msgstr "Ryd filterhistorik"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
#, fuzzy
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Automatisk scanne indholdet af dine delte mapper ved start. Hvis de er "
"deaktiveret, opdateres dine shares kun, når du manuelt starter en "
"genscanning."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Omindexere dine filer ved programstart"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/shares.ui:82
#, fuzzy
msgid "Buddy shares"
msgstr "Venneliste"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
#, fuzzy
msgid "Trusted shares"
msgstr "Omindexere delede filer"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
#, fuzzy
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr "Autoclear færdig / annulleret uploads fra overførsel liste"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
#, fuzzy
msgid "Double-click action for uploads:"
msgstr "Dobbeltklik handling for uploads:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
#, fuzzy
msgid "Limit upload speed:"
msgstr "Begræns uploadhastigheden til"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
#, fuzzy
msgid "Per transfer"
msgstr "per overforsel"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
#, fuzzy
msgid "Total transfers"
msgstr "totalt"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
#, fuzzy
msgid "Upload Slots"
msgstr "Uploads"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
#, fuzzy
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Filer vil blive uploadet på cyklisk måde til de brugere, der venter i kø."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
#, fuzzy
msgid "Upload queue type:"
msgstr "Overfør køtype:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
#, fuzzy
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr "Kø uploads, hvis den samlede overførselshastighed når (KiB/s):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
#, fuzzy
msgid "Fixed number of upload slots:"
msgstr "Begræns uploadhastigheden til"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
#, fuzzy
msgid "Prioritize all buddies"
msgstr "Mine venner går forrest i downloadkøen"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
#, fuzzy
msgid "Queue Limits"
msgstr "Køstørrelse: %i"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
#, fuzzy
msgid "Maximum number of queued files per user:"
msgstr "Begræns antallet af resultater pr. søgning:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
#, fuzzy
msgid "Limits do not apply to buddies"
msgstr "Mine venner slipper kørestriktioner"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
#, fuzzy
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"Forekomster af $ vil blive erstattet af linket. Systemets standardwebbrowser "
"bruges i de tilfælde, hvor en protokol ikke er konfigureret."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
#, fuzzy
msgid "File manager command:"
msgstr "Kommandoen Filhåndtering ($ for filsti):"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
#, fuzzy
msgid "Reset Picture"
msgstr "Nulstil billede"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
#, fuzzy
msgid "Self Description"
msgstr "Beskrivelse:"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
#, fuzzy
msgid "Picture:"
msgstr "Billede:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
#, fuzzy
msgid "Prefer dark mode"
msgstr "Foretrækker mørk tilstand"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
#, fuzzy
msgid "Use header bar"
msgstr "Brug _Header bar"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
#, fuzzy
msgid "Display tray icon"
msgstr "Ikonet Vis bakke"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
#, fuzzy
msgid "Minimize to tray on startup"
msgstr "Minimer til bakke ved start"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
#, fuzzy
msgid "Language (requires a restart):"
msgstr "Lytteportområde (kræver genstart):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
#, fuzzy
msgid "When closing window:"
msgstr "Ved lukning af Nicotine+:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
#, fuzzy
msgid "Notifications"
msgstr "Meddelelser"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
#, fuzzy
msgid "Enable sound for notifications"
msgstr "Aktiver lyd til pop op-meddelelser"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
#, fuzzy
msgid "Show notification for private chats and mentions in the window title"
msgstr "Vis notifikation for private chats og omtaler i vinduestitlen"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
#, fuzzy
msgid "Show notifications for:"
msgstr "Vis meddelelsesikoner under faner"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
#, fuzzy
msgid "Finished file downloads"
msgstr "Fil hentet"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
#, fuzzy
msgid "Finished folder downloads"
msgstr "Mappe hentet"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
#, fuzzy
msgid "Private messages"
msgstr "Privat meddelelse fra %s"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
#, fuzzy
msgid "Chat room messages"
msgstr "Chatrumsmeddelelse:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
#, fuzzy
msgid "Chat room mentions"
msgstr "Færdiggørelse"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
#, fuzzy
msgid "Wishlist results found"
msgstr "Ønskelisteresultater fundet"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
#, fuzzy
msgid "Restore the previously active main tab at startup"
msgstr "Gendan tidligere åbne private chats ved start"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
#, fuzzy
msgid "Close-buttons on secondary tabs"
msgstr "Luk knapper under sekundære faner"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
#, fuzzy
msgid "Regular tab label color:"
msgstr "Farve på den almindelige faneetiket:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
#, fuzzy
msgid "Changed tab label color:"
msgstr "Ændret faneetiketfarve:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
#, fuzzy
msgid "Highlighted tab label color:"
msgstr "Fremhævet etiketfarve på fanen:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
#, fuzzy
msgid "Visible main tabs:"
msgstr "Synlige primære faner:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
#, fuzzy
msgid "Tab bar positions:"
msgstr "Placering på tabulatorlinjen:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
#, fuzzy
msgid "Main tabs"
msgstr "Hovedfaner"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
#, fuzzy
msgid "Show reverse file paths (requires a restart)"
msgstr ""
"Vis stier til omvendte filer i søge- og overførselsvisninger (kræver "
"genstart)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
#, fuzzy
msgid "Show exact file sizes (requires a restart)"
msgstr "Netværksgrænseflade (kræver genstart):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
#, fuzzy
msgid "List text color:"
msgstr "Vis tekstfarve:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
#, fuzzy
msgid "Enable colored usernames"
msgstr "Chatrumsmeddelelse:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
#, fuzzy
msgid "Chat username appearance:"
msgstr "Udseende til chat brugernavn:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
#, fuzzy
msgid "Remote text color:"
msgstr "Tekstfarve på fjerntekst:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
#, fuzzy
msgid "Local text color:"
msgstr "Lokalt filfejl"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
#, fuzzy
msgid "Command output text color:"
msgstr "Tekstfarve på fjerntekst:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
#, fuzzy
msgid "/me action text color:"
msgstr "/me handlingstekstfarve:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
#, fuzzy
msgid "Highlighted text color:"
msgstr "Fremhævet tekstfarve:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
#, fuzzy
msgid "URL link text color:"
msgstr "Tekstfarve for URL-link:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
#, fuzzy
msgid "User Statuses"
msgstr "USA"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
#, fuzzy
msgid "Online color:"
msgstr "Online tekstfarve:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
#, fuzzy
msgid "Away color:"
msgstr "Tekstfarve, der ikke er på lager:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
#, fuzzy
msgid "Offline color:"
msgstr "Offlinetekstfarve:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
#, fuzzy
msgid "Text Entries"
msgstr "Tekstposter"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
#, fuzzy
msgid "Text entry background color:"
msgstr "Baggrundsfarve for tekstindtastning:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
#, fuzzy
msgid "Text entry text color:"
msgstr "Tekstfarve for tekst med tekst til tekst til tekst:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
#, fuzzy
msgid "Fonts"
msgstr "Skrifttyper"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
#, fuzzy
msgid "Global font:"
msgstr "Global"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
#, fuzzy
msgid "List font:"
msgstr "Skrifttype på liste:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
#, fuzzy
msgid "Text view font:"
msgstr "Skrifttype for tekstvisning:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
#, fuzzy
msgid "Chat font:"
msgstr "Chattrum"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
#, fuzzy
msgid "Transfers font:"
msgstr "Overførsler"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
#, fuzzy
msgid "Search font:"
msgstr "Søg filer"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
#, fuzzy
msgid "Browse font:"
msgstr "Kig i filer"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
#, fuzzy
msgid "Icons"
msgstr "Ikoner"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
#, fuzzy
msgid "Icon theme folder:"
msgstr "Ufuldstændig filmappe:"

#: pynicotine/gtkgui/ui/uploads.ui:86
#, fuzzy
msgid "Abort User(s)"
msgstr "Afbrudt"

#: pynicotine/gtkgui/ui/uploads.ui:116
#, fuzzy
msgid "Ban User(s)"
msgstr "Forbyd bruger(er)"

#: pynicotine/gtkgui/ui/uploads.ui:138
#, fuzzy
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Autoclear færdig / annulleret uploads fra overførsel liste"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
#, fuzzy
msgid "Message All"
msgstr "Meddelelser"

#: pynicotine/gtkgui/ui/uploads.ui:199
#, fuzzy
msgid "Clear Specific Uploads"
msgstr "Rens logfil"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
#, fuzzy
msgid "Save Shares List to Disk"
msgstr "_Save-shares-liste til disk"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
#, fuzzy
msgid "Refresh Files"
msgstr "Opdatere filer"

#: pynicotine/gtkgui/ui/userinfo.ui:101
#, fuzzy
msgid "Edit Profile"
msgstr "Delede mapper"

#: pynicotine/gtkgui/ui/userinfo.ui:149
#, fuzzy
msgid "Shared Files"
msgstr "_Søg filer"

#: pynicotine/gtkgui/ui/userinfo.ui:203
#, fuzzy
msgid "Upload Speed"
msgstr "Uploads"

#: pynicotine/gtkgui/ui/userinfo.ui:230
#, fuzzy
msgid "Free Upload Slots"
msgstr "Ledig plads"

#: pynicotine/gtkgui/ui/userinfo.ui:284
#, fuzzy
msgid "Queued Uploads"
msgstr "Uploads"

#: pynicotine/gtkgui/ui/userinfo.ui:354
#, fuzzy
msgid "Edit Interests"
msgstr "Interesser"

#: pynicotine/gtkgui/ui/userinfo.ui:624
#, fuzzy
msgid "_Gift Privileges…"
msgstr "indrømme privilegier"

#: pynicotine/gtkgui/ui/userinfo.ui:663
#, fuzzy
msgid "_Refresh Profile"
msgstr "Opdatere filer"

#: pynicotine/plugins/core_commands/PLUGININFO:3
#, fuzzy
msgid "Nicotine+ Commands"
msgstr "Nicotine+"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Nicotine+-holdet"

#, fuzzy
#~ msgid "Listening port (requires a restart):"
#~ msgstr "Lytteportområde (kræver genstart):"

#, fuzzy
#~ msgid "Network interface (requires a restart):"
#~ msgstr "Lytteportområde (kræver genstart):"

#, fuzzy
#~ msgid "Invalid Password"
#~ msgstr "Password:"

#, fuzzy
#~ msgid "Change _Login Details"
#~ msgstr "Skift logonoplysninger"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i privilegier"

#, fuzzy
#~ msgid "_Set Up…"
#~ msgstr "_Set op…"

#, fuzzy
#~ msgid "Queued search result text color:"
#~ msgstr "Tekstfarve i søgeresultatet i kø:"

#~ msgid "_Clear"
#~ msgstr "Rens"

#~ msgid "_Remove"
#~ msgstr "Fjern"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr ""
#~ "Serveren ser ud til at være nede eller svarar ikke, forsøger igen om %i "
#~ "sekunder"

#~ msgid "--- disconnected ---"
#~ msgstr "--- offline ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- tilslutted ---"

#~ msgid "Join room 'room'"
#~ msgstr "Stig ind i rum 'rum'"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "Tilføj brugeren 'bruger' til din ban-liste"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "Fjern brugeren 'bruger' fra din ban-liste"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "Tilføj brugeren 'bruger' til din ignore-liste"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "Fjern brugeren 'bruger' fra din ignore-liste"

#~ msgid "Show IP for user 'user'"
#~ msgstr "Vis IP-adresse for brugeren 'bruger'"

#~ msgid "Start a new search for 'query'"
#~ msgstr "Begynd søgning efter 'forespørgsel'"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "Søg i tilkoblede rum efter 'forespørgsel'"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "Søg i vennelisten efter 'forespørgsel'"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "Send besked 'besked' til brugeren 'bruger'"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr "Åbn privat chatvindue for brugeren 'bruger'"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "Kunne ikke finde alias (%s)"

#~ msgid "Aliases:"
#~ msgstr "Alias:"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "Fjernede aliaset %(alias)s: %(action)s\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "Kunne ikke finde alias (%(alias)s)\n"

#~ msgid "Add a new alias"
#~ msgstr "Tilføj et nyt alias"

#~ msgid "Remove an alias"
#~ msgstr "Fjern et alias"

#~ msgid "Aborted"
#~ msgstr "Afbrudt"

#, python-format
#~ msgid "You've been mentioned in the %(room)s room"
#~ msgstr "%(room)s kom ind i rummet"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "Kender ikke kommandoen %s"

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "OS-fejl: %s"

#~ msgid "Free slot"
#~ msgstr "Ledig plads"

#~ msgid "Length"
#~ msgstr "Længde"

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "I/O-fejl: %s"

#~ msgid "Protocol:"
#~ msgstr "Protokol:"

#~ msgid "Establishing connection"
#~ msgstr "Opretter forbindelse"

#~ msgid "User:"
#~ msgstr "Bruger:"

#~ msgid "Handler"
#~ msgstr "Håndterere"

#~ msgid "Transfers"
#~ msgstr "Overførsler"

#~ msgid "Categories"
#~ msgstr "Kategorier"

#~ msgid "Handler:"
#~ msgstr "Håndterere:"

#~ msgid "_Privileged"
#~ msgstr "_Priveligiered"

#~ msgid "Comments"
#~ msgstr "Kommentarer"

#~ msgid "Add user 'user' to your user list"
#~ msgstr "Tilføj brugeren 'bruger' til din brugerliste"

#~ msgid "Remove user 'user' from your user list"
#~ msgstr "Fjern brugeren 'bruger' fra din ban-liste"

#~ msgid "Request user info for user 'user'"
#~ msgstr "Hent brugerinfo fra brugeren 'bruger'"

#, python-format
#~ msgid "Can't back config file up, error: %s"
#~ msgstr "Kunne ikke gemme konfigurationsfil: %s"

#, python-format
#~ msgid "Can't rename config file, error: %s"
#~ msgstr "Kunne ikke gemme konfigurationsfil, I/O-fel: %s"

#, python-format
#~ msgid "Connection closed by peer: %s"
#~ msgstr "Forbindelsen lukket af anden bruger: %s"

#, python-format
#~ msgid ""
#~ "Server reported port 0 for the 10th time for user %(user)s, giving up"
#~ msgstr ""
#~ "Serveren rapporterede port 0 for bruger %(user)s for tiende gang, gir' op"

#, python-format
#~ msgid ""
#~ "Server reported non-zero port for user %(user)s after %(tries)i retries"
#~ msgstr ""
#~ "Serveren rapporterede ikke-nul-port for bruger %(user)s efter %(tries)i "
#~ "forsøg"

#, python-format
#~ msgid "Server reported port 0 for user %(user)s, retrying"
#~ msgstr "Serveren rapporterede port 0 for bruger %(user)s, forsøger igen"

#, python-format
#~ msgid "Can not log in, reason: %s"
#~ msgstr "Kan ikke logge ind, årsag: %s"

#~ msgid ""
#~ "Someone else is logging in with the same nickname, server is going to "
#~ "disconnect us"
#~ msgstr ""
#~ "Nogen anden logger in med samme brugernavn, Serveren lukker din "
#~ "forbindelse"

#, python-format
#~ msgid "Unknown tunneled message: %s"
#~ msgstr "Ukendt besked: %s"

#~ msgid "Shared files database seems to be corrupted, rescan your shares"
#~ msgstr ""
#~ "Databasen for delede filer virker beskadiget, omindexer dine delede filer"

#, python-format
#~ msgid "Empty message made, class %s"
#~ msgstr "Skabte et tomt besked, class %s"

#, python-format
#~ msgid "Can't parse incoming messages, class %s"
#~ msgstr "Kunne ikke tolke indkommande besked, class %s"

#, python-format
#~ msgid "Can't handle connection type %s"
#~ msgstr "Kunne ikke håndtere tilslutningstypen %s"

#, python-format
#~ msgid ""
#~ "Can't send the message over the closed connection: %(type)s %(msg_obj)s"
#~ msgstr ""
#~ "Kunne ikke sende besked over lukket forbindels: %(type)s %(msg_obj)s"

#, python-format
#~ msgid "Filtering: %s"
#~ msgstr "Filtrere: %s"

#, python-format
#~ msgid "Retrying failed download: user %(user)s, file %(file)s"
#~ msgstr "Pröver mislykked download igen: bruger %(user)s, fil %(file)s"

#, python-format
#~ msgid "Got transfer request %s but cannot determine requestor"
#~ msgstr "Overførsels begæring modtaget %s, men kunne ikke bestemme modpart"

#, python-format
#~ msgid "Got unknown transfer response: %s"
#~ msgstr "Modtog okendt overførselssvar: %s"

#, python-format
#~ msgid "Download finished: %(file)s"
#~ msgstr "Download færdig: fil %(file)s"

#~ msgid "(friend)"
#~ msgstr "(ven)"

#, python-format
#~ msgid "Upload finished: %(user)s, file %(file)s"
#~ msgstr "Mislykked download: bruger %(user)s, fil %(file)s"

#~ msgid "Get user i_nfo"
#~ msgstr "Vis brugeri_nfo"

#~ msgid "_Add user to list"
#~ msgstr "Tilføj brugeren til vennelisten"

#~ msgid "_Ban this user"
#~ msgstr "_Banne denne bruger"

#~ msgid "_Ignore this user"
#~ msgstr "_Ignorer denne bruger"

#~ msgid "Clear aborted"
#~ msgstr "Rens afbrudne"

#~ msgid "Clear queued"
#~ msgstr "Rens køede"

#~ msgid "Abor_t"
#~ msgstr "Afbryd"

#~ msgid "Directory"
#~ msgstr "Mappe"

#~ msgid "User info"
#~ msgstr "Brugerinfo"

#~ msgid "Rescanning Buddy Shares finished"
#~ msgstr "Indexering færdig"

#~ msgid "Rescanning finished"
#~ msgstr "Indexering færdig"

#~ msgid "I like"
#~ msgstr "Jeg kan li'"

#~ msgid "I _don't like this"
#~ msgstr "Jeg kan ikke li' _dette"

#~ msgid "Ban this user"
#~ msgstr "Banne denne bruger"

#~ msgid "Ignore this user"
#~ msgstr "Ignorer denne bruger"

#~ msgid "In queue"
#~ msgstr "Køplads"

#~ msgid "Ignore user..."
#~ msgstr "Ignorerade brugere:"

#~ msgid "Ban user..."
#~ msgstr "Banne denne bruger"

#~ msgid "Server"
#~ msgstr "Server"

#~ msgid "Geo Block"
#~ msgstr "Geo-blok"

#~ msgid "URL Catching"
#~ msgstr "URL-fangst"

#~ msgid "Initializing transfer"
#~ msgstr "Initialiserer overforsel"

#~ msgid "Waiting for peer to connect"
#~ msgstr "Venter på forbindelse fra anden bruger"

#~ msgid "Getting address"
#~ msgstr "Henter adresse"

#~ msgid "Directories"
#~ msgstr "Mappe"

#~ msgid "Download r_ecursive to..."
#~ msgstr "Hent r_ekursivt til..."

#~ msgid "Upload Directory to..."
#~ msgstr "Hent Mappe _til..."

#, python-format
#~ msgid "Total uploads allowed: %i"
#~ msgstr "Totalt antal tilladte uploads: %i"

#, python-format
#~ msgid "Slots free: %s"
#~ msgstr "Uploadforspørgsel: %s"

#~ msgid "Log"
#~ msgstr "Logfil"

#~ msgid "Total uploads allowed: unknown"
#~ msgstr "Total antal tilladte uploads: ukendt"

#~ msgid "Slots free: unknown"
#~ msgstr "Pladser frie: ukendt"

#~ msgid "Queue size: unknown"
#~ msgstr "Længde på kø: ukendt"

#~ msgid "Files: unknown"
#~ msgstr "Filer: ukendt"

#~ msgid "Directories: unknown"
#~ msgstr "Mappe: ukendt"

#~ msgid "Add..."
#~ msgstr "Tilføj..."

#~ msgid "About search filters"
#~ msgstr "Om søgefilter"

#~ msgid "_Private Chat"
#~ msgstr "_Privat chat"

#~ msgid "_Interests"
#~ msgstr "_Interesser"

#~ msgid "About _chat room commands"
#~ msgstr "Om kommandon for _chattrum"

#~ msgid "About _private chat commands"
#~ msgstr "Om kommandon for _private chattrum"

#~ msgid "About _search filters"
#~ msgstr "Om _søgefilter"

#~ msgid "Toggle away after "
#~ msgstr "Skift till away efter "

#~ msgid "Decimal separator:"
#~ msgstr "Separere decimaler med:"

#~ msgid "Enable geographical blocker"
#~ msgstr "Aktivera geografisk blokerere"

#~ msgid "Geographical paranoia (block unresolvable IPs)"
#~ msgstr "Geografisk paranoia (blokera ikke-identificerede IP-nummer)"

#~ msgid "Send out a max of"
#~ msgstr "Returnere max."

#~ msgid "Filter out:"
#~ msgstr "Filtrere ud:"

#~ msgid "Filter in:"
#~ msgstr "Filtrere ind:"

#~ msgid "KBytes/sec"
#~ msgstr "KB/s"

#~ msgid "Megabytes"
#~ msgstr "MB"

#~ msgid "Enable URL catching"
#~ msgstr "Aktiver URL-fangst"

#~ msgid "Humanize slsk:// urls"
#~ msgstr "Humanisere 'slsk://'-URL:er"
