# SPDX-FileCopyrightText: 2023-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2024-10-31 19:00+0000\n"
"Last-Translator: Bugmenot <<EMAIL>>\n"
"Language-Team: Romanian <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/ro/>\n"
"Language: ro\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=n==1 ? 0 : (n==0 || (n%100 > 0 && n%100 < "
"20)) ? 1 : 2;\n"
"X-Generator: Weblate 5.8.2-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr "Client Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Client grafic pentru rețeaua peer-to-peer Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr "Soulseek;Nicotine;partajare;mesagerie;P2P;peer-to-peer;GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
#, fuzzy
msgid "Browse the Soulseek network"
msgstr "Client grafic pentru rețeaua Soulseek"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+ este un client grafic pentru rețeaua peer-to-peer Soulseek."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
#, fuzzy
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ își propune să fie o alternativă plăcută, gratuită și cu sursă "
"deschisă (FOSS) la clientul oficial Soulseek, oferind funcționalități "
"suplimentare, fiind în același timp la curent cu protocolul Soulseek."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
#, fuzzy
msgid "Search Files"
msgstr "Cauta fișiere"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Descărcări"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
#, fuzzy
msgid "Browse Shares"
msgstr "Răsfoiți acțiuni"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
#, fuzzy
msgid "Private Chat"
msgstr "Chat privat"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Echipa Nicotine+"

#: pynicotine/__init__.py:50
#, python-format
msgid "Website: %s"
msgstr "Site web: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "afișați acest mesaj de ajutor și ieșiți"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "fișier"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr "folosește fișier de configurație non-implicit"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "dir"

#: pynicotine/__init__.py:64
msgid "alternative directory for user data and plugins"
msgstr "folder alternativ pentru datele utilizatorului și pluginuri"

#: pynicotine/__init__.py:68
msgid "start the program without showing window"
msgstr "pornește programul fără afișarea ferestrei"

#: pynicotine/__init__.py:71
#, fuzzy
msgid "ip"
msgstr "ip"

#: pynicotine/__init__.py:72
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "legați socketurile la IP-ul dat (util pentru VPN)"

#: pynicotine/__init__.py:75
#, fuzzy
msgid "port"
msgstr "port"

#: pynicotine/__init__.py:76
msgid "listen on the given port"
msgstr "ascultă pe portul dat"

#: pynicotine/__init__.py:80
msgid "rescan shared files"
msgstr "rescanați fișierele partajate"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr "porniți programul în modul headless (fără GUI)"

#: pynicotine/__init__.py:88
#, fuzzy
msgid "display version and exit"
msgstr "afișează versiunea și iese"

#: pynicotine/__init__.py:121
#, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Folosiți o versiune nesuportată de Python (%(old_version)s).\n"
"Ar trebui să instalați Python %(min_version)s sau mai nou."

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Nu s-au putut scana partajările. Va rog să închideți alte instanțe Nicotine+ "
"și încercați din nou."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, fuzzy, python-format
msgid "%(user)s is away"
msgstr "%(user)s este plecat"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, fuzzy, python-format
msgid "%(user)s is online"
msgstr "%(user)s este online"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, fuzzy, python-format
msgid "%(user)s is offline"
msgstr "%(user)s este offline"

#: pynicotine/buddies.py:316
#, fuzzy
msgid "Buddy Status"
msgstr "Stare de prieten"

#: pynicotine/chatrooms.py:383
#, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Ați fost adăugat în camera privată %(room)s"

#: pynicotine/chatrooms.py:478
#, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr ""
"Mesaj de la utilizatorul \"%(user)s\" în camera \"%(room)s\": %(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr "Nu s-a putut crea folder-ul \"%(path)s\", eroare raportată: %(error)s"

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr "Eroare la backup-ul configurației: %s"

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr "Backup configurației la: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Încărcare %(program)s %(version)s"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Ieșire %(program)s %(version)s, %(status)s…"

#: pynicotine/core.py:246
msgid "terminating"
msgstr "se termină"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "aplicația se închide"

#: pynicotine/core.py:259
#, fuzzy, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Încărcare %(program)s %(version)s"

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr ""
"Trebuie să specificați un nume de utilizator și o parolă înainte de "
"conectare…"

#: pynicotine/downloads.py:239
#, fuzzy, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""
"Eroare: Filtrul de descărcare a eșuat! Verificați-vă filtrele. Motiv: %s"

#: pynicotine/downloads.py:254
#, fuzzy, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Eroare: %(num)d Descărcarea filtrelor a eșuat! %(error)s "

#: pynicotine/downloads.py:366
#, fuzzy, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s descărcat de pe %(user)s"

#: pynicotine/downloads.py:370
#, fuzzy
msgid "File Downloaded"
msgstr "Fișier descărcat"

#: pynicotine/downloads.py:378
#, fuzzy, python-format
msgid "Executed: %s"
msgstr "Executat: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, fuzzy, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "Executarea „%(command)s” a eșuat: %(error)s"

#: pynicotine/downloads.py:407
#, fuzzy, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s descărcat de pe %(user)s"

#: pynicotine/downloads.py:411
#, fuzzy
msgid "Folder Downloaded"
msgstr "Dosar descărcat"

#: pynicotine/downloads.py:419
#, fuzzy, python-format
msgid "Executed on folder: %s"
msgstr "Execut în folder: %s"

#: pynicotine/downloads.py:443
#, fuzzy, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "Nu s-a putut muta „%(tempfile)s” în „%(file)s”: %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
#, fuzzy
msgid "Download Folder Error"
msgstr "Eroare de descărcare a folderului"

#: pynicotine/downloads.py:489
#, fuzzy, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Descărcare finalizată: utilizator %(user)s, fișier %(file)s"

#: pynicotine/downloads.py:499
#, fuzzy, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Descărcare anulată, fișierul utilizatorului %(user)s %(file)s"

#: pynicotine/downloads.py:1150
#, fuzzy, python-format
msgid "Download I/O error: %s"
msgstr "Eroare de descărcare I/O: %s"

#: pynicotine/downloads.py:1189
#, fuzzy, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Nu se poate obține o blocare exclusivă a fișierului - eroare I/O: %s"

#: pynicotine/downloads.py:1202
#, fuzzy, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Nu se poate salva fișierul în %(folder_path)s: %(error)s"

#: pynicotine/downloads.py:1221
#, fuzzy, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Descărcarea a început: utilizator %(user)s, fișier %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, fuzzy, python-format
msgid "Cannot find %s, please install it."
msgstr "Nu s-a putut găsi %s, vă rog să instalați componenta lipsă."

#: pynicotine/gtkgui/__init__.py:162
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr "Nu există un mediu grafic, folosind modul headless (fără GUI)"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
#, fuzzy
msgid "_Connect"
msgstr "_Conectați"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
#, fuzzy
msgid "_Disconnect"
msgstr "_Deconectat"

#: pynicotine/gtkgui/application.py:308
#, fuzzy
msgid "Soulseek _Privileges"
msgstr "Soulseek _Privilegii"

#: pynicotine/gtkgui/application.py:314
#, fuzzy
msgid "_Preferences"
msgstr "_Preferințe"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
msgid "_Quit"
msgstr "_Ieși"

#: pynicotine/gtkgui/application.py:337
#, fuzzy
msgid "Browse _Public Shares"
msgstr "_Răsfoiți acțiuni publice"

#: pynicotine/gtkgui/application.py:338
#, fuzzy
msgid "Browse _Buddy Shares"
msgstr "Răsfoiește acțiunile lui Buddy"

#: pynicotine/gtkgui/application.py:339
#, fuzzy
msgid "Browse _Trusted Shares"
msgstr "Răsfoiește acțiunile lui Buddy"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
#, fuzzy
msgid "_Rescan Shares"
msgstr "_Rescanați acțiunile"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
#, fuzzy
msgid "Configure _Shares"
msgstr "Configurați partajări"

#: pynicotine/gtkgui/application.py:371
#, fuzzy
msgid "_Keyboard Shortcuts"
msgstr "_Comenzi rapide de la tastatură"

#: pynicotine/gtkgui/application.py:372
#, fuzzy
msgid "_Setup Assistant"
msgstr "_Setup Assistant"

#: pynicotine/gtkgui/application.py:373
#, fuzzy
msgid "_Transfer Statistics"
msgstr "_Statistici de transfer"

#: pynicotine/gtkgui/application.py:378
#, fuzzy
msgid "Report a _Bug"
msgstr "Raporteaza o eroare"

#: pynicotine/gtkgui/application.py:379
#, fuzzy
msgid "Improve T_ranslations"
msgstr "Îmbunătățiți traducerile"

#: pynicotine/gtkgui/application.py:383
#, fuzzy
msgid "_About Nicotine+"
msgstr "_Despre Nicotina+"

#: pynicotine/gtkgui/application.py:394
#, fuzzy
msgid "_File"
msgstr "_Fişier"

#: pynicotine/gtkgui/application.py:395
#, fuzzy
msgid "_Shares"
msgstr "_Acțiuni"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
#, fuzzy
msgid "_Help"
msgstr "_Ajutor"

#: pynicotine/gtkgui/application.py:410
#, fuzzy
msgid "_Browse Shares"
msgstr "Răsfoiți acțiuni"

#: pynicotine/gtkgui/application.py:465
#, fuzzy, python-format
msgid "Unable to show notification: %s"
msgstr "Nu se poate afișa notificarea: %s"

#: pynicotine/gtkgui/application.py:526
#, fuzzy
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Ești sigur că vrei să ieși?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr "Așteaptă finalizarea încărcărilor"

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr "Ești sigur că vrei să ieși?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
#, fuzzy
msgid "_No"
msgstr "_Nu"

#: pynicotine/gtkgui/application.py:535
msgid "_Run in Background"
msgstr "_Rulează în fundal"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr "Ieși Nicotine+"

#: pynicotine/gtkgui/application.py:561
msgid "Shares Not Available"
msgstr "Partajări nevalabile"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Verificați că diskurile externe sunt montate și permisiunile folderelor sunt "
"corecte."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
msgid "_Cancel"
msgstr "_Anulează"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "_Reîncearcă"

#: pynicotine/gtkgui/application.py:567
msgid "_Force Rescan"
msgstr "_Re-scanează forțat"

#: pynicotine/gtkgui/application.py:742
msgid "Message Downloading Users"
msgstr "Trimite mesaj utilizatorilor care descarcă"

#: pynicotine/gtkgui/application.py:743
msgid "Send private message to all users who are downloading from you:"
msgstr ""
"Trimite un mesaj privat tuturor utilizatorilor care descarcă de la tine:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
msgid "_Send Message"
msgstr "_Trimite mesaj"

#: pynicotine/gtkgui/application.py:756
msgid "Message Buddies"
msgstr "Trimite mesaj prietenilor"

#: pynicotine/gtkgui/application.py:757
msgid "Send private message to all online buddies:"
msgstr "Trimite mesaj privat către toți prietenii online:"

#: pynicotine/gtkgui/application.py:786
msgid "Select a Saved Shares List File"
msgstr "Selectați un fișier salvat cu listă de partajări"

#: pynicotine/gtkgui/application.py:877
msgid "Critical Error"
msgstr "Eroare Critică"

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ a întâmpinat o eroare critică și trebuie să iasă. Vă rog să "
"copiați acest mesaj și includeți-l într-un raport de eroare:"

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr "_Ieșiti Nicotine+"

#: pynicotine/gtkgui/application.py:883
msgid "_Copy & Report Bug"
msgstr "_Copiați & Raportați eroare"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Stare"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr "Țară"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Utilizator"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Viteză"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Fișiere"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
#, fuzzy
msgid "Trusted"
msgstr "De încredere"

#: pynicotine/gtkgui/buddies.py:108
#, fuzzy
msgid "Notify"
msgstr "Notifică"

#: pynicotine/gtkgui/buddies.py:114
#, fuzzy
msgid "Prioritized"
msgstr "Prioritizate"

#: pynicotine/gtkgui/buddies.py:120
#, fuzzy
msgid "Last Seen"
msgstr "Vazut ultima data"

#: pynicotine/gtkgui/buddies.py:126
#, fuzzy
msgid "Note"
msgstr "Notă"

#: pynicotine/gtkgui/buddies.py:143
#, fuzzy
msgid "Add User _Note…"
msgstr "Adăugați utilizator _Notă…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
#, fuzzy
msgid "Remove"
msgstr "Elimina"

#: pynicotine/gtkgui/buddies.py:364
#, fuzzy
msgid "Never seen"
msgstr "Nemaivăzut"

#: pynicotine/gtkgui/buddies.py:529
#, fuzzy
msgid "Add User Note"
msgstr "Adăugați o notă de utilizator"

#: pynicotine/gtkgui/buddies.py:530
#, fuzzy, python-format
msgid "Add a note about user %s:"
msgstr "Adăugați o notă despre utilizatorul %s:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
#, fuzzy
msgid "_Add"
msgstr "_Adăuga"

#: pynicotine/gtkgui/chatrooms.py:224
msgid "Create New Room?"
msgstr "Creare cameră nouă?"

#: pynicotine/gtkgui/chatrooms.py:225
#, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Chiar doriți să creați camera nouă \"%s\"?"

#: pynicotine/gtkgui/chatrooms.py:226
msgid "Make room private"
msgstr "Faceți camera privată"

#: pynicotine/gtkgui/chatrooms.py:515
#, fuzzy
msgid "Search activity log…"
msgstr "Termen de căutare…"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
#, fuzzy
msgid "Search chat log…"
msgstr "Termen de căutare…"

#: pynicotine/gtkgui/chatrooms.py:597
msgid "Sear_ch User's Files"
msgstr "Căuta_ți Fișierele Utilizatorului"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
msgid "Find…"
msgstr "Găsește…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Copiază"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr "Copiază tot"

#: pynicotine/gtkgui/chatrooms.py:608
msgid "Clear Activity View"
msgstr "Golește vizualizarea activității"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
msgid "_Leave Room"
msgstr "_Părăsește camera"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
msgid "Copy Link"
msgstr "Copiază link"

#: pynicotine/gtkgui/chatrooms.py:624
msgid "View Room Log"
msgstr "Vezi istoric cameră"

#: pynicotine/gtkgui/chatrooms.py:627
msgid "Delete Room Log…"
msgstr "Ștergeți istoric cameră…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
msgid "Clear Message View"
msgstr "Goliți vizualizare mesaje"

#: pynicotine/gtkgui/chatrooms.py:832
#, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s te-a menționat în camera %(room)s"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "Menționat de %(user)s în camera %(room)s"

#: pynicotine/gtkgui/chatrooms.py:855
#, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "Mesaj de %(user)s în camera %(room)s"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s a intrat în cameră"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s a ieșit din cameră"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s a plecat"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s s-a întors"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
msgid "Delete Logged Messages?"
msgstr "Ștergeți istoric mesaje?"

#: pynicotine/gtkgui/chatrooms.py:1197
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"Chiar doriți să ștergeți permanent tot istoricul de mesaje din această "
"cameră?"

#: pynicotine/gtkgui/dialogs/about.py:405
msgid "About"
msgstr "Despre"

#: pynicotine/gtkgui/dialogs/about.py:415
#, fuzzy
msgid "Website"
msgstr "Site web: %s"

#: pynicotine/gtkgui/dialogs/about.py:457
#, fuzzy, python-format
msgid "Error checking latest version: %s"
msgstr "Nu s-a putut retrage cea mai recentă versiune: %s"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr "Versiune nouă disponibilă: %s"

#: pynicotine/gtkgui/dialogs/about.py:467
#, fuzzy
msgid "Up to date"
msgstr "Actualizat"

#: pynicotine/gtkgui/dialogs/about.py:496
#, fuzzy
msgid "Checking latest version…"
msgstr "Verificați _Ultima versiune"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
msgid "Setup Assistant"
msgstr "Asistent de configurare"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
msgid "Virtual Folder"
msgstr "Folder virtual"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
#, fuzzy
msgid "Folder"
msgstr "Pliant"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#, fuzzy
msgid "_Previous"
msgstr "_Anterior"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
msgid "_Finish"
msgstr "_Finalizare"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
msgid "_Next"
msgstr "_Următorul"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
msgid "Add a Shared Folder"
msgstr "Adăugați folder partajat"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
msgid "Edit Shared Folder"
msgstr "Modificați folder partajat"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Introduceți un nume virtual nou pentru \"%(dir)s\":"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
#, fuzzy
msgid "_Edit"
msgstr "Editați | ×…"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"Utilizatorul %s deja există și parola introdusă este greșită. Vă rog să "
"alegeți alt nume de utilizator dacă aceasta este prima oară când vă "
"conectați."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
msgid "File Properties"
msgstr "Proprietăți fișier"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "Proprietăți fișier (%(num)i din %(total)i  /  %(size)s  /  %(length)s)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Proprietăți fișier (%(num)i of %(total)i  /  %(size)s)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
#, fuzzy
msgid "_Apply"
msgstr "_Aplica"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
#, fuzzy
msgid "Add…"
msgstr "Adăuga…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
#, fuzzy
msgid "Edit…"
msgstr "Editați | ×…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, fuzzy, python-format
msgid "%s Settings"
msgstr "%s Setări"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
#, fuzzy
msgid "Add Item"
msgstr "Articol"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
#, fuzzy
msgid "Edit Item"
msgstr "Editați interesele"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "Necunoscut"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
msgid "Check Port Status"
msgstr "Verifică stare port"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, fuzzy, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, portul %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
msgid "Password Change Rejected"
msgstr "Schimbare parolă respinsă"

#: pynicotine/gtkgui/dialogs/preferences.py:218
msgid "Enter a new password for your Soulseek account:"
msgstr "Introduceți o parolă nouă pentru contul dvs. de Soulseek:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
#, fuzzy
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"În prezent, sunteți deconectat din rețeaua Soulseek. Dacă doriți să "
"schimbați parola unui cont Soulseek existent, trebuie să fiți conectat la "
"acel cont."

#: pynicotine/gtkgui/dialogs/preferences.py:223
#, fuzzy
msgid "Enter password to use when logging in:"
msgstr "Introduceți parola de utilizat când vă conectați:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
#, fuzzy
msgid "Change Password"
msgstr "Schimbaţi parola"

#: pynicotine/gtkgui/dialogs/preferences.py:230
#, fuzzy
msgid "_Change"
msgstr "Fila schimbată"

#: pynicotine/gtkgui/dialogs/preferences.py:274
#, fuzzy
msgid "No one"
msgstr "Nici unul"

#: pynicotine/gtkgui/dialogs/preferences.py:275
#, fuzzy
msgid "Everyone"
msgstr "Toata lumea"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
#, fuzzy
msgid "Buddies"
msgstr "Prieteni"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
#, fuzzy
msgid "Trusted buddies"
msgstr "Prieteni de încredere"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
#, fuzzy
msgid "Nothing"
msgstr "Nimic"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
#, fuzzy
msgid "Open File"
msgstr "Fișierul următor"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
#, fuzzy
msgid "Open in File Manager"
msgstr "Deschideți în File Manager"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
#, fuzzy
msgid "Search"
msgstr "Căutare"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
#, fuzzy
msgid "Pause"
msgstr "Pauză"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
#, fuzzy
msgid "Resume"
msgstr "Relua"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
#, fuzzy
msgid "Browse Folder"
msgstr "Răsfoiți folderul"

#: pynicotine/gtkgui/dialogs/preferences.py:317
#, fuzzy
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Sintaxă</b>: nu ține cont de majuscule și minuscule. Dacă este activată, "
"expresiile regulate Python pot fi folosite, altfel sunt acceptate numai "
"potrivirile cu caractere joker *."

#: pynicotine/gtkgui/dialogs/preferences.py:327
#, fuzzy
msgid "Filter"
msgstr "Filtru"

#: pynicotine/gtkgui/dialogs/preferences.py:334
#, fuzzy
msgid "Regex"
msgstr "Regex"

#: pynicotine/gtkgui/dialogs/preferences.py:468
#, fuzzy
msgid "Add Download Filter"
msgstr "Adăugați filtru de descărcare"

#: pynicotine/gtkgui/dialogs/preferences.py:469
#, fuzzy
msgid "Enter a new download filter:"
msgstr "Introduceți un nou filtru de descărcare:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
#, fuzzy
msgid "Enable regular expressions"
msgstr "Activați expresiile regulate"

#: pynicotine/gtkgui/dialogs/preferences.py:498
#, fuzzy
msgid "Edit Download Filter"
msgstr "Editați filtrul de descărcare"

#: pynicotine/gtkgui/dialogs/preferences.py:499
#, fuzzy
msgid "Modify the following download filter:"
msgstr "Modificați următorul filtru de descărcare:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, fuzzy, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d Eșuat! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
#, fuzzy
msgid "Filters Successful"
msgstr "Filtre de succes"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr "Public"

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr "Accesibil pentru"

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
#, fuzzy
msgid "Abort"
msgstr "Avorta"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
#, fuzzy
msgid "Retry"
msgstr "Reîncercați"

#: pynicotine/gtkgui/dialogs/preferences.py:828
#, fuzzy
msgid "Round Robin"
msgstr "Round Robin"

#: pynicotine/gtkgui/dialogs/preferences.py:829
#, fuzzy
msgid "First In, First Out"
msgstr "Primul intrat, primul ieşit"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
#, fuzzy
msgid "Username"
msgstr "Nume de utilizator"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
msgid "IP Address"
msgstr "Adresă IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
#, fuzzy
msgid "Ignore User"
msgstr "Ignorați utilizatorul"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
#, fuzzy
msgid "Enter the name of the user you want to ignore:"
msgstr "Introduceți numele utilizatorului pe care doriți să-l ignorați:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
#, fuzzy
msgid "Ignore IP Address"
msgstr "Ignorați adresa IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#, fuzzy
msgid "Enter an IP address you want to ignore:"
msgstr "Introduceți o adresă IP pe care doriți să o ignorați:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
#, fuzzy
msgid "* is a wildcard"
msgstr "* este un wildcard"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
#, fuzzy
msgid "Ban User"
msgstr "Ban utilizator"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
#, fuzzy
msgid "Enter the name of the user you want to ban:"
msgstr "Introduceți numele utilizatorului pe care doriți să-l interziceți:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
#, fuzzy
msgid "Ban IP Address"
msgstr "Interziceți adresa IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
#, fuzzy
msgid "Enter an IP address you want to ban:"
msgstr "Introduceți o adresă IP pe care doriți să o interziceți:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
#, fuzzy
msgid "Pattern"
msgstr "Model"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
#, fuzzy
msgid "Replacement"
msgstr "Înlocuire"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
#, fuzzy
msgid "Censor Pattern"
msgstr "Model de cenzură"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
#, fuzzy
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Introduceți un model pe care doriți să-l cenzurați. Adăugați spații în jurul "
"modelului dacă nu doriți să potriviți șiruri în interiorul cuvintelor (poate "
"eșua la începutul și la sfârșitul rândurilor)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
#, fuzzy
msgid "Edit Censored Pattern"
msgstr "Editați modelul cenzurat"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
#, fuzzy
msgid "Add Replacement"
msgstr "Adăugați înlocuitor"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
#, fuzzy
msgid "Enter a text pattern and what to replace it with:"
msgstr "Introduceți un model de text și cu ce să îl înlocuiți:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
#, fuzzy
msgid "Edit Replacement"
msgstr "Editați înlocuirea"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
#, fuzzy
msgid "System default"
msgstr "Defectiune de sistem"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
#, fuzzy
msgid "Show confirmation dialog"
msgstr "Afișează dialogul de confirmare"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
#, fuzzy
msgid "Run in the background"
msgstr "Alergați în fundal"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
#, fuzzy
msgid "bold"
msgstr "îndrăzneţ"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
#, fuzzy
msgid "italic"
msgstr "cursiv"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
#, fuzzy
msgid "underline"
msgstr "subliniază"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
#, fuzzy
msgid "normal"
msgstr "normal"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
#, fuzzy
msgid "Separate Buddies tab"
msgstr "Trimite mesaj prietenilor"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
#, fuzzy
msgid "Sidebar in Chat Rooms tab"
msgstr "Lista de prieteni în camerele de chat"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr "Bară laterală mereu vizibilă"

#: pynicotine/gtkgui/dialogs/preferences.py:1777
#, fuzzy
msgid "Top"
msgstr "Top"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
#, fuzzy
msgid "Bottom"
msgstr "Fund"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
#, fuzzy
msgid "Left"
msgstr "Stânga"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
#, fuzzy
msgid "Right"
msgstr "Dreapta"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
#, fuzzy
msgid "Online"
msgstr "Pe net"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
#, fuzzy
msgid "Away"
msgstr "Departe"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
#, fuzzy
msgid "Offline"
msgstr "Deconectat"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
#, fuzzy
msgid "Tab Changed"
msgstr "Fila schimbată"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
#, fuzzy
msgid "Tab Highlight"
msgstr "Evidențiați fila"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
#, fuzzy
msgid "Window"
msgstr "Fereastră"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
#, fuzzy
msgid "Online (Tray)"
msgstr "Online (tavă)"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
#, fuzzy
msgid "Away (Tray)"
msgstr "Away (Tavă)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
#, fuzzy
msgid "Offline (Tray)"
msgstr "Offline (tavă)"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
#, fuzzy
msgid "Message (Tray)"
msgstr "Mesaj (tava)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
#, fuzzy
msgid "Protocol"
msgstr "Protocol"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
#, fuzzy
msgid "Command"
msgstr "Comanda"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
#, fuzzy
msgid "Add URL Handler"
msgstr "Adăugați un handler URL"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
#, fuzzy
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Introduceți protocolul și comanda pentru handler-ul URL:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
#, fuzzy
msgid "Edit Command"
msgstr "Comanda Editare"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, fuzzy, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Introduceți o nouă comandă pentru protocolul %s:"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
#, fuzzy
msgid "Username;APIKEY"
msgstr "Nume utilizator;APIKEY:"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
#, fuzzy
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr ""
"Music player (de exemplu, amarok, audacious, exaile); lăsați gol pentru a "
"detecta automat:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
#, fuzzy
msgid "Username: "
msgstr "Nume de utilizator:"

#: pynicotine/gtkgui/dialogs/preferences.py:2767
#, fuzzy
msgid "Command:"
msgstr "Comanda:"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
#, fuzzy
msgid "Title"
msgstr "Titlu"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, fuzzy, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "În curs de redare (de obicei „%(artist)s - %(title)s”)"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
#, fuzzy
msgid "Artist"
msgstr "Artist"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
#, fuzzy
msgid "Duration"
msgstr "Durată"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
#, fuzzy
msgid "Bitrate"
msgstr "Rata de biți"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
#, fuzzy
msgid "Comment"
msgstr "cometariu"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
#, fuzzy
msgid "Album"
msgstr "Album"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
#, fuzzy
msgid "Track Number"
msgstr "Numărul piesei"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
#, fuzzy
msgid "Year"
msgstr "An"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
#, fuzzy
msgid "Filename (URI)"
msgstr "Nume fișier (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
#, fuzzy
msgid "Program"
msgstr "Program"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
#, fuzzy
msgid "Enabled"
msgstr "Activat"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
#, fuzzy
msgid "Plugin"
msgstr "Conecteaza"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
#, fuzzy
msgid "No Plugin Selected"
msgstr "Niciun plugin selectat"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
#, fuzzy
msgid "Preferences"
msgstr "Preferințe"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
msgid "Network"
msgstr "Rețea"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
msgid "User Interface"
msgstr "Interfață utilizator"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Partajări"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Încărcări"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Căutări"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
msgid "User Profile"
msgstr "Profil utilizator"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
msgid "Chats"
msgstr "Conversații"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
#, fuzzy
msgid "Now Playing"
msgstr "Acum se joacă"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
#, fuzzy
msgid "Logging"
msgstr "Logare"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
msgid "Banned Users"
msgstr "Utilizatori interziși"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
msgid "Ignored Users"
msgstr "Utilizatori ignorați"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
#, fuzzy
msgid "URL Handlers"
msgstr "Gestionare URL"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr "Plugin-uri"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
#, fuzzy
msgid "Pick a File Name for Config Backup"
msgstr "Alegeți un nume de fișier pentru Config Backup"

#: pynicotine/gtkgui/dialogs/statistics.py:75
#, fuzzy
msgid "Transfer Statistics"
msgstr "Statistici de transfer"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, fuzzy, python-format
msgid "Total Since %(date)s"
msgstr "Total de la %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
#, fuzzy
msgid "Reset Transfer Statistics?"
msgstr "Resetați statisticile de transfer?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
#, fuzzy
msgid "Do you really want to reset transfer statistics?"
msgstr "Chiar doriți să resetați statisticile de transfer?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
#, fuzzy
msgid "Wishlist"
msgstr "lista de dorințe"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
#, fuzzy
msgid "Wish"
msgstr "Dori"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
#, fuzzy
msgid "_Search for Item"
msgstr "_Căutați articol"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
#, fuzzy
msgid "Edit Wish"
msgstr "Editează dorința"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, fuzzy, python-format
msgid "Enter new value for wish '%s':"
msgstr "Introduceți o nouă valoare pentru dorința „%s”:"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
#, fuzzy
msgid "Clear Wishlist?"
msgstr "Ștergeți lista de dorințe?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
#, fuzzy
msgid "Do you really want to clear your wishlist?"
msgstr "Chiar vrei să-ți ștergi lista de dorințe?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
#, fuzzy
msgid "Path"
msgstr "cale"

#: pynicotine/gtkgui/downloads.py:47
#, fuzzy
msgid "_Resume"
msgstr "_Relua"

#: pynicotine/gtkgui/downloads.py:48
#, fuzzy
msgid "P_ause"
msgstr "Pauză"

#: pynicotine/gtkgui/downloads.py:72
#, fuzzy
msgid "Finished / Filtered"
msgstr "Terminat / filtrat"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
#, fuzzy
msgid "Finished"
msgstr "Terminat"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
#, fuzzy
msgid "Paused"
msgstr "Întrerupt"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
#, fuzzy
msgid "Filtered"
msgstr "Filtrată"

#: pynicotine/gtkgui/downloads.py:77
#, fuzzy
msgid "Deleted"
msgstr "Șters"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
#, fuzzy
msgid "Queued…"
msgstr "În așteptare…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
#, fuzzy
msgid "Everything…"
msgstr "Tot…"

#: pynicotine/gtkgui/downloads.py:132
#, fuzzy, python-format
msgid "Downloads: %(speed)s"
msgstr "Descărcări: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
#, fuzzy
msgid "Clear Queued Downloads"
msgstr "Ștergeți descărcări în coadă"

#: pynicotine/gtkgui/downloads.py:139
#, fuzzy
msgid "Do you really want to clear all queued downloads?"
msgstr "Chiar doriți să ștergeți toate descărcările din coadă?"

#: pynicotine/gtkgui/downloads.py:151
#, fuzzy
msgid "Clear All Downloads"
msgstr "Ștergeți toate descărcările"

#: pynicotine/gtkgui/downloads.py:152
#, fuzzy
msgid "Do you really want to clear all downloads?"
msgstr "Chiar doriți să ștergeți toate descărcările?"

#: pynicotine/gtkgui/downloads.py:169
#, fuzzy, python-format
msgid "Download %(num)i files?"
msgstr "Descărcați fișierele %(num)i?"

#: pynicotine/gtkgui/downloads.py:170
#, fuzzy, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"Chiar doriți să descărcați fișiere %(num)i din folderul %(folder)s al "
"%(user)s?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
#, fuzzy
msgid "_Download Folder"
msgstr "_Descărcați folderul"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
#, fuzzy
msgid "Likes"
msgstr "Îi place"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
#, fuzzy
msgid "Dislikes"
msgstr "Antipatiile"

#: pynicotine/gtkgui/interests.py:104
#, fuzzy
msgid "Rating"
msgstr "Evaluare"

#: pynicotine/gtkgui/interests.py:111
#, fuzzy
msgid "Item"
msgstr "Articol"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
#, fuzzy
msgid "_Recommendations for Item"
msgstr "_Recomandări pentru articol"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
#, fuzzy
msgid "I _Like This"
msgstr "Îmi place asta"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
#, fuzzy
msgid "I _Dislike This"
msgstr "_Nu-mi place asta"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
#, fuzzy
msgid "Recommendations"
msgstr "Recomandări"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
#, fuzzy
msgid "Similar Users"
msgstr "Utilizatori similari"

#: pynicotine/gtkgui/interests.py:427
#, fuzzy, python-format
msgid "Recommendations (%s)"
msgstr "Recomandări (%s)"

#: pynicotine/gtkgui/interests.py:451
#, fuzzy, python-format
msgid "Similar Users (%s)"
msgstr "Utilizatori similari (%s)"

#: pynicotine/gtkgui/mainwindow.py:238
#, fuzzy
msgid "Search log…"
msgstr "Termen de căutare…"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, fuzzy, python-format
msgid "Private Message from %(user)s"
msgstr "Mesaj privat de la %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
#, fuzzy
msgid "Wishlist Results Found"
msgstr "S-au găsit rezultatele listei de dorințe"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
#, fuzzy
msgid "User Profiles"
msgstr "Profilurile utilizatorilor"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
#, fuzzy
msgid "Chat Rooms"
msgstr "Camere de chat"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
#, fuzzy
msgid "Interests"
msgstr "Interese"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
#, fuzzy
msgid "Chat"
msgstr "conversație"

#: pynicotine/gtkgui/mainwindow.py:1111
#, fuzzy
msgid "[Debug] Connections"
msgstr "[Depanare] Conexiuni"

#: pynicotine/gtkgui/mainwindow.py:1112
#, fuzzy
msgid "[Debug] Messages"
msgstr "[Depanare] Mesaje"

#: pynicotine/gtkgui/mainwindow.py:1113
#, fuzzy
msgid "[Debug] Transfers"
msgstr "[Depanare] Transferuri"

#: pynicotine/gtkgui/mainwindow.py:1114
#, fuzzy
msgid "[Debug] Miscellaneous"
msgstr "[Depanare] Diverse"

#: pynicotine/gtkgui/mainwindow.py:1119
#, fuzzy
msgid "_Find…"
msgstr "_Găsi…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
#, fuzzy
msgid "_Copy"
msgstr "_Copie"

#: pynicotine/gtkgui/mainwindow.py:1122
#, fuzzy
msgid "Copy _All"
msgstr "Copiați _Toate"

#: pynicotine/gtkgui/mainwindow.py:1127
#, fuzzy
msgid "View _Debug Logs"
msgstr "Vezi istoric cameră"

#: pynicotine/gtkgui/mainwindow.py:1128
#, fuzzy
msgid "View _Transfer Logs"
msgstr "Deschideți _Transfer Log"

#: pynicotine/gtkgui/mainwindow.py:1132
#, fuzzy
msgid "_Log Categories"
msgstr "_Categorii de jurnal"

#: pynicotine/gtkgui/mainwindow.py:1134
#, fuzzy
msgid "Clear Log View"
msgstr "Ștergeți vizualizarea jurnalului"

#: pynicotine/gtkgui/mainwindow.py:1199
#, fuzzy
msgid "Preparing Shares"
msgstr "Scanarea acțiunilor"

#: pynicotine/gtkgui/mainwindow.py:1210
#, fuzzy
msgid "Scanning Shares"
msgstr "Scanarea acțiunilor"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
#, fuzzy
msgid "Shared Folders"
msgstr "Foldere partajate"

#: pynicotine/gtkgui/popovers/chathistory.py:77
#, fuzzy
msgid "Latest Message"
msgstr "Ultimul mesaj"

#: pynicotine/gtkgui/popovers/roomlist.py:66
#, fuzzy
msgid "Room"
msgstr "Cameră"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
#, fuzzy
msgid "Users"
msgstr "Utilizatori"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
#, fuzzy
msgid "Join Room"
msgstr "Alăturați-vă camerei"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
#, fuzzy
msgid "Leave Room"
msgstr "Paraseste camera"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
#, fuzzy
msgid "Disown Private Room"
msgstr "Cameră privată Disown"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
#, fuzzy
msgid "Cancel Room Membership"
msgstr "Anulează calitatea de membru al camerei"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
#, fuzzy
msgid "Close All Tabs…"
msgstr "Inchide toate filele…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
#, fuzzy
msgid "_Close Tab"
msgstr "_Închideți fila"

#: pynicotine/gtkgui/privatechat.py:379
#, fuzzy
msgid "View Chat Log"
msgstr "Vizualizați jurnalul de chat"

#: pynicotine/gtkgui/privatechat.py:382
#, fuzzy
msgid "Delete Chat Log…"
msgstr "Ștergeți jurnalul de chat…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
#, fuzzy
msgid "User Actions"
msgstr "Acțiunile utilizatorului"

#: pynicotine/gtkgui/privatechat.py:477
#, fuzzy
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Chiar doriți să ștergeți definitiv toate mesajele înregistrate pentru acest "
"utilizator?"

#: pynicotine/gtkgui/privatechat.py:528
#, fuzzy
msgid "* Messages sent while you were offline"
msgstr "* Mesaje trimise în timp ce erai offline"

#: pynicotine/gtkgui/search.py:90
#, fuzzy
msgid "_Global"
msgstr "_Global"

#: pynicotine/gtkgui/search.py:91
#, fuzzy
msgid "_Buddies"
msgstr "_Prieteni"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
#, fuzzy
msgid "_Rooms"
msgstr "_Camere"

#: pynicotine/gtkgui/search.py:93
#, fuzzy
msgid "_User"
msgstr "_Utilizator"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
#, fuzzy
msgid "In Queue"
msgstr "În coadă"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
#, fuzzy
msgid "File Type"
msgstr "Tip fișier"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
#, fuzzy
msgid "Filename"
msgstr "Nume de fișier"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
#, fuzzy
msgid "Size"
msgstr "mărimea"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
#, fuzzy
msgid "Quality"
msgstr "Calitate"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
#, fuzzy
msgid "Copy _File Path"
msgstr "Copiați _Cale fișierului"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
#, fuzzy
msgid "Copy _URL"
msgstr "Copiați _URL"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
#, fuzzy
msgid "Copy Folder U_RL"
msgstr "Copiați dosarul U_RL"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
#, fuzzy
msgid "_Download File(s)"
msgstr "_Descărcați fișiere"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
#, fuzzy
msgid "Download File(s) _To…"
msgstr "Descărcați fișierele _Pentru…"

#: pynicotine/gtkgui/search.py:615
#, fuzzy
msgid "Download _Folder(s)"
msgstr "Descărcați _Folder(e)"

#: pynicotine/gtkgui/search.py:616
#, fuzzy
msgid "Download F_older(s) To…"
msgstr "Descărcați F_older(e) la…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
#, fuzzy
msgid "View User _Profile"
msgstr "Vizualizați _Profilul utilizatorului"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
#, fuzzy
msgid "_Browse Folder"
msgstr "Răsfoiți folderul"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
#, fuzzy
msgid "F_ile Properties"
msgstr "Proprietăți fișier"

#: pynicotine/gtkgui/search.py:629
#, fuzzy
msgid "Copy Search Term"
msgstr "Copiați termenul de căutare"

#: pynicotine/gtkgui/search.py:631
#, fuzzy
msgid "Clear All Results"
msgstr "Ștergeți toate rezultatele"

#: pynicotine/gtkgui/search.py:718
#, fuzzy
msgid "Clear Filters"
msgstr "Ștergeți filtrele"

#: pynicotine/gtkgui/search.py:721
#, fuzzy
msgid "Restore Filters"
msgstr "Restaurați filtrele"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, fuzzy, python-format
msgid "[PRIVATE]  %s"
msgstr "[PRIVAT] %s"

#: pynicotine/gtkgui/search.py:1273
#, fuzzy, python-format
msgid "_Result Filters [%d]"
msgstr "_Filtre de rezultate [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
#, fuzzy
msgid "_Result Filters"
msgstr "_Filtre de rezultate"

#: pynicotine/gtkgui/search.py:1277
#, fuzzy, python-format
msgid "%d active filter(s)"
msgstr "%d filtru(i) activ(e)"

#: pynicotine/gtkgui/search.py:1329
#, fuzzy
msgid "Add Wi_sh"
msgstr "Adăugați Wi_sh"

#: pynicotine/gtkgui/search.py:1332
#, fuzzy
msgid "Remove Wi_sh"
msgstr "Eliminați Wi_sh"

#: pynicotine/gtkgui/search.py:1349
#, fuzzy
msgid "Select User's Results"
msgstr "Selectați Rezultatele utilizatorului"

#: pynicotine/gtkgui/search.py:1472
#, fuzzy, python-format
msgid "Total: %s"
msgstr "Total: %s"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
#, fuzzy
msgid "Results"
msgstr "Rezultate"

#: pynicotine/gtkgui/search.py:1590
#, fuzzy
msgid "Select Destination Folder for File(s)"
msgstr "Selectați folderul de destinație pentru fișiere"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
#, fuzzy
msgid "Select Destination Folder"
msgstr "Selectați Dosarul de destinație"

#: pynicotine/gtkgui/transfers.py:61
#, fuzzy
msgid "Queued"
msgstr "În așteptare"

#: pynicotine/gtkgui/transfers.py:62
#, fuzzy
msgid "Queued (prioritized)"
msgstr "În coadă (cu prioritate)"

#: pynicotine/gtkgui/transfers.py:63
#, fuzzy
msgid "Queued (privileged)"
msgstr "În coadă (privilegiat)"

#: pynicotine/gtkgui/transfers.py:64
#, fuzzy
msgid "Getting status"
msgstr "Obținerea statutului"

#: pynicotine/gtkgui/transfers.py:65
#, fuzzy
msgid "Transferring"
msgstr "Transfer"

#: pynicotine/gtkgui/transfers.py:66
#, fuzzy
msgid "Connection closed"
msgstr "Conexiuni"

#: pynicotine/gtkgui/transfers.py:67
#, fuzzy
msgid "Connection timeout"
msgstr "Timeout conexiune"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
#, fuzzy
msgid "User logged off"
msgstr "Utilizatorul sa deconectat"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
#, fuzzy
msgid "Cancelled"
msgstr "Anulat"

#: pynicotine/gtkgui/transfers.py:73
#, fuzzy
msgid "Download folder error"
msgstr "Eroare de descărcare a folderului"

#: pynicotine/gtkgui/transfers.py:74
#, fuzzy
msgid "Local file error"
msgstr "Eroare de fișier local"

#: pynicotine/gtkgui/transfers.py:75
#, fuzzy
msgid "Banned"
msgstr "Interzis"

#: pynicotine/gtkgui/transfers.py:76
#, fuzzy
msgid "File not shared"
msgstr "Fișierul nu a fost partajat"

#: pynicotine/gtkgui/transfers.py:77
#, fuzzy
msgid "Pending shutdown"
msgstr "Închidere în așteptare"

#: pynicotine/gtkgui/transfers.py:78
#, fuzzy
msgid "File read error"
msgstr "Transferuri de fișiere"

#: pynicotine/gtkgui/transfers.py:182
#, fuzzy
msgid "Queue"
msgstr "Coadă"

#: pynicotine/gtkgui/transfers.py:188
#, fuzzy
msgid "Percent"
msgstr "La sută"

#: pynicotine/gtkgui/transfers.py:208
#, fuzzy
msgid "Time Elapsed"
msgstr "Timpul scurs"

#: pynicotine/gtkgui/transfers.py:215
#, fuzzy
msgid "Time Left"
msgstr "Timp rămas"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
#, fuzzy
msgid "_Open File"
msgstr "_Deschide lista"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
#, fuzzy
msgid "Open in File _Manager"
msgstr "Deschideți în File _Manager"

#: pynicotine/gtkgui/transfers.py:286
#, fuzzy
msgid "_Search"
msgstr "_Căutare"

#: pynicotine/gtkgui/transfers.py:289
#, fuzzy
msgid "Clear All"
msgstr "Curata tot"

#: pynicotine/gtkgui/transfers.py:953
#, fuzzy
msgid "Select User's Transfers"
msgstr "Selectați Transferuri utilizator"

#: pynicotine/gtkgui/uploads.py:50
#, fuzzy
msgid "_Abort"
msgstr "_Anulează"

#: pynicotine/gtkgui/uploads.py:74
#, fuzzy
msgid "Finished / Cancelled / Failed"
msgstr "Terminat/Anulat/Eșuat"

#: pynicotine/gtkgui/uploads.py:75
#, fuzzy
msgid "Finished / Cancelled"
msgstr "Terminat/Anulat"

#: pynicotine/gtkgui/uploads.py:79
#, fuzzy
msgid "Failed"
msgstr "A eșuat"

#: pynicotine/gtkgui/uploads.py:80
#, fuzzy
msgid "User Logged Off"
msgstr "Utilizator deconectat"

#: pynicotine/gtkgui/uploads.py:142
#, fuzzy, python-format
msgid "Uploads: %(speed)s"
msgstr "Încărcări: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr "În curse de ieșire..."

#: pynicotine/gtkgui/uploads.py:164
#, fuzzy
msgid "Clear Queued Uploads"
msgstr "Ștergeți Încărcările în coadă"

#: pynicotine/gtkgui/uploads.py:165
#, fuzzy
msgid "Do you really want to clear all queued uploads?"
msgstr "Chiar doriți să ștergeți toate încărcările din coadă?"

#: pynicotine/gtkgui/uploads.py:177
#, fuzzy
msgid "Clear All Uploads"
msgstr "Ștergeți toate încărcările"

#: pynicotine/gtkgui/uploads.py:178
#, fuzzy
msgid "Do you really want to clear all uploads?"
msgstr "Chiar doriți să ștergeți toate încărcările?"

#: pynicotine/gtkgui/userbrowse.py:282
#, fuzzy
msgid "_Save Shares List to Disk"
msgstr "_Salvați lista de acțiuni pe disc"

#: pynicotine/gtkgui/userbrowse.py:292
#, fuzzy
msgid "Upload Folder & Subfolders…"
msgstr "Încărcați folderul și subdosarele…"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
#, fuzzy
msgid "Copy _Folder Path"
msgstr "Copiați calea _dosarului"

#: pynicotine/gtkgui/userbrowse.py:309
#, fuzzy
msgid "_Download Folder & Subfolders"
msgstr "Descărcați folderul și subdosarele"

#: pynicotine/gtkgui/userbrowse.py:310
#, fuzzy
msgid "Download Folder & Subfolders _To…"
msgstr "Descărcați folderul și subdosarele în…"

#: pynicotine/gtkgui/userbrowse.py:334
#, fuzzy
msgid "File Name"
msgstr "Nume de fișier"

#: pynicotine/gtkgui/userbrowse.py:373
#, fuzzy
msgid "Up_load File(s)…"
msgstr "Încărca fișiere)…"

#: pynicotine/gtkgui/userbrowse.py:374
#, fuzzy
msgid "Upload Folder…"
msgstr "Încărcați dosarul…"

#: pynicotine/gtkgui/userbrowse.py:396
#, fuzzy
msgid "Download Folder _To…"
msgstr "Descărcați folderul _Pentru…"

#: pynicotine/gtkgui/userbrowse.py:600
#, fuzzy
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"Lista utilizatorului de fișiere partajate este goală. Fie utilizatorul nu "
"partajează nimic, fie partajează fișiere în mod privat."

#: pynicotine/gtkgui/userbrowse.py:615
#, fuzzy
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Nu se pot solicita fișiere partajate de la utilizator. Fie utilizatorul este "
"offline, fie porturile de ascultare sunt închise pe ambele părți, fie există "
"o problemă temporară de conectivitate."

#: pynicotine/gtkgui/userbrowse.py:953
#, fuzzy
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Selectați Destinație pentru descărcarea mai multor foldere"

#: pynicotine/gtkgui/userbrowse.py:997
#, fuzzy
msgid "Upload Folder (with Subfolders) To User"
msgstr "Încărcați folderul (cu subdosare) către utilizator"

#: pynicotine/gtkgui/userbrowse.py:999
#, fuzzy
msgid "Upload Folder To User"
msgstr "Încărcați dosarul la utilizator"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
#, fuzzy
msgid "Enter the name of the user you want to upload to:"
msgstr "Introduceți numele utilizatorului în care doriți să încărcați:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
#, fuzzy
msgid "_Upload"
msgstr "Încărcări"

#: pynicotine/gtkgui/userbrowse.py:1139
#, fuzzy
msgid "Select Destination Folder for Files"
msgstr "Selectați folderul de destinație pentru fișiere"

#: pynicotine/gtkgui/userbrowse.py:1161
#, fuzzy
msgid "Upload File(s) To User"
msgstr "Încărcați fișierele la utilizator"

#: pynicotine/gtkgui/userinfo.py:376
#, fuzzy
msgid "Copy Picture"
msgstr "Copiați imaginea"

#: pynicotine/gtkgui/userinfo.py:377
#, fuzzy
msgid "Save Picture"
msgstr "Salvează poza"

#: pynicotine/gtkgui/userinfo.py:468
#, fuzzy, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Nu s-a putut încărca imaginea pentru utilizatorul %(user)s: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
#, fuzzy
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Nu se pot solicita informații de la utilizator. Fie aveți amândoi un port de "
"ascultare închis, utilizatorul este offline, fie există o problemă temporară "
"de conectivitate."

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Remove _Buddy"
msgstr "Eliminați _Buddy"

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Add _Buddy"
msgstr "Adauga prieten"

#: pynicotine/gtkgui/userinfo.py:581
#, fuzzy
msgid "Unban User"
msgstr "Anulează utilizatorul"

#: pynicotine/gtkgui/userinfo.py:585
#, fuzzy
msgid "Unignore User"
msgstr "Anulați ignorarea utilizatorului"

#: pynicotine/gtkgui/userinfo.py:613
#, fuzzy
msgid "Yes"
msgstr "da"

#: pynicotine/gtkgui/userinfo.py:613
#, fuzzy
msgid "No"
msgstr "Nu"

#: pynicotine/gtkgui/userinfo.py:773
#, fuzzy
msgid "Please enter number of days."
msgstr "Vă rugăm să introduceți numărul de zile."

#: pynicotine/gtkgui/userinfo.py:787
#, fuzzy, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Dăruiește zilele privilegiilor tale Soulseek utilizatorului %(user)s "
"(%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, fuzzy, python-format
msgid "%(days)s days left"
msgstr "Au mai rămas %(days)s zile"

#: pynicotine/gtkgui/userinfo.py:795
#, fuzzy
msgid "Gift Privileges"
msgstr "Privilegii cadou"

#: pynicotine/gtkgui/userinfo.py:797
#, fuzzy
msgid "_Give Privileges"
msgstr "Privilegii cadou"

#: pynicotine/gtkgui/widgets/dialogs.py:309
#, fuzzy
msgid "Close"
msgstr "Închide"

#: pynicotine/gtkgui/widgets/dialogs.py:488
#, fuzzy
msgid "_Yes"
msgstr "_Da"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
#, fuzzy
msgid "_OK"
msgstr "_BINE"

#: pynicotine/gtkgui/widgets/filechooser.py:39
#, fuzzy
msgid "Select a File"
msgstr "Selectați un fișier"

#: pynicotine/gtkgui/widgets/filechooser.py:174
#, fuzzy
msgid "Select a Folder"
msgstr "Selectați un folder"

#: pynicotine/gtkgui/widgets/filechooser.py:179
#, fuzzy
msgid "_Select"
msgstr "_Selectați"

#: pynicotine/gtkgui/widgets/filechooser.py:196
#, fuzzy
msgid "Select an Image"
msgstr "Selectați o imagine"

#: pynicotine/gtkgui/widgets/filechooser.py:203
#, fuzzy
msgid "All images"
msgstr "Toate imaginile"

#: pynicotine/gtkgui/widgets/filechooser.py:241
#, fuzzy
msgid "Save as…"
msgstr "Salvează ca…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
#, fuzzy
msgid "(None)"
msgstr "(Nici unul)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
#, fuzzy
msgid "Close Tab"
msgstr "Închideți fila"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
#, fuzzy
msgid "Close All Tabs?"
msgstr "Inchide toate filele?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
#, fuzzy
msgid "Do you really want to close all tabs?"
msgstr "Chiar vrei să închizi toate filele?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, fuzzy, python-format
msgid "%i Unread Tab(s)"
msgstr "File necitite"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
#, fuzzy
msgid "All Tabs"
msgstr "Toate filele"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
#, fuzzy
msgid "Re_open Closed Tab"
msgstr "_Închideți fila"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, fuzzy, python-format
msgid "%s File(s) Selected"
msgstr "%s Fișier(e) selectat(e)."

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
#, fuzzy
msgid "_Browse Files"
msgstr "_Cauta fisiere"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
#, fuzzy
msgid "_Add Buddy"
msgstr "_Adauga prieten"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
#, fuzzy
msgid "Show IP A_ddress"
msgstr "Afișați adresa IP"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
msgid "Private Rooms"
msgstr "Camere private"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, fuzzy, python-format
msgid "Remove from Private Room %s"
msgstr "Eliminați din camera privată %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, fuzzy, python-format
msgid "Add to Private Room %s"
msgstr "Adăugați în camera privată %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, fuzzy, python-format
msgid "Remove as Operator of %s"
msgstr "Eliminați ca operator al %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, fuzzy, python-format
msgid "Add as Operator of %s"
msgstr "Adăugați ca operator pentru %s"

#: pynicotine/gtkgui/widgets/textentry.py:37
#, fuzzy
msgid "Send message…"
msgstr "Trimite mesaj…"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
#, fuzzy
msgid "Find Previous Match"
msgstr "Găsiți potrivirea anterioară"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
#, fuzzy
msgid "Find Next Match"
msgstr "Găsiți meciul următor"

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr "--- mesaje vechi mai sus ---"

#: pynicotine/gtkgui/widgets/theme.py:243
#, fuzzy
msgid "Executable"
msgstr "Executabil"

#: pynicotine/gtkgui/widgets/theme.py:244
#, fuzzy
msgid "Audio"
msgstr "Audio"

#: pynicotine/gtkgui/widgets/theme.py:245
#, fuzzy
msgid "Image"
msgstr "Imagine"

#: pynicotine/gtkgui/widgets/theme.py:246
#, fuzzy
msgid "Archive"
msgstr "Arhiva"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
#, fuzzy
msgid "Miscellaneous"
msgstr "Diverse"

#: pynicotine/gtkgui/widgets/theme.py:248
#, fuzzy
msgid "Video"
msgstr "Video"

#: pynicotine/gtkgui/widgets/theme.py:249
#, fuzzy
msgid "Document"
msgstr "Document/Text"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr "Text"

#: pynicotine/gtkgui/widgets/theme.py:357
#, fuzzy, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "Eroare la încărcarea pictogramei personalizate %(path)s: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
#, fuzzy
msgid "Hide Nicotine+"
msgstr "Ascunde Nicotina+"

#: pynicotine/gtkgui/widgets/trayicon.py:114
#, fuzzy
msgid "Show Nicotine+"
msgstr "Arată Nicotina+"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, fuzzy, python-format
msgid "Column #%i"
msgstr "Coloana #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
#, fuzzy
msgid "Ungrouped"
msgstr "Negrupat"

#: pynicotine/gtkgui/widgets/treeview.py:960
#, fuzzy
msgid "Group by Folder"
msgstr "Grupați după folder"

#: pynicotine/gtkgui/widgets/treeview.py:963
#, fuzzy
msgid "Group by User"
msgstr "Grupați după utilizator"

#: pynicotine/headless/application.py:77
#, fuzzy, python-format
msgid "Do you really want to exit? %s"
msgstr "Ești sigur că vrei să ieși?"

#: pynicotine/headless/application.py:81
#, fuzzy, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr ""
"Utilizatorul %s deja există și parola introdusă este greșită. Vă rog să "
"alegeți alt nume de utilizator dacă aceasta este prima oară când vă "
"conectați."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr ""
"Tastează %s pentru a te autentifica folosind un alt nume de utilizator sau o "
"altă parolă."

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
#, fuzzy
msgid "Password: "
msgstr "Parola"

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
#, fuzzy
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Pentru a crea un nou cont Soulseek, completați numele de utilizator și "
"parola dorite. Dacă aveți deja un cont, completați datele de conectare "
"existente."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr "Următoarele partajări sunt disponibile:"

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr "Reîncearcă scanarea? %s"

#: pynicotine/logfacility.py:181
#, fuzzy, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr "Nu s-a putut scrie în fișierul jurnal „%(filename)s”: %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, fuzzy, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Nu se poate accesa fișierul jurnal %(path)s: %(error)s"

#: pynicotine/networkfilter.py:40
#, fuzzy
msgid "Andorra"
msgstr "Andorra"

#: pynicotine/networkfilter.py:41
#, fuzzy
msgid "United Arab Emirates"
msgstr "Emiratele Arabe Unite"

#: pynicotine/networkfilter.py:42
#, fuzzy
msgid "Afghanistan"
msgstr "Afganistan"

#: pynicotine/networkfilter.py:43
#, fuzzy
msgid "Antigua & Barbuda"
msgstr "Antigua & Barbuda"

#: pynicotine/networkfilter.py:44
#, fuzzy
msgid "Anguilla"
msgstr "Anguilla"

#: pynicotine/networkfilter.py:45
#, fuzzy
msgid "Albania"
msgstr "Albania"

#: pynicotine/networkfilter.py:46
#, fuzzy
msgid "Armenia"
msgstr "Armenia"

#: pynicotine/networkfilter.py:47
#, fuzzy
msgid "Angola"
msgstr "Angola"

#: pynicotine/networkfilter.py:48
#, fuzzy
msgid "Antarctica"
msgstr "Antarctica"

#: pynicotine/networkfilter.py:49
#, fuzzy
msgid "Argentina"
msgstr "Argentina"

#: pynicotine/networkfilter.py:50
#, fuzzy
msgid "American Samoa"
msgstr "Samoa Americană"

#: pynicotine/networkfilter.py:51
#, fuzzy
msgid "Austria"
msgstr "Austria"

#: pynicotine/networkfilter.py:52
#, fuzzy
msgid "Australia"
msgstr "Australia"

#: pynicotine/networkfilter.py:53
#, fuzzy
msgid "Aruba"
msgstr "Aruba"

#: pynicotine/networkfilter.py:54
#, fuzzy
msgid "Åland Islands"
msgstr "Insulele Aland"

#: pynicotine/networkfilter.py:55
#, fuzzy
msgid "Azerbaijan"
msgstr "Azerbaidjan"

#: pynicotine/networkfilter.py:56
#, fuzzy
msgid "Bosnia & Herzegovina"
msgstr "Bosnia și Herțegovina"

#: pynicotine/networkfilter.py:57
#, fuzzy
msgid "Barbados"
msgstr "Barbados"

#: pynicotine/networkfilter.py:58
#, fuzzy
msgid "Bangladesh"
msgstr "Bangladesh"

#: pynicotine/networkfilter.py:59
#, fuzzy
msgid "Belgium"
msgstr "Belgia"

#: pynicotine/networkfilter.py:60
#, fuzzy
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: pynicotine/networkfilter.py:61
#, fuzzy
msgid "Bulgaria"
msgstr "Bulgaria"

#: pynicotine/networkfilter.py:62
#, fuzzy
msgid "Bahrain"
msgstr "Bahrain"

#: pynicotine/networkfilter.py:63
#, fuzzy
msgid "Burundi"
msgstr "Burundi"

#: pynicotine/networkfilter.py:64
#, fuzzy
msgid "Benin"
msgstr "Benin"

#: pynicotine/networkfilter.py:65
#, fuzzy
msgid "Saint Barthelemy"
msgstr "Sfântul Bartolomeu"

#: pynicotine/networkfilter.py:66
#, fuzzy
msgid "Bermuda"
msgstr "Bermude"

#: pynicotine/networkfilter.py:67
#, fuzzy
msgid "Brunei Darussalam"
msgstr "Brunei Darussalam"

#: pynicotine/networkfilter.py:68
#, fuzzy
msgid "Bolivia"
msgstr "Bolivia"

#: pynicotine/networkfilter.py:69
#, fuzzy
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius și Saba"

#: pynicotine/networkfilter.py:70
#, fuzzy
msgid "Brazil"
msgstr "Brazilia"

#: pynicotine/networkfilter.py:71
#, fuzzy
msgid "Bahamas"
msgstr "Bahamas"

#: pynicotine/networkfilter.py:72
#, fuzzy
msgid "Bhutan"
msgstr "Bhutan"

#: pynicotine/networkfilter.py:73
#, fuzzy
msgid "Bouvet Island"
msgstr "Insula Bouvet"

#: pynicotine/networkfilter.py:74
#, fuzzy
msgid "Botswana"
msgstr "Botswana"

#: pynicotine/networkfilter.py:75
#, fuzzy
msgid "Belarus"
msgstr "Bielorusia"

#: pynicotine/networkfilter.py:76
#, fuzzy
msgid "Belize"
msgstr "Belize"

#: pynicotine/networkfilter.py:77
#, fuzzy
msgid "Canada"
msgstr "Canada"

#: pynicotine/networkfilter.py:78
#, fuzzy
msgid "Cocos (Keeling) Islands"
msgstr "Insulele Cocos (Keeling)."

#: pynicotine/networkfilter.py:79
#, fuzzy
msgid "Democratic Republic of Congo"
msgstr "Republica Democrată Congo"

#: pynicotine/networkfilter.py:80
#, fuzzy
msgid "Central African Republic"
msgstr "Republica Centrafricană"

#: pynicotine/networkfilter.py:81
#, fuzzy
msgid "Congo"
msgstr "Congo"

#: pynicotine/networkfilter.py:82
#, fuzzy
msgid "Switzerland"
msgstr "Elveţia"

#: pynicotine/networkfilter.py:83
#, fuzzy
msgid "Ivory Coast"
msgstr "coasta de Fildes"

#: pynicotine/networkfilter.py:84
#, fuzzy
msgid "Cook Islands"
msgstr "Insulele Cook"

#: pynicotine/networkfilter.py:85
#, fuzzy
msgid "Chile"
msgstr "Chile"

#: pynicotine/networkfilter.py:86
#, fuzzy
msgid "Cameroon"
msgstr "Camerun"

#: pynicotine/networkfilter.py:87
#, fuzzy
msgid "China"
msgstr "China"

#: pynicotine/networkfilter.py:88
#, fuzzy
msgid "Colombia"
msgstr "Columbia"

#: pynicotine/networkfilter.py:89
#, fuzzy
msgid "Costa Rica"
msgstr "Costa Rica"

#: pynicotine/networkfilter.py:90
#, fuzzy
msgid "Cuba"
msgstr "Cuba"

#: pynicotine/networkfilter.py:91
#, fuzzy
msgid "Cabo Verde"
msgstr "Cabo Verde"

#: pynicotine/networkfilter.py:92
#, fuzzy
msgid "Curaçao"
msgstr "Curaçao"

#: pynicotine/networkfilter.py:93
#, fuzzy
msgid "Christmas Island"
msgstr "Insula Craciunului"

#: pynicotine/networkfilter.py:94
#, fuzzy
msgid "Cyprus"
msgstr "Cipru"

#: pynicotine/networkfilter.py:95
#, fuzzy
msgid "Czechia"
msgstr "Cehia"

#: pynicotine/networkfilter.py:96
#, fuzzy
msgid "Germany"
msgstr "Germania"

#: pynicotine/networkfilter.py:97
#, fuzzy
msgid "Djibouti"
msgstr "Djibouti"

#: pynicotine/networkfilter.py:98
#, fuzzy
msgid "Denmark"
msgstr "Danemarca"

#: pynicotine/networkfilter.py:99
#, fuzzy
msgid "Dominica"
msgstr "Dominica"

#: pynicotine/networkfilter.py:100
#, fuzzy
msgid "Dominican Republic"
msgstr "Republica Dominicană"

#: pynicotine/networkfilter.py:101
#, fuzzy
msgid "Algeria"
msgstr "Algeria"

#: pynicotine/networkfilter.py:102
#, fuzzy
msgid "Ecuador"
msgstr "Ecuador"

#: pynicotine/networkfilter.py:103
#, fuzzy
msgid "Estonia"
msgstr "Estonia"

#: pynicotine/networkfilter.py:104
#, fuzzy
msgid "Egypt"
msgstr "Egipt"

#: pynicotine/networkfilter.py:105
#, fuzzy
msgid "Western Sahara"
msgstr "sahara de Vest"

#: pynicotine/networkfilter.py:106
#, fuzzy
msgid "Eritrea"
msgstr "Eritreea"

#: pynicotine/networkfilter.py:107
#, fuzzy
msgid "Spain"
msgstr "Spania"

#: pynicotine/networkfilter.py:108
#, fuzzy
msgid "Ethiopia"
msgstr "Etiopia"

#: pynicotine/networkfilter.py:109
#, fuzzy
msgid "Europe"
msgstr "Europa"

#: pynicotine/networkfilter.py:110
#, fuzzy
msgid "Finland"
msgstr "Finlanda"

#: pynicotine/networkfilter.py:111
#, fuzzy
msgid "Fiji"
msgstr "Fiji"

#: pynicotine/networkfilter.py:112
#, fuzzy
msgid "Falkland Islands (Malvinas)"
msgstr "Insulele Falkland (Malvinas)"

#: pynicotine/networkfilter.py:113
#, fuzzy
msgid "Micronesia"
msgstr "Micronezia"

#: pynicotine/networkfilter.py:114
#, fuzzy
msgid "Faroe Islands"
msgstr "Insulele Feroe"

#: pynicotine/networkfilter.py:115
#, fuzzy
msgid "France"
msgstr "Franţa"

#: pynicotine/networkfilter.py:116
#, fuzzy
msgid "Gabon"
msgstr "Gabon"

#: pynicotine/networkfilter.py:117
#, fuzzy
msgid "Great Britain"
msgstr "Marea Britanie"

#: pynicotine/networkfilter.py:118
#, fuzzy
msgid "Grenada"
msgstr "Grenada"

#: pynicotine/networkfilter.py:119
#, fuzzy
msgid "Georgia"
msgstr "Georgia"

#: pynicotine/networkfilter.py:120
#, fuzzy
msgid "French Guiana"
msgstr "Guyana Franceză"

#: pynicotine/networkfilter.py:121
#, fuzzy
msgid "Guernsey"
msgstr "Guernsey"

#: pynicotine/networkfilter.py:122
#, fuzzy
msgid "Ghana"
msgstr "Ghana"

#: pynicotine/networkfilter.py:123
#, fuzzy
msgid "Gibraltar"
msgstr "Gibraltar"

#: pynicotine/networkfilter.py:124
#, fuzzy
msgid "Greenland"
msgstr "Groenlanda"

#: pynicotine/networkfilter.py:125
#, fuzzy
msgid "Gambia"
msgstr "Gambia"

#: pynicotine/networkfilter.py:126
#, fuzzy
msgid "Guinea"
msgstr "Guineea"

#: pynicotine/networkfilter.py:127
#, fuzzy
msgid "Guadeloupe"
msgstr "Guadelupa"

#: pynicotine/networkfilter.py:128
#, fuzzy
msgid "Equatorial Guinea"
msgstr "Guineea Ecuatorială"

#: pynicotine/networkfilter.py:129
#, fuzzy
msgid "Greece"
msgstr "Grecia"

#: pynicotine/networkfilter.py:130
#, fuzzy
msgid "South Georgia & South Sandwich Islands"
msgstr "Insulele Georgia de Sud și Sandwich de Sud"

#: pynicotine/networkfilter.py:131
#, fuzzy
msgid "Guatemala"
msgstr "Guatemala"

#: pynicotine/networkfilter.py:132
#, fuzzy
msgid "Guam"
msgstr "Guam"

#: pynicotine/networkfilter.py:133
#, fuzzy
msgid "Guinea-Bissau"
msgstr "Guineea-Bissau"

#: pynicotine/networkfilter.py:134
#, fuzzy
msgid "Guyana"
msgstr "Guyana"

#: pynicotine/networkfilter.py:135
#, fuzzy
msgid "Hong Kong"
msgstr "Hong Kong"

#: pynicotine/networkfilter.py:136
#, fuzzy
msgid "Heard & McDonald Islands"
msgstr "Insulele Heard și McDonald"

#: pynicotine/networkfilter.py:137
#, fuzzy
msgid "Honduras"
msgstr "Honduras"

#: pynicotine/networkfilter.py:138
#, fuzzy
msgid "Croatia"
msgstr "Croaţia"

#: pynicotine/networkfilter.py:139
#, fuzzy
msgid "Haiti"
msgstr "Haiti"

#: pynicotine/networkfilter.py:140
#, fuzzy
msgid "Hungary"
msgstr "Ungaria"

#: pynicotine/networkfilter.py:141
#, fuzzy
msgid "Indonesia"
msgstr "Indonezia"

#: pynicotine/networkfilter.py:142
#, fuzzy
msgid "Ireland"
msgstr "Irlanda"

#: pynicotine/networkfilter.py:143
#, fuzzy
msgid "Israel"
msgstr "Israel"

#: pynicotine/networkfilter.py:144
#, fuzzy
msgid "Isle of Man"
msgstr "insula Barbatului"

#: pynicotine/networkfilter.py:145
#, fuzzy
msgid "India"
msgstr "India"

#: pynicotine/networkfilter.py:146
#, fuzzy
msgid "British Indian Ocean Territory"
msgstr "Teritoriul Britanic al Oceanului Indian"

#: pynicotine/networkfilter.py:147
#, fuzzy
msgid "Iraq"
msgstr "Irak"

#: pynicotine/networkfilter.py:148
#, fuzzy
msgid "Iran"
msgstr "Iranul"

#: pynicotine/networkfilter.py:149
#, fuzzy
msgid "Iceland"
msgstr "Islanda"

#: pynicotine/networkfilter.py:150
#, fuzzy
msgid "Italy"
msgstr "Italia"

#: pynicotine/networkfilter.py:151
#, fuzzy
msgid "Jersey"
msgstr "Jersey"

#: pynicotine/networkfilter.py:152
#, fuzzy
msgid "Jamaica"
msgstr "Jamaica"

#: pynicotine/networkfilter.py:153
#, fuzzy
msgid "Jordan"
msgstr "Iordania"

#: pynicotine/networkfilter.py:154
#, fuzzy
msgid "Japan"
msgstr "Japonia"

#: pynicotine/networkfilter.py:155
#, fuzzy
msgid "Kenya"
msgstr "Kenya"

#: pynicotine/networkfilter.py:156
#, fuzzy
msgid "Kyrgyzstan"
msgstr "Kârgâzstan"

#: pynicotine/networkfilter.py:157
#, fuzzy
msgid "Cambodia"
msgstr "Cambodgia"

#: pynicotine/networkfilter.py:158
#, fuzzy
msgid "Kiribati"
msgstr "Kiribati"

#: pynicotine/networkfilter.py:159
#, fuzzy
msgid "Comoros"
msgstr "Comore"

#: pynicotine/networkfilter.py:160
#, fuzzy
msgid "Saint Kitts & Nevis"
msgstr "Saint Kitts & Nevis"

#: pynicotine/networkfilter.py:161
#, fuzzy
msgid "North Korea"
msgstr "Coreea de Nord"

#: pynicotine/networkfilter.py:162
#, fuzzy
msgid "South Korea"
msgstr "Coreea de Sud"

#: pynicotine/networkfilter.py:163
#, fuzzy
msgid "Kuwait"
msgstr "Kuweit"

#: pynicotine/networkfilter.py:164
#, fuzzy
msgid "Cayman Islands"
msgstr "Insulele Cayman"

#: pynicotine/networkfilter.py:165
#, fuzzy
msgid "Kazakhstan"
msgstr "Kazahstan"

#: pynicotine/networkfilter.py:166
#, fuzzy
msgid "Laos"
msgstr "Laos"

#: pynicotine/networkfilter.py:167
#, fuzzy
msgid "Lebanon"
msgstr "Liban"

#: pynicotine/networkfilter.py:168
#, fuzzy
msgid "Saint Lucia"
msgstr "Sfânta Lucia"

#: pynicotine/networkfilter.py:169
#, fuzzy
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: pynicotine/networkfilter.py:170
#, fuzzy
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: pynicotine/networkfilter.py:171
#, fuzzy
msgid "Liberia"
msgstr "Liberia"

#: pynicotine/networkfilter.py:172
#, fuzzy
msgid "Lesotho"
msgstr "Lesotho"

#: pynicotine/networkfilter.py:173
#, fuzzy
msgid "Lithuania"
msgstr "Lituania"

#: pynicotine/networkfilter.py:174
#, fuzzy
msgid "Luxembourg"
msgstr "Luxemburg"

#: pynicotine/networkfilter.py:175
#, fuzzy
msgid "Latvia"
msgstr "Letonia"

#: pynicotine/networkfilter.py:176
#, fuzzy
msgid "Libya"
msgstr "Libia"

#: pynicotine/networkfilter.py:177
#, fuzzy
msgid "Morocco"
msgstr "Maroc"

#: pynicotine/networkfilter.py:178
#, fuzzy
msgid "Monaco"
msgstr "Monaco"

#: pynicotine/networkfilter.py:179
#, fuzzy
msgid "Moldova"
msgstr "Moldova"

#: pynicotine/networkfilter.py:180
#, fuzzy
msgid "Montenegro"
msgstr "Muntenegru"

#: pynicotine/networkfilter.py:181
#, fuzzy
msgid "Saint Martin"
msgstr "Sfântul Martin"

#: pynicotine/networkfilter.py:182
#, fuzzy
msgid "Madagascar"
msgstr "Madagascar"

#: pynicotine/networkfilter.py:183
#, fuzzy
msgid "Marshall Islands"
msgstr "Insulele Marshall"

#: pynicotine/networkfilter.py:184
#, fuzzy
msgid "North Macedonia"
msgstr "Macedonia de Nord"

#: pynicotine/networkfilter.py:185
#, fuzzy
msgid "Mali"
msgstr "Mali"

#: pynicotine/networkfilter.py:186
#, fuzzy
msgid "Myanmar"
msgstr "Myanmar"

#: pynicotine/networkfilter.py:187
#, fuzzy
msgid "Mongolia"
msgstr "Mongolia"

#: pynicotine/networkfilter.py:188
#, fuzzy
msgid "Macau"
msgstr "Macao"

#: pynicotine/networkfilter.py:189
#, fuzzy
msgid "Northern Mariana Islands"
msgstr "Insulele Mariane de Nord"

#: pynicotine/networkfilter.py:190
#, fuzzy
msgid "Martinique"
msgstr "Martinica"

#: pynicotine/networkfilter.py:191
#, fuzzy
msgid "Mauritania"
msgstr "Mauritania"

#: pynicotine/networkfilter.py:192
#, fuzzy
msgid "Montserrat"
msgstr "Montserrat"

#: pynicotine/networkfilter.py:193
#, fuzzy
msgid "Malta"
msgstr "Malta"

#: pynicotine/networkfilter.py:194
#, fuzzy
msgid "Mauritius"
msgstr "Mauritius"

#: pynicotine/networkfilter.py:195
#, fuzzy
msgid "Maldives"
msgstr "Maldive"

#: pynicotine/networkfilter.py:196
#, fuzzy
msgid "Malawi"
msgstr "Malawi"

#: pynicotine/networkfilter.py:197
#, fuzzy
msgid "Mexico"
msgstr "Mexic"

#: pynicotine/networkfilter.py:198
#, fuzzy
msgid "Malaysia"
msgstr "Malaezia"

#: pynicotine/networkfilter.py:199
#, fuzzy
msgid "Mozambique"
msgstr "Mozambic"

#: pynicotine/networkfilter.py:200
#, fuzzy
msgid "Namibia"
msgstr "Namibia"

#: pynicotine/networkfilter.py:201
#, fuzzy
msgid "New Caledonia"
msgstr "Noua Caledonie"

#: pynicotine/networkfilter.py:202
#, fuzzy
msgid "Niger"
msgstr "Niger"

#: pynicotine/networkfilter.py:203
#, fuzzy
msgid "Norfolk Island"
msgstr "Insula Norfolk"

#: pynicotine/networkfilter.py:204
#, fuzzy
msgid "Nigeria"
msgstr "Nigeria"

#: pynicotine/networkfilter.py:205
#, fuzzy
msgid "Nicaragua"
msgstr "Nicaragua"

#: pynicotine/networkfilter.py:206
#, fuzzy
msgid "Netherlands"
msgstr "Olanda"

#: pynicotine/networkfilter.py:207
#, fuzzy
msgid "Norway"
msgstr "Norvegia"

#: pynicotine/networkfilter.py:208
#, fuzzy
msgid "Nepal"
msgstr "Nepal"

#: pynicotine/networkfilter.py:209
#, fuzzy
msgid "Nauru"
msgstr "Nauru"

#: pynicotine/networkfilter.py:210
#, fuzzy
msgid "Niue"
msgstr "Niue"

#: pynicotine/networkfilter.py:211
#, fuzzy
msgid "New Zealand"
msgstr "Noua Zeelandă"

#: pynicotine/networkfilter.py:212
#, fuzzy
msgid "Oman"
msgstr "Oman"

#: pynicotine/networkfilter.py:213
#, fuzzy
msgid "Panama"
msgstr "Panama"

#: pynicotine/networkfilter.py:214
#, fuzzy
msgid "Peru"
msgstr "Peru"

#: pynicotine/networkfilter.py:215
#, fuzzy
msgid "French Polynesia"
msgstr "Polinezia Franceză"

#: pynicotine/networkfilter.py:216
#, fuzzy
msgid "Papua New Guinea"
msgstr "Papua Noua Guinee"

#: pynicotine/networkfilter.py:217
#, fuzzy
msgid "Philippines"
msgstr "Filipine"

#: pynicotine/networkfilter.py:218
#, fuzzy
msgid "Pakistan"
msgstr "Pakistan"

#: pynicotine/networkfilter.py:219
#, fuzzy
msgid "Poland"
msgstr "Polonia"

#: pynicotine/networkfilter.py:220
#, fuzzy
msgid "Saint Pierre & Miquelon"
msgstr "Saint Pierre & Miquelon"

#: pynicotine/networkfilter.py:221
#, fuzzy
msgid "Pitcairn"
msgstr "Pitcairn"

#: pynicotine/networkfilter.py:222
#, fuzzy
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: pynicotine/networkfilter.py:223
#, fuzzy
msgid "State of Palestine"
msgstr "Statul Palestinei"

#: pynicotine/networkfilter.py:224
#, fuzzy
msgid "Portugal"
msgstr "Portugalia"

#: pynicotine/networkfilter.py:225
#, fuzzy
msgid "Palau"
msgstr "Palau"

#: pynicotine/networkfilter.py:226
#, fuzzy
msgid "Paraguay"
msgstr "Paraguay"

#: pynicotine/networkfilter.py:227
#, fuzzy
msgid "Qatar"
msgstr "Qatar"

#: pynicotine/networkfilter.py:228
#, fuzzy
msgid "Réunion"
msgstr "Reuniune"

#: pynicotine/networkfilter.py:229
#, fuzzy
msgid "Romania"
msgstr "România"

#: pynicotine/networkfilter.py:230
#, fuzzy
msgid "Serbia"
msgstr "Serbia"

#: pynicotine/networkfilter.py:231
#, fuzzy
msgid "Russia"
msgstr "Rusia"

#: pynicotine/networkfilter.py:232
#, fuzzy
msgid "Rwanda"
msgstr "Rwanda"

#: pynicotine/networkfilter.py:233
#, fuzzy
msgid "Saudi Arabia"
msgstr "Arabia Saudită"

#: pynicotine/networkfilter.py:234
#, fuzzy
msgid "Solomon Islands"
msgstr "Insulele Solomon"

#: pynicotine/networkfilter.py:235
#, fuzzy
msgid "Seychelles"
msgstr "Seychelles"

#: pynicotine/networkfilter.py:236
#, fuzzy
msgid "Sudan"
msgstr "Sudan"

#: pynicotine/networkfilter.py:237
#, fuzzy
msgid "Sweden"
msgstr "Suedia"

#: pynicotine/networkfilter.py:238
#, fuzzy
msgid "Singapore"
msgstr "Singapore"

#: pynicotine/networkfilter.py:239
#, fuzzy
msgid "Saint Helena"
msgstr "Sfânta Elena"

#: pynicotine/networkfilter.py:240
#, fuzzy
msgid "Slovenia"
msgstr "Slovenia"

#: pynicotine/networkfilter.py:241
#, fuzzy
msgid "Svalbard & Jan Mayen Islands"
msgstr "Insulele Svalbard și Jan Mayen"

#: pynicotine/networkfilter.py:242
#, fuzzy
msgid "Slovak Republic"
msgstr "Republica Slovaca"

#: pynicotine/networkfilter.py:243
#, fuzzy
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: pynicotine/networkfilter.py:244
#, fuzzy
msgid "San Marino"
msgstr "San Marino"

#: pynicotine/networkfilter.py:245
#, fuzzy
msgid "Senegal"
msgstr "Senegal"

#: pynicotine/networkfilter.py:246
#, fuzzy
msgid "Somalia"
msgstr "Somalia"

#: pynicotine/networkfilter.py:247
#, fuzzy
msgid "Suriname"
msgstr "Surinam"

#: pynicotine/networkfilter.py:248
#, fuzzy
msgid "South Sudan"
msgstr "Sudul Sudanului"

#: pynicotine/networkfilter.py:249
#, fuzzy
msgid "Sao Tome & Principe"
msgstr "Sao Tome & Principe"

#: pynicotine/networkfilter.py:250
#, fuzzy
msgid "El Salvador"
msgstr "El Salvador"

#: pynicotine/networkfilter.py:251
#, fuzzy
msgid "Sint Maarten"
msgstr "Sint Maarten"

#: pynicotine/networkfilter.py:252
#, fuzzy
msgid "Syria"
msgstr "Siria"

#: pynicotine/networkfilter.py:253
#, fuzzy
msgid "Eswatini"
msgstr "Eswatini"

#: pynicotine/networkfilter.py:254
#, fuzzy
msgid "Turks & Caicos Islands"
msgstr "Insulele Turks și Caicos"

#: pynicotine/networkfilter.py:255
#, fuzzy
msgid "Chad"
msgstr "Ciad"

#: pynicotine/networkfilter.py:256
#, fuzzy
msgid "French Southern Territories"
msgstr "teritoriile din sudul Frantei"

#: pynicotine/networkfilter.py:257
#, fuzzy
msgid "Togo"
msgstr "A merge"

#: pynicotine/networkfilter.py:258
#, fuzzy
msgid "Thailand"
msgstr "Tailanda"

#: pynicotine/networkfilter.py:259
#, fuzzy
msgid "Tajikistan"
msgstr "Tadjikistan"

#: pynicotine/networkfilter.py:260
#, fuzzy
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
#, fuzzy
msgid "Timor-Leste"
msgstr "Timorul de Est"

#: pynicotine/networkfilter.py:262
#, fuzzy
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: pynicotine/networkfilter.py:263
#, fuzzy
msgid "Tunisia"
msgstr "Tunisia"

#: pynicotine/networkfilter.py:264
#, fuzzy
msgid "Tonga"
msgstr "Tonga"

#: pynicotine/networkfilter.py:265
#, fuzzy
msgid "Türkiye"
msgstr "Turcia"

#: pynicotine/networkfilter.py:266
#, fuzzy
msgid "Trinidad & Tobago"
msgstr "Trinidad și Tobago"

#: pynicotine/networkfilter.py:267
#, fuzzy
msgid "Tuvalu"
msgstr "Tuvalu"

#: pynicotine/networkfilter.py:268
#, fuzzy
msgid "Taiwan"
msgstr "Taiwan"

#: pynicotine/networkfilter.py:269
#, fuzzy
msgid "Tanzania"
msgstr "Tanzania"

#: pynicotine/networkfilter.py:270
#, fuzzy
msgid "Ukraine"
msgstr "Ucraina"

#: pynicotine/networkfilter.py:271
#, fuzzy
msgid "Uganda"
msgstr "Uganda"

#: pynicotine/networkfilter.py:272
#, fuzzy
msgid "U.S. Minor Outlying Islands"
msgstr "Insulele minore periferice ale SUA"

#: pynicotine/networkfilter.py:273
#, fuzzy
msgid "United States"
msgstr "Statele Unite"

#: pynicotine/networkfilter.py:274
#, fuzzy
msgid "Uruguay"
msgstr "Uruguay"

#: pynicotine/networkfilter.py:275
#, fuzzy
msgid "Uzbekistan"
msgstr "Uzbekistan"

#: pynicotine/networkfilter.py:276
#, fuzzy
msgid "Holy See (Vatican City State)"
msgstr "Sfântul Scaun (statul orașului Vatican)"

#: pynicotine/networkfilter.py:277
#, fuzzy
msgid "Saint Vincent & The Grenadines"
msgstr "Saint Vincent și Grenadinele"

#: pynicotine/networkfilter.py:278
#, fuzzy
msgid "Venezuela"
msgstr "Venezuela"

#: pynicotine/networkfilter.py:279
#, fuzzy
msgid "British Virgin Islands"
msgstr "Insulele Virgine Britanice"

#: pynicotine/networkfilter.py:280
#, fuzzy
msgid "U.S. Virgin Islands"
msgstr "Insulele Virgine americane"

#: pynicotine/networkfilter.py:281
#, fuzzy
msgid "Viet Nam"
msgstr "Vietnam"

#: pynicotine/networkfilter.py:282
#, fuzzy
msgid "Vanuatu"
msgstr "Vanuatu"

#: pynicotine/networkfilter.py:283
#, fuzzy
msgid "Wallis & Futuna"
msgstr "Wallis & Futuna"

#: pynicotine/networkfilter.py:284
#, fuzzy
msgid "Samoa"
msgstr "Samoa"

#: pynicotine/networkfilter.py:285
#, fuzzy
msgid "Yemen"
msgstr "Yemen"

#: pynicotine/networkfilter.py:286
#, fuzzy
msgid "Mayotte"
msgstr "Mayotte"

#: pynicotine/networkfilter.py:287
#, fuzzy
msgid "South Africa"
msgstr "Africa de Sud"

#: pynicotine/networkfilter.py:288
#, fuzzy
msgid "Zambia"
msgstr "Zambia"

#: pynicotine/networkfilter.py:289
#, fuzzy
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, fuzzy, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "Transpunerea textului în vorbire pentru mesaj a eșuat: %s"

#: pynicotine/nowplaying.py:130
#, fuzzy
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr ""
"Last.fm: vă rugăm să furnizați atât numele de utilizator Last.fm, cât și "
"cheia API"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
#, fuzzy
msgid "Now Playing Error"
msgstr "Acum se redă eroare"

#: pynicotine/nowplaying.py:140
#, fuzzy, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm: Nu s-a putut conecta la Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:162
#, fuzzy, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr ""
"Last.fm: Nu s-a putut obține melodia recentă de la Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:196
#, fuzzy
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: Nu am putut găsi un player MPRIS potrivit"

#: pynicotine/nowplaying.py:201
#, fuzzy, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr "S-au găsit mai multe playere MPRIS: %(players)s. Folosind: %(player)s"

#: pynicotine/nowplaying.py:204
#, fuzzy, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Player MPRIS detectat automat: %s"

#: pynicotine/nowplaying.py:219
#, fuzzy, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr "MPRIS: Ceva a mers prost la interogarea %(player)s: %(exception)s"

#: pynicotine/nowplaying.py:266
#, fuzzy
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "ListenBrainz: Vă rugăm să furnizați numele de utilizator ListenBrainz"

#: pynicotine/nowplaying.py:275
#, fuzzy, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "ListenBrainz: Nu s-a putut conecta la ListenBrainz: %(error)s"

#: pynicotine/nowplaying.py:283
#, fuzzy
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "ListenBrainz: Se pare că nu asculți nimic în acest moment"

#: pynicotine/nowplaying.py:297
#, fuzzy, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz: Nu s-a putut obține melodia curentă de la ListenBrainz: "
"%(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
#, fuzzy
msgid "Network Filters"
msgstr "Filtre de rețea"

#: pynicotine/plugins/core_commands/__init__.py:44
#, fuzzy
msgid "List available commands"
msgstr "Listează comenzile disponibile"

#: pynicotine/plugins/core_commands/__init__.py:49
#, fuzzy
msgid "Connect to the server"
msgstr "Nu s-a putut conecta la server. Motiv: %s"

#: pynicotine/plugins/core_commands/__init__.py:53
#, fuzzy
msgid "Disconnect from the server"
msgstr "Deconectat de la server %(host)s:%(port)s"

#: pynicotine/plugins/core_commands/__init__.py:58
#, fuzzy
msgid "Toggle away status"
msgstr "Dezactivați starea"

#: pynicotine/plugins/core_commands/__init__.py:62
#, fuzzy
msgid "Manage plugins"
msgstr "Gestionați pluginurile"

#: pynicotine/plugins/core_commands/__init__.py:74
#, fuzzy
msgid "Clear chat window"
msgstr "Ștergeți fereastra de chat"

#: pynicotine/plugins/core_commands/__init__.py:80
#, fuzzy
msgid "Say something in the third-person"
msgstr "Spune ceva la persoana a treia"

#: pynicotine/plugins/core_commands/__init__.py:87
#, fuzzy
msgid "Announce the song currently playing"
msgstr "Anunțați melodia care se redă în prezent"

#: pynicotine/plugins/core_commands/__init__.py:94
#, fuzzy
msgid "Join chat room"
msgstr "Alăturați-vă camerei de chat"

#: pynicotine/plugins/core_commands/__init__.py:102
#, fuzzy
msgid "Leave chat room"
msgstr "Părăsiți camera de chat"

#: pynicotine/plugins/core_commands/__init__.py:110
#, fuzzy
msgid "Say message in specified chat room"
msgstr "Spuneți mesajul în camera de chat specificată"

#: pynicotine/plugins/core_commands/__init__.py:117
#, fuzzy
msgid "Open private chat"
msgstr "Deschide chatul privat"

#: pynicotine/plugins/core_commands/__init__.py:125
#, fuzzy
msgid "Close private chat"
msgstr "Închide chatul privat"

#: pynicotine/plugins/core_commands/__init__.py:133
#, fuzzy
msgid "Request user's client version"
msgstr "Solicitați versiunea client a utilizatorului"

#: pynicotine/plugins/core_commands/__init__.py:142
#, fuzzy
msgid "Send private message to user"
msgstr "Trimiteți mesaj privat utilizatorului"

#: pynicotine/plugins/core_commands/__init__.py:150
#, fuzzy
msgid "Add user to buddy list"
msgstr "Adăugați un utilizator la lista de prieteni"

#: pynicotine/plugins/core_commands/__init__.py:158
#, fuzzy
msgid "Remove buddy from buddy list"
msgstr "Eliminați prietenul din lista de prieteni"

#: pynicotine/plugins/core_commands/__init__.py:166
#, fuzzy
msgid "Browse files of user"
msgstr "Răsfoiți fișierele utilizatorului"

#: pynicotine/plugins/core_commands/__init__.py:175
#, fuzzy
msgid "Show user profile information"
msgstr "Afișați informațiile despre profilul utilizatorului"

#: pynicotine/plugins/core_commands/__init__.py:183
#, fuzzy
msgid "Show IP address or username"
msgstr "Afișați adresa IP sau numele de utilizator"

#: pynicotine/plugins/core_commands/__init__.py:190
#, fuzzy
msgid "Block connections from user or IP address"
msgstr "Blocați conexiunile de la utilizator sau adresa IP"

#: pynicotine/plugins/core_commands/__init__.py:197
#, fuzzy
msgid "Remove user or IP address from ban lists"
msgstr "Eliminați utilizatorul sau adresa IP din listele de interdicție"

#: pynicotine/plugins/core_commands/__init__.py:204
#, fuzzy
msgid "Silence messages from user or IP address"
msgstr "Opriți mesajele de la utilizator sau adresa IP"

#: pynicotine/plugins/core_commands/__init__.py:212
#, fuzzy
msgid "Remove user or IP address from ignore lists"
msgstr "Eliminați utilizatorul sau adresa IP din listele de ignorare"

#: pynicotine/plugins/core_commands/__init__.py:220
#, fuzzy
msgid "Add share"
msgstr "Adăugați Wi_sh"

#: pynicotine/plugins/core_commands/__init__.py:226
#, fuzzy
msgid "Remove share"
msgstr "Eliminați Wi_sh"

#: pynicotine/plugins/core_commands/__init__.py:233
#, fuzzy
msgid "List shares"
msgstr "Listează cotele"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Rescanează partajările"

#: pynicotine/plugins/core_commands/__init__.py:246
#, fuzzy
msgid "Start global file search"
msgstr "Începeți căutarea globală de fișiere"

#: pynicotine/plugins/core_commands/__init__.py:254
#, fuzzy
msgid "Search files in joined rooms"
msgstr "Căutați fișiere în sălile unite"

#: pynicotine/plugins/core_commands/__init__.py:262
#, fuzzy
msgid "Search files of all buddies"
msgstr "Căutați fișierele tuturor prietenilor"

#: pynicotine/plugins/core_commands/__init__.py:270
#, fuzzy
msgid "Search a user's shared files"
msgstr "Căutați fișierele partajate ale unui utilizator"

#: pynicotine/plugins/core_commands/__init__.py:296
#, fuzzy, python-format
msgid "Listing %(num)i available commands:"
msgstr "Listarea comenzilor disponibile %(num)i:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, fuzzy, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr ""
"Listarea comenzilor disponibile %(num)i care se potrivesc cu „%(query)s”:"

#: pynicotine/plugins/core_commands/__init__.py:311
#, fuzzy, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Tastați %(command)s pentru a lista comenzi similare"

#: pynicotine/plugins/core_commands/__init__.py:314
#, fuzzy, python-format
msgid "Type %(command)s to list available commands"
msgstr "Tastați %(command)s pentru a lista comenzile disponibile"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, fuzzy, python-format
msgid "Not joined in room %s"
msgstr "Nu a fost alăturat în cameră %s"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:408
#, fuzzy, python-format
msgid "Closed private chat of user %s"
msgstr "Închide chatul privat"

#: pynicotine/plugins/core_commands/__init__.py:476
#, fuzzy, python-format
msgid "Banned %s"
msgstr "Banat %s"

#: pynicotine/plugins/core_commands/__init__.py:490
#, fuzzy, python-format
msgid "Unbanned %s"
msgstr "Neinterzis %s"

#: pynicotine/plugins/core_commands/__init__.py:503
#, fuzzy, python-format
msgid "Ignored %s"
msgstr "Ignorat %s"

#: pynicotine/plugins/core_commands/__init__.py:517
#, fuzzy, python-format
msgid "Unignored %s"
msgstr "Neignorat %s"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr "%(num_listed)s partajări listate %(num_total)s configurate)"

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr "Nu se poate partaja dosarul inaccesibil: \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr "Nicio partajare cu numele \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr "Partajarea \"%s\" a fost eliminată (necesită rescanare)"

#: pynicotine/pluginsystem.py:413
#, fuzzy
msgid "Loading plugin system"
msgstr "Se încarcă sistemul de pluginuri"

#: pynicotine/pluginsystem.py:516
#, fuzzy, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Nu se poate încărca pluginul %(name)s. Numele folderului pluginului conține "
"caractere nevalide: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, fuzzy, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "Comandă %(interface)s în conflict în pluginul %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, fuzzy, python-format
msgid "Loaded plugin %s"
msgstr "Plugin încărcat %s"

#: pynicotine/pluginsystem.py:579
#, fuzzy, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Nu se poate încărca pluginul %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, fuzzy, python-format
msgid "Unloaded plugin %s"
msgstr "Plugin descărcat %s"

#: pynicotine/pluginsystem.py:648
#, fuzzy, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Nu se poate descărca pluginul %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, fuzzy, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Pluginul %(module)s a eșuat cu eroarea %(errortype)s: %(error)s.\n"
"Urmărire: %(trace)s"

#: pynicotine/pluginsystem.py:810
#, fuzzy
msgid "No description"
msgstr "fără descriere"

#: pynicotine/pluginsystem.py:887
#, fuzzy, python-format
msgid "Missing %s argument"
msgstr "Argumentul %s lipsește"

#: pynicotine/pluginsystem.py:896
#, fuzzy, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Argument nevalid, opțiuni posibile: %s"

#: pynicotine/pluginsystem.py:901
#, fuzzy, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Utilizare: %(command)s %(args)s"

#: pynicotine/pluginsystem.py:940
#, fuzzy, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Comanda necunoscută: %(command)s. Tastați %(help_command)s pentru a lista "
"comenzile disponibile."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
#, fuzzy
msgid "No UPnP devices found"
msgstr "Nu s-au găsit dispozitive UPnP"

#: pynicotine/portmapper.py:633
#, fuzzy, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"%(protocol)s: Nu s-a redirecționat portul extern %(external_port)s: %(error)s"

#: pynicotine/portmapper.py:647
#, fuzzy, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"%(protocol)s: Portul extern %(external_port)s a fost redirecționat cu succes "
"către adresa IP locală %(ip_address)s portul %(local_port)s"

#: pynicotine/privatechat.py:220
#, fuzzy, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Mesaj privat de la utilizatorul „%(user)s”: %(message)s"

#: pynicotine/search.py:368
#, fuzzy, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Se caută articolul din lista de dorințe „%s”"

#: pynicotine/search.py:434
#, fuzzy, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Perioada de așteptare a listei de dorințe setată la %s secunde"

#: pynicotine/search.py:760
#, fuzzy, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr "Utilizatorul %(user)s caută „%(query)s”, a găsit rezultate %(num)i."

#: pynicotine/shares.py:302
#, fuzzy
msgid "Rebuilding shares…"
msgstr "Refacerea acțiunilor…"

#: pynicotine/shares.py:302
#, fuzzy
msgid "Rescanning shares…"
msgstr "Se rescanează acțiunile…"

#: pynicotine/shares.py:324
#, fuzzy, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Rescanare finalizată: dosare %(num)s găsite"

#: pynicotine/shares.py:334
#, fuzzy, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"A apărut o eroare gravă la rescanarea distribuirilor. Dacă această problemă "
"persistă, ștergeți %(dir)s/*.db și încercați din nou. Dacă acest lucru nu "
"ajută, vă rugăm să trimiteți un raport de eroare cu această urmărire a "
"stivei inclusă: %(trace)s"

#: pynicotine/shares.py:582
#, fuzzy, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Eroare la scanarea fișierului %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, fuzzy, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Eroare la scanarea folderului %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, fuzzy, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "Eroare la scanarea metadatelor pentru fișierul %(path)s: %(error)s"

#: pynicotine/shares.py:1046
#, fuzzy, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Rescanarea a fost anulată din cauza partajărilor indisponibile: %s"

#: pynicotine/shares.py:1184
#, fuzzy, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "Utilizatorul %(user)s răsfoiește lista dvs. de fișiere partajate"

#: pynicotine/slskmessages.py:3120
#, fuzzy, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Imposibil de citit baza de date cu acțiuni. Vă rugăm să scanați din nou "
"acțiunile dvs. Eroare: %s"

#: pynicotine/slskproto.py:500
#, fuzzy, python-format
msgid "Specified network interface '%s' is not available"
msgstr "Interfața de rețea specificată „%s” nu este disponibilă"

#: pynicotine/slskproto.py:511
#, fuzzy, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Nu se poate asculta pe portul %(port)s. Asigurați-vă că nicio altă aplicație "
"nu îl folosește sau alegeți un alt port. Eroare: %(error)s"

#: pynicotine/slskproto.py:523
#, fuzzy, python-format
msgid "Listening on port: %i"
msgstr "Ascultare pe portul: %i"

#: pynicotine/slskproto.py:805
#, fuzzy, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "Nu se poate conecta la server %(host)s:%(port)s: %(error)s"

#: pynicotine/slskproto.py:1095
#, fuzzy, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Conectare automată la server la pornire"

#: pynicotine/slskproto.py:1170
#, fuzzy, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Conectarea la %(host)s:%(port)s"

#: pynicotine/slskproto.py:1208
#, fuzzy, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Conectat la server %(host)s:%(port)s, conectare…"

#: pynicotine/slskproto.py:1493
#, fuzzy, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Deconectat de la server %(host)s:%(port)s"

#: pynicotine/slskproto.py:1499
#, fuzzy
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Cineva sa conectat la contul tău Soulseek în altă parte"

#: pynicotine/uploads.py:382
#, fuzzy, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Încărcare finalizată: utilizator %(user)s, adresa IP %(ip)s, fișier %(file)s"

#: pynicotine/uploads.py:395
#, fuzzy, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Încărcarea a fost anulată, fișierul utilizatorului %(user)s %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, fuzzy, python-format
msgid "Upload I/O error: %s"
msgstr "Eroare de încărcare I/O: %s"

#: pynicotine/uploads.py:1103
#, fuzzy, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Încărcarea a început: utilizator %(user)s, adresa IP %(ip)s, fișier %(file)s"

#: pynicotine/userbrowse.py:176
#, fuzzy, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr "Nu se poate crea directorul „%(folder)s”, eroare raportată: %(error)s"

#: pynicotine/userbrowse.py:236
#, fuzzy, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Încărcarea partajărilor de pe disc a eșuat: %(error)s"

#: pynicotine/userbrowse.py:282
#, fuzzy, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr ""
"Lista salvată de fișiere partajate pentru utilizatorul „%(user)s” la %(dir)s"

#: pynicotine/userbrowse.py:286
#, fuzzy, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr "Nu se pot salva distribuiri, „%(user)s”, eroare raportată: %(error)s"

#: pynicotine/userinfo.py:160
#, fuzzy, python-format
msgid "Picture saved to %s"
msgstr "Imaginea salvată pe %s"

#: pynicotine/userinfo.py:163
#, fuzzy, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Nu se poate salva fotografia pe %(filename)s: %(error)s"

#: pynicotine/userinfo.py:190
#, fuzzy, python-format
msgid "User %(user)s is viewing your profile"
msgstr "Utilizatorul %(user)s vă vizualizează profilul"

#: pynicotine/users.py:272
#, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Nu s-a putut conecta la server. Motiv: %s"

#: pynicotine/users.py:272
msgid "Cannot Connect"
msgstr "Nu s-a putut conecta"

#: pynicotine/users.py:306
#, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr ""
"Nu s-a putut recupera adresa IP a utilizatorului %s, deoarece acest "
"utilizator este offline"

#: pynicotine/users.py:315
#, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr "Adresa IP a utilizatorului %(user)s: %(ip)s, port %(port)i%(country)s"

#: pynicotine/users.py:433
msgid "Soulseek Announcement"
msgstr "Anunț Soulseek"

#: pynicotine/users.py:449
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"Nu aveți privilegii Soulseek. În timp ce privilegiile sunt active, "
"descărcările dvs. vor fi puse în coadă înaintea celor ale utilizatorilor "
"fără privilegii."

#: pynicotine/users.py:455
#, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"%(days)i zile, %(hours)i ore, %(minutes)i minute, %(seconds)i secunde de "
"privilegii Soulseek rămase"

#: pynicotine/users.py:473
msgid "Your password has been changed"
msgstr "Parola dvs. a fost schimbată"

#: pynicotine/users.py:473
msgid "Password Changed"
msgstr "Parolă schimbată"

#: pynicotine/utils.py:574
#, fuzzy, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Nu se poate deschide calea fișierului %(path)s: %(error)s"

#: pynicotine/utils.py:621
#, fuzzy, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "Nu se poate deschide adresa URL %(url)s: %(error)s"

#: pynicotine/utils.py:646
#, fuzzy, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr "A apărut o eroare la citirea fișierului %(filename)s: %(error)s"

#: pynicotine/utils.py:651
#, fuzzy, python-format
msgid "Attempting to load backup of file %s"
msgstr "Se încearcă încărcarea copiei de rezervă a fișierului %s"

#: pynicotine/utils.py:673
#, fuzzy, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Nu se poate face backup pentru fișierul %(path)s: %(error)s"

#: pynicotine/utils.py:694
#, fuzzy, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Nu se poate salva fișierul %(path)s: %(error)s"

#: pynicotine/utils.py:705
#, fuzzy, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Nu se poate restabili fișierul anterior %(path)s: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
#, fuzzy
msgid "Add buddy…"
msgstr "Adauga prieten…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
#, fuzzy
msgid "Toggle Text-to-Speech"
msgstr "Comutați text-to-speech"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
#, fuzzy
msgid "Chat Room Command Help"
msgstr "Ajutor pentru comanda camerei de chat"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
#, fuzzy
msgid "_Log"
msgstr "_Buturuga"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
#, fuzzy
msgid "Room Wall"
msgstr "Peretele camerei"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
#, fuzzy
msgid "R_oom Wall"
msgstr "R_oom Wall"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
#, fuzzy
msgid "Created by"
msgstr "Creat de"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
#, fuzzy
msgid "Translated by"
msgstr "Tradus de"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
#, fuzzy
msgid "License"
msgstr "Licență"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
#, fuzzy
msgid "Welcome to Nicotine+"
msgstr "Bun venit la Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
#, fuzzy
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Dacă numele de utilizator dorit este deja luat, vi se va solicita să îl "
"schimbați."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
#, fuzzy
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Dacă portul dvs. de ascultare este închis, vă veți putea conecta numai la "
"utilizatori ale căror porturi de ascultare sunt deschise."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
#, fuzzy
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"Dacă este necesar, alegeți un alt port de ascultare de mai jos. Acest lucru "
"se poate face și mai târziu în preferințe."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
#, fuzzy
msgid "Download Files to Folder"
msgstr "Descărcați fișiere în folder"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
#, fuzzy
msgid "Share Folders"
msgstr "Partajați foldere"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
#, fuzzy
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Utilizatorii Soulseek vor putea descărca din acțiunile dvs. Contribuiți la "
"rețeaua Soulseek partajând propria colecție și redistribuind ceea ce ați "
"descărcat de la alți utilizatori."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
#, fuzzy
msgid "You are ready to use Nicotine+!"
msgstr "Ești gata să folosești Nicotine+!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr "Soulseek este un protocol necriptat și nu oferă comunicație sigură."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
#, fuzzy
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Donarea către Soulseek vă oferă privilegii pentru o anumită perioadă de "
"timp. Dacă aveți privilegii, descărcările dvs. vor fi puse în coadă înaintea "
"utilizatorilor fără privilegii."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
#, fuzzy
msgid "Previous File"
msgstr "Fișierul anterior"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
#, fuzzy
msgid "Next File"
msgstr "Fișierul următor"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
#, fuzzy
msgid "Name"
msgstr "Nume"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
#, fuzzy
msgid "Last Speed"
msgstr "Ultima viteză"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
#, fuzzy
msgid "_Export…"
msgstr "_Export…"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
#, fuzzy
msgid "Keyboard Shortcuts"
msgstr "Comenzi rapide de la tastatură"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
#, fuzzy
msgid "General"
msgstr "General"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
#, fuzzy
msgid "Connect"
msgstr "Conectați"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
#, fuzzy
msgid "Disconnect"
msgstr "Deconectat"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
#, fuzzy
msgid "Rescan Shares"
msgstr "Rescanați acțiunile"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
#, fuzzy
msgid "Show Log Pane"
msgstr "Afișați panoul de jurnal"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
#, fuzzy
msgid "Confirm Quit"
msgstr "Configurați chat-uri"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
#, fuzzy
msgid "Quit"
msgstr "Părăsi"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
#, fuzzy
msgid "Menus"
msgstr "Meniuri"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
#, fuzzy
msgid "Open Main Menu"
msgstr "Deschideți meniul principal"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
#, fuzzy
msgid "Open Context Menu"
msgstr "Deschideți meniul contextual"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
#, fuzzy
msgid "Tabs"
msgstr "Filele"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
#, fuzzy
msgid "Change Main Tab"
msgstr "Schimbați fila principală"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
#, fuzzy
msgid "Go to Previous Secondary Tab"
msgstr "Accesați fila secundară anterioară"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
#, fuzzy
msgid "Go to Next Secondary Tab"
msgstr "Accesați următoarea filă secundară"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
#, fuzzy
msgid "Reopen Closed Secondary Tab"
msgstr "Închideți fila secundară"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
#, fuzzy
msgid "Close Secondary Tab"
msgstr "Închideți fila secundară"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
#, fuzzy
msgid "Lists"
msgstr "Liste"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
#, fuzzy
msgid "Copy Selected Cell"
msgstr "Copiați celula selectată"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
#, fuzzy
msgid "Select All"
msgstr "Selectează tot"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
#, fuzzy
msgid "Find"
msgstr "Găsi"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
#, fuzzy
msgid "Remove Selected Row"
msgstr "Eliminați rândul selectat"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
#, fuzzy
msgid "Editing"
msgstr "Editare"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
#, fuzzy
msgid "Cut"
msgstr "A tăia"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
#, fuzzy
msgid "Paste"
msgstr "Pastă"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
#, fuzzy
msgid "Insert Emoji"
msgstr "Introduceți emoji"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
#, fuzzy
msgid "File Transfers"
msgstr "Transferuri de fișiere"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
#, fuzzy
msgid "Resume / Retry Transfer"
msgstr "Reluați/Reîncercați transferul"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
#, fuzzy
msgid "Pause / Abort Transfer"
msgstr "Întrerupeți / Anulați transferul"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
#, fuzzy
msgid "Download / Upload To"
msgstr "Descărcați/Încărcați în"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
#, fuzzy
msgid "Save List to Disk"
msgstr "Salvați lista pe disc"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
#, fuzzy
msgid "Refresh"
msgstr "Reîmprospăta"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
#, fuzzy
msgid "Expand / Collapse All"
msgstr "Extinde / Restrânge toate"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
#, fuzzy
msgid "Back to Parent Folder"
msgstr "Înapoi la dosarul părinte"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
#, fuzzy
msgid "File Search"
msgstr "Căutare fișier"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
#, fuzzy
msgid "Result Filters"
msgstr "Filtre de rezultate"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
#, fuzzy
msgid "Current Session"
msgstr "Sesiunea curenta"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
#, fuzzy
msgid "Completed Downloads"
msgstr "Descărcări finalizate"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
#, fuzzy
msgid "Downloaded Size"
msgstr "Dimensiune descărcată"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
#, fuzzy
msgid "Completed Uploads"
msgstr "Încărcări finalizate"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
#, fuzzy
msgid "Uploaded Size"
msgstr "Dimensiune încărcată"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
#, fuzzy
msgid "Total"
msgstr "Total"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
#, fuzzy
msgid "_Reset…"
msgstr "_Resetați…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
#, fuzzy
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Articolele din lista de dorințe sunt căutate automat la intervale regulate, "
"pentru a descoperi fișiere neobișnuite."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
#, fuzzy
msgid "Add Wish…"
msgstr "Adăugați o dorință…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
#, fuzzy
msgid "Clear All…"
msgstr "Curata tot…"

#: pynicotine/gtkgui/ui/downloads.ui:138
#, fuzzy
msgid "Clear All Finished/Filtered Downloads"
msgstr "Ștergeți toate descărcările finalizate/filtrate"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
#, fuzzy
msgid "Clear Finished"
msgstr "Clar Terminat"

#: pynicotine/gtkgui/ui/downloads.ui:169
#, fuzzy
msgid "Clear Specific Downloads"
msgstr "Ștergeți Descărcări specifice"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
#, fuzzy
msgid "Clear _All…"
msgstr "Curata tot…"

#: pynicotine/gtkgui/ui/interests.ui:21
#, fuzzy
msgid "Personal Interests"
msgstr "Interese personale"

#: pynicotine/gtkgui/ui/interests.ui:40
#, fuzzy
msgid "Add something you like…"
msgstr "Adăugați ceva care vă place…"

#: pynicotine/gtkgui/ui/interests.ui:62
#, fuzzy
msgid "Personal Dislikes"
msgstr "Antipatiile personale"

#: pynicotine/gtkgui/ui/interests.ui:80
#, fuzzy
msgid "Add something you dislike…"
msgstr "Adăugați ceva ce nu vă place…"

#: pynicotine/gtkgui/ui/interests.ui:143
#, fuzzy
msgid "Refresh Recommendations"
msgstr "Reîmprospătați recomandările"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
#, fuzzy
msgid "Main Menu"
msgstr "Meniu principal"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
#, fuzzy
msgid "Room…"
msgstr "Cameră…"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
#, fuzzy
msgid "Username…"
msgstr "Nume de utilizator…"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
#, fuzzy
msgid "Search term…"
msgstr "Termen de căutare…"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
#, fuzzy
msgid "Clear"
msgstr "clar"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
#, fuzzy
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Modele de căutare: cu un cuvânt = termen, fără un cuvânt = -term, cuvânt "
"parțial = *erm"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
#, fuzzy
msgid "Search Scope"
msgstr "Domeniul de căutare"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
#, fuzzy
msgid "_Wishlist"
msgstr "_Lista de dorințe"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
#, fuzzy
msgid "Configure Searches"
msgstr "Configurați căutările"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
#, fuzzy
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Introduceți un termen de căutare pentru a căuta fișiere partajate de alți "
"utilizatori din rețeaua Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
#, fuzzy
msgid "File Grouping Mode"
msgstr "Modul de grupare a fișierelor"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
#, fuzzy
msgid "Configure Downloads"
msgstr "Configurați Descărcări"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
#, fuzzy
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Fișierele pe care le descărcați de la alți utilizatori sunt puse în coadă "
"aici și pot fi întrerupte și reluate la cerere"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
#, fuzzy
msgid "Configure Uploads"
msgstr "Configurați încărcările"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
#, fuzzy
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Încercările utilizatorilor de a descărca fișierele dvs. partajate sunt puse "
"în coadă și gestionate aici"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
#, fuzzy
msgid "_Open List"
msgstr "_Deschide lista"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
#, fuzzy
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Deschide o listă locală de fișiere partajate care au fost salvate anterior "
"pe disc"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
#, fuzzy
msgid "Configure Shares"
msgstr "Configurați partajări"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
#, fuzzy
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Introduceți numele unui utilizator, ale cărui fișiere partajate doriți să le "
"căutați. Puteți, de asemenea, să salvați lista pe disc și să o inspectați "
"mai târziu."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
#, fuzzy
msgid "_Personal Profile"
msgstr "_Profil personal"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
#, fuzzy
msgid "Configure Account"
msgstr "Configurați chat-uri"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
#, fuzzy
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Introduceți numele unui utilizator pentru a vedea descrierea utilizatorului, "
"informațiile și fotografia personală"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
#, fuzzy
msgid "Chat _History"
msgstr "Chat _Istoric"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
#, fuzzy
msgid "Configure Chats"
msgstr "Configurați chat-uri"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
#, fuzzy
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr ""
"Introduceți numele unui utilizator pentru a începe o conversație text cu "
"acesta în privat"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
#, fuzzy
msgid "_Message All"
msgstr "_Mesajul tuturor"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
#, fuzzy
msgid "Configure Ignored Users"
msgstr "Utilizatori ignorați"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
#, fuzzy
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Adăugați utilizatori ca prieteni pentru a partaja anumite dosare cu ei și "
"pentru a primi notificări atunci când sunt online"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
#, fuzzy
msgid "Join or create room…"
msgstr "Alăturați-vă sau creați o cameră…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
#, fuzzy
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Alăturați-vă unei camere de chat existente sau creați o cameră nouă pentru a "
"discuta cu alți utilizatori din rețeaua Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
#, fuzzy
msgid "Configure User Profile"
msgstr "Vizualizați profilul utilizatorului"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
#, fuzzy
msgid "Connections"
msgstr "Conexiuni"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
#, fuzzy
msgid "Downloading (Speed / Active Users)"
msgstr "Descărcare (Viteză/Utilizatori activi)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
#, fuzzy
msgid "Uploading (Speed / Active Users)"
msgstr "Încărcare (Viteză/Utilizatori activi)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
#, fuzzy
msgid "Search chat history…"
msgstr "Termen de căutare…"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
#, fuzzy
msgid "Download Speed Limits"
msgstr "Descărcați Limite de viteză"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
#, fuzzy
msgid "Unlimited download speed"
msgstr "Viteză de descărcare nelimitată"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
#, fuzzy
msgid "Use download speed limit (KiB/s):"
msgstr "Utilizați limita de viteză de descărcare (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
#, fuzzy
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Utilizați o limită alternativă de viteză de descărcare (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
#, fuzzy
msgid "Search rooms…"
msgstr "Termen de căutare…"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
#, fuzzy
msgid "Refresh Rooms"
msgstr "Reîmprospătați camerele"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
#, fuzzy
msgid "_Show feed of public chat room messages"
msgstr "_Afișați fluxul de mesaje publice din camera de chat"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
#, fuzzy
msgid "_Accept private room invitations"
msgstr "_Acceptați invitațiile în camere private"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
#, fuzzy
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Caracteristica peretelui camerei permite utilizatorilor dintr-o cameră să "
"specifice un mesaj unic pentru a-l afișa altora. Mesajele recente sunt "
"afișate în partea de sus."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
#, fuzzy
msgid "Set wall message…"
msgstr "Setați mesajul de pe perete…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
#, fuzzy
msgid "Search Result Filters"
msgstr "Filtre de rezultate ale căutării"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
#, fuzzy
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Filtrele rezultatelor căutării sunt folosite pentru a rafina rezultatele "
"căutării care sunt afișate."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
#, fuzzy
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Fiecare listă de rezultate ale căutării are propriul filtru care poate fi "
"dezvăluit prin comutarea butonului Filtre de rezultate. Un filtru este "
"format din mai multe câmpuri, toate fiind aplicate atunci când apăsați Enter "
"în oricare dintre câmpurile sale. Filtrarea se aplică imediat rezultatelor "
"deja primite, precum și celor care urmează să ajungă."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
#, fuzzy
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"După cum sugerează și numele, un filtru pentru rezultatele căutării nu poate "
"extinde căutarea inițială, ci doar o poate restrânge. Pentru a extinde sau a "
"modifica termenii de căutare, efectuați o nouă căutare."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
#, fuzzy
msgid "Result Filter Usage"
msgstr "Utilizarea filtrului de rezultate"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
#, fuzzy
msgid "Include Text"
msgstr "Includeți text"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
#, fuzzy
msgid "Files, folders and usernames containing this text will be shown."
msgstr ""
"Fișierele, folderele și numele de utilizator care conțin acest text vor fi "
"afișate."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
#, fuzzy
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Lipsa majusculelor este insensibilă, dar ordinea cuvintelor este importantă: "
"„Instrumental Remix” nu va afișa niciun „Remix Instrumental”"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
#, fuzzy
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Utilizați | (sau conducte) pentru a separa mai multe fraze exacte. Exemplu:\n"
"    Remix|Dub Mix|Instrumental"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
#, fuzzy
msgid "Exclude Text"
msgstr "Excludeți text"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
#, fuzzy
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"Ca mai sus, dar fișierele, folderele și numele de utilizator sunt filtrate "
"dacă textul se potrivește."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
#, fuzzy
msgid "Filters files based upon their file extension."
msgstr "Filtrează fișierele în funcție de extensia lor."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
#, fuzzy
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Pot fi specificate mai multe extensii de fișiere, care la rândul lor vor "
"dezvălui mai multe din lista de rezultate. Exemplu:\n"
"    flac wav maimuţă"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
#, fuzzy
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"De asemenea, este posibil să inversați filtrul, specificând extensiile de "
"fișiere pe care nu le doriți în rezultate cu un semn de exclamare! Exemplu:\n"
"    !mp3 !jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
#, fuzzy
msgid "File Size"
msgstr "Mărime fișier"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
#, fuzzy
msgid "Filters files based upon their file size."
msgstr "Filtrează fișierele în funcție de dimensiunea acestora."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
#, fuzzy
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"În mod implicit, unitatea utilizată este octeții (B) și fișierele mai mari "
"sau egale cu (>=), valoarea va fi potrivită."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
#, fuzzy
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Adăugați b, k, m sau g (alternativ kib, mib sau gib) pentru a specifica "
"unități de octet, kibibyte, mebibyte sau gibibyte:\n"
"    20 m pentru a afișa fișiere mai mari de 20 MiB (mebibytes)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
#, fuzzy
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Prepend = la o valoare pentru a specifica o potrivire exactă:\n"
"    =1024 corespunde fișierelor care au exact 1 KiB (kibibyte)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
#, fuzzy
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Predată! la o valoare pentru a exclude fișierele de o anumită dimensiune:\n"
"    !30,5 m pentru a ascunde fișierele care au 30,5 MiB (mebiocteți)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
#, fuzzy
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Adăugați < sau > pentru a găsi fișiere mai mici/mai mari decât valoarea "
"dată. Folosiți un spațiu între fiecare condiție pentru a include un "
"interval:\n"
"    >10,5 m <1 g pentru a afișa fișiere mai mari de 10,5 MiB, dar mai mici "
"de 1 GiB."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
#, fuzzy
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Variantele mai cunoscute kb, mb și gb pot fi utilizate și pentru unități "
"kilobyte, megabyte și gigabyte."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
#, fuzzy
msgid "Filters files based upon their bitrate."
msgstr "Filtrează fișierele în funcție de rata de biți."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
#, fuzzy
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Valorile trebuie introduse numai ca cifre numerice. Unitatea este "
"întotdeauna Kb/s (kilobiți pe secundă)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
#, fuzzy
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Ca și dimensiunea fișierului (mai sus), pot fi utilizați operatorii =, !, <, "
">, <= sau >= și pot fi specificate mai multe condiții, de exemplu pentru a "
"afișa fișiere cu un bitrate de cel puțin 256 Kb/s cu un maxim rata de biți "
"de 1411 Kb/s:\n"
"    256 <=1411"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
#, fuzzy
msgid "Filters files based upon their duration."
msgstr "Filtrează fișierele în funcție de durata lor."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
#, fuzzy
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"În mod implicit, fișierele mai lungi sau egale cu (>=) durata introdusă vor "
"fi potrivite, cu excepția cazului în care se folosește un operator (=, !, "
"<=, < sau >)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
#, fuzzy
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Introduceți o valoare brută în secunde sau utilizați formatele de timp MM:SS "
"și HH:MM:SS:\n"
"    =53 afișează fișierele care au aproximativ 53 de secunde.\n"
"    >5:30 pentru a afișa fișiere cu o durată mai mare de 5 minute și "
"jumătate.\n"
"    <5:30:00 afișează fișiere de mai puțin de 5 ore și jumătate."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
#, fuzzy
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Pot fi specificate mai multe condiții:\n"
"    >6:00 <12:00 pentru a afișa fișiere cu durata cuprinsă între 6 și 12 "
"minute.\n"
"    !9:54 !8:43 !7:32 pentru a ascunde unele fișiere specifice din "
"rezultate.\n"
"    =5:34 =4:23 =3:05 pentru a include fișiere cu durate specifice."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
#, fuzzy
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Filtrează fișierele în funcție de locația geografică a utilizatorilor, "
"conform codurilor de țară definite de ISO 3166-2:\n"
"    SUA vor afișa numai rezultate de la utilizatorii cu adrese IP din "
"Statele Unite.\n"
"    !GB va ascunde rezultatele care vin de la utilizatorii din Marea "
"Britanie."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
#, fuzzy
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Mai multe țări pot fi specificate cu virgule sau spații."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
#, fuzzy
msgid "Free Slot"
msgstr "Slot gratuit"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
#, fuzzy
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Afișați numai acele rezultate de la utilizatorii care au cel puțin un spațiu "
"de încărcare liber, adică fișiere care sunt disponibile imediat."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
#, fuzzy
msgid "Upload Speed Limits"
msgstr "Limite de viteză de încărcare"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
#, fuzzy
msgid "Unlimited upload speed"
msgstr "Viteză de încărcare nelimitată"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
#, fuzzy
msgid "Use upload speed limit (KiB/s):"
msgstr "Utilizați limita de viteză de încărcare (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
#, fuzzy
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Utilizați o limită alternativă de viteză de încărcare (KiB/s):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
#, fuzzy
msgid "Private Chat Command Help"
msgstr "Ajutor pentru comanda de chat privat"

#: pynicotine/gtkgui/ui/search.ui:7
#, fuzzy
msgid "Include text…"
msgstr "Includeți text…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
#, fuzzy
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtrați rezultatele ale căror căi ale fișierelor conțin textul specificat. "
"Pot fi specificate mai multe expresii și cuvinte, de ex. frază exactă|muzică|"
"termen|fraza exactă doi"

#: pynicotine/gtkgui/ui/search.ui:18
#, fuzzy
msgid "Exclude text…"
msgstr "Excludeți text…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
#, fuzzy
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtrați rezultatele ale căror căi ale fișierelor conțin textul specificat. "
"Pot fi specificate mai multe expresii și cuvinte, de ex. frază exactă|muzică|"
"termen|fraza exactă doi"

#: pynicotine/gtkgui/ui/search.ui:29
#, fuzzy
msgid "File type…"
msgstr "Tip fișier…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
#, fuzzy
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Tip de fișier, de ex. flac wav sau !mp3 !m4a"

#: pynicotine/gtkgui/ui/search.ui:40
#, fuzzy
msgid "File size…"
msgstr "Mărime fișier…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
#, fuzzy
msgid "File size, e.g. >10.5m <1g"
msgstr "Dimensiunea fișierului, de ex. >10,5m <1g"

#: pynicotine/gtkgui/ui/search.ui:51
#, fuzzy
msgid "Bitrate…"
msgstr "Rata de biți…"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
#, fuzzy
msgid "Bitrate, e.g. 256 <1412"
msgstr "Rata de biți, de ex. 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
#, fuzzy
msgid "Duration…"
msgstr "Durată…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
#, fuzzy
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Durata, de ex. >6:00 <12:00 !6:54"

#: pynicotine/gtkgui/ui/search.ui:73
#, fuzzy
msgid "Country code…"
msgstr "Codul tarii…"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
#, fuzzy
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Codul țării, de ex. US ES sau !DE !GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
#, fuzzy
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Interziceți utilizatorilor să acceseze fișierele dvs. partajate, pe baza "
"numelui de utilizator, a adresei IP sau a țării."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
#, fuzzy
msgid "Country codes to block (comma separated):"
msgstr "Codurile de țară de blocat (separate prin virgulă):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
#, fuzzy
msgid "Codes must be in ISO 3166-2 format."
msgstr "Codurile trebuie să fie în format ISO 3166-2."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
#, fuzzy
msgid "Use custom geo block message:"
msgstr "Utilizați mesajul personalizat de blocare geografică:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
#, fuzzy
msgid "Use custom ban message:"
msgstr "Utilizați mesajul de interdicție personalizat:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
#, fuzzy
msgid "IP Addresses"
msgstr "Adrese IP"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
#, fuzzy
msgid "Restore previously open private chats on startup"
msgstr "Restabiliți conversațiile private deschise anterior la pornire"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
#, fuzzy
msgid "Enable spell checker"
msgstr "Activați corectorul ortografic (necesită o repornire)"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
#, fuzzy
msgid "Enable CTCP-like private message responses (client version)"
msgstr ""
"Activați răspunsurile la mesaje private asemănătoare CTCP (versiunea client)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
#, fuzzy
msgid "Number of recent private chat messages to show:"
msgstr "Numărul de mesaje recente de chat privat de afișat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
#, fuzzy
msgid "Number of recent chat room messages to show:"
msgstr "Numărul de mesaje recente din camera de chat de afișat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
#, fuzzy
msgid "Chat Completion"
msgstr "Finalizare chat"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
#, fuzzy
msgid "Enable tab-key completion"
msgstr "Activați completarea tastei tab"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
#, fuzzy
msgid "Enable completion drop-down list"
msgstr "Activați lista derulantă de finalizare"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
#, fuzzy
msgid "Minimum characters required to display drop-down:"
msgstr "Caracterele minime necesare pentru afișarea meniului drop-down:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
#, fuzzy
msgid "Allowed chat completions:"
msgstr "Finalizări permise prin chat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
#, fuzzy
msgid "Buddy names"
msgstr "Nume de prieteni"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
#, fuzzy
msgid "Chat room usernames"
msgstr "Nume de utilizator pentru camerele de chat"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
#, fuzzy
msgid "Room names"
msgstr "Numele camerelor"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
#, fuzzy
msgid "Commands"
msgstr "Comenzi"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
#, fuzzy
msgid "Timestamps"
msgstr "Marcaje temporale"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
#, fuzzy
msgid "Private chat format:"
msgstr "Format de chat privat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
#, fuzzy
msgid "Default"
msgstr "Mod implicit"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
#, fuzzy
msgid "Chat room format:"
msgstr "Format camera de chat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
#, fuzzy
msgid "Text-to-Speech"
msgstr "Text-to-Speech"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
#, fuzzy
msgid "Enable Text-to-Speech"
msgstr "Activați text-to-speech"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
#, fuzzy
msgid "Text-to-Speech command:"
msgstr "Comanda text-to-speech:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
#, fuzzy
msgid "Private chat message:"
msgstr "Mesaj de chat privat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
#, fuzzy
msgid "Chat room message:"
msgstr "Mesaj din camera de chat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
#, fuzzy
msgid "Censor"
msgstr "Cenzura"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
#, fuzzy
msgid "Enable censoring of text patterns"
msgstr "Activați cenzurarea modelelor de text"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
#, fuzzy
msgid "Auto-Replace"
msgstr "Înlocuire automată"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
#, fuzzy
msgid "Enable automatic replacement of words"
msgstr "Activați înlocuirea automată a cuvintelor"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
#, fuzzy
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr "Șterge automat descărcările finalizate/filtrate din lista de transfer"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
#, fuzzy
msgid "Store completed downloads in username subfolders"
msgstr "Stocați descărcările finalizate în subdosarele nume de utilizator"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
#, fuzzy
msgid "Double-click action for downloads:"
msgstr "Faceți dublu clic pe acțiune pentru descărcări:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
#, fuzzy
msgid "Allow users to send you any files:"
msgstr "Permiteți utilizatorilor să vă trimită orice fișiere:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
#, fuzzy
msgid "Folders"
msgstr "Foldere"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
#, fuzzy
msgid "Finished downloads:"
msgstr "Descărcări finalizate:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
#, fuzzy
msgid "Incomplete downloads:"
msgstr "Descărcări incomplete:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
#, fuzzy
msgid "Received files:"
msgstr "Fișiere primite:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
#, fuzzy
msgid "Events"
msgstr "Evenimente"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
#, fuzzy
msgid "Run command after file download finishes ($ for file path):"
msgstr ""
"Rulați comanda după ce descărcarea fișierului se termină ($ pentru calea "
"fișierului):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
#, fuzzy
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"Rulați comanda după ce descărcarea folderului se termină ($ pentru calea "
"folderului):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
#, fuzzy
msgid "Download Filters"
msgstr "Descărcați filtre"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
#, fuzzy
msgid "Enable download filters"
msgstr "Activați filtrele de descărcare"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
#, fuzzy
msgid "Add"
msgstr "Adăuga"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
#, fuzzy
msgid "Load Defaults"
msgstr "Încarcă setările implicite"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
#, fuzzy
msgid "Verify Filters"
msgstr "Verificați filtrele"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
#, fuzzy
msgid "Unverified"
msgstr "Neverificat"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
#, fuzzy
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Ignorați mesajele de chat și rezultatele căutării de la utilizatori, pe baza "
"numelui de utilizator sau a adresei IP."

#: pynicotine/gtkgui/ui/settings/log.ui:94
#, fuzzy
msgid "Log chatrooms by default"
msgstr "Înregistrați camerele de chat în mod implicit"

#: pynicotine/gtkgui/ui/settings/log.ui:118
#, fuzzy
msgid "Log private chat by default"
msgstr "Înregistrează chatul privat în mod implicit"

#: pynicotine/gtkgui/ui/settings/log.ui:142
#, fuzzy
msgid "Log transfers to file"
msgstr "Transferurile de jurnal în fișier"

#: pynicotine/gtkgui/ui/settings/log.ui:166
#, fuzzy
msgid "Log debug messages to file"
msgstr "Înregistrați mesajele de depanare în fișier"

#: pynicotine/gtkgui/ui/settings/log.ui:190
#, fuzzy
msgid "Log timestamp format:"
msgstr "Format de marcaj de timp al jurnalului:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
#, fuzzy
msgid "Folder Locations"
msgstr "Locațiile folderelor"

#: pynicotine/gtkgui/ui/settings/log.ui:245
#, fuzzy
msgid "Chatroom logs folder:"
msgstr "Dosarul jurnalelor din sala de chat:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
#, fuzzy
msgid "Private chat logs folder:"
msgstr "Dosarul jurnalelor de chat privat:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
#, fuzzy
msgid "Transfer logs folder:"
msgstr "Dosarul de jurnal de transfer:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
#, fuzzy
msgid "Debug logs folder:"
msgstr "Dosarul jurnalelor de depanare:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
#, fuzzy
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Conectați-vă la un cont Soulseek existent sau creați unul nou. Numele de "
"utilizator sunt sensibile la majuscule și sunt unice."

#: pynicotine/gtkgui/ui/settings/network.ui:118
#, fuzzy
msgid "Public IP address:"
msgstr "Adresă IP publică:"

#: pynicotine/gtkgui/ui/settings/network.ui:159
#, fuzzy
msgid "Listening port:"
msgstr "Ascultare pe portul: %i"

#: pynicotine/gtkgui/ui/settings/network.ui:185
#, fuzzy
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Port de ascultare redirecționat automat (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
#, fuzzy
msgid "Away Status"
msgstr "Status Away"

#: pynicotine/gtkgui/ui/settings/network.ui:228
#, fuzzy
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Minute de inactivitate înainte de a pleca (0 pentru a dezactiva):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
#, fuzzy
msgid "Auto-reply message when away:"
msgstr "Mesaj de răspuns automat când sunteți plecat:"

#: pynicotine/gtkgui/ui/settings/network.ui:299
#, fuzzy
msgid "Auto-connect to server on startup"
msgstr "Conectare automată la server la pornire"

#: pynicotine/gtkgui/ui/settings/network.ui:323
#, fuzzy
msgid "Soulseek server:"
msgstr "Serverul Soulseek:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
#, fuzzy
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Leagă conexiunile la o interfață de rețea specifică, utilă de ex. asigurându-"
"vă că un VPN este utilizat în orice moment. Lăsați gol pentru a utiliza "
"orice interfață disponibilă. Schimbați această valoare doar dacă știți ce "
"faceți."

#: pynicotine/gtkgui/ui/settings/network.ui:351
#, fuzzy
msgid "Network interface:"
msgstr "Filtre de rețea"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
#, fuzzy
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"Now Playing vă permite să afișați ce redă playerul dvs. media utilizând "
"comanda /now din chat."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
#, fuzzy
msgid "Other"
msgstr "Alte"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
#, fuzzy
msgid "Now Playing Format"
msgstr "Format de redare acum"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
#, fuzzy
msgid "Now Playing message format:"
msgstr "Acum se redă formatul mesajului:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
#, fuzzy
msgid "Test Configuration"
msgstr "Configurare de testare"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
#, fuzzy
msgid "Enable plugins"
msgstr "Activați pluginurile"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
#, fuzzy
msgid "Add Plugins"
msgstr "Adăugați pluginuri"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
#, fuzzy
msgid "_Add Plugins"
msgstr "_Adăugați pluginuri"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
#, fuzzy
msgid "Settings"
msgstr "Setări"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
#, fuzzy
msgid "_Settings"
msgstr "_Setări"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
#, fuzzy
msgid "Version:"
msgstr "Versiune:"

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
#, fuzzy
msgid "Created by:"
msgstr "Creat de:"

#: pynicotine/gtkgui/ui/settings/search.ui:51
#, fuzzy
msgid "Enable search history"
msgstr "Activați istoricul căutărilor"

#: pynicotine/gtkgui/ui/settings/search.ui:70
#, fuzzy
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Alți clienți Soulseek pot avea opțiunea de a partaja fișiere în mod privat. "
"Dacă da, aceste fișiere vor fi prefixate cu „[PRIVATE]” și nu pot fi "
"descărcate până când persoana care a încărcat nu dă permisiunea explicită. "
"Întrebați-i cu amabilitate."

#: pynicotine/gtkgui/ui/settings/search.ui:76
#, fuzzy
msgid "Show privately shared files in search results"
msgstr "Afișați fișierele partajate în mod privat în rezultatele căutării"

#: pynicotine/gtkgui/ui/settings/search.ui:106
#, fuzzy
msgid "Limit number of results per search:"
msgstr "Limitați numărul de rezultate pe căutare:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
#, fuzzy
msgid "Result Filter Help"
msgstr "Ajutor pentru filtrul de rezultate"

#: pynicotine/gtkgui/ui/settings/search.ui:177
#, fuzzy
msgid "Enable search result filters by default"
msgstr "Activați filtrele pentru rezultatele căutării în mod prestabilit"

#: pynicotine/gtkgui/ui/settings/search.ui:211
#, fuzzy
msgid "Include:"
msgstr "Include:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
#, fuzzy
msgid "Exclude:"
msgstr "Exclude:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
#, fuzzy
msgid "File Type:"
msgstr "Tip fișier:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
#, fuzzy
msgid "Size:"
msgstr "Mărimea:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
#, fuzzy
msgid "Bitrate:"
msgstr "Rata de biți:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
#, fuzzy
msgid "Duration:"
msgstr "Durată:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
#, fuzzy
msgid "Country Code:"
msgstr "Codul tarii:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
#, fuzzy
msgid "Only show results from users with an available upload slot."
msgstr ""
"Afișați numai rezultatele de la utilizatorii cu un spațiu de încărcare "
"disponibil."

#: pynicotine/gtkgui/ui/settings/search.ui:430
#, fuzzy
msgid "Network Searches"
msgstr "Căutări în rețea"

#: pynicotine/gtkgui/ui/settings/search.ui:452
#, fuzzy
msgid "Respond to search requests from other users"
msgstr "Răspunde la solicitările de căutare de la alți utilizatori"

#: pynicotine/gtkgui/ui/settings/search.ui:486
#, fuzzy
msgid "Searches shorter than this number of characters will be ignored:"
msgstr "Căutările mai scurte decât acest număr de caractere vor fi ignorate:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
#, fuzzy
msgid "Maximum search results to send per search request:"
msgstr ""
"Numărul maxim de rezultate ale căutării de trimis pentru fiecare cerere de "
"căutare:"

#: pynicotine/gtkgui/ui/settings/search.ui:578
#, fuzzy
msgid "Clear Search History"
msgstr "Sterge istoricul cautarii"

#: pynicotine/gtkgui/ui/settings/search.ui:626
#, fuzzy
msgid "Clear Filter History"
msgstr "Ștergeți istoricul filtrelor"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
#, fuzzy
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Rescanează automat conținutul folderelor partajate la pornire. Dacă este "
"dezactivată, partajările dvs. sunt actualizate numai atunci când inițiați "
"manual o rescanare."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
#, fuzzy
msgid "Rescan shares on startup"
msgstr "Rescanați acțiunile la pornire"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr "Vizibil tuturor:"

#: pynicotine/gtkgui/ui/settings/shares.ui:82
#, fuzzy
msgid "Buddy shares"
msgstr "Nume de prieteni"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
#, fuzzy
msgid "Trusted shares"
msgstr "Listează cotele"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
#, fuzzy
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr ""
"Ștergeți automat încărcările terminate/anulate din lista de transferuri"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
#, fuzzy
msgid "Double-click action for uploads:"
msgstr "Faceți dublu clic pe acțiune pentru încărcări:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
#, fuzzy
msgid "Limit upload speed:"
msgstr "Limitați viteza de încărcare:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
#, fuzzy
msgid "Per transfer"
msgstr "Per transfer"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
#, fuzzy
msgid "Total transfers"
msgstr "Transferuri totale"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
#, fuzzy
msgid "Upload Slots"
msgstr "Încărcați sloturi"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
#, fuzzy
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Round Robin: Fișierele vor fi încărcate în mod ciclic pentru utilizatorii "
"care așteaptă la coadă.\n"
"First In, First Out: Fișierele vor fi încărcate în ordinea în care au fost "
"puse în coadă."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
#, fuzzy
msgid "Upload queue type:"
msgstr "Tip de coadă de încărcare:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
#, fuzzy
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr "Încărcări în coadă dacă viteza totală de transfer atinge (KiB/s):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
#, fuzzy
msgid "Fixed number of upload slots:"
msgstr "Limitați numărul de spații de încărcare la:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
#, fuzzy
msgid "Prioritize all buddies"
msgstr "Prioritizează toți prietenii"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
#, fuzzy
msgid "Queue Limits"
msgstr "Limite de coadă"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
#, fuzzy
msgid "Maximum number of queued files per user:"
msgstr "Limitați numărul de rezultate pe căutare:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr "Mărimea totală maximă a fișierelor alocate per utilizator (MiB):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
#, fuzzy
msgid "Limits do not apply to buddies"
msgstr "Limitele nu se aplică prietenilor"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
#, fuzzy
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"Instanțele de $ sunt înlocuite cu adresa URL. Aplicațiile de sistem "
"implicite sunt utilizate în cazurile în care nu a fost configurat un "
"protocol."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
#, fuzzy
msgid "File manager command:"
msgstr "Comanda managerului de fișiere:"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
#, fuzzy
msgid "Reset Picture"
msgstr "Resetează imaginea"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
#, fuzzy
msgid "Self Description"
msgstr "Despre sine"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""
"Adaugă lucruri pe care vrei să le vadă toată lumea, precum o descriere "
"scurtă, sfaturi ajutătoare, sau reguli de respectat pentru a descărca "
"partajările tale."

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
#, fuzzy
msgid "Picture:"
msgstr "Imagine:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
#, fuzzy
msgid "Prefer dark mode"
msgstr "Preferați modul întunecat"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
#, fuzzy
msgid "Use header bar"
msgstr "Utilizați _Header Bar"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
#, fuzzy
msgid "Display tray icon"
msgstr "Afișează pictograma tavă"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
#, fuzzy
msgid "Minimize to tray on startup"
msgstr "Minimizați în tavă la pornire"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
#, fuzzy
msgid "Language (requires a restart):"
msgstr "Limba (necesită o repornire):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
#, fuzzy
msgid "When closing window:"
msgstr "Când închideți Nicotine+:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
#, fuzzy
msgid "Notifications"
msgstr "Notificări"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
#, fuzzy
msgid "Enable sound for notifications"
msgstr "Activați sunetul pentru notificări"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
#, fuzzy
msgid "Show notification for private chats and mentions in the window title"
msgstr ""
"Afișați notificarea pentru chaturile private și mențiunile în titlul "
"ferestrei"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
#, fuzzy
msgid "Show notifications for:"
msgstr "Afișați notificări pentru:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
#, fuzzy
msgid "Finished file downloads"
msgstr "Descărcări de fișiere finalizate"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
#, fuzzy
msgid "Finished folder downloads"
msgstr "Descărcări de foldere finalizate"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
#, fuzzy
msgid "Private messages"
msgstr "Mesaje private"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
#, fuzzy
msgid "Chat room messages"
msgstr "Mesaje din camera de chat"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
#, fuzzy
msgid "Chat room mentions"
msgstr "Mențiuni din camera de chat"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
#, fuzzy
msgid "Wishlist results found"
msgstr "S-au găsit rezultatele listei de dorințe"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
#, fuzzy
msgid "Restore the previously active main tab at startup"
msgstr "Restaurați fila principală activă anterior la pornire"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
#, fuzzy
msgid "Close-buttons on secondary tabs"
msgstr "Butoane de închidere pe filele secundare"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
#, fuzzy
msgid "Regular tab label color:"
msgstr "Culoare obișnuită a etichetei filei:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
#, fuzzy
msgid "Changed tab label color:"
msgstr "S-a schimbat culoarea etichetei filei:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
#, fuzzy
msgid "Highlighted tab label color:"
msgstr "Culoarea etichetei filei evidențiate:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr "Poziție în lista de prieteni:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
#, fuzzy
msgid "Visible main tabs:"
msgstr "File principale vizibile:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
#, fuzzy
msgid "Tab bar positions:"
msgstr "Pozițiile barei de file:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
#, fuzzy
msgid "Main tabs"
msgstr "Filele principale"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
#, fuzzy
msgid "Show reverse file paths (requires a restart)"
msgstr "Afișați căile inverse ale fișierelor (necesită o repornire)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
#, fuzzy
msgid "Show exact file sizes (requires a restart)"
msgstr "Afișați dimensiunile exacte ale fișierelor (necesită o repornire)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
#, fuzzy
msgid "List text color:"
msgstr "Culoarea textului listei:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
#, fuzzy
msgid "Enable colored usernames"
msgstr "Activați numele de utilizator colorate"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
#, fuzzy
msgid "Chat username appearance:"
msgstr "Apariția numelui de utilizator la chat:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
#, fuzzy
msgid "Remote text color:"
msgstr "Culoarea textului de la distanță:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
#, fuzzy
msgid "Local text color:"
msgstr "Culoarea textului local:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
#, fuzzy
msgid "Command output text color:"
msgstr "Culoarea textului de ieșire a comenzii:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
#, fuzzy
msgid "/me action text color:"
msgstr "culoarea textului acțiunii /me:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
#, fuzzy
msgid "Highlighted text color:"
msgstr "Culoarea textului evidențiat:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
#, fuzzy
msgid "URL link text color:"
msgstr "Culoarea textului linkului URL:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
#, fuzzy
msgid "User Statuses"
msgstr "Status de utilizator"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
#, fuzzy
msgid "Online color:"
msgstr "Culoare online:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
#, fuzzy
msgid "Away color:"
msgstr "Culoare deplasare:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
#, fuzzy
msgid "Offline color:"
msgstr "Culoare offline:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
#, fuzzy
msgid "Text Entries"
msgstr "Intrări de text"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
#, fuzzy
msgid "Text entry background color:"
msgstr "Culoare de fundal pentru introducerea textului:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
#, fuzzy
msgid "Text entry text color:"
msgstr "Culoarea textului pentru introducerea textului:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
#, fuzzy
msgid "Fonts"
msgstr "Fonturi"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
#, fuzzy
msgid "Global font:"
msgstr "Font global:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
#, fuzzy
msgid "List font:"
msgstr "Fontul listei:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
#, fuzzy
msgid "Text view font:"
msgstr "Font vizualizare text:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
#, fuzzy
msgid "Chat font:"
msgstr "Font pentru chat:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
#, fuzzy
msgid "Transfers font:"
msgstr "Font transfer:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
#, fuzzy
msgid "Search font:"
msgstr "Font de căutare:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
#, fuzzy
msgid "Browse font:"
msgstr "Răsfoiți fontul:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
#, fuzzy
msgid "Icons"
msgstr "Pictograme"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
#, fuzzy
msgid "Icon theme folder:"
msgstr "Dosarul cu tema pictogramelor:"

#: pynicotine/gtkgui/ui/uploads.ui:86
#, fuzzy
msgid "Abort User(s)"
msgstr "Anulați utilizatorul(i)"

#: pynicotine/gtkgui/ui/uploads.ui:116
#, fuzzy
msgid "Ban User(s)"
msgstr "Interziceți utilizatorii"

#: pynicotine/gtkgui/ui/uploads.ui:138
#, fuzzy
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Ștergeți toate încărcările finalizate/anulate"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
#, fuzzy
msgid "Message All"
msgstr "Trimite mesajul tuturor"

#: pynicotine/gtkgui/ui/uploads.ui:199
#, fuzzy
msgid "Clear Specific Uploads"
msgstr "Ștergeți încărcări specifice"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
#, fuzzy
msgid "Save Shares List to Disk"
msgstr "Salvați lista de acțiuni pe disc"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
#, fuzzy
msgid "Refresh Files"
msgstr "Actualizează fișierele"

#: pynicotine/gtkgui/ui/userinfo.ui:101
#, fuzzy
msgid "Edit Profile"
msgstr "Editează profilul"

#: pynicotine/gtkgui/ui/userinfo.ui:149
#, fuzzy
msgid "Shared Files"
msgstr "Fișiere partajate"

#: pynicotine/gtkgui/ui/userinfo.ui:203
#, fuzzy
msgid "Upload Speed"
msgstr "Viteza de upload"

#: pynicotine/gtkgui/ui/userinfo.ui:230
#, fuzzy
msgid "Free Upload Slots"
msgstr "Sloturi de încărcare gratuite"

#: pynicotine/gtkgui/ui/userinfo.ui:284
#, fuzzy
msgid "Queued Uploads"
msgstr "Încărcări în coadă"

#: pynicotine/gtkgui/ui/userinfo.ui:354
#, fuzzy
msgid "Edit Interests"
msgstr "Editați interesele"

#: pynicotine/gtkgui/ui/userinfo.ui:624
#, fuzzy
msgid "_Gift Privileges…"
msgstr "_Privilegii cadou…"

#: pynicotine/gtkgui/ui/userinfo.ui:663
#, fuzzy
msgid "_Refresh Profile"
msgstr "_Actualizați profilul"

#: pynicotine/plugins/core_commands/PLUGININFO:3
#, fuzzy
msgid "Nicotine+ Commands"
msgstr "Comenzi Nicotine+"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Echipa Nicotine+"

#, fuzzy
#~ msgid "Listening port (requires a restart):"
#~ msgstr "Limba (necesită o repornire):"

#, fuzzy
#~ msgid "Network interface (requires a restart):"
#~ msgstr "Limba (necesită o repornire):"

#~ msgid "Invalid Password"
#~ msgstr "Parolă greșită"

#~ msgid "Change _Login Details"
#~ msgstr "Schimbați _Detaliile de logare"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i utilizatori privilegiați"

#, fuzzy
#~ msgid "_Set Up…"
#~ msgstr "_Înființat…"

#, fuzzy
#~ msgid "Queued search result text color:"
#~ msgstr "Culoarea textului rezultatului căutării în coadă:"

#~ msgid "Out of Date"
#~ msgstr "Învechit"

#, python-format
#~ msgid "Version %(version)s is available, released on %(date)s"
#~ msgstr "Versiunea %(version)s este valabilă, publicată pe %(date)s"

#, python-format
#~ msgid "You are using a development version of %s"
#~ msgstr "Folosiți versiunea de dezvoltare a %s"

#, python-format
#~ msgid "You are using the latest version of %s"
#~ msgstr "Folosiți cea mai recentă versiunea a %s"

#~ msgid "Latest Version Unknown"
#~ msgstr "Cea mai recentă versiunea este necunoscută"

#, python-format
#~ msgid "Quit %(program)s %(version)s, %(status)s!"
#~ msgstr "Ieșire %(program)s %(version)s, %(status)s!"

#~ msgid "terminated"
#~ msgstr "terminat"

#~ msgid "done"
#~ msgstr "gata"

#~ msgid "Remember choice"
#~ msgstr "Reține alegerea"

#~ msgid "--- disconnected ---"
#~ msgstr "--- deconectat ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- reconectat ---"
