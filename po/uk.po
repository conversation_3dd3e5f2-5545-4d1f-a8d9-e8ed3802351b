# SPDX-FileCopyrightText: 2022-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2025-01-10 09:00+0000\n"
"Last-Translator: Олег Грицун <<EMAIL>>\n"
"Language-Team: Ukrainian <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/uk/>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Generator: Weblate 5.10-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr "Клієнт Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Графічний клієнт для однорангової мережі Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr "Soulseek;Nicotine;обмін;чат;повідомлення;P2P;одноранговий;GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
#, fuzzy
msgid "Browse the Soulseek network"
msgstr "Графічний клієнт для мережі Soulseek"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+ - це графічний клієнт для однорангової мережі Soulseek."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ прагне бути легковажною, приємною, безкоштовною та відкритою "
"(FOSS) альтернативою офіційному клієнту Soulseek, одночасно надаючи повний "
"набір функцій."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
msgid "Search Files"
msgstr "Пошук файлів"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Завантаження"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
msgid "Browse Shares"
msgstr "Переглянути спільні каталоги"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
msgid "Private Chat"
msgstr "Приватний чат"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Команда Nicotine+"

#: pynicotine/__init__.py:50
#, python-format
msgid "Website: %s"
msgstr "Вебсайт: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "показати це повідомлення та вийти"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "файл"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr "використовувати користувацький файл конфігурації"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "каталог"

#: pynicotine/__init__.py:64
msgid "alternative directory for user data and plugins"
msgstr "альтернативна директорія для даних користувача та плагінів"

#: pynicotine/__init__.py:68
msgid "start the program without showing window"
msgstr "запускати програму без відображення вікна"

#: pynicotine/__init__.py:71
msgid "ip"
msgstr "ip-адреса"

#: pynicotine/__init__.py:72
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "прив'язувати сокети до заданої IP-адреси (корисно для VPN)"

#: pynicotine/__init__.py:75
msgid "port"
msgstr "порт"

#: pynicotine/__init__.py:76
msgid "listen on the given port"
msgstr "слухати на заданому порту"

#: pynicotine/__init__.py:80
msgid "rescan shared files"
msgstr "повторно сканувати спільні файли"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr "запустити програму в фоновому режимі (без GUI)"

#: pynicotine/__init__.py:88
msgid "display version and exit"
msgstr "відобразити версію та вийти"

#: pynicotine/__init__.py:121
#, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Ви використовуєте непідтримувану версію Python (%(old_version)s).\n"
"Вам слід встановити Python %(min_version)s або новішу."

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Не вдалося сканувати спільні ресурси. Закрийте інші екземпляри Nicotine+ і "
"повторіть спробу."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, fuzzy, python-format
msgid "%(user)s is away"
msgstr "Користувач %s відсутній"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, fuzzy, python-format
msgid "%(user)s is online"
msgstr "Користувач %s в мережі"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, fuzzy, python-format
msgid "%(user)s is offline"
msgstr "Користувач %s поза мережею"

#: pynicotine/buddies.py:316
msgid "Buddy Status"
msgstr "Статус друга"

#: pynicotine/chatrooms.py:383
#, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Вас додали до приватної кімнати: %(room)s"

#: pynicotine/chatrooms.py:478
#, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr ""
"Повідомлення чату від користувача \"%(user)s\" у кімнаті \"%(room)s\": "
"%(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr ""
"Не вдається створити каталог \"%(path)s\", повідомляється про помилку: "
"%(error)s"

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr "Помилка резервного копіювання конфігурації: %s"

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr "Резервна копія конфігурації: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Завантаження %(program)s %(version)s"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Завершення %(program)s %(version)s, %(status)s…"

#: pynicotine/core.py:246
msgid "terminating"
msgstr "припинення"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "закриття програми"

#: pynicotine/core.py:259
#, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Вийти %(program)s %(version)s!"

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr "Вам потрібно вказати ім’я користувача та пароль перед підключенням…"

#: pynicotine/downloads.py:239
#, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""
"Помилка: не вдалося завантажити фільтр! Перевірте свої фільтри. Причина: %s"

#: pynicotine/downloads.py:254
#, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Помилка: %(num)d Не вдалося завантажити фільтри! %(error)s "

#: pynicotine/downloads.py:366
#, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s завантажено від %(user)s"

#: pynicotine/downloads.py:370
msgid "File Downloaded"
msgstr "Файл завантажено"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Виконано: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "Не вдалося виконати \"%(command)s\": %(error)s"

#: pynicotine/downloads.py:407
#, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s завантажено від %(user)s"

#: pynicotine/downloads.py:411
msgid "Folder Downloaded"
msgstr "Каталог завантажено"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Виконано в каталозі: %s"

#: pynicotine/downloads.py:443
#, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "Не вдалося перемістити \"%(tempfile)s\" до \"%(file)s\": %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
#, fuzzy
msgid "Download Folder Error"
msgstr "Помилка завантаження каталогу"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Завантаження завершено: користувач %(user)s, файл %(file)s"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Завантаження перервано, користувач %(user)s файл %(file)s"

#: pynicotine/downloads.py:1150
#, python-format
msgid "Download I/O error: %s"
msgstr "Помилка завантаження введення-виводу: %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr ""
"Не вдається отримати ексклюзивне блокування файлу – помилка вводу-виводу: %s"

#: pynicotine/downloads.py:1202
#, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Не вдається зберегти файл в %(folder_path)s: %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Завантаження розпочато: користувач %(user)s, файл %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, python-format
msgid "Cannot find %s, please install it."
msgstr "Не вдається знайти %s, будь ласка, встановіть його."

#: pynicotine/gtkgui/__init__.py:162
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr ""
"Немає доступного графічного середовища, використовується безголовий режим "
"(без GUI)"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "_Підключитися"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "_Відключити"

#: pynicotine/gtkgui/application.py:308
msgid "Soulseek _Privileges"
msgstr "_Привілеї Soulseek"

#: pynicotine/gtkgui/application.py:314
msgid "_Preferences"
msgstr "_Налаштування"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
msgid "_Quit"
msgstr "_Вихід"

#: pynicotine/gtkgui/application.py:337
#, fuzzy
msgid "Browse _Public Shares"
msgstr "_Перегляд спільних каталогів"

#: pynicotine/gtkgui/application.py:338
#, fuzzy
msgid "Browse _Buddy Shares"
msgstr "_Перегляд спільних каталогів друзів"

#: pynicotine/gtkgui/application.py:339
#, fuzzy
msgid "Browse _Trusted Shares"
msgstr "_Перегляд спільних каталогів друзів"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
msgid "_Rescan Shares"
msgstr "_Пересканувати спільні каталоги"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
#, fuzzy
msgid "Configure _Shares"
msgstr "_Налаштувати спільні каталоги"

#: pynicotine/gtkgui/application.py:371
msgid "_Keyboard Shortcuts"
msgstr "_Гарячі клавіши"

#: pynicotine/gtkgui/application.py:372
msgid "_Setup Assistant"
msgstr "_Помічник з налаштування"

#: pynicotine/gtkgui/application.py:373
msgid "_Transfer Statistics"
msgstr "_Статистика передач"

#: pynicotine/gtkgui/application.py:378
msgid "Report a _Bug"
msgstr "Повідомити про помилку"

#: pynicotine/gtkgui/application.py:379
msgid "Improve T_ranslations"
msgstr "Покращити Пе_реклад"

#: pynicotine/gtkgui/application.py:383
msgid "_About Nicotine+"
msgstr "_Про Nicotine+"

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "_Файл"

#: pynicotine/gtkgui/application.py:395
msgid "_Shares"
msgstr "_Спільні каталоги"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
msgid "_Help"
msgstr "_Допомога"

#: pynicotine/gtkgui/application.py:410
#, fuzzy
msgid "_Browse Shares"
msgstr "Переглянути спільні каталоги"

#: pynicotine/gtkgui/application.py:465
#, python-format
msgid "Unable to show notification: %s"
msgstr "Не вдається показати сповіщення: %s"

#: pynicotine/gtkgui/application.py:526
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Ви все ще завантажуєте файли. Ви дійсно хочете вийти?"

#: pynicotine/gtkgui/application.py:527
#, fuzzy
msgid "Wait for uploads to finish"
msgstr "Чекати завершення відвантажень"

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr "Ви дійсно хочете вийти?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
msgid "_No"
msgstr "_Ні"

#: pynicotine/gtkgui/application.py:535
msgid "_Run in Background"
msgstr "_Запуск у фоновому режимі"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr "Вийти з Nicotine+"

#: pynicotine/gtkgui/application.py:561
msgid "Shares Not Available"
msgstr "Спільні каталоги недоступні"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
#, fuzzy
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Переконайтеся, що зовнішні диски підключено та дозволи для папок правильні."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
msgid "_Cancel"
msgstr "_Скасувати"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "_Повторити спробу"

#: pynicotine/gtkgui/application.py:567
#, fuzzy
msgid "_Force Rescan"
msgstr "_Примусове повторне сканування"

#: pynicotine/gtkgui/application.py:742
msgid "Message Downloading Users"
msgstr "Повідомити користувачів які завантажують"

#: pynicotine/gtkgui/application.py:743
msgid "Send private message to all users who are downloading from you:"
msgstr ""
"Надіслати приватне повідомлення всім користувачам, які завантажують ваші "
"файли:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
msgid "_Send Message"
msgstr "_Відправити повідомлення"

#: pynicotine/gtkgui/application.py:756
msgid "Message Buddies"
msgstr "Написати друзям"

#: pynicotine/gtkgui/application.py:757
#, fuzzy
msgid "Send private message to all online buddies:"
msgstr "Надіслати приватне повідомлення всім друзям онлайн:"

#: pynicotine/gtkgui/application.py:786
msgid "Select a Saved Shares List File"
msgstr "Виберіть збережений файл списку спільних каталогів"

#: pynicotine/gtkgui/application.py:877
msgid "Critical Error"
msgstr "Критична помилка"

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ зіткнувся з критичною помилкою, тому його потрібно закрити. Будь "
"ласка, скопіюйте наступне повідомлення та додайте його до звіту про помилку:"

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr "_Закрити Nicotine+"

#: pynicotine/gtkgui/application.py:883
msgid "_Copy & Report Bug"
msgstr "_Копіювати та повідомити про помилку"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Статус"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr "Країна"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Користувач"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Швидкість"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Файли"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted"
msgstr "Довірений"

#: pynicotine/gtkgui/buddies.py:108
msgid "Notify"
msgstr "Повідомити"

#: pynicotine/gtkgui/buddies.py:114
msgid "Prioritized"
msgstr "Пріоритетні"

#: pynicotine/gtkgui/buddies.py:120
msgid "Last Seen"
msgstr "Востаннє у мережі"

#: pynicotine/gtkgui/buddies.py:126
msgid "Note"
msgstr "Примітка"

#: pynicotine/gtkgui/buddies.py:143
msgid "Add User _Note…"
msgstr "Додати _примітку користувача…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Видалити"

#: pynicotine/gtkgui/buddies.py:364
msgid "Never seen"
msgstr "Ніколи"

#: pynicotine/gtkgui/buddies.py:529
msgid "Add User Note"
msgstr "Додати примітку користувача"

#: pynicotine/gtkgui/buddies.py:530
#, python-format
msgid "Add a note about user %s:"
msgstr "Додати примітку про користувача %s:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
#, fuzzy
msgid "_Add"
msgstr "_Додати"

#: pynicotine/gtkgui/chatrooms.py:224
msgid "Create New Room?"
msgstr "Створити нову кімнату?"

#: pynicotine/gtkgui/chatrooms.py:225
#, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Ви дійсно хочете створити нову кімнату \"%s\"?"

#: pynicotine/gtkgui/chatrooms.py:226
msgid "Make room private"
msgstr "Зробити кімнату приватною"

#: pynicotine/gtkgui/chatrooms.py:515
#, fuzzy
msgid "Search activity log…"
msgstr "Журнал пошуку…"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
#, fuzzy
msgid "Search chat log…"
msgstr "Пошуковий термін…"

#: pynicotine/gtkgui/chatrooms.py:597
msgid "Sear_ch User's Files"
msgstr "По_шук у файлах користувача"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
msgid "Find…"
msgstr "Шукати…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Копіювати"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr "Копіювати все"

#: pynicotine/gtkgui/chatrooms.py:608
msgid "Clear Activity View"
msgstr "Очистити перегляд активності"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
msgid "_Leave Room"
msgstr "_Покинути кімнату"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
msgid "Copy Link"
msgstr "Копіювати посилання"

#: pynicotine/gtkgui/chatrooms.py:624
#, fuzzy
msgid "View Room Log"
msgstr "Переглянути журнал кімнати"

#: pynicotine/gtkgui/chatrooms.py:627
#, fuzzy
msgid "Delete Room Log…"
msgstr "Видалити журнал кімнати…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
msgid "Clear Message View"
msgstr "Очистити перегляд повідомлень"

#: pynicotine/gtkgui/chatrooms.py:832
#, fuzzy, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s згадав вас у кімнаті %(room)s"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, fuzzy, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "Повідомлення від %(user)s у кімнаті %(room)s"

#: pynicotine/gtkgui/chatrooms.py:855
#, fuzzy, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "Повідомлення від %(user)s у кімнаті %(room)s"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s приєднався до кімнати"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s покинув кімнату"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s пішов"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s повернувся"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
msgid "Delete Logged Messages?"
msgstr "Видалити зареєстровані повідомлення?"

#: pynicotine/gtkgui/chatrooms.py:1197
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr "Ви дійсно хочете назавжди видалити всі повідомлення для цієї кімнати?"

#: pynicotine/gtkgui/dialogs/about.py:405
#, fuzzy
msgid "About"
msgstr "Про"

#: pynicotine/gtkgui/dialogs/about.py:415
msgid "Website"
msgstr "Web-сторінка"

#: pynicotine/gtkgui/dialogs/about.py:457
#, python-format
msgid "Error checking latest version: %s"
msgstr "Помилка перевірки останньої версії: %s"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr "Доступна нова версія: %s"

#: pynicotine/gtkgui/dialogs/about.py:467
#, fuzzy
msgid "Up to date"
msgstr "Актуально"

#: pynicotine/gtkgui/dialogs/about.py:496
msgid "Checking latest version…"
msgstr "Перевірити останню версію…"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
msgid "Setup Assistant"
msgstr "Помічник з налаштування"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
msgid "Virtual Folder"
msgstr "Віртуальний каталог"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
msgid "Folder"
msgstr "Каталог"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
msgid "_Previous"
msgstr "_Попередній"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
msgid "_Finish"
msgstr "_Готово"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
msgid "_Next"
msgstr "_Далі"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
msgid "Add a Shared Folder"
msgstr "Додати спільний каталог"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
msgid "Edit Shared Folder"
msgstr "Редагувати спільний каталог"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Введіть нове віртуальне ім'я для \"%(dir)s\":"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
msgid "_Edit"
msgstr "_Редагувати"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"Користувач %s вже існує, а введений пароль недійсний. Будь ласка, виберіть "
"інше ім’я користувача, якщо це ваш перший вхід."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
msgid "File Properties"
msgstr "Властивості файлу"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "Властивості файлу (%(num)i з %(total)i  /  %(size)s  /  %(length)s)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Властивості файлу (%(num)i з %(total)i / %(size)s)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
msgid "_Apply"
msgstr "_Застосувати"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
msgid "Add…"
msgstr "Додати…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
msgid "Edit…"
msgstr "Редагувати…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, python-format
msgid "%s Settings"
msgstr "%s Налаштування"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
msgid "Add Item"
msgstr "Додати елемент"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
msgid "Edit Item"
msgstr "Редагувати елемент"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "Невідомо"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
msgid "Check Port Status"
msgstr "Перевірити статус порту"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, fuzzy, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, порт %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
msgid "Password Change Rejected"
msgstr "Зміна пароля відхилена"

#: pynicotine/gtkgui/dialogs/preferences.py:218
msgid "Enter a new password for your Soulseek account:"
msgstr "Введіть новий пароль для свого облікового запису Soulseek:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"Наразі ви вийшли з мережі Soulseek. Якщо ви хочете змінити пароль існуючого "
"облікового запису Soulseek, вам потрібно увійти в цей обліковий запис."

#: pynicotine/gtkgui/dialogs/preferences.py:223
msgid "Enter password to use when logging in:"
msgstr "Введіть пароль для входу в систему:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
msgid "Change Password"
msgstr "Змінити пароль"

#: pynicotine/gtkgui/dialogs/preferences.py:230
msgid "_Change"
msgstr "_Змінити"

#: pynicotine/gtkgui/dialogs/preferences.py:274
msgid "No one"
msgstr "Ніхто"

#: pynicotine/gtkgui/dialogs/preferences.py:275
msgid "Everyone"
msgstr "Кожен"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Друзі"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
#, fuzzy
msgid "Trusted buddies"
msgstr "Довірені друзі"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
msgid "Nothing"
msgstr "Нічого"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
msgid "Open File"
msgstr "Відкрити файл"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
msgid "Open in File Manager"
msgstr "Відкрити в диспетчері файлів"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Пошук"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
msgid "Pause"
msgstr "Призупинити"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
msgid "Resume"
msgstr "Продовжити"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
msgid "Browse Folder"
msgstr "Перегляд каталогу"

#: pynicotine/gtkgui/dialogs/preferences.py:317
#, fuzzy
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Синтаксис</b>: регістр не враховується. Якщо ввімкнено, можна "
"використовувати регулярні вирази Python, інакше підтримуються лише збіги "
"символів підстановки *."

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr "Фільтр"

#: pynicotine/gtkgui/dialogs/preferences.py:334
#, fuzzy
msgid "Regex"
msgstr "Регулярний вираз"

#: pynicotine/gtkgui/dialogs/preferences.py:468
msgid "Add Download Filter"
msgstr "Додати фільтр завантаження"

#: pynicotine/gtkgui/dialogs/preferences.py:469
msgid "Enter a new download filter:"
msgstr "Введіть новий фільтр завантаження:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
#, fuzzy
msgid "Enable regular expressions"
msgstr "Увімкнути регулярні вирази"

#: pynicotine/gtkgui/dialogs/preferences.py:498
msgid "Edit Download Filter"
msgstr "Редагувати фільтр завантаження"

#: pynicotine/gtkgui/dialogs/preferences.py:499
msgid "Modify the following download filter:"
msgstr "Змініть такий фільтр завантаження:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d Помилка! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
msgid "Filters Successful"
msgstr "Фільтри успішно застосовані"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
#, fuzzy
msgid "Public"
msgstr "Публічний"

#: pynicotine/gtkgui/dialogs/preferences.py:626
#, fuzzy
msgid "Accessible To"
msgstr "Доступно для"

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "Перервати"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
msgid "Retry"
msgstr "Повторити спробу"

#: pynicotine/gtkgui/dialogs/preferences.py:828
msgid "Round Robin"
msgstr "Циклічний/круговий"

#: pynicotine/gtkgui/dialogs/preferences.py:829
msgid "First In, First Out"
msgstr "Першим зайшов, першим вийшов"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
msgid "Username"
msgstr "Ім'я користувача"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
msgid "IP Address"
msgstr "IP-адреса"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
msgid "Ignore User"
msgstr "Ігнорувати користувача"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
msgid "Enter the name of the user you want to ignore:"
msgstr "Введіть ім’я користувача, якого ви хочете проігнорувати:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
msgid "Ignore IP Address"
msgstr "Ігнорувати IP-адресу"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
msgid "Enter an IP address you want to ignore:"
msgstr "Введіть IP-адресу, яку ви хочете ігнорувати:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "* is a wildcard"
msgstr "* є символом підстановки"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
msgid "Ban User"
msgstr "Блокувати користувача"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
msgid "Enter the name of the user you want to ban:"
msgstr "Введіть ім'я користувача, якого хочете заблокувати:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
msgid "Ban IP Address"
msgstr "Забанити IP-адресу"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "Enter an IP address you want to ban:"
msgstr "Введіть IP-адресу, яку хочете заблокувати:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
#, fuzzy
msgid "Format codes"
msgstr "Формат кодів"

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
msgid "Pattern"
msgstr "Шаблон"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
msgid "Replacement"
msgstr "Заміна"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
msgid "Censor Pattern"
msgstr "Шаблон цензури"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Введіть шаблон, який потрібно піддати цензурі. Додайте пробіли навколо "
"шаблону, якщо ви не хочете збігати рядки всередині слів (може не вдатися на "
"початку і в кінці рядків)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
#, fuzzy
msgid "Edit Censored Pattern"
msgstr "Цензурові шаблони"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
#, fuzzy
msgid "Add Replacement"
msgstr "Додати заміну"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
#, fuzzy
msgid "Enter a text pattern and what to replace it with:"
msgstr "Введіть шаблон тексту та заміну відповідно:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
#, fuzzy
msgid "Edit Replacement"
msgstr "Редагувати заміну"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
#, fuzzy
msgid "System default"
msgstr "За замовчуванням"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
msgid "Show confirmation dialog"
msgstr "Показати діалогове вікно підтвердження"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
msgid "Run in the background"
msgstr "Запуск у фоновому режимі"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
msgid "bold"
msgstr "напівжирний"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
msgid "italic"
msgstr "курсив"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
msgid "underline"
msgstr "підкреслений"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
msgid "normal"
msgstr "звичайний"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
#, fuzzy
msgid "Separate Buddies tab"
msgstr "Довірені друзі"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
#, fuzzy
msgid "Sidebar in Chat Rooms tab"
msgstr "Список друзів у чатах"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
#, fuzzy
msgid "Always visible sidebar"
msgstr "Бічна панель завжди видима"

#: pynicotine/gtkgui/dialogs/preferences.py:1777
msgid "Top"
msgstr "Зверху"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
msgid "Bottom"
msgstr "Знизу"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
msgid "Left"
msgstr "Ліворуч"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
msgid "Right"
msgstr "Праворуч"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "В мережі"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Відсутній"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Поза мережею"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
#, fuzzy
msgid "Tab Changed"
msgstr "Вкладку змінено"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
#, fuzzy
msgid "Tab Highlight"
msgstr "Вкладку підсвічено"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
msgid "Window"
msgstr "Вікно"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
#, fuzzy
msgid "Online (Tray)"
msgstr "Підключено (лоток)"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
msgid "Away (Tray)"
msgstr "Відсутній (лоток)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
#, fuzzy
msgid "Offline (Tray)"
msgstr "Поза мережею"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
msgid "Message (Tray)"
msgstr "Повідомлення (лоток)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Протокол"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
msgid "Command"
msgstr "Команда"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
#, fuzzy
msgid "Add URL Handler"
msgstr "Додати обробник URL-адрес"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
#, fuzzy
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Введіть протокол і команду для обробки URL-адреси відповідно:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
#, fuzzy
msgid "Edit Command"
msgstr "Редагувати Команду"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, fuzzy, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Введіть нову команду для протоколу \"%s\":"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
msgid "Username;APIKEY"
msgstr "Ім'я користувача;APIKEY"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
#, fuzzy
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr ""
"Музичний програвач (наприклад, amarok, audacious, exaile); залиште пустим "
"щоб визначити автоматично:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
msgid "Username: "
msgstr "Ім'я користувача: "

#: pynicotine/gtkgui/dialogs/preferences.py:2767
msgid "Command:"
msgstr "Команда:"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
msgid "Title"
msgstr "Назва"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Зараз відтворюється (зазвичай \"%(artist)s - %(title)s\")"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
msgid "Artist"
msgstr "Виконавець"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
msgid "Duration"
msgstr "Тривалість"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "Бітрейт"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
msgid "Comment"
msgstr "Коментар"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
msgid "Album"
msgstr "Альбом"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
msgid "Track Number"
msgstr "Номер треку"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
msgid "Year"
msgstr "Рік"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
msgid "Filename (URI)"
msgstr "Ім'я файлу (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
msgid "Program"
msgstr "Програма"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
msgid "Enabled"
msgstr "Увімкнено"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
msgid "Plugin"
msgstr "Плагін"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
msgid "No Plugin Selected"
msgstr "Не вибрано плагін"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
msgid "Preferences"
msgstr "Налаштування"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
msgid "Network"
msgstr "Мережа"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
msgid "User Interface"
msgstr "Користувацький інтерфейс"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Спільні файли"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Вивантаження"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Пошуки"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
msgid "User Profile"
msgstr "Профіль користувача"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
msgid "Chats"
msgstr "Чати"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
msgid "Now Playing"
msgstr "Зараз грає"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Журналювання"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
msgid "Banned Users"
msgstr "Заблоковані користувачі"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
msgid "Ignored Users"
msgstr "Ігноровані користувачі"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
msgid "URL Handlers"
msgstr "Обробники URL-адрес"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr "Плагіни"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
msgid "Pick a File Name for Config Backup"
msgstr "Виберіть ім’я файлу для резервного копіювання конфігурації"

#: pynicotine/gtkgui/dialogs/statistics.py:75
msgid "Transfer Statistics"
msgstr "Статистика передач"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, fuzzy, python-format
msgid "Total Since %(date)s"
msgstr "Усього з %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
msgid "Reset Transfer Statistics?"
msgstr "Скинути статистику передач?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
msgid "Do you really want to reset transfer statistics?"
msgstr "Ви дійсно хочете скинути статистику передач?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
msgid "Wishlist"
msgstr "Список бажань"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
msgid "Wish"
msgstr "Побажання"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
msgid "_Search for Item"
msgstr "_Знайти елемент"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
msgid "Edit Wish"
msgstr "Редагувати побажання"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, python-format
msgid "Enter new value for wish '%s':"
msgstr "Введіть нове значення для побажання \"%s\":"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
msgid "Clear Wishlist?"
msgstr "Очистити список побажань?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
msgid "Do you really want to clear your wishlist?"
msgstr "Ви дійсно хочете очистити свій список побажань?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Шлях"

#: pynicotine/gtkgui/downloads.py:47
msgid "_Resume"
msgstr "_Відновити"

#: pynicotine/gtkgui/downloads.py:48
msgid "P_ause"
msgstr "П_ауза"

#: pynicotine/gtkgui/downloads.py:72
msgid "Finished / Filtered"
msgstr "Готово / відфільтровано"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Готово"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
msgid "Paused"
msgstr "Призупинено"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
msgid "Filtered"
msgstr "Відфільтровано"

#: pynicotine/gtkgui/downloads.py:77
msgid "Deleted"
msgstr "Видалено"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
msgid "Queued…"
msgstr "У черзі…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
msgid "Everything…"
msgstr "Все…"

#: pynicotine/gtkgui/downloads.py:132
#, python-format
msgid "Downloads: %(speed)s"
msgstr "Завантаження: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
msgid "Clear Queued Downloads"
msgstr "Очистити завантаження в черзі"

#: pynicotine/gtkgui/downloads.py:139
msgid "Do you really want to clear all queued downloads?"
msgstr "Ви дійсно хочете очистити всі завантаження в черзі?"

#: pynicotine/gtkgui/downloads.py:151
msgid "Clear All Downloads"
msgstr "Очистити всі завантаження"

#: pynicotine/gtkgui/downloads.py:152
msgid "Do you really want to clear all downloads?"
msgstr "Ви дійсно хочете очистити всі завантаження?"

#: pynicotine/gtkgui/downloads.py:169
#, python-format
msgid "Download %(num)i files?"
msgstr "Завантажити файли %(num)i?"

#: pynicotine/gtkgui/downloads.py:170
#, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"Ви дійсно хочете завантажити файли %(num)i з каталогу %(user)s %(folder)s?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
msgid "_Download Folder"
msgstr "_Завантажити каталог"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
msgid "Likes"
msgstr "Подобається"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
msgid "Dislikes"
msgstr "Не подобається"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Рейтинг"

#: pynicotine/gtkgui/interests.py:111
msgid "Item"
msgstr "Елемент"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
msgid "_Recommendations for Item"
msgstr "_Рекомендації щодо елемента"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
msgid "I _Like This"
msgstr "Мені це _подобається"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
msgid "I _Dislike This"
msgstr "Мені це _не подобається"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
msgid "Recommendations"
msgstr "Рекомендації"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
msgid "Similar Users"
msgstr "Схожі користувачі"

#: pynicotine/gtkgui/interests.py:427
#, python-format
msgid "Recommendations (%s)"
msgstr "Рекомендації (%s)"

#: pynicotine/gtkgui/interests.py:451
#, python-format
msgid "Similar Users (%s)"
msgstr "Схожі користувачі (%s)"

#: pynicotine/gtkgui/mainwindow.py:238
#, fuzzy
msgid "Search log…"
msgstr "Пошук у журналі…"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, python-format
msgid "Private Message from %(user)s"
msgstr "Приватне повідомлення від %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
#, fuzzy
msgid "Wishlist Results Found"
msgstr "Знайдено результати згідно списку бажань"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
msgid "User Profiles"
msgstr "Профілі користувачів"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
msgid "Chat Rooms"
msgstr "Чати"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "Інтереси"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
msgid "Chat"
msgstr "Чат"

#: pynicotine/gtkgui/mainwindow.py:1111
msgid "[Debug] Connections"
msgstr "[Відлагодження] Підключення"

#: pynicotine/gtkgui/mainwindow.py:1112
msgid "[Debug] Messages"
msgstr "[Відлагодження] Повідомлення"

#: pynicotine/gtkgui/mainwindow.py:1113
msgid "[Debug] Transfers"
msgstr "[Відлагодження] Передачі"

#: pynicotine/gtkgui/mainwindow.py:1114
msgid "[Debug] Miscellaneous"
msgstr "[Відлагодження] Різне"

#: pynicotine/gtkgui/mainwindow.py:1119
msgid "_Find…"
msgstr "_Знайти…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
msgid "_Copy"
msgstr "_Копіювати"

#: pynicotine/gtkgui/mainwindow.py:1122
msgid "Copy _All"
msgstr "Копіювати _Все"

#: pynicotine/gtkgui/mainwindow.py:1127
#, fuzzy
msgid "View _Debug Logs"
msgstr "Переглянути журнали _налагодження"

#: pynicotine/gtkgui/mainwindow.py:1128
#, fuzzy
msgid "View _Transfer Logs"
msgstr "Переглянути журнал _передач"

#: pynicotine/gtkgui/mainwindow.py:1132
msgid "_Log Categories"
msgstr "_Категорії журналу"

#: pynicotine/gtkgui/mainwindow.py:1134
msgid "Clear Log View"
msgstr "Очистити вигляд журналу"

#: pynicotine/gtkgui/mainwindow.py:1199
#, fuzzy
msgid "Preparing Shares"
msgstr "Підготовка спільних каталогів"

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr "Сканування спільних каталогів"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
msgid "Shared Folders"
msgstr "Спільні каталоги"

#: pynicotine/gtkgui/popovers/chathistory.py:77
msgid "Latest Message"
msgstr "Останні повідомлення"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Кімната"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Користувачі"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
msgid "Join Room"
msgstr "Приєднатися до кімнати"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
msgid "Leave Room"
msgstr "Залишити кімнату"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
msgid "Disown Private Room"
msgstr "Відмовитися від окремої кімнати"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
msgid "Cancel Room Membership"
msgstr "Скасувати членство в кімнаті"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
msgid "Close All Tabs…"
msgstr "Закрити всі вкладинки…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
msgid "_Close Tab"
msgstr "_Закрити вкладинку"

#: pynicotine/gtkgui/privatechat.py:379
msgid "View Chat Log"
msgstr "Переглянути журнал чату"

#: pynicotine/gtkgui/privatechat.py:382
msgid "Delete Chat Log…"
msgstr "Видалити журнал чату…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
#, fuzzy
msgid "User Actions"
msgstr "Дії користувача"

#: pynicotine/gtkgui/privatechat.py:477
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Ви дійсно хочете назавжди видалити всі зареєстровані повідомлення для цього "
"користувача?"

#: pynicotine/gtkgui/privatechat.py:528
#, fuzzy
msgid "* Messages sent while you were offline"
msgstr "* Повідомлення, які були надіслані коли ви були офлайн"

#: pynicotine/gtkgui/search.py:90
msgid "_Global"
msgstr "_Глобальний"

#: pynicotine/gtkgui/search.py:91
msgid "_Buddies"
msgstr "_Друзі"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
msgid "_Rooms"
msgstr "_Кімнати"

#: pynicotine/gtkgui/search.py:93
msgid "_User"
msgstr "_Користувач"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
msgid "In Queue"
msgstr "У черзі"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
msgid "File Type"
msgstr "Тип файлу"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Ім'я файлу"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Розмір"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
#, fuzzy
msgid "Quality"
msgstr "Якість"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
msgid "Copy _File Path"
msgstr "Копіювати _шлях до файлу"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "Копіювати _посилання"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
msgid "Copy Folder U_RL"
msgstr "Копіювати _каталог посилання"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
msgid "_Download File(s)"
msgstr "_Завантажити файл(-и)"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
msgid "Download File(s) _To…"
msgstr "Завантажити файл(-и) _до…"

#: pynicotine/gtkgui/search.py:615
msgid "Download _Folder(s)"
msgstr "Завантажити _каталог(-и)"

#: pynicotine/gtkgui/search.py:616
msgid "Download F_older(s) To…"
msgstr "Завантажити к_аталог(-и) до…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
#, fuzzy
msgid "View User _Profile"
msgstr "Переглянути _профіль користувача"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
#, fuzzy
msgid "_Browse Folder"
msgstr "Перегляд папки"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
msgid "F_ile Properties"
msgstr "Властивості _файлу"

#: pynicotine/gtkgui/search.py:629
msgid "Copy Search Term"
msgstr "Копіювати пошуковий термін"

#: pynicotine/gtkgui/search.py:631
msgid "Clear All Results"
msgstr "Очистити всі результати"

#: pynicotine/gtkgui/search.py:718
#, fuzzy
msgid "Clear Filters"
msgstr "Очистити фільтри"

#: pynicotine/gtkgui/search.py:721
msgid "Restore Filters"
msgstr "Відновити фільтри"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, python-format
msgid "[PRIVATE]  %s"
msgstr "[ПРИВАТНО] %s"

#: pynicotine/gtkgui/search.py:1273
#, python-format
msgid "_Result Filters [%d]"
msgstr "_Фільтри результатів [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
msgid "_Result Filters"
msgstr "Фільтри _результатів"

#: pynicotine/gtkgui/search.py:1277
#, python-format
msgid "%d active filter(s)"
msgstr "%d активних фільтрів"

#: pynicotine/gtkgui/search.py:1329
msgid "Add Wi_sh"
msgstr "Додати _бажання"

#: pynicotine/gtkgui/search.py:1332
msgid "Remove Wi_sh"
msgstr "Видалити б_ажання"

#: pynicotine/gtkgui/search.py:1349
msgid "Select User's Results"
msgstr "Виберіть результати користувача"

#: pynicotine/gtkgui/search.py:1472
#, python-format
msgid "Total: %s"
msgstr "Всього: %s"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
msgid "Results"
msgstr "Результати"

#: pynicotine/gtkgui/search.py:1590
msgid "Select Destination Folder for File(s)"
msgstr "Виберіть каталог призначення для файлу(-ів)"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
msgid "Select Destination Folder"
msgstr "Виберіть каталог призначення"

#: pynicotine/gtkgui/transfers.py:61
msgid "Queued"
msgstr "В черзі"

#: pynicotine/gtkgui/transfers.py:62
msgid "Queued (prioritized)"
msgstr "В черзі (з пріоритетом)"

#: pynicotine/gtkgui/transfers.py:63
msgid "Queued (privileged)"
msgstr "В черзі (привілейований)"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Отримання статусу"

#: pynicotine/gtkgui/transfers.py:65
msgid "Transferring"
msgstr "Передавання"

#: pynicotine/gtkgui/transfers.py:66
msgid "Connection closed"
msgstr "З'єднання закрито"

#: pynicotine/gtkgui/transfers.py:67
msgid "Connection timeout"
msgstr "Час очікування з'єднання вичерпано"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Користувач вийшов"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
msgid "Cancelled"
msgstr "Скасовано"

#: pynicotine/gtkgui/transfers.py:73
msgid "Download folder error"
msgstr "Помилка завантаження каталогу"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Помилка локального файлу"

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr "Заблоковано"

#: pynicotine/gtkgui/transfers.py:76
msgid "File not shared"
msgstr "Файл не є спільним"

#: pynicotine/gtkgui/transfers.py:77
msgid "Pending shutdown"
msgstr "Очікування вимкнення"

#: pynicotine/gtkgui/transfers.py:78
msgid "File read error"
msgstr "Помилка передачі файлу"

#: pynicotine/gtkgui/transfers.py:182
msgid "Queue"
msgstr "В черзі"

#: pynicotine/gtkgui/transfers.py:188
msgid "Percent"
msgstr "Відсоток"

#: pynicotine/gtkgui/transfers.py:208
msgid "Time Elapsed"
msgstr "Минуло часу"

#: pynicotine/gtkgui/transfers.py:215
msgid "Time Left"
msgstr "Залишилося часу"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
msgid "_Open File"
msgstr "_Відкрити файл"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
msgid "Open in File _Manager"
msgstr "Відкрити у _файловому менеджері"

#: pynicotine/gtkgui/transfers.py:286
msgid "_Search"
msgstr "_Пошук"

#: pynicotine/gtkgui/transfers.py:289
msgid "Clear All"
msgstr "Очистити все"

#: pynicotine/gtkgui/transfers.py:953
msgid "Select User's Transfers"
msgstr "Виберіть передачі користувачів"

#: pynicotine/gtkgui/uploads.py:50
msgid "_Abort"
msgstr "_Перервати"

#: pynicotine/gtkgui/uploads.py:74
msgid "Finished / Cancelled / Failed"
msgstr "Завершено / Скасовано / Не вдалося"

#: pynicotine/gtkgui/uploads.py:75
msgid "Finished / Cancelled"
msgstr "Завершено / Скасовано"

#: pynicotine/gtkgui/uploads.py:79
msgid "Failed"
msgstr "Не вдалося"

#: pynicotine/gtkgui/uploads.py:80
msgid "User Logged Off"
msgstr "Користувач вийшов"

#: pynicotine/gtkgui/uploads.py:142
#, python-format
msgid "Uploads: %(speed)s"
msgstr "Вивантаження: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr "Завершення..."

#: pynicotine/gtkgui/uploads.py:164
msgid "Clear Queued Uploads"
msgstr "Очистити чергу вивантажень"

#: pynicotine/gtkgui/uploads.py:165
msgid "Do you really want to clear all queued uploads?"
msgstr "Ви дійсно хочете очистити всі вивантаження в черзі?"

#: pynicotine/gtkgui/uploads.py:177
msgid "Clear All Uploads"
msgstr "Очистити всі вивантаження"

#: pynicotine/gtkgui/uploads.py:178
msgid "Do you really want to clear all uploads?"
msgstr "Ви дійсно хочете очистити всі вивантаження?"

#: pynicotine/gtkgui/userbrowse.py:282
msgid "_Save Shares List to Disk"
msgstr "_Зберегти список спільних каталогів на диск"

#: pynicotine/gtkgui/userbrowse.py:292
msgid "Upload Folder & Subfolders…"
msgstr "Вивантажити каталог з підкаталогами…"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
msgid "Copy _Folder Path"
msgstr "Копіювати _шлях до папки"

#: pynicotine/gtkgui/userbrowse.py:309
msgid "_Download Folder & Subfolders"
msgstr "Завантажити каталог та підкаталоги"

#: pynicotine/gtkgui/userbrowse.py:310
msgid "Download Folder & Subfolders _To…"
msgstr "Завантажити каталог та підкаталоги до…"

#: pynicotine/gtkgui/userbrowse.py:334
msgid "File Name"
msgstr "Ім'я файлу"

#: pynicotine/gtkgui/userbrowse.py:373
msgid "Up_load File(s)…"
msgstr "_Вивантажити файл(-и)…"

#: pynicotine/gtkgui/userbrowse.py:374
msgid "Upload Folder…"
msgstr "Вивантажити каталог…"

#: pynicotine/gtkgui/userbrowse.py:396
msgid "Download Folder _To…"
msgstr "Завантажити каталог _до…"

#: pynicotine/gtkgui/userbrowse.py:600
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"Список спільних файлів користувача порожній. Або користувач нічим не "
"ділиться, або він ділиться файлами приватно."

#: pynicotine/gtkgui/userbrowse.py:615
#, fuzzy
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Неможливо запитати спільні файли від користувача. Або користувач поза "
"мережею, у вас обох закритий порт прослуховування, або виникла тимчасова "
"проблема з підключенням."

#: pynicotine/gtkgui/userbrowse.py:953
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Виберіть розміщення для завантаження кількох каталогів"

#: pynicotine/gtkgui/userbrowse.py:997
msgid "Upload Folder (with Subfolders) To User"
msgstr "Вивантажити каталог (з підкаталогами) користувачеві"

#: pynicotine/gtkgui/userbrowse.py:999
msgid "Upload Folder To User"
msgstr "Вивантажити каталог користувачеві"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
msgid "Enter the name of the user you want to upload to:"
msgstr "Введіть ім’я користувача, якому ви хочете вивантажити:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
#, fuzzy
msgid "_Upload"
msgstr "Вивантаження"

#: pynicotine/gtkgui/userbrowse.py:1139
#, fuzzy
msgid "Select Destination Folder for Files"
msgstr "Виберіть каталог призначення для файлу(-ів)"

#: pynicotine/gtkgui/userbrowse.py:1161
msgid "Upload File(s) To User"
msgstr "Вивантажити файл(-и) користувачеві"

#: pynicotine/gtkgui/userinfo.py:376
#, fuzzy
msgid "Copy Picture"
msgstr "Копіювати Зображення"

#: pynicotine/gtkgui/userinfo.py:377
msgid "Save Picture"
msgstr "Зберегти зображення"

#: pynicotine/gtkgui/userinfo.py:468
#, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Не вдалося завантажити зображення для користувача %(user)s: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Неможливо запитати інформацію від користувача. Або ви обидва маєте закритий "
"порт прослуховування, або користувач перебуває поза мережею, або є тимчасові "
"проблеми зі з'єднанням."

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Remove _Buddy"
msgstr "Видалити б_ажання"

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Add _Buddy"
msgstr "Додати _друга"

#: pynicotine/gtkgui/userinfo.py:581
#, fuzzy
msgid "Unban User"
msgstr "Блокувати користувача"

#: pynicotine/gtkgui/userinfo.py:585
#, fuzzy
msgid "Unignore User"
msgstr "Ігнорувати користувача"

#: pynicotine/gtkgui/userinfo.py:613
msgid "Yes"
msgstr "Так"

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr "Немає"

#: pynicotine/gtkgui/userinfo.py:773
msgid "Please enter number of days."
msgstr "Будь ласка, введіть кількість днів."

#: pynicotine/gtkgui/userinfo.py:787
#, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Подарувати дні ваших привілеїв Soulseek користувачеві %(user)s "
"(%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, python-format
msgid "%(days)s days left"
msgstr "Залишилося %(days)s днів"

#: pynicotine/gtkgui/userinfo.py:795
msgid "Gift Privileges"
msgstr "Подарувати привілеї"

#: pynicotine/gtkgui/userinfo.py:797
#, fuzzy
msgid "_Give Privileges"
msgstr "Подарувати привілеї"

#: pynicotine/gtkgui/widgets/dialogs.py:309
msgid "Close"
msgstr "Закрити"

#: pynicotine/gtkgui/widgets/dialogs.py:488
msgid "_Yes"
msgstr "_Так"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
msgid "_OK"
msgstr "_Гаразд"

#: pynicotine/gtkgui/widgets/filechooser.py:39
msgid "Select a File"
msgstr "Виберіть файл"

#: pynicotine/gtkgui/widgets/filechooser.py:174
msgid "Select a Folder"
msgstr "Виберіть каталог"

#: pynicotine/gtkgui/widgets/filechooser.py:179
#, fuzzy
msgid "_Select"
msgstr "Вибрати все"

#: pynicotine/gtkgui/widgets/filechooser.py:196
msgid "Select an Image"
msgstr "Виберіть зображення"

#: pynicotine/gtkgui/widgets/filechooser.py:203
#, fuzzy
msgid "All images"
msgstr "Всі зображення"

#: pynicotine/gtkgui/widgets/filechooser.py:241
msgid "Save as…"
msgstr "Зберегти як…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
msgid "(None)"
msgstr "(Немає)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
#, fuzzy
msgid "Close Tab"
msgstr "_Закрити вкладинку"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
msgid "Close All Tabs?"
msgstr "Закрити всі вкладки?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
msgid "Do you really want to close all tabs?"
msgstr "Ви дійсно хочете закрити всі вкладки?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, fuzzy, python-format
msgid "%i Unread Tab(s)"
msgstr "Непрочитані вкладинки"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
#, fuzzy
msgid "All Tabs"
msgstr "Всі вкладки"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
#, fuzzy
msgid "Re_open Closed Tab"
msgstr "_Закрити вкладинку"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, python-format
msgid "%s File(s) Selected"
msgstr "Вибрано %s файл(-ів)"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
msgid "_Browse Files"
msgstr "_Перегляд файлів"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
#, fuzzy
msgid "_Add Buddy"
msgstr "Д_одати друга"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
msgid "Show IP A_ddress"
msgstr "Показати IP-_адресу"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
msgid "Private Rooms"
msgstr "Приватні кімнати"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, python-format
msgid "Remove from Private Room %s"
msgstr "Видалити з приватної кімнати %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, python-format
msgid "Add to Private Room %s"
msgstr "Додати до приватної кімнати %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, python-format
msgid "Remove as Operator of %s"
msgstr "Видалити як оператора %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, python-format
msgid "Add as Operator of %s"
msgstr "Додати як оператора %s"

#: pynicotine/gtkgui/widgets/textentry.py:37
#, fuzzy
msgid "Send message…"
msgstr "Відправити повідомлення…"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
msgid "Find Previous Match"
msgstr "Знайти попередне співпадіння"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
msgid "Find Next Match"
msgstr "Знайти наступне співпадіння"

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr "--- старі повідомлення вище ---"

#: pynicotine/gtkgui/widgets/theme.py:243
#, fuzzy
msgid "Executable"
msgstr "Виконано: %s"

#: pynicotine/gtkgui/widgets/theme.py:244
#, fuzzy
msgid "Audio"
msgstr "Аудіо"

#: pynicotine/gtkgui/widgets/theme.py:245
#, fuzzy
msgid "Image"
msgstr "Зображення"

#: pynicotine/gtkgui/widgets/theme.py:246
#, fuzzy
msgid "Archive"
msgstr "Архів"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
msgid "Miscellaneous"
msgstr "Різне"

#: pynicotine/gtkgui/widgets/theme.py:248
#, fuzzy
msgid "Video"
msgstr "відео"

#: pynicotine/gtkgui/widgets/theme.py:249
#, fuzzy
msgid "Document"
msgstr "Документ/Текст"

#: pynicotine/gtkgui/widgets/theme.py:250
#, fuzzy
msgid "Text"
msgstr "Текст"

#: pynicotine/gtkgui/widgets/theme.py:357
#, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "Помилка завантаження спеціального значка %(path)s: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Hide Nicotine+"
msgstr "Приховати Nicotine+"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Show Nicotine+"
msgstr "Показати Nicotine+"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, python-format
msgid "Column #%i"
msgstr "Стовпець №%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
msgid "Ungrouped"
msgstr "Незгрупований"

#: pynicotine/gtkgui/widgets/treeview.py:960
msgid "Group by Folder"
msgstr "Групувати за каталогом"

#: pynicotine/gtkgui/widgets/treeview.py:963
msgid "Group by User"
msgstr "Групувати за користувачем"

#: pynicotine/headless/application.py:77
#, python-format
msgid "Do you really want to exit? %s"
msgstr "Ви дійсно хочете вийти? %s"

#: pynicotine/headless/application.py:81
#, fuzzy, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr ""
"Користувач %s вже існує, а введений пароль недійсний. Будь ласка, виберіть "
"інше ім’я користувача, якщо це ваш перший вхід."

#: pynicotine/headless/application.py:83
#, fuzzy, python-format
msgid "Type %s to log in with another username or password."
msgstr "[X0X] обмінів вказано ([X30X] налаштовано)"

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
#, fuzzy
msgid "Password: "
msgstr "Пароль: "

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Щоб створити новий обліковий запис Soulseek, введіть бажане ім’я користувача "
"та пароль. Якщо у вас уже є обліковий запис, заповніть існуючі дані для "
"входу."

#: pynicotine/headless/application.py:119
#, fuzzy
msgid "The following shares are unavailable:"
msgstr "Наступні обміні недоступні:"

#: pynicotine/headless/application.py:125
#, fuzzy, python-format
msgid "Retry rescan? %s"
msgstr "Повторити повторне сканування? %s"

#: pynicotine/logfacility.py:181
#, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr "Не вдалося записати у файл журналу \"%(filename)s\": %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, fuzzy, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Не вдається зберегти файл %(path)s: %(error)s"

#: pynicotine/networkfilter.py:40
msgid "Andorra"
msgstr "Андорра"

#: pynicotine/networkfilter.py:41
msgid "United Arab Emirates"
msgstr "Об'єднані Арабські Емірати"

#: pynicotine/networkfilter.py:42
msgid "Afghanistan"
msgstr "Афганістан"

#: pynicotine/networkfilter.py:43
msgid "Antigua & Barbuda"
msgstr "Антигуа і Барбуда"

#: pynicotine/networkfilter.py:44
msgid "Anguilla"
msgstr "Ангілья"

#: pynicotine/networkfilter.py:45
msgid "Albania"
msgstr "Албанія"

#: pynicotine/networkfilter.py:46
msgid "Armenia"
msgstr "Вірменія"

#: pynicotine/networkfilter.py:47
msgid "Angola"
msgstr "Ангола"

#: pynicotine/networkfilter.py:48
msgid "Antarctica"
msgstr "Антарктида"

#: pynicotine/networkfilter.py:49
msgid "Argentina"
msgstr "Аргентина"

#: pynicotine/networkfilter.py:50
msgid "American Samoa"
msgstr "Американське Самоа"

#: pynicotine/networkfilter.py:51
msgid "Austria"
msgstr "Австрія"

#: pynicotine/networkfilter.py:52
msgid "Australia"
msgstr "Австралія"

#: pynicotine/networkfilter.py:53
msgid "Aruba"
msgstr "Аруба"

#: pynicotine/networkfilter.py:54
msgid "Åland Islands"
msgstr "Аландські острови"

#: pynicotine/networkfilter.py:55
msgid "Azerbaijan"
msgstr "Азербайджан"

#: pynicotine/networkfilter.py:56
msgid "Bosnia & Herzegovina"
msgstr "Боснія і Герцеговина"

#: pynicotine/networkfilter.py:57
msgid "Barbados"
msgstr "Барбадос"

#: pynicotine/networkfilter.py:58
msgid "Bangladesh"
msgstr "Бангладеш"

#: pynicotine/networkfilter.py:59
msgid "Belgium"
msgstr "Бельгія"

#: pynicotine/networkfilter.py:60
msgid "Burkina Faso"
msgstr "Буркіна-Фасо"

#: pynicotine/networkfilter.py:61
msgid "Bulgaria"
msgstr "Болгарія"

#: pynicotine/networkfilter.py:62
msgid "Bahrain"
msgstr "Бахрейн"

#: pynicotine/networkfilter.py:63
msgid "Burundi"
msgstr "Бурунді"

#: pynicotine/networkfilter.py:64
msgid "Benin"
msgstr "Бенін"

#: pynicotine/networkfilter.py:65
msgid "Saint Barthelemy"
msgstr "Сен-Бартелемі"

#: pynicotine/networkfilter.py:66
msgid "Bermuda"
msgstr "Бермудські острови"

#: pynicotine/networkfilter.py:67
msgid "Brunei Darussalam"
msgstr "Бруней-Даруссалам"

#: pynicotine/networkfilter.py:68
msgid "Bolivia"
msgstr "Болівія"

#: pynicotine/networkfilter.py:69
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Бонайре, Сент-Естатіус і Саба"

#: pynicotine/networkfilter.py:70
msgid "Brazil"
msgstr "Бразилія"

#: pynicotine/networkfilter.py:71
msgid "Bahamas"
msgstr "Багамські острови"

#: pynicotine/networkfilter.py:72
msgid "Bhutan"
msgstr "Бутан"

#: pynicotine/networkfilter.py:73
msgid "Bouvet Island"
msgstr "Острів Буве"

#: pynicotine/networkfilter.py:74
msgid "Botswana"
msgstr "Ботсвана"

#: pynicotine/networkfilter.py:75
msgid "Belarus"
msgstr "Білорусь"

#: pynicotine/networkfilter.py:76
msgid "Belize"
msgstr "Беліз"

#: pynicotine/networkfilter.py:77
msgid "Canada"
msgstr "Канада"

#: pynicotine/networkfilter.py:78
msgid "Cocos (Keeling) Islands"
msgstr "Кокосові (Кілінг) острови"

#: pynicotine/networkfilter.py:79
msgid "Democratic Republic of Congo"
msgstr "Демократична Республіка Конго"

#: pynicotine/networkfilter.py:80
msgid "Central African Republic"
msgstr "Центральноафриканська Республіка"

#: pynicotine/networkfilter.py:81
msgid "Congo"
msgstr "Конго"

#: pynicotine/networkfilter.py:82
msgid "Switzerland"
msgstr "Швейцарія"

#: pynicotine/networkfilter.py:83
msgid "Ivory Coast"
msgstr "Кот-Д'Ивуар"

#: pynicotine/networkfilter.py:84
msgid "Cook Islands"
msgstr "Острови Кука"

#: pynicotine/networkfilter.py:85
msgid "Chile"
msgstr "Чилі"

#: pynicotine/networkfilter.py:86
msgid "Cameroon"
msgstr "Камерун"

#: pynicotine/networkfilter.py:87
msgid "China"
msgstr "Китай"

#: pynicotine/networkfilter.py:88
msgid "Colombia"
msgstr "Колумбія"

#: pynicotine/networkfilter.py:89
msgid "Costa Rica"
msgstr "Коста-Рика"

#: pynicotine/networkfilter.py:90
msgid "Cuba"
msgstr "Куба"

#: pynicotine/networkfilter.py:91
msgid "Cabo Verde"
msgstr "Кабо-Верде"

#: pynicotine/networkfilter.py:92
msgid "Curaçao"
msgstr "Кюрасао"

#: pynicotine/networkfilter.py:93
msgid "Christmas Island"
msgstr "Острів Різдва"

#: pynicotine/networkfilter.py:94
msgid "Cyprus"
msgstr "Кіпр"

#: pynicotine/networkfilter.py:95
#, fuzzy
msgid "Czechia"
msgstr "Чехія"

#: pynicotine/networkfilter.py:96
msgid "Germany"
msgstr "Німеччина"

#: pynicotine/networkfilter.py:97
msgid "Djibouti"
msgstr "Джібуті"

#: pynicotine/networkfilter.py:98
msgid "Denmark"
msgstr "Данія"

#: pynicotine/networkfilter.py:99
msgid "Dominica"
msgstr "Домініка"

#: pynicotine/networkfilter.py:100
msgid "Dominican Republic"
msgstr "Домініканська республіка"

#: pynicotine/networkfilter.py:101
msgid "Algeria"
msgstr "Алжир"

#: pynicotine/networkfilter.py:102
msgid "Ecuador"
msgstr "Еквадор"

#: pynicotine/networkfilter.py:103
msgid "Estonia"
msgstr "Естонія"

#: pynicotine/networkfilter.py:104
msgid "Egypt"
msgstr "Єгипет"

#: pynicotine/networkfilter.py:105
msgid "Western Sahara"
msgstr "Західна Сахара"

#: pynicotine/networkfilter.py:106
msgid "Eritrea"
msgstr "Еритрея"

#: pynicotine/networkfilter.py:107
msgid "Spain"
msgstr "Іспанія"

#: pynicotine/networkfilter.py:108
msgid "Ethiopia"
msgstr "Ефіопія"

#: pynicotine/networkfilter.py:109
msgid "Europe"
msgstr "Європа"

#: pynicotine/networkfilter.py:110
msgid "Finland"
msgstr "Фінляндія"

#: pynicotine/networkfilter.py:111
msgid "Fiji"
msgstr "Фіджі"

#: pynicotine/networkfilter.py:112
msgid "Falkland Islands (Malvinas)"
msgstr "Фолклендські (Мальвінські) острови"

#: pynicotine/networkfilter.py:113
msgid "Micronesia"
msgstr "Мікронезія"

#: pynicotine/networkfilter.py:114
msgid "Faroe Islands"
msgstr "Фарерські острови"

#: pynicotine/networkfilter.py:115
msgid "France"
msgstr "Франція"

#: pynicotine/networkfilter.py:116
msgid "Gabon"
msgstr "Габон"

#: pynicotine/networkfilter.py:117
msgid "Great Britain"
msgstr "Велика Британія"

#: pynicotine/networkfilter.py:118
msgid "Grenada"
msgstr "Гренада"

#: pynicotine/networkfilter.py:119
msgid "Georgia"
msgstr "Грузія"

#: pynicotine/networkfilter.py:120
msgid "French Guiana"
msgstr "Французька Гвіана"

#: pynicotine/networkfilter.py:121
msgid "Guernsey"
msgstr "Гернсі"

#: pynicotine/networkfilter.py:122
msgid "Ghana"
msgstr "Гана"

#: pynicotine/networkfilter.py:123
msgid "Gibraltar"
msgstr "Гібралтар"

#: pynicotine/networkfilter.py:124
msgid "Greenland"
msgstr "Гренландія"

#: pynicotine/networkfilter.py:125
msgid "Gambia"
msgstr "Гамбія"

#: pynicotine/networkfilter.py:126
msgid "Guinea"
msgstr "Гвінея"

#: pynicotine/networkfilter.py:127
msgid "Guadeloupe"
msgstr "Гваделупа"

#: pynicotine/networkfilter.py:128
msgid "Equatorial Guinea"
msgstr "Екваторіальна Гвінея"

#: pynicotine/networkfilter.py:129
msgid "Greece"
msgstr "Греція"

#: pynicotine/networkfilter.py:130
msgid "South Georgia & South Sandwich Islands"
msgstr "Південна Джорджія та Південні Сандвічеві острови"

#: pynicotine/networkfilter.py:131
msgid "Guatemala"
msgstr "Гватемала"

#: pynicotine/networkfilter.py:132
msgid "Guam"
msgstr "Гуам"

#: pynicotine/networkfilter.py:133
msgid "Guinea-Bissau"
msgstr "Гвінея-Бісау"

#: pynicotine/networkfilter.py:134
msgid "Guyana"
msgstr "Гайана"

#: pynicotine/networkfilter.py:135
msgid "Hong Kong"
msgstr "Гонконг"

#: pynicotine/networkfilter.py:136
msgid "Heard & McDonald Islands"
msgstr "Острови Херда і МакДональда"

#: pynicotine/networkfilter.py:137
msgid "Honduras"
msgstr "Гондурас"

#: pynicotine/networkfilter.py:138
msgid "Croatia"
msgstr "Хорватія"

#: pynicotine/networkfilter.py:139
msgid "Haiti"
msgstr "Гаїті"

#: pynicotine/networkfilter.py:140
msgid "Hungary"
msgstr "Угорщина"

#: pynicotine/networkfilter.py:141
msgid "Indonesia"
msgstr "Індонезія"

#: pynicotine/networkfilter.py:142
msgid "Ireland"
msgstr "Ірландія"

#: pynicotine/networkfilter.py:143
msgid "Israel"
msgstr "Ізраїль"

#: pynicotine/networkfilter.py:144
msgid "Isle of Man"
msgstr "Острів Мен"

#: pynicotine/networkfilter.py:145
msgid "India"
msgstr "Індія"

#: pynicotine/networkfilter.py:146
msgid "British Indian Ocean Territory"
msgstr "Британська територія в Індійському океані"

#: pynicotine/networkfilter.py:147
msgid "Iraq"
msgstr "Ірак"

#: pynicotine/networkfilter.py:148
msgid "Iran"
msgstr "Іран"

#: pynicotine/networkfilter.py:149
msgid "Iceland"
msgstr "Ісландія"

#: pynicotine/networkfilter.py:150
msgid "Italy"
msgstr "Італія"

#: pynicotine/networkfilter.py:151
msgid "Jersey"
msgstr "Джерсі"

#: pynicotine/networkfilter.py:152
msgid "Jamaica"
msgstr "Ямайка"

#: pynicotine/networkfilter.py:153
msgid "Jordan"
msgstr "Йорданія"

#: pynicotine/networkfilter.py:154
msgid "Japan"
msgstr "Японія"

#: pynicotine/networkfilter.py:155
msgid "Kenya"
msgstr "Кенія"

#: pynicotine/networkfilter.py:156
msgid "Kyrgyzstan"
msgstr "Киргизстан"

#: pynicotine/networkfilter.py:157
msgid "Cambodia"
msgstr "Камбоджа"

#: pynicotine/networkfilter.py:158
msgid "Kiribati"
msgstr "Кірібаті"

#: pynicotine/networkfilter.py:159
msgid "Comoros"
msgstr "Коморські острови"

#: pynicotine/networkfilter.py:160
msgid "Saint Kitts & Nevis"
msgstr "Сент-Кітс і Невіс"

#: pynicotine/networkfilter.py:161
msgid "North Korea"
msgstr "Північна Корея"

#: pynicotine/networkfilter.py:162
msgid "South Korea"
msgstr "Південна Корея"

#: pynicotine/networkfilter.py:163
msgid "Kuwait"
msgstr "Кувейт"

#: pynicotine/networkfilter.py:164
msgid "Cayman Islands"
msgstr "Кайманові острови"

#: pynicotine/networkfilter.py:165
msgid "Kazakhstan"
msgstr "Казахстан"

#: pynicotine/networkfilter.py:166
msgid "Laos"
msgstr "Лаос"

#: pynicotine/networkfilter.py:167
msgid "Lebanon"
msgstr "Ліван"

#: pynicotine/networkfilter.py:168
msgid "Saint Lucia"
msgstr "Сент-Люсія"

#: pynicotine/networkfilter.py:169
msgid "Liechtenstein"
msgstr "Ліхтенштейн"

#: pynicotine/networkfilter.py:170
msgid "Sri Lanka"
msgstr "Шрі-Ланка"

#: pynicotine/networkfilter.py:171
msgid "Liberia"
msgstr "Ліберія"

#: pynicotine/networkfilter.py:172
msgid "Lesotho"
msgstr "Лесото"

#: pynicotine/networkfilter.py:173
msgid "Lithuania"
msgstr "Литва"

#: pynicotine/networkfilter.py:174
msgid "Luxembourg"
msgstr "Люксембург"

#: pynicotine/networkfilter.py:175
msgid "Latvia"
msgstr "Латвія"

#: pynicotine/networkfilter.py:176
msgid "Libya"
msgstr "Лівія"

#: pynicotine/networkfilter.py:177
msgid "Morocco"
msgstr "Марокко"

#: pynicotine/networkfilter.py:178
msgid "Monaco"
msgstr "Монако"

#: pynicotine/networkfilter.py:179
msgid "Moldova"
msgstr "Молдова"

#: pynicotine/networkfilter.py:180
msgid "Montenegro"
msgstr "Чорногорія"

#: pynicotine/networkfilter.py:181
msgid "Saint Martin"
msgstr "Сен-Мартен"

#: pynicotine/networkfilter.py:182
msgid "Madagascar"
msgstr "Мадагаскар"

#: pynicotine/networkfilter.py:183
msgid "Marshall Islands"
msgstr "Маршаллові острови"

#: pynicotine/networkfilter.py:184
msgid "North Macedonia"
msgstr "Північна Македонія"

#: pynicotine/networkfilter.py:185
msgid "Mali"
msgstr "Малі"

#: pynicotine/networkfilter.py:186
msgid "Myanmar"
msgstr "М'янма"

#: pynicotine/networkfilter.py:187
msgid "Mongolia"
msgstr "Монголія"

#: pynicotine/networkfilter.py:188
msgid "Macau"
msgstr "Макао"

#: pynicotine/networkfilter.py:189
msgid "Northern Mariana Islands"
msgstr "Північні Маріанські острови"

#: pynicotine/networkfilter.py:190
msgid "Martinique"
msgstr "Мартініка"

#: pynicotine/networkfilter.py:191
msgid "Mauritania"
msgstr "Мавританія"

#: pynicotine/networkfilter.py:192
msgid "Montserrat"
msgstr "Монтсеррат"

#: pynicotine/networkfilter.py:193
msgid "Malta"
msgstr "Мальта"

#: pynicotine/networkfilter.py:194
msgid "Mauritius"
msgstr "Маврикій"

#: pynicotine/networkfilter.py:195
msgid "Maldives"
msgstr "Мальдіви"

#: pynicotine/networkfilter.py:196
msgid "Malawi"
msgstr "Малаві"

#: pynicotine/networkfilter.py:197
msgid "Mexico"
msgstr "Мексика"

#: pynicotine/networkfilter.py:198
msgid "Malaysia"
msgstr "Малайзія"

#: pynicotine/networkfilter.py:199
msgid "Mozambique"
msgstr "Мозамбік"

#: pynicotine/networkfilter.py:200
msgid "Namibia"
msgstr "Намібія"

#: pynicotine/networkfilter.py:201
msgid "New Caledonia"
msgstr "Нова Каледонія"

#: pynicotine/networkfilter.py:202
msgid "Niger"
msgstr "Нігер"

#: pynicotine/networkfilter.py:203
msgid "Norfolk Island"
msgstr "Острів Норфолк"

#: pynicotine/networkfilter.py:204
msgid "Nigeria"
msgstr "Нігерія"

#: pynicotine/networkfilter.py:205
msgid "Nicaragua"
msgstr "Нікарагуа"

#: pynicotine/networkfilter.py:206
msgid "Netherlands"
msgstr "Нідерланди"

#: pynicotine/networkfilter.py:207
msgid "Norway"
msgstr "Норвегія"

#: pynicotine/networkfilter.py:208
msgid "Nepal"
msgstr "Непал"

#: pynicotine/networkfilter.py:209
msgid "Nauru"
msgstr "Науру"

#: pynicotine/networkfilter.py:210
msgid "Niue"
msgstr "Ніуе"

#: pynicotine/networkfilter.py:211
msgid "New Zealand"
msgstr "Нова Зеландія"

#: pynicotine/networkfilter.py:212
msgid "Oman"
msgstr "Оман"

#: pynicotine/networkfilter.py:213
msgid "Panama"
msgstr "Панама"

#: pynicotine/networkfilter.py:214
msgid "Peru"
msgstr "Перу"

#: pynicotine/networkfilter.py:215
msgid "French Polynesia"
msgstr "Французька Полінезія"

#: pynicotine/networkfilter.py:216
msgid "Papua New Guinea"
msgstr "Папуа-Нова Гвінея"

#: pynicotine/networkfilter.py:217
msgid "Philippines"
msgstr "Філіппіни"

#: pynicotine/networkfilter.py:218
msgid "Pakistan"
msgstr "Пакистан"

#: pynicotine/networkfilter.py:219
msgid "Poland"
msgstr "Польща"

#: pynicotine/networkfilter.py:220
msgid "Saint Pierre & Miquelon"
msgstr "Сен-П'єр і Мікелон"

#: pynicotine/networkfilter.py:221
msgid "Pitcairn"
msgstr "Піткерн"

#: pynicotine/networkfilter.py:222
msgid "Puerto Rico"
msgstr "Пуерто-Ріко"

#: pynicotine/networkfilter.py:223
msgid "State of Palestine"
msgstr "Палестина"

#: pynicotine/networkfilter.py:224
msgid "Portugal"
msgstr "Португалія"

#: pynicotine/networkfilter.py:225
msgid "Palau"
msgstr "Палау"

#: pynicotine/networkfilter.py:226
msgid "Paraguay"
msgstr "Парагвай"

#: pynicotine/networkfilter.py:227
msgid "Qatar"
msgstr "Катар"

#: pynicotine/networkfilter.py:228
msgid "Réunion"
msgstr "Реюньон"

#: pynicotine/networkfilter.py:229
msgid "Romania"
msgstr "Румунія"

#: pynicotine/networkfilter.py:230
msgid "Serbia"
msgstr "Сербія"

#: pynicotine/networkfilter.py:231
msgid "Russia"
msgstr "Росія"

#: pynicotine/networkfilter.py:232
msgid "Rwanda"
msgstr "Руанда"

#: pynicotine/networkfilter.py:233
msgid "Saudi Arabia"
msgstr "Саудівська Аравія"

#: pynicotine/networkfilter.py:234
msgid "Solomon Islands"
msgstr "Соломонові острови"

#: pynicotine/networkfilter.py:235
msgid "Seychelles"
msgstr "Сейшельські острови"

#: pynicotine/networkfilter.py:236
msgid "Sudan"
msgstr "Судан"

#: pynicotine/networkfilter.py:237
msgid "Sweden"
msgstr "Швеція"

#: pynicotine/networkfilter.py:238
msgid "Singapore"
msgstr "Сінгапур"

#: pynicotine/networkfilter.py:239
msgid "Saint Helena"
msgstr "Свята Олена"

#: pynicotine/networkfilter.py:240
msgid "Slovenia"
msgstr "Словенія"

#: pynicotine/networkfilter.py:241
msgid "Svalbard & Jan Mayen Islands"
msgstr "Острови Шпіцберген і Ян-Майєн"

#: pynicotine/networkfilter.py:242
msgid "Slovak Republic"
msgstr "Словацька Республіка"

#: pynicotine/networkfilter.py:243
msgid "Sierra Leone"
msgstr "Сьєрра-Леоне"

#: pynicotine/networkfilter.py:244
msgid "San Marino"
msgstr "Сан-Марино"

#: pynicotine/networkfilter.py:245
msgid "Senegal"
msgstr "Сенегал"

#: pynicotine/networkfilter.py:246
msgid "Somalia"
msgstr "Сомалі"

#: pynicotine/networkfilter.py:247
msgid "Suriname"
msgstr "Суринам"

#: pynicotine/networkfilter.py:248
msgid "South Sudan"
msgstr "Південний Судан"

#: pynicotine/networkfilter.py:249
msgid "Sao Tome & Principe"
msgstr "Сан-Томе і Прінсіпі"

#: pynicotine/networkfilter.py:250
msgid "El Salvador"
msgstr "Сальвадор"

#: pynicotine/networkfilter.py:251
msgid "Sint Maarten"
msgstr "Сінт-Мартен"

#: pynicotine/networkfilter.py:252
msgid "Syria"
msgstr "Сирія"

#: pynicotine/networkfilter.py:253
msgid "Eswatini"
msgstr "Есватіні"

#: pynicotine/networkfilter.py:254
msgid "Turks & Caicos Islands"
msgstr "Острови Теркс і Кайкос"

#: pynicotine/networkfilter.py:255
msgid "Chad"
msgstr "Чад"

#: pynicotine/networkfilter.py:256
msgid "French Southern Territories"
msgstr "Французькі Південні Території"

#: pynicotine/networkfilter.py:257
msgid "Togo"
msgstr "Того"

#: pynicotine/networkfilter.py:258
msgid "Thailand"
msgstr "Таїланд"

#: pynicotine/networkfilter.py:259
msgid "Tajikistan"
msgstr "Таджикистан"

#: pynicotine/networkfilter.py:260
msgid "Tokelau"
msgstr "Токелау"

#: pynicotine/networkfilter.py:261
msgid "Timor-Leste"
msgstr "Тимор-Лешті"

#: pynicotine/networkfilter.py:262
msgid "Turkmenistan"
msgstr "Туркменістан"

#: pynicotine/networkfilter.py:263
msgid "Tunisia"
msgstr "Туніс"

#: pynicotine/networkfilter.py:264
msgid "Tonga"
msgstr "Тонга"

#: pynicotine/networkfilter.py:265
#, fuzzy
msgid "Türkiye"
msgstr "Туреччина"

#: pynicotine/networkfilter.py:266
msgid "Trinidad & Tobago"
msgstr "Тринідад і Тобаго"

#: pynicotine/networkfilter.py:267
msgid "Tuvalu"
msgstr "Тувалу"

#: pynicotine/networkfilter.py:268
msgid "Taiwan"
msgstr "Тайвань"

#: pynicotine/networkfilter.py:269
msgid "Tanzania"
msgstr "Танзанія"

#: pynicotine/networkfilter.py:270
msgid "Ukraine"
msgstr "Україна"

#: pynicotine/networkfilter.py:271
msgid "Uganda"
msgstr "Уганда"

#: pynicotine/networkfilter.py:272
msgid "U.S. Minor Outlying Islands"
msgstr "Малі віддалені острови США"

#: pynicotine/networkfilter.py:273
msgid "United States"
msgstr "Сполучені Штати Америки"

#: pynicotine/networkfilter.py:274
msgid "Uruguay"
msgstr "Уругвай"

#: pynicotine/networkfilter.py:275
msgid "Uzbekistan"
msgstr "Узбекистан"

#: pynicotine/networkfilter.py:276
msgid "Holy See (Vatican City State)"
msgstr "Ватикан"

#: pynicotine/networkfilter.py:277
msgid "Saint Vincent & The Grenadines"
msgstr "Сент-Вінсент і Гренадіни"

#: pynicotine/networkfilter.py:278
msgid "Venezuela"
msgstr "Венесуела"

#: pynicotine/networkfilter.py:279
msgid "British Virgin Islands"
msgstr "Британські Віргінські острови"

#: pynicotine/networkfilter.py:280
msgid "U.S. Virgin Islands"
msgstr "Віргінські острови США"

#: pynicotine/networkfilter.py:281
msgid "Viet Nam"
msgstr "В'єтнам"

#: pynicotine/networkfilter.py:282
msgid "Vanuatu"
msgstr "Вануату"

#: pynicotine/networkfilter.py:283
msgid "Wallis & Futuna"
msgstr "Уолліс і Футуна"

#: pynicotine/networkfilter.py:284
msgid "Samoa"
msgstr "Самоа"

#: pynicotine/networkfilter.py:285
msgid "Yemen"
msgstr "Ємен"

#: pynicotine/networkfilter.py:286
msgid "Mayotte"
msgstr "Майотта"

#: pynicotine/networkfilter.py:287
msgid "South Africa"
msgstr "Південна Африка"

#: pynicotine/networkfilter.py:288
msgid "Zambia"
msgstr "Замбія"

#: pynicotine/networkfilter.py:289
msgid "Zimbabwe"
msgstr "Зімбабве"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "Помилка синтезу мовлення для повідомлення: %s"

#: pynicotine/nowplaying.py:130
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr "Last.fm: будь ласка, надайте своє ім’я користувача Last.fm і ключ API"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
#, fuzzy
msgid "Now Playing Error"
msgstr "Помилка \"Зараз відтворюється\""

#: pynicotine/nowplaying.py:140
#, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm: не вдалося підключитися до Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:162
#, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr ""
"Last.fm: не вдалося отримати останній трек від Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:196
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: Не вдалося знайти відповідний програвач MPRIS"

#: pynicotine/nowplaying.py:201
#, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr ""
"Знайдено кілька програвачів MPRIS: %(players)s. Використання: %(player)s"

#: pynicotine/nowplaying.py:204
#, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Автовизначений програвач MPRIS: %s"

#: pynicotine/nowplaying.py:219
#, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr "MPRIS: Сталася помилка під час запиту %(player)s: %(exception)s"

#: pynicotine/nowplaying.py:266
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "ListenBrainz: будь ласка, вкажіть своє ім’я користувача ListenBrainz"

#: pynicotine/nowplaying.py:275
#, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "ListenBrainz: не вдалося підключитися до ListenBrainz: %(error)s"

#: pynicotine/nowplaying.py:283
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "ListenBrainz: Ви, здається, зараз нічого не слухаєте"

#: pynicotine/nowplaying.py:297
#, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz: не вдалося отримати поточний трек від ListenBrainz: %(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
#, fuzzy
msgid "Network Filters"
msgstr "Пошуки в мережі"

#: pynicotine/plugins/core_commands/__init__.py:44
#, fuzzy
msgid "List available commands"
msgstr "Вбудовані команди"

#: pynicotine/plugins/core_commands/__init__.py:49
#, fuzzy
msgid "Connect to the server"
msgstr "Не вдається підключитися до сервера. Причина: %s"

#: pynicotine/plugins/core_commands/__init__.py:53
#, fuzzy
msgid "Disconnect from the server"
msgstr "Відключено від сервера %(host)s: %(port)s"

#: pynicotine/plugins/core_commands/__init__.py:58
#, fuzzy
msgid "Toggle away status"
msgstr "Перемикає ваш статус відсутності"

#: pynicotine/plugins/core_commands/__init__.py:62
#, fuzzy
msgid "Manage plugins"
msgstr "Увімкнути плагіни"

#: pynicotine/plugins/core_commands/__init__.py:74
#, fuzzy
msgid "Clear chat window"
msgstr "Очистіть вікно чату"

#: pynicotine/plugins/core_commands/__init__.py:80
msgid "Say something in the third-person"
msgstr "Скажіть щось від третьої особи"

#: pynicotine/plugins/core_commands/__init__.py:87
#, fuzzy
msgid "Announce the song currently playing"
msgstr "Оголосити пісню, яка зараз грає"

#: pynicotine/plugins/core_commands/__init__.py:94
#, fuzzy
msgid "Join chat room"
msgstr "Приєднатися до кімнати"

#: pynicotine/plugins/core_commands/__init__.py:102
#, fuzzy
msgid "Leave chat room"
msgstr "Залишити поточну кімнату"

#: pynicotine/plugins/core_commands/__init__.py:110
#, fuzzy
msgid "Say message in specified chat room"
msgstr "Скажіть повідомлення у вказаній кімнаті чату"

#: pynicotine/plugins/core_commands/__init__.py:117
#, fuzzy
msgid "Open private chat"
msgstr "Приватний чат"

#: pynicotine/plugins/core_commands/__init__.py:125
#, fuzzy
msgid "Close private chat"
msgstr "Закрийте поточний приватний чат"

#: pynicotine/plugins/core_commands/__init__.py:133
#, fuzzy
msgid "Request user's client version"
msgstr "Запит інформації про користувача"

#: pynicotine/plugins/core_commands/__init__.py:142
#, fuzzy
msgid "Send private message to user"
msgstr "Надіслати приватне повідомлення користувачу"

#: pynicotine/plugins/core_commands/__init__.py:150
#, fuzzy
msgid "Add user to buddy list"
msgstr "Додати користувача до свого списку друзів"

#: pynicotine/plugins/core_commands/__init__.py:158
#, fuzzy
msgid "Remove buddy from buddy list"
msgstr "Видалити користувача зі свого списку друзів"

#: pynicotine/plugins/core_commands/__init__.py:166
#, fuzzy
msgid "Browse files of user"
msgstr "Перегляд файлів користувача 'user'"

#: pynicotine/plugins/core_commands/__init__.py:175
#, fuzzy
msgid "Show user profile information"
msgstr "Показати інформацію профілю користувача"

#: pynicotine/plugins/core_commands/__init__.py:183
#, fuzzy
msgid "Show IP address or username"
msgstr "Показати IP користувача"

#: pynicotine/plugins/core_commands/__init__.py:190
#, fuzzy
msgid "Block connections from user or IP address"
msgstr "Блокувати підключення від користувача або IP-адреси"

#: pynicotine/plugins/core_commands/__init__.py:197
#, fuzzy
msgid "Remove user or IP address from ban lists"
msgstr "Видалити користувача зі списку блокувань"

#: pynicotine/plugins/core_commands/__init__.py:204
#, fuzzy
msgid "Silence messages from user or IP address"
msgstr "Вимкнення повідомлень від користувача або IP-адреси"

#: pynicotine/plugins/core_commands/__init__.py:212
#, fuzzy
msgid "Remove user or IP address from ignore lists"
msgstr "Видалити користувача зі списку ігнорування"

#: pynicotine/plugins/core_commands/__init__.py:220
#, fuzzy
msgid "Add share"
msgstr "Додати _бажання"

#: pynicotine/plugins/core_commands/__init__.py:226
#, fuzzy
msgid "Remove share"
msgstr "Видалити б_ажання"

#: pynicotine/plugins/core_commands/__init__.py:233
#, fuzzy
msgid "List shares"
msgstr "Пересканування спільних каталогів"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Пересканування спільних каталогів"

#: pynicotine/plugins/core_commands/__init__.py:246
#, fuzzy
msgid "Start global file search"
msgstr "Запустіть глобальний пошук файлів"

#: pynicotine/plugins/core_commands/__init__.py:254
#, fuzzy
msgid "Search files in joined rooms"
msgstr "Пошук файлів і каталоргів (точна відповідність)"

#: pynicotine/plugins/core_commands/__init__.py:262
#, fuzzy
msgid "Search files of all buddies"
msgstr "Пошук файлів і каталоргів (точна відповідність)"

#: pynicotine/plugins/core_commands/__init__.py:270
#, fuzzy
msgid "Search a user's shared files"
msgstr "Шукати в спільних ресурсах користувача за \"запитом\""

#: pynicotine/plugins/core_commands/__init__.py:296
#, fuzzy, python-format
msgid "Listing %(num)i available commands:"
msgstr "Список %(num)i доступних команд:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, fuzzy, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr "Список %(num)i доступних команд, що відповідають \"%(query)s\":"

#: pynicotine/plugins/core_commands/__init__.py:311
#, fuzzy, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Введіть %(command)s, щоб отримати список подібних команд"

#: pynicotine/plugins/core_commands/__init__.py:314
#, fuzzy, python-format
msgid "Type %(command)s to list available commands"
msgstr "Введіть %(command)s, щоб отримати список доступних команд"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, fuzzy, python-format
msgid "Not joined in room %s"
msgstr "%s приєднався до кімнати"

#: pynicotine/plugins/core_commands/__init__.py:404
#, fuzzy, python-format
msgid "Not messaging with user %s"
msgstr "Не ведеться листування з користувачем %s"

#: pynicotine/plugins/core_commands/__init__.py:408
#, fuzzy, python-format
msgid "Closed private chat of user %s"
msgstr "Закрийте поточний приватний чат"

#: pynicotine/plugins/core_commands/__init__.py:476
#, fuzzy, python-format
msgid "Banned %s"
msgstr "Заблоковано"

#: pynicotine/plugins/core_commands/__init__.py:490
#, fuzzy, python-format
msgid "Unbanned %s"
msgstr "Блокувати користувача"

#: pynicotine/plugins/core_commands/__init__.py:503
#, fuzzy, python-format
msgid "Ignored %s"
msgstr "Ігноровані користувачі"

#: pynicotine/plugins/core_commands/__init__.py:517
#, fuzzy, python-format
msgid "Unignored %s"
msgstr "Ігнорувати користувача"

#: pynicotine/plugins/core_commands/__init__.py:553
#, fuzzy, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr "%(num_listed)s обмінів в списку (%(num_total)s налаштовано)"

#: pynicotine/plugins/core_commands/__init__.py:565
#, fuzzy, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr "Не можу обмінятися недоступною папкою \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:568
#, fuzzy, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""
"Додано %(group_name)s обмін \"%(virtual_name)s\" (потрібне повторне "
"сканування)"

#: pynicotine/plugins/core_commands/__init__.py:579
#, fuzzy, python-format
msgid "No share with name \"%s\""
msgstr "Немає обміну з назвою \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:582
#, fuzzy, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr "Видалено обмін \"%s\" (потрібне повторне сканування)"

#: pynicotine/pluginsystem.py:413
msgid "Loading plugin system"
msgstr "Завантаження системи плагінів"

#: pynicotine/pluginsystem.py:516
#, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Не вдається завантажити плагін %(name)s. Назва каталога плагіна містить "
"недійсні символи: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, fuzzy, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "Конфліктна команда %(interface)s у плагіні %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, python-format
msgid "Loaded plugin %s"
msgstr "Завантажений плагін %s"

#: pynicotine/pluginsystem.py:579
#, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Не вдається завантажити плагін %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, python-format
msgid "Unloaded plugin %s"
msgstr "Вивантажений плагін %s"

#: pynicotine/pluginsystem.py:648
#, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Не вдається вивантажити плагін %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Сбій плагіна %(module)s з помилкою %(errortype)s: %(error)s.\n"
"Отчет: %(trace)s"

#: pynicotine/pluginsystem.py:810
#, fuzzy
msgid "No description"
msgstr "Самоопис"

#: pynicotine/pluginsystem.py:887
#, fuzzy, python-format
msgid "Missing %s argument"
msgstr "Відсутній аргумент %s."

#: pynicotine/pluginsystem.py:896
#, fuzzy, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Недійсна відповідь: %s"

#: pynicotine/pluginsystem.py:901
#, fuzzy, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Використання: %(command)s %(args)s"

#: pynicotine/pluginsystem.py:940
#, fuzzy, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Невідома команда: %(command)s. Введіть %(help_command)s, щоб отримати список "
"доступних команд."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
#, fuzzy
msgid "No UPnP devices found"
msgstr "Пристроїв UPnP не знайдено"

#: pynicotine/portmapper.py:633
#, fuzzy, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr "UPnP: не вдалося переслати зовнішній порт %(external_port)s: %(error)s"

#: pynicotine/portmapper.py:647
#, fuzzy, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"UPnP: зовнішній порт %(external_port)s успішно перенаправлено на локальну IP-"
"адресу %(ip_address)s порт %(local_port)s"

#: pynicotine/privatechat.py:220
#, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Приватне повідомлення від користувача \"%(user)s\": %(message)s"

#: pynicotine/search.py:368
#, fuzzy, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Пошук елемента списку бажань \"%s\""

#: pynicotine/search.py:434
#, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Період очікування списку бажань встановлено на %s секунд"

#: pynicotine/search.py:760
#, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr "Користувач %(user)s шукає \"%(query)s\", знайдено %(num)i результатів"

#: pynicotine/shares.py:302
#, fuzzy
msgid "Rebuilding shares…"
msgstr "Пересканування спільних каталогів…"

#: pynicotine/shares.py:302
msgid "Rescanning shares…"
msgstr "Пересканування спільних каталогів…"

#: pynicotine/shares.py:324
#, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Пересканування завершено: знайдено %(num)s каталог(-ів)"

#: pynicotine/shares.py:334
#, fuzzy, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Під час пересканування спільних ресурсів сталася серйозна помилка. Якщо "
"проблема не зникне, видаліть %(dir)s/*.db і повторіть спробу. Якщо це не "
"допомогло, надішліть звіт про помилку з включеною трасуванням стека: "
"%(trace)s"

#: pynicotine/shares.py:582
#, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Помилка під час сканування файлу %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Помилка під час сканування каталога %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "Помилка під час сканування метаданих для файлу %(path)s: %(error)s"

#: pynicotine/shares.py:1046
#, fuzzy, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Повторне сканування перервано через недоступність спільних ресурсів"

#: pynicotine/shares.py:1184
#, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "Користувач %(user)s переглядає ваш список спільних файлів"

#: pynicotine/slskmessages.py:3120
#, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Неможливо прочитати базу даних спільних ресурсів. Будь ласка, перескануйте "
"спільні ресурси. Помилка: %s"

#: pynicotine/slskproto.py:500
#, fuzzy, python-format
msgid "Specified network interface '%s' is not available"
msgstr "Зазначений мережевий інтерфейс \"%s\" не існує"

#: pynicotine/slskproto.py:511
#, fuzzy, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Не вдається прослухати через порт %(port)s. Переконайтеся, що жодна інша "
"програма не використовує його, або виберіть інший порт. Помилка: %(error)s"

#: pynicotine/slskproto.py:523
#, python-format
msgid "Listening on port: %i"
msgstr "Прослуховування через порт: %i"

#: pynicotine/slskproto.py:805
#, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "Не вдається підключитися до сервера %(host)s:%(port)s: %(error)s"

#: pynicotine/slskproto.py:1095
#, fuzzy, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Автоматичне підключення до сервера під час запуску"

#: pynicotine/slskproto.py:1170
#, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "З'єднання з %(host)s: %(port)s"

#: pynicotine/slskproto.py:1208
#, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Підключено до сервера %(host)s: %(port)s, вхід…"

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Відключено від сервера %(host)s: %(port)s"

#: pynicotine/slskproto.py:1499
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Хтось увійшов у ваш обліковий запис Soulseek в іншому місці"

#: pynicotine/uploads.py:382
#, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Вивантаження завершено: користувач %(user)s, IP-адреса %(ip)s, файл %(file)s"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Вивантаження перервано, користувач %(user)s файл %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, python-format
msgid "Upload I/O error: %s"
msgstr "Помилка вивантаження введення-виводу: %s"

#: pynicotine/uploads.py:1103
#, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Вивантаження розпочато: користувач %(user)s, IP-адреса %(ip)s, файл %(file)s"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr "Не вдається створити каталог \"%(folder)s\", помилка: %(error)s"

#: pynicotine/userbrowse.py:236
#, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Збій завантаження спільних ресурсів з диска: %(error)s"

#: pynicotine/userbrowse.py:282
#, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr ""
"Список спільних файлів для користувача \"%(user)s\" збережено на %(dir)s"

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr ""
"Не вдається зберегти спільні ресурси, \"%(user)s\", повідомляється про "
"помилку: %(error)s"

#: pynicotine/userinfo.py:160
#, python-format
msgid "Picture saved to %s"
msgstr "Зображення збережено у %s"

#: pynicotine/userinfo.py:163
#, fuzzy, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Не вдається зберегти %(filename)s: %(error)s"

#: pynicotine/userinfo.py:190
#, fuzzy, python-format
msgid "User %(user)s is viewing your profile"
msgstr "Користувач %(user)s читає вашу інформацію про користувача"

#: pynicotine/users.py:272
#, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Не вдається підключитися до сервера. Причина: %s"

#: pynicotine/users.py:272
msgid "Cannot Connect"
msgstr "Не вдається підключитися"

#: pynicotine/users.py:306
#, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr ""
"Не вдається отримати IP-адресу користувача %s, оскільки цей користувач не в "
"мережі"

#: pynicotine/users.py:315
#, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr "IP-адреса користувача %(user)s: %(ip)s, порт %(port)i%(country)s"

#: pynicotine/users.py:433
#, fuzzy
msgid "Soulseek Announcement"
msgstr "Оголошення Soulseek"

#: pynicotine/users.py:449
#, fuzzy
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"У вас немає привілеїв. Привілеї не є обов’язковими, але дозволяють ставати "
"завантаженням в чергу перед непривілейованими користувачами."

#: pynicotine/users.py:455
#, fuzzy, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"Привелеїв на завантаження залишилося %(days)i днів, %(hours)i годин, "
"%(minutes)i хвилин, %(seconds)i секунд."

#: pynicotine/users.py:473
#, fuzzy
msgid "Your password has been changed"
msgstr "Ваш пароль був змінений. Пароль %s"

#: pynicotine/users.py:473
#, fuzzy
msgid "Password Changed"
msgstr "Зміна пароля відхилена"

#: pynicotine/utils.py:574
#, fuzzy, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Не вдається зберегти файл %(path)s: %(error)s"

#: pynicotine/utils.py:621
#, fuzzy, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "Не вдається зберегти %(filename)s: %(error)s"

#: pynicotine/utils.py:646
#, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr "Під час читання файлу %(filename)s сталася помилка: %(error)s"

#: pynicotine/utils.py:651
#, python-format
msgid "Attempting to load backup of file %s"
msgstr "Спроба завантажити резервну копію файлу %s"

#: pynicotine/utils.py:673
#, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Не вдається створити резервну копію файлу %(path)s: %(error)s"

#: pynicotine/utils.py:694
#, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Не вдається зберегти файл %(path)s: %(error)s"

#: pynicotine/utils.py:705
#, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Не вдається відновити попередній файл %(path)s: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
msgid "Add buddy…"
msgstr "Додати друга…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
msgid "Toggle Text-to-Speech"
msgstr "Перемкнути режиму синтезу мовлення"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
#, fuzzy
msgid "Chat Room Command Help"
msgstr "Довідка команди чат-кімнати"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
msgid "_Log"
msgstr "_Журнал"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
#, fuzzy
msgid "Room Wall"
msgstr "Стіна кімнати"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
#, fuzzy
msgid "R_oom Wall"
msgstr "Стіна кімнати"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
#, fuzzy
msgid "Created by"
msgstr "Створений"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
#, fuzzy
msgid "Translated by"
msgstr "Переклав"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
#, fuzzy
msgid "License"
msgstr "Ліцензія"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
msgid "Welcome to Nicotine+"
msgstr "Ласкаво просимо до Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Якщо потрібне ім’я користувача вже зайнято, вам буде запропоновано його "
"змінити."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
#, fuzzy
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""
"Щоб підключитися до інших користувачів Soulseek, на вашому роутері потрібно "
"налаштувати перенаправлення порту на ваш комп'ютер."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Якщо ваш порт прослуховування закритий, ви зможете підключатися лише до тих "
"користувачів, чиї порти прослуховування відкриті."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
#, fuzzy
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"За потреби виберіть інший порт прослуховування нижче. Це також можна зробити "
"пізніше в налаштуваннях."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
msgid "Download Files to Folder"
msgstr "Завантажити файли в каталог"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
msgid "Share Folders"
msgstr "Спільні каталоги"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
#, fuzzy
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Користувачі Soulseek зможуть завантажувати з ваших спільних ресурсів. Робіть "
"внесок у мережу Soulseek, ділячись своєю колекцією та повторно ділячись тим, "
"що ви завантажили від інших користувачів."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
msgid "You are ready to use Nicotine+!"
msgstr "Ви готові до використання Nicotine+!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
#, fuzzy
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""
"Soulseek - це незашифрований протокол, який не призначений для захищеного "
"спілкування."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Пожертвування на Soulseek надає вам привілеї на певний період часу. Якщо у "
"вас є привілеї, ваші завантаження будуть стояти в черзі перед "
"непривілейованими користувачами."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
#, fuzzy
msgid "Previous File"
msgstr "Попередній файл"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
#, fuzzy
msgid "Next File"
msgstr "Наступний файл"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
msgid "Name"
msgstr "Ім'я"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
msgid "Last Speed"
msgstr "Остання швидкість"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
msgid "_Export…"
msgstr "_Експорт…"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
msgid "Keyboard Shortcuts"
msgstr "Гарячі клавіши"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
msgid "General"
msgstr "Загальне"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr "З'єднатися"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr "Від’єднатися"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
msgid "Rescan Shares"
msgstr "Пересканування спільних каталогів"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
msgid "Show Log Pane"
msgstr "Показати панель журналу"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
#, fuzzy
msgid "Confirm Quit"
msgstr "Налаштувати чати"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
msgid "Quit"
msgstr "Вийти"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
msgid "Menus"
msgstr "Меню"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
msgid "Open Main Menu"
msgstr "Відкрити Головне меню"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
msgid "Open Context Menu"
msgstr "Відкрити контекстне меню"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
msgid "Tabs"
msgstr "Вкладинки"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
msgid "Change Main Tab"
msgstr "Змінити головну вкладку"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
msgid "Go to Previous Secondary Tab"
msgstr "Перейти на попередню додаткову вкладку"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
msgid "Go to Next Secondary Tab"
msgstr "Перейдіть на наступну додаткову вкладку"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
#, fuzzy
msgid "Reopen Closed Secondary Tab"
msgstr "Закрити додаткову вкладку"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
msgid "Close Secondary Tab"
msgstr "Закрити додаткову вкладку"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
msgid "Lists"
msgstr "Списки"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
msgid "Copy Selected Cell"
msgstr "Копіювати вибрану клітинку"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
msgid "Select All"
msgstr "Вибрати все"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
msgid "Find"
msgstr "Знайти"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
msgid "Remove Selected Row"
msgstr "Видалити виділений рядок"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
msgid "Editing"
msgstr "Редагувати"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
msgid "Cut"
msgstr "Вирізати"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
msgid "Paste"
msgstr "Вставити"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
msgid "Insert Emoji"
msgstr "Вставити Emoji"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
msgid "File Transfers"
msgstr "Передача файлів"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
msgid "Resume / Retry Transfer"
msgstr "Відновити / Повторити передачу"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
msgid "Pause / Abort Transfer"
msgstr "Призупинення / Переривання передачі"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
msgid "Download / Upload To"
msgstr "Завантажити / Вивантажити до"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
msgid "Save List to Disk"
msgstr "Зберегти список на диск"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
msgid "Refresh"
msgstr "Оновити"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
msgid "Expand / Collapse All"
msgstr "Розгорнути/Згорнути все"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
msgid "Back to Parent Folder"
msgstr "Назад до батьківського каталога"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
msgid "File Search"
msgstr "Пошук файлів"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
msgid "Result Filters"
msgstr "Фільтри результатів"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
msgid "Current Session"
msgstr "Поточна сесія"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
msgid "Completed Downloads"
msgstr "Завершено завантаження"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
msgid "Downloaded Size"
msgstr "Завантажений розмір"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
msgid "Completed Uploads"
msgstr "Завершені вивантаження"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
msgid "Uploaded Size"
msgstr "Вивантажений розмір"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
msgid "Total"
msgstr "Всього"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
msgid "_Reset…"
msgstr "_Скинути…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Елементи списку бажань автоматично шукаються через регулярні проміжки часу "
"для виявлення файлів."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
msgid "Add Wish…"
msgstr "Додати бажання…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
msgid "Clear All…"
msgstr "Очистити все…"

#: pynicotine/gtkgui/ui/downloads.ui:138
#, fuzzy
msgid "Clear All Finished/Filtered Downloads"
msgstr "Очистити всі завершені та відфільтровані завантаження."

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
msgid "Clear Finished"
msgstr "Очистити завершені"

#: pynicotine/gtkgui/ui/downloads.ui:169
#, fuzzy
msgid "Clear Specific Downloads"
msgstr "Очистити всі завантаження"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
msgid "Clear _All…"
msgstr "Очистити _все…"

#: pynicotine/gtkgui/ui/interests.ui:21
msgid "Personal Interests"
msgstr "Особисті інтереси"

#: pynicotine/gtkgui/ui/interests.ui:40
msgid "Add something you like…"
msgstr "Додайте те, що вам подобається…"

#: pynicotine/gtkgui/ui/interests.ui:62
msgid "Personal Dislikes"
msgstr "Особисті антипатії"

#: pynicotine/gtkgui/ui/interests.ui:80
msgid "Add something you dislike…"
msgstr "Додайте те, що вам не подобається…"

#: pynicotine/gtkgui/ui/interests.ui:143
#, fuzzy
msgid "Refresh Recommendations"
msgstr "Оновити список рекомендацій"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
#, fuzzy
msgid "Main Menu"
msgstr "Відкрити Головне меню"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
msgid "Room…"
msgstr "Кімната…"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
msgid "Username…"
msgstr "Ім'я користувача…"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
msgid "Search term…"
msgstr "Пошуковий термін…"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Очистити"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Шаблони пошуку: зі словом = слово, без слова = -слово, часткове слово = *ово"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
#, fuzzy
msgid "Search Scope"
msgstr "Сфера пошуку"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
msgid "_Wishlist"
msgstr "_Список бажань"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
#, fuzzy
msgid "Configure Searches"
msgstr "Налаштувати пошук"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Введіть пошуковий термін, щоб шукати файли, які зробили спільними інші "
"користувачі в мережі Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
#, fuzzy
msgid "File Grouping Mode"
msgstr "Режим групування файлів"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
#, fuzzy
msgid "Configure Downloads"
msgstr "Налаштувати завантаження"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Файли, які ви завантажуєте від інших користувачів, знаходяться тут у черзі, "
"і їх можна призупинити та відновити за запитом"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
#, fuzzy
msgid "Configure Uploads"
msgstr "Налаштувати вивантаження"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Спроби користувачів завантажити ваші спільні файли стоять у черзі й "
"керуються тут"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
msgid "_Open List"
msgstr "_Відкрити список"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Відкриває локальний список спільних файлів, які раніше були збережені на "
"диску"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
#, fuzzy
msgid "Configure Shares"
msgstr "_Налаштувати спільні каталоги"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Введіть ім’я користувача, чиї спільні файли ви хочете переглянути. Ви також "
"можете зберегти список на диск і перевірити його пізніше."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
#, fuzzy
msgid "_Personal Profile"
msgstr "Особисті антипатії"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
#, fuzzy
msgid "Configure Account"
msgstr "Налаштувати чати"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Введіть ім’я користувача, щоб переглянути його опис, інформацію та особисте "
"зображення"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
msgid "Chat _History"
msgstr "_Історія чату"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
#, fuzzy
msgid "Configure Chats"
msgstr "Налаштувати чати"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr "Введіть ім’я користувача, щоб почати текстову розмову з ним"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
#, fuzzy
msgid "_Message All"
msgstr "Повідомлення (лоток)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
#, fuzzy
msgid "Configure Ignored Users"
msgstr "Ігноровані користувачі"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Додайте користувачів як друзів, щоб ділитися з ними певними каталогами та "
"отримувати сповіщення, коли вони в мережі"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
#, fuzzy
msgid "Join or create room…"
msgstr "Приєднайтеся або створіть кімнату…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Приєднайтеся до наявної кімнати чату або створіть нову кімнату для "
"спілкування з іншими користувачами мережі Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
#, fuzzy
msgid "Configure User Profile"
msgstr "Переглянути профіль користувача"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
msgid "Connections"
msgstr "З'єднання"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
#, fuzzy
msgid "Downloading (Speed / Active Users)"
msgstr "Завантаження (швидкість / активні користувачі)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
#, fuzzy
msgid "Uploading (Speed / Active Users)"
msgstr "Вивантаження (швидкість / активні користувачі)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
#, fuzzy
msgid "Search chat history…"
msgstr "Пошуковий термін…"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
msgid "Download Speed Limits"
msgstr "Обмеження швидкості завантаження"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
#, fuzzy
msgid "Unlimited download speed"
msgstr "Необмежена швидкість завантаження"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
#, fuzzy
msgid "Use download speed limit (KiB/s):"
msgstr "Альтернативне обмеження швидкості завантаження (КБ/с):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
#, fuzzy
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Альтернативне обмеження швидкості завантаження (КБ/с):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
#, fuzzy
msgid "Search rooms…"
msgstr "Пошуковий термін…"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
#, fuzzy
msgid "Refresh Rooms"
msgstr "Оновити список кімнат"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
msgid "_Show feed of public chat room messages"
msgstr "_Показати стрічку повідомлень публічної кімнати чату"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
msgid "_Accept private room invitations"
msgstr "_Прийняти запрошення в приватну кімнату"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
#, fuzzy
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Функція стіни кімнати дозволяє користувачам у кімнаті показати іншим "
"унікальне повідомлення. Останні повідомлення відображаються вгорі."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
msgid "Set wall message…"
msgstr "Встановити повідомлення на стіні…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
msgid "Search Result Filters"
msgstr "Фільтри результатів пошуку"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Фільтри результатів пошуку використовуються, щоб уточнити, які результати "
"пошуку відображаються."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
#, fuzzy
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Кожен список результатів пошуку має власний фільтр, який можна відкрити, "
"натиснувши кнопку «Фільтри результатів». Фільтр складається з кількох полів, "
"усі з яких застосовуються при натисканні клавіші Enter у будь-якому з полів. "
"Фільтрація застосовується негайно до вже отриманих результатів, а також до "
"тих, які ще не надійшли. Щоб знову переглянути повні результати, просто "
"очистіть усі фільтри і повторно застосуйте його. Як випливає з назви, фільтр "
"результатів пошуку не може розширити початковий пошук, він може лише звузити "
"його. Щоб розширити або змінити пошукові терміни, виконайте новий пошук."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
#, fuzzy
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Як випливає з назви, фільтр результатів пошуку не може розширити початковий "
"пошук, він може лише звузити його. Щоб розширити або змінити терміни пошуку, "
"виконайте новий пошук."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
#, fuzzy
msgid "Result Filter Usage"
msgstr "Фільтри результатів"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
msgid "Include Text"
msgstr "Включає текст"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
#, fuzzy
msgid "Files, folders and usernames containing this text will be shown."
msgstr "Будуть показані файли та каталоги, що містять цей текст."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
#, fuzzy
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"До регістру нечутливе, але порядок слів важливий: \"Спірс Брітні\" не "
"показуватиме \"Брітні Спірс\""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
#, fuzzy
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Використовуйте | (або труби), щоб відокремити кілька точних фраз. приклад:\n"
"    Ремікс|Дуб Мікс|Інструментал"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
msgid "Exclude Text"
msgstr "Не включає текст"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
#, fuzzy
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"Як і вище, але файли та каталоги відфільтровуються, якщо текст збігається."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
msgid "Filters files based upon their file extension."
msgstr "Фільтрує файли на основі їх розширення."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
#, fuzzy
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Можна вказати кілька розширень файлів, що, у свою чергу, розширить список "
"результатів.\n"
"    Приклад: flac|wav|ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
#, fuzzy
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"Також можна інвертувати фільтр, вказуючи розширення файлів, які ви не хочете "
"бачити в результатах.\n"
"    Приклад: !mp3|!jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
msgid "File Size"
msgstr "Розмір файлу"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
msgid "Filters files based upon their file size."
msgstr "Фільтрує файли на основі їх розміру."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
#, fuzzy
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"За замовчуванням використовуваною одиницею є байти, і файли, що перевищують "
"або дорівнюють вказаному значенню."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
#, fuzzy
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Додайте b, k, m або g (альтернативно kib, mib або gib), щоб вказати байт, "
"кібібайт, мебібайт або гібібайт:\n"
"    <1024k знайде файли розміром 1024 кібібайт (тобто 1 мебібайт) або менше."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
#, fuzzy
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Додайте знак \"=\" до значення, щоб указати точну відповідність:\n"
"    =1024 відповідає лише файлам розміром 1024 байти (тобто 1 кібібайт)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
#, fuzzy
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Додайте знак \"=\" до значення, щоб указати точну відповідність:\n"
"    =1024 відповідає лише файлам розміром 1024 байти (тобто 1 кібібайт)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Додайте < або > перед значенням, щоб знайти файли менші/більші за вказане "
"значення. Використовуйте пробіл між умовами, щоб включити діапазон:\n"
"    >10.5m <1g щоб показати файли більші за 10.5 МіБ, але менші за 1 ГіБ."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
#, fuzzy
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Для зручності також можна використовувати варіанти kb, mb і gb для відоміших "
"одиниць кіло-, мега- та гігабайт."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
msgid "Filters files based upon their bitrate."
msgstr "Фільтрує файли на основі їх бітрейту."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
#, fuzzy
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Значення слід вводити лише як цифри. Одиницею завжди є Кбіт/с (кілобіт на "
"секунду)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Як і Розмір файлу (вище), можна використовувати оператори =, !, <, >, <= або "
">=, та вказувати кілька умов, наприклад, щоб показати файли з бітрейтом "
"щонайменше 256 Кб/с із максимальним бітрейтом 1411 Кб/с:\n"
"    256 <=1411"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
#, fuzzy
msgid "Filters files based upon their duration."
msgstr "Фільтрує файли на основі їх бітрейту."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
#, fuzzy
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"За замовчуванням буде зіставлено файли, тривалість яких перевищує або "
"дорівнює введеній тривалості, якщо не використовується оператор (=, !, < або "
">)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Введіть значення у секундах або використовуйте формати часу MM:SS та HH:MM:"
"SS:\n"
"    =53 показує файли, тривалість яких приблизно 53 секунди.\n"
"    >5:30, щоб показати файли тривалістю понад 5 з половиною хвилин.\n"
"    <5:30:00 показує файли, які тривають менше ніж 5 з половиною годин."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
#, fuzzy
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Кілька умов можна вказати за допомогою | роздільники труб:\n"
"    >6:00|<12:00, щоб показати файли тривалістю від 6 до 12 хвилин.\n"
"    !9:54|!8:43|!7:32, щоб приховати певні файли з результатів.\n"
"    =5:34|=4:23|=3:05, щоб включити файли з певною тривалістю."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Фільтрує файли на основі географічного розташування користувачів згідно з "
"кодами країн, визначеними ISO 3166-2:\n"
"    US покаже результати тільки від користувачів з ip-адресами у Сполучених "
"Штатах.\n"
"    !GB приховає результати, які надходять від користувачів у Великій "
"Британії."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
#, fuzzy
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Кілька країн можна вказати через кому або пробіли."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
msgid "Free Slot"
msgstr "Вільний слот"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
#, fuzzy
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Показувати лише ті результати від користувачів, у яких є принаймні один "
"вільний слот для вивантаження. Цей фільтр застосовується негайно."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
msgid "Upload Speed Limits"
msgstr "Обмеження швидкості вивантаження"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
#, fuzzy
msgid "Unlimited upload speed"
msgstr "Необмежена швидкість вивантаження"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
#, fuzzy
msgid "Use upload speed limit (KiB/s):"
msgstr "Альтернативне обмеження швидкості вивантаження (КіБ/с):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
#, fuzzy
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Альтернативне обмеження швидкості вивантаження (КіБ/с):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
#, fuzzy
msgid "Private Chat Command Help"
msgstr "Довідка команди приватного чату"

#: pynicotine/gtkgui/ui/search.ui:7
msgid "Include text…"
msgstr "Включає текст…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Включити до результатів, шляхи до файлів яких містять вказаний текст. Можна "
"вказати кілька фраз і слів, наприклад точна фраза|музика|термін|точна фраза "
"друга"

#: pynicotine/gtkgui/ui/search.ui:18
msgid "Exclude text…"
msgstr "Не включає текст…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Виключити з результатів, шляхи до файлів яких містять вказаний текст. Можна "
"вказати кілька фраз і слів, наприклад точна фраза|музика|термін|точна фраза "
"друга"

#: pynicotine/gtkgui/ui/search.ui:29
msgid "File type…"
msgstr "Тип файлу…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
#, fuzzy
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Тип файлу, напр. flac|wav|ape або !mp3|!m4a"

#: pynicotine/gtkgui/ui/search.ui:40
msgid "File size…"
msgstr "Розмір файлу…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
#, fuzzy
msgid "File size, e.g. >10.5m <1g"
msgstr "Розмір файлу, напр. >10,5м <1г"

#: pynicotine/gtkgui/ui/search.ui:51
msgid "Bitrate…"
msgstr "Бітрейт…"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
#, fuzzy
msgid "Bitrate, e.g. 256 <1412"
msgstr "Бітрейт, напр. 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
#, fuzzy
msgid "Duration…"
msgstr "Тривалість…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
#, fuzzy
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Тривалість, напр. 2:20|!3:30|=4:40"

#: pynicotine/gtkgui/ui/search.ui:73
msgid "Country code…"
msgstr "Код країни…"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
#, fuzzy
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Код країни, напр. US|GB|ES або !DE|!GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Заборонити користувачам доступ до ваших спільних файлів на основі імені "
"користувача, IP-адреси або країни."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Коди країн для блокування (розділені комами):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
msgid "Codes must be in ISO 3166-2 format."
msgstr "Коди мають бути у форматі ISO 3166-2."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
msgid "Use custom geo block message:"
msgstr "Використовуйте власне повідомлення про геоблокування:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Використовуйте власне повідомлення про блокування:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
msgid "IP Addresses"
msgstr "IP-адреси"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
msgid "Restore previously open private chats on startup"
msgstr "Відновлювати раніше відкриті приватні чати під час запуску"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
#, fuzzy
msgid "Enable spell checker"
msgstr "Увімкнути перевірку орфографії (потрібно перезапустити)"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
msgid "Enable CTCP-like private message responses (client version)"
msgstr ""
"Увімкнути відповіді на приватні повідомлення, схожі на CTCP (клієнтська "
"версія)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
msgid "Number of recent private chat messages to show:"
msgstr "Кількість останніх повідомлень приватного чату для відображення:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
msgid "Number of recent chat room messages to show:"
msgstr "Кількість останніх повідомлень кімнати чату для відображення:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
msgid "Chat Completion"
msgstr "Автодоповнення чату"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
msgid "Enable tab-key completion"
msgstr "Увімкнути автодоповнення табуляцією"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
msgid "Enable completion drop-down list"
msgstr "Увімкнути випадаючий список автодоповнення"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
msgid "Minimum characters required to display drop-down:"
msgstr ""
"Мінімальна кількість символів, необхідних для відображення випадаючого меню:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
msgid "Allowed chat completions:"
msgstr "Дозволені автодоповнення чату:"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
msgid "Buddy names"
msgstr "Імена друзів"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
msgid "Chat room usernames"
msgstr "Імена користувачів чат-кімнати"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
msgid "Room names"
msgstr "Назви кімнат"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
#, fuzzy
msgid "Commands"
msgstr "Команда"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
msgid "Timestamps"
msgstr "Позначки часу"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
msgid "Private chat format:"
msgstr "Формат приватної кімнати:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "За замовчуванням"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
msgid "Chat room format:"
msgstr "Формат чату:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
msgid "Text-to-Speech"
msgstr "Синтез мовлення"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
msgid "Enable Text-to-Speech"
msgstr "Увімкнути синтез мовлення"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
msgid "Text-to-Speech command:"
msgstr "Команда синтезу мовлення:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
msgid "Private chat message:"
msgstr "Повідомлення в приватному чаті:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
msgid "Chat room message:"
msgstr "Повідомлення чату:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
msgid "Censor"
msgstr "Цензура"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
msgid "Enable censoring of text patterns"
msgstr "Увімкнути цензування текстових шаблонів"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
msgid "Auto-Replace"
msgstr "Автозаміна"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
msgid "Enable automatic replacement of words"
msgstr "Увімкнути автоматичну заміну слів"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr ""
"Автоматичне очищення завершених/відфільтрованих завантажень зі списку передач"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
msgid "Store completed downloads in username subfolders"
msgstr "Зберігайте завершені завантаження у підкаталогах імені користувача"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
msgid "Double-click action for downloads:"
msgstr "Двічі клацніть для завантаження:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
#, fuzzy
msgid "Allow users to send you any files:"
msgstr "Дозволити цим користувачам надсилати вам файли:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
msgid "Folders"
msgstr "Каталоги"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
msgid "Finished downloads:"
msgstr "Завершені завантаження:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
msgid "Incomplete downloads:"
msgstr "Незавершені завантаження:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
#, fuzzy
msgid "Received files:"
msgstr "Отримано файли:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "Події"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
msgid "Run command after file download finishes ($ for file path):"
msgstr ""
"Виконати команду після завершення завантаження файлу ($ для шляху до файлу):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"Виконати команду після завершення завантаження каталогу ($ для шляху до "
"каталогу):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
msgid "Download Filters"
msgstr "Фільтри завантаження"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
msgid "Enable download filters"
msgstr "Увімкнути фільтри завантаження"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Додати"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Встановити параметри за замовчуванням"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
msgid "Verify Filters"
msgstr "Перевірити фільтри"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
msgid "Unverified"
msgstr "Неперевірено"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Ігнорувати повідомлення чату та результати пошуку від користувачів на основі "
"імені користувача чи IP-адреси."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Журналювати чати за умовчанням"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Журналювати приватні чати за замовчуванням"

#: pynicotine/gtkgui/ui/settings/log.ui:142
msgid "Log transfers to file"
msgstr "Журналювати передачі у файл"

#: pynicotine/gtkgui/ui/settings/log.ui:166
msgid "Log debug messages to file"
msgstr "Журналювати повідомлення про відлагодження у файл"

#: pynicotine/gtkgui/ui/settings/log.ui:190
#, fuzzy
msgid "Log timestamp format:"
msgstr "Формат часової позначки файлу журналу:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
msgid "Folder Locations"
msgstr "Розташування каталогів"

#: pynicotine/gtkgui/ui/settings/log.ui:245
msgid "Chatroom logs folder:"
msgstr "Каталог журналів чату:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
msgid "Private chat logs folder:"
msgstr "Каталог журналів приватного чату:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
msgid "Transfer logs folder:"
msgstr "Каталог журналів передачі:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
msgid "Debug logs folder:"
msgstr "Каталог журналів відлагодження:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Увійдіть до наявного облікового запису Soulseek або створіть новий. Імена "
"користувачів чутливі до регістру та унікальні."

#: pynicotine/gtkgui/ui/settings/network.ui:118
#, fuzzy
msgid "Public IP address:"
msgstr "Блокувати IP-адресу"

#: pynicotine/gtkgui/ui/settings/network.ui:159
#, fuzzy
msgid "Listening port:"
msgstr "Прослуховування через порт: %i"

#: pynicotine/gtkgui/ui/settings/network.ui:185
#, fuzzy
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Автоматично перенаправляти порт прослуховування (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
msgid "Away Status"
msgstr "Статус відсутності"

#: pynicotine/gtkgui/ui/settings/network.ui:228
#, fuzzy
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Хвилини бездіяльності перед відходом (0 для вимкнення):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
msgid "Auto-reply message when away:"
msgstr "Повідомлення автоматичної відповіді, коли відсутній:"

#: pynicotine/gtkgui/ui/settings/network.ui:299
msgid "Auto-connect to server on startup"
msgstr "Автоматичне підключення до сервера під час запуску"

#: pynicotine/gtkgui/ui/settings/network.ui:323
msgid "Soulseek server:"
msgstr "Сервер Soulseek:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Прив’язує з’єднання до певного мережевого інтерфейсу, корисно, наприклад, "
"для забезпечення постійного використання VPN. Залиште порожнім, щоб "
"використовувати будь-який доступний інтерфейс. Змінюйте це значення, лише "
"якщо ви знаєте, що робите."

#: pynicotine/gtkgui/ui/settings/network.ui:351
#, fuzzy
msgid "Network interface:"
msgstr "Пошуки в мережі"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"Функція «Зараз відтворюється» дозволяє відображати, що відтворює ваш "
"програвач, за допомогою команди /now у чаті."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
msgid "Other"
msgstr "Інше"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
msgid "Now Playing Format"
msgstr "Формат \"Зараз відтворюється\""

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
msgid "Now Playing message format:"
msgstr "Формат повідомлення \"Зараз відтворюється\":"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
msgid "Test Configuration"
msgstr "Тестувати конфігурацію"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
msgid "Enable plugins"
msgstr "Увімкнути плагіни"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
msgid "Add Plugins"
msgstr "Додати плагіни"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
msgid "_Add Plugins"
msgstr "_Додати плагіни"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
msgid "Settings"
msgstr "Налаштування"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
msgid "_Settings"
msgstr "_Налаштування"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
msgid "Version:"
msgstr "Версія:"

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
#, fuzzy
msgid "Created by:"
msgstr "Створений"

#: pynicotine/gtkgui/ui/settings/search.ui:51
msgid "Enable search history"
msgstr "Увімкнути історію пошуку"

#: pynicotine/gtkgui/ui/settings/search.ui:70
#, fuzzy
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Інші клієнти можуть мати налаштування для приватного обміну файлами у "
"відповідь на запити пошуку. Такі файли мають префікс \"[ПРИВАТНО]\", і їх не "
"можна завантажити, якщо користувач, який роздає, не надав чіткого дозволу. "
"Запитуйте дозвіл у власника безпосередньо."

#: pynicotine/gtkgui/ui/settings/search.ui:76
msgid "Show privately shared files in search results"
msgstr "Показувати приватні файли в результатах пошуку"

#: pynicotine/gtkgui/ui/settings/search.ui:106
msgid "Limit number of results per search:"
msgstr "Обмежити кількість результатів на пошук:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
msgid "Result Filter Help"
msgstr "Довідка фільтра результатів"

#: pynicotine/gtkgui/ui/settings/search.ui:177
msgid "Enable search result filters by default"
msgstr "Увімкнути фільтри результатів пошуку за замовчуванням"

#: pynicotine/gtkgui/ui/settings/search.ui:211
msgid "Include:"
msgstr "Включає:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
msgid "Exclude:"
msgstr "Виключає:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
msgid "File Type:"
msgstr "Тип файлу:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "Розмір:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Бітрейт:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
#, fuzzy
msgid "Duration:"
msgstr "Тривалість:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
msgid "Country Code:"
msgstr "Код країни:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
msgid "Only show results from users with an available upload slot."
msgstr ""
"Показувати результати лише від користувачів із доступним слотом для "
"вивантаження."

#: pynicotine/gtkgui/ui/settings/search.ui:430
msgid "Network Searches"
msgstr "Пошуки в мережі"

#: pynicotine/gtkgui/ui/settings/search.ui:452
msgid "Respond to search requests from other users"
msgstr "Відповідати на пошукові запити інших користувачів"

#: pynicotine/gtkgui/ui/settings/search.ui:486
msgid "Searches shorter than this number of characters will be ignored:"
msgstr "Пошуки, менші за цю кількість символів, будуть ігноровані:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
msgid "Maximum search results to send per search request:"
msgstr ""
"Максимальна кількість результатів пошуку для надсилання на пошуковий запит:"

#: pynicotine/gtkgui/ui/settings/search.ui:578
msgid "Clear Search History"
msgstr "Очистити історію пошуку"

#: pynicotine/gtkgui/ui/settings/search.ui:626
msgid "Clear Filter History"
msgstr "Очистити історію фільтрів"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Автоматично пересканує вміст ваших спільних каталогів час запуску. Якщо "
"вимкнено, ваші спільні ресурси оновлюватимуться лише тоді, коли ви вручну "
"ініціюєте пересканування."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Пересканувати спільні ресурси під час запуску"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
#, fuzzy
msgid "Visible to everyone:"
msgstr "Видимо для всіх:"

#: pynicotine/gtkgui/ui/settings/shares.ui:82
#, fuzzy
msgid "Buddy shares"
msgstr "Імена друзів"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
#, fuzzy
msgid "Trusted shares"
msgstr "Пересканування спільних каталогів"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr ""
"Автоматичне очищення завершених/скасованих вивантажень зі списку передач"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
msgid "Double-click action for uploads:"
msgstr "Двічі клацніть для вивантаження:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
msgid "Limit upload speed:"
msgstr "Обмеження швидкості вивантаження:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
msgid "Per transfer"
msgstr "За одну передачу"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
msgid "Total transfers"
msgstr "Кількість передач"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
msgid "Upload Slots"
msgstr "Слоти вивантаження"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Циклічно: файли будуть вивантажуватися циклічно користувачам, які чекають у "
"черзі.\n"
"Першим зайшов, першим вийшов: файли вивантажуватимуться в тому порядку, в "
"якому вони стояли в чергу."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
msgid "Upload queue type:"
msgstr "Тип черги вивантаження:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
#, fuzzy
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr ""
"Вивантаження в черзі, якщо загальна швидкість передачі досягає (КіБ/с):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
#, fuzzy
msgid "Fixed number of upload slots:"
msgstr "Обмежте кількість слотів для вивантаження:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
msgid "Prioritize all buddies"
msgstr "Пріоритети для всіх друзів"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
msgid "Queue Limits"
msgstr "Обмеження черги"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
#, fuzzy
msgid "Maximum number of queued files per user:"
msgstr "Обмежити кількість результатів на пошук:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
#, fuzzy
msgid "Maximum total size of queued files per user (MiB):"
msgstr "Максимальний загальний розмір файлів в черзі на користувача (МіБ):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
#, fuzzy
msgid "Limits do not apply to buddies"
msgstr "Обмеження черги не поширюються на друзів"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"Примірники $ замінюються URL-адресою. Системні програми за замовчуванням "
"використовуються в тих випадках, коли протокол не налаштовано."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
msgid "File manager command:"
msgstr "Команда файлового менеджера:"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
msgid "Reset Picture"
msgstr "Скинути зображення"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
msgid "Self Description"
msgstr "Самоопис"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
#, fuzzy
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""
"Додайте речі, які ви хочете, щоб всі бачили, наприклад короткий опис, "
"корисні поради або правила для завантаження ваших обмінів."

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
msgid "Picture:"
msgstr "Зображення:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
msgid "Prefer dark mode"
msgstr "Віддавати перевагу темному режиму"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
#, fuzzy
msgid "Use header bar"
msgstr "Використовувати панель заголовків"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
msgid "Display tray icon"
msgstr "Відображення значка в треї"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
msgid "Minimize to tray on startup"
msgstr "Згорнути в трей під час запуску"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
#, fuzzy
msgid "Language (requires a restart):"
msgstr "Діапазон портів прослуховування (потрібно перезавантажити):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
#, fuzzy
msgid "When closing window:"
msgstr "При закритті Nicotine+:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
msgid "Notifications"
msgstr "Сповіщення"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
msgid "Enable sound for notifications"
msgstr "Увімкнути звук для сповіщень"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
msgid "Show notification for private chats and mentions in the window title"
msgstr "Показувати сповіщення про приватні чати та згадки в заголовку вікна"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
msgid "Show notifications for:"
msgstr "Показати сповіщення для:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
msgid "Finished file downloads"
msgstr "Завершено завантаження файлів"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
msgid "Finished folder downloads"
msgstr "Завершено завантаження каталогу"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
msgid "Private messages"
msgstr "Приватні повідомлення"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
msgid "Chat room messages"
msgstr "Повідомлення чату"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
msgid "Chat room mentions"
msgstr "Згадки в чаті"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
#, fuzzy
msgid "Wishlist results found"
msgstr "Список бажань знайдено"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
msgid "Restore the previously active main tab at startup"
msgstr "Відновлювати раніше активну головну вкладку під час запуску"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
msgid "Close-buttons on secondary tabs"
msgstr "Кнопки закриття на додаткових вкладках"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
msgid "Regular tab label color:"
msgstr "Колір мітки звичайної вкладки:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
msgid "Changed tab label color:"
msgstr "Змінений колір мітки вкладки:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
msgid "Highlighted tab label color:"
msgstr "Колір мітки виділеної вкладки:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
#, fuzzy
msgid "Buddy list position:"
msgstr "Позиція списку друзів:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
msgid "Visible main tabs:"
msgstr "Видимі основні вкладки:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
#, fuzzy
msgid "Tab bar positions:"
msgstr "Положення панелі вкладок:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
#, fuzzy
msgid "Main tabs"
msgstr "Основні вкладки"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
#, fuzzy
msgid "Show reverse file paths (requires a restart)"
msgstr ""
"Показувати зворотні шляхи до файлів у переглядах пошуку та передачі "
"(потрібно перезавантажити)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
#, fuzzy
msgid "Show exact file sizes (requires a restart)"
msgstr "Мережевий інтерфейс (потрібно перезавантажити):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
msgid "List text color:"
msgstr "Колір тексту списку:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
#, fuzzy
msgid "Enable colored usernames"
msgstr "Імена користувачів чат-кімнати"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
msgid "Chat username appearance:"
msgstr "Зовнішній вигляд імені користувача в чаті:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
msgid "Remote text color:"
msgstr "Колір віддаленого тексту:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
msgid "Local text color:"
msgstr "Колір місцевого тексту:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
#, fuzzy
msgid "Command output text color:"
msgstr "Колір віддаленого тексту:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
msgid "/me action text color:"
msgstr "/me дія колір-тексту:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
msgid "Highlighted text color:"
msgstr "Колір виділеного тексту:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
msgid "URL link text color:"
msgstr "Колір тексту посилання URL:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
#, fuzzy
msgid "User Statuses"
msgstr "Сполучені Штати Америки"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
#, fuzzy
msgid "Online color:"
msgstr "Колір тексту у мережі:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
#, fuzzy
msgid "Away color:"
msgstr "Колір тексту при відсутності:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
#, fuzzy
msgid "Offline color:"
msgstr "Колір тексту поза мережею:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
msgid "Text Entries"
msgstr "Текстові записи"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
msgid "Text entry background color:"
msgstr "Колір фону введення тексту:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
msgid "Text entry text color:"
msgstr "Колір тексту введення:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
msgid "Fonts"
msgstr "Шрифти"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
msgid "Global font:"
msgstr "Загальний шрифт:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
msgid "List font:"
msgstr "Шрифт списку:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
#, fuzzy
msgid "Text view font:"
msgstr "Шрифт перегляду тексту:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
msgid "Chat font:"
msgstr "Шрифт чату:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
msgid "Transfers font:"
msgstr "Шрифт передач:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
msgid "Search font:"
msgstr "Шрифт пошуку:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
msgid "Browse font:"
msgstr "Шрифт переглядачу:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
msgid "Icons"
msgstr "Іконки"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
#, fuzzy
msgid "Icon theme folder:"
msgstr "Каталог неповних файлів:"

#: pynicotine/gtkgui/ui/uploads.ui:86
msgid "Abort User(s)"
msgstr "Перервати користувача(-ів)"

#: pynicotine/gtkgui/ui/uploads.ui:116
msgid "Ban User(s)"
msgstr "Заблокувати користувача(-ів)"

#: pynicotine/gtkgui/ui/uploads.ui:138
#, fuzzy
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Очистити всі завершені та скасовані завантаження."

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
#, fuzzy
msgid "Message All"
msgstr "Повідомлення (лоток)"

#: pynicotine/gtkgui/ui/uploads.ui:199
#, fuzzy
msgid "Clear Specific Uploads"
msgstr "Очистити всі вивантаження"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
#, fuzzy
msgid "Save Shares List to Disk"
msgstr "_Зберегти список спільних каталогів на диск"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
#, fuzzy
msgid "Refresh Files"
msgstr "Оновити файли"

#: pynicotine/gtkgui/ui/userinfo.ui:101
#, fuzzy
msgid "Edit Profile"
msgstr "Редагувати спільний каталог"

#: pynicotine/gtkgui/ui/userinfo.ui:149
msgid "Shared Files"
msgstr "Спільні файли"

#: pynicotine/gtkgui/ui/userinfo.ui:203
msgid "Upload Speed"
msgstr "Швидкість вивантаження"

#: pynicotine/gtkgui/ui/userinfo.ui:230
msgid "Free Upload Slots"
msgstr "Вільні слоти вивантаження"

#: pynicotine/gtkgui/ui/userinfo.ui:284
msgid "Queued Uploads"
msgstr "Черга вивантаження"

#: pynicotine/gtkgui/ui/userinfo.ui:354
#, fuzzy
msgid "Edit Interests"
msgstr "Інтереси"

#: pynicotine/gtkgui/ui/userinfo.ui:624
msgid "_Gift Privileges…"
msgstr "_Подарувати привілеї…"

#: pynicotine/gtkgui/ui/userinfo.ui:663
#, fuzzy
msgid "_Refresh Profile"
msgstr "Оновити файли"

#: pynicotine/plugins/core_commands/PLUGININFO:3
#, fuzzy
msgid "Nicotine+ Commands"
msgstr "Команда Nicotine+"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Команда Nicotine+"

#, fuzzy
#~ msgid "Listening port (requires a restart):"
#~ msgstr "Діапазон портів прослуховування (потрібно перезавантажити):"

#, fuzzy
#~ msgid "Network interface (requires a restart):"
#~ msgstr "Діапазон портів прослуховування (потрібно перезавантажити):"

#~ msgid "Invalid Password"
#~ msgstr "Недійсний пароль"

#~ msgid "Change _Login Details"
#~ msgstr "Змінити _Дані входу"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i привілейованих користувачів"

#~ msgid "_Set Up…"
#~ msgstr "_Налаштувати…"

#~ msgid "Queued search result text color:"
#~ msgstr "Колір тексту результату пошуку в черзі:"

#, python-format
#~ msgid "Failed to fetch the shared folder %(folder)s: %(error)s"
#~ msgstr "Не вдалося отримати спільний каталог %(folder)s: %(error)s"

#~ msgid "_Clear"
#~ msgstr "_Очистити"

#, python-format
#~ msgid "Can't save %(filename)s: %(error)s"
#~ msgstr "Не вдається зберегти %(filename)s: %(error)s"

#~ msgid "Trusted Buddies"
#~ msgstr "Довірені друзі"

#~ msgid "Quit program"
#~ msgstr "Вийти з програми"

#~ msgid "Username:"
#~ msgstr "Ім'я користувача:"

#~ msgid "Re_commendations for Item"
#~ msgstr "_Рекомендації щодо елементу"

#~ msgid "_Remove Item"
#~ msgstr "_Видалити елемент"

#~ msgid "_Remove"
#~ msgstr "_Видалити"

#~ msgid "Send M_essage"
#~ msgstr "Відправити _повідомлення"

#~ msgid "Send Message"
#~ msgstr "Відправити повідомлення"

#~ msgid "Start Messaging"
#~ msgstr "Почати листування"

#~ msgid "Enter the name of the user whom you want to send a message:"
#~ msgstr "Введіть ім'я користувача, якому ви хочете надіслати повідомлення:"

#~ msgid "Enter the name of the user whose shares you want to see:"
#~ msgstr "Введіть ім'я користувача, чиї спільні каталоги ви хочете побачити:"

#~ msgid "Password"
#~ msgstr "Пароль"

#~ msgid "Enter the username of the person whose files you want to see"
#~ msgstr "Введіть ім’я користувача особи, чиї файли ви хочете побачити"

#~ msgid "Enter the username of the person whose information you want to see"
#~ msgstr "Введіть ім’я користувача особи, чию інформацію ви хочете побачити"

#~ msgid "Enter the username of the person you want to send a message to"
#~ msgstr ""
#~ "Введіть ім’я користувача особи, якій ви хочете надіслати повідомлення"

#~ msgid "Enter the username of the person you want to add to your buddy list"
#~ msgstr ""
#~ "Введіть ім’я користувача, якого ви хочете додати до свого списку друзів"

#~ msgid ""
#~ "Enter the name of a room you want to join. If the room doesn't exist, it "
#~ "will be created."
#~ msgstr ""
#~ "Введіть назву кімнати, до якої хочете приєднатися. Якщо кімната не існує, "
#~ "вона буде створена."

#~ msgid "Save _Picture"
#~ msgstr "Зберегти _Зображення"

#, python-format
#~ msgid ""
#~ "Filtered out incorrect search result %(filepath)s from user %(user)s for "
#~ "search query \"%(query)s\""
#~ msgstr ""
#~ "Відфільтровано неправильний результат пошуку %(filepath)s від користувача "
#~ "%(user)s для пошукового запиту \"%(query)s\""

#~ msgid ""
#~ "Certain clients don't send search results if special characters are "
#~ "included."
#~ msgstr ""
#~ "Деякі клієнти не надсилають результати пошуку, якщо в них є спеціальні "
#~ "символи."

#~ msgid "Remove special characters from search terms"
#~ msgstr "Видаляти спеціальні символи з пошукових запитів"

#, python-format
#~ msgid "Trouble executing '%s'"
#~ msgstr "Проблема виконання \"%s\""

#, python-format
#~ msgid "Trouble executing on folder: %s"
#~ msgstr "Проблема з виконанням у каталозі: %s"

#~ msgid "Disallowed extension"
#~ msgstr "Заборонене розширення"

#~ msgid "Too many files"
#~ msgstr "Забагато файлів"

#~ msgid "Too many megabytes"
#~ msgstr "Забагато мегабайт"

#~ msgid "Server does not permit performing wishlist searches at this time"
#~ msgstr "На даний момент сервер не дозволяє виконувати пошук у списку бажань"

#~ msgid ""
#~ "File transfer speeds depend on users you are downloading from. Certain "
#~ "users will be faster, while others will be slow."
#~ msgstr ""
#~ "Швидкість передачі файлів залежить від користувачів, від яких ви "
#~ "завантажуєте файли. Деякі користувачі будуть швидшими, а інші – "
#~ "повільними."

#~ msgid "Started Downloads"
#~ msgstr "Розпочато завантаження"

#~ msgid "Started Uploads"
#~ msgstr "Розпочато вивантаження"

#~ msgid "Replace censored letters with:"
#~ msgstr "Заміняти цензуровану лексику на:"

#~ msgid "Censored Patterns"
#~ msgstr "Цензурові шаблони"

#~ msgid "Replacements"
#~ msgstr "Заміни"

#~ msgid ""
#~ "If a user on the Soulseek network searches for a file that exists in your "
#~ "shares, search results will be sent to the user."
#~ msgstr ""
#~ "Якщо користувач у мережі Soulseek шукає файл, який існує у ваших спільних "
#~ "ресурсах, результати пошуку будуть надіслані користувачеві."

#~ msgid "Send to Player"
#~ msgstr "Надіслати до програвача"

#~ msgid "Send to _Player"
#~ msgstr "Надіслати до _програвачу"

#~ msgid "_Open in File Manager"
#~ msgstr "_Відкрити в диспетчері файлів"

#~ msgid "Media player command:"
#~ msgstr "Команда програвача:"

#, python-format
#~ msgid ""
#~ "Unable to save download to username subfolder, falling back to default "
#~ "download folder. Error: %s"
#~ msgstr ""
#~ "Не вдається зберегти завантаження у каталог з іменем користувача, "
#~ "повернення до каталога завантаження за замовчуванням. Помилка: %s"

#~ msgid "Buddy-only"
#~ msgstr "Лише для друзів"

#~ msgid "_Quit…"
#~ msgstr "_Вийти…"

#~ msgid "_Configure Shares"
#~ msgstr "_Налаштувати спільні каталоги"

#~ msgid "Remote file error"
#~ msgstr "Помилка віддаленого файлу"

#, python-format
#~ msgid "Cannot find %(option1)s or %(option2)s, please install either one."
#~ msgstr ""
#~ "Не вдається знайти %(option1)s або %(option2)s, будь ласка, встановіть "
#~ "будь-який з них."

#, python-format
#~ msgid "Failed to process the following databases: %(names)s"
#~ msgstr "Не вдалося обробити такі бази даних: %(names)s"

#, python-format
#~ msgid "Failed to send number of shared files to the server: %s"
#~ msgstr "Не вдалося надіслати кількість спільних файлів на сервер: %s"

#~ msgid "Quit / Run in Background"
#~ msgstr "Вийти/запустити у фоновому режимі"

#~ msgid "Limit buddy-only shares to trusted buddies"
#~ msgstr ""
#~ "Обмежувати доступ до дружніх спільних ресурсів лише довіреним друзям"

#~ msgid "Shared"
#~ msgstr "Розмір спільного"

#~ msgid "Prefer Dark _Mode"
#~ msgstr "Віддавати перевагу _темному режиму"

#~ msgid "Show _Log History Pane"
#~ msgstr "Показувати панель журналу історії"

#~ msgid "Buddy List in Separate Tab"
#~ msgstr "Список друзів на окремій вкладці"

#~ msgid "Buddy List Always Visible"
#~ msgstr "Список друзів завжди видимий"

#~ msgid "_View"
#~ msgstr "_Вигляд"

#~ msgid "_Open Log Folder"
#~ msgstr "_Відкрити каталог журналу"

#~ msgid "_Browse Folder(s)"
#~ msgstr "_Переглянути каталог(-и)"

#~ msgid "Kibibytes (2^10 bytes) per second."
#~ msgstr "Кібібайти (2^10 байт) на секунду."

#~ msgid "Sent to users as the reason for being geo blocked."
#~ msgstr "Надіслано користувачам як причина геоблокування."

#~ msgid "Sent to users as the reason for being banned."
#~ msgstr "Надсилати користувачам як причину блокування."

#~ msgid "Each user may queue a maximum of either:"
#~ msgstr "Кожен користувач може поставити в чергу не більше:"

#~ msgid "Mebibytes (2^20 bytes)."
#~ msgstr "Мебібайти (2^20 байт)."

#~ msgid "MiB"
#~ msgstr "МіБ"

#~ msgid "files"
#~ msgstr "файлів"

#~ msgid "Queue Behavior"
#~ msgstr "Поведінка черги"

#~ msgid ""
#~ "If disabled, slots will automatically be determined by available "
#~ "bandwidth limitations."
#~ msgstr ""
#~ "Якщо вимкнено, слоти автоматично визначатимуться доступними обмеженнями "
#~ "пропускної здатності."

#~ msgid "Note that the operating system's theme may take precedence."
#~ msgstr "Зауважте, що тема операційної системи може мати пріоритет."

#~ msgid "By default the leftmost tab is activated at startup"
#~ msgstr "За замовчуванням під час запуску активується крайня ліва вкладка"

#, python-format
#~ msgid "Quit %(program)s %(version)s, %(status)s!"
#~ msgstr "Вихід з %(program)s %(version)s, %(status)s!"

#~ msgid "terminated"
#~ msgstr "припинено"

#~ msgid "done"
#~ msgstr "виконано"

#~ msgid "Remember choice"
#~ msgstr "Запам'ятати вибір"

#~ msgid "Kosovo"
#~ msgstr "Косово"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr ""
#~ "Здається, що сервер не працює або не відповідає, повторна спроба через %i "
#~ "секунд"

#~ msgid ""
#~ "Nicotine+ uses peer-to-peer networking to connect to other users. In "
#~ "order to allow users to connect to you without trouble, an open listening "
#~ "port is crucial."
#~ msgstr ""
#~ "Nicotine+ використовує однорангову мережу для підключення до інших "
#~ "користувачів. Щоб користувачі могли без проблем підключатися до вас, "
#~ "відкритий порт прослуховування має вирішальне значення."

#~ msgid "--- disconnected ---"
#~ msgstr "--- відключено ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- відновлено ---"

#~ msgid "ID"
#~ msgstr "Ідентифікатор"

#~ msgid "Earth"
#~ msgstr "Земля"

#~ msgid "Czech Republic"
#~ msgstr "Чеська Республіка"

#~ msgid "Turkey"
#~ msgstr "Туреччина"

#~ msgid "Joined Rooms "
#~ msgstr "Об’єднані кімнати "

#~ msgid "_Auto-join Room"
#~ msgstr "_Автоматичне приєднання до кімнати"

#~ msgid "Escaped"
#~ msgstr "Такий, що втік"

#~ msgid "to"
#~ msgstr "до"

#~ msgid ""
#~ "Cannot import the Gtk module. Bad install of the python-gobject module?"
#~ msgstr ""
#~ "Не вдається імпортувати модуль Gtk. Погане встановлення модуля python-"
#~ "gobject?"

#, python-format
#~ msgid ""
#~ "You are using an unsupported version of GTK %(major_version)s. You should "
#~ "install GTK %(complete_version)s or newer."
#~ msgstr ""
#~ "Ви використовуєте непідтримувану версію GTK %(major_version)s. Вам слід "
#~ "встановити GTK %(complete_version)s або новішу."

#~ msgid "User Info"
#~ msgstr "Інформація про користувача"

#~ msgid "Zoom 1:1"
#~ msgstr "Масштаб 1:1"

#~ msgid "Zoom In"
#~ msgstr "Збільшити"

#~ msgid "Zoom Out"
#~ msgstr "Зменшити"

#~ msgid "Show User I_nfo"
#~ msgstr "Показати інформацію про _користувача"

#~ msgid "Request User's Info"
#~ msgstr "Запит інформації про користувача"

#~ msgid "Request User's Shares"
#~ msgstr "Запит спільних каталогів користувача"

#~ msgid "Request User Info"
#~ msgstr "Запит інформації про користувача"

#~ msgid "Enter the name of the user whose info you want to see:"
#~ msgstr "Введіть ім'я користувача, чию інформацію ви хочете побачити:"

#~ msgid "Request Shares List"
#~ msgstr "Запит на список спільних каталогів"

#, python-format
#~ msgid "Invalid Soulseek URL: %s"
#~ msgstr "Недійсна URL-адреса Soulseek: %s"

#~ msgid ""
#~ "Users on the Soulseek network will be able to download files from folders "
#~ "you share. Sharing files is crucial for the health of the Soulseek "
#~ "network."
#~ msgstr ""
#~ "Користувачі мережі Soulseek зможуть завантажувати файли з каталогів, які "
#~ "ви зробили спільними. Спільний доступ до файлів має вирішальне значення "
#~ "для здоров’я мережі Soulseek."

#~ msgid "Update I_nfo"
#~ msgstr "Оновити І_нфо"

#~ msgid "Chat Room Commands"
#~ msgstr "Команди кімнати чату"

#~ msgid "/join /j 'room'"
#~ msgstr "/join /j 'кімната'"

#~ msgid "Join room 'room'"
#~ msgstr "Приєднатися до кімнати \"кімната\""

#~ msgid "/me 'message'"
#~ msgstr "/me 'повідомлення'"

#~ msgid "Display the Now Playing script's output"
#~ msgstr "Відобразити вихідні дані сценарію \"Зараз відтворюється\""

#~ msgid "/add /ad 'user'"
#~ msgstr "/add /ad 'користувач'"

#~ msgid "Add user 'user' to your buddy list"
#~ msgstr "Додати користувача \"user\" до свого списку друзів"

#~ msgid "/rem /unbuddy 'user'"
#~ msgstr "/rem /unbuddy 'користувач'"

#~ msgid "Remove user 'user' from your buddy list"
#~ msgstr "Видалити користувача \"user\" зі списку друзів"

#~ msgid "/ban 'user'"
#~ msgstr "/ban 'користувач'"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "Додати користувача \"user\" до вашого списку блокувань"

#~ msgid "/unban 'user'"
#~ msgstr "/unban 'користувач'"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "Видалити користувача \"user\" зі списку блокувань"

#~ msgid "/ignore 'user'"
#~ msgstr "/ignore 'користувач'"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "Додати користувача \"user\" до свого списку ігнорування"

#~ msgid "/unignore 'user'"
#~ msgstr "/unignore 'користувач'"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "Видаліть користувача \"user\" зі списку ігнорування"

#~ msgid "/browse /b 'user'"
#~ msgstr "/browse /b 'користувач'"

#~ msgid "/whois /w 'user'"
#~ msgstr "/whois /w 'користувач'"

#~ msgid "Request info for 'user'"
#~ msgstr "Запит інформації для \"користувача\""

#~ msgid "/ip 'user'"
#~ msgstr "/ip 'користувач'"

#~ msgid "Show IP for user 'user'"
#~ msgstr "Показати IP для користувача \"user\""

#~ msgid "/search /s 'query'"
#~ msgstr "/search /s 'запит'"

#~ msgid "Start a new search for 'query'"
#~ msgstr "Почати новий пошук за \"запитом\""

#~ msgid "/rsearch /rs 'query'"
#~ msgstr "/rsearch /rs 'запит'"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "Шукайте в об'єднаних кімнатах \"запит\""

#~ msgid "/bsearch /bs 'query'"
#~ msgstr "/bsearch /bs 'запит'"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "Знайдіть у списку друзів \"запит\""

#~ msgid "/usearch /us 'user' 'query'"
#~ msgstr "/usearch /us 'користувач' 'запит'"

#~ msgid "/msg 'user' 'message'"
#~ msgstr "/msg 'користувач' 'повідомлення'"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "Надіслати повідомлення 'message' користувачу 'user'"

#~ msgid "/pm 'user'"
#~ msgstr "/pm 'користувач'"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr "Відкрити вікно приватного чату для користувача 'user'"

#~ msgid "Private Chat Commands"
#~ msgstr "Команди приватного чату"

#~ msgid "Add user to your ban list"
#~ msgstr "Додати користувача до списку блокувань"

#~ msgid "Add user to your ignore list"
#~ msgstr "Додати користувача до списку ігнорування"

#~ msgid "Browse shares of user"
#~ msgstr "Переглянути спільні ресурси користувача"

#~ msgid "File size"
#~ msgstr "Розмір файлу"

#~ msgid "Cycle through completions when pressing tab-key"
#~ msgstr "Перебирати автодоповнення, натискаючи клавішу Tab"

#~ msgid "Hide drop-down when only one matches"
#~ msgstr "Приховати випадаюче меню, якщо збігається лише одне"

#~ msgid "Download folders in reverse alphanumerical order"
#~ msgstr "Завантажуйте каталоги у зворотному алфавітно-цифровому порядку"

#~ msgid "Incomplete file folder:"
#~ msgstr "Каталог неповних файлів:"

#~ msgid "Download folder:"
#~ msgstr "Каталог завантаження:"

#~ msgid "Save buddies' uploads to:"
#~ msgstr "Зберігайте завантаження друзів у:"

#~ msgid ""
#~ "Share folders with every Soulseek user or buddies, allowing contents to "
#~ "be downloaded directly from your device. Hidden files are never shared."
#~ msgstr ""
#~ "Поділіться каталогами з кожним користувачем або друзями Soulseek, "
#~ "дозволяючи завантажувати вміст безпосередньо з вашого пристрою. Приховані "
#~ "файли ніколи не надсилаються."

#~ msgid "Secondary Tabs"
#~ msgstr "Додаткові вкладинки"

#~ msgid "Chat room tab bar position:"
#~ msgstr "Розташування панелі вкладок кімнати чату:"

#~ msgid "Private chat tab bar position:"
#~ msgstr "Положення панелі вкладок приватного чату:"

#~ msgid "Search tab bar position:"
#~ msgstr "Позиція панелі вкладок пошуку:"

#~ msgid "User info tab bar position:"
#~ msgstr "Розташування панелі вкладки інформації про користувача:"

#~ msgid "User browse tab bar position:"
#~ msgstr "Позиція панелі вкладок перегляду користувача:"

#~ msgid "Tab Labels"
#~ msgstr "Мітки вкладок"

#~ msgid "_Refresh Info"
#~ msgstr "_Оновити інформацію"

#~ msgid "Block IP Address"
#~ msgstr "Блокувати IP-адресу"

#~ msgid "Connected"
#~ msgstr "Підключено"

#~ msgid "Disconnected"
#~ msgstr "Відключено"

#~ msgid "Disconnected (Tray)"
#~ msgstr "Відключено (лоток)"

#~ msgid "User(s)"
#~ msgstr "Користувач(-и)"

#, python-format
#~ msgid "Alias \"%s\" returned nothing"
#~ msgstr "Псевдонім \"%s\" нічого не повернув"

#~ msgid "Alternative Speed Limits"
#~ msgstr "Альтернативні обмеження швидкості"

#~ msgid "Last played"
#~ msgstr "Востаннє відтворювався"

#~ msgid "Playing now"
#~ msgstr "Відтворюється зараз"

#, python-format
#~ msgid "Error code %(code)s: %(description)s"
#~ msgstr "Код помилки %(code)s: %(description)s"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "Немає такого псевдоніма (%s)"

#~ msgid "Aliases:"
#~ msgstr "Псевдоніми:"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "Вилучено псевдонім %(alias)s: %(action)s\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "Немає псевдоніма (%(alias)s)\n"

#~ msgid "Aliases"
#~ msgstr "Псевдоніми"

#~ msgid "/alias /al 'command' 'definition'"
#~ msgstr "/alias /al 'команда' 'визначення'"

#~ msgid "Add a new alias"
#~ msgstr "Додати новий псевдонім"

#~ msgid "/unalias /un 'command'"
#~ msgstr "/unalias /un 'команда'"

#~ msgid "Remove an alias"
#~ msgstr "Видалити псевдонім"

#~ msgid "Chat History"
#~ msgstr "Історія чату"

#~ msgid "Command aliases"
#~ msgstr "Псевдоніми команд"

#~ msgid "Limit download speed to (KiB/s):"
#~ msgstr "Обмежити швидкість завантаження до (КіБ/с):"

#~ msgid "Author(s):"
#~ msgstr "Автор(-и):"

#~ msgid "Limit upload speed to (KiB/s):"
#~ msgstr "Обмежити швидкість вивантаження до (КіБ/с):"

#~ msgid "Tabs show user status icons instead of status text"
#~ msgstr ""
#~ "На вкладках замість тексту відображаються значки статусу користувача"

#~ msgid "Show file path tooltips in file list views"
#~ msgstr "Показувати підказки шляхів до файлів у переглядах списку файлів"

#~ msgid "Colored and clickable usernames"
#~ msgstr "Кольорові та клікабельні імена користувачів"

#~ msgid "Notification changes the tab's text color"
#~ msgstr "Сповіщення змінює колір тексту вкладки"

#~ msgid "Cancel"
#~ msgstr "Скасувати"

#~ msgid "OK"
#~ msgstr "Гаразд"

#~ msgid "_Add to Buddy List"
#~ msgstr "_Додати до списку друзів"

#~ msgid "use non-default user data directory for e.g. list of downloads"
#~ msgstr ""
#~ "використовувати користувацький каталог даних, наприклад, список "
#~ "завантажень"

#, python-format
#~ msgid "Unknown config section '%s'"
#~ msgstr "Невідомий розділ конфігурації \"%s\""

#, python-format
#~ msgid "Unknown config option '%(option)s' in section '%(section)s'"
#~ msgstr ""
#~ "Невідомий параметр конфігурації \"%(option)s\" у розділі \"%(section)s\""

#, python-format
#~ msgid "Version %s is available"
#~ msgstr "Доступна версія %s"

#, python-format
#~ msgid "released on %s"
#~ msgstr "випущено на %s"

#~ msgid "Nicotine+ is running in the background"
#~ msgstr "Nicotine+ працює у фоновому режимі"

#, python-format
#~ msgid "Private message from %s"
#~ msgstr "Приватне повідомлення від %s"

#~ msgid "Aborted"
#~ msgstr "Перервано"

#~ msgid "Blocked country"
#~ msgstr "Заблокована країна"

#~ msgid "Finished / Aborted"
#~ msgstr "Завершено / перервано"

#~ msgid "Close tab"
#~ msgstr "Закрити вкладинку"

#, python-format
#~ msgid "You've been mentioned in the %(room)s room"
#~ msgstr "Вас згадали в кімнаті %(room)s"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "Команда %s не розпізнається"

#, python-format
#~ msgid "(Warning: %(realuser)s is attempting to spoof %(fakeuser)s) "
#~ msgstr "(Попередження: %(realuser)s намагається підробити %(fakeuser)s) "

#, python-format
#~ msgid ""
#~ "The network interface you specified, '%s', does not exist. Change or "
#~ "remove the specified network interface and restart Nicotine+."
#~ msgstr ""
#~ "Мережевий інтерфейс, який ви вказали, «%s», не існує. Змініть або "
#~ "видаліть вказаний мережевий інтерфейс та перезапустіть Nicotine+."

#~ msgid ""
#~ "The range you specified for client connection ports was {}-{}, but none "
#~ "of these were usable. Increase and/or "
#~ msgstr ""
#~ "Діапазон, який ви вказали для портів підключення клієнта, був {}-{}, але "
#~ "жоден з них не можна було використовувати. Збільшіть та/або "

#~ msgid ""
#~ "Note that part of your range lies below 1024, this is usually not allowed "
#~ "on most operating systems with the exception of Windows."
#~ msgstr ""
#~ "Зауважте, що частина вашого діапазону знаходиться нижче 1024, це зазвичай "
#~ "не дозволяється в більшості операційних систем, за винятком Windows."

#, python-format
#~ msgid "Rescan progress: %s"
#~ msgstr "Перебіг пересканування: %s"

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "Помилка ОС: %s"

#~ msgid "UPnP is not available on this network"
#~ msgstr "UPnP недоступний у цій мережі"

#, python-format
#~ msgid "Failed to map the external WAN port: %(error)s"
#~ msgstr "Не вдалося зіставити зовнішній порт WAN: %(error)s"

#~ msgid "Room wall"
#~ msgstr "Стіна кімнати"

#~ msgid ""
#~ "The default listening port '2234' works fine in most cases. If you need "
#~ "to use a different port, you will be able to modify it in the preferences "
#~ "later."
#~ msgstr ""
#~ "Порт прослуховування за замовчуванням «2234» у більшості випадків працює "
#~ "нормально. Якщо вам потрібно використовувати інший порт, ви зможете "
#~ "змінити його в налаштуваннях пізніше."

#~ msgid "Show users with similar interests"
#~ msgstr "Показати користувачів зі схожими інтересами"

#~ msgid "Menu"
#~ msgstr "Меню"

#~ msgid "Expand / Collapse all"
#~ msgstr "Розгорнути/згорнути все"

#~ msgid "Configure shares"
#~ msgstr "Налаштувати спільні каталоги"

#~ msgid "Create or join room…"
#~ msgstr "Створити або приєднатися до кімнати…"

#~ msgid "_Room List"
#~ msgstr "_Список кімнат"

#~ msgid "Enable alternative download and upload speed limits"
#~ msgstr ""
#~ "Увімкнути альтернативні обмеження швидкості завантаження та вивантаження"

#~ msgid "Show log history"
#~ msgstr "Показати історію журналу"

#~ msgid "Result grouping mode"
#~ msgstr "Режим групування результатів"

#~ msgid "Free slot"
#~ msgstr "Вільний слот"

#~ msgid "Save shares list to disk"
#~ msgstr "Зберегти список спільних ресурсів на диск"

#~ msgid "_Away"
#~ msgstr "_Відсутній"

#, python-format
#~ msgid "Failed to load ui file %(file)s: %(error)s"
#~ msgstr ""
#~ "Не вдалося завантажити файл інтерфейсу користувача %(file)s: %(error)s"

#~ msgid ""
#~ "Attempting to reset index of shared files due to an error. Please rescan "
#~ "your shares."
#~ msgstr ""
#~ "Спроба скинути індекс спільних файлів через помилку. Будь ласка, "
#~ "перескануйте свої спільні ресурси."

#~ msgid ""
#~ "File index of shared files could not be accessed. This could occur due to "
#~ "several instances of Nicotine+ being active simultaneously, file "
#~ "permission issues, or another issue in Nicotine+."
#~ msgstr ""
#~ "Не вдалося отримати доступ до індексу файлів спільного доступу. Це може "
#~ "статися через декілька випадків одночасної активності Nicotine+, проблеми "
#~ "з дозволом на файл або іншу проблему в Nicotine+."

#~ msgid "Nicotine+ is a Soulseek client"
#~ msgstr "Nicotine+ є клієнтом Soulseek"

#~ msgid "enable the tray icon"
#~ msgstr "відображати піктограму в треї"

#~ msgid "disable the tray icon"
#~ msgstr "приховати піктограму в треї"

#~ msgid "Get Soulseek Privileges…"
#~ msgstr "Отримати привілеї Soulseek…"

#, python-format
#~ msgid ""
#~ "Public IP address is <b>%(ip)s</b> and active listening port is "
#~ "<b>%(port)s</b>"
#~ msgstr ""
#~ "Загальнодоступна IP-адреса — <b>%(ip)s</b>, а активний порт "
#~ "прослуховування — <b>%(port)s</b>"

#~ msgid "unknown"
#~ msgstr "невідомий"

#~ msgid "Notification"
#~ msgstr "Повідомлення"

#~ msgid "Length"
#~ msgstr "Тривалість"

#, python-format
#~ msgid "Picture not saved, %s already exists."
#~ msgstr "Зображення не збережено, %s вже існує."

#~ msgid "_Open"
#~ msgstr "_Відкрити"

#~ msgid "_Save"
#~ msgstr "_Зберегти"

#, python-format
#~ msgid "Failed to load plugin '%s', could not find it."
#~ msgstr "Не вдалося завантажити плагін \"%s\", не вдалося його знайти."

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "Помилка введення-виводу: %s"

#, python-format
#~ msgid "Failed to open file path: %s"
#~ msgstr "Не вдалося відкрити шлях до файлу: %s"

#, python-format
#~ msgid "Failed to open URL: %s"
#~ msgstr "Не вдалося відкрити URL-адресу: %s"

#~ msgid "_Log Conversation"
#~ msgstr "_Журнал розмови"

#~ msgid "Result Filter List"
#~ msgstr "Список фільтрів результатів"

#~ msgid "Prepend < or > to find files less/greater than the given value."
#~ msgstr "Додайте <or>, щоб знайти файли, менші/більші за задане значення."

#~ msgid ""
#~ "VBR files display their average bitrate and are typically lower in "
#~ "bitrate than a compressed 320 kbps CBR file of the same audio quality."
#~ msgstr ""
#~ "Файли VBR відображають середній бітрейт і, як правило, нижчий за бітрейт, "
#~ "ніж стиснений файл CBR зі швидкістю 320 кбіт/с з такою ж якістю звуку."

#~ msgid "Like Size above, =, <, and > can be used."
#~ msgstr "Як і у фільтрі розміру, можна використовувати =, < і >."

#~ msgid ""
#~ "Prevent write access by other programs for files being downloaded (turn "
#~ "off for NFS)"
#~ msgstr ""
#~ "Заборонити доступ на запис іншим програмам для файлів, що завантажуються "
#~ "(вимкніть для NFS)"

#~ msgid "Where incomplete downloads are temporarily stored."
#~ msgstr "Де тимчасово зберігаються неповні завантаження."

#~ msgid ""
#~ "Where buddies' uploads will be stored (with a subfolder created for each "
#~ "buddy)."
#~ msgstr ""
#~ "Де зберігатимуться вивантаження друзів (з підкаталогом, створеною для "
#~ "кожного друга)."

#~ msgid "Toggle away status after minutes of inactivity:"
#~ msgstr "Перемкнути статус відсутності після хвилин бездіяльності:"

#~ msgid "Protocol:"
#~ msgstr "Протокол:"

#~ msgid "Icon theme folder (requires restart):"
#~ msgstr "Каталог теми іконок (потрібно перезавантажити):"

#~ msgid "Establishing connection"
#~ msgstr "Встановлення з'єднання"

#~ msgid "Clear Groups"
#~ msgstr "Очистити групи"

#~ msgid "_Reset Statistics…"
#~ msgstr "_Скинути статистику…"

#~ msgid "Clear _Downloads…"
#~ msgstr "Очистити _завантажені…"

#~ msgid "Clear Uploa_ds…"
#~ msgstr "Очистити ви_вантаження…"

#~ msgid "Block User's IP Address"
#~ msgstr "Блокувати IP-адресу користувача"

#~ msgid "Ignore User's IP Address"
#~ msgstr "Ігнорувати IP-адресу користувача"

#~ msgid "Usernames"
#~ msgstr "Імена користувачів"

#~ msgid "Display logged chat room messages when a room is rejoined"
#~ msgstr ""
#~ "Відображати зареєстровані повідомлення кімнати чату, коли знову "
#~ "приєднуєтеся до кімнати"

#~ msgid "Queue Position"
#~ msgstr "Позиція в черзі"

#, python-format
#~ msgid ""
#~ "User %(user)s is directly searching for \"%(query)s\", returning %(num)i "
#~ "results"
#~ msgstr ""
#~ "Користувач %(user)s безпосередньо шукає \"%(query)s\", повертаючи "
#~ "результати %(num)i"
