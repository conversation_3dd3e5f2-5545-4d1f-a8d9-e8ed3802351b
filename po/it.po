# SPDX-FileCopyrightText: 2003-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2024-08-07 19:33+0000\n"
"Last-Translator: Mat <<EMAIL>>\n"
"Language-Team: Italian <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/it/>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.7-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr "Client per Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Client grafico per la rete peer-to-peer Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr "Soulseek;Nicotine;condivisione;chat;messaggi;P2P;peer-to-peer;GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
#, fuzzy
msgid "Browse the Soulseek network"
msgstr "Client grafico per la rete Soulseek"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+ è un client grafico per la rete peer-to-peer Soulseek."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ si propone di essere un'alternativa leggera, piacevole, gratuita e "
"open source (FOSS) al client ufficiale di Soulseek, offrendo anche un "
"insieme completo di funzionalità."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
msgid "Search Files"
msgstr "Cerca File"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Download"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
msgid "Browse Shares"
msgstr "Sfoglia Condivisi"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
msgid "Private Chat"
msgstr "Chat Privata"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Squadra di Nicotine+"

#: pynicotine/__init__.py:50
#, python-format
msgid "Website: %s"
msgstr "Sito web: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "mostra questo messaggio di aiuto ed esci"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "file"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr "usa file non predefinito di configurazione"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "cartella"

#: pynicotine/__init__.py:64
msgid "alternative directory for user data and plugins"
msgstr "cartella alternativa per i dati utente e le estensioni"

#: pynicotine/__init__.py:68
msgid "start the program without showing window"
msgstr "avvia il programma senza mostrare la finestra"

#: pynicotine/__init__.py:71
msgid "ip"
msgstr "ip"

#: pynicotine/__init__.py:72
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "associa socket all'IP assegnato (comodo per le VPN)"

#: pynicotine/__init__.py:75
msgid "port"
msgstr "porta"

#: pynicotine/__init__.py:76
msgid "listen on the given port"
msgstr "ascolta sulla porta assegnata"

#: pynicotine/__init__.py:80
msgid "rescan shared files"
msgstr "Indicizza file condivisi"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr "avvia il programma in modalità headless (nessuna GUI)"

#: pynicotine/__init__.py:88
msgid "display version and exit"
msgstr "mostra versione ed esci"

#: pynicotine/__init__.py:121
#, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Stai usando una versione non supportata di Python (%(old_version)s).\n"
"Dovresti installare Python %(min_version)s o più recente."

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Impossibile eseguire la scansione dei condivisi. Chiudi altre istanze di "
"Nicotine+ e riprova."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, python-format
msgid "%(user)s is away"
msgstr "%(user)s è assente"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, python-format
msgid "%(user)s is online"
msgstr "%(user)s è online"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, python-format
msgid "%(user)s is offline"
msgstr "%(user)s è offline"

#: pynicotine/buddies.py:316
msgid "Buddy Status"
msgstr "Stato amici"

#: pynicotine/chatrooms.py:383
#, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Sei stato aggiunto alla chat privata: %(room)s"

#: pynicotine/chatrooms.py:478
#, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr ""
"Messaggio chat dall'utente '%(user)s' nel canale '%(room)s': %(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr "Impossibile creare la cartella '%(path)s', errore riportato: %(error)s"

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr "Errore durante il backup della configurazione: %s"

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr "Configurazione salvata in: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Caricamento %(program)s %(version)s"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Uscendo %(program)s %(version)s, %(status)s…"

#: pynicotine/core.py:246
msgid "terminating"
msgstr "terminazione"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "chiusura applicazione"

#: pynicotine/core.py:259
#, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Esci da %(program)s %(version)s!"

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr "Devi specificare un nome utente ed una password prima di connetterti…"

#: pynicotine/downloads.py:239
#, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr "Errore: Filtro Download fallito! Verifica i tuoi filtri: %s"

#: pynicotine/downloads.py:254
#, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Errore: %(num)d Download filtri rotto %(error)s "

#: pynicotine/downloads.py:366
#, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s ricevuto da %(user)s"

#: pynicotine/downloads.py:370
msgid "File Downloaded"
msgstr "File scaricato"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Eseguito: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "Esecuzione fallita di '%(command)s': %(error)s"

#: pynicotine/downloads.py:407
#, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s scaricato da %(user)s"

#: pynicotine/downloads.py:411
msgid "Folder Downloaded"
msgstr "Cartella scaricata"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Eseguito sulla cartella: %s"

#: pynicotine/downloads.py:443
#, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "Impossibile spostare '%(tempfile)s' in '%(file)s': %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
msgid "Download Folder Error"
msgstr "Errore download cartella"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Download completato: utente %(user)s, file %(file)s"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Download interrotto: utente %(user)s, file %(file)s"

#: pynicotine/downloads.py:1150
#, python-format
msgid "Download I/O error: %s"
msgstr "Errore I/O in download: %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Impossibile ottenere lock l'esclusiva su file - errore di I/O: %s"

#: pynicotine/downloads.py:1202
#, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Impossibile salvare il file in %(folder_path)s: %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Download iniziato: utente %(user)s, file %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, python-format
msgid "Cannot find %s, please install it."
msgstr "Impossibile trovare %s, installalo."

#: pynicotine/gtkgui/__init__.py:162
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr ""
"Nessun ambiente grafico disponibile, verrà utilizzata la modalità headless "
"(senza GUI)"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "_Connetti"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "_Disconnetti"

#: pynicotine/gtkgui/application.py:308
msgid "Soulseek _Privileges"
msgstr "_Privilegi Soulseek"

#: pynicotine/gtkgui/application.py:314
msgid "_Preferences"
msgstr "_Preferenze"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
msgid "_Quit"
msgstr "_Esci"

#: pynicotine/gtkgui/application.py:337
msgid "Browse _Public Shares"
msgstr "Sfoglia _Condivisioni Pubbliche"

#: pynicotine/gtkgui/application.py:338
msgid "Browse _Buddy Shares"
msgstr "Sfoglia _Condividi Buddy"

#: pynicotine/gtkgui/application.py:339
msgid "Browse _Trusted Shares"
msgstr "Sfoglia _Condivisioni Affidabili"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
msgid "_Rescan Shares"
msgstr "Indicizza Condivisi"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
msgid "Configure _Shares"
msgstr "Configura _Condividi"

#: pynicotine/gtkgui/application.py:371
msgid "_Keyboard Shortcuts"
msgstr "_Scorciatoie tastiera"

#: pynicotine/gtkgui/application.py:372
msgid "_Setup Assistant"
msgstr "Configurazione Guidata"

#: pynicotine/gtkgui/application.py:373
msgid "_Transfer Statistics"
msgstr "_Statistiche trasferimento"

#: pynicotine/gtkgui/application.py:378
msgid "Report a _Bug"
msgstr "Segnala un _Bug"

#: pynicotine/gtkgui/application.py:379
msgid "Improve T_ranslations"
msgstr "Migliora _Traduzioni"

#: pynicotine/gtkgui/application.py:383
msgid "_About Nicotine+"
msgstr "Informazioni su Nicotine+"

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "_File"

#: pynicotine/gtkgui/application.py:395
msgid "_Shares"
msgstr "Condivi_si"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
msgid "_Help"
msgstr "Aiuto"

#: pynicotine/gtkgui/application.py:410
msgid "_Browse Shares"
msgstr "_Sfoglia Condividi"

#: pynicotine/gtkgui/application.py:465
#, python-format
msgid "Unable to show notification: %s"
msgstr "Impossibile mostrare notifica: %s"

#: pynicotine/gtkgui/application.py:526
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Stai ancora caricando file. Sei sicuro di voler uscire?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr "Attendere che i carichi finiscano"

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr "Vuoi davvero uscire?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
msgid "_No"
msgstr "_No"

#: pynicotine/gtkgui/application.py:535
msgid "_Run in Background"
msgstr "Esegui in Backg_round"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr "Esci da Nicotine+"

#: pynicotine/gtkgui/application.py:561
msgid "Shares Not Available"
msgstr "Condivisioni Non Disponibili"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Verifica che i dischi esterni siano montati e che i permessi delle cartelle "
"siano corretti."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
msgid "_Cancel"
msgstr "Annulla"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "_Riprova"

#: pynicotine/gtkgui/application.py:567
msgid "_Force Rescan"
msgstr "_Forza Indicizzazione"

#: pynicotine/gtkgui/application.py:742
msgid "Message Downloading Users"
msgstr "Messaggia Gli Utenti Che Stanno Scaricando"

#: pynicotine/gtkgui/application.py:743
msgid "Send private message to all users who are downloading from you:"
msgstr ""
"Invia un messaggio privato a tutti gli utenti che stanno scaricando da te:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
msgid "_Send Message"
msgstr "_Invia Messaggio"

#: pynicotine/gtkgui/application.py:756
msgid "Message Buddies"
msgstr "Messaggia Amici"

#: pynicotine/gtkgui/application.py:757
msgid "Send private message to all online buddies:"
msgstr "Invia un messaggio privato a tutti gli amici online:"

#: pynicotine/gtkgui/application.py:786
msgid "Select a Saved Shares List File"
msgstr "Seleziona un File Salvato di Lista Condivisi"

#: pynicotine/gtkgui/application.py:877
msgid "Critical Error"
msgstr "Errore Critico"

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ ha riscontrato un errore critico e deve uscire. Copia il seguente "
"messaggio e includerlo in una segnalazione di errore:"

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr "Esci da Nicotine+"

#: pynicotine/gtkgui/application.py:883
msgid "_Copy & Report Bug"
msgstr "_Copia e Segnala Errore"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Stato"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr "Paese"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Utente"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Velocità"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "File"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted"
msgstr "Fidato"

#: pynicotine/gtkgui/buddies.py:108
msgid "Notify"
msgstr "Notifica"

#: pynicotine/gtkgui/buddies.py:114
msgid "Prioritized"
msgstr "Priorità"

#: pynicotine/gtkgui/buddies.py:120
msgid "Last Seen"
msgstr "Ultima Visita"

#: pynicotine/gtkgui/buddies.py:126
msgid "Note"
msgstr "Nota"

#: pynicotine/gtkgui/buddies.py:143
msgid "Add User _Note…"
msgstr "Aggiungi _Nota Utente…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Rimuovi"

#: pynicotine/gtkgui/buddies.py:364
msgid "Never seen"
msgstr "Mai visitato"

#: pynicotine/gtkgui/buddies.py:529
msgid "Add User Note"
msgstr "Aggiungi Nota Utente"

#: pynicotine/gtkgui/buddies.py:530
#, python-format
msgid "Add a note about user %s:"
msgstr "Aggiungi nota associata all'utente %s:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
msgid "_Add"
msgstr "_Aggiungi"

#: pynicotine/gtkgui/chatrooms.py:224
msgid "Create New Room?"
msgstr "Creare Nuovo Canale?"

#: pynicotine/gtkgui/chatrooms.py:225
#, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Sei sicuro di voler creare un nuovo canale \"%s\"?"

#: pynicotine/gtkgui/chatrooms.py:226
msgid "Make room private"
msgstr "Rendi canale privato"

#: pynicotine/gtkgui/chatrooms.py:515
msgid "Search activity log…"
msgstr "Cerca nel registro attività…"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
msgid "Search chat log…"
msgstr "Cerca nel registro chat…"

#: pynicotine/gtkgui/chatrooms.py:597
msgid "Sear_ch User's Files"
msgstr "Cerca File dell'Utente"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
msgid "Find…"
msgstr "Cerca…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Copia"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr "Copia Tutti"

#: pynicotine/gtkgui/chatrooms.py:608
msgid "Clear Activity View"
msgstr "Pulisci Vista Attività"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
msgid "_Leave Room"
msgstr "_Abbandona stanza"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
msgid "Copy Link"
msgstr "Copia Link"

#: pynicotine/gtkgui/chatrooms.py:624
msgid "View Room Log"
msgstr "Mostra Registro Canale"

#: pynicotine/gtkgui/chatrooms.py:627
msgid "Delete Room Log…"
msgstr "Elimina Registro Canale…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
msgid "Clear Message View"
msgstr "Pulisci Vista Messaggio"

#: pynicotine/gtkgui/chatrooms.py:832
#, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s ti ha menzionato nella stanza %(room)s"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "Menzionato da %(user)s nella stanza %(room)s"

#: pynicotine/gtkgui/chatrooms.py:855
#, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "Messaggio di %(user)s nella Stanza %(room)s"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s è entrato nel canale"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s lascia il canale"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s è andato via"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s è tornato"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
msgid "Delete Logged Messages?"
msgstr "Eliminare i Messaggi Registrati?"

#: pynicotine/gtkgui/chatrooms.py:1197
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"Sei sicuro di voler eliminare definitivamente tutti i messaggi registrati "
"per questo canale?"

#: pynicotine/gtkgui/dialogs/about.py:405
msgid "About"
msgstr "Riguardo al programma"

#: pynicotine/gtkgui/dialogs/about.py:415
msgid "Website"
msgstr "Sito web"

#: pynicotine/gtkgui/dialogs/about.py:457
#, python-format
msgid "Error checking latest version: %s"
msgstr "Errore durante il controllo dell'ultima versione: %s"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr "Nuova versione disponibile: %s"

#: pynicotine/gtkgui/dialogs/about.py:467
msgid "Up to date"
msgstr "Aggiornato"

#: pynicotine/gtkgui/dialogs/about.py:496
msgid "Checking latest version…"
msgstr "Controlla ultima versione…"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
msgid "Setup Assistant"
msgstr "Configurazione Guidata"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
msgid "Virtual Folder"
msgstr "Cartella Virtuale"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
msgid "Folder"
msgstr "Cartella"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
msgid "_Previous"
msgstr "_Precedente"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
msgid "_Finish"
msgstr "Completato"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
msgid "_Next"
msgstr "Successivo"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
msgid "Add a Shared Folder"
msgstr "Aggiungi una Cartella Condivisa"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
msgid "Edit Shared Folder"
msgstr "Modifica Cartella Condivisa"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Inserisci nuovo nome per virtuale per '%(dir)s':"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
msgid "_Edit"
msgstr "_Modifica"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"L'utente %s esiste già e la password inserita non è valida. Scegli un altro "
"nome utente se è la prima volta che accedi."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
msgid "File Properties"
msgstr "Proprietà File"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "Proprietà File (%(num)i di %(total)i   /   %(size)s   /   %(length)s)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Proprietà File (%(num)i di %(total)i  /  %(size)s)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
msgid "_Apply"
msgstr "_Applica"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
msgid "Add…"
msgstr "Aggiungi…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
msgid "Edit…"
msgstr "Modifica…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, python-format
msgid "%s Settings"
msgstr "Impostazioni %s"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
msgid "Add Item"
msgstr "Aggiungi elemento"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
msgid "Edit Item"
msgstr "Modifica elemento"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "Sconosciuto"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
msgid "Check Port Status"
msgstr "Controlla Stato Porta"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, porta %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
msgid "Password Change Rejected"
msgstr "Cambio Password Respinto"

#: pynicotine/gtkgui/dialogs/preferences.py:218
msgid "Enter a new password for your Soulseek account:"
msgstr "Inserisci una nuova password per il tuo account Soulseek:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"Sei attualmente disconnesso dalla rete Soulseek. Se vuoi cambiare la "
"password di un account Soulseek esistente, devi prima accedere all'account "
"in questione."

#: pynicotine/gtkgui/dialogs/preferences.py:223
msgid "Enter password to use when logging in:"
msgstr "Inserisci la password da utilizzare per l'accesso:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
msgid "Change Password"
msgstr "Cambia Password"

#: pynicotine/gtkgui/dialogs/preferences.py:230
msgid "_Change"
msgstr "_Cambia"

#: pynicotine/gtkgui/dialogs/preferences.py:274
msgid "No one"
msgstr "Nessuno"

#: pynicotine/gtkgui/dialogs/preferences.py:275
msgid "Everyone"
msgstr "Tutti"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Amici"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted buddies"
msgstr "Amici fidati"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
msgid "Nothing"
msgstr "Niente"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
msgid "Open File"
msgstr "Apri File"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
msgid "Open in File Manager"
msgstr "Apri nel Gestore dei File"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Ricerca"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
msgid "Pause"
msgstr "Pausa"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
msgid "Resume"
msgstr "Riprendi"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
msgid "Browse Folder"
msgstr "Sfogliare la cartella"

#: pynicotine/gtkgui/dialogs/preferences.py:317
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Sintassi</b>: senza distinzione tra maiuscole e minuscole. Se abilitato, "
"possono essere utilizzate le espressioni regolari Python, altrimenti sono "
"supportate solo le corrispondenze con caratteri jolly *."

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr "Filtro"

#: pynicotine/gtkgui/dialogs/preferences.py:334
msgid "Regex"
msgstr "Regex"

#: pynicotine/gtkgui/dialogs/preferences.py:468
msgid "Add Download Filter"
msgstr "Aggiungi Filtro di Download"

#: pynicotine/gtkgui/dialogs/preferences.py:469
msgid "Enter a new download filter:"
msgstr "Inserisci un nuovo filtro download:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
msgid "Enable regular expressions"
msgstr "Abilita le espressioni regolari"

#: pynicotine/gtkgui/dialogs/preferences.py:498
msgid "Edit Download Filter"
msgstr "Modifica Filtro di Upload"

#: pynicotine/gtkgui/dialogs/preferences.py:499
msgid "Modify the following download filter:"
msgstr "Modifica il seguente filtro download:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d Fallito! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
msgid "Filters Successful"
msgstr "Filtri Riusciti"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr "Pubblica"

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr "Accessibile a"

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "Interrompi"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
msgid "Retry"
msgstr "Riprova"

#: pynicotine/gtkgui/dialogs/preferences.py:828
msgid "Round Robin"
msgstr "Round Robin"

#: pynicotine/gtkgui/dialogs/preferences.py:829
msgid "First In, First Out"
msgstr "Primo dentro, primo fuori"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
msgid "Username"
msgstr "Nome utente"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
msgid "IP Address"
msgstr "Indirizzo IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
msgid "Ignore User"
msgstr "Ignora Utente"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
msgid "Enter the name of the user you want to ignore:"
msgstr "Inserisci il nome dell'utente che desideri ignorare:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
msgid "Ignore IP Address"
msgstr "Ignora Indirizzo IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
msgid "Enter an IP address you want to ignore:"
msgstr "Inserisci un indirizzo IP che desideri ignorare:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "* is a wildcard"
msgstr "* è un carattere jolly"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
msgid "Ban User"
msgstr "Blocca Utente"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
msgid "Enter the name of the user you want to ban:"
msgstr "Inserisci il nome dell'utente che desideri bloccare:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
msgid "Ban IP Address"
msgstr "Banna indirizzo IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "Enter an IP address you want to ban:"
msgstr "Inserisci un indirizzo IP che desideri bloccare:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr "Formato dei codici"

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
msgid "Pattern"
msgstr "Modello"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
msgid "Replacement"
msgstr "Sostituzione"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
msgid "Censor Pattern"
msgstr "Modello di Censura"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Inserisci un modello che desideri censurare. Aggiungi spazi attorno alle "
"parole se non desideri corrispondenze con stringhe interne alle parole: "
"(potrebbe fallire all'inizio e alla fine delle linee)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
msgid "Edit Censored Pattern"
msgstr "Modifica corrispondenze censurate"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
msgid "Add Replacement"
msgstr "Aggiungi sostituzione"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
msgid "Enter a text pattern and what to replace it with:"
msgstr "Immetti un modello di testo e la sostituzione:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
msgid "Edit Replacement"
msgstr "Modifica sostituzione"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
msgid "System default"
msgstr "Predefinito di sistema"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
msgid "Show confirmation dialog"
msgstr "Mostra finestra di conferma"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
msgid "Run in the background"
msgstr "Esegui in background"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
msgid "bold"
msgstr "grassetto"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
msgid "italic"
msgstr "corsivo"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
msgid "underline"
msgstr "sottolineato"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
msgid "normal"
msgstr "normale"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
msgid "Separate Buddies tab"
msgstr "Scheda Amici separata"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
msgid "Sidebar in Chat Rooms tab"
msgstr "Barra laterale nella scheda delle Chat Room"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr "Barra laterale sempre visibile"

#: pynicotine/gtkgui/dialogs/preferences.py:1777
msgid "Top"
msgstr "Sopra"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
msgid "Bottom"
msgstr "Sotto"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
msgid "Left"
msgstr "Sinistra"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
msgid "Right"
msgstr "Destra"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "Connesso"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Assente"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Disconnesso"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
msgid "Tab Changed"
msgstr "Scheda Cambiata"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
msgid "Tab Highlight"
msgstr "Evidenziazione Scheda"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
msgid "Window"
msgstr "Finestra"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
msgid "Online (Tray)"
msgstr "Connesso (Icona)"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
msgid "Away (Tray)"
msgstr "Assente (Vassoio)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
msgid "Offline (Tray)"
msgstr "Disconnesso (Icona)"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
msgid "Message (Tray)"
msgstr "Messaggio (Vassoio)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Protocollo"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
msgid "Command"
msgstr "Comando"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
msgid "Add URL Handler"
msgstr "Aggiungi Gestore URL"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Immetti il protocollo e il comando per il gestore URL:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
msgid "Edit Command"
msgstr "Modifica comando"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Inserisci un nuovo comando per il protocollo '%s':"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
msgid "Username;APIKEY"
msgstr "Nome Utente;APIKEY"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr ""
"Player musicale (ad es. Amarok, Audacious, Exaile); lascia vuoto per "
"rilevarlo automaticamente:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
msgid "Username: "
msgstr "Nome utente: "

#: pynicotine/gtkgui/dialogs/preferences.py:2767
msgid "Command:"
msgstr "Comando:"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
msgid "Title"
msgstr "Titolo"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Sta Ascoltando (di solito \"%(artist)s - %(title)s\")"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
msgid "Artist"
msgstr "Artista"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
msgid "Duration"
msgstr "Durata"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "Bitrate"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
msgid "Comment"
msgstr "Commento"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
msgid "Album"
msgstr "Album"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
msgid "Track Number"
msgstr "Numero Traccia"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
msgid "Year"
msgstr "Anno"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
msgid "Filename (URI)"
msgstr "Nome file (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
msgid "Program"
msgstr "Programma"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
msgid "Enabled"
msgstr "Attivo"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
msgid "Plugin"
msgstr "Estensione"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
msgid "No Plugin Selected"
msgstr "Nessuna Estensione Selezionata"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
msgid "Preferences"
msgstr "Preferenze"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
msgid "Network"
msgstr "Rete"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
msgid "User Interface"
msgstr "Interfaccia Utente"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Condivisi"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Upload"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Ricerche"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
msgid "User Profile"
msgstr "Profilo Utente"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
msgid "Chats"
msgstr "Chat"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
msgid "Now Playing"
msgstr "Sta Ascoltando"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Registro"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
msgid "Banned Users"
msgstr "Utenti Bloccati"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
msgid "Ignored Users"
msgstr "Utenti Ignorati"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
msgid "URL Handlers"
msgstr "Associazione URL"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr "Estensioni"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
msgid "Pick a File Name for Config Backup"
msgstr "Scegli un Nome File per il Backup di Configurazione"

#: pynicotine/gtkgui/dialogs/statistics.py:75
msgid "Transfer Statistics"
msgstr "Statistiche trasferimento"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, python-format
msgid "Total Since %(date)s"
msgstr "Totale Da %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
msgid "Reset Transfer Statistics?"
msgstr "Azzerare le Statistiche di Trasferimento?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
msgid "Do you really want to reset transfer statistics?"
msgstr "Sei sicuro di voler azzerare le statistiche di trasferimento?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
msgid "Wishlist"
msgstr "Lista desideri"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
msgid "Wish"
msgstr "Desideri"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
msgid "_Search for Item"
msgstr "Cerca Elemento"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
msgid "Edit Wish"
msgstr "Modifica Desiderio"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, python-format
msgid "Enter new value for wish '%s':"
msgstr "Inserisci nuovo valore per desiderio'%s':"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
msgid "Clear Wishlist?"
msgstr "Pulire la Lista dei Desideri?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
msgid "Do you really want to clear your wishlist?"
msgstr "Sei sicuro di voler pulire la tua lista dei desideri?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Percorso"

#: pynicotine/gtkgui/downloads.py:47
msgid "_Resume"
msgstr "_Riprendi"

#: pynicotine/gtkgui/downloads.py:48
msgid "P_ause"
msgstr "P_ausa"

#: pynicotine/gtkgui/downloads.py:72
msgid "Finished / Filtered"
msgstr "Completati / Filtrati"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Completato"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
msgid "Paused"
msgstr "In Pausa"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
msgid "Filtered"
msgstr "Filtrato"

#: pynicotine/gtkgui/downloads.py:77
msgid "Deleted"
msgstr "Eliminato"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
msgid "Queued…"
msgstr "In Coda…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
msgid "Everything…"
msgstr "Tutto…"

#: pynicotine/gtkgui/downloads.py:132
#, python-format
msgid "Downloads: %(speed)s"
msgstr "Download: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
msgid "Clear Queued Downloads"
msgstr "Pulire Tutti i Download Accodati"

#: pynicotine/gtkgui/downloads.py:139
msgid "Do you really want to clear all queued downloads?"
msgstr "Sei sicuro di voler pulire tutti i download accodati?"

#: pynicotine/gtkgui/downloads.py:151
msgid "Clear All Downloads"
msgstr "Pulisci Tutti i Download"

#: pynicotine/gtkgui/downloads.py:152
msgid "Do you really want to clear all downloads?"
msgstr "Sei sicuro di voler pulire tutti i download?"

#: pynicotine/gtkgui/downloads.py:169
#, python-format
msgid "Download %(num)i files?"
msgstr "Scaricare %(num)i file?"

#: pynicotine/gtkgui/downloads.py:170
#, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"Sei sicuro di voler scaricare %(num)i file dalla cartella %(folder)s "
"dell'utente %(user)s?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
msgid "_Download Folder"
msgstr "Scarica Cartella"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
msgid "Likes"
msgstr "Mi piace"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
msgid "Dislikes"
msgstr "Non mi piace"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Valutazione"

#: pynicotine/gtkgui/interests.py:111
msgid "Item"
msgstr "Elemento"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
msgid "_Recommendations for Item"
msgstr "_Consigli per l' Elemento"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
msgid "I _Like This"
msgstr "Mi Piace Questo"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
msgid "I _Dislike This"
msgstr "Non Mi Piace Questo"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
msgid "Recommendations"
msgstr "Consigliati"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
msgid "Similar Users"
msgstr "Utenti Simili"

#: pynicotine/gtkgui/interests.py:427
#, python-format
msgid "Recommendations (%s)"
msgstr "Consigliati (%s)"

#: pynicotine/gtkgui/interests.py:451
#, python-format
msgid "Similar Users (%s)"
msgstr "Utenti Simili (%s)"

#: pynicotine/gtkgui/mainwindow.py:238
msgid "Search log…"
msgstr "Cerca nel registro…"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, python-format
msgid "Private Message from %(user)s"
msgstr "Messaggio Privato da %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
msgid "Wishlist Results Found"
msgstr "Risultati Della Lista Dei Desideri Trovati"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
msgid "User Profiles"
msgstr "Profili Utente"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
msgid "Chat Rooms"
msgstr "Canali"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "Interessi"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
msgid "Chat"
msgstr "Chat"

#: pynicotine/gtkgui/mainwindow.py:1111
msgid "[Debug] Connections"
msgstr "[Debug] Connessioni"

#: pynicotine/gtkgui/mainwindow.py:1112
msgid "[Debug] Messages"
msgstr "[Debug] Messaggi"

#: pynicotine/gtkgui/mainwindow.py:1113
msgid "[Debug] Transfers"
msgstr "[Debug] Trasferimenti"

#: pynicotine/gtkgui/mainwindow.py:1114
msgid "[Debug] Miscellaneous"
msgstr "[Debug] Varie"

#: pynicotine/gtkgui/mainwindow.py:1119
msgid "_Find…"
msgstr "_Cerca…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
msgid "_Copy"
msgstr "_Copia"

#: pynicotine/gtkgui/mainwindow.py:1122
msgid "Copy _All"
msgstr "Copia Tutto"

#: pynicotine/gtkgui/mainwindow.py:1127
msgid "View _Debug Logs"
msgstr "Visualizza i _Debug Logs"

#: pynicotine/gtkgui/mainwindow.py:1128
msgid "View _Transfer Logs"
msgstr "Visualizza _Log di Trasferimento"

#: pynicotine/gtkgui/mainwindow.py:1132
msgid "_Log Categories"
msgstr "Registro Categorie"

#: pynicotine/gtkgui/mainwindow.py:1134
msgid "Clear Log View"
msgstr "Pulisci Vista Registro"

#: pynicotine/gtkgui/mainwindow.py:1199
msgid "Preparing Shares"
msgstr "Preparazione dei Condividi"

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr "Indicizzazione Condivisi"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
msgid "Shared Folders"
msgstr "Cartelle Condivise"

#: pynicotine/gtkgui/popovers/chathistory.py:77
msgid "Latest Message"
msgstr "Ultimo Messaggio"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Canale"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Utenti"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
msgid "Join Room"
msgstr "Entra nel Canale"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
msgid "Leave Room"
msgstr "Abbandona Canale"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
msgid "Disown Private Room"
msgstr "Abbandona Canale Privato"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
msgid "Cancel Room Membership"
msgstr "Elimina Appartenenza a Canale"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
msgid "Close All Tabs…"
msgstr "Chiudere Tutte le Schede…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
msgid "_Close Tab"
msgstr "_Chiudi Scheda"

#: pynicotine/gtkgui/privatechat.py:379
msgid "View Chat Log"
msgstr "Mostra Registro Chat"

#: pynicotine/gtkgui/privatechat.py:382
msgid "Delete Chat Log…"
msgstr "Elimina Registro Chat…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
msgid "User Actions"
msgstr "Azioni Utente"

#: pynicotine/gtkgui/privatechat.py:477
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Sei sicuro di voler eliminare definitivamente tutti i messaggi registrati "
"per questo utente?"

#: pynicotine/gtkgui/privatechat.py:528
msgid "* Messages sent while you were offline"
msgstr "* Messaggi inviati mentre eri disconnesso"

#: pynicotine/gtkgui/search.py:90
msgid "_Global"
msgstr "_Globale"

#: pynicotine/gtkgui/search.py:91
msgid "_Buddies"
msgstr "_Amici"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
msgid "_Rooms"
msgstr "Canali"

#: pynicotine/gtkgui/search.py:93
msgid "_User"
msgstr "_Utente"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
msgid "In Queue"
msgstr "In Coda"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
msgid "File Type"
msgstr "Tipo di File"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Nome file"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Dimensione"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
msgid "Quality"
msgstr "Qualità"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
msgid "Copy _File Path"
msgstr "Copia Percorso _File"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "Copia _URL"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
msgid "Copy Folder U_RL"
msgstr "Copia U_RL Cartella"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
msgid "_Download File(s)"
msgstr "Scarica File"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
msgid "Download File(s) _To…"
msgstr "Scarica File In…"

#: pynicotine/gtkgui/search.py:615
msgid "Download _Folder(s)"
msgstr "Scarica Cartella(e)"

#: pynicotine/gtkgui/search.py:616
msgid "Download F_older(s) To…"
msgstr "Scarica Cartella(e) In…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
msgid "View User _Profile"
msgstr "Vedi _Profilo Utente"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
msgid "_Browse Folder"
msgstr "_Sfoglia Cartella"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
msgid "F_ile Properties"
msgstr "Proprietà F_ile"

#: pynicotine/gtkgui/search.py:629
msgid "Copy Search Term"
msgstr "Copia Termine di Ricerca"

#: pynicotine/gtkgui/search.py:631
msgid "Clear All Results"
msgstr "Pulisci Tutti i Risultati"

#: pynicotine/gtkgui/search.py:718
msgid "Clear Filters"
msgstr "Rimuovi Filtri"

#: pynicotine/gtkgui/search.py:721
msgid "Restore Filters"
msgstr "Ripristina Filtri"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, python-format
msgid "[PRIVATE]  %s"
msgstr "[PRIVATO]  %s"

#: pynicotine/gtkgui/search.py:1273
#, python-format
msgid "_Result Filters [%d]"
msgstr "Filtri _Risultato [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
msgid "_Result Filters"
msgstr "Filtri _Risultato"

#: pynicotine/gtkgui/search.py:1277
#, python-format
msgid "%d active filter(s)"
msgstr "%d filtro(i) attivo(i)"

#: pynicotine/gtkgui/search.py:1329
msgid "Add Wi_sh"
msgstr "Aggiungi De_siderio"

#: pynicotine/gtkgui/search.py:1332
msgid "Remove Wi_sh"
msgstr "Rimuovi De_siderio"

#: pynicotine/gtkgui/search.py:1349
msgid "Select User's Results"
msgstr "Seleziona Risultati Utente"

#: pynicotine/gtkgui/search.py:1472
#, python-format
msgid "Total: %s"
msgstr "Totale: %s"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
msgid "Results"
msgstr "Risultati"

#: pynicotine/gtkgui/search.py:1590
msgid "Select Destination Folder for File(s)"
msgstr "Seleziona Cartella Destinazione per File"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
msgid "Select Destination Folder"
msgstr "Seleziona Cartella Destinazione"

#: pynicotine/gtkgui/transfers.py:61
msgid "Queued"
msgstr "In coda"

#: pynicotine/gtkgui/transfers.py:62
msgid "Queued (prioritized)"
msgstr "In Coda (con priorità)"

#: pynicotine/gtkgui/transfers.py:63
msgid "Queued (privileged)"
msgstr "In Coda (con privilegi)"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Ricezione dello stato"

#: pynicotine/gtkgui/transfers.py:65
msgid "Transferring"
msgstr "In trasferimento"

#: pynicotine/gtkgui/transfers.py:66
msgid "Connection closed"
msgstr "Connessione chiusa"

#: pynicotine/gtkgui/transfers.py:67
msgid "Connection timeout"
msgstr "Scadenza connessione"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Utente uscito"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
msgid "Cancelled"
msgstr "Annullato"

#: pynicotine/gtkgui/transfers.py:73
msgid "Download folder error"
msgstr "Errore download cartella"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Errore di file locale"

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr "Bloccati"

#: pynicotine/gtkgui/transfers.py:76
msgid "File not shared"
msgstr "File non condiviso"

#: pynicotine/gtkgui/transfers.py:77
msgid "Pending shutdown"
msgstr "In attesa di spegnimento"

#: pynicotine/gtkgui/transfers.py:78
msgid "File read error"
msgstr "Errore di lettura del file"

#: pynicotine/gtkgui/transfers.py:182
msgid "Queue"
msgstr "Coda"

#: pynicotine/gtkgui/transfers.py:188
msgid "Percent"
msgstr "Percentuale"

#: pynicotine/gtkgui/transfers.py:208
msgid "Time Elapsed"
msgstr "Tempo Trascorso"

#: pynicotine/gtkgui/transfers.py:215
msgid "Time Left"
msgstr "Tempo Rimasto"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
msgid "_Open File"
msgstr "_Apri File"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
msgid "Open in File _Manager"
msgstr "Apri nel Gestore dei File"

#: pynicotine/gtkgui/transfers.py:286
msgid "_Search"
msgstr "Ricerca"

#: pynicotine/gtkgui/transfers.py:289
msgid "Clear All"
msgstr "Pulisci Tutto"

#: pynicotine/gtkgui/transfers.py:953
msgid "Select User's Transfers"
msgstr "Seleziona Trasferimenti dell'Utente"

#: pynicotine/gtkgui/uploads.py:50
msgid "_Abort"
msgstr "_Annulla"

#: pynicotine/gtkgui/uploads.py:74
msgid "Finished / Cancelled / Failed"
msgstr "Completati / Interrotti / Falliti"

#: pynicotine/gtkgui/uploads.py:75
msgid "Finished / Cancelled"
msgstr "Completati / Interrotti"

#: pynicotine/gtkgui/uploads.py:79
msgid "Failed"
msgstr "Falliti"

#: pynicotine/gtkgui/uploads.py:80
msgid "User Logged Off"
msgstr "L'Utente Si È Disconnesso"

#: pynicotine/gtkgui/uploads.py:142
#, python-format
msgid "Uploads: %(speed)s"
msgstr "Upload: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr "Smettila..."

#: pynicotine/gtkgui/uploads.py:164
msgid "Clear Queued Uploads"
msgstr "Pulire Tutti gli Upload in Coda"

#: pynicotine/gtkgui/uploads.py:165
msgid "Do you really want to clear all queued uploads?"
msgstr "Sei sicuro di voler pulire tutti gli upload accodati?"

#: pynicotine/gtkgui/uploads.py:177
msgid "Clear All Uploads"
msgstr "Pulisci Tutti gli Upload"

#: pynicotine/gtkgui/uploads.py:178
msgid "Do you really want to clear all uploads?"
msgstr "Sei sicuro di voler pulire tutti gli upload?"

#: pynicotine/gtkgui/userbrowse.py:282
msgid "_Save Shares List to Disk"
msgstr "_Salva Lista Condivisi su Disco"

#: pynicotine/gtkgui/userbrowse.py:292
msgid "Upload Folder & Subfolders…"
msgstr "Carica Cartella & Sottocartelle…"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
msgid "Copy _Folder Path"
msgstr "Copia Percorso Cartella"

#: pynicotine/gtkgui/userbrowse.py:309
#, fuzzy
msgid "_Download Folder & Subfolders"
msgstr "Scarica Cartella & Sottocartelle"

#: pynicotine/gtkgui/userbrowse.py:310
#, fuzzy
msgid "Download Folder & Subfolders _To…"
msgstr "Scarica Cartella & Sottocartelle In…"

#: pynicotine/gtkgui/userbrowse.py:334
msgid "File Name"
msgstr "Nome File"

#: pynicotine/gtkgui/userbrowse.py:373
msgid "Up_load File(s)…"
msgstr "Carica Fi_le…"

#: pynicotine/gtkgui/userbrowse.py:374
msgid "Upload Folder…"
msgstr "Carica Cartella…"

#: pynicotine/gtkgui/userbrowse.py:396
msgid "Download Folder _To…"
msgstr "Scarica Cartella In…"

#: pynicotine/gtkgui/userbrowse.py:600
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"Lista dei file condivisi dall'utente vuota. O l'utente non condivide nulla o "
"condivide file in privato."

#: pynicotine/gtkgui/userbrowse.py:615
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Impossibile richiedere i file condivisi dall'utente. O l'utente è "
"disconnesso o entrambi avete una porta di ascolto chiusa o c'è un problema "
"di connettività temporaneo."

#: pynicotine/gtkgui/userbrowse.py:953
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Seleziona Destinazione per Download Cartelle Multiple"

#: pynicotine/gtkgui/userbrowse.py:997
msgid "Upload Folder (with Subfolders) To User"
msgstr "Carica Cartella (con Sotto-cartelle) Verso Utente"

#: pynicotine/gtkgui/userbrowse.py:999
msgid "Upload Folder To User"
msgstr "Carica Cartella Verso Utente"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
msgid "Enter the name of the user you want to upload to:"
msgstr "Inserisci il nome dell'utente verso cui fare upload:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
msgid "_Upload"
msgstr "_Upload"

#: pynicotine/gtkgui/userbrowse.py:1139
msgid "Select Destination Folder for Files"
msgstr "Seleziona Cartella di Destinazione per i File"

#: pynicotine/gtkgui/userbrowse.py:1161
msgid "Upload File(s) To User"
msgstr "Upload File verso Utente"

#: pynicotine/gtkgui/userinfo.py:376
msgid "Copy Picture"
msgstr "Copia Immagine"

#: pynicotine/gtkgui/userinfo.py:377
msgid "Save Picture"
msgstr "Salva immagine"

#: pynicotine/gtkgui/userinfo.py:468
#, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Errore nel caricamento dell'immagine per l'utente %(user)s: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Impossibile ottenere dettagli dall'utente. Entrambi avete una porta di "
"ascolto chiusa, l'utente è disconnesso o c'è un problema di connettività "
"temporaneo."

#: pynicotine/gtkgui/userinfo.py:577
msgid "Remove _Buddy"
msgstr "Rimuovi _Amico"

#: pynicotine/gtkgui/userinfo.py:577
msgid "Add _Buddy"
msgstr "Aggiungi _Amico"

#: pynicotine/gtkgui/userinfo.py:581
msgid "Unban User"
msgstr "Sblocca Utente"

#: pynicotine/gtkgui/userinfo.py:585
msgid "Unignore User"
msgstr "Annulla Ignora Utente"

#: pynicotine/gtkgui/userinfo.py:613
msgid "Yes"
msgstr "Si"

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr "No"

#: pynicotine/gtkgui/userinfo.py:773
msgid "Please enter number of days."
msgstr "Per favore inserisci il numero di giorni."

#: pynicotine/gtkgui/userinfo.py:787
#, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Regala giorni dei tuoi privilegi Soulseek all'utente %(user)s "
"(%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, python-format
msgid "%(days)s days left"
msgstr "%(days)s giorni rimanenti"

#: pynicotine/gtkgui/userinfo.py:795
msgid "Gift Privileges"
msgstr "Regala Privilegi"

#: pynicotine/gtkgui/userinfo.py:797
msgid "_Give Privileges"
msgstr "_Concedi privilegi"

#: pynicotine/gtkgui/widgets/dialogs.py:309
msgid "Close"
msgstr "Chiudi"

#: pynicotine/gtkgui/widgets/dialogs.py:488
msgid "_Yes"
msgstr "_Sì"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
msgid "_OK"
msgstr "_OK"

#: pynicotine/gtkgui/widgets/filechooser.py:39
msgid "Select a File"
msgstr "Seleziona un File"

#: pynicotine/gtkgui/widgets/filechooser.py:174
msgid "Select a Folder"
msgstr "Seleziona una Cartella"

#: pynicotine/gtkgui/widgets/filechooser.py:179
msgid "_Select"
msgstr "_Seleziona"

#: pynicotine/gtkgui/widgets/filechooser.py:196
msgid "Select an Image"
msgstr "Seleziona un'Immagine"

#: pynicotine/gtkgui/widgets/filechooser.py:203
msgid "All images"
msgstr "Tutte le immagini"

#: pynicotine/gtkgui/widgets/filechooser.py:241
msgid "Save as…"
msgstr "Salva come…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
msgid "(None)"
msgstr "(Nulla)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
msgid "Close Tab"
msgstr "Chiudi Scheda"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
msgid "Close All Tabs?"
msgstr "Chiudere Tutte le Schede?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
msgid "Do you really want to close all tabs?"
msgstr "Sei sicuro di voler chiudere tutte le schede?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, python-format
msgid "%i Unread Tab(s)"
msgstr "%i Scheda(e) non letta(e)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
msgid "All Tabs"
msgstr "Tutte le Schede"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
msgid "Re_open Closed Tab"
msgstr "Riapri la scheda chiusa"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, python-format
msgid "%s File(s) Selected"
msgstr "%s File Selezionati"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
msgid "_Browse Files"
msgstr "Sfoglia File"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
msgid "_Add Buddy"
msgstr "_Aggiungi amico"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
msgid "Show IP A_ddress"
msgstr "Mostra Indirizzo IP"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
msgid "Private Rooms"
msgstr "Canali Privati"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, python-format
msgid "Remove from Private Room %s"
msgstr "Rimuovi dalla Chat Privata %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, python-format
msgid "Add to Private Room %s"
msgstr "Aggiungi alla Chat Privata %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, python-format
msgid "Remove as Operator of %s"
msgstr "Rimuovi come Operatore di %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, python-format
msgid "Add as Operator of %s"
msgstr "Aggiungi come Operatore di %s"

#: pynicotine/gtkgui/widgets/textentry.py:37
msgid "Send message…"
msgstr "Invia messaggio…"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
msgid "Find Previous Match"
msgstr "Cerca Corrispondenza Precedente"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
msgid "Find Next Match"
msgstr "Cerca Corrispondenza Successiva"

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr "--- vecchi messaggi sopra ---"

#: pynicotine/gtkgui/widgets/theme.py:243
msgid "Executable"
msgstr "Eseguibile"

#: pynicotine/gtkgui/widgets/theme.py:244
msgid "Audio"
msgstr "Audio"

#: pynicotine/gtkgui/widgets/theme.py:245
msgid "Image"
msgstr "Immagine"

#: pynicotine/gtkgui/widgets/theme.py:246
msgid "Archive"
msgstr "Archivio"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
msgid "Miscellaneous"
msgstr "Varie"

#: pynicotine/gtkgui/widgets/theme.py:248
msgid "Video"
msgstr "Video"

#: pynicotine/gtkgui/widgets/theme.py:249
msgid "Document"
msgstr "Documento"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr "Testo"

#: pynicotine/gtkgui/widgets/theme.py:357
#, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "Errore di caricamento dell'icona personalizzata %(path)s: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Hide Nicotine+"
msgstr "Nascondi Nicotine+"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Show Nicotine+"
msgstr "Mostra Nicotine+"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, python-format
msgid "Column #%i"
msgstr "Colonna #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
msgid "Ungrouped"
msgstr "Non raggruppare"

#: pynicotine/gtkgui/widgets/treeview.py:960
msgid "Group by Folder"
msgstr "Raggruppa per Cartella"

#: pynicotine/gtkgui/widgets/treeview.py:963
msgid "Group by User"
msgstr "Raggruppa per Utente"

#: pynicotine/headless/application.py:77
#, python-format
msgid "Do you really want to exit? %s"
msgstr "Vuoi davvero uscire? %s"

#: pynicotine/headless/application.py:81
#, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr "L'utente %s esiste già e la password che hai inserito non è valida."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr "Digitare %s per accedere con un altro nome utente o password."

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
msgid "Password: "
msgstr "Password: "

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Per creare un nuovo account Soulseek, inserisci il nome utente e la password "
"che desideri. Se hai già un account, inserisci i dettagli di accesso."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr "Le seguenti azioni non sono disponibili:"

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr "Riprovare l'indicizzazione? %s"

#: pynicotine/logfacility.py:181
#, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr "Impossibile scrivere su file registro \"%(filename)s\": %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Impossibile accedere al file log %(path)s: %(error)s"

#: pynicotine/networkfilter.py:40
msgid "Andorra"
msgstr "Andorra"

#: pynicotine/networkfilter.py:41
msgid "United Arab Emirates"
msgstr "Emirati Arabi Uniti"

#: pynicotine/networkfilter.py:42
msgid "Afghanistan"
msgstr "Afghanistan"

#: pynicotine/networkfilter.py:43
msgid "Antigua & Barbuda"
msgstr "Antigua e Barbuda"

#: pynicotine/networkfilter.py:44
msgid "Anguilla"
msgstr "Anguilla"

#: pynicotine/networkfilter.py:45
msgid "Albania"
msgstr "Albania"

#: pynicotine/networkfilter.py:46
msgid "Armenia"
msgstr "Armenia"

#: pynicotine/networkfilter.py:47
msgid "Angola"
msgstr "Angola"

#: pynicotine/networkfilter.py:48
msgid "Antarctica"
msgstr "Antartide"

#: pynicotine/networkfilter.py:49
msgid "Argentina"
msgstr "Argentina"

#: pynicotine/networkfilter.py:50
msgid "American Samoa"
msgstr "Samoa Americane"

#: pynicotine/networkfilter.py:51
msgid "Austria"
msgstr "Austria"

#: pynicotine/networkfilter.py:52
msgid "Australia"
msgstr "Australia"

#: pynicotine/networkfilter.py:53
msgid "Aruba"
msgstr "Aruba"

#: pynicotine/networkfilter.py:54
msgid "Åland Islands"
msgstr "Isole Åland"

#: pynicotine/networkfilter.py:55
msgid "Azerbaijan"
msgstr "Azerbaijan"

#: pynicotine/networkfilter.py:56
msgid "Bosnia & Herzegovina"
msgstr "Bosnia ed Erzegovina"

#: pynicotine/networkfilter.py:57
msgid "Barbados"
msgstr "Barbados"

#: pynicotine/networkfilter.py:58
msgid "Bangladesh"
msgstr "Bangladesh"

#: pynicotine/networkfilter.py:59
msgid "Belgium"
msgstr "Belgio"

#: pynicotine/networkfilter.py:60
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: pynicotine/networkfilter.py:61
msgid "Bulgaria"
msgstr "Bulgaria"

#: pynicotine/networkfilter.py:62
msgid "Bahrain"
msgstr "Bahrein"

#: pynicotine/networkfilter.py:63
msgid "Burundi"
msgstr "Burundi"

#: pynicotine/networkfilter.py:64
msgid "Benin"
msgstr "Benin"

#: pynicotine/networkfilter.py:65
msgid "Saint Barthelemy"
msgstr "Saint-Barthélemy"

#: pynicotine/networkfilter.py:66
msgid "Bermuda"
msgstr "Bermuda"

#: pynicotine/networkfilter.py:67
msgid "Brunei Darussalam"
msgstr "Brunei Darussalam"

#: pynicotine/networkfilter.py:68
msgid "Bolivia"
msgstr "Bolivia"

#: pynicotine/networkfilter.py:69
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius e Saba"

#: pynicotine/networkfilter.py:70
msgid "Brazil"
msgstr "Brasile"

#: pynicotine/networkfilter.py:71
msgid "Bahamas"
msgstr "Bahamas"

#: pynicotine/networkfilter.py:72
msgid "Bhutan"
msgstr "Bhutan"

#: pynicotine/networkfilter.py:73
msgid "Bouvet Island"
msgstr "Isola Bouvet"

#: pynicotine/networkfilter.py:74
msgid "Botswana"
msgstr "Botswana"

#: pynicotine/networkfilter.py:75
msgid "Belarus"
msgstr "Bielorussia"

#: pynicotine/networkfilter.py:76
msgid "Belize"
msgstr "Belize"

#: pynicotine/networkfilter.py:77
msgid "Canada"
msgstr "Canada"

#: pynicotine/networkfilter.py:78
msgid "Cocos (Keeling) Islands"
msgstr "Isole Cocos (Keeling)"

#: pynicotine/networkfilter.py:79
msgid "Democratic Republic of Congo"
msgstr "Repubblica Democratica del Congo"

#: pynicotine/networkfilter.py:80
msgid "Central African Republic"
msgstr "Repubblica Centrafricana"

#: pynicotine/networkfilter.py:81
msgid "Congo"
msgstr "Congo"

#: pynicotine/networkfilter.py:82
msgid "Switzerland"
msgstr "Svizzera"

#: pynicotine/networkfilter.py:83
msgid "Ivory Coast"
msgstr "Costa d'Avorio"

#: pynicotine/networkfilter.py:84
msgid "Cook Islands"
msgstr "Isole Cook"

#: pynicotine/networkfilter.py:85
msgid "Chile"
msgstr "Cile"

#: pynicotine/networkfilter.py:86
msgid "Cameroon"
msgstr "Camerun"

#: pynicotine/networkfilter.py:87
msgid "China"
msgstr "Cina"

#: pynicotine/networkfilter.py:88
msgid "Colombia"
msgstr "Colombia"

#: pynicotine/networkfilter.py:89
msgid "Costa Rica"
msgstr "Costa Rica"

#: pynicotine/networkfilter.py:90
msgid "Cuba"
msgstr "Cuba"

#: pynicotine/networkfilter.py:91
msgid "Cabo Verde"
msgstr "Capo Verde"

#: pynicotine/networkfilter.py:92
msgid "Curaçao"
msgstr "Curaçao"

#: pynicotine/networkfilter.py:93
msgid "Christmas Island"
msgstr "Isola di Natale"

#: pynicotine/networkfilter.py:94
msgid "Cyprus"
msgstr "Cipro"

#: pynicotine/networkfilter.py:95
msgid "Czechia"
msgstr "Cechia"

#: pynicotine/networkfilter.py:96
msgid "Germany"
msgstr "Germania"

#: pynicotine/networkfilter.py:97
msgid "Djibouti"
msgstr "Gibuti"

#: pynicotine/networkfilter.py:98
msgid "Denmark"
msgstr "Danimarca"

#: pynicotine/networkfilter.py:99
msgid "Dominica"
msgstr "Dominica"

#: pynicotine/networkfilter.py:100
msgid "Dominican Republic"
msgstr "Repubblica Dominicana"

#: pynicotine/networkfilter.py:101
msgid "Algeria"
msgstr "Algeria"

#: pynicotine/networkfilter.py:102
msgid "Ecuador"
msgstr "Ecuador"

#: pynicotine/networkfilter.py:103
msgid "Estonia"
msgstr "Estonia"

#: pynicotine/networkfilter.py:104
msgid "Egypt"
msgstr "Egitto"

#: pynicotine/networkfilter.py:105
msgid "Western Sahara"
msgstr "Sahara Occidentale"

#: pynicotine/networkfilter.py:106
msgid "Eritrea"
msgstr "Eritrea"

#: pynicotine/networkfilter.py:107
msgid "Spain"
msgstr "Spagna"

#: pynicotine/networkfilter.py:108
msgid "Ethiopia"
msgstr "Etiopia"

#: pynicotine/networkfilter.py:109
msgid "Europe"
msgstr "Europa"

#: pynicotine/networkfilter.py:110
msgid "Finland"
msgstr "Finlandia"

#: pynicotine/networkfilter.py:111
msgid "Fiji"
msgstr "Figi"

#: pynicotine/networkfilter.py:112
msgid "Falkland Islands (Malvinas)"
msgstr "Isole Falkland (Malvine)"

#: pynicotine/networkfilter.py:113
msgid "Micronesia"
msgstr "Stati Federati di Micronesia"

#: pynicotine/networkfilter.py:114
msgid "Faroe Islands"
msgstr "Isole Faroe"

#: pynicotine/networkfilter.py:115
msgid "France"
msgstr "Francia"

#: pynicotine/networkfilter.py:116
msgid "Gabon"
msgstr "Gabon"

#: pynicotine/networkfilter.py:117
msgid "Great Britain"
msgstr "Gran Bretagna"

#: pynicotine/networkfilter.py:118
msgid "Grenada"
msgstr "Grenada"

#: pynicotine/networkfilter.py:119
msgid "Georgia"
msgstr "Georgia"

#: pynicotine/networkfilter.py:120
msgid "French Guiana"
msgstr "Guyana Francese"

#: pynicotine/networkfilter.py:121
msgid "Guernsey"
msgstr "Guernsey"

#: pynicotine/networkfilter.py:122
msgid "Ghana"
msgstr "Ghana"

#: pynicotine/networkfilter.py:123
msgid "Gibraltar"
msgstr "Gibilterra"

#: pynicotine/networkfilter.py:124
msgid "Greenland"
msgstr "Groenlandia"

#: pynicotine/networkfilter.py:125
msgid "Gambia"
msgstr "Gambia"

#: pynicotine/networkfilter.py:126
msgid "Guinea"
msgstr "Guinea"

#: pynicotine/networkfilter.py:127
msgid "Guadeloupe"
msgstr "Guadalupa"

#: pynicotine/networkfilter.py:128
msgid "Equatorial Guinea"
msgstr "Guinea Equatoriale"

#: pynicotine/networkfilter.py:129
msgid "Greece"
msgstr "Grecia"

#: pynicotine/networkfilter.py:130
msgid "South Georgia & South Sandwich Islands"
msgstr "Georgia del Sud e Sandwich Australi"

#: pynicotine/networkfilter.py:131
msgid "Guatemala"
msgstr "Guatemala"

#: pynicotine/networkfilter.py:132
msgid "Guam"
msgstr "Guam"

#: pynicotine/networkfilter.py:133
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: pynicotine/networkfilter.py:134
msgid "Guyana"
msgstr "Guyana"

#: pynicotine/networkfilter.py:135
msgid "Hong Kong"
msgstr "Hong Kong"

#: pynicotine/networkfilter.py:136
msgid "Heard & McDonald Islands"
msgstr "Isola Heard e McDonald"

#: pynicotine/networkfilter.py:137
msgid "Honduras"
msgstr "Honduras"

#: pynicotine/networkfilter.py:138
msgid "Croatia"
msgstr "Croazia"

#: pynicotine/networkfilter.py:139
msgid "Haiti"
msgstr "Haiti"

#: pynicotine/networkfilter.py:140
msgid "Hungary"
msgstr "Ungheria"

#: pynicotine/networkfilter.py:141
msgid "Indonesia"
msgstr "Indonesia"

#: pynicotine/networkfilter.py:142
msgid "Ireland"
msgstr "Irlanda"

#: pynicotine/networkfilter.py:143
msgid "Israel"
msgstr "Israele"

#: pynicotine/networkfilter.py:144
msgid "Isle of Man"
msgstr "Isola di Man"

#: pynicotine/networkfilter.py:145
msgid "India"
msgstr "India"

#: pynicotine/networkfilter.py:146
msgid "British Indian Ocean Territory"
msgstr "Territori Britannici dell'Oceano Indiano"

#: pynicotine/networkfilter.py:147
msgid "Iraq"
msgstr "Iraq"

#: pynicotine/networkfilter.py:148
msgid "Iran"
msgstr "Iran"

#: pynicotine/networkfilter.py:149
msgid "Iceland"
msgstr "Islanda"

#: pynicotine/networkfilter.py:150
msgid "Italy"
msgstr "Italia"

#: pynicotine/networkfilter.py:151
msgid "Jersey"
msgstr "Jersey"

#: pynicotine/networkfilter.py:152
msgid "Jamaica"
msgstr "Giamaica"

#: pynicotine/networkfilter.py:153
msgid "Jordan"
msgstr "Giordania"

#: pynicotine/networkfilter.py:154
msgid "Japan"
msgstr "Giappone"

#: pynicotine/networkfilter.py:155
msgid "Kenya"
msgstr "Kenya"

#: pynicotine/networkfilter.py:156
msgid "Kyrgyzstan"
msgstr "Kirghizistan"

#: pynicotine/networkfilter.py:157
msgid "Cambodia"
msgstr "Cambogia"

#: pynicotine/networkfilter.py:158
msgid "Kiribati"
msgstr "Kiribati"

#: pynicotine/networkfilter.py:159
msgid "Comoros"
msgstr "Comore"

#: pynicotine/networkfilter.py:160
msgid "Saint Kitts & Nevis"
msgstr "Saint Kitts e Nevis"

#: pynicotine/networkfilter.py:161
msgid "North Korea"
msgstr "Corea del Nord"

#: pynicotine/networkfilter.py:162
msgid "South Korea"
msgstr "Corea del Sud"

#: pynicotine/networkfilter.py:163
msgid "Kuwait"
msgstr "Kuwait"

#: pynicotine/networkfilter.py:164
msgid "Cayman Islands"
msgstr "Isole Cayman"

#: pynicotine/networkfilter.py:165
msgid "Kazakhstan"
msgstr "Kazakistan"

#: pynicotine/networkfilter.py:166
msgid "Laos"
msgstr "Laos"

#: pynicotine/networkfilter.py:167
msgid "Lebanon"
msgstr "Libano"

#: pynicotine/networkfilter.py:168
msgid "Saint Lucia"
msgstr "Santa Lucia"

#: pynicotine/networkfilter.py:169
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: pynicotine/networkfilter.py:170
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: pynicotine/networkfilter.py:171
msgid "Liberia"
msgstr "Liberia"

#: pynicotine/networkfilter.py:172
msgid "Lesotho"
msgstr "Lesotho"

#: pynicotine/networkfilter.py:173
msgid "Lithuania"
msgstr "Lituania"

#: pynicotine/networkfilter.py:174
msgid "Luxembourg"
msgstr "Lussemburgo"

#: pynicotine/networkfilter.py:175
msgid "Latvia"
msgstr "Lettonia"

#: pynicotine/networkfilter.py:176
msgid "Libya"
msgstr "Libia"

#: pynicotine/networkfilter.py:177
msgid "Morocco"
msgstr "Marocco"

#: pynicotine/networkfilter.py:178
msgid "Monaco"
msgstr "Monaco"

#: pynicotine/networkfilter.py:179
msgid "Moldova"
msgstr "Moldavia"

#: pynicotine/networkfilter.py:180
msgid "Montenegro"
msgstr "Montenegro"

#: pynicotine/networkfilter.py:181
msgid "Saint Martin"
msgstr "Saint Martin"

#: pynicotine/networkfilter.py:182
msgid "Madagascar"
msgstr "Madagascar"

#: pynicotine/networkfilter.py:183
msgid "Marshall Islands"
msgstr "Isole Marshall"

#: pynicotine/networkfilter.py:184
msgid "North Macedonia"
msgstr "Macedonia del Nord"

#: pynicotine/networkfilter.py:185
msgid "Mali"
msgstr "Mali"

#: pynicotine/networkfilter.py:186
msgid "Myanmar"
msgstr "Myanmar"

#: pynicotine/networkfilter.py:187
msgid "Mongolia"
msgstr "Mongolia"

#: pynicotine/networkfilter.py:188
msgid "Macau"
msgstr "Macao"

#: pynicotine/networkfilter.py:189
msgid "Northern Mariana Islands"
msgstr "Isole Marianne Settentrionali"

#: pynicotine/networkfilter.py:190
msgid "Martinique"
msgstr "Martinica"

#: pynicotine/networkfilter.py:191
msgid "Mauritania"
msgstr "Mauritania"

#: pynicotine/networkfilter.py:192
msgid "Montserrat"
msgstr "Montserrat"

#: pynicotine/networkfilter.py:193
msgid "Malta"
msgstr "Malta"

#: pynicotine/networkfilter.py:194
msgid "Mauritius"
msgstr "Mauritius"

#: pynicotine/networkfilter.py:195
msgid "Maldives"
msgstr "Maldive"

#: pynicotine/networkfilter.py:196
msgid "Malawi"
msgstr "Malawi"

#: pynicotine/networkfilter.py:197
msgid "Mexico"
msgstr "Messico"

#: pynicotine/networkfilter.py:198
msgid "Malaysia"
msgstr "Malesia"

#: pynicotine/networkfilter.py:199
msgid "Mozambique"
msgstr "Mozambico"

#: pynicotine/networkfilter.py:200
msgid "Namibia"
msgstr "Namibia"

#: pynicotine/networkfilter.py:201
msgid "New Caledonia"
msgstr "Nuova Caledonia"

#: pynicotine/networkfilter.py:202
msgid "Niger"
msgstr "Niger"

#: pynicotine/networkfilter.py:203
msgid "Norfolk Island"
msgstr "Isola Norfolk"

#: pynicotine/networkfilter.py:204
msgid "Nigeria"
msgstr "Nigeria"

#: pynicotine/networkfilter.py:205
msgid "Nicaragua"
msgstr "Nicaragua"

#: pynicotine/networkfilter.py:206
msgid "Netherlands"
msgstr "Paesi Bassi"

#: pynicotine/networkfilter.py:207
msgid "Norway"
msgstr "Norvegia"

#: pynicotine/networkfilter.py:208
msgid "Nepal"
msgstr "Nepal"

#: pynicotine/networkfilter.py:209
msgid "Nauru"
msgstr "Nauru"

#: pynicotine/networkfilter.py:210
msgid "Niue"
msgstr "Niue"

#: pynicotine/networkfilter.py:211
msgid "New Zealand"
msgstr "Nuova Zelanda"

#: pynicotine/networkfilter.py:212
msgid "Oman"
msgstr "Oman"

#: pynicotine/networkfilter.py:213
msgid "Panama"
msgstr "Panama"

#: pynicotine/networkfilter.py:214
msgid "Peru"
msgstr "Peru"

#: pynicotine/networkfilter.py:215
msgid "French Polynesia"
msgstr "Polinesia Francese"

#: pynicotine/networkfilter.py:216
msgid "Papua New Guinea"
msgstr "Papua Nuova Guinea"

#: pynicotine/networkfilter.py:217
msgid "Philippines"
msgstr "Filippine"

#: pynicotine/networkfilter.py:218
msgid "Pakistan"
msgstr "Pakistan"

#: pynicotine/networkfilter.py:219
msgid "Poland"
msgstr "Polonia"

#: pynicotine/networkfilter.py:220
msgid "Saint Pierre & Miquelon"
msgstr "Saint-Pierre e Miquelon"

#: pynicotine/networkfilter.py:221
msgid "Pitcairn"
msgstr "Isole Pitcairn"

#: pynicotine/networkfilter.py:222
msgid "Puerto Rico"
msgstr "Portorico"

#: pynicotine/networkfilter.py:223
msgid "State of Palestine"
msgstr "Palestina"

#: pynicotine/networkfilter.py:224
msgid "Portugal"
msgstr "Portogallo"

#: pynicotine/networkfilter.py:225
msgid "Palau"
msgstr "Palau"

#: pynicotine/networkfilter.py:226
msgid "Paraguay"
msgstr "Paraguay"

#: pynicotine/networkfilter.py:227
msgid "Qatar"
msgstr "Qatar"

#: pynicotine/networkfilter.py:228
msgid "Réunion"
msgstr "Riunione"

#: pynicotine/networkfilter.py:229
msgid "Romania"
msgstr "Romania"

#: pynicotine/networkfilter.py:230
msgid "Serbia"
msgstr "Serbia"

#: pynicotine/networkfilter.py:231
msgid "Russia"
msgstr "Russia"

#: pynicotine/networkfilter.py:232
msgid "Rwanda"
msgstr "Ruanda"

#: pynicotine/networkfilter.py:233
msgid "Saudi Arabia"
msgstr "Arabia Saudita"

#: pynicotine/networkfilter.py:234
msgid "Solomon Islands"
msgstr "Isole Salomone"

#: pynicotine/networkfilter.py:235
msgid "Seychelles"
msgstr "Seychelles"

#: pynicotine/networkfilter.py:236
msgid "Sudan"
msgstr "Sudan"

#: pynicotine/networkfilter.py:237
msgid "Sweden"
msgstr "Svezia"

#: pynicotine/networkfilter.py:238
msgid "Singapore"
msgstr "Singapore"

#: pynicotine/networkfilter.py:239
msgid "Saint Helena"
msgstr "Sant'Elena"

#: pynicotine/networkfilter.py:240
msgid "Slovenia"
msgstr "Slovenia"

#: pynicotine/networkfilter.py:241
msgid "Svalbard & Jan Mayen Islands"
msgstr "Isole Svalbard e Jan Mayen"

#: pynicotine/networkfilter.py:242
msgid "Slovak Republic"
msgstr "Repubblica Slovacca"

#: pynicotine/networkfilter.py:243
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: pynicotine/networkfilter.py:244
msgid "San Marino"
msgstr "San Marino"

#: pynicotine/networkfilter.py:245
msgid "Senegal"
msgstr "Senegal"

#: pynicotine/networkfilter.py:246
msgid "Somalia"
msgstr "Somalia"

#: pynicotine/networkfilter.py:247
msgid "Suriname"
msgstr "Suriname"

#: pynicotine/networkfilter.py:248
msgid "South Sudan"
msgstr "Sudan del Sud"

#: pynicotine/networkfilter.py:249
msgid "Sao Tome & Principe"
msgstr "Sao Tome e Principe"

#: pynicotine/networkfilter.py:250
msgid "El Salvador"
msgstr "El Salvador"

#: pynicotine/networkfilter.py:251
msgid "Sint Maarten"
msgstr "San Martino"

#: pynicotine/networkfilter.py:252
msgid "Syria"
msgstr "Siria"

#: pynicotine/networkfilter.py:253
msgid "Eswatini"
msgstr "Eswatini"

#: pynicotine/networkfilter.py:254
msgid "Turks & Caicos Islands"
msgstr "Isole Turks e Caicos"

#: pynicotine/networkfilter.py:255
msgid "Chad"
msgstr "Ciad"

#: pynicotine/networkfilter.py:256
msgid "French Southern Territories"
msgstr "Terre Australi Francesi"

#: pynicotine/networkfilter.py:257
msgid "Togo"
msgstr "Togo"

#: pynicotine/networkfilter.py:258
msgid "Thailand"
msgstr "Tailandia"

#: pynicotine/networkfilter.py:259
msgid "Tajikistan"
msgstr "Tagikistan"

#: pynicotine/networkfilter.py:260
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
msgid "Timor-Leste"
msgstr "Timor Est"

#: pynicotine/networkfilter.py:262
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: pynicotine/networkfilter.py:263
msgid "Tunisia"
msgstr "Tunisia"

#: pynicotine/networkfilter.py:264
msgid "Tonga"
msgstr "Tonga"

#: pynicotine/networkfilter.py:265
msgid "Türkiye"
msgstr "Turchia"

#: pynicotine/networkfilter.py:266
msgid "Trinidad & Tobago"
msgstr "Trinidad e Tobago"

#: pynicotine/networkfilter.py:267
msgid "Tuvalu"
msgstr "Tuvalu"

#: pynicotine/networkfilter.py:268
msgid "Taiwan"
msgstr "Taiwan"

#: pynicotine/networkfilter.py:269
msgid "Tanzania"
msgstr "Tanzania"

#: pynicotine/networkfilter.py:270
msgid "Ukraine"
msgstr "Ucraina"

#: pynicotine/networkfilter.py:271
msgid "Uganda"
msgstr "Uganda"

#: pynicotine/networkfilter.py:272
msgid "U.S. Minor Outlying Islands"
msgstr "Isole Minori Esterne degli USA"

#: pynicotine/networkfilter.py:273
msgid "United States"
msgstr "Stati Uniti"

#: pynicotine/networkfilter.py:274
msgid "Uruguay"
msgstr "Uruguay"

#: pynicotine/networkfilter.py:275
msgid "Uzbekistan"
msgstr "Uzbekistan"

#: pynicotine/networkfilter.py:276
msgid "Holy See (Vatican City State)"
msgstr "Città del Vaticano"

#: pynicotine/networkfilter.py:277
msgid "Saint Vincent & The Grenadines"
msgstr "San Vincenzo e Grenadine"

#: pynicotine/networkfilter.py:278
msgid "Venezuela"
msgstr "Venezuela"

#: pynicotine/networkfilter.py:279
msgid "British Virgin Islands"
msgstr "Isole Vergini Britanniche"

#: pynicotine/networkfilter.py:280
msgid "U.S. Virgin Islands"
msgstr "Isole Vergini degli USA"

#: pynicotine/networkfilter.py:281
msgid "Viet Nam"
msgstr "Viet Nam"

#: pynicotine/networkfilter.py:282
msgid "Vanuatu"
msgstr "Vanuatu"

#: pynicotine/networkfilter.py:283
msgid "Wallis & Futuna"
msgstr "Wallis e Futuna"

#: pynicotine/networkfilter.py:284
msgid "Samoa"
msgstr "Samoa"

#: pynicotine/networkfilter.py:285
msgid "Yemen"
msgstr "Yemen"

#: pynicotine/networkfilter.py:286
msgid "Mayotte"
msgstr "Maiotta"

#: pynicotine/networkfilter.py:287
msgid "South Africa"
msgstr "Sudafrica"

#: pynicotine/networkfilter.py:288
msgid "Zambia"
msgstr "Zambia"

#: pynicotine/networkfilter.py:289
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "Sintesi vocale per messaggio fallita: %s"

#: pynicotine/nowplaying.py:130
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr ""
"Last.fm: Si prega di fornire sia il nome utente Last.fm che la chiave API"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
msgid "Now Playing Error"
msgstr "Riproducendo Errore"

#: pynicotine/nowplaying.py:140
#, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm: Impossibile connettersi all'Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:162
#, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr ""
"Last.fm: Impossibile ottenere la traccia recente dall'Audioscrobbler: "
"%(error)s"

#: pynicotine/nowplaying.py:196
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: Impossibile trovare un player compatibile con MPRIS"

#: pynicotine/nowplaying.py:201
#, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr "Trovati più player MPRIS: %(players)s. Player scelto: %(player)s"

#: pynicotine/nowplaying.py:204
#, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Player MPRIS autorilevato: %s"

#: pynicotine/nowplaying.py:219
#, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr ""
"MPRIS: Qualcosa è andato storto nell'interazione con %(player)s: "
"%(exception)s"

#: pynicotine/nowplaying.py:266
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "ListenBrainz: Si prega di fornire il nome utente ListenBrainz"

#: pynicotine/nowplaying.py:275
#, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "ListenBrainz: Impossibile connettersi a ListenBrainz: %(error)s"

#: pynicotine/nowplaying.py:283
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr ""
"ListenBrainz: Sembra che tu non stia ascoltando niente in questo momento"

#: pynicotine/nowplaying.py:297
#, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz: Impossibile ottenere la traccia recente da ListenBrainz: "
"%(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
msgid "Network Filters"
msgstr "Filtri di Rete"

#: pynicotine/plugins/core_commands/__init__.py:44
msgid "List available commands"
msgstr "Elenca i comandi disponibili"

#: pynicotine/plugins/core_commands/__init__.py:49
msgid "Connect to the server"
msgstr "Connettiti al server"

#: pynicotine/plugins/core_commands/__init__.py:53
msgid "Disconnect from the server"
msgstr "Disconnettiti dal server"

#: pynicotine/plugins/core_commands/__init__.py:58
msgid "Toggle away status"
msgstr "Imposta lo stato su assente"

#: pynicotine/plugins/core_commands/__init__.py:62
msgid "Manage plugins"
msgstr "Gestisci estensioni"

#: pynicotine/plugins/core_commands/__init__.py:74
msgid "Clear chat window"
msgstr "Pulisci la finestra della chat"

#: pynicotine/plugins/core_commands/__init__.py:80
msgid "Say something in the third-person"
msgstr "Scrivi qualcosa in terza persona"

#: pynicotine/plugins/core_commands/__init__.py:87
msgid "Announce the song currently playing"
msgstr "Annuncia il brano attualmente in riproduzione"

#: pynicotine/plugins/core_commands/__init__.py:94
msgid "Join chat room"
msgstr "Unisciti alla stanza"

#: pynicotine/plugins/core_commands/__init__.py:102
msgid "Leave chat room"
msgstr "Lascia la stanza"

#: pynicotine/plugins/core_commands/__init__.py:110
msgid "Say message in specified chat room"
msgstr "Di' il messaggio nella stanza specificata"

#: pynicotine/plugins/core_commands/__init__.py:117
msgid "Open private chat"
msgstr "Apri chat privata"

#: pynicotine/plugins/core_commands/__init__.py:125
msgid "Close private chat"
msgstr "Chiudi la chat privata"

#: pynicotine/plugins/core_commands/__init__.py:133
msgid "Request user's client version"
msgstr "Richiedi la versione del client dell'utente"

#: pynicotine/plugins/core_commands/__init__.py:142
msgid "Send private message to user"
msgstr "Invia un messaggio privato all'utente"

#: pynicotine/plugins/core_commands/__init__.py:150
msgid "Add user to buddy list"
msgstr "Aggiungi utente alla lista amici"

#: pynicotine/plugins/core_commands/__init__.py:158
msgid "Remove buddy from buddy list"
msgstr "Rimuovi utente dalla lista amici"

#: pynicotine/plugins/core_commands/__init__.py:166
msgid "Browse files of user"
msgstr "Sfoglia i file dell'utente"

#: pynicotine/plugins/core_commands/__init__.py:175
msgid "Show user profile information"
msgstr "Mostra informazioni del profilo dell'utente"

#: pynicotine/plugins/core_commands/__init__.py:183
msgid "Show IP address or username"
msgstr "Mostra indirizzo IP o nome utente"

#: pynicotine/plugins/core_commands/__init__.py:190
msgid "Block connections from user or IP address"
msgstr "Blocca connessioni dall'utente o dall'indirizzo IP"

#: pynicotine/plugins/core_commands/__init__.py:197
msgid "Remove user or IP address from ban lists"
msgstr "Rimuovi utente o indirizzo IP dalla lista bloccati"

#: pynicotine/plugins/core_commands/__init__.py:204
msgid "Silence messages from user or IP address"
msgstr "Silenzia i messaggi dall'utente o dall'indirizzo IP"

#: pynicotine/plugins/core_commands/__init__.py:212
msgid "Remove user or IP address from ignore lists"
msgstr "Rimuovi utente o indirizzo IP dalla lista ignorati"

#: pynicotine/plugins/core_commands/__init__.py:220
msgid "Add share"
msgstr "Aggiungi Condividi"

#: pynicotine/plugins/core_commands/__init__.py:226
msgid "Remove share"
msgstr "Rimuovi Condividi"

#: pynicotine/plugins/core_commands/__init__.py:233
msgid "List shares"
msgstr "Elenca condivisioni"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Indicizza condivisi"

#: pynicotine/plugins/core_commands/__init__.py:246
msgid "Start global file search"
msgstr "Avvia una ricerca globale del file"

#: pynicotine/plugins/core_commands/__init__.py:254
msgid "Search files in joined rooms"
msgstr "Cerca file nelle stanze in cui sei"

#: pynicotine/plugins/core_commands/__init__.py:262
msgid "Search files of all buddies"
msgstr "Cerca file di tutti gli amici"

#: pynicotine/plugins/core_commands/__init__.py:270
msgid "Search a user's shared files"
msgstr "Cerca i file condivisi di un utente"

#: pynicotine/plugins/core_commands/__init__.py:296
#, python-format
msgid "Listing %(num)i available commands:"
msgstr "Elenco dei %(num)i comandi disponibili:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr ""
"Elenco dei %(num)i comandi disponibili che combaciano con \"%(query)s\":"

#: pynicotine/plugins/core_commands/__init__.py:311
#, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Digita %(command)s per elencare comandi simili"

#: pynicotine/plugins/core_commands/__init__.py:314
#, python-format
msgid "Type %(command)s to list available commands"
msgstr "Digita %(command)s per elencare i comandi disponibili"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, python-format
msgid "Not joined in room %s"
msgstr "Non entrato nella stanza %s"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr "Non messaggi con l'utente %s"

#: pynicotine/plugins/core_commands/__init__.py:408
#, python-format
msgid "Closed private chat of user %s"
msgstr "Chat privato chiuso dell'utente %s"

#: pynicotine/plugins/core_commands/__init__.py:476
#, python-format
msgid "Banned %s"
msgstr "Bloccato %s"

#: pynicotine/plugins/core_commands/__init__.py:490
#, python-format
msgid "Unbanned %s"
msgstr "Sbloccato %s"

#: pynicotine/plugins/core_commands/__init__.py:503
#, python-format
msgid "Ignored %s"
msgstr "Ignorato %s"

#: pynicotine/plugins/core_commands/__init__.py:517
#, python-format
msgid "Unignored %s"
msgstr "Rimosso dagli ignorati %s"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr "%(num_listed)s condivisioni elencate (%(num_total)s configurate)"

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr "Impossibile condividere la cartella inaccessibile \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""
"Aggiunto %(group_name)s condividi \"%(virtual_name)s\" (necessaria nuova "
"scansione)"

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr "Nessun condiviso con nome \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr "Condiviso \"%s\" rimosso (richiede indicizzazione)"

#: pynicotine/pluginsystem.py:413
msgid "Loading plugin system"
msgstr "Caricamento del sistema di plugin"

#: pynicotine/pluginsystem.py:516
#, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Impossibile abilitare l'estensione %(name)s. Il nome cartella "
"dell'estensione contiene caratteri non validi: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr ""
"Comando %(interface)s in conflitto nell'estensione %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, python-format
msgid "Loaded plugin %s"
msgstr "Estensione %s abilitata"

#: pynicotine/pluginsystem.py:579
#, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Impossibile abilitare l'estensione %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, python-format
msgid "Unloaded plugin %s"
msgstr "Estensione %s disabilitata"

#: pynicotine/pluginsystem.py:648
#, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Impossibile disabilitare l'estensione %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Estensione %(module)s fallita con errore %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"

#: pynicotine/pluginsystem.py:810
msgid "No description"
msgstr "Nessuna descrizione"

#: pynicotine/pluginsystem.py:887
#, python-format
msgid "Missing %s argument"
msgstr "Argomento %s mancante"

#: pynicotine/pluginsystem.py:896
#, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Parametro non valido, scelte possibili: %s"

#: pynicotine/pluginsystem.py:901
#, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Utilizzo: %(command)s %(parameters)s"

#: pynicotine/pluginsystem.py:940
#, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Comando sconosciuto: %(command)s. Digita %(help_command)s per elencare i "
"comandi disponibili."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
msgid "No UPnP devices found"
msgstr "Nessun dispositivo UPnP trovato"

#: pynicotine/portmapper.py:633
#, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"%(protocol)s: Inoltro della porta esterna %(external_port)s fallito: "
"%(error)s"

#: pynicotine/portmapper.py:647
#, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"%(protocol)s: Porta esterna %(external_port)s inoltrata con successo "
"all'indirizzo IP locale %(ip_address)s porta %(local_port)s"

#: pynicotine/privatechat.py:220
#, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Messaggio privato dall'utene '%(user)s': %(message)s"

#: pynicotine/search.py:368
#, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Ricerca elemento della lista dei desideri \"%s\""

#: pynicotine/search.py:434
#, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Attesa per lista dei desideri impostata a %s secondi"

#: pynicotine/search.py:760
#, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr ""
"L'utente %(user)s sta cercando \"%(query)s\", %(num)i risultati ottenuti"

#: pynicotine/shares.py:302
msgid "Rebuilding shares…"
msgstr "Ricostruendo condivisioni…"

#: pynicotine/shares.py:302
msgid "Rescanning shares…"
msgstr "Indicizzazione condivisi…"

#: pynicotine/shares.py:324
#, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Indice aggiornato: %(num)s cartelle trovate"

#: pynicotine/shares.py:334
#, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Si è verificato un errore grave durante la nuova scansione dei Condividi. Se "
"il problema persiste, elimina %(dir)s/*.dbn e prova di nuovo. Se ciò non "
"aiuta, per favore invia una segnalazione di bug includendo questa traccia "
"dello stack: %(trace)s"

#: pynicotine/shares.py:582
#, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Errore di indicizzazione file %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Errore di indicizzazione cartella %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "Errore durante l'esame dei metadati per il file %(path)s: %(error)s"

#: pynicotine/shares.py:1046
#, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Indicizzazione interrotta a causa di condivisi non disponibili: %s"

#: pynicotine/shares.py:1184
#, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "L'utente %(user)s sta sfogliando il tuo elenco di file condivisi"

#: pynicotine/slskmessages.py:3120
#, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Impossibile leggere database dei condivisi. Indicizza di nuovo i tuoi "
"condivisi. Errore: %s"

#: pynicotine/slskproto.py:500
#, python-format
msgid "Specified network interface '%s' is not available"
msgstr "L'interfaccia di rete specificata '%s' non è disponibile"

#: pynicotine/slskproto.py:511
#, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Impossibile ascoltare sulla porta %(port)s. Assicurati che nessun'altra "
"applicazione la utilizzi o scegli una porta diversa. Errore: %(error)s"

#: pynicotine/slskproto.py:523
#, python-format
msgid "Listening on port: %i"
msgstr "In ascolto sulla porta: %i"

#: pynicotine/slskproto.py:805
#, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "Impossibile connettersi al server %(host)s:%(port)s: %(error)s"

#: pynicotine/slskproto.py:1095
#, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Riconnessione al server tra %s secondi"

#: pynicotine/slskproto.py:1170
#, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Connessione a %(host)s:%(port)s"

#: pynicotine/slskproto.py:1208
#, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Connesso al server %(host)s:%(port)s, autenticazione…"

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Disconnesso dal server %(host)s:%(port)s"

#: pynicotine/slskproto.py:1499
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Qualcuno ha effettuato l'accesso al tuo account Soulseek altrove"

#: pynicotine/uploads.py:382
#, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr "Invio completato: utente %(user)s, indirizzo IP %(ip)s, file %(file)s"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Invio interrotto: utente %(user)s, file %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, python-format
msgid "Upload I/O error: %s"
msgstr "Errore I/O in Invio: %s"

#: pynicotine/uploads.py:1103
#, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Caricamento iniziato: utente %(user)s, indirizzo IP %(ip)s, file %(file)s"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr ""
"Impossibile creare la cartella '%(folder)s', errore riportato: %(error)s"

#: pynicotine/userbrowse.py:236
#, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Caricamento Condivisi dal disco fallito: %(error)s"

#: pynicotine/userbrowse.py:282
#, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr "Lista dei file condivisi per l'utente '%(user)s' su %(dir)s salvata"

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr "Impossibile salvare condivisi, '%(user)s', errore riportato: %(error)s"

#: pynicotine/userinfo.py:160
#, python-format
msgid "Picture saved to %s"
msgstr "Immagine salvata in %s"

#: pynicotine/userinfo.py:163
#, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Impossibile salvare immagine in %(filename)s: %(error)s"

#: pynicotine/userinfo.py:190
#, python-format
msgid "User %(user)s is viewing your profile"
msgstr "L'utente %(user)s sta guardando il tuo profilo"

#: pynicotine/users.py:272
#, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Impossibile connettersi al server. Motivo: %s"

#: pynicotine/users.py:272
msgid "Cannot Connect"
msgstr "Impossibile connettersi"

#: pynicotine/users.py:306
#, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr "Impossibile ottenere IP dell'utente %s, poichè disconnesso"

#: pynicotine/users.py:315
#, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr "Indirizzo IP di %(user)s: %(ip)s, porta %(port)i%(country)s"

#: pynicotine/users.py:433
msgid "Soulseek Announcement"
msgstr "Annuncio Soulseek"

#: pynicotine/users.py:449
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"Non hai privilegi Soulseek. Quando i privilegi sono attivi, i tuoi download "
"saranno messi in coda davanti a quelli degli utenti senza privilegi."

#: pynicotine/users.py:455
#, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"%(days)i giorni, %(hours)i ore, %(minutes)i minuti, %(seconds)i secondi di "
"privilegi Soulseek rimanenti"

#: pynicotine/users.py:473
msgid "Your password has been changed"
msgstr "La tua password è stata cambiata"

#: pynicotine/users.py:473
msgid "Password Changed"
msgstr "Password Cambiata"

#: pynicotine/utils.py:574
#, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Impossibile aprire il percorso file %(path)s: %(error)s"

#: pynicotine/utils.py:621
#, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "Impossibile aprire l'URL %(url)s: %(error)s"

#: pynicotine/utils.py:646
#, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr ""
"Qualcosa è andato storto nella lettura del file %(filename)s: %(error)s"

#: pynicotine/utils.py:651
#, python-format
msgid "Attempting to load backup of file %s"
msgstr "Tentativo di caricamento del backup del file %s"

#: pynicotine/utils.py:673
#, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Impossibile eseguire backup di %(path)s: %(error)s"

#: pynicotine/utils.py:694
#, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Impossibile salvare il file %(path)s: %(error)s"

#: pynicotine/utils.py:705
#, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Impossibile ripristinare il file precedente %(path)s: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
msgid "Add buddy…"
msgstr "Aggiungi amico…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
msgid "Toggle Text-to-Speech"
msgstr "Commuta Sintesi Vocale"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
msgid "Chat Room Command Help"
msgstr "Chat Stanza Comando Aiuto"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
msgid "_Log"
msgstr "_Registro"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
msgid "Room Wall"
msgstr "Stanza Bacheca"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
msgid "R_oom Wall"
msgstr "S_tanza Bacheca"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
msgid "Created by"
msgstr "Creato da"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
msgid "Translated by"
msgstr "Tradotto da"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
msgid "License"
msgstr "Licenza"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
msgid "Welcome to Nicotine+"
msgstr "Benvenuto in Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Se il nome utente desiderato è già stato preso, ti verrà richiesto di "
"cambiarlo."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""
"Per connetterti con altri peer Soulseek, una porta di ascolto sul tuo router "
"deve essere inoltrata al tuo computer."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Se la tua porta di ascolto è chiusa, potrai connetterti solo agli utenti le "
"cui porte di ascolto sono aperte."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"Se necessario, scegli una porta di ascolto diversa qui sotto. Questo può "
"essere fatto anche successivamente nelle preferenze."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
msgid "Download Files to Folder"
msgstr "Scarica File nella Cartella"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
msgid "Share Folders"
msgstr "Condividi Cartelle"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Gli utenti di Soulseek potranno scaricare dai tuoi Condividi. Contribuisci "
"alla rete Soulseek condividendo i tuoi file e ricondividendo ciò che hai "
"scaricato da altri utenti."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
msgid "You are ready to use Nicotine+!"
msgstr "Sei pronto ad usare Nicotine+!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""
"Soulseek è un protocollo non crittografato non destinato alla comunicazione "
"sicura."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Donare a Soulseek ti concede privilegi per un certo periodo di tempo. Se si "
"dispone di privilegi, i download avranno priorità maggiore rispetto agli "
"utenti senza privilegi."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
msgid "Previous File"
msgstr "File precedente"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
msgid "Next File"
msgstr "File successivo"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
msgid "Name"
msgstr "Nome"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
msgid "Last Speed"
msgstr "Ultima Velocità"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
msgid "_Export…"
msgstr "_Esporta…"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
msgid "Keyboard Shortcuts"
msgstr "Scorciatoie da Tastiera"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
msgid "General"
msgstr "Generale"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr "Connetti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr "Disconnetti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
msgid "Rescan Shares"
msgstr "Indicizza Condivisi"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
msgid "Show Log Pane"
msgstr "Mostra Pannello Registro"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
msgid "Confirm Quit"
msgstr "Conferma l'uscita"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
msgid "Quit"
msgstr "Esci"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
msgid "Menus"
msgstr "Menu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
msgid "Open Main Menu"
msgstr "Apri Menu Principale"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
msgid "Open Context Menu"
msgstr "Apri Menu Contestuale"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
msgid "Tabs"
msgstr "Schede"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
msgid "Change Main Tab"
msgstr "Cambia Scheda Principale"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
msgid "Go to Previous Secondary Tab"
msgstr "Vai alla Scheda Secondaria Precedente"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
msgid "Go to Next Secondary Tab"
msgstr "Vai alla Scheda Secondaria Successiva"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
msgid "Reopen Closed Secondary Tab"
msgstr "Riapri la scheda secondaria chiusa"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
msgid "Close Secondary Tab"
msgstr "Chiudi Scheda Secondaria"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
msgid "Lists"
msgstr "Liste"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
msgid "Copy Selected Cell"
msgstr "Copia Cella Selezionata"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
msgid "Select All"
msgstr "Seleziona Tutto"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
msgid "Find"
msgstr "Trova"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
msgid "Remove Selected Row"
msgstr "Rimuovi Riga Selezionata"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
msgid "Editing"
msgstr "Modifica"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
msgid "Cut"
msgstr "Taglia"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
msgid "Paste"
msgstr "Incolla"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
msgid "Insert Emoji"
msgstr "Inserisci Emoki"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
msgid "File Transfers"
msgstr "Trasferimenti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
msgid "Resume / Retry Transfer"
msgstr "Riprendi / Riprova Trasferimento"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
msgid "Pause / Abort Transfer"
msgstr "Pausa / Annulla Trasferimento"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
msgid "Download / Upload To"
msgstr "Download / Upload In"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
msgid "Save List to Disk"
msgstr "Salva Lista su Disco"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
msgid "Refresh"
msgstr "Aggiorna"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
msgid "Expand / Collapse All"
msgstr "Espandi / Contrai Tutto"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
msgid "Back to Parent Folder"
msgstr "Vai alla Cartella Superiore"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
msgid "File Search"
msgstr "Ricerca File"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
msgid "Result Filters"
msgstr "Filtri _Risultato"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
msgid "Current Session"
msgstr "Sessione Corrente"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
msgid "Completed Downloads"
msgstr "Download Completati"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
msgid "Downloaded Size"
msgstr "Dimensione Ricevuta"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
msgid "Completed Uploads"
msgstr "Upload Completati"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
msgid "Uploaded Size"
msgstr "Dimensione Inviata"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
msgid "Total"
msgstr "Totale"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
msgid "_Reset…"
msgstr "Azze_ra…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Gli elementi della lista dei desideri vengono cercati automaticamente "
"periodicamente, per scoprire file non comuni."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
msgid "Add Wish…"
msgstr "Aggiungi Desiderio…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
msgid "Clear All…"
msgstr "Pulisci Tutto…"

#: pynicotine/gtkgui/ui/downloads.ui:138
msgid "Clear All Finished/Filtered Downloads"
msgstr "Rimuovi Tutti i Download Finiti/Filtrati"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
msgid "Clear Finished"
msgstr "Pulisci Completati"

#: pynicotine/gtkgui/ui/downloads.ui:169
msgid "Clear Specific Downloads"
msgstr "Pulisci download specifici"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
msgid "Clear _All…"
msgstr "Pulisci Tutto…"

#: pynicotine/gtkgui/ui/interests.ui:21
msgid "Personal Interests"
msgstr "Mi Interessa"

#: pynicotine/gtkgui/ui/interests.ui:40
msgid "Add something you like…"
msgstr "Aggiungi qualcosa che ti piace…"

#: pynicotine/gtkgui/ui/interests.ui:62
msgid "Personal Dislikes"
msgstr "Non Mi Interessa"

#: pynicotine/gtkgui/ui/interests.ui:80
msgid "Add something you dislike…"
msgstr "Aggiungi qualcosa che non ti piace…"

#: pynicotine/gtkgui/ui/interests.ui:143
msgid "Refresh Recommendations"
msgstr "Aggiorna lista consigliati"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
msgid "Main Menu"
msgstr "Menu principale"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
msgid "Room…"
msgstr "Canale…"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
msgid "Username…"
msgstr "Nome utente…"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
msgid "Search term…"
msgstr "Termine di ricerca…"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Pulisci"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Modelli di ricerca: con una parola = term, senza una parola = -term, parola "
"parziale = *erm"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
msgid "Search Scope"
msgstr "Contesto ricerca"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
msgid "_Wishlist"
msgstr "Lista dei Desideri"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
msgid "Configure Searches"
msgstr "Configura ricerche"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Inserisci un termine di ricerca per cercare i file condivisi da altri utenti "
"sulla rete Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
msgid "File Grouping Mode"
msgstr "Modalità raggruppamento file"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
msgid "Configure Downloads"
msgstr "Configura download"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"I file scaricati da altri utenti vengono accodati qui e possono essere messi "
"in pausa e ripresi su richiesta"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
msgid "Configure Uploads"
msgstr "Configura upload"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"I tentativi degli utenti di scaricare i file condivisi vengono accodati e "
"gestiti qui"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
msgid "_Open List"
msgstr "Apri Lista"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Apre un elenco locale di file condivisi precedentemente salvati su disco"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
msgid "Configure Shares"
msgstr "Configura condivisi"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Inserisci il nome di un utente per sfogliarne i file condivisi. Puoi anche "
"salvare la lista su disco e consultarla successivamente."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
msgid "_Personal Profile"
msgstr "Profilo _Personale"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
msgid "Configure Account"
msgstr "Configura account"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Inserisci il nome di un utente per visualizzarne la descrizione, i dettagli "
"e l'immagine personale"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
msgid "Chat _History"
msgstr "Cronologia C_hat"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
msgid "Configure Chats"
msgstr "Configura chat"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr ""
"Inserisci il nome di un utente per avviare una conversazione testuale privata"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
msgid "_Message All"
msgstr "_Messaggia tutti"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
msgid "Configure Ignored Users"
msgstr "Configura utenti ignorati"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Aggiungi utenti come amici per condividere cartelle specifiche con loro e "
"ricevere notifiche quando sono online"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
msgid "Join or create room…"
msgstr "Unisciti o crea una stanza…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Unisciti ad un canale esistente o creane uno nuovo per parlare con altri "
"utenti sulla rete Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
msgid "Configure User Profile"
msgstr "Configura profilo utente"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
msgid "Connections"
msgstr "Connessioni"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
msgid "Downloading (Speed / Active Users)"
msgstr "In download (velocità / utenti attivi)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
msgid "Uploading (Speed / Active Users)"
msgstr "Invio (velocità / utenti attivi)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
msgid "Search chat history…"
msgstr "Cerca nella cronologia chat…"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
msgid "Download Speed Limits"
msgstr "Limiti Velocità Download"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
msgid "Unlimited download speed"
msgstr "Velocità download illimitata"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
msgid "Use download speed limit (KiB/s):"
msgstr "Usa limite velocità di download (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Usa velocità alternativa di download (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
msgid "Search rooms…"
msgstr "Cerca stanze…"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
msgid "Refresh Rooms"
msgstr "Aggiorna stanze"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
msgid "_Show feed of public chat room messages"
msgstr "_Mostra feed di messaggi di chat room pubbliche"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
msgid "_Accept private room invitations"
msgstr "_Accetta inviti a canale privato"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Scrivi un singolo messaggio che gli altri utenti della stanza possano "
"leggere in seguito. I messaggi recenti sono mostrati in alto."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
msgid "Set wall message…"
msgstr "Imposta messaggio di bacheca…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
msgid "Search Result Filters"
msgstr "Filtri su Risultati di Ricerca"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"I filtri dei risultati di ricerca vengono utilizzati per perfezionare i "
"risultati di ricerca visualizzati."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Ogni elenco di risultati di ricerca ha il proprio filtro, che può essere "
"rivelato attivando il pulsante Filtra risultati. Un filtro è composto da più "
"campi, che vengono tutti applicati quando si preme Invio in uno dei suoi "
"campi. Il filtraggio viene applicato immediatamente ai risultati già "
"ricevuti e a quelli che appariranno."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Come suggerisce il nome, un filtro dei risultati di ricerca non può "
"espandere la tua ricerca originale, può solo restringerla. Per ampliare o "
"modificare i termini di ricerca, esegui una nuova ricerca."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
msgid "Result Filter Usage"
msgstr "Utilizzo del filtro risultati"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
msgid "Include Text"
msgstr "Includi Testo"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
msgid "Files, folders and usernames containing this text will be shown."
msgstr ""
"Verranno visualizzati i file, le cartelle e i nomi utente contenenti questo "
"testo."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Le Maiuscole/minuscole sono ignorate, ma l'ordine delle parole rimane "
"importante: 'Instrumental Remix' non mostrerà alcuna 'Remix Instrumental'"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Usa | (o tubi) per separare varie frasi esatte. Esempio:\n"
"    Remix|Dub Mix|Instrumental"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
msgid "Exclude Text"
msgstr "Escludi Testo"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"Come sopra, ma i file, le cartelle e i nomi utente vengono scartati se il "
"testo combacia."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
msgid "Filters files based upon their file extension."
msgstr "Filtra file in base alla loro estensione."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"È possibile specificare più estensioni di file e, di conseguenza, verranno "
"mostrati più risultati dall'elenco. Esempio:\n"
"    flac wav ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"È anche possibile invertire il filtro, specificando le estensioni di file "
"che non si desiderano nei risultati usando un punto esclamativo! Esempio:\n"
"    !mp3 !jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
msgid "File Size"
msgstr "Dimensioni File"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
msgid "Filters files based upon their file size."
msgstr "Filtra file in base alla loro dimensione."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"Per impostazione predefinita, l'unità utilizzata è il byte (B) e i file con "
"dimensione maggiore o uguale del (>=) valore verranno mostrati."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Aggiungi b, k, m, o g (oppure kib, mib, o gib) per specificare unità di "
"byte, kibibyte, mebibyte, o gibibyte: \n"
"    20m per mostrare file con dimensioni più grandi di 20 MiB (mebibytes)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Anteponi = ad un valore per specificare una corrispondenza esatta:\n"
"    =1024 combacia con i file di dimensione di esattamente 1 KiB (kibibyte)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Anteponi ! ad un valore per escludere i file di una dimensione specifica:\n"
"    !30.5m per nascondere i file che pesano 30.5 MiB (mebibytes)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Anteponi < o > per trovare file di dimensioni minori/maggiori del valore "
"dato. Usa uno spazio tra ogni condizione per includere un intervallo:\n"
"    >10.5m <1g per mostrare file più grandi di 10.5 MiB, ma più piccoli di 1 "
"GiB."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Anche le più note varianti kb, mb e gb possono essere utilizzate per le "
"unità kilobyte, megabyte e gigabyte."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
msgid "Filters files based upon their bitrate."
msgstr "Filtra i file in base al bitrate."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"I valori devono essere inseriti solo come cifre numeriche. L'unità è sempre "
"Kb/s (Kilobit al secondo)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Come con la Dimensione dei File (sopra), gli operatori =, !, <, >, <= o >= "
"possono essere usati, e si possono specificare più condizioni, per esempio "
"per mostrare file con un bitrate di almeno 256 Kb/s e con un massimo bitrate "
"di 1411 Kb/s:\n"
"    256 <=1411"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
msgid "Filters files based upon their duration."
msgstr "Filtra i file in base alla durata."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"Per impostazione predefinita, verranno selezionati i file più lunghi o con "
"durata uguale (>=) a quella immessa, a meno che non venga utilizzato un "
"operatore (=, !, <=, < o >)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Immettere un valore grezzo in secondi o utilizzare i formati dell'ora MM:SS "
"e HH:MM:SS:\n"
"    =53 mostra file che sono lunghi circa 53 secondi.\n"
"    >5:30 per mostrare file più lunghi di 5 minuti e mezzo.\n"
"    <5:30:00 mostra file più corti di 5 ore e mezzo."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"È possibile specificare più condizioni:\n"
"    >6:00 <12:00 per mostrare file di durata compresa tra i 6 e i 12 "
"minuti.\n"
"    !9:54 !8:43 !7:32 per nascondere alcuni file specifici dai risultati.\n"
"    =5:34 =4:23 =3:05 per includere file con durate specifiche."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Filtra i file basandosi sulla posizione geografica degli utenti secondo i "
"codici dei paesi definiti da ISO 3166-2:\n"
"    US mostrerà risultati solamente da utenti con indirizzi IP negli Stati "
"Uniti.\n"
"    !GB nasconderà i risultati che provengono da utenti in Gran Bretagna."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
msgid "Multiple countries can be specified with commas or spaces."
msgstr "È possibile specificare più paesi con virgole o spazi."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
msgid "Free Slot"
msgstr "Slot Libero"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Mostra solo i risultati dagli utenti che hanno almeno uno slot di upload "
"libero, ovvero file che sono disponibili immediatamente."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
msgid "Upload Speed Limits"
msgstr "Limiti Velocità Upload"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
msgid "Unlimited upload speed"
msgstr "Velocità upload illimitata"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
msgid "Use upload speed limit (KiB/s):"
msgstr "Usa limite alla velocità di upload (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Usa limite alternativo velocità di upload (KiB/s):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
msgid "Private Chat Command Help"
msgstr "Aiuto Comandi della Chat Privata"

#: pynicotine/gtkgui/ui/search.ui:7
msgid "Include text…"
msgstr "Includi testo…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Includi risultati i cui percorsi dei file contengono il testo specificato. È "
"possibile specificare più frasi e parole, ad es. frase esatta|musica|termine|"
"frase esatta due"

#: pynicotine/gtkgui/ui/search.ui:18
msgid "Exclude text…"
msgstr "Escludi testo…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Escludi risultati i cui percorsi di file contengono il testo specificato. È "
"possibile specificare più frasi e parole, ad es. frase esatta|musica|termine|"
"frase esatta due"

#: pynicotine/gtkgui/ui/search.ui:29
msgid "File type…"
msgstr "Tipo di file…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Tipo di file, ad es. flac wav o !mp3 !m4a"

#: pynicotine/gtkgui/ui/search.ui:40
msgid "File size…"
msgstr "Dimensione file…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
msgid "File size, e.g. >10.5m <1g"
msgstr "Dimensione del file, ad es. >10.5m <1g"

#: pynicotine/gtkgui/ui/search.ui:51
msgid "Bitrate…"
msgstr "Bitrate…"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
msgid "Bitrate, e.g. 256 <1412"
msgstr "Bitrate, ad es. 256<1412"

#: pynicotine/gtkgui/ui/search.ui:62
msgid "Duration…"
msgstr "Durata…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Durata, ad es. >6:00 <12:00 !6:54"

#: pynicotine/gtkgui/ui/search.ui:73
msgid "Country code…"
msgstr "Codice paese…"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Codice paese, ad es. US ES o !DE !GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Proibisci agli utenti di accedere ai tuoi file condivisi, in base a nome "
"utente, indirizzo IP o paese."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Codici paese da bloccare (separati da virgole):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
msgid "Codes must be in ISO 3166-2 format."
msgstr "I codici devono essere in formato ISO 3166-2."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
msgid "Use custom geo block message:"
msgstr "Usa messaggio personalizzato per il bllocco geografico:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Usa messaggio di blocco personalizzato:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
msgid "IP Addresses"
msgstr "Indirizzi IP"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
msgid "Restore previously open private chats on startup"
msgstr "Ripristina chat privata precedente all'avvio"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
msgid "Enable spell checker"
msgstr "Attiva il controllo ortografico"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
msgid "Enable CTCP-like private message responses (client version)"
msgstr "Abilita risposte di messaggio privato alà CTCP (versione client)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
msgid "Number of recent private chat messages to show:"
msgstr "Numero di righe di chat private recenti da mostrare:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
msgid "Number of recent chat room messages to show:"
msgstr "Numero di righe di stanze chat recenti da mostrare:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
msgid "Chat Completion"
msgstr "Completamento Chat"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
msgid "Enable tab-key completion"
msgstr "Abilita completamento con tasto Tab"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
msgid "Enable completion drop-down list"
msgstr "Abilita menu a tendina di completamento"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
msgid "Minimum characters required to display drop-down:"
msgstr "Numero minimo di caratteri mostrati nel menu a tendina:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
msgid "Allowed chat completions:"
msgstr "Completamenti chat permessi:"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
msgid "Buddy names"
msgstr "Lista amici"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
msgid "Chat room usernames"
msgstr "Nomi utente in chat di canale"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
msgid "Room names"
msgstr "Nomi Canale"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
msgid "Commands"
msgstr "Comandi"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
msgid "Timestamps"
msgstr "Marcatura temporale"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
msgid "Private chat format:"
msgstr "Formato chat privata:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "Predefinito"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
msgid "Chat room format:"
msgstr "Formato Chat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
msgid "Text-to-Speech"
msgstr "Sintesi Vocale"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
msgid "Enable Text-to-Speech"
msgstr "Abilita Sintesi Vocale"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
msgid "Text-to-Speech command:"
msgstr "Comando sintesi vocale:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
msgid "Private chat message:"
msgstr "Messaggio chat privata:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
msgid "Chat room message:"
msgstr "Messaggio di canale:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
msgid "Censor"
msgstr "Censura"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
msgid "Enable censoring of text patterns"
msgstr "Abilita censura di modelli di testo"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
msgid "Auto-Replace"
msgstr "Auto-Sostituzione"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
msgid "Enable automatic replacement of words"
msgstr "Abilita sostituzione automatica delle parole"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr "Autopulisci download completati/filtrati dalla lista trasferimenti"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
msgid "Store completed downloads in username subfolders"
msgstr "Memorizza download completati nelle sottocartelle con nome utente"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
msgid "Double-click action for downloads:"
msgstr "Azione di doppio clic sui download:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
msgid "Allow users to send you any files:"
msgstr "Permetti agli utenti di inviarti qualsiasi file:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
msgid "Folders"
msgstr "Cartelle"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
msgid "Finished downloads:"
msgstr "Download completati:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
msgid "Incomplete downloads:"
msgstr "Download parziali:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
msgid "Received files:"
msgstr "File ricevuti:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "Eventi"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
msgid "Run command after file download finishes ($ for file path):"
msgstr "Esegui comando a download file completato ($ per percorso file):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"Esegui comando a download cartella completato ($ per percorso cartella):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
msgid "Download Filters"
msgstr "Filtri di Download"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
msgid "Enable download filters"
msgstr "Abilita filtri in download"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Aggiungi"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Carica Predefinite"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
msgid "Verify Filters"
msgstr "Verifica Filtri"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
msgid "Unverified"
msgstr "Non verificato"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Ignora messaggi di chat e risultati di ricerca degli utenti, in base al nome "
"utente o all'indirizzo IP."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Registra canali come predefinito"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Registra chat private come predefinito"

#: pynicotine/gtkgui/ui/settings/log.ui:142
msgid "Log transfers to file"
msgstr "Registra trasferimenti su file"

#: pynicotine/gtkgui/ui/settings/log.ui:166
msgid "Log debug messages to file"
msgstr "Registra messaggi di debug su file"

#: pynicotine/gtkgui/ui/settings/log.ui:190
msgid "Log timestamp format:"
msgstr "Formato data file di log:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
msgid "Folder Locations"
msgstr "Posizioni Cartella"

#: pynicotine/gtkgui/ui/settings/log.ui:245
msgid "Chatroom logs folder:"
msgstr "Cartella registri chat di canale:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
msgid "Private chat logs folder:"
msgstr "Cartella registri chat privata:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
msgid "Transfer logs folder:"
msgstr "Cartella registri trasferimento:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
msgid "Debug logs folder:"
msgstr "Cartella registri di debug:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Accedi a un account Soulseek esistente o creane uno nuovo. I nomi utente "
"fanno distinzione tra maiuscole e minuscole e sono univoci."

#: pynicotine/gtkgui/ui/settings/network.ui:118
msgid "Public IP address:"
msgstr "Indirizzo IP pubblico:"

#: pynicotine/gtkgui/ui/settings/network.ui:159
msgid "Listening port:"
msgstr "Porta di ascolto:"

#: pynicotine/gtkgui/ui/settings/network.ui:185
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Apri automaticamente le porte di ascolto (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
msgid "Away Status"
msgstr "Stato Assente"

#: pynicotine/gtkgui/ui/settings/network.ui:228
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Minuti di inattività prima di andare via (0 per disabilitare):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
msgid "Auto-reply message when away:"
msgstr "Risposta automatica quando assente:"

#: pynicotine/gtkgui/ui/settings/network.ui:299
msgid "Auto-connect to server on startup"
msgstr "Connessione automatica al server all'avvio"

#: pynicotine/gtkgui/ui/settings/network.ui:323
msgid "Soulseek server:"
msgstr "Server Soulseek:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Associa le connessioni a una specifica interfaccia di rete, utile ad es. per "
"assicurarti che venga sempre usata una VPN. Lascia vuoto per utilizzare "
"qualsiasi interfaccia disponibile. Modifica questo valore solo se sai cosa "
"stai facendo."

#: pynicotine/gtkgui/ui/settings/network.ui:351
msgid "Network interface:"
msgstr "Interfaccia di rete:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"Sta Ascoltando ti consente di visualizzare ciò che sta riproducendo il tuo "
"lettore multimediale utilizzando il comando /now in chat."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
msgid "Other"
msgstr "Altro"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
msgid "Now Playing Format"
msgstr "Formato Sta Ascoltando"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
msgid "Now Playing message format:"
msgstr "Formato Sta Ascoltando:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
msgid "Test Configuration"
msgstr "Prova Configurazione"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
msgid "Enable plugins"
msgstr "Attiva estensioni"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
msgid "Add Plugins"
msgstr "Aggiungi Estensioni"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
msgid "_Add Plugins"
msgstr "_Aggiungi Estensioni"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
msgid "Settings"
msgstr "Impostazioni"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
msgid "_Settings"
msgstr "Impo_stazioni"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
msgid "Version:"
msgstr "Versione:"

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
msgid "Created by:"
msgstr "Creato da:"

#: pynicotine/gtkgui/ui/settings/search.ui:51
msgid "Enable search history"
msgstr "Abilita cronologia di ricerca"

#: pynicotine/gtkgui/ui/settings/search.ui:70
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"I file condivisi privatamente che sono stati resi visibili a tutti saranno "
"prefissati con '[PRIVATO]', e non possono essere scaricati fino a quando "
"l'uploader non dà il permesso esplicito. Chiedi gentilmente."

#: pynicotine/gtkgui/ui/settings/search.ui:76
msgid "Show privately shared files in search results"
msgstr "Mostra file condivisi privatamente nei risultati di ricerca"

#: pynicotine/gtkgui/ui/settings/search.ui:106
msgid "Limit number of results per search:"
msgstr "Limita il numero di risultati per ricerca:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
msgid "Result Filter Help"
msgstr "Aiuto Filtri Risultato"

#: pynicotine/gtkgui/ui/settings/search.ui:177
msgid "Enable search result filters by default"
msgstr "Abilita, come predefinito, i filtri su risultati di ricerca"

#: pynicotine/gtkgui/ui/settings/search.ui:211
msgid "Include:"
msgstr "Includi:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
msgid "Exclude:"
msgstr "Escludi:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
msgid "File Type:"
msgstr "Tipo di File:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "Dimensione:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Bitrate:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
msgid "Duration:"
msgstr "Durata:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
msgid "Country Code:"
msgstr "Codice Paese:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
msgid "Only show results from users with an available upload slot."
msgstr ""
"Mostra solo i risultati degli utenti con uno slot di upload disponibile."

#: pynicotine/gtkgui/ui/settings/search.ui:430
msgid "Network Searches"
msgstr "Ricerche di Rete"

#: pynicotine/gtkgui/ui/settings/search.ui:452
msgid "Respond to search requests from other users"
msgstr "Rispondi alle richieste di ricerca di altri utenti"

#: pynicotine/gtkgui/ui/settings/search.ui:486
msgid "Searches shorter than this number of characters will be ignored:"
msgstr "Le ricerche più brevi di questo numero di caratteri verranno ignorate:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
msgid "Maximum search results to send per search request:"
msgstr "Numero massimo di risultati da inviare per richiesta di ricerca:"

#: pynicotine/gtkgui/ui/settings/search.ui:578
msgid "Clear Search History"
msgstr "Pulisci Cronologia Ricerca"

#: pynicotine/gtkgui/ui/settings/search.ui:626
msgid "Clear Filter History"
msgstr "Pulisci Cronologia Filtri"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Indicizzazione automatica del contenuto delle cartelle condivise all'avvio. "
"Se disabilitato, i condivisi vengono aggiornati solo quando avvii "
"manualmente una nuova indicizzazione."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Indicizza condivisi all'avvio"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr "Visibile a tutti:"

#: pynicotine/gtkgui/ui/settings/shares.ui:82
msgid "Buddy shares"
msgstr "Amico condivide"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
msgid "Trusted shares"
msgstr "Condivisioni affidabili"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr "Auto-pulisci upload completati/annullati dalla lista trasferimenti"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
msgid "Double-click action for uploads:"
msgstr "Azione di doppio clic sugli upload:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
msgid "Limit upload speed:"
msgstr "Limita velocità upload:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
msgid "Per transfer"
msgstr "Per transferimento"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
msgid "Total transfers"
msgstr "Trasferimenti totali"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
msgid "Upload Slots"
msgstr "Slot per Upload"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Round Robin: i file verranno caricati in modo ciclico verso gli utenti in "
"attesa in coda.\n"
"First In, First Out: i file verranno caricati nell'ordine in cui sono stati "
"messi in coda."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
msgid "Upload queue type:"
msgstr "Tipo di Coda Upload:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr ""
"Assegna slot di caricamento fino a raggiungere una velocità totale di (KiB/"
"s):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
msgid "Fixed number of upload slots:"
msgstr "Numero fisso di slot di caricamento:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
msgid "Prioritize all buddies"
msgstr "Dai la priorità a tutti gli amici"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
msgid "Queue Limits"
msgstr "Limiti Coda"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
msgid "Maximum number of queued files per user:"
msgstr "Numero massimo di file in coda per utente:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr "Dimensione totale massima dei file in coda per utente (MiB):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
msgid "Limits do not apply to buddies"
msgstr "I limiti non vengono applicati agli amici"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"Le occorrenze di $ saranno sostituite dall'URL. Verranno aperte le "
"applicazioni di sistema predefinite nei casi in cui non è stato configurato "
"un protocollo."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
msgid "File manager command:"
msgstr "Comando Gestore file:"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
msgid "Reset Picture"
msgstr "Azzera Immagine"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
msgid "Self Description"
msgstr "Descrizione di sé"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""
"Aggiungi elementi che vuoi che tutti vedano, ad esempio una breve "
"descrizione, suggerimenti utili o linee guida per scaricare i tuoi condivisi."

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
msgid "Picture:"
msgstr "Immagine:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
msgid "Prefer dark mode"
msgstr "Preferisci modalità scura"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
msgid "Use header bar"
msgstr "Usa la barra dell'intestazione"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
msgid "Display tray icon"
msgstr "Visualizza icona nel vassoio"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
msgid "Minimize to tray on startup"
msgstr "Riduci nel vassoio all'avvio"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
msgid "Language (requires a restart):"
msgstr "Lingua (richiede il riavvio):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
msgid "When closing window:"
msgstr "Quando si chiude la finestra:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
msgid "Notifications"
msgstr "Notifiche"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
msgid "Enable sound for notifications"
msgstr "Abilita suono per le notifiche"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
msgid "Show notification for private chats and mentions in the window title"
msgstr "Mostra notifica nel titolo della finestra per chat private e menzioni"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
msgid "Show notifications for:"
msgstr "Mostra notifiche per:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
msgid "Finished file downloads"
msgstr "Download di file completati"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
msgid "Finished folder downloads"
msgstr "Download di cartella completati"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
msgid "Private messages"
msgstr "Messaggio privato"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
msgid "Chat room messages"
msgstr "Messaggi di canale"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
msgid "Chat room mentions"
msgstr "Menzioni in chat di canale"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
msgid "Wishlist results found"
msgstr "Risultati della lista dei desideri trovati"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
msgid "Restore the previously active main tab at startup"
msgstr "Ripristina l'ultima scheda principale attiva all'avvio"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
msgid "Close-buttons on secondary tabs"
msgstr "Pulsanti di chiusura nelle schede secondarie"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
msgid "Regular tab label color:"
msgstr "Colore scheda regolare:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
msgid "Changed tab label color:"
msgstr "Colore scheda cambiata:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
msgid "Highlighted tab label color:"
msgstr "Colore scheda evidenziata:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr "Posizione lista amici:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
msgid "Visible main tabs:"
msgstr "Schede principali visibili:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
msgid "Tab bar positions:"
msgstr "Posizioni nella barra schede:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
msgid "Main tabs"
msgstr "Schede principali"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
msgid "Show reverse file paths (requires a restart)"
msgstr "Mostra percorsi file inversi (richiede un riavvio)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
msgid "Show exact file sizes (requires a restart)"
msgstr "Mostra dimensioni esatte dei file (richiede un riavvio)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
msgid "List text color:"
msgstr "Colore testo Lista:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
msgid "Enable colored usernames"
msgstr "Attiva nomi utente colorati"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
msgid "Chat username appearance:"
msgstr "Aspetto nome utente in chat:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
msgid "Remote text color:"
msgstr "Colore test Remoto:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
msgid "Local text color:"
msgstr "Colore testo Locale:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
msgid "Command output text color:"
msgstr "Colore del testo dell'output dei comandi:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
msgid "/me action text color:"
msgstr "Colore testo azione /me:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
msgid "Highlighted text color:"
msgstr "Colore testo Evidenziato:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
msgid "URL link text color:"
msgstr "Colore testo Collegamento URL:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
msgid "User Statuses"
msgstr "Stato degli utenti"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
msgid "Online color:"
msgstr "Colore Online:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
msgid "Away color:"
msgstr "Colore Assente:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
msgid "Offline color:"
msgstr "Colore Offline:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
msgid "Text Entries"
msgstr "Voci di Testo"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
msgid "Text entry background color:"
msgstr "Colore sfondo di voce testuale:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
msgid "Text entry text color:"
msgstr "Colore testo di voce testuale:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
msgid "Fonts"
msgstr "Caratteri"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
msgid "Global font:"
msgstr "Carattere globale:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
msgid "List font:"
msgstr "Carattere lista:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
msgid "Text view font:"
msgstr "Font carattere visualizzato:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
msgid "Chat font:"
msgstr "Carattere chat:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
msgid "Transfers font:"
msgstr "Carattere trasferimenti:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
msgid "Search font:"
msgstr "Carattere ricerca:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
msgid "Browse font:"
msgstr "Sfoglia caratteri:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
msgid "Icons"
msgstr "Icone"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
msgid "Icon theme folder:"
msgstr "Tema icona cartella:"

#: pynicotine/gtkgui/ui/uploads.ui:86
msgid "Abort User(s)"
msgstr "Interrompi Utente(i)"

#: pynicotine/gtkgui/ui/uploads.ui:116
msgid "Ban User(s)"
msgstr "Blocca Utente(i)"

#: pynicotine/gtkgui/ui/uploads.ui:138
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Rimuovi Tutti gli Upload Completati/Annullati"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
msgid "Message All"
msgstr "Messaggia tutti"

#: pynicotine/gtkgui/ui/uploads.ui:199
msgid "Clear Specific Uploads"
msgstr "Pulisci upload specifici"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
msgid "Save Shares List to Disk"
msgstr "Salva l'Elenco delle Condivisioni sul Disco"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
msgid "Refresh Files"
msgstr "Aggiorna file"

#: pynicotine/gtkgui/ui/userinfo.ui:101
msgid "Edit Profile"
msgstr "Modifica Profilo"

#: pynicotine/gtkgui/ui/userinfo.ui:149
msgid "Shared Files"
msgstr "File Condivisi"

#: pynicotine/gtkgui/ui/userinfo.ui:203
msgid "Upload Speed"
msgstr "Velocità Upload"

#: pynicotine/gtkgui/ui/userinfo.ui:230
msgid "Free Upload Slots"
msgstr "Slot Liberi per Upload"

#: pynicotine/gtkgui/ui/userinfo.ui:284
msgid "Queued Uploads"
msgstr "Upload in Coda"

#: pynicotine/gtkgui/ui/userinfo.ui:354
msgid "Edit Interests"
msgstr "Modifica Interessi"

#: pynicotine/gtkgui/ui/userinfo.ui:624
msgid "_Gift Privileges…"
msgstr "Re_gala Privilegi…"

#: pynicotine/gtkgui/ui/userinfo.ui:663
msgid "_Refresh Profile"
msgstr "_Aggiorna Profilo"

#: pynicotine/plugins/core_commands/PLUGININFO:3
msgid "Nicotine+ Commands"
msgstr "Comandi di Nicotine+"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Squadra di Nicotine+"

#~ msgid "Listening port (requires a restart):"
#~ msgstr "Porta di ascolto (richiede un riavvio):"

#~ msgid "Network interface (requires a restart):"
#~ msgstr "Interfaccia di rete (richiede un riavvio):"

#~ msgid "Invalid Password"
#~ msgstr "Password Errata"

#~ msgid "Change _Login Details"
#~ msgstr "Cambia Dettagli Accesso"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i utenti privilegiati"

#~ msgid "_Set Up…"
#~ msgstr "Configura…"

#~ msgid "Queued search result text color:"
#~ msgstr "Colore testo Cerca nella coda:"

#, python-format
#~ msgid "Failed to fetch the shared folder %(folder)s: %(error)s"
#~ msgstr "Impossibile recuperare la cartella condivisa %(folder)s: %(error)s"

#~ msgid "_Clear"
#~ msgstr "_Pulisci"

#, python-format
#~ msgid "Can't save %(filename)s: %(error)s"
#~ msgstr "Impossibile salvare %(filename)s: %(error)s"

#~ msgid "Trusted Buddies"
#~ msgstr "Utenti Fidati"

#~ msgid "Quit program"
#~ msgstr "Esce dall'applicazione"

#~ msgid "Username:"
#~ msgstr "Nome utente:"

#~ msgid "Re_commendations for Item"
#~ msgstr "_Consigli per l'Elemento"

#~ msgid "_Remove Item"
#~ msgstr "_Rimuovi Elemento"

#~ msgid "_Remove"
#~ msgstr "_Rimuovi"

#~ msgid "Send M_essage"
#~ msgstr "Invia M_essaggio"

#~ msgid "Send Message"
#~ msgstr "Invia Messaggio"

#~ msgid "View User Profile"
#~ msgstr "Visualizza Profilo Utente"

#~ msgid "Start Messaging"
#~ msgstr "Messaggio Iniziale"

#~ msgid "Enter the name of the user whom you want to send a message:"
#~ msgstr "Inserisci il nome dell'utente a cui desideri inviare un messaggio:"

#~ msgid "_Message"
#~ msgstr "_Messaggia"

#~ msgid "Enter the name of the user whose profile you want to see:"
#~ msgstr "Inserisci il nome dell'utente di cui vuoi vedere il profilo:"

#~ msgid "_View Profile"
#~ msgstr "_Visualizza profilo"

#~ msgid "Enter the name of the user whose shares you want to see:"
#~ msgstr "Inserisci il nome dell'utente di cui desideri ricevere i condivisi:"

#~ msgid "_Browse"
#~ msgstr "_Sfoglia"

#~ msgid "Password"
#~ msgstr "Password"

#~ msgid "Download File"
#~ msgstr "Scarica file"

#~ msgid "Refresh Similar Users"
#~ msgstr "Aggiorna utenti simili"

#~ msgid "Enter the username of the person whose files you want to see"
#~ msgstr ""
#~ "Inserisci il nome utente della persona di cui si desidera visualizzare i "
#~ "file"

#~ msgid "Enter the username of the person whose information you want to see"
#~ msgstr ""
#~ "Inserisci il nome utente della persona di cui si desidera mostrare i "
#~ "dettagli"

#~ msgid "Enter the username of the person you want to send a message to"
#~ msgstr ""
#~ "Inserisci il nome utente della persona a cui desideri inviare un messaggio"

#~ msgid "Enter the username of the person you want to add to your buddy list"
#~ msgstr "Inserisci il nome utente di chi desideri aggiungere agli amici"

#~ msgid ""
#~ "Enter the name of a room you want to join. If the room doesn't exist, it "
#~ "will be created."
#~ msgstr ""
#~ "Immettere il nome di un canale virtuale in cui si desidera entrare. Se il "
#~ "canale non esiste, verrà creato."

#~ msgid "Show Log History Pane"
#~ msgstr "Mostra pannello log cronologia"

#~ msgid "Save _Picture"
#~ msgstr "Salva _Immagine"

#, python-format
#~ msgid ""
#~ "Filtered out incorrect search result %(filepath)s from user %(user)s for "
#~ "search query \"%(query)s\""
#~ msgstr ""
#~ "Risultato di ricerca errato %(filepath)s filtrato dall'utente %(user)s "
#~ "per la query di ricerca \"%(query)s\""

#~ msgid ""
#~ "Certain clients don't send search results if special characters are "
#~ "included."
#~ msgstr ""
#~ "Alcuni client non inviano risultati di ricerca se sono inclusi caratteri "
#~ "speciali."

#~ msgid "Remove special characters from search terms"
#~ msgstr "Rimuovi i caratteri speciali dai termini di ricerca"

#, python-format
#~ msgid "Trouble executing '%s'"
#~ msgstr "Problemi nell'esecuzione '%s'"

#, python-format
#~ msgid "Trouble executing on folder: %s"
#~ msgstr "Problema di esecuzione sulla cartella: %s"

#~ msgid "Disallowed extension"
#~ msgstr "Tipo file non consentito"

#~ msgid "Too many files"
#~ msgstr "Troppi file"

#~ msgid "Too many megabytes"
#~ msgstr "Troppi megabyte"

#~ msgid "Server does not permit performing wishlist searches at this time"
#~ msgstr ""
#~ "Il server non consente di eseguire ricerche nella lista dei desideri in "
#~ "questo momento"

#~ msgid ""
#~ "File transfer speeds depend on users you are downloading from. Certain "
#~ "users will be faster, while others will be slow."
#~ msgstr ""
#~ "Le velocità di trasferimento dipendono dagli utenti dai quali stai "
#~ "scaricando. Alcuni utenti saranno più veloci rispetto ad altri."

#~ msgid "Started Downloads"
#~ msgstr "Download Iniziati"

#~ msgid "Started Uploads"
#~ msgstr "Upload Iniziati"

#~ msgid "Replace censored letters with:"
#~ msgstr "Sostituisci le lettere censurate con:"

#~ msgid "Censored Patterns"
#~ msgstr "Corrispondenze Censurate"

#~ msgid "Replacements"
#~ msgstr "Sostituzioni"

#~ msgid ""
#~ "If a user on the Soulseek network searches for a file that exists in your "
#~ "shares, search results will be sent to the user."
#~ msgstr ""
#~ "Se un utente sulla rete Soulseek cerca un file presente nelle tue "
#~ "condivisioni, i risultati della ricerca verranno inviati all'utente."

#~ msgid "Send to Player"
#~ msgstr "Invia al Player"

#~ msgid "Send to _Player"
#~ msgstr "Invia al _Player"

#~ msgid "_Open in File Manager"
#~ msgstr "Apri nel Gestore dei File"

#~ msgid "Media player command:"
#~ msgstr "Comando Media Player:"

#, python-format
#~ msgid ""
#~ "Unable to save download to username subfolder, falling back to default "
#~ "download folder. Error: %s"
#~ msgstr ""
#~ "Impossibile salvare il download nella sottocartella del nome utente, "
#~ "ripiego sulla cartella di download predefinita. Errore: %s"

#~ msgid "Buddy-only"
#~ msgstr "Solo Amici"

#~ msgid "Share with buddies only"
#~ msgstr "Condividi solo con gli amici"

#~ msgid "_Quit…"
#~ msgstr "Esci…"

#~ msgid "_Configure Shares"
#~ msgstr "_Configura Condivisi"

#~ msgid "Remote file error"
#~ msgstr "Errore di file remoto"

#, python-format
#~ msgid "Cannot find %(option1)s or %(option2)s, please install either one."
#~ msgstr ""
#~ "Impossibile trovare %(option1)s o %(option2)s,, installa uno dei due."

#, python-format
#~ msgid "%(num)s folders found before rescan"
#~ msgstr "%(num)s cartelle trovate prima della reindicizzazione"

#, python-format
#~ msgid "Failed to process the following databases: %(names)s"
#~ msgstr "Elaborazione dei seguenti database fallita: %(names)s"

#, python-format
#~ msgid "Failed to send number of shared files to the server: %s"
#~ msgstr "Invio del numero di file condivisi al server fallito: %s"

#~ msgid "Quit / Run in Background"
#~ msgstr "Esci / Esegui in Background"

#~ msgid "Limit buddy-only shares to trusted buddies"
#~ msgstr "Limita condivisi con amici ad amici attendibili"

#~ msgid "Shared"
#~ msgstr "Condiviso"

#~ msgid "Search Files and Folders"
#~ msgstr "Cerca file e cartelle"

#~ msgid "Out of Date"
#~ msgstr "Versione obsoleta"

#, python-format
#~ msgid "Version %(version)s is available, released on %(date)s"
#~ msgstr "È disponibile la versione %(version)s, rilasciata il %(date)s"

#, python-format
#~ msgid "You are using a development version of %s"
#~ msgstr "Stai usando una versione di sviluppo di %s"

#, python-format
#~ msgid "You are using the latest version of %s"
#~ msgstr "Stai usando la versione più recente di %s"

#~ msgid "Latest Version Unknown"
#~ msgstr "Ultima Versione Sconosciuta"

#~ msgid "Prefer Dark _Mode"
#~ msgstr "Preferisci Modalità Scura"

#~ msgid "Show _Log History Pane"
#~ msgstr "Mostra Crono_logia Registro"

#~ msgid "Buddy List in Separate Tab"
#~ msgstr "Lista Amici in Scheda Separata"

#~ msgid "Buddy List Always Visible"
#~ msgstr "Lista Amici Sempre Visibile"

#~ msgid "_View"
#~ msgstr "_Visualizza"

#~ msgid "_Open Log Folder"
#~ msgstr "Apri Cartella Registro"

#~ msgid "_Browse Folder(s)"
#~ msgstr "Sfoglia Cartella(e)"

#~ msgid "Kibibytes (2^10 bytes) per second."
#~ msgstr "Kibibyte (2^10 byte) per secondo."

#~ msgid "Sent to users as the reason for being geo blocked."
#~ msgstr "Inviato agli utenti come motivo del blocco geografico."

#~ msgid "Sent to users as the reason for being banned."
#~ msgstr "Inviato agli utenti come motivo per essere stato bloccato."

#~ msgid ""
#~ "Once you interact with the application being away, status will be set to "
#~ "online."
#~ msgstr ""
#~ "Se interagisci con l'applicazione mentre sei assente, lo stato verrà "
#~ "impostato su online."

#~ msgid "Each user may queue a maximum of either:"
#~ msgstr "Ogni utente può accodare un massimo di:"

#~ msgid "Mebibytes (2^20 bytes)."
#~ msgstr "Mebibyte (2^17 byte)."

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "files"
#~ msgstr "file"

#~ msgid "Queue Behavior"
#~ msgstr "Comportamento Coda"

#~ msgid ""
#~ "If disabled, slots will automatically be determined by available "
#~ "bandwidth limitations."
#~ msgstr ""
#~ "Se disabilitato, gli slot verranno determinati dalle risorse di banda "
#~ "disponibili."

#~ msgid "Note that the operating system's theme may take precedence."
#~ msgstr ""
#~ "Considera che il tema del sistema operativo potrebbe avere la precedenza."

#~ msgid "By default the leftmost tab is activated at startup"
#~ msgstr "Di predefinito, la scheda all'estrema sinistra è attivata all'avvio"

#, python-format
#~ msgid "Quit %(program)s %(version)s, %(status)s!"
#~ msgstr "Esci da %(program)s %(version)s, %(status)s!"

#~ msgid "terminated"
#~ msgstr "terminato"

#~ msgid "done"
#~ msgstr "fatto"

#~ msgid "Remember choice"
#~ msgstr "Ricorda preferenza"

#~ msgid "Kosovo"
#~ msgstr "Kosovo"

#~ msgid "Unknown Network Interface"
#~ msgstr "Interfaccia di Rete Sconosciuta"

#~ msgid "Listening Port Unavailable"
#~ msgstr "Porta di ascolto non disponibile"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr "Il server sembra essere non raggiungibile, tentativo fra %i secondi"

#~ msgid ""
#~ "Nicotine+ uses peer-to-peer networking to connect to other users. In "
#~ "order to allow users to connect to you without trouble, an open listening "
#~ "port is crucial."
#~ msgstr ""
#~ "Nicotine+ utilizza la rete peer-to-peer per connettersi ad altri utenti. "
#~ "Per consentire agli utenti di connettersi a te senza problemi, è "
#~ "fondamentale una porta di ascolto aperta."

#~ msgid "--- disconnected ---"
#~ msgstr "--- disconnesso ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- riconnesso ---"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Earth"
#~ msgstr "Terra"

#~ msgid "Czech Republic"
#~ msgstr "Repubblica Ceca"

#~ msgid "Turkey"
#~ msgstr "Turchia"

#~ msgid "Joined Rooms "
#~ msgstr "Canali Acceduti "

#~ msgid "_Auto-join Room"
#~ msgstr "Auto-partecipa nel Canale"

#~ msgid "Escaped"
#~ msgstr "Con Carattere di Escape"

#~ msgid "Escape filter"
#~ msgstr "Filtro di escape"

#~ msgid "Enter a text pattern and what to replace it with"
#~ msgstr "Inserire il pattern di testo e con quale testo sostituirlo"

#, python-format
#~ msgid "%(num)s folders found before rescan, rebuilding…"
#~ msgstr "%(num)s cartelle trovate prima dell'indicizzazione, ricostruzione…"

#, python-format
#~ msgid "No listening port is available in the specified port range %s–%s"
#~ msgstr ""
#~ "Nessuna porta di ascolto disponibile nell'intervallo di porte specificato "
#~ "%s–%s"

#~ msgid ""
#~ "Choose a range to select a listening port from. The first available port "
#~ "in the range will be used."
#~ msgstr ""
#~ "Scegli un intervallo da cui selezionare una porta di ascolto. Verrà "
#~ "utilizzata la prima porta disponibile nell'intervallo."

#~ msgid "First Port"
#~ msgstr "Prima porta"

#~ msgid "to"
#~ msgstr "a"

#~ msgid "Last Port"
#~ msgstr "Ultima porta"

#, python-format
#~ msgid "Cannot find %s or newer, please install it."
#~ msgstr "Impossibile trovare %s o più recente, per favore installalo."

#~ msgid ""
#~ "Cannot import the Gtk module. Bad install of the python-gobject module?"
#~ msgstr ""
#~ "Impossibile importare il modulo Gtk. Il modulo python-gobject è "
#~ "installato correttamente?"

#, python-format
#~ msgid ""
#~ "You are using an unsupported version of GTK %(major_version)s. You should "
#~ "install GTK %(complete_version)s or newer."
#~ msgstr ""
#~ "Stai usando una versione non supportata di GTK %(major_version)s. "
#~ "Dovresti installare GTK %(complete_version)s o più recente."

#~ msgid "User Info"
#~ msgstr "Dettagli Utente"

#~ msgid "Zoom 1:1"
#~ msgstr "Dimensione originale"

#~ msgid "Zoom In"
#~ msgstr "Ingrandisci"

#~ msgid "Zoom Out"
#~ msgstr "Riduci"

#~ msgid "Show User I_nfo"
#~ msgstr "Mostra I_nfo Utente"

#~ msgid "Request User's Info"
#~ msgstr "Richiedi Dettagli Utente"

#~ msgid "Request User's Shares"
#~ msgstr "Richiedi Condivisi da Utente"

#~ msgid "Request User Info"
#~ msgstr "Ottieni Dettagli Utente"

#~ msgid "Enter the name of the user whose info you want to see:"
#~ msgstr "Inserisci il nome dell'utente di cui desideri ricevere dettagli:"

#~ msgid "Request Shares List"
#~ msgstr "Ottieni Lista Condivisi"

#, python-format
#~ msgid "Invalid Soulseek URL: %s"
#~ msgstr "URL Soulseek non valido: %s"

#~ msgid ""
#~ "Users on the Soulseek network will be able to download files from folders "
#~ "you share. Sharing files is crucial for the health of the Soulseek "
#~ "network."
#~ msgstr ""
#~ "Gli utenti della rete Soulseek potranno scaricare file dalle cartelle che "
#~ "condividi. La condivisione di file è fondamentale per la salute della "
#~ "rete Soulseek."

#~ msgid "Update I_nfo"
#~ msgstr "Aggiorna Dettagli"

#~ msgid "Chat Room Commands"
#~ msgstr "Comandi in Canale Chat"

#~ msgid "/join /j 'room'"
#~ msgstr "/join /j 'canale'"

#~ msgid "Join room 'room'"
#~ msgstr "Entra nel canale 'canale'"

#~ msgid "/me 'message'"
#~ msgstr "/me 'messaggio'"

#~ msgid "Display the Now Playing script's output"
#~ msgstr "Mostra l'output dello script Sta Ascoltando"

#~ msgid "/add /ad 'user'"
#~ msgstr "/add /ad 'utente'"

#~ msgid "Add user 'user' to your buddy list"
#~ msgstr "Aggiungi utente 'utente' ai tuoi amici"

#~ msgid "/rem /unbuddy 'user'"
#~ msgstr "/rem /unbuddy 'utente"

#~ msgid "Remove user 'user' from your buddy list"
#~ msgstr "Rimuovi utente 'utente' dai tuoi amici"

#~ msgid "/ban 'user'"
#~ msgstr "/ban 'utente'"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "Aggiungi l'utente 'utente' alla tua lista degli utenti bloccati"

#~ msgid "/unban 'user'"
#~ msgstr "/unban 'utente'"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "Rimuovi l'utente 'utente' dalla lista dei bloccati"

#~ msgid "/ignore 'user'"
#~ msgstr "/ignore 'utente'"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "Aggiungi l'utente 'utente' alla tua lista degli utenti ignorati"

#~ msgid "/unignore 'user'"
#~ msgstr "/unignore 'utente'"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "Rimuovi l'utente 'utente' dalla lista degli ignorati"

#~ msgid "/browse /b 'user'"
#~ msgstr "/browse /b 'utente'"

#~ msgid "/whois /w 'user'"
#~ msgstr "/whois /w 'utente'"

#~ msgid "Request info for 'user'"
#~ msgstr "Richiedi dettagli per 'utente'"

#~ msgid "/ip 'user'"
#~ msgstr "/ip 'utente'"

#~ msgid "Show IP for user 'user'"
#~ msgstr "Mostra IP per l'utente 'utente'"

#~ msgid "/search /s 'query'"
#~ msgstr "/search /s 'interrogazione'"

#~ msgid "Start a new search for 'query'"
#~ msgstr "Comincia una nuova ricerca per 'query'"

#~ msgid "/rsearch /rs 'query'"
#~ msgstr "/rsearch /rs 'interrogazione'"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "Cerca la 'query' nei canali"

#~ msgid "/bsearch /bs 'query'"
#~ msgstr "/bsearch /bs 'interrogazione'"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "Cerca nella lista degli amici 'interrogazione'"

#~ msgid "/usearch /us 'user' 'query'"
#~ msgstr "/usearch /us 'utente' 'interrogazione'"

#~ msgid "/msg 'user' 'message'"
#~ msgstr "/msg 'utente' 'messaggio'"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "Invia il messaggio 'messaggio' all'utente 'utente'"

#~ msgid "/pm 'user'"
#~ msgstr "/pm 'utente'"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr "Apri una finestra con una chat privata per l'utente 'utente'"

#~ msgid "Private Chat Commands"
#~ msgstr "Comandi in Chat Privata"

#~ msgid "Add user to your ban list"
#~ msgstr "Aggiungi utente alla tua lista bloccati"

#~ msgid "Add user to your ignore list"
#~ msgstr "Aggiungi utente alla tua lista ignorati"

#~ msgid "Browse shares of user"
#~ msgstr "Sfoglia condivisi dell'utente"

#~ msgid "File size"
#~ msgstr "Dimensione file"

#~ msgid "Clear Active Filters"
#~ msgstr "Pulisci filtri attivi"

#~ msgid "Cycle through completions when pressing tab-key"
#~ msgstr "Scorri i completamenti col tasto Tab"

#~ msgid "Hide drop-down when only one matches"
#~ msgstr "Nascondi menu a tendina quando c'è solo una corrispondenza"

#~ msgid "Download folders in reverse alphanumerical order"
#~ msgstr "Download delle cartelle in ordine alfanumerico invertito"

#~ msgid "Incomplete file folder:"
#~ msgstr "Cartella dei file incompleti:"

#~ msgid "Download folder:"
#~ msgstr "Cartella di download:"

#~ msgid "Save buddies' uploads to:"
#~ msgstr "Salva upload amici in:"

#~ msgid "Use UPnP to forward listening port"
#~ msgstr "Usa UPnP per l'inoltro della porta di ascolto"

#~ msgid ""
#~ "Share folders with every Soulseek user or buddies, allowing contents to "
#~ "be downloaded directly from your device. Hidden files are never shared."
#~ msgstr ""
#~ "Condividi le cartelle con ogni utente o amico di Soulseek, consentendo il "
#~ "download dei contenuti direttamente dal tuo dispositivo. I file nascosti "
#~ "non sono mai condivisi."

#~ msgid "Secondary Tabs"
#~ msgstr "Schede Secondarie"

#~ msgid "Chat room tab bar position:"
#~ msgstr "Posizione barra delle schede della chat:"

#~ msgid "Private chat tab bar position:"
#~ msgstr "Posizione barra delle schede della chat privata:"

#~ msgid "Search tab bar position:"
#~ msgstr "Posizione barra delle schede di ricerca:"

#~ msgid "User info tab bar position:"
#~ msgstr "Posizione barra della scheda dettagli utente:"

#~ msgid "User browse tab bar position:"
#~ msgstr "Posizione barra delle schede sfoglia utente:"

#~ msgid "Tab Labels"
#~ msgstr "Etichette Scheda"

#~ msgid "_Refresh Info"
#~ msgstr "Aggiorna Dettagli"

#~ msgid "Block IP Address"
#~ msgstr "Blocca Indirizzo IP"

#~ msgid "Connected"
#~ msgstr "Connesso"

#~ msgid "Disconnected"
#~ msgstr "Disconnesso"

#~ msgid "Disconnected (Tray)"
#~ msgstr "Disconnesso (Vassoio)"

#~ msgid "User(s)"
#~ msgstr "Utente(i)"

#, python-format
#~ msgid "Alias \"%s\" returned nothing"
#~ msgstr "Alias \"%s\" non ha ritornato nulla"

#~ msgid "Alternative Speed Limits"
#~ msgstr "Limiti Alternativi Velocità"

#~ msgid "Last played"
#~ msgstr "Ultimo ascolto"

#~ msgid "Playing now"
#~ msgstr "Sta Ascoltando"

#, python-format
#~ msgid "Error code %(code)s: %(description)s"
#~ msgstr "Codice di errore %(code)s: %(description)s"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "Nessun alias (%s)"

#~ msgid "Aliases:"
#~ msgstr "Alias:"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "Rimosso alias %(alias)s: %(action)s\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "Nessun alias (%(alias)s)\n"

#~ msgid "Aliases"
#~ msgstr "Alias"

#~ msgid "/alias /al 'command' 'definition'"
#~ msgstr "/alias /al 'comando' 'definizione'"

#~ msgid "Add a new alias"
#~ msgstr "Aggiungi un nuovo alias"

#~ msgid "/unalias /un 'command'"
#~ msgstr "/unalias /un 'comando'"

#~ msgid "Remove an alias"
#~ msgstr "Rimuovi un alias"

#~ msgid "Chat History"
#~ msgstr "Cronologia Chat"

#~ msgid "Command aliases"
#~ msgstr "Alias di comando"

#~ msgid "Limit download speed to (KiB/s):"
#~ msgstr "Limita velocità download a (KiB/s):"

#~ msgid "Author(s):"
#~ msgstr "Autore(i):"

#~ msgid "Limit upload speed to (KiB/s):"
#~ msgstr "Limita velocità upload a (KiB/s):"

#~ msgid "Tabs show user status icons instead of status text"
#~ msgstr ""
#~ "Le schede mostrano le icone di stato dell'utente invece del testo di stato"

#~ msgid "Show file path tooltips in file list views"
#~ msgstr "Visualizza percorso dei file nelle viste di elenco file"

#~ msgid "Colored and clickable usernames"
#~ msgstr "Nomi utente colorati e cliccabili"

#~ msgid "Notification changes the tab's text color"
#~ msgstr "La notifica cambia il colore di testo delle schede"

#~ msgid "Cancel"
#~ msgstr "Annulla"

#~ msgid "OK"
#~ msgstr "OK"

#~ msgid "_Add to Buddy List"
#~ msgstr "_Aggiungi a Lista Amici"

#~ msgid "use non-default user data directory for e.g. list of downloads"
#~ msgstr ""
#~ "usa cartella dei dati utente non predefinita per es. elenco di download"

#, python-format
#~ msgid "Unknown config section '%s'"
#~ msgstr "Sezione di configurazione sconosciuta '%s'"

#, python-format
#~ msgid "Unknown config option '%(option)s' in section '%(section)s'"
#~ msgstr ""
#~ "Opzione di configurazione sconosciuta '%(option)s' nella sezione "
#~ "'%(section)s'"

#, python-format
#~ msgid "Version %s is available"
#~ msgstr "È disponibile la versione %s"

#, python-format
#~ msgid "released on %s"
#~ msgstr "rilasciata il %s"

#~ msgid "Nicotine+ is running in the background"
#~ msgstr "Nicotine+ è in esecuzione in background"

#, python-format
#~ msgid "Private message from %s"
#~ msgstr "Messaggio privato da %s"

#~ msgid "Aborted"
#~ msgstr "Annullato"

#~ msgid "Blocked country"
#~ msgstr "Paese escluso"

#~ msgid "Finished / Aborted"
#~ msgstr "Completati / Interrotti"

#~ msgid "Close tab"
#~ msgstr "Chiudi scheda"

#, python-format
#~ msgid "You've been mentioned in the %(room)s room"
#~ msgstr "Qualcuno parla di te nel canale %(room)s"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "Il commando %s non è riconosciuto"

#, python-format
#~ msgid "(Warning: %(realuser)s is attempting to spoof %(fakeuser)s) "
#~ msgstr ""
#~ "(Attenzione: %(realuser)s sta tentando di falsificare l'identità di "
#~ "%(fakeuser)s) "

#, python-format
#~ msgid ""
#~ "The network interface you specified, '%s', does not exist. Change or "
#~ "remove the specified network interface and restart Nicotine+."
#~ msgstr ""
#~ "L'interfaccia di rete specificata, '%s', non esiste. Modifica o rimuovi "
#~ "l'interfaccia di rete specificata e riavvia Nicotine+."

#~ msgid ""
#~ "The range you specified for client connection ports was {}-{}, but none "
#~ "of these were usable. Increase and/or "
#~ msgstr ""
#~ "L'intervallo specificato per le porte di connessione del client erano {}-"
#~ "{}, ma sono risultate inaccessibili. Incrementa e/o "

#~ msgid ""
#~ "Note that part of your range lies below 1024, this is usually not allowed "
#~ "on most operating systems with the exception of Windows."
#~ msgstr ""
#~ "Nota che parte dell'intervallo è inferiore a 1024 e ciò generalmente non "
#~ "è permesso su molti sistemi operativi, ad eccezione di Windows."

#, python-format
#~ msgid "Rescan progress: %s"
#~ msgstr "Progresso indicizzazione: %s"

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "Errore OS: %s"

#~ msgid "UPnP is not available on this network"
#~ msgstr "UPnP non è disponibile su questa rete"

#, python-format
#~ msgid "Failed to map the external WAN port: %(error)s"
#~ msgstr "Mappatura della porta esterna WAN fallita: %(error)s"

#~ msgid "Room wall"
#~ msgstr "Bacheca Canale"

#~ msgid ""
#~ "The default listening port '2234' works fine in most cases. If you need "
#~ "to use a different port, you will be able to modify it in the preferences "
#~ "later."
#~ msgstr ""
#~ "La porta di ascolto predefinita \"2234\" funziona bene nella maggior "
#~ "parte dei casi. Se hai bisogno di usare una porta diversa, potrai "
#~ "modificarla nelle preferenze in un secondo momento."

#~ msgid "Show users with similar interests"
#~ msgstr "Mostra utenti con interessi simili"

#~ msgid "Menu"
#~ msgstr "Menu"

#~ msgid "Expand / Collapse all"
#~ msgstr "Espandi / Contrai tutto"

#~ msgid "Configure shares"
#~ msgstr "Configura condivisi"

#~ msgid "Create or join room…"
#~ msgstr "Crea o entra nel canale…"

#~ msgid "_Room List"
#~ msgstr "Lista Canali"

#~ msgid "Enable alternative download and upload speed limits"
#~ msgstr "Abilita limiti di velocità di download e upload alternativi"

#~ msgid "Show log history"
#~ msgstr "Mostra cronologia registro"

#~ msgid "Result grouping mode"
#~ msgstr "Modalità raggruppamento risultati"

#~ msgid "Free slot"
#~ msgstr "Slot libero"

#~ msgid "Save shares list to disk"
#~ msgstr "Salva lista condivisi su disco"

#~ msgid "_Away"
#~ msgstr "_Assente"

#, python-format
#~ msgid "Failed to load ui file %(file)s: %(error)s"
#~ msgstr "Errore nel caricamento del file di interfaccia %(file)s: %(error)s"

#~ msgid ""
#~ "Attempting to reset index of shared files due to an error. Please rescan "
#~ "your shares."
#~ msgstr ""
#~ "Tentativo di reimpostare l'indice dei file condivisi a causa di un "
#~ "errore. Per favore, scansiona di nuovo i tuoi condivisi."

#~ msgid ""
#~ "File index of shared files could not be accessed. This could occur due to "
#~ "several instances of Nicotine+ being active simultaneously, file "
#~ "permission issues, or another issue in Nicotine+."
#~ msgstr ""
#~ "Impossibile accedere all'indice dei file condivisi. Ciò potrebbe "
#~ "verificarsi a causa di diverse istanze di Nicotine+ attive "
#~ "contemporaneamente, problemi di autorizzazione dei file o un altro "
#~ "problema di Nicotine+."

#~ msgid "Nicotine+ is a Soulseek client"
#~ msgstr "Nicotine+ è un client per Soulseek"

#~ msgid "enable the tray icon"
#~ msgstr "abilita icona nel vassoio"

#~ msgid "disable the tray icon"
#~ msgstr "disabilita icona nel vassoio"

#~ msgid "Get Soulseek Privileges…"
#~ msgstr "Ottieni Privilegi Soulseek…"

#, python-format
#~ msgid ""
#~ "Public IP address is <b>%(ip)s</b> and active listening port is "
#~ "<b>%(port)s</b>"
#~ msgstr ""
#~ "L'indirizzo IP pubblico è <b>%(ip)s</b> e la porta di ascolto attiva è "
#~ "<b>%(port)s</b>"

#~ msgid "unknown"
#~ msgstr "sconosciuto"

#~ msgid "Notification"
#~ msgstr "Notifica"

#~ msgid "Length"
#~ msgstr "Durata"

#, python-format
#~ msgid "Picture not saved, %s already exists."
#~ msgstr "Immagine non salvata, %s esiste già."

#~ msgid "_Open"
#~ msgstr "Apri"

#~ msgid "_Save"
#~ msgstr "_Salva"

#, python-format
#~ msgid "Failed to load plugin '%s', could not find it."
#~ msgstr "Caricamento dell'estensione '%s' fallito, impossibile trovarla."

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "Errore di I/O: %s"

#, python-format
#~ msgid "Failed to open file path: %s"
#~ msgstr "Apertura del percorso file fallita: %s"

#, python-format
#~ msgid "Failed to open URL: %s"
#~ msgstr "Apertura di URL fallita: %s"

#~ msgid "_Log Conversation"
#~ msgstr "Registra Conversazione"

#~ msgid "Result Filter List"
#~ msgstr "Lista Filtri Risultati"

#~ msgid "Prepend < or > to find files less/greater than the given value."
#~ msgstr ""
#~ "Usa < o > per trovare i file con dimensioni minori/maggiori o uguali al "
#~ "valore."

#~ msgid ""
#~ "VBR files display their average bitrate and are typically lower in "
#~ "bitrate than a compressed 320 kbps CBR file of the same audio quality."
#~ msgstr ""
#~ "I file VBR mostrano il loro bitrate medio e in genere hanno un bitrate "
#~ "inferiore rispetto a un file CBR compresso da 320 kbps della stessa "
#~ "qualità audio."

#~ msgid "Like Size above, =, <, and > can be used."
#~ msgstr "Come Dimensione sopra, è possibile utilizzare =, < e >."

#~ msgid ""
#~ "Prevent write access by other programs for files being downloaded (turn "
#~ "off for NFS)"
#~ msgstr ""
#~ "Impedisci l'accesso in scrittura da parte di altri programmi per i file "
#~ "ricevuti (disattiva per NFS)"

#~ msgid "Where incomplete downloads are temporarily stored."
#~ msgstr "Dove vengono conservati temporaneamente i download incompleti."

#~ msgid ""
#~ "Where buddies' uploads will be stored (with a subfolder created for each "
#~ "buddy)."
#~ msgstr ""
#~ "Dove saranno salvati gli upload degli amici (sottocartella per ogni "
#~ "amico)."

#~ msgid "Toggle away status after minutes of inactivity:"
#~ msgstr "Abilita stato Assente dopo minuti di inattività:"

#~ msgid "Protocol:"
#~ msgstr "Protocollo:"

#~ msgid "Icon theme folder (requires restart):"
#~ msgstr "Cartella tema icone (richiede riavvio):"

#~ msgid "Establishing connection"
#~ msgstr "Connessione in corso"

#~ msgid "Clear Groups"
#~ msgstr "Pulisci Gruppi"

#~ msgid "User List"
#~ msgstr "Lista Utenti"

#~ msgid "_Reset Statistics…"
#~ msgstr "Azze_ra Statistiche…"

#~ msgid "Clear _Downloads…"
#~ msgstr "Pulisci Download…"

#~ msgid "Clear Uploa_ds…"
#~ msgstr "Pulisci Uploa_d…"

#~ msgid "Block User's IP Address"
#~ msgstr "Blocca Indirizzo IP dell'Utente"

#~ msgid "Ignore User's IP Address"
#~ msgstr "Ignora Indirizzo IP dell' Utente"

#~ msgid "Usernames"
#~ msgstr "Nomi utente"

#~ msgid "Display logged chat room messages when a room is rejoined"
#~ msgstr "Mostra i messaggi registrati nelle chat quando si entra in una chat"

#~ msgid "Queue Position"
#~ msgstr "Posizione in Coda"

#, python-format
#~ msgid ""
#~ "User %(user)s is directly searching for \"%(query)s\", returning %(num)i "
#~ "results"
#~ msgstr ""
#~ "L'utente %(user)s sta cercando direttamente %(query)s\", ottenuti %(num)i "
#~ "risultati"

#~ msgid "Room wall (personal message set)"
#~ msgstr "Bacheca canale (messaggio personale impostato)"

#~ msgid "Your config file is corrupt"
#~ msgstr "Il file della tua configurazione è corrotto"

#, python-format
#~ msgid ""
#~ "We're sorry, but it seems your configuration file is corrupt. Please "
#~ "reconfigure Nicotine+.\n"
#~ "\n"
#~ "We renamed your old configuration file to\n"
#~ "%(corrupt)s\n"
#~ "If you open this file with a text editor you might be able to rescue some "
#~ "of your settings."
#~ msgstr ""
#~ "Spiacente, ma sembra che il file della tua configurazione sia corrotto. "
#~ "Riconfigura Nicotine+.\n"
#~ "\n"
#~ "Il tuo vecchio file di configurazione è stato rinominato in\n"
#~ "%(corrupt)s\n"
#~ "Potresti essere in grado di recuperare alcune delle tue impostazioni con "
#~ "editor di testo."

#~ msgid "User Description"
#~ msgstr "Descrizione Utente"

#~ msgid "User Interests"
#~ msgstr "Interessi Utente"

#~ msgid "User Picture"
#~ msgstr "Immagine Utente"

#~ msgid "Search Wishlist"
#~ msgstr "Ricerca Lista Desideri"

#, python-format
#~ msgid "Quitting Nicotine+ %(version)s, %(status)s…"
#~ msgstr "In uscita da Nicotine+ %(version)s, %(status)s…"

#, python-format
#~ msgid "Quit Nicotine+ %(version)s, %(status)s!"
#~ msgstr "Esci da Nicotine+ %(version)s, %(status)s!"

#~ msgid "User:"
#~ msgstr "Utente:"

#, python-format
#~ msgid "All %(ext)s"
#~ msgstr "Tutti %(ext)s"

#, python-format
#~ msgid "%(number)2s files "
#~ msgstr "%(number)2s files "

#~ msgid "Allow regular expressions for the filter's include and exclude"
#~ msgstr ""
#~ "Permetti espressioni regolari per i filtri di inclusione e esclusione"

#~ msgid "Close Nicotine+?"
#~ msgstr "Chiudere Nicotine+?"

#~ msgid "Do you really want to exit Nicotine+?"
#~ msgstr "Vuoi davvero uscire da Nicotine+?"

#~ msgid "Run in Background"
#~ msgstr "Esegui in Background"

#~ msgid "_Online Notify"
#~ msgstr "Notifica Connessione"

#~ msgid "_Prioritize User"
#~ msgstr "Dai _Priorità all'Utente"

#~ msgid "_Trust User"
#~ msgstr "Fida_ti"

#~ msgid "Request User's IP Address"
#~ msgstr "Richiedi Indirizzo IP dell'Utente"

#~ msgid "Request IP Address"
#~ msgstr "Richiedi Indirizzo IP"

#~ msgid "Enter the name of the user whose IP address you want to see:"
#~ msgstr ""
#~ "Inserisci il nome dell'utente di cui desideri ricevere l'indirizzo IP:"

#~ msgid "Downloaded"
#~ msgstr "Ricevuto"

#, python-format
#~ msgid "Failed to add download %(filename)s to shared files: %(error)s"
#~ msgstr ""
#~ "Impossibile aggiungere il download %(filename)s ai file condivisi: "
#~ "%(error)s"

#~ msgid "Automatically share completed downloads"
#~ msgstr "Condividi automaticamente download completati"

#~ msgid ""
#~ "The equivalent of adding your download folder as a public share, however "
#~ "files downloaded to this folder will be automatically accessible to "
#~ "others (no rescan required)."
#~ msgstr ""
#~ "Equivale ad aggiungere la cartella di download come condivisi con tutti, "
#~ "tuttavia i file ricevuti in questa cartella saranno automaticamente "
#~ "accessibili agli altri (nessuna indicizzazione richiesta)."

#~ msgid "Unable to Share Folder"
#~ msgstr "Impossibile Condividere Cartella"

#~ msgid "The chosen virtual name is empty"
#~ msgstr "Il nome virtuale scelto è vuoto"

#~ msgid "The chosen virtual name already exists"
#~ msgstr "Il nome virtuale scelto esiste già"

#~ msgid "The chosen folder is already shared"
#~ msgstr "La cartella scelta è già condivisa"

#~ msgid "Set Virtual Name"
#~ msgstr "Imposta Nome Virtuale"

#, python-format
#~ msgid "Enter virtual name for '%(dir)s':"
#~ msgstr "Inserisci nome virtuale per '%(dir)s':"

#~ msgid "The chosen virtual name is either empty or already exists"
#~ msgstr "Il nome virtuale scelto è vuoto o esiste già"

#~ msgid "The chosen folder is already shared."
#~ msgstr "La cartella scelta è già condivisa."

#, python-format
#~ msgid "%s Properties"
#~ msgstr "Proprietà di %s"

#~ msgid "Finished rescanning shares"
#~ msgstr "Indicizzazione dei condivisi completata"

#~ msgid "Plugin List"
#~ msgstr "Elenco Estensioni"

#, python-format
#~ msgid "Error while scanning %(path)s: %(error)s"
#~ msgstr "Errore di indicizzazione per %(path)s: %(error)s"

#~ msgid "Show _Log Pane"
#~ msgstr "Mostra Pannello Registro"

#~ msgid "Addresses"
#~ msgstr "Indirizzi"

#~ msgid "Handler"
#~ msgstr "Gestore"

#~ msgid "Could not enable plugin."
#~ msgstr "Impossibile attivare l'estensione."

#~ msgid "Could not disable plugin."
#~ msgstr "Impossibile disattivare l'estensione."

#~ msgid "Transfers"
#~ msgstr "Trasferimenti"

#~ msgid "Ban List"
#~ msgstr "Lista Bloccati"

#~ msgid "Ignore List"
#~ msgstr "Lista Ignorati"

#~ msgid "Censor & Replace"
#~ msgstr "Censura & Sostituisci"

#~ msgid "Completion"
#~ msgstr "Completamento"

#~ msgid "Categories"
#~ msgstr "Categorie"

#~ msgid "Upload Folder To…"
#~ msgstr "Carica Cartella In…"

#~ msgid "Upload Folder Recursive To…"
#~ msgstr "Carica Sottocartelle In…"

#~ msgid "Download _Recursive"
#~ msgstr "Scarica Sottocartelle"

#~ msgid "Download R_ecursive To…"
#~ msgstr "Scarica Sottocartelle In…"

#~ msgid "Up_load File(s)"
#~ msgstr "Carica File"

#, python-format
#~ msgid ""
#~ "Error while attempting to display folder '%(folder)s', reported error: "
#~ "%(error)s"
#~ msgstr ""
#~ "Errore nel tentativo di mostrare la cartella '%(folder)s', errore "
#~ "riportato: %(error)s"

#~ msgid "Select Destination for Downloading Folder with Subfolders from User"
#~ msgstr ""
#~ "Seleziona Destinazione per Download Cartella con Sotto-cartelle "
#~ "dall'Utente"

#~ msgid "Select Destination for Downloading a Folder from User"
#~ msgstr "Seleziona Destinazione per Download Cartella dall'Utente"

#~ msgid "Select Destination for Downloading File(s) from User"
#~ msgstr "Seleziona Destinazione per Download File dall'Utente"

#~ msgid "Wishes"
#~ msgstr "Desideri"

#~ msgid "privileged"
#~ msgstr "privilegiato"

#~ msgid "prioritized"
#~ msgstr "prioritario"

#~ msgid "Blocked IP Addresses"
#~ msgstr "Indirizzi IP Bloccati"

#~ msgid "Complete buddy names"
#~ msgstr "Completa nome amici"

#~ msgid "Complete usernames in chat rooms"
#~ msgstr "Completa nomi utente nelle chat"

#~ msgid "Complete room names"
#~ msgstr "Completa nomi canale"

#~ msgid "Drop-down List"
#~ msgstr "Menu a Tendina"

#~ msgid "File Manager command ($ for file path):"
#~ msgstr "Comando per il Gestore file ($ per percorso file):"

#~ msgid "Media Player command ($ for file path):"
#~ msgstr "Comando Media Player ($ per percorso file):"

#~ msgid "Ignored IP Addresses"
#~ msgstr "Indirizzi IP Ignorati"

#~ msgid "Display timestamps"
#~ msgstr "Mostra marcatura temporale"

#~ msgid "Notification Popups"
#~ msgstr "Notifiche"

#~ msgid "Show notification popup when a file has finished downloading"
#~ msgstr "Mostra notifica quando un file ha terminato il download"

#~ msgid "Show notification popup when a folder has finished downloading"
#~ msgstr "Mostra notifica quando una cartella ha terminato il download"

#~ msgid "Show notification popup when you receive a private message"
#~ msgstr "Mostra notifica quando ricevi un messaggio privato"

#~ msgid "Show notification popup when someone sends a message in a chat room"
#~ msgstr "Mostra notifica quando qualcuno invia un messaggio in una chat"

#~ msgid "Show notification popup when you are mentioned in a chat room"
#~ msgstr "Mostra notifica quando vieni menzionato in una chat"

#~ msgid "Tray"
#~ msgstr "Vassoio"

#~ msgid ""
#~ "Instances of $ will be replaced by the link. The default web browser of "
#~ "the system will be used in cases where a protocol has not been configured."
#~ msgstr ""
#~ "Le corrispondenze con $ saranno sostituite dal link. Nei casi in cui non "
#~ "è stato configurato un protocollo, verrà utilizzato il browser Web "
#~ "predefinito di sistema."

#~ msgid "Handler:"
#~ msgstr "Associazioni:"

#~ msgid "Primary Tabs"
#~ msgstr "Schede Primarie"

#~ msgid "Enable file path tooltips in search and transfer views"
#~ msgstr ""
#~ "Abilita descrizioni per percorso file nelle viste di ricerca e "
#~ "trasferimento"

#~ msgid ""
#~ "Displays the complete file path of a search result or file transfer when "
#~ "you hover a folder path or file name with your cursor."
#~ msgstr ""
#~ "Visualizza il percorso completo del file di un risultato di ricerca o di "
#~ "un trasferimento file al passaggio del mouse sul percorso di cartella o "
#~ "sul nome del file."

#~ msgid "Show privately shared files in user shares"
#~ msgstr "Mostra file condivisi privatamente nelle condivisioni utente"

#~ msgid ""
#~ "Other clients may offer an option to send privately shared files when you "
#~ "browse their shares. Folders containing such files are prefixed with "
#~ "'[PRIVATE FOLDER]', and are not downloadable unless the uploader gives "
#~ "explicit permission."
#~ msgstr ""
#~ "Altri client possono offrire un'opzione per inviare file condivisi "
#~ "privatamente quando si esplorano le loro condivisioni. Le cartelle "
#~ "contenenti tali file hanno il prefisso '[PRIVATE FOLDER]' e non possono "
#~ "essere scaricate a meno che l'autore dell'upload non dia "
#~ "un'autorizzazione esplicita."

#~ msgid "Show _Debug Log Controls"
#~ msgstr "Mostra Controlli Registro Debug"

#~ msgid "Debug Logging"
#~ msgstr "Registrazione Debug"

#~ msgid "Virtual Name"
#~ msgstr "Nome Virtuale"

#~ msgid "Edit Virtual Name"
#~ msgstr "Modifica Nome Virtuale"

#~ msgid "Copy Folder URL"
#~ msgstr "Copia URL Cartella"

#~ msgid "Download _To…"
#~ msgstr "Scarica In…"

#~ msgid "Download"
#~ msgstr "Download"

#~ msgid "Upload"
#~ msgstr "Upload"

#~ msgid "Upload Folder's Contents"
#~ msgstr "Carica Contenuto Cartella"

#~ msgid "Rename"
#~ msgstr "Rinomina"

#~ msgid "File Lists"
#~ msgstr "Liste File"

#~ msgid "Copy File Path"
#~ msgstr "Copia Percorso File"

#~ msgid "R_emove Wish"
#~ msgstr "Rimuovi D_esiderio"

#, python-format
#~ msgid "It appears '%s' is not a directory, not loading plugins."
#~ msgstr "'%s' non è una cartella, estensioni non caricate."

#, python-format
#~ msgid "Your buddy, %s, is attempting to upload file(s) to you."
#~ msgstr "Il tuo amico, %s, sta provando a inviarti dei file."

#, python-format
#~ msgid ""
#~ "%s is not allowed to send you file(s), but is attempting to, anyway. "
#~ "Warning Sent."
#~ msgstr ""
#~ "%s non è autorizzato a inviarti file, ma ci sta provando comunque. Avviso "
#~ "Inviato."

#~ msgid "Client Version"
#~ msgstr "Versione Client"

#, python-format
#~ msgid "How many days of privileges should user %s be gifted?"
#~ msgstr "Quanti giorni di privilegi assegnare all'utente %s?"

#~ msgid ""
#~ "Buddies will have higher priority in the queue, the same as globally "
#~ "privileged users."
#~ msgstr ""
#~ "Gli amici avranno una priorità maggiore nella coda, la stessa degli "
#~ "utenti globali privilegiati."

#~ msgid "Privileged"
#~ msgstr "Privilegiato"

#~ msgid "_Privileged"
#~ msgstr "_Privilegiati"

#~ msgid "Comments"
#~ msgstr "Commenti"

#~ msgid "Edit _Comments…"
#~ msgstr "Modifica _Commenti…"

#~ msgid ""
#~ "Creates subfolders based on the user you are downloading from, and stores "
#~ "the downloaded file / folder there."
#~ msgstr ""
#~ "Crea sottocartelle in base all'utente da cui stai scaricando e memorizza "
#~ "il file / cartella scaricato lì."

#~ msgid "Login Details"
#~ msgstr "Dettagli Accesso"

#~ msgid "Advanced"
#~ msgstr "Avanzate"

#~ msgid "Port mapping renewal interval in hours:"
#~ msgstr "Intervallo di rinnovo della mappatura delle porte in ore:"

#~ msgid "Enable buddy-only shares"
#~ msgstr "Abilita solo condivisi con amici"

#~ msgid "Files will be uploaded in the order they were queued."
#~ msgstr "I file saranno inviati nell'ordine di accodamento."

#~ msgid "Rescanning normal shares…"
#~ msgstr "Indicizzazione dei condivisi…"

#~ msgid "Finished rescanning public shares"
#~ msgstr "Indicizzazione dei condivisi con tutti completata"

#~ msgid "Scanning Buddy Shares"
#~ msgstr "Indicizzazione Condivisi con Amici"

#~ msgid "_Rescan Public Shares"
#~ msgstr "Indicizza Condivisi con Tutti"

#~ msgid "Rescan B_uddy Shares"
#~ msgstr "Indicizza Condivisi con Amici"

#~ msgid "Rescan Public Shares"
#~ msgstr "Indicizza Condivisi con Tutti"

#~ msgid "Rescan Buddy Shares"
#~ msgstr "Indicizza Condivisi con Amici"

#~ msgid "Mark each shared folder as buddy-only"
#~ msgstr "Spunta ogni cartella condivisa come solo con amici"

#~ msgid ""
#~ "Overrides the per-share option, useful if you temporarily need to prevent "
#~ "public access to shares."
#~ msgstr ""
#~ "Ignora opzioni di condivisione, utile se è necessario impedire "
#~ "temporaneamente l'accesso pubblico alle condivisioni."

#~ msgid ""
#~ "Only users marked as trusted on your buddy list can access your buddy-"
#~ "only shares."
#~ msgstr ""
#~ "Solo gli utenti contrassegnati come attendibili nel tuo elenco di amici "
#~ "possono accedere ai condivisi con amici."

#~ msgid "Filtered out excluded search result "
#~ msgstr "Risultati di ricerca esclusi dal filtro "

#~ msgid "Filtered out inexact or incorrect search result "
#~ msgstr "Risultati di ricerca inesatti o incorretti esclusi dal filtro "

#, python-format
#~ msgid ""
#~ "Stored setting '%(key)s' is no longer present in the '%(name)s' plugin"
#~ msgstr ""
#~ "L'impostazione salvata '%(key)s' non è più presente nell'estensione "
#~ "'%(name)s'"

#, python-format
#~ msgid "Plugin %(module)s returned something weird, '%(value)s', ignoring"
#~ msgstr ""
#~ "L'estensione %(module)s presenta un insolito valore di ritorno, "
#~ "'%(value)s', ignorato"

#, python-format
#~ msgid "Inconsistent cache for '%(vdir)s', rebuilding '%(dir)s'"
#~ msgstr "Cache inconsistente per '%(vdir)s', rigenero '%(dir)s'"

#, python-format
#~ msgid "Dropping missing folder %(dir)s"
#~ msgstr "Rimozione della cartella mancante %(dir)s"

#~ msgid ""
#~ "Nicotine+ allows you to share folders directly from your computer. All "
#~ "the contents of these folders (with the exception of dotfiles) can be "
#~ "downloaded by other users on the Soulseek network. Public shares are "
#~ "available for every user, while users in your buddy list can access buddy-"
#~ "only shares in addition to public shares."
#~ msgstr ""
#~ "Nicotine+ ti permette di condividere cartelle direttamente dal tuo "
#~ "computer. Tutti i contenuti di queste cartelle (ad eccezione dei dotfile) "
#~ "possono essere scaricati da altri utenti sulla rete Soulseek. Le "
#~ "condivisioni pubbliche sono disponibili per ogni utente, mentre gli "
#~ "utenti della lista amici possono accedere ai tuoi condivisi con amici "
#~ "oltre a quelli condivisi con tutti."

#, python-format
#~ msgid "All tickers / wall messages for %(room)s:"
#~ msgstr "Tutti i messaggi personali / di bacheca di %(room)s:"

#~ msgid "Set your personal ticker"
#~ msgstr "Imposta il messaggio personale"

#~ msgid "Show all the tickers"
#~ msgstr "Mostra tutti i messaggi personali"

#~ msgid ""
#~ "Failed to scan shares. If Nicotine+ is currently running, please close "
#~ "the program before scanning."
#~ msgstr ""
#~ "Scansione dei condivisi fallita. Se Nicotine+ è attualmente in "
#~ "esecuzione, chiudi il programma prima di indicizzare."

#~ msgid "Are you sure you wish to exit Nicotine+ at this time?"
#~ msgstr "Sei sicuro di voler uscire da Nicotine+ ora?"

#~ msgid "Receive a User's IP Address"
#~ msgstr "Ricevi Indirizzo IP da Utente"

#~ msgid "Receive a User's Info"
#~ msgstr "Ricevi Dettagli da Utente"

#~ msgid "The server forbid us from doing wishlist searches."
#~ msgstr "Il server ci proibisce di fare ricerche dalla lista dei desideri."

#, python-format
#~ msgid ""
#~ "Your shares database is corrupted. Please rescan your shares and report "
#~ "any potential scanning issues to the developers. Error: %s"
#~ msgstr ""
#~ "Il database dei tuoi condivisi è corrotto. Reindicizza i tuoi condivisi e "
#~ "segnala qualsiasi potenziale problema di indicizzazione agli "
#~ "sviluppatori. Errore: %s"

#~ msgid ""
#~ "Please keep in mind that certain usernames may be taken. If this is the "
#~ "case, you will be prompted to change your username when connecting to the "
#~ "network."
#~ msgstr ""
#~ "Tieni presente che alcuni nomi utente potrebbero essere utilizzati. In "
#~ "questo caso, ti verrà chiesto di cambiare il tuo nome utente quando ti "
#~ "connetti alla rete."

#~ msgid "Enter the username of the person you to receive information about"
#~ msgstr "Inserisci il nome utente della persona su cui ricevere dettagli"

#~ msgid "Add user 'user' to your user list"
#~ msgstr "Aggiungi l'utente 'utente' alla tua lista degli utenti"

#~ msgid "Remove user 'user' from your user list"
#~ msgstr "Rimuovi l'utente 'utente' dalla lista dei bloccati"

#~ msgid "Request user info for user 'user'"
#~ msgstr "Richiesta dei dettagli utente per l'utente 'utente'"

#~ msgid "Add user to your user list"
#~ msgstr "Aggiungi utente alla tua lista utenti"

#~ msgid "Remove user from your user list"
#~ msgstr "Rimuovi utente dalla tua lista utenti"

#~ msgid "Respond to search requests containing minimum character count:"
#~ msgstr ""
#~ "Rispondi alle richieste di ricerca che contengono un numero minimo di "
#~ "caratteri:"

#~ msgid ""
#~ "Queued users will be uploaded one file at a time in a cyclical fashion."
#~ msgstr ""
#~ "Agli utenti in coda verrà inviato un file alla volta in modo ciclico."

#~ msgid ""
#~ "Could not execute now playing code. Are you sure you picked the right "
#~ "player?"
#~ msgstr ""
#~ "Impossibile eseguire la funzione Sta Ascoltando. Sei sicuro di aver "
#~ "scelto il player giusto?"

#~ msgid "/leave /l /part /p"
#~ msgstr "/leave /l /part /p"

#~ msgid "/clear /cl"
#~ msgstr "/clear /cl"

#~ msgid "/tick /t"
#~ msgstr "/tick /t"

#~ msgid "/tickers"
#~ msgstr "/tickers"

#~ msgid "/now"
#~ msgstr "/now"

#~ msgid "/away /a"
#~ msgstr "/away /a"

#~ msgid "/quit /q /exit"
#~ msgstr "/quit /q /exit"

#~ msgid "/close /c"
#~ msgstr "/close /c"

#~ msgid "/add /ad"
#~ msgstr "/add /ad"

#~ msgid "/rem /unbuddy"
#~ msgstr "/rem /unbuddy"

#~ msgid "/ban"
#~ msgstr "/ban"

#~ msgid "/unban"
#~ msgstr "/unban"

#~ msgid "/ignore"
#~ msgstr "/ignore"

#~ msgid "/unignore"
#~ msgstr "/unignore"

#~ msgid "/browse /b"
#~ msgstr "/browse /b"

#~ msgid "/whois /w"
#~ msgstr "/whois /w"

#~ msgid "/ip"
#~ msgstr "/ip"

#, python-format
#~ msgid "The password you've entered is invalid for user %s"
#~ msgstr "La password che hai inserito non è valida per l'utente %s"

#~ msgid ""
#~ "You can create a new Soulseek account or log in to an existing one by "
#~ "entering your desired details below. Please keep in mind that some "
#~ "usernames may already be taken. If you're unable to connect with your "
#~ "selected username, try choosing another one."
#~ msgstr ""
#~ "Puoi creare un nuovo account inserendo i dettagli desiderati qui sotto. "
#~ "Ricordati che alcuni nomi utente potrebbero già esistere. Se non riesci a "
#~ "collegarti col nome da te selezionato, provane un altro."

#~ msgid "Find..."
#~ msgstr "Trova..."

#~ msgid "Queued..."
#~ msgstr "In coda..."

#~ msgid "Clear All..."
#~ msgstr "Pulisci Tutto..."

#~ msgid "Close All Tabs..."
#~ msgstr "Chiudi Tutte le Schede..."

#~ msgid "Edit _Comments..."
#~ msgstr "Modifica _Commenti..."

#~ msgid "_Add..."
#~ msgstr "_Aggiungi..."

#~ msgid "Room..."
#~ msgstr "Canale..."

#~ msgid "Username..."
#~ msgstr "Nome utente..."

#~ msgid "Add Wish..."
#~ msgstr "Aggiungi Desiderio..."

#~ msgid "Include text..."
#~ msgstr "Includi testo..."

#~ msgid "Exclude text..."
#~ msgstr "Escludi testo..."

#~ msgid "File type..."
#~ msgstr "Tipo di file..."

#~ msgid "Bitrate..."
#~ msgstr "Bitrate..."

#~ msgid "Add..."
#~ msgstr "Aggiungi..."

#~ msgid "Edit..."
#~ msgstr "Modifica..."

#, python-format
#~ msgid ""
#~ "You are using an unsupported version of GTK.\n"
#~ "You should install GTK %s or newer."
#~ msgstr ""
#~ "Stai usando una versione non supportata di GTK.\n"
#~ "Dovresti installare GTK %s o più recente."

#~ msgid "_Search Files"
#~ msgstr "Cerca _file"

#~ msgid "_Downloads"
#~ msgstr "_Download"

#~ msgid "User I_nfo"
#~ msgstr "I_nformazioni Utente"

#~ msgid "_Private Chat"
#~ msgstr "Chat _Privata"

#~ msgid "Buddy _List"
#~ msgstr "_Lista Amici"

#~ msgid "_Chat Rooms"
#~ msgstr "Canali _Chat"

#~ msgid "_Interests"
#~ msgstr "_Interessi"

#~ msgid "_Modes"
#~ msgstr "_Schede"

#, python-format
#~ msgid "Hide %(tab)s"
#~ msgstr "Nascondi %(tab)s"

#~ msgid "Your IP address has not been retrieved from the server"
#~ msgstr "Non è stato possibile ottenere il tuo indirizzo IP dal server"

#, python-format
#~ msgid "Your IP address is <b>%(ip)s</b>"
#~ msgstr "Il tuo indirizzo IP è <b>%(ip)s</b>"

#~ msgid "Geo Block"
#~ msgstr "Blocco Geografico"

#~ msgid "Away Mode"
#~ msgstr "Modalita Assente"

#~ msgid "URL Catching"
#~ msgstr "Gestione URL"

#~ msgid "Check _Port Status"
#~ msgstr "Controlla Stato _Porta"

#~ msgid "_Clear All..."
#~ msgstr "Pulisci Tutto..."

#~ msgid "Free Sl_ot"
#~ msgstr "Sl_ot Libero"

#~ msgid "Search result filter help"
#~ msgstr "Aiuto filtro di ricerca"

#~ msgid ""
#~ "Geo Block controls from which countries users are allowed access to your "
#~ "shares."
#~ msgstr ""
#~ "Il Blocco Geografico controlla da quali paesi gli utenti possono accedere "
#~ "ai tuoi condivisi."

#~ msgid "Enable geographical blocker"
#~ msgstr "Abilita il blocco geografico"

#~ msgid ""
#~ "If the source country of an IP address cannot be determined, it will be "
#~ "blocked."
#~ msgstr ""
#~ "Se il paese di origine di un indirizzo IP non può essere determinato, "
#~ "verrà bloccato."

#~ msgid "Countries"
#~ msgstr "Paesi"

#~ msgid "last.fm"
#~ msgstr "last.fm"

#~ msgid "MPRIS (v2)"
#~ msgstr "MPRIS (v2)"

#~ msgid "ListenBrainz"
#~ msgstr "ListenBrainz"

#~ msgid "Enable URL catching"
#~ msgstr "Abilita cattura URL"

#~ msgid "Humanize slsk:// URLs"
#~ msgstr "Semplifica URL slsk://"

#~ msgid "Protocol Handlers"
#~ msgstr "Gestori Protocollo"

#~ msgid "Decimal separator:"
#~ msgstr "Separatore di decimali:"

#~ msgid "<None>"
#~ msgstr "<Nulla>"

#~ msgid "<space>"
#~ msgstr "<spazio>"

#~ msgid "Clicked usernames reveal the user context menu."
#~ msgstr "Il click sui nomi utente mostrano il menu contestuale utente."

#, python-format
#~ msgid "Active listening port is <b>%(port)s</b>"
#~ msgstr "La porta di ascolto attiva è <b>%(port)s</b>"

#~ msgid "Censor List"
#~ msgstr "Lista di Censura"

#~ msgid "Auto-Replace List"
#~ msgstr "Lista Auto-Sostituzione"

#~ msgid "Get Soulseek _Privileges..."
#~ msgstr "Ottieni _Privilegi Soulseek..."

#~ msgid ""
#~ "Wishlist items are auto-searched at regular intervals, useful for "
#~ "discovering uncommon files."
#~ msgstr ""
#~ "Gli elementi della lista dei desideri vengono ricercati automaticamente "
#~ "periodicamente, utile per scoprire file non comuni."

#~ msgid ""
#~ "Enable automatic replacement of chat words you've typed incorrectly or as "
#~ "an acronym"
#~ msgstr ""
#~ "Abilita sostituzione automatica delle parole in chat non correttamente "
#~ "scritte o come acronimo"

#~ msgid "Custom ban message:"
#~ msgstr "Messaggio di blocco personalizzato:"

#~ msgid "Use custom Geo Block message:"
#~ msgstr "Usa messaggio personalizzato per il Blocco Geografico:"

#~ msgid "Sent to users as the reason for being blocked."
#~ msgstr "Inviato agli utenti come motivo del blocco."

#~ msgid ""
#~ "Instances of $ will be replaced by the link, add empty handlers to use "
#~ "the system default web browser."
#~ msgstr ""
#~ "Le corrispondenze con $ saranno sostituite dal link, aggiungi "
#~ "associazioni vuote per usare il browser predefinito di sistema."

#~ msgid "Show _Buttons in Transfer Tabs"
#~ msgstr "Mostra Pulsanti nelle Schede Trasferimento"

#~ msgid ""
#~ "Nicotine+ will still work to some degree if your port is closed. However, "
#~ "do keep in mind that you won't be able to connect to users whose port is "
#~ "also closed."
#~ msgstr ""
#~ "Nicotine+ funzionerà ancora in una certa misura a porta chiusa. Tuttavia, "
#~ "tieni presente che non sarai in grado di connetterti agli utenti la cui "
#~ "porta è chiusa."

#~ msgid ""
#~ "Sharing files is crucial for the health of the Soulseek network. Many "
#~ "people will ban you if you download from them without sharing anything "
#~ "yourself."
#~ msgstr ""
#~ "La condivisione dei file è fondamentale per la salute della rete Soulseek "
#~ "e molte persone ti bloccheranno se scarichi da loro senza condividere "
#~ "nulla."

#~ msgid "Interface"
#~ msgstr "Interfaccia"

#~ msgid "Show Buttons in Transfer Tabs"
#~ msgstr "Mostra Pulsanti nelle Schede Trasferimento"

#~ msgid "Show _Flag Columns in User Lists"
#~ msgstr "Mostra Bandiere nelle Liste Utente"

#, python-format
#~ msgid ""
#~ "Unable to fully disable plugin %(module)s\n"
#~ "%(exc_trace)s"
#~ msgstr ""
#~ "Impossibile disabilitare completamente l'estensione %(module)s\n"
#~ "%(exc_trace)s"

#, python-format
#~ msgid "UPnP exception: %(error)s"
#~ msgstr "Eccezione UPnP: %(error)s"

#~ msgid "Failed to automate the creation of UPnP Port Mapping rule."
#~ msgstr ""
#~ "Creazione automatica della regola di Mappatura Porte tramite UPnP fallita."

#~ msgid "Show Flag Columns in User Lists"
#~ msgstr "Mostra Bandiera nelle Liste Utente"

#~ msgid "File _Properties"
#~ msgstr "_Proprietà File"

#~ msgid "File P_roperties"
#~ msgstr "P_roprietà File"

#~ msgid "Abor_t"
#~ msgstr "Annulla"
