# SPDX-FileCopyrightText: 2024-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2024-02-07 02:01+0000\n"
"Last-Translator: ButterflyOfFire <<EMAIL>>\n"
"Language-Team: Arabic <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/ar/>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 ? 4 : 5;\n"
"X-Generator: Weblate 5.4-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr ""

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr ""

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr ""

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
msgid "Browse the Soulseek network"
msgstr ""

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr ""

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
msgid "Search Files"
msgstr ""

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
#, fuzzy
msgid "Downloads"
msgstr "التنزيلاتShkarkimet"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
msgid "Browse Shares"
msgstr ""

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
msgid "Private Chat"
msgstr ""

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
#, fuzzy
msgid "Nicotine+ Team"
msgstr "فريق نيكوتين+Ekipi Nicotine +"

#: pynicotine/__init__.py:50
#, fuzzy, python-format
msgid "Website: %s"
msgstr "Faqja e internetit: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr ""

#: pynicotine/__init__.py:59
msgid "file"
msgstr "ملف"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr ""

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "مجلد"

#: pynicotine/__init__.py:64
msgid "alternative directory for user data and plugins"
msgstr "مجلد بديل لبيانات المستخدم والإضافات"

#: pynicotine/__init__.py:68
msgid "start the program without showing window"
msgstr ""

#: pynicotine/__init__.py:71
msgid "ip"
msgstr ""

#: pynicotine/__init__.py:72
msgid "bind sockets to the given IP (useful for VPN)"
msgstr ""

#: pynicotine/__init__.py:75
msgid "port"
msgstr "منفذ"

#: pynicotine/__init__.py:76
msgid "listen on the given port"
msgstr "استمع على المنفذ المحدد"

#: pynicotine/__init__.py:80
msgid "rescan shared files"
msgstr "إعادة فحص الملفات المشتركة"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr ""

#: pynicotine/__init__.py:88
msgid "display version and exit"
msgstr "عرض الإصدار والخروج"

#: pynicotine/__init__.py:121
#, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, python-format
msgid "%(user)s is away"
msgstr ""

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, python-format
msgid "%(user)s is online"
msgstr ""

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, python-format
msgid "%(user)s is offline"
msgstr ""

#: pynicotine/buddies.py:316
msgid "Buddy Status"
msgstr ""

#: pynicotine/chatrooms.py:383
#, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "لقد تمت دعوتك إلى غرفة خاصة: %(room)s"

#: pynicotine/chatrooms.py:478
#, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr ""

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr ""

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr ""

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr ""

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, fuzzy, python-format
msgid "Loading %(program)s %(version)s"
msgstr "جاري التحميل %(program)s %(version)s"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr ""

#: pynicotine/core.py:246
msgid "terminating"
msgstr "إنهاء"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "إغلاق البرنامج"

#: pynicotine/core.py:259
#, python-format
msgid "Quit %(program)s %(version)s!"
msgstr ""

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr ""

#: pynicotine/downloads.py:239
#, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""

#: pynicotine/downloads.py:254
#, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr ""

#: pynicotine/downloads.py:366
#, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr ""

#: pynicotine/downloads.py:370
msgid "File Downloaded"
msgstr ""

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr ""

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr ""

#: pynicotine/downloads.py:407
#, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr ""

#: pynicotine/downloads.py:411
msgid "Folder Downloaded"
msgstr ""

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr ""

#: pynicotine/downloads.py:443
#, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr ""

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
msgid "Download Folder Error"
msgstr ""

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr ""

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr ""

#: pynicotine/downloads.py:1150
#, python-format
msgid "Download I/O error: %s"
msgstr ""

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr ""

#: pynicotine/downloads.py:1202
#, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr ""

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr ""

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, python-format
msgid "Cannot find %s, please install it."
msgstr ""

#: pynicotine/gtkgui/__init__.py:162
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr ""

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr ""

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr ""

#: pynicotine/gtkgui/application.py:308
msgid "Soulseek _Privileges"
msgstr ""

#: pynicotine/gtkgui/application.py:314
msgid "_Preferences"
msgstr ""

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
msgid "_Quit"
msgstr ""

#: pynicotine/gtkgui/application.py:337
msgid "Browse _Public Shares"
msgstr ""

#: pynicotine/gtkgui/application.py:338
msgid "Browse _Buddy Shares"
msgstr ""

#: pynicotine/gtkgui/application.py:339
msgid "Browse _Trusted Shares"
msgstr ""

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
msgid "_Rescan Shares"
msgstr ""

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
msgid "Configure _Shares"
msgstr ""

#: pynicotine/gtkgui/application.py:371
msgid "_Keyboard Shortcuts"
msgstr ""

#: pynicotine/gtkgui/application.py:372
msgid "_Setup Assistant"
msgstr ""

#: pynicotine/gtkgui/application.py:373
msgid "_Transfer Statistics"
msgstr ""

#: pynicotine/gtkgui/application.py:378
msgid "Report a _Bug"
msgstr ""

#: pynicotine/gtkgui/application.py:379
msgid "Improve T_ranslations"
msgstr ""

#: pynicotine/gtkgui/application.py:383
msgid "_About Nicotine+"
msgstr ""

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr ""

#: pynicotine/gtkgui/application.py:395
msgid "_Shares"
msgstr ""

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
msgid "_Help"
msgstr ""

#: pynicotine/gtkgui/application.py:410
msgid "_Browse Shares"
msgstr ""

#: pynicotine/gtkgui/application.py:465
#, python-format
msgid "Unable to show notification: %s"
msgstr ""

#: pynicotine/gtkgui/application.py:526
msgid "You are still uploading files. Do you really want to exit?"
msgstr ""

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr ""

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr ""

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
msgid "_No"
msgstr ""

#: pynicotine/gtkgui/application.py:535
msgid "_Run in Background"
msgstr ""

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr ""

#: pynicotine/gtkgui/application.py:561
msgid "Shares Not Available"
msgstr ""

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
msgid "_Cancel"
msgstr ""

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr ""

#: pynicotine/gtkgui/application.py:567
msgid "_Force Rescan"
msgstr ""

#: pynicotine/gtkgui/application.py:742
msgid "Message Downloading Users"
msgstr ""

#: pynicotine/gtkgui/application.py:743
msgid "Send private message to all users who are downloading from you:"
msgstr ""

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
msgid "_Send Message"
msgstr ""

#: pynicotine/gtkgui/application.py:756
msgid "Message Buddies"
msgstr ""

#: pynicotine/gtkgui/application.py:757
msgid "Send private message to all online buddies:"
msgstr ""

#: pynicotine/gtkgui/application.py:786
msgid "Select a Saved Shares List File"
msgstr ""

#: pynicotine/gtkgui/application.py:877
msgid "Critical Error"
msgstr ""

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr ""

#: pynicotine/gtkgui/application.py:883
msgid "_Copy & Report Bug"
msgstr ""

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr ""

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr ""

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr ""

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr ""

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr ""

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted"
msgstr ""

#: pynicotine/gtkgui/buddies.py:108
msgid "Notify"
msgstr ""

#: pynicotine/gtkgui/buddies.py:114
msgid "Prioritized"
msgstr ""

#: pynicotine/gtkgui/buddies.py:120
msgid "Last Seen"
msgstr ""

#: pynicotine/gtkgui/buddies.py:126
msgid "Note"
msgstr ""

#: pynicotine/gtkgui/buddies.py:143
msgid "Add User _Note…"
msgstr ""

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr ""

#: pynicotine/gtkgui/buddies.py:364
msgid "Never seen"
msgstr ""

#: pynicotine/gtkgui/buddies.py:529
msgid "Add User Note"
msgstr ""

#: pynicotine/gtkgui/buddies.py:530
#, python-format
msgid "Add a note about user %s:"
msgstr ""

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
msgid "_Add"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:224
msgid "Create New Room?"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:225
#, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:226
msgid "Make room private"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:515
msgid "Search activity log…"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
msgid "Search chat log…"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:597
msgid "Sear_ch User's Files"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
msgid "Find…"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:608
msgid "Clear Activity View"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
msgid "_Leave Room"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
msgid "Copy Link"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:624
msgid "View Room Log"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:627
msgid "Delete Room Log…"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
msgid "Clear Message View"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:832
#, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:855
#, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
msgid "Delete Logged Messages?"
msgstr ""

#: pynicotine/gtkgui/chatrooms.py:1197
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""

#: pynicotine/gtkgui/dialogs/about.py:405
msgid "About"
msgstr ""

#: pynicotine/gtkgui/dialogs/about.py:415
msgid "Website"
msgstr ""

#: pynicotine/gtkgui/dialogs/about.py:457
#, python-format
msgid "Error checking latest version: %s"
msgstr ""

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr ""

#: pynicotine/gtkgui/dialogs/about.py:467
msgid "Up to date"
msgstr ""

#: pynicotine/gtkgui/dialogs/about.py:496
msgid "Checking latest version…"
msgstr ""

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
msgid "Setup Assistant"
msgstr ""

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
msgid "Virtual Folder"
msgstr ""

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
msgid "Folder"
msgstr ""

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
msgid "_Previous"
msgstr ""

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
msgid "_Finish"
msgstr ""

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
msgid "_Next"
msgstr ""

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
msgid "Add a Shared Folder"
msgstr ""

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
msgid "Edit Shared Folder"
msgstr ""

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr ""

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
msgid "_Edit"
msgstr ""

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
msgid "File Properties"
msgstr ""

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr ""

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr ""

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
msgid "_Apply"
msgstr ""

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
msgid "Add…"
msgstr ""

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
msgid "Edit…"
msgstr ""

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, python-format
msgid "%s Settings"
msgstr ""

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
msgid "Add Item"
msgstr ""

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
msgid "Edit Item"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "غير معروف"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
msgid "Check Port Status"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:199
msgid "Password Change Rejected"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:218
msgid "Enter a new password for your Soulseek account:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:220
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:223
msgid "Enter password to use when logging in:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
msgid "Change Password"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:230
msgid "_Change"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:274
msgid "No one"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:275
msgid "Everyone"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted buddies"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
msgid "Nothing"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
msgid "Open File"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
msgid "Open in File Manager"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
msgid "Pause"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
msgid "Resume"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
msgid "Browse Folder"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:317
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:334
msgid "Regex"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:468
msgid "Add Download Filter"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:469
msgid "Enter a new download filter:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
msgid "Enable regular expressions"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:498
msgid "Edit Download Filter"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:499
msgid "Modify the following download filter:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, python-format
msgid "%(num)d Failed! %(error)s "
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:578
msgid "Filters Successful"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
msgid "Retry"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:828
msgid "Round Robin"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:829
msgid "First In, First Out"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
msgid "Username"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
msgid "IP Address"
msgstr "عنوان الـ IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
msgid "Ignore User"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1058
msgid "Enter the name of the user you want to ignore:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
msgid "Ignore IP Address"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1099
msgid "Enter an IP address you want to ignore:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "* is a wildcard"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
msgid "Ban User"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1249
msgid "Enter the name of the user you want to ban:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
msgid "Ban IP Address"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "Enter an IP address you want to ban:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
msgid "Pattern"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1391
msgid "Replacement"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1524
msgid "Censor Pattern"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1554
msgid "Edit Censored Pattern"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1588
msgid "Add Replacement"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
msgid "Enter a text pattern and what to replace it with:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1620
msgid "Edit Replacement"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1736
msgid "System default"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1750
msgid "Show confirmation dialog"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1751
msgid "Run in the background"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1759
msgid "bold"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1760
msgid "italic"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1761
msgid "underline"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1762
msgid "normal"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1770
msgid "Separate Buddies tab"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1771
msgid "Sidebar in Chat Rooms tab"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1777
msgid "Top"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1778
msgid "Bottom"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1779
msgid "Left"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1780
msgid "Right"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1917
msgid "Tab Changed"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1918
msgid "Tab Highlight"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1919
msgid "Window"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1923
msgid "Online (Tray)"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1924
msgid "Away (Tray)"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1925
msgid "Offline (Tray)"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1926
msgid "Message (Tray)"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2503
msgid "Command"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2570
msgid "Add URL Handler"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2571
msgid "Enter the protocol and the command for the URL handler:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2599
msgid "Edit Command"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, python-format
msgid "Enter a new command for protocol %s:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2755
msgid "Username;APIKEY"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2759
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
msgid "Username: "
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2767
msgid "Command:"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
msgid "Title"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
msgid "Artist"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
msgid "Duration"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2784
msgid "Comment"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2788
msgid "Album"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2790
msgid "Track Number"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2792
msgid "Year"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2794
msgid "Filename (URI)"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2796
msgid "Program"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2859
msgid "Enabled"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2866
msgid "Plugin"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:2919
msgid "No Plugin Selected"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
msgid "Preferences"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
msgid "Network"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
msgid "User Interface"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3036
msgid "User Profile"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
msgid "Chats"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
msgid "Now Playing"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
msgid "Banned Users"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
msgid "Ignored Users"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
msgid "URL Handlers"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:3433
msgid "Pick a File Name for Config Backup"
msgstr ""

#: pynicotine/gtkgui/dialogs/statistics.py:75
msgid "Transfer Statistics"
msgstr ""

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, python-format
msgid "Total Since %(date)s"
msgstr ""

#: pynicotine/gtkgui/dialogs/statistics.py:123
msgid "Reset Transfer Statistics?"
msgstr ""

#: pynicotine/gtkgui/dialogs/statistics.py:124
msgid "Do you really want to reset transfer statistics?"
msgstr ""

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
msgid "Wishlist"
msgstr ""

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
msgid "Wish"
msgstr ""

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
msgid "_Search for Item"
msgstr ""

#: pynicotine/gtkgui/dialogs/wishlist.py:137
msgid "Edit Wish"
msgstr ""

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, python-format
msgid "Enter new value for wish '%s':"
msgstr ""

#: pynicotine/gtkgui/dialogs/wishlist.py:173
msgid "Clear Wishlist?"
msgstr ""

#: pynicotine/gtkgui/dialogs/wishlist.py:174
msgid "Do you really want to clear your wishlist?"
msgstr ""

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr ""

#: pynicotine/gtkgui/downloads.py:47
msgid "_Resume"
msgstr ""

#: pynicotine/gtkgui/downloads.py:48
msgid "P_ause"
msgstr ""

#: pynicotine/gtkgui/downloads.py:72
msgid "Finished / Filtered"
msgstr ""

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr ""

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
msgid "Paused"
msgstr ""

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
msgid "Filtered"
msgstr ""

#: pynicotine/gtkgui/downloads.py:77
msgid "Deleted"
msgstr ""

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
msgid "Queued…"
msgstr ""

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
msgid "Everything…"
msgstr ""

#: pynicotine/gtkgui/downloads.py:132
#, python-format
msgid "Downloads: %(speed)s"
msgstr ""

#: pynicotine/gtkgui/downloads.py:138
msgid "Clear Queued Downloads"
msgstr ""

#: pynicotine/gtkgui/downloads.py:139
msgid "Do you really want to clear all queued downloads?"
msgstr ""

#: pynicotine/gtkgui/downloads.py:151
msgid "Clear All Downloads"
msgstr ""

#: pynicotine/gtkgui/downloads.py:152
msgid "Do you really want to clear all downloads?"
msgstr ""

#: pynicotine/gtkgui/downloads.py:169
#, python-format
msgid "Download %(num)i files?"
msgstr ""

#: pynicotine/gtkgui/downloads.py:170
#, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
msgid "_Download Folder"
msgstr ""

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
msgid "Likes"
msgstr ""

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
msgid "Dislikes"
msgstr ""

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr ""

#: pynicotine/gtkgui/interests.py:111
msgid "Item"
msgstr ""

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
msgid "_Recommendations for Item"
msgstr ""

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
msgid "I _Like This"
msgstr ""

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
msgid "I _Dislike This"
msgstr ""

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
msgid "Recommendations"
msgstr ""

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
msgid "Similar Users"
msgstr ""

#: pynicotine/gtkgui/interests.py:427
#, python-format
msgid "Recommendations (%s)"
msgstr ""

#: pynicotine/gtkgui/interests.py:451
#, python-format
msgid "Similar Users (%s)"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:238
msgid "Search log…"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, python-format
msgid "Private Message from %(user)s"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
msgid "Wishlist Results Found"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
msgid "User Profiles"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
msgid "Chat Rooms"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
msgid "Chat"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1111
msgid "[Debug] Connections"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1112
msgid "[Debug] Messages"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1113
msgid "[Debug] Transfers"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1114
msgid "[Debug] Miscellaneous"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1119
msgid "_Find…"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
msgid "_Copy"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1122
msgid "Copy _All"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1127
msgid "View _Debug Logs"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1128
msgid "View _Transfer Logs"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1132
msgid "_Log Categories"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1134
msgid "Clear Log View"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1199
msgid "Preparing Shares"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr ""

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
msgid "Shared Folders"
msgstr ""

#: pynicotine/gtkgui/popovers/chathistory.py:77
msgid "Latest Message"
msgstr ""

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr ""

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr ""

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
msgid "Join Room"
msgstr ""

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
msgid "Leave Room"
msgstr ""

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
msgid "Disown Private Room"
msgstr ""

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
msgid "Cancel Room Membership"
msgstr ""

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
msgid "Close All Tabs…"
msgstr ""

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
msgid "_Close Tab"
msgstr ""

#: pynicotine/gtkgui/privatechat.py:379
msgid "View Chat Log"
msgstr ""

#: pynicotine/gtkgui/privatechat.py:382
msgid "Delete Chat Log…"
msgstr ""

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
msgid "User Actions"
msgstr ""

#: pynicotine/gtkgui/privatechat.py:477
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""

#: pynicotine/gtkgui/privatechat.py:528
msgid "* Messages sent while you were offline"
msgstr ""

#: pynicotine/gtkgui/search.py:90
msgid "_Global"
msgstr ""

#: pynicotine/gtkgui/search.py:91
msgid "_Buddies"
msgstr ""

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
msgid "_Rooms"
msgstr ""

#: pynicotine/gtkgui/search.py:93
msgid "_User"
msgstr ""

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
msgid "In Queue"
msgstr ""

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
msgid "File Type"
msgstr ""

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr ""

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr ""

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
msgid "Quality"
msgstr ""

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
msgid "Copy _File Path"
msgstr ""

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr ""

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
msgid "Copy Folder U_RL"
msgstr ""

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
msgid "_Download File(s)"
msgstr ""

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
msgid "Download File(s) _To…"
msgstr ""

#: pynicotine/gtkgui/search.py:615
msgid "Download _Folder(s)"
msgstr ""

#: pynicotine/gtkgui/search.py:616
msgid "Download F_older(s) To…"
msgstr ""

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
msgid "View User _Profile"
msgstr ""

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
msgid "_Browse Folder"
msgstr ""

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
msgid "F_ile Properties"
msgstr ""

#: pynicotine/gtkgui/search.py:629
msgid "Copy Search Term"
msgstr ""

#: pynicotine/gtkgui/search.py:631
msgid "Clear All Results"
msgstr ""

#: pynicotine/gtkgui/search.py:718
msgid "Clear Filters"
msgstr ""

#: pynicotine/gtkgui/search.py:721
msgid "Restore Filters"
msgstr ""

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, python-format
msgid "[PRIVATE]  %s"
msgstr ""

#: pynicotine/gtkgui/search.py:1273
#, python-format
msgid "_Result Filters [%d]"
msgstr ""

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
msgid "_Result Filters"
msgstr ""

#: pynicotine/gtkgui/search.py:1277
#, python-format
msgid "%d active filter(s)"
msgstr ""

#: pynicotine/gtkgui/search.py:1329
msgid "Add Wi_sh"
msgstr ""

#: pynicotine/gtkgui/search.py:1332
msgid "Remove Wi_sh"
msgstr ""

#: pynicotine/gtkgui/search.py:1349
msgid "Select User's Results"
msgstr ""

#: pynicotine/gtkgui/search.py:1472
#, python-format
msgid "Total: %s"
msgstr ""

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
msgid "Results"
msgstr ""

#: pynicotine/gtkgui/search.py:1590
msgid "Select Destination Folder for File(s)"
msgstr ""

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
msgid "Select Destination Folder"
msgstr ""

#: pynicotine/gtkgui/transfers.py:61
msgid "Queued"
msgstr ""

#: pynicotine/gtkgui/transfers.py:62
msgid "Queued (prioritized)"
msgstr ""

#: pynicotine/gtkgui/transfers.py:63
msgid "Queued (privileged)"
msgstr ""

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr ""

#: pynicotine/gtkgui/transfers.py:65
msgid "Transferring"
msgstr ""

#: pynicotine/gtkgui/transfers.py:66
msgid "Connection closed"
msgstr ""

#: pynicotine/gtkgui/transfers.py:67
msgid "Connection timeout"
msgstr ""

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr ""

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
msgid "Cancelled"
msgstr ""

#: pynicotine/gtkgui/transfers.py:73
msgid "Download folder error"
msgstr ""

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr ""

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr ""

#: pynicotine/gtkgui/transfers.py:76
msgid "File not shared"
msgstr ""

#: pynicotine/gtkgui/transfers.py:77
msgid "Pending shutdown"
msgstr ""

#: pynicotine/gtkgui/transfers.py:78
msgid "File read error"
msgstr ""

#: pynicotine/gtkgui/transfers.py:182
msgid "Queue"
msgstr ""

#: pynicotine/gtkgui/transfers.py:188
msgid "Percent"
msgstr ""

#: pynicotine/gtkgui/transfers.py:208
msgid "Time Elapsed"
msgstr ""

#: pynicotine/gtkgui/transfers.py:215
msgid "Time Left"
msgstr ""

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
msgid "_Open File"
msgstr ""

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
msgid "Open in File _Manager"
msgstr ""

#: pynicotine/gtkgui/transfers.py:286
msgid "_Search"
msgstr ""

#: pynicotine/gtkgui/transfers.py:289
msgid "Clear All"
msgstr ""

#: pynicotine/gtkgui/transfers.py:953
msgid "Select User's Transfers"
msgstr ""

#: pynicotine/gtkgui/uploads.py:50
msgid "_Abort"
msgstr ""

#: pynicotine/gtkgui/uploads.py:74
msgid "Finished / Cancelled / Failed"
msgstr ""

#: pynicotine/gtkgui/uploads.py:75
msgid "Finished / Cancelled"
msgstr ""

#: pynicotine/gtkgui/uploads.py:79
msgid "Failed"
msgstr ""

#: pynicotine/gtkgui/uploads.py:80
msgid "User Logged Off"
msgstr ""

#: pynicotine/gtkgui/uploads.py:142
#, python-format
msgid "Uploads: %(speed)s"
msgstr ""

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr ""

#: pynicotine/gtkgui/uploads.py:164
msgid "Clear Queued Uploads"
msgstr ""

#: pynicotine/gtkgui/uploads.py:165
msgid "Do you really want to clear all queued uploads?"
msgstr ""

#: pynicotine/gtkgui/uploads.py:177
msgid "Clear All Uploads"
msgstr ""

#: pynicotine/gtkgui/uploads.py:178
msgid "Do you really want to clear all uploads?"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:282
msgid "_Save Shares List to Disk"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:292
msgid "Upload Folder & Subfolders…"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
msgid "Copy _Folder Path"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:309
msgid "_Download Folder & Subfolders"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:310
msgid "Download Folder & Subfolders _To…"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:334
msgid "File Name"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:373
msgid "Up_load File(s)…"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:374
msgid "Upload Folder…"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:396
msgid "Download Folder _To…"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:600
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:615
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:953
msgid "Select Destination for Downloading Multiple Folders"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:997
msgid "Upload Folder (with Subfolders) To User"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:999
msgid "Upload Folder To User"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
msgid "Enter the name of the user you want to upload to:"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
msgid "_Upload"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:1139
msgid "Select Destination Folder for Files"
msgstr ""

#: pynicotine/gtkgui/userbrowse.py:1161
msgid "Upload File(s) To User"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:376
msgid "Copy Picture"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:377
msgid "Save Picture"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:468
#, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:505
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""

#: pynicotine/gtkgui/userinfo.py:577
msgid "Remove _Buddy"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:577
msgid "Add _Buddy"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:581
msgid "Unban User"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:585
msgid "Unignore User"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:613
msgid "Yes"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:773
msgid "Please enter number of days."
msgstr ""

#: pynicotine/gtkgui/userinfo.py:787
#, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:788
#, python-format
msgid "%(days)s days left"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:795
msgid "Gift Privileges"
msgstr ""

#: pynicotine/gtkgui/userinfo.py:797
msgid "_Give Privileges"
msgstr ""

#: pynicotine/gtkgui/widgets/dialogs.py:309
msgid "Close"
msgstr ""

#: pynicotine/gtkgui/widgets/dialogs.py:488
msgid "_Yes"
msgstr ""

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
msgid "_OK"
msgstr ""

#: pynicotine/gtkgui/widgets/filechooser.py:39
msgid "Select a File"
msgstr ""

#: pynicotine/gtkgui/widgets/filechooser.py:174
msgid "Select a Folder"
msgstr ""

#: pynicotine/gtkgui/widgets/filechooser.py:179
msgid "_Select"
msgstr ""

#: pynicotine/gtkgui/widgets/filechooser.py:196
msgid "Select an Image"
msgstr ""

#: pynicotine/gtkgui/widgets/filechooser.py:203
msgid "All images"
msgstr ""

#: pynicotine/gtkgui/widgets/filechooser.py:241
msgid "Save as…"
msgstr ""

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
msgid "(None)"
msgstr ""

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
msgid "Close Tab"
msgstr ""

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
msgid "Close All Tabs?"
msgstr ""

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
msgid "Do you really want to close all tabs?"
msgstr ""

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, python-format
msgid "%i Unread Tab(s)"
msgstr ""

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
msgid "All Tabs"
msgstr ""

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
msgid "Re_open Closed Tab"
msgstr ""

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, python-format
msgid "%s File(s) Selected"
msgstr ""

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
msgid "_Browse Files"
msgstr ""

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
msgid "_Add Buddy"
msgstr ""

#: pynicotine/gtkgui/widgets/popupmenu.py:453
msgid "Show IP A_ddress"
msgstr ""

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
msgid "Private Rooms"
msgstr ""

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, python-format
msgid "Remove from Private Room %s"
msgstr ""

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, python-format
msgid "Add to Private Room %s"
msgstr ""

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, python-format
msgid "Remove as Operator of %s"
msgstr ""

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, python-format
msgid "Add as Operator of %s"
msgstr ""

#: pynicotine/gtkgui/widgets/textentry.py:37
msgid "Send message…"
msgstr ""

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
msgid "Find Previous Match"
msgstr ""

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
msgid "Find Next Match"
msgstr ""

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:243
msgid "Executable"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:244
msgid "Audio"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:245
msgid "Image"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:246
msgid "Archive"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
msgid "Miscellaneous"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:248
msgid "Video"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:249
msgid "Document"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:357
#, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr ""

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Hide Nicotine+"
msgstr ""

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Show Nicotine+"
msgstr ""

#: pynicotine/gtkgui/widgets/treeview.py:778
#, python-format
msgid "Column #%i"
msgstr ""

#: pynicotine/gtkgui/widgets/treeview.py:957
msgid "Ungrouped"
msgstr ""

#: pynicotine/gtkgui/widgets/treeview.py:960
msgid "Group by Folder"
msgstr ""

#: pynicotine/gtkgui/widgets/treeview.py:963
msgid "Group by User"
msgstr ""

#: pynicotine/headless/application.py:77
#, python-format
msgid "Do you really want to exit? %s"
msgstr ""

#: pynicotine/headless/application.py:81
#, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr ""

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr ""

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
msgid "Password: "
msgstr ""

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr ""

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr ""

#: pynicotine/logfacility.py:181
#, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr ""

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr ""

#: pynicotine/networkfilter.py:40
msgid "Andorra"
msgstr ""

#: pynicotine/networkfilter.py:41
msgid "United Arab Emirates"
msgstr ""

#: pynicotine/networkfilter.py:42
msgid "Afghanistan"
msgstr ""

#: pynicotine/networkfilter.py:43
msgid "Antigua & Barbuda"
msgstr ""

#: pynicotine/networkfilter.py:44
msgid "Anguilla"
msgstr ""

#: pynicotine/networkfilter.py:45
msgid "Albania"
msgstr ""

#: pynicotine/networkfilter.py:46
msgid "Armenia"
msgstr ""

#: pynicotine/networkfilter.py:47
msgid "Angola"
msgstr ""

#: pynicotine/networkfilter.py:48
msgid "Antarctica"
msgstr ""

#: pynicotine/networkfilter.py:49
msgid "Argentina"
msgstr ""

#: pynicotine/networkfilter.py:50
msgid "American Samoa"
msgstr ""

#: pynicotine/networkfilter.py:51
msgid "Austria"
msgstr ""

#: pynicotine/networkfilter.py:52
msgid "Australia"
msgstr ""

#: pynicotine/networkfilter.py:53
msgid "Aruba"
msgstr ""

#: pynicotine/networkfilter.py:54
msgid "Åland Islands"
msgstr ""

#: pynicotine/networkfilter.py:55
msgid "Azerbaijan"
msgstr ""

#: pynicotine/networkfilter.py:56
msgid "Bosnia & Herzegovina"
msgstr ""

#: pynicotine/networkfilter.py:57
msgid "Barbados"
msgstr ""

#: pynicotine/networkfilter.py:58
msgid "Bangladesh"
msgstr ""

#: pynicotine/networkfilter.py:59
msgid "Belgium"
msgstr ""

#: pynicotine/networkfilter.py:60
msgid "Burkina Faso"
msgstr ""

#: pynicotine/networkfilter.py:61
msgid "Bulgaria"
msgstr ""

#: pynicotine/networkfilter.py:62
msgid "Bahrain"
msgstr ""

#: pynicotine/networkfilter.py:63
msgid "Burundi"
msgstr ""

#: pynicotine/networkfilter.py:64
msgid "Benin"
msgstr ""

#: pynicotine/networkfilter.py:65
msgid "Saint Barthelemy"
msgstr ""

#: pynicotine/networkfilter.py:66
msgid "Bermuda"
msgstr ""

#: pynicotine/networkfilter.py:67
msgid "Brunei Darussalam"
msgstr ""

#: pynicotine/networkfilter.py:68
msgid "Bolivia"
msgstr ""

#: pynicotine/networkfilter.py:69
msgid "Bonaire, Sint Eustatius and Saba"
msgstr ""

#: pynicotine/networkfilter.py:70
msgid "Brazil"
msgstr ""

#: pynicotine/networkfilter.py:71
msgid "Bahamas"
msgstr ""

#: pynicotine/networkfilter.py:72
msgid "Bhutan"
msgstr ""

#: pynicotine/networkfilter.py:73
msgid "Bouvet Island"
msgstr ""

#: pynicotine/networkfilter.py:74
msgid "Botswana"
msgstr ""

#: pynicotine/networkfilter.py:75
msgid "Belarus"
msgstr ""

#: pynicotine/networkfilter.py:76
msgid "Belize"
msgstr ""

#: pynicotine/networkfilter.py:77
msgid "Canada"
msgstr ""

#: pynicotine/networkfilter.py:78
msgid "Cocos (Keeling) Islands"
msgstr ""

#: pynicotine/networkfilter.py:79
msgid "Democratic Republic of Congo"
msgstr ""

#: pynicotine/networkfilter.py:80
msgid "Central African Republic"
msgstr ""

#: pynicotine/networkfilter.py:81
msgid "Congo"
msgstr ""

#: pynicotine/networkfilter.py:82
msgid "Switzerland"
msgstr ""

#: pynicotine/networkfilter.py:83
msgid "Ivory Coast"
msgstr ""

#: pynicotine/networkfilter.py:84
msgid "Cook Islands"
msgstr ""

#: pynicotine/networkfilter.py:85
msgid "Chile"
msgstr ""

#: pynicotine/networkfilter.py:86
msgid "Cameroon"
msgstr ""

#: pynicotine/networkfilter.py:87
msgid "China"
msgstr ""

#: pynicotine/networkfilter.py:88
msgid "Colombia"
msgstr ""

#: pynicotine/networkfilter.py:89
msgid "Costa Rica"
msgstr ""

#: pynicotine/networkfilter.py:90
msgid "Cuba"
msgstr ""

#: pynicotine/networkfilter.py:91
msgid "Cabo Verde"
msgstr ""

#: pynicotine/networkfilter.py:92
msgid "Curaçao"
msgstr ""

#: pynicotine/networkfilter.py:93
msgid "Christmas Island"
msgstr ""

#: pynicotine/networkfilter.py:94
msgid "Cyprus"
msgstr ""

#: pynicotine/networkfilter.py:95
msgid "Czechia"
msgstr ""

#: pynicotine/networkfilter.py:96
msgid "Germany"
msgstr ""

#: pynicotine/networkfilter.py:97
msgid "Djibouti"
msgstr ""

#: pynicotine/networkfilter.py:98
msgid "Denmark"
msgstr ""

#: pynicotine/networkfilter.py:99
msgid "Dominica"
msgstr ""

#: pynicotine/networkfilter.py:100
msgid "Dominican Republic"
msgstr ""

#: pynicotine/networkfilter.py:101
msgid "Algeria"
msgstr ""

#: pynicotine/networkfilter.py:102
msgid "Ecuador"
msgstr ""

#: pynicotine/networkfilter.py:103
msgid "Estonia"
msgstr ""

#: pynicotine/networkfilter.py:104
msgid "Egypt"
msgstr ""

#: pynicotine/networkfilter.py:105
msgid "Western Sahara"
msgstr ""

#: pynicotine/networkfilter.py:106
msgid "Eritrea"
msgstr ""

#: pynicotine/networkfilter.py:107
msgid "Spain"
msgstr ""

#: pynicotine/networkfilter.py:108
msgid "Ethiopia"
msgstr ""

#: pynicotine/networkfilter.py:109
msgid "Europe"
msgstr ""

#: pynicotine/networkfilter.py:110
msgid "Finland"
msgstr ""

#: pynicotine/networkfilter.py:111
msgid "Fiji"
msgstr ""

#: pynicotine/networkfilter.py:112
msgid "Falkland Islands (Malvinas)"
msgstr ""

#: pynicotine/networkfilter.py:113
msgid "Micronesia"
msgstr ""

#: pynicotine/networkfilter.py:114
msgid "Faroe Islands"
msgstr ""

#: pynicotine/networkfilter.py:115
msgid "France"
msgstr ""

#: pynicotine/networkfilter.py:116
msgid "Gabon"
msgstr ""

#: pynicotine/networkfilter.py:117
msgid "Great Britain"
msgstr ""

#: pynicotine/networkfilter.py:118
msgid "Grenada"
msgstr ""

#: pynicotine/networkfilter.py:119
msgid "Georgia"
msgstr ""

#: pynicotine/networkfilter.py:120
msgid "French Guiana"
msgstr ""

#: pynicotine/networkfilter.py:121
msgid "Guernsey"
msgstr ""

#: pynicotine/networkfilter.py:122
msgid "Ghana"
msgstr ""

#: pynicotine/networkfilter.py:123
msgid "Gibraltar"
msgstr ""

#: pynicotine/networkfilter.py:124
msgid "Greenland"
msgstr ""

#: pynicotine/networkfilter.py:125
msgid "Gambia"
msgstr ""

#: pynicotine/networkfilter.py:126
msgid "Guinea"
msgstr ""

#: pynicotine/networkfilter.py:127
msgid "Guadeloupe"
msgstr ""

#: pynicotine/networkfilter.py:128
msgid "Equatorial Guinea"
msgstr ""

#: pynicotine/networkfilter.py:129
msgid "Greece"
msgstr ""

#: pynicotine/networkfilter.py:130
msgid "South Georgia & South Sandwich Islands"
msgstr ""

#: pynicotine/networkfilter.py:131
msgid "Guatemala"
msgstr ""

#: pynicotine/networkfilter.py:132
msgid "Guam"
msgstr ""

#: pynicotine/networkfilter.py:133
msgid "Guinea-Bissau"
msgstr ""

#: pynicotine/networkfilter.py:134
msgid "Guyana"
msgstr ""

#: pynicotine/networkfilter.py:135
msgid "Hong Kong"
msgstr ""

#: pynicotine/networkfilter.py:136
msgid "Heard & McDonald Islands"
msgstr ""

#: pynicotine/networkfilter.py:137
msgid "Honduras"
msgstr ""

#: pynicotine/networkfilter.py:138
msgid "Croatia"
msgstr ""

#: pynicotine/networkfilter.py:139
msgid "Haiti"
msgstr ""

#: pynicotine/networkfilter.py:140
msgid "Hungary"
msgstr ""

#: pynicotine/networkfilter.py:141
msgid "Indonesia"
msgstr ""

#: pynicotine/networkfilter.py:142
msgid "Ireland"
msgstr ""

#: pynicotine/networkfilter.py:143
msgid "Israel"
msgstr ""

#: pynicotine/networkfilter.py:144
msgid "Isle of Man"
msgstr ""

#: pynicotine/networkfilter.py:145
msgid "India"
msgstr ""

#: pynicotine/networkfilter.py:146
msgid "British Indian Ocean Territory"
msgstr ""

#: pynicotine/networkfilter.py:147
msgid "Iraq"
msgstr ""

#: pynicotine/networkfilter.py:148
msgid "Iran"
msgstr ""

#: pynicotine/networkfilter.py:149
msgid "Iceland"
msgstr ""

#: pynicotine/networkfilter.py:150
msgid "Italy"
msgstr ""

#: pynicotine/networkfilter.py:151
msgid "Jersey"
msgstr ""

#: pynicotine/networkfilter.py:152
msgid "Jamaica"
msgstr ""

#: pynicotine/networkfilter.py:153
msgid "Jordan"
msgstr ""

#: pynicotine/networkfilter.py:154
msgid "Japan"
msgstr ""

#: pynicotine/networkfilter.py:155
msgid "Kenya"
msgstr ""

#: pynicotine/networkfilter.py:156
msgid "Kyrgyzstan"
msgstr ""

#: pynicotine/networkfilter.py:157
msgid "Cambodia"
msgstr ""

#: pynicotine/networkfilter.py:158
msgid "Kiribati"
msgstr ""

#: pynicotine/networkfilter.py:159
msgid "Comoros"
msgstr ""

#: pynicotine/networkfilter.py:160
msgid "Saint Kitts & Nevis"
msgstr ""

#: pynicotine/networkfilter.py:161
msgid "North Korea"
msgstr ""

#: pynicotine/networkfilter.py:162
msgid "South Korea"
msgstr ""

#: pynicotine/networkfilter.py:163
msgid "Kuwait"
msgstr ""

#: pynicotine/networkfilter.py:164
msgid "Cayman Islands"
msgstr ""

#: pynicotine/networkfilter.py:165
msgid "Kazakhstan"
msgstr ""

#: pynicotine/networkfilter.py:166
msgid "Laos"
msgstr ""

#: pynicotine/networkfilter.py:167
msgid "Lebanon"
msgstr ""

#: pynicotine/networkfilter.py:168
msgid "Saint Lucia"
msgstr ""

#: pynicotine/networkfilter.py:169
msgid "Liechtenstein"
msgstr ""

#: pynicotine/networkfilter.py:170
msgid "Sri Lanka"
msgstr ""

#: pynicotine/networkfilter.py:171
msgid "Liberia"
msgstr ""

#: pynicotine/networkfilter.py:172
msgid "Lesotho"
msgstr ""

#: pynicotine/networkfilter.py:173
msgid "Lithuania"
msgstr ""

#: pynicotine/networkfilter.py:174
msgid "Luxembourg"
msgstr ""

#: pynicotine/networkfilter.py:175
msgid "Latvia"
msgstr ""

#: pynicotine/networkfilter.py:176
msgid "Libya"
msgstr ""

#: pynicotine/networkfilter.py:177
msgid "Morocco"
msgstr ""

#: pynicotine/networkfilter.py:178
msgid "Monaco"
msgstr ""

#: pynicotine/networkfilter.py:179
msgid "Moldova"
msgstr ""

#: pynicotine/networkfilter.py:180
msgid "Montenegro"
msgstr ""

#: pynicotine/networkfilter.py:181
msgid "Saint Martin"
msgstr ""

#: pynicotine/networkfilter.py:182
msgid "Madagascar"
msgstr ""

#: pynicotine/networkfilter.py:183
msgid "Marshall Islands"
msgstr ""

#: pynicotine/networkfilter.py:184
msgid "North Macedonia"
msgstr ""

#: pynicotine/networkfilter.py:185
msgid "Mali"
msgstr ""

#: pynicotine/networkfilter.py:186
msgid "Myanmar"
msgstr ""

#: pynicotine/networkfilter.py:187
msgid "Mongolia"
msgstr ""

#: pynicotine/networkfilter.py:188
msgid "Macau"
msgstr ""

#: pynicotine/networkfilter.py:189
msgid "Northern Mariana Islands"
msgstr ""

#: pynicotine/networkfilter.py:190
msgid "Martinique"
msgstr ""

#: pynicotine/networkfilter.py:191
msgid "Mauritania"
msgstr ""

#: pynicotine/networkfilter.py:192
msgid "Montserrat"
msgstr ""

#: pynicotine/networkfilter.py:193
msgid "Malta"
msgstr ""

#: pynicotine/networkfilter.py:194
msgid "Mauritius"
msgstr ""

#: pynicotine/networkfilter.py:195
msgid "Maldives"
msgstr ""

#: pynicotine/networkfilter.py:196
msgid "Malawi"
msgstr ""

#: pynicotine/networkfilter.py:197
msgid "Mexico"
msgstr ""

#: pynicotine/networkfilter.py:198
msgid "Malaysia"
msgstr ""

#: pynicotine/networkfilter.py:199
msgid "Mozambique"
msgstr ""

#: pynicotine/networkfilter.py:200
msgid "Namibia"
msgstr ""

#: pynicotine/networkfilter.py:201
msgid "New Caledonia"
msgstr ""

#: pynicotine/networkfilter.py:202
msgid "Niger"
msgstr ""

#: pynicotine/networkfilter.py:203
msgid "Norfolk Island"
msgstr ""

#: pynicotine/networkfilter.py:204
msgid "Nigeria"
msgstr ""

#: pynicotine/networkfilter.py:205
msgid "Nicaragua"
msgstr ""

#: pynicotine/networkfilter.py:206
msgid "Netherlands"
msgstr ""

#: pynicotine/networkfilter.py:207
msgid "Norway"
msgstr ""

#: pynicotine/networkfilter.py:208
msgid "Nepal"
msgstr ""

#: pynicotine/networkfilter.py:209
msgid "Nauru"
msgstr ""

#: pynicotine/networkfilter.py:210
msgid "Niue"
msgstr ""

#: pynicotine/networkfilter.py:211
msgid "New Zealand"
msgstr ""

#: pynicotine/networkfilter.py:212
msgid "Oman"
msgstr ""

#: pynicotine/networkfilter.py:213
msgid "Panama"
msgstr ""

#: pynicotine/networkfilter.py:214
msgid "Peru"
msgstr ""

#: pynicotine/networkfilter.py:215
msgid "French Polynesia"
msgstr ""

#: pynicotine/networkfilter.py:216
msgid "Papua New Guinea"
msgstr ""

#: pynicotine/networkfilter.py:217
msgid "Philippines"
msgstr ""

#: pynicotine/networkfilter.py:218
msgid "Pakistan"
msgstr ""

#: pynicotine/networkfilter.py:219
msgid "Poland"
msgstr ""

#: pynicotine/networkfilter.py:220
msgid "Saint Pierre & Miquelon"
msgstr ""

#: pynicotine/networkfilter.py:221
msgid "Pitcairn"
msgstr ""

#: pynicotine/networkfilter.py:222
msgid "Puerto Rico"
msgstr ""

#: pynicotine/networkfilter.py:223
msgid "State of Palestine"
msgstr ""

#: pynicotine/networkfilter.py:224
msgid "Portugal"
msgstr ""

#: pynicotine/networkfilter.py:225
msgid "Palau"
msgstr ""

#: pynicotine/networkfilter.py:226
msgid "Paraguay"
msgstr ""

#: pynicotine/networkfilter.py:227
msgid "Qatar"
msgstr ""

#: pynicotine/networkfilter.py:228
msgid "Réunion"
msgstr ""

#: pynicotine/networkfilter.py:229
msgid "Romania"
msgstr ""

#: pynicotine/networkfilter.py:230
msgid "Serbia"
msgstr ""

#: pynicotine/networkfilter.py:231
msgid "Russia"
msgstr ""

#: pynicotine/networkfilter.py:232
msgid "Rwanda"
msgstr ""

#: pynicotine/networkfilter.py:233
msgid "Saudi Arabia"
msgstr ""

#: pynicotine/networkfilter.py:234
msgid "Solomon Islands"
msgstr ""

#: pynicotine/networkfilter.py:235
msgid "Seychelles"
msgstr ""

#: pynicotine/networkfilter.py:236
msgid "Sudan"
msgstr ""

#: pynicotine/networkfilter.py:237
msgid "Sweden"
msgstr ""

#: pynicotine/networkfilter.py:238
msgid "Singapore"
msgstr ""

#: pynicotine/networkfilter.py:239
msgid "Saint Helena"
msgstr ""

#: pynicotine/networkfilter.py:240
msgid "Slovenia"
msgstr ""

#: pynicotine/networkfilter.py:241
msgid "Svalbard & Jan Mayen Islands"
msgstr ""

#: pynicotine/networkfilter.py:242
msgid "Slovak Republic"
msgstr ""

#: pynicotine/networkfilter.py:243
msgid "Sierra Leone"
msgstr ""

#: pynicotine/networkfilter.py:244
msgid "San Marino"
msgstr ""

#: pynicotine/networkfilter.py:245
msgid "Senegal"
msgstr ""

#: pynicotine/networkfilter.py:246
msgid "Somalia"
msgstr ""

#: pynicotine/networkfilter.py:247
msgid "Suriname"
msgstr ""

#: pynicotine/networkfilter.py:248
msgid "South Sudan"
msgstr ""

#: pynicotine/networkfilter.py:249
msgid "Sao Tome & Principe"
msgstr ""

#: pynicotine/networkfilter.py:250
msgid "El Salvador"
msgstr ""

#: pynicotine/networkfilter.py:251
msgid "Sint Maarten"
msgstr ""

#: pynicotine/networkfilter.py:252
msgid "Syria"
msgstr ""

#: pynicotine/networkfilter.py:253
msgid "Eswatini"
msgstr ""

#: pynicotine/networkfilter.py:254
msgid "Turks & Caicos Islands"
msgstr ""

#: pynicotine/networkfilter.py:255
msgid "Chad"
msgstr ""

#: pynicotine/networkfilter.py:256
msgid "French Southern Territories"
msgstr ""

#: pynicotine/networkfilter.py:257
msgid "Togo"
msgstr ""

#: pynicotine/networkfilter.py:258
msgid "Thailand"
msgstr ""

#: pynicotine/networkfilter.py:259
msgid "Tajikistan"
msgstr ""

#: pynicotine/networkfilter.py:260
msgid "Tokelau"
msgstr ""

#: pynicotine/networkfilter.py:261
msgid "Timor-Leste"
msgstr ""

#: pynicotine/networkfilter.py:262
msgid "Turkmenistan"
msgstr ""

#: pynicotine/networkfilter.py:263
msgid "Tunisia"
msgstr ""

#: pynicotine/networkfilter.py:264
msgid "Tonga"
msgstr ""

#: pynicotine/networkfilter.py:265
msgid "Türkiye"
msgstr ""

#: pynicotine/networkfilter.py:266
msgid "Trinidad & Tobago"
msgstr ""

#: pynicotine/networkfilter.py:267
msgid "Tuvalu"
msgstr ""

#: pynicotine/networkfilter.py:268
msgid "Taiwan"
msgstr ""

#: pynicotine/networkfilter.py:269
msgid "Tanzania"
msgstr ""

#: pynicotine/networkfilter.py:270
msgid "Ukraine"
msgstr ""

#: pynicotine/networkfilter.py:271
msgid "Uganda"
msgstr ""

#: pynicotine/networkfilter.py:272
msgid "U.S. Minor Outlying Islands"
msgstr ""

#: pynicotine/networkfilter.py:273
msgid "United States"
msgstr ""

#: pynicotine/networkfilter.py:274
msgid "Uruguay"
msgstr ""

#: pynicotine/networkfilter.py:275
msgid "Uzbekistan"
msgstr ""

#: pynicotine/networkfilter.py:276
msgid "Holy See (Vatican City State)"
msgstr ""

#: pynicotine/networkfilter.py:277
msgid "Saint Vincent & The Grenadines"
msgstr ""

#: pynicotine/networkfilter.py:278
msgid "Venezuela"
msgstr ""

#: pynicotine/networkfilter.py:279
msgid "British Virgin Islands"
msgstr ""

#: pynicotine/networkfilter.py:280
msgid "U.S. Virgin Islands"
msgstr ""

#: pynicotine/networkfilter.py:281
msgid "Viet Nam"
msgstr ""

#: pynicotine/networkfilter.py:282
msgid "Vanuatu"
msgstr ""

#: pynicotine/networkfilter.py:283
msgid "Wallis & Futuna"
msgstr ""

#: pynicotine/networkfilter.py:284
msgid "Samoa"
msgstr ""

#: pynicotine/networkfilter.py:285
msgid "Yemen"
msgstr ""

#: pynicotine/networkfilter.py:286
msgid "Mayotte"
msgstr ""

#: pynicotine/networkfilter.py:287
msgid "South Africa"
msgstr ""

#: pynicotine/networkfilter.py:288
msgid "Zambia"
msgstr ""

#: pynicotine/networkfilter.py:289
msgid "Zimbabwe"
msgstr ""

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, python-format
msgid "Text-to-speech for message failed: %s"
msgstr ""

#: pynicotine/nowplaying.py:130
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr ""

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
msgid "Now Playing Error"
msgstr ""

#: pynicotine/nowplaying.py:140
#, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr ""

#: pynicotine/nowplaying.py:162
#, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr ""

#: pynicotine/nowplaying.py:196
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr ""

#: pynicotine/nowplaying.py:201
#, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr ""

#: pynicotine/nowplaying.py:204
#, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr ""

#: pynicotine/nowplaying.py:219
#, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr ""

#: pynicotine/nowplaying.py:266
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr ""

#: pynicotine/nowplaying.py:275
#, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr ""

#: pynicotine/nowplaying.py:283
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr ""

#: pynicotine/nowplaying.py:297
#, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:28
msgid "Network Filters"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:44
msgid "List available commands"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:49
msgid "Connect to the server"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:53
msgid "Disconnect from the server"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:58
msgid "Toggle away status"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:62
msgid "Manage plugins"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:74
msgid "Clear chat window"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:80
msgid "Say something in the third-person"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:87
msgid "Announce the song currently playing"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:94
msgid "Join chat room"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:102
msgid "Leave chat room"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:110
msgid "Say message in specified chat room"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:117
msgid "Open private chat"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:125
msgid "Close private chat"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:133
msgid "Request user's client version"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:142
msgid "Send private message to user"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:150
msgid "Add user to buddy list"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:158
msgid "Remove buddy from buddy list"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:166
msgid "Browse files of user"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:175
msgid "Show user profile information"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:183
msgid "Show IP address or username"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:190
msgid "Block connections from user or IP address"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:197
msgid "Remove user or IP address from ban lists"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:204
msgid "Silence messages from user or IP address"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:212
msgid "Remove user or IP address from ignore lists"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:220
msgid "Add share"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:226
msgid "Remove share"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:233
msgid "List shares"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:246
msgid "Start global file search"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:254
msgid "Search files in joined rooms"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:262
msgid "Search files of all buddies"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:270
msgid "Search a user's shared files"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:296
#, python-format
msgid "Listing %(num)i available commands:"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:298
#, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:311
#, python-format
msgid "Type %(command)s to list similar commands"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:314
#, python-format
msgid "Type %(command)s to list available commands"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, python-format
msgid "Not joined in room %s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:408
#, python-format
msgid "Closed private chat of user %s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:476
#, python-format
msgid "Banned %s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:490
#, python-format
msgid "Unbanned %s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:503
#, python-format
msgid "Ignored %s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:517
#, python-format
msgid "Unignored %s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr ""

#: pynicotine/pluginsystem.py:413
msgid "Loading plugin system"
msgstr ""

#: pynicotine/pluginsystem.py:516
#, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""

#: pynicotine/pluginsystem.py:548
#, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr ""

#: pynicotine/pluginsystem.py:575
#, python-format
msgid "Loaded plugin %s"
msgstr ""

#: pynicotine/pluginsystem.py:579
#, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""

#: pynicotine/pluginsystem.py:644
#, python-format
msgid "Unloaded plugin %s"
msgstr ""

#: pynicotine/pluginsystem.py:648
#, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""

#: pynicotine/pluginsystem.py:752
#, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""

#: pynicotine/pluginsystem.py:810
msgid "No description"
msgstr ""

#: pynicotine/pluginsystem.py:887
#, python-format
msgid "Missing %s argument"
msgstr ""

#: pynicotine/pluginsystem.py:896
#, python-format
msgid "Invalid argument, possible choices: %s"
msgstr ""

#: pynicotine/pluginsystem.py:901
#, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr ""

#: pynicotine/pluginsystem.py:940
#, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
msgid "No UPnP devices found"
msgstr ""

#: pynicotine/portmapper.py:633
#, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""

#: pynicotine/portmapper.py:647
#, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""

#: pynicotine/privatechat.py:220
#, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr ""

#: pynicotine/search.py:368
#, python-format
msgid "Searching for wishlist item \"%s\""
msgstr ""

#: pynicotine/search.py:434
#, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr ""

#: pynicotine/search.py:760
#, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr ""

#: pynicotine/shares.py:302
msgid "Rebuilding shares…"
msgstr ""

#: pynicotine/shares.py:302
msgid "Rescanning shares…"
msgstr ""

#: pynicotine/shares.py:324
#, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr ""

#: pynicotine/shares.py:334
#, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""

#: pynicotine/shares.py:582
#, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr ""

#: pynicotine/shares.py:590
#, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr ""

#: pynicotine/shares.py:627
#, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr ""

#: pynicotine/shares.py:1046
#, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr ""

#: pynicotine/shares.py:1184
#, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr ""

#: pynicotine/slskmessages.py:3120
#, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""

#: pynicotine/slskproto.py:500
#, python-format
msgid "Specified network interface '%s' is not available"
msgstr ""

#: pynicotine/slskproto.py:511
#, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""

#: pynicotine/slskproto.py:523
#, python-format
msgid "Listening on port: %i"
msgstr ""

#: pynicotine/slskproto.py:805
#, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr ""

#: pynicotine/slskproto.py:1095
#, python-format
msgid "Reconnecting to server in %s seconds"
msgstr ""

#: pynicotine/slskproto.py:1170
#, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr ""

#: pynicotine/slskproto.py:1208
#, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr ""

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr ""

#: pynicotine/slskproto.py:1499
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr ""

#: pynicotine/uploads.py:382
#, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr ""

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, python-format
msgid "Upload I/O error: %s"
msgstr ""

#: pynicotine/uploads.py:1103
#, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr ""

#: pynicotine/userbrowse.py:236
#, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr ""

#: pynicotine/userbrowse.py:282
#, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr ""

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr ""

#: pynicotine/userinfo.py:160
#, python-format
msgid "Picture saved to %s"
msgstr ""

#: pynicotine/userinfo.py:163
#, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr ""

#: pynicotine/userinfo.py:190
#, python-format
msgid "User %(user)s is viewing your profile"
msgstr ""

#: pynicotine/users.py:272
#, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr ""

#: pynicotine/users.py:272
msgid "Cannot Connect"
msgstr ""

#: pynicotine/users.py:306
#, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr ""

#: pynicotine/users.py:315
#, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr ""

#: pynicotine/users.py:433
msgid "Soulseek Announcement"
msgstr "إعلان Soulseek"

#: pynicotine/users.py:449
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""

#: pynicotine/users.py:455
#, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""

#: pynicotine/users.py:473
msgid "Your password has been changed"
msgstr ""

#: pynicotine/users.py:473
msgid "Password Changed"
msgstr ""

#: pynicotine/utils.py:574
#, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr ""

#: pynicotine/utils.py:621
#, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr ""

#: pynicotine/utils.py:646
#, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr ""

#: pynicotine/utils.py:651
#, python-format
msgid "Attempting to load backup of file %s"
msgstr ""

#: pynicotine/utils.py:673
#, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr ""

#: pynicotine/utils.py:694
#, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr ""

#: pynicotine/utils.py:705
#, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr ""

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
msgid "Add buddy…"
msgstr ""

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
msgid "Toggle Text-to-Speech"
msgstr ""

#: pynicotine/gtkgui/ui/chatrooms.ui:100
msgid "Chat Room Command Help"
msgstr ""

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
msgid "_Log"
msgstr ""

#: pynicotine/gtkgui/ui/chatrooms.ui:187
msgid "Room Wall"
msgstr ""

#: pynicotine/gtkgui/ui/chatrooms.ui:202
msgid "R_oom Wall"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
msgid "Created by"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
msgid "Translated by"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
msgid "License"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
msgid "Welcome to Nicotine+"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
msgid "Download Files to Folder"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
msgid "Share Folders"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
msgid "You are ready to use Nicotine+!"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
msgid "Previous File"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
msgid "Next File"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
msgid "Name"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
msgid "Last Speed"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
msgid "_Export…"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
msgid "Keyboard Shortcuts"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
msgid "General"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
msgid "Rescan Shares"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
msgid "Show Log Pane"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
msgid "Confirm Quit"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
msgid "Quit"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
msgid "Menus"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
msgid "Open Main Menu"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
msgid "Open Context Menu"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
msgid "Tabs"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
msgid "Change Main Tab"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
msgid "Go to Previous Secondary Tab"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
msgid "Go to Next Secondary Tab"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
msgid "Reopen Closed Secondary Tab"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
msgid "Close Secondary Tab"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
msgid "Lists"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
msgid "Copy Selected Cell"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
msgid "Select All"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
msgid "Find"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
msgid "Remove Selected Row"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
msgid "Editing"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
msgid "Cut"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
msgid "Paste"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
msgid "Insert Emoji"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
msgid "File Transfers"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
msgid "Resume / Retry Transfer"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
msgid "Pause / Abort Transfer"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
msgid "Download / Upload To"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
msgid "Save List to Disk"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
msgid "Refresh"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
msgid "Expand / Collapse All"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
msgid "Back to Parent Folder"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
msgid "File Search"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
msgid "Result Filters"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
msgid "Current Session"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
msgid "Completed Downloads"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
msgid "Downloaded Size"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
msgid "Completed Uploads"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
msgid "Uploaded Size"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
msgid "Total"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
msgid "_Reset…"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
msgid "Add Wish…"
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
msgid "Clear All…"
msgstr ""

#: pynicotine/gtkgui/ui/downloads.ui:138
msgid "Clear All Finished/Filtered Downloads"
msgstr ""

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
msgid "Clear Finished"
msgstr ""

#: pynicotine/gtkgui/ui/downloads.ui:169
msgid "Clear Specific Downloads"
msgstr ""

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
msgid "Clear _All…"
msgstr ""

#: pynicotine/gtkgui/ui/interests.ui:21
msgid "Personal Interests"
msgstr ""

#: pynicotine/gtkgui/ui/interests.ui:40
msgid "Add something you like…"
msgstr ""

#: pynicotine/gtkgui/ui/interests.ui:62
msgid "Personal Dislikes"
msgstr ""

#: pynicotine/gtkgui/ui/interests.ui:80
msgid "Add something you dislike…"
msgstr ""

#: pynicotine/gtkgui/ui/interests.ui:143
msgid "Refresh Recommendations"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:26
msgid "Main Menu"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:43
msgid "Room…"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
msgid "Username…"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:58
msgid "Search term…"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:61
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:97
msgid "Search Scope"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:143
msgid "_Wishlist"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:165
msgid "Configure Searches"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:232
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
msgid "File Grouping Mode"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:404
msgid "Configure Downloads"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:471
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:643
msgid "Configure Uploads"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:710
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:788
msgid "_Open List"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:790
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:811
msgid "Configure Shares"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:878
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:956
msgid "_Personal Profile"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:978
msgid "Configure Account"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
msgid "Chat _History"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
msgid "Configure Chats"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
msgid "_Message All"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
msgid "Configure Ignored Users"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
msgid "Join or create room…"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
msgid "Configure User Profile"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
msgid "Connections"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
msgid "Downloading (Speed / Active Users)"
msgstr ""

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
msgid "Uploading (Speed / Active Users)"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
msgid "Search chat history…"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
msgid "Download Speed Limits"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
msgid "Unlimited download speed"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
msgid "Use download speed limit (KiB/s):"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
msgid "Use alternative download speed limit (KiB/s):"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
msgid "Search rooms…"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
msgid "Refresh Rooms"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
msgid "_Show feed of public chat room messages"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
msgid "_Accept private room invitations"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
msgid "Set wall message…"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
msgid "Search Result Filters"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
msgid "Result Filter Usage"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
msgid "Include Text"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
msgid "Files, folders and usernames containing this text will be shown."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
msgid "Exclude Text"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
msgid "Filters files based upon their file extension."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
msgid "File Size"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
msgid "Filters files based upon their file size."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
msgid "Filters files based upon their bitrate."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
msgid "Filters files based upon their duration."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
msgid "Multiple countries can be specified with commas or spaces."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
msgid "Free Slot"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
msgid "Upload Speed Limits"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
msgid "Unlimited upload speed"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
msgid "Use upload speed limit (KiB/s):"
msgstr ""

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
msgid "Use alternative upload speed limit (KiB/s):"
msgstr ""

#: pynicotine/gtkgui/ui/privatechat.ui:63
msgid "Private Chat Command Help"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:7
msgid "Include text…"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:18
msgid "Exclude text…"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:29
msgid "File type…"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:40
msgid "File size…"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
msgid "File size, e.g. >10.5m <1g"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:51
msgid "Bitrate…"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
msgid "Bitrate, e.g. 256 <1412"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:62
msgid "Duration…"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:73
msgid "Country code…"
msgstr ""

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
msgid "Country code, e.g. US ES or !DE !GB"
msgstr ""

#: pynicotine/gtkgui/ui/settings/ban.ui:28
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr ""

#: pynicotine/gtkgui/ui/settings/ban.ui:51
msgid "Codes must be in ISO 3166-2 format."
msgstr ""

#: pynicotine/gtkgui/ui/settings/ban.ui:66
msgid "Use custom geo block message:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
msgid "IP Addresses"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:75
msgid "Restore previously open private chats on startup"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:99
msgid "Enable spell checker"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:123
msgid "Enable CTCP-like private message responses (client version)"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:147
msgid "Number of recent private chat messages to show:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:172
msgid "Number of recent chat room messages to show:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:201
msgid "Chat Completion"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:219
msgid "Enable tab-key completion"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:247
msgid "Enable completion drop-down list"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:276
msgid "Minimum characters required to display drop-down:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:315
msgid "Allowed chat completions:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:342
msgid "Buddy names"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:354
msgid "Chat room usernames"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:366
msgid "Room names"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:378
msgid "Commands"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:404
msgid "Timestamps"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:421
msgid "Private chat format:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:448
msgid "Chat room format:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:485
msgid "Text-to-Speech"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:506
msgid "Enable Text-to-Speech"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:540
msgid "Text-to-Speech command:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:556
msgid "Private chat message:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:583
msgid "Chat room message:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:638
msgid "Censor"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:656
msgid "Enable censoring of text patterns"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:821
msgid "Auto-Replace"
msgstr ""

#: pynicotine/gtkgui/ui/settings/chats.ui:839
msgid "Enable automatic replacement of words"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
msgid "Store completed downloads in username subfolders"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
msgid "Double-click action for downloads:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
msgid "Allow users to send you any files:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
msgid "Folders"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
msgid "Finished downloads:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
msgid "Incomplete downloads:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
msgid "Received files:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
msgid "Run command after file download finishes ($ for file path):"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
msgid "Download Filters"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
msgid "Enable download filters"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
msgid "Verify Filters"
msgstr ""

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
msgid "Unverified"
msgstr ""

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr ""

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr ""

#: pynicotine/gtkgui/ui/settings/log.ui:142
msgid "Log transfers to file"
msgstr ""

#: pynicotine/gtkgui/ui/settings/log.ui:166
msgid "Log debug messages to file"
msgstr ""

#: pynicotine/gtkgui/ui/settings/log.ui:190
msgid "Log timestamp format:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/log.ui:228
msgid "Folder Locations"
msgstr ""

#: pynicotine/gtkgui/ui/settings/log.ui:245
msgid "Chatroom logs folder:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/log.ui:261
msgid "Private chat logs folder:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/log.ui:277
msgid "Transfer logs folder:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/log.ui:293
msgid "Debug logs folder:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/network.ui:39
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""

#: pynicotine/gtkgui/ui/settings/network.ui:118
msgid "Public IP address:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/network.ui:159
#, fuzzy
msgid "Listening port:"
msgstr "استمع على المنفذ المحدد"

#: pynicotine/gtkgui/ui/settings/network.ui:185
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr ""

#: pynicotine/gtkgui/ui/settings/network.ui:211
msgid "Away Status"
msgstr ""

#: pynicotine/gtkgui/ui/settings/network.ui:228
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr ""

#: pynicotine/gtkgui/ui/settings/network.ui:253
msgid "Auto-reply message when away:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/network.ui:299
msgid "Auto-connect to server on startup"
msgstr ""

#: pynicotine/gtkgui/ui/settings/network.ui:323
msgid "Soulseek server:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/network.ui:347
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""

#: pynicotine/gtkgui/ui/settings/network.ui:351
msgid "Network interface:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
msgid "Other"
msgstr ""

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
msgid "Now Playing Format"
msgstr ""

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
msgid "Now Playing message format:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
msgid "Test Configuration"
msgstr ""

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
msgid "Enable plugins"
msgstr ""

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
msgid "Add Plugins"
msgstr ""

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
msgid "_Add Plugins"
msgstr ""

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
msgid "Settings"
msgstr "الإعدادات"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
msgid "_Settings"
msgstr ""

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
msgid "Version:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
msgid "Created by:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:51
msgid "Enable search history"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:70
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:76
msgid "Show privately shared files in search results"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:106
msgid "Limit number of results per search:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:149
msgid "Result Filter Help"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:177
msgid "Enable search result filters by default"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:211
msgid "Include:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:236
msgid "Exclude:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:261
msgid "File Type:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:342
msgid "Duration:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:369
msgid "Country Code:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:407
msgid "Only show results from users with an available upload slot."
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:430
msgid "Network Searches"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:452
msgid "Respond to search requests from other users"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:486
msgid "Searches shorter than this number of characters will be ignored:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:511
msgid "Maximum search results to send per search request:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:578
msgid "Clear Search History"
msgstr ""

#: pynicotine/gtkgui/ui/settings/search.ui:626
msgid "Clear Filter History"
msgstr ""

#: pynicotine/gtkgui/ui/settings/shares.ui:33
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr ""

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/shares.ui:82
msgid "Buddy shares"
msgstr ""

#: pynicotine/gtkgui/ui/settings/shares.ui:89
msgid "Trusted shares"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
msgid "Double-click action for uploads:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
msgid "Limit upload speed:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
msgid "Per transfer"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
msgid "Total transfers"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
msgid "Upload Slots"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
msgid "Upload queue type:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
msgid "Fixed number of upload slots:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
msgid "Prioritize all buddies"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
msgid "Queue Limits"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
msgid "Maximum number of queued files per user:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
msgid "Limits do not apply to buddies"
msgstr ""

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
msgid "File manager command:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
msgid "Reset Picture"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
msgid "Self Description"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
msgid "Picture:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
msgid "Prefer dark mode"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
msgid "Use header bar"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
msgid "Display tray icon"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
msgid "Minimize to tray on startup"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
msgid "Language (requires a restart):"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
msgid "When closing window:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
msgid "Notifications"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
msgid "Enable sound for notifications"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
msgid "Show notification for private chats and mentions in the window title"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
msgid "Show notifications for:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
msgid "Finished file downloads"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
msgid "Finished folder downloads"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
msgid "Private messages"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
msgid "Chat room messages"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
msgid "Chat room mentions"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
msgid "Wishlist results found"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
msgid "Restore the previously active main tab at startup"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
msgid "Close-buttons on secondary tabs"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
msgid "Regular tab label color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
msgid "Changed tab label color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
msgid "Highlighted tab label color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
msgid "Visible main tabs:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
msgid "Tab bar positions:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
msgid "Main tabs"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
msgid "Show reverse file paths (requires a restart)"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
msgid "Show exact file sizes (requires a restart)"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
msgid "List text color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
msgid "Enable colored usernames"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
msgid "Chat username appearance:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
msgid "Remote text color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
msgid "Local text color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
msgid "Command output text color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
msgid "/me action text color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
msgid "Highlighted text color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
msgid "URL link text color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
msgid "User Statuses"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
msgid "Online color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
msgid "Away color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
msgid "Offline color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
msgid "Text Entries"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
msgid "Text entry background color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
msgid "Text entry text color:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
msgid "Fonts"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
msgid "Global font:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
msgid "List font:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
msgid "Text view font:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
msgid "Chat font:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
msgid "Transfers font:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
msgid "Search font:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
msgid "Browse font:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
msgid "Icons"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
msgid "Icon theme folder:"
msgstr ""

#: pynicotine/gtkgui/ui/uploads.ui:86
msgid "Abort User(s)"
msgstr ""

#: pynicotine/gtkgui/ui/uploads.ui:116
msgid "Ban User(s)"
msgstr ""

#: pynicotine/gtkgui/ui/uploads.ui:138
msgid "Clear All Finished/Cancelled Uploads"
msgstr ""

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
msgid "Message All"
msgstr ""

#: pynicotine/gtkgui/ui/uploads.ui:199
msgid "Clear Specific Uploads"
msgstr ""

#: pynicotine/gtkgui/ui/userbrowse.ui:161
msgid "Save Shares List to Disk"
msgstr ""

#: pynicotine/gtkgui/ui/userbrowse.ui:178
msgid "Refresh Files"
msgstr ""

#: pynicotine/gtkgui/ui/userinfo.ui:101
msgid "Edit Profile"
msgstr ""

#: pynicotine/gtkgui/ui/userinfo.ui:149
msgid "Shared Files"
msgstr ""

#: pynicotine/gtkgui/ui/userinfo.ui:203
msgid "Upload Speed"
msgstr ""

#: pynicotine/gtkgui/ui/userinfo.ui:230
msgid "Free Upload Slots"
msgstr ""

#: pynicotine/gtkgui/ui/userinfo.ui:284
msgid "Queued Uploads"
msgstr ""

#: pynicotine/gtkgui/ui/userinfo.ui:354
msgid "Edit Interests"
msgstr ""

#: pynicotine/gtkgui/ui/userinfo.ui:624
msgid "_Gift Privileges…"
msgstr ""

#: pynicotine/gtkgui/ui/userinfo.ui:663
msgid "_Refresh Profile"
msgstr ""

#: pynicotine/plugins/core_commands/PLUGININFO:3
msgid "Nicotine+ Commands"
msgstr ""

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "فريق نيكوتين+Ekipi Nicotine +"
