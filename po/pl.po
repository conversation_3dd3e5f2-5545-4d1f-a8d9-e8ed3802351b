# SPDX-FileCopyrightText: 2003-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2025-03-02 14:14+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Polish <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/pl/>\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 "
"|| n%100>=20) ? 1 : 2);\n"
"X-Generator: Weblate 5.10.3-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr "Klient Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Graficzny klient sieci peer-to-peer Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr "Soulseek;Nicotine;udostępnianie;czat;komunikacja;P2P;peer-to-peer;GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
msgid "Browse the Soulseek network"
msgstr "Przeglądaj sieć Soulseek"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+ to graficzny klient sieci peer-to-peer Soulseek."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ ma na celu być lekką, przyjemną, darmową i otwartoźródłową (FOSS) "
"alternatywą dla oficjalnego klienta Soulseek, oferując jednocześnie obszerny "
"zestaw funkcji."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
msgid "Search Files"
msgstr "Szukaj plików"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Pobieranie"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
msgid "Browse Shares"
msgstr "Przeglądaj zasoby"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
msgid "Private Chat"
msgstr "Czat prywatny"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Zespół Nicotine+"

#: pynicotine/__init__.py:50
#, python-format
msgid "Website: %s"
msgstr "Strona internetowa: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "pokaż tę wiadomość pomocy i wyjdź"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "plik"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr "użyj niestandardowego pliku konfiguracyjnego"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "katalog"

#: pynicotine/__init__.py:64
msgid "alternative directory for user data and plugins"
msgstr "alternatywny katalog dla danych użytkownika i wtyczek"

#: pynicotine/__init__.py:68
msgid "start the program without showing window"
msgstr "uruchom program bez wyświetlania okna"

#: pynicotine/__init__.py:71
msgid "ip"
msgstr "ip"

#: pynicotine/__init__.py:72
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "powiąż gniazda z podanym adresem IP (przydatne dla VPN)"

#: pynicotine/__init__.py:75
msgid "port"
msgstr "port"

#: pynicotine/__init__.py:76
msgid "listen on the given port"
msgstr "nasłuchuj na danym porcie"

#: pynicotine/__init__.py:80
msgid "rescan shared files"
msgstr "przeskanuj udostępnione pliki"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr "uruchom program w trybie headless (bez GUI)"

#: pynicotine/__init__.py:88
msgid "display version and exit"
msgstr "wyświetl wersję i wyjdź"

#: pynicotine/__init__.py:121
#, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Używasz niewspieranej wersji Pythona (%(old_version)s).\n"
"Wymagana wersja to Python %(min_version)s lub nowsza."

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Nie udało się przeskanować zasobów. Proszę zamknąć inne instancje Nicotine+ "
"i spróbować ponownie."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, python-format
msgid "%(user)s is away"
msgstr "%(user)s zaraz wraca"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, python-format
msgid "%(user)s is online"
msgstr "%(user)s jest dostępny"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, python-format
msgid "%(user)s is offline"
msgstr "%(user)s jest niedostępny"

#: pynicotine/buddies.py:316
msgid "Buddy Status"
msgstr "Status znajomego"

#: pynicotine/chatrooms.py:383
#, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Zostałeś dodany do prywatnego pokoju: %(room)s"

#: pynicotine/chatrooms.py:478
#, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr ""
"Wiadomość na czacie od użytkownika '%(user)s' w pokoju '%(room)s': "
"%(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr "Nie można utworzyć katalogu '%(path)s', błąd: %(error)s"

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr "Błąd tworzenia kopii zapasowej konfiguracji: %s"

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr "Kopia zapasowa konfiguracji zapisana do: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Ładowanie %(program)s %(version)s"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Zamykanie %(program)s %(version)s, %(status)s…"

#: pynicotine/core.py:246
msgid "terminating"
msgstr "zakańczanie"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "zamykanie aplikacji"

#: pynicotine/core.py:259
#, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Wychodzenie z %(program)s %(version)s!"

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr "Musisz podać nazwę użytkownika i hasło przed połączeniem…"

#: pynicotine/downloads.py:239
#, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""
"Błąd: Pobieranie filtrów nie powiodło się! Zweryfikuj filtry. Uzasadnienie: "
"%s"

#: pynicotine/downloads.py:254
#, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Błąd: %(num)d Pobieranie filtrów nie powiodło się! %(error)s "

#: pynicotine/downloads.py:366
#, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s pobrany od %(user)s"

#: pynicotine/downloads.py:370
msgid "File Downloaded"
msgstr "Plik pobrany"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Wywołano: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "Problem z wywołaniem '%(command)s': %(error)s"

#: pynicotine/downloads.py:407
#, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s pobrany od %(user)s"

#: pynicotine/downloads.py:411
msgid "Folder Downloaded"
msgstr "Folder pobrany"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Wywołano na folderze: %s"

#: pynicotine/downloads.py:443
#, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "Nie można przenieść '%(tempfile)s' do '%(file)s': %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
msgid "Download Folder Error"
msgstr "Błąd pobierania folderu"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Pobieranie zakończone: użytkownik %(user)s, plik %(file)s"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Pobieranie przerwane, użytkownik %(user)s plik %(file)s"

#: pynicotine/downloads.py:1150
#, python-format
msgid "Download I/O error: %s"
msgstr "Błąd pobierania I/O: %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Nie można uzyskać wyłącznej blokady na pliku - błąd I/O: %s"

#: pynicotine/downloads.py:1202
#, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Nie można zapisać pliku w %(folder_path)s: %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Pobieranie rozpoczęte: użytkownik %(user)s, plik %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, python-format
msgid "Cannot find %s, please install it."
msgstr "Nie można znaleźć %s, proszę zainstalować."

#: pynicotine/gtkgui/__init__.py:162
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr ""
"Brak dostępnego środowiska graficznego, użycie trybu headless (bez GUI)"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "_Połącz"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "_Rozłącz"

#: pynicotine/gtkgui/application.py:308
msgid "Soulseek _Privileges"
msgstr "_Przywileje Soulseek"

#: pynicotine/gtkgui/application.py:314
msgid "_Preferences"
msgstr "_Ustawienia"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
msgid "_Quit"
msgstr "_Zamknij"

#: pynicotine/gtkgui/application.py:337
msgid "Browse _Public Shares"
msgstr "_Przeglądaj zasoby publiczne"

#: pynicotine/gtkgui/application.py:338
msgid "Browse _Buddy Shares"
msgstr "Przeglądaj zasoby _znajomych"

#: pynicotine/gtkgui/application.py:339
msgid "Browse _Trusted Shares"
msgstr "Przeglądaj zasoby zau_fanych"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
msgid "_Rescan Shares"
msgstr "_Przeskanuj zasoby"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
msgid "Configure _Shares"
msgstr "Konfiguruj _zasoby"

#: pynicotine/gtkgui/application.py:371
msgid "_Keyboard Shortcuts"
msgstr "_Skróty klawiszowe"

#: pynicotine/gtkgui/application.py:372
msgid "_Setup Assistant"
msgstr "_Asystent konfiguracji"

#: pynicotine/gtkgui/application.py:373
msgid "_Transfer Statistics"
msgstr "_Statystyki transferów"

#: pynicotine/gtkgui/application.py:378
msgid "Report a _Bug"
msgstr "Zgłoś _błąd"

#: pynicotine/gtkgui/application.py:379
msgid "Improve T_ranslations"
msgstr "Popraw _tłumaczenie"

#: pynicotine/gtkgui/application.py:383
msgid "_About Nicotine+"
msgstr "_O Nicotine+"

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "_Plik"

#: pynicotine/gtkgui/application.py:395
msgid "_Shares"
msgstr "_Zasoby"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
msgid "_Help"
msgstr "_Pomoc"

#: pynicotine/gtkgui/application.py:410
msgid "_Browse Shares"
msgstr "_Przeglądaj zasoby"

#: pynicotine/gtkgui/application.py:465
#, python-format
msgid "Unable to show notification: %s"
msgstr "Nie można wyświetlić powiadomienia: %s"

#: pynicotine/gtkgui/application.py:526
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Wciąż wysyłasz pliki. Czy na pewno chcesz wyjść?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr "Poczekaj na zakończenie przesyłania"

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr "Czy na pewno chcesz wyjść?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
msgid "_No"
msgstr "_Nie"

#: pynicotine/gtkgui/application.py:535
msgid "_Run in Background"
msgstr "_Działaj w tle"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr "Zamknij Nicotine+"

#: pynicotine/gtkgui/application.py:561
msgid "Shares Not Available"
msgstr "Zasoby niedostępne"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Sprawdź, czy dyski zewnętrzne są zamontowane, a uprawnienia do folderów są "
"prawidłowe."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
msgid "_Cancel"
msgstr "_Anuluj"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "_Ponów"

#: pynicotine/gtkgui/application.py:567
msgid "_Force Rescan"
msgstr "_Wymuś ponowne skanowanie"

#: pynicotine/gtkgui/application.py:742
msgid "Message Downloading Users"
msgstr "Informowanie użytkowników pobierających pliki"

#: pynicotine/gtkgui/application.py:743
msgid "Send private message to all users who are downloading from you:"
msgstr ""
"Wyślij prywatną wiadomość do wszystkich użytkowników, którzy pobierają od "
"Ciebie:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
msgid "_Send Message"
msgstr "_Wyślij wiadomość"

#: pynicotine/gtkgui/application.py:756
msgid "Message Buddies"
msgstr "Zawiadom znajomych"

#: pynicotine/gtkgui/application.py:757
msgid "Send private message to all online buddies:"
msgstr "Wyślij prywatną wiadomość do wszystkich znajomych online:"

#: pynicotine/gtkgui/application.py:786
msgid "Select a Saved Shares List File"
msgstr "Wybierz zapisaną listę zasobów z pliku"

#: pynicotine/gtkgui/application.py:877
msgid "Critical Error"
msgstr "Błąd krytyczny"

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Program Nicotine+ napotkał błąd krytyczny i musi zostać zamknięty. Proszę "
"skopiować poniższy błąd i dołączyć go do raportu o błędzie:"

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr "_Zamknij Nicotine+"

#: pynicotine/gtkgui/application.py:883
msgid "_Copy & Report Bug"
msgstr "_Skopiuj i zgłoś błąd"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Status"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr "Kraj"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Użytkownik"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Prędkość"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Pliki"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted"
msgstr "Zaufany"

#: pynicotine/gtkgui/buddies.py:108
msgid "Notify"
msgstr "Powiadom"

#: pynicotine/gtkgui/buddies.py:114
msgid "Prioritized"
msgstr "Priorytetowe"

#: pynicotine/gtkgui/buddies.py:120
msgid "Last Seen"
msgstr "Ostatnio widziany"

#: pynicotine/gtkgui/buddies.py:126
msgid "Note"
msgstr "Notatka"

#: pynicotine/gtkgui/buddies.py:143
msgid "Add User _Note…"
msgstr "Dodaj _notatkę użytkownika…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Usuń"

#: pynicotine/gtkgui/buddies.py:364
msgid "Never seen"
msgstr "Nigdy niewidziany"

#: pynicotine/gtkgui/buddies.py:529
msgid "Add User Note"
msgstr "Dodaj notatkę użytkownika"

#: pynicotine/gtkgui/buddies.py:530
#, python-format
msgid "Add a note about user %s:"
msgstr "Dodaj notatkę o użytkowniku %s:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
msgid "_Add"
msgstr "_Dodaj"

#: pynicotine/gtkgui/chatrooms.py:224
msgid "Create New Room?"
msgstr "Utworzyć nowy pokój?"

#: pynicotine/gtkgui/chatrooms.py:225
#, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Jesteś pewien, że chcesz utworzyć nowy pokój \"%s\"?"

#: pynicotine/gtkgui/chatrooms.py:226
msgid "Make room private"
msgstr "Uczyń pokój prywatnym"

#: pynicotine/gtkgui/chatrooms.py:515
msgid "Search activity log…"
msgstr "Przeszukaj dziennik aktywności…"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
msgid "Search chat log…"
msgstr "Przeszukaj dziennik czatów…"

#: pynicotine/gtkgui/chatrooms.py:597
msgid "Sear_ch User's Files"
msgstr "Szuk_aj wśród plików użytkownika"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
msgid "Find…"
msgstr "Szukaj…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Kopiuj"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr "Kopiuj wszystko"

#: pynicotine/gtkgui/chatrooms.py:608
msgid "Clear Activity View"
msgstr "Wyczyść widok aktywności"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
msgid "_Leave Room"
msgstr "Opuść _pokój"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
msgid "Copy Link"
msgstr "Kopiuj link"

#: pynicotine/gtkgui/chatrooms.py:624
msgid "View Room Log"
msgstr "Wyświetl dziennik pokoju"

#: pynicotine/gtkgui/chatrooms.py:627
msgid "Delete Room Log…"
msgstr "Usuń dziennik pokoju…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
msgid "Clear Message View"
msgstr "Wyczyść widok wiadomości"

#: pynicotine/gtkgui/chatrooms.py:832
#, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s wspomniał o Tobie w pokoju %(room)s"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "Wspomniany przez %(user)s w pokoju %(room)s"

#: pynicotine/gtkgui/chatrooms.py:855
#, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "Wiadomość od %(user)s w pokoju %(room)s"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s dołączył do pokoju"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s opuścił pokój"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s zaraz wraca"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s powrócił"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
msgid "Delete Logged Messages?"
msgstr "Usunąć zarejestrowane wiadomości?"

#: pynicotine/gtkgui/chatrooms.py:1197
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"Czy na pewno chcesz trwale usunąć wszystkie zarejestrowane wiadomości dla "
"tego pokoju?"

#: pynicotine/gtkgui/dialogs/about.py:405
msgid "About"
msgstr "O alikacji"

#: pynicotine/gtkgui/dialogs/about.py:415
msgid "Website"
msgstr "Strona internetowa"

#: pynicotine/gtkgui/dialogs/about.py:457
#, python-format
msgid "Error checking latest version: %s"
msgstr "Błąd podczas sprawdzania najnowszej wersji: %s"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr "Dostępna nowa wersja: %s"

#: pynicotine/gtkgui/dialogs/about.py:467
msgid "Up to date"
msgstr "Aktualny"

#: pynicotine/gtkgui/dialogs/about.py:496
msgid "Checking latest version…"
msgstr "Sprawdzanie najnowszej wersji…"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
msgid "Setup Assistant"
msgstr "Asystent konfiguracji"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
msgid "Virtual Folder"
msgstr "Wirtualny folder"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
msgid "Folder"
msgstr "Folder"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
msgid "_Previous"
msgstr "_Wstecz"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
msgid "_Finish"
msgstr "_Zakończ"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
msgid "_Next"
msgstr "_Następny"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
msgid "Add a Shared Folder"
msgstr "Udostępnij folder"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
msgid "Edit Shared Folder"
msgstr "Edytuj udostępnione foldery"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Wprowadź nową wirtualną nazwę dla '%(dir)s':"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
msgid "_Edit"
msgstr "_Edytuj"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"Użytkownik %s już istnieje, a wprowadzone hasło jest nieprawidłowe. Proszę "
"wybrać inną nazwę użytkownika, jeśli logujesz się po raz pierwszy."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
msgid "File Properties"
msgstr "Właściwości pliku"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "Właściwości pliku (%(num)i z %(total)i / %(size)s / %(length)s)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Właściwości pliku (%(num)i z %(total)i / %(size)s)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
msgid "_Apply"
msgstr "_Zastosuj"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
msgid "Add…"
msgstr "Dodaj…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
msgid "Edit…"
msgstr "Edytuj…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, python-format
msgid "%s Settings"
msgstr "Ustawienia %s"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
msgid "Add Item"
msgstr "Dodaj obiekt"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
msgid "Edit Item"
msgstr "Edytuj obiekt"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "Nieznany"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
msgid "Check Port Status"
msgstr "Sprawdź status portu"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, port %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
msgid "Password Change Rejected"
msgstr "Nie można zmienić hasła"

#: pynicotine/gtkgui/dialogs/preferences.py:218
msgid "Enter a new password for your Soulseek account:"
msgstr "Wprowadź nowe hasło do swojego konta Soulseek:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"Jesteś wylogowany z sieci Soulseek. Jeśli próbujesz zmienić hasło dla "
"istniejącego już konta, musisz najpierw zalogować się."

#: pynicotine/gtkgui/dialogs/preferences.py:223
msgid "Enter password to use when logging in:"
msgstr "Wprowadź hasło, które będzie używane podczas logowania:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
msgid "Change Password"
msgstr "Zmień hasło"

#: pynicotine/gtkgui/dialogs/preferences.py:230
msgid "_Change"
msgstr "Z_mień"

#: pynicotine/gtkgui/dialogs/preferences.py:274
msgid "No one"
msgstr "Nikt"

#: pynicotine/gtkgui/dialogs/preferences.py:275
msgid "Everyone"
msgstr "Każdy"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Znajomi"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted buddies"
msgstr "Zaufani użytkownicy"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
msgid "Nothing"
msgstr "Nic"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
msgid "Open File"
msgstr "Otwórz plik"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
msgid "Open in File Manager"
msgstr "Otwórz w menedżerze plików"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Wyszukiwanie"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
msgid "Pause"
msgstr "Pauza"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
msgid "Resume"
msgstr "Wznów"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
msgid "Browse Folder"
msgstr "Przeglądaj folder"

#: pynicotine/gtkgui/dialogs/preferences.py:317
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Składnia</b>: wielkość liter nie ma znaczenia. Jeśli włączone, można "
"używać wyrażeń regularnych języka Python, w przeciwnym razie obsługiwane są "
"tylko dopasowania wieloznaczne *."

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr "Filtr"

#: pynicotine/gtkgui/dialogs/preferences.py:334
msgid "Regex"
msgstr "Wyrażenie regularne"

#: pynicotine/gtkgui/dialogs/preferences.py:468
msgid "Add Download Filter"
msgstr "Dodaj filtr pobierania"

#: pynicotine/gtkgui/dialogs/preferences.py:469
msgid "Enter a new download filter:"
msgstr "Wprowadź nowy filtr pobierania:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
msgid "Enable regular expressions"
msgstr "Włącz wyrażenia regularne"

#: pynicotine/gtkgui/dialogs/preferences.py:498
msgid "Edit Download Filter"
msgstr "Edytuj filtr pobierania"

#: pynicotine/gtkgui/dialogs/preferences.py:499
msgid "Modify the following download filter:"
msgstr "Modyfikuj ten filtr pobierania:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d Błąd! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
msgid "Filters Successful"
msgstr "Filtrowanie zakończone"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr "Publiczny"

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr "Dostępne dla"

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "Przerwij"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
msgid "Retry"
msgstr "Ponów"

#: pynicotine/gtkgui/dialogs/preferences.py:828
msgid "Round Robin"
msgstr "Algorytm karuzelowy (round-robin)"

#: pynicotine/gtkgui/dialogs/preferences.py:829
msgid "First In, First Out"
msgstr "Pierwsze weszło, pierwsze wyszło (FIFO)"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
msgid "Username"
msgstr "Użytkownik"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
msgid "IP Address"
msgstr "Adres IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
msgid "Ignore User"
msgstr "Ignoruj użytkownika"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
msgid "Enter the name of the user you want to ignore:"
msgstr "Wprowadź nazwę użytkownika, którego chcesz ignorować:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
msgid "Ignore IP Address"
msgstr "Ignoruj adres IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
msgid "Enter an IP address you want to ignore:"
msgstr "Wprowadź adres IP, który chcesz ignorować:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "* is a wildcard"
msgstr "* to symbol wieloznaczny"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
msgid "Ban User"
msgstr "Zablokuj użytkownika"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
msgid "Enter the name of the user you want to ban:"
msgstr "Wprowadź nazwę użytkownika, którego chcesz zablokować:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
msgid "Ban IP Address"
msgstr "Zablokuj adres IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "Enter an IP address you want to ban:"
msgstr "Wpisz adres IP, który chcesz zablokować:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr "Format kodów"

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
msgid "Pattern"
msgstr "Wzór"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
msgid "Replacement"
msgstr "Zamiennik"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
msgid "Censor Pattern"
msgstr "Wzór cenzurowania"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Wprowadź wzór, który chcesz ocenzurować. Dodaj spacje wokół słów, jeśli nie "
"chcesz uwzględniać poszczególnych ciągów znaków wewnątrz wyrazów (może "
"skutkować błędami w przypadku końca linii)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
msgid "Edit Censored Pattern"
msgstr "Edycja cenzurowanego wzoru"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
msgid "Add Replacement"
msgstr "Dodaj zamiennik"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
msgid "Enter a text pattern and what to replace it with:"
msgstr "Wprowadź wzór tekstu i to, co ma go zastąpić:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
msgid "Edit Replacement"
msgstr "Edytuj zamiennik"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
msgid "System default"
msgstr "Domyślne systemowe"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
msgid "Show confirmation dialog"
msgstr "Pokaż okno potwierdzenia"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
msgid "Run in the background"
msgstr "Działaj w tle"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
msgid "bold"
msgstr "pogrubienie"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
msgid "italic"
msgstr "podkreślenie"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
msgid "underline"
msgstr "podkreślenie"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
msgid "normal"
msgstr "normalnie"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
msgid "Separate Buddies tab"
msgstr "Oddzielna karta znajomych"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
msgid "Sidebar in Chat Rooms tab"
msgstr "Pasek boczny na karcie Pokoje rozmów"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr "Zawsze widoczny pasek boczny"

#: pynicotine/gtkgui/dialogs/preferences.py:1777
msgid "Top"
msgstr "Góra"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
msgid "Bottom"
msgstr "Dół"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
msgid "Left"
msgstr "Lewa"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
msgid "Right"
msgstr "Prawa"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "Połączony"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Zaraz wracam"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Rozłączony"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
msgid "Tab Changed"
msgstr "Karta zmieniona"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
msgid "Tab Highlight"
msgstr "Karta podświetlona"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
msgid "Window"
msgstr "Okno"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
msgid "Online (Tray)"
msgstr "Połączony (zasobnik)"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
msgid "Away (Tray)"
msgstr "Zaraz wracam (zasobnik)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
msgid "Offline (Tray)"
msgstr "Rozłączony (zasobnik)"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
msgid "Message (Tray)"
msgstr "Wiadomość (zasobnik)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Protokół"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
msgid "Command"
msgstr "Komenda"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
msgid "Add URL Handler"
msgstr "Dodaj obsługę adresu URL"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Wprowadź protokół i polecenie programu obsługi adresów URL:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
msgid "Edit Command"
msgstr "Edytuj polecenie"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Wprowadź nowe polecenie dla protokołu %s:"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
msgid "Username;APIKEY"
msgstr "Nazwa użytkownika;APIKEY"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr ""
"Odtwarzacz muzyki (np. amarok, audacious, exaile); pozostaw puste, aby "
"automatycznie wykryć:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
msgid "Username: "
msgstr "Użytkownik: "

#: pynicotine/gtkgui/dialogs/preferences.py:2767
msgid "Command:"
msgstr "Komenda:"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
msgid "Title"
msgstr "Tytuł"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Teraz odtwarzanie (zazwyczaj \"%(artist)s - %(title)s\")"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
msgid "Artist"
msgstr "Artysta"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
msgid "Duration"
msgstr "Czas trwania"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "Przepływność"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
msgid "Comment"
msgstr "Komentarz"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
msgid "Album"
msgstr "Album"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
msgid "Track Number"
msgstr "Numer ścieżki"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
msgid "Year"
msgstr "Rok"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
msgid "Filename (URI)"
msgstr "Nazwa pliku (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
msgid "Program"
msgstr "Program"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
msgid "Enabled"
msgstr "Włączony"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
msgid "Plugin"
msgstr "Wtyczka"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
msgid "No Plugin Selected"
msgstr "Nie wybrano wtyczki"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
msgid "Preferences"
msgstr "Ustawienia"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
msgid "Network"
msgstr "Sieć"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
msgid "User Interface"
msgstr "Interfejs użytkownika"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Udostępnione zasoby"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Wysyłanie"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Wyszukiwanie"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
msgid "User Profile"
msgstr "Profil użytkownika"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
msgid "Chats"
msgstr "Czaty"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
msgid "Now Playing"
msgstr "Teraz odtwarzanie"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Logowanie zdarzeń"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
msgid "Banned Users"
msgstr "Zablokowani użytkownicy"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
msgid "Ignored Users"
msgstr "Ignorowani użytkownicy"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
msgid "URL Handlers"
msgstr "Obsługi protokołów URL"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr "Wtyczki"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
msgid "Pick a File Name for Config Backup"
msgstr "Podaj nazwę pliku dla kopii zapasowej konfiguracji"

#: pynicotine/gtkgui/dialogs/statistics.py:75
msgid "Transfer Statistics"
msgstr "Statystyki transferów"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, python-format
msgid "Total Since %(date)s"
msgstr "Razem od %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
msgid "Reset Transfer Statistics?"
msgstr "Zresetować statystyki transferów?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
msgid "Do you really want to reset transfer statistics?"
msgstr "Czy jesteś pewien, że chcesz zresetować statystyki transferów?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
msgid "Wishlist"
msgstr "Lista życzeń"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
msgid "Wish"
msgstr "Życzenia"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
msgid "_Search for Item"
msgstr "_Szukaj tego obiektu"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
msgid "Edit Wish"
msgstr "Edytuj życzenie"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, python-format
msgid "Enter new value for wish '%s':"
msgstr "Wprowadź nową wartość dla życzenia '%s':"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
msgid "Clear Wishlist?"
msgstr "Wyczyścić listę życzeń?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
msgid "Do you really want to clear your wishlist?"
msgstr "Jesteś pewien, że chcesz wyczyścić listę życzeń?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Ścieżka"

#: pynicotine/gtkgui/downloads.py:47
msgid "_Resume"
msgstr "_Wznów"

#: pynicotine/gtkgui/downloads.py:48
msgid "P_ause"
msgstr "Wstrzy_maj"

#: pynicotine/gtkgui/downloads.py:72
msgid "Finished / Filtered"
msgstr "Zakończone / Filtrowane"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Zakończone"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
msgid "Paused"
msgstr "Wstrzymane"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
msgid "Filtered"
msgstr "Filtrowane"

#: pynicotine/gtkgui/downloads.py:77
msgid "Deleted"
msgstr "Usunięto"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
msgid "Queued…"
msgstr "W kolejce…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
msgid "Everything…"
msgstr "Wszystko…"

#: pynicotine/gtkgui/downloads.py:132
#, python-format
msgid "Downloads: %(speed)s"
msgstr "Pobierane: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
msgid "Clear Queued Downloads"
msgstr "Wyczyść kolejkę pobierania"

#: pynicotine/gtkgui/downloads.py:139
msgid "Do you really want to clear all queued downloads?"
msgstr "Czy na pewno chcesz usunąć wszystkie pliki z kolejki pobierania?"

#: pynicotine/gtkgui/downloads.py:151
msgid "Clear All Downloads"
msgstr "Wyczyść wszystkie pobrane"

#: pynicotine/gtkgui/downloads.py:152
msgid "Do you really want to clear all downloads?"
msgstr "Jesteś pewien, że chcesz wyczyścić wszystkie pobrane?"

#: pynicotine/gtkgui/downloads.py:169
#, python-format
msgid "Download %(num)i files?"
msgstr "Pobrać %(num)i plików?"

#: pynicotine/gtkgui/downloads.py:170
#, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"Jesteś pewien, że chcesz pobrać %(num)i plików z folderu %(folder)s "
"użytkownika %(user)s?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
msgid "_Download Folder"
msgstr "Folder _pobierania"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
msgid "Likes"
msgstr "Lubię"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
msgid "Dislikes"
msgstr "Nie lubię"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Ocena"

#: pynicotine/gtkgui/interests.py:111
msgid "Item"
msgstr "Obiekt"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
msgid "_Recommendations for Item"
msgstr "Re_komendacje dla tego obiektu"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
msgid "I _Like This"
msgstr "L_ubię to"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
msgid "I _Dislike This"
msgstr "Nie lu_bię tego"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
msgid "Recommendations"
msgstr "Rekomendacje"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
msgid "Similar Users"
msgstr "Podobni użytkownicy"

#: pynicotine/gtkgui/interests.py:427
#, python-format
msgid "Recommendations (%s)"
msgstr "Rekomendacje (%s)"

#: pynicotine/gtkgui/interests.py:451
#, python-format
msgid "Similar Users (%s)"
msgstr "Podobni użytkownicy (%s)"

#: pynicotine/gtkgui/mainwindow.py:238
msgid "Search log…"
msgstr "Szukaj w logach…"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, python-format
msgid "Private Message from %(user)s"
msgstr "Prywatna wiadomość od %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
msgid "Wishlist Results Found"
msgstr "Znaleziono wyniki listy życzeń"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
msgid "User Profiles"
msgstr "Profile użytkowników"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
msgid "Chat Rooms"
msgstr "Lista pokoi"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "Zainteresowania"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
msgid "Chat"
msgstr "Czat"

#: pynicotine/gtkgui/mainwindow.py:1111
msgid "[Debug] Connections"
msgstr "[Debugowanie] Połączenia"

#: pynicotine/gtkgui/mainwindow.py:1112
msgid "[Debug] Messages"
msgstr "[Debugowanie] Wiadomości"

#: pynicotine/gtkgui/mainwindow.py:1113
msgid "[Debug] Transfers"
msgstr "[Debugowanie] Transfery"

#: pynicotine/gtkgui/mainwindow.py:1114
msgid "[Debug] Miscellaneous"
msgstr "[Debugowanie] Różne"

#: pynicotine/gtkgui/mainwindow.py:1119
msgid "_Find…"
msgstr "_Szukaj…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
msgid "_Copy"
msgstr "_Kopiuj"

#: pynicotine/gtkgui/mainwindow.py:1122
msgid "Copy _All"
msgstr "Kopiuj wszystko"

#: pynicotine/gtkgui/mainwindow.py:1127
msgid "View _Debug Logs"
msgstr "Wyświetl _dzienniki debugowania"

#: pynicotine/gtkgui/mainwindow.py:1128
msgid "View _Transfer Logs"
msgstr "Wyświetl dziennik _transferu"

#: pynicotine/gtkgui/mainwindow.py:1132
msgid "_Log Categories"
msgstr "_Kategorie dziennika"

#: pynicotine/gtkgui/mainwindow.py:1134
msgid "Clear Log View"
msgstr "Wyczyść widok dziennika"

#: pynicotine/gtkgui/mainwindow.py:1199
msgid "Preparing Shares"
msgstr "Przygotowywanie zasobów"

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr "Skanowanie zasobów"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
msgid "Shared Folders"
msgstr "Udostępnione foldery"

#: pynicotine/gtkgui/popovers/chathistory.py:77
msgid "Latest Message"
msgstr "Ostatnia wiadomość"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Pokój"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Użytkownicy"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
msgid "Join Room"
msgstr "Dołącz do pokoju"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
msgid "Leave Room"
msgstr "Opuść pokój"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
msgid "Disown Private Room"
msgstr "Odrzuć prywatny pokój"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
msgid "Cancel Room Membership"
msgstr "Anuluj członkostwo w pokoju"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
msgid "Close All Tabs…"
msgstr "Zamknij wszystkie karty…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
msgid "_Close Tab"
msgstr "_Zamknij kartę"

#: pynicotine/gtkgui/privatechat.py:379
msgid "View Chat Log"
msgstr "Wyświetl dziennik czatu"

#: pynicotine/gtkgui/privatechat.py:382
msgid "Delete Chat Log…"
msgstr "Usuń dziennik czatu…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
msgid "User Actions"
msgstr "Działania użytkownika"

#: pynicotine/gtkgui/privatechat.py:477
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Czy na pewno chcesz trwale usunąć wszystkie zarejestrowane wiadomości dla "
"tego użytkownika?"

#: pynicotine/gtkgui/privatechat.py:528
msgid "* Messages sent while you were offline"
msgstr "* Wiadomości wysłane, podczas bycia rozłączonym"

#: pynicotine/gtkgui/search.py:90
msgid "_Global"
msgstr "_Globalne"

#: pynicotine/gtkgui/search.py:91
msgid "_Buddies"
msgstr "_Znajomi"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
msgid "_Rooms"
msgstr "_Pokoje"

#: pynicotine/gtkgui/search.py:93
msgid "_User"
msgstr "_Użytkownik"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
msgid "In Queue"
msgstr "W kolejce"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
msgid "File Type"
msgstr "Typ pliku"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Nazwa pliku"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Rozmiar"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
msgid "Quality"
msgstr "Jakość"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
msgid "Copy _File Path"
msgstr "Kopiuj ścieżkę _pliku"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "Kopiuj _URL"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
msgid "Copy Folder U_RL"
msgstr "Kopiuj U_RL folderu"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
msgid "_Download File(s)"
msgstr "_Pobierz plik(i)"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
msgid "Download File(s) _To…"
msgstr "Pobierz plik(i) _do…"

#: pynicotine/gtkgui/search.py:615
msgid "Download _Folder(s)"
msgstr "Pobierz _folder(y)"

#: pynicotine/gtkgui/search.py:616
msgid "Download F_older(s) To…"
msgstr "Pobierz _folder(y) do…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
msgid "View User _Profile"
msgstr "Wyświetl profi_l użytkownika"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
msgid "_Browse Folder"
msgstr "_Przeglądaj folder"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
msgid "F_ile Properties"
msgstr "_Właściwości pliku"

#: pynicotine/gtkgui/search.py:629
msgid "Copy Search Term"
msgstr "Kopiuj wyszukiwane hasło"

#: pynicotine/gtkgui/search.py:631
msgid "Clear All Results"
msgstr "Wyczyść wszystkie wyniki"

#: pynicotine/gtkgui/search.py:718
msgid "Clear Filters"
msgstr "Wyczyść filtry"

#: pynicotine/gtkgui/search.py:721
msgid "Restore Filters"
msgstr "Przywróć filtry"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, python-format
msgid "[PRIVATE]  %s"
msgstr "[PRYWATNY] %s"

#: pynicotine/gtkgui/search.py:1273
#, python-format
msgid "_Result Filters [%d]"
msgstr "_Filtrowanie wyników [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
msgid "_Result Filters"
msgstr "_Filtrowanie wyników"

#: pynicotine/gtkgui/search.py:1277
#, python-format
msgid "%d active filter(s)"
msgstr "%d - liczba aktywnych filtrów"

#: pynicotine/gtkgui/search.py:1329
msgid "Add Wi_sh"
msgstr "Dodaj ży_czenie"

#: pynicotine/gtkgui/search.py:1332
msgid "Remove Wi_sh"
msgstr "Usuń życze_nie"

#: pynicotine/gtkgui/search.py:1349
msgid "Select User's Results"
msgstr "Wybierz wyniki użytkownika"

#: pynicotine/gtkgui/search.py:1472
#, python-format
msgid "Total: %s"
msgstr "Razem: %s"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
msgid "Results"
msgstr "Wyniki"

#: pynicotine/gtkgui/search.py:1590
msgid "Select Destination Folder for File(s)"
msgstr "Wybierz folder docelowy dla pliku(ów)"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
msgid "Select Destination Folder"
msgstr "Wybierz folder docelowy"

#: pynicotine/gtkgui/transfers.py:61
msgid "Queued"
msgstr "W kolejce"

#: pynicotine/gtkgui/transfers.py:62
msgid "Queued (prioritized)"
msgstr "W kolejce (z priorytetem)"

#: pynicotine/gtkgui/transfers.py:63
msgid "Queued (privileged)"
msgstr "W kolejce (uprzywilejowany)"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Pobieranie statusu"

#: pynicotine/gtkgui/transfers.py:65
msgid "Transferring"
msgstr "Transferowanie"

#: pynicotine/gtkgui/transfers.py:66
msgid "Connection closed"
msgstr "Połączenie zakończone"

#: pynicotine/gtkgui/transfers.py:67
msgid "Connection timeout"
msgstr "Limit czasu połączenia"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Użytkownik wylogowany"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
msgid "Cancelled"
msgstr "Anulowane"

#: pynicotine/gtkgui/transfers.py:73
msgid "Download folder error"
msgstr "Błąd pobierania folderu"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Błąd lokalnego pliku"

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr "Zablokowany"

#: pynicotine/gtkgui/transfers.py:76
msgid "File not shared"
msgstr "Plik nie jest udostępniany"

#: pynicotine/gtkgui/transfers.py:77
msgid "Pending shutdown"
msgstr "Oczekiwanie na wyłączenie"

#: pynicotine/gtkgui/transfers.py:78
msgid "File read error"
msgstr "Błąd odczytu pliku"

#: pynicotine/gtkgui/transfers.py:182
msgid "Queue"
msgstr "Kolejka"

#: pynicotine/gtkgui/transfers.py:188
msgid "Percent"
msgstr "Procent"

#: pynicotine/gtkgui/transfers.py:208
msgid "Time Elapsed"
msgstr "Upłynęło"

#: pynicotine/gtkgui/transfers.py:215
msgid "Time Left"
msgstr "Pozostało"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
msgid "_Open File"
msgstr "_Otwórz plik"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
msgid "Open in File _Manager"
msgstr "Otwórz w _menedżerze plików"

#: pynicotine/gtkgui/transfers.py:286
msgid "_Search"
msgstr "_Szukaj"

#: pynicotine/gtkgui/transfers.py:289
msgid "Clear All"
msgstr "Wyczyść wszystko"

#: pynicotine/gtkgui/transfers.py:953
msgid "Select User's Transfers"
msgstr "Wybierz transfery użytkownika"

#: pynicotine/gtkgui/uploads.py:50
msgid "_Abort"
msgstr "_Przerwij"

#: pynicotine/gtkgui/uploads.py:74
msgid "Finished / Cancelled / Failed"
msgstr "Zakończone / Anulowane / Błędne"

#: pynicotine/gtkgui/uploads.py:75
msgid "Finished / Cancelled"
msgstr "Zakończone / Anulowane"

#: pynicotine/gtkgui/uploads.py:79
msgid "Failed"
msgstr "Błędne"

#: pynicotine/gtkgui/uploads.py:80
msgid "User Logged Off"
msgstr "Użytkownik wylogowany"

#: pynicotine/gtkgui/uploads.py:142
#, python-format
msgid "Uploads: %(speed)s"
msgstr "Wysyłane: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr "Wychodzenie..."

#: pynicotine/gtkgui/uploads.py:164
msgid "Clear Queued Uploads"
msgstr "Wyczyść kolejkę wysyłania"

#: pynicotine/gtkgui/uploads.py:165
msgid "Do you really want to clear all queued uploads?"
msgstr "Jesteś pewien, że chcesz wyczyścić kolejkę wysyłania?"

#: pynicotine/gtkgui/uploads.py:177
msgid "Clear All Uploads"
msgstr "Wyczyść wszystkie wysłane"

#: pynicotine/gtkgui/uploads.py:178
msgid "Do you really want to clear all uploads?"
msgstr "Jesteś pewien, że chcesz wyczyścić wszystkie wysłane?"

#: pynicotine/gtkgui/userbrowse.py:282
msgid "_Save Shares List to Disk"
msgstr "_Zapisz listę zasobów na dysk"

#: pynicotine/gtkgui/userbrowse.py:292
msgid "Upload Folder & Subfolders…"
msgstr "Wyślij folder i podfolder(y)…"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
msgid "Copy _Folder Path"
msgstr "Kopiuj ścież_kę folderu"

#: pynicotine/gtkgui/userbrowse.py:309
msgid "_Download Folder & Subfolders"
msgstr "Pobierz folder i po_dfolder(y)"

#: pynicotine/gtkgui/userbrowse.py:310
msgid "Download Folder & Subfolders _To…"
msgstr "Pobierz folder i podfo_lder(y) do…"

#: pynicotine/gtkgui/userbrowse.py:334
msgid "File Name"
msgstr "Nazwa pliku"

#: pynicotine/gtkgui/userbrowse.py:373
msgid "Up_load File(s)…"
msgstr "_Wyślij plik(i)…"

#: pynicotine/gtkgui/userbrowse.py:374
msgid "Upload Folder…"
msgstr "Wyślij folder…"

#: pynicotine/gtkgui/userbrowse.py:396
msgid "Download Folder _To…"
msgstr "Pobierz _folder do…"

#: pynicotine/gtkgui/userbrowse.py:600
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"Lista zasobów użytkownika jest pusta. Możliwe, że użytkownik niczego nie "
"udostępnia lub robi to prywatnie."

#: pynicotine/gtkgui/userbrowse.py:615
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Nie można zażądać listy zasobów użytkownika. Możliwe, że użytkownik jest "
"rozłączony, obaj macie zamknięte porty albo jest tymczasowy problem z "
"połączeniem."

#: pynicotine/gtkgui/userbrowse.py:953
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Wybierz miejsce docelowe dla pobierania wielu folderów"

#: pynicotine/gtkgui/userbrowse.py:997
msgid "Upload Folder (with Subfolders) To User"
msgstr "Wyślij folder (z podfolderami) do użytkownika"

#: pynicotine/gtkgui/userbrowse.py:999
msgid "Upload Folder To User"
msgstr "Wyślij folder do użytkownika"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
msgid "Enter the name of the user you want to upload to:"
msgstr "Wprowadź użytkownika, któremu chcesz wysłać:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
msgid "_Upload"
msgstr "_Wyślij"

#: pynicotine/gtkgui/userbrowse.py:1139
msgid "Select Destination Folder for Files"
msgstr "Wybierz folder docelowy dla pliku(ów)"

#: pynicotine/gtkgui/userbrowse.py:1161
msgid "Upload File(s) To User"
msgstr "Wyślij plik(i) do użytkownika"

#: pynicotine/gtkgui/userinfo.py:376
msgid "Copy Picture"
msgstr "Kopiuj zdjęcie"

#: pynicotine/gtkgui/userinfo.py:377
msgid "Save Picture"
msgstr "Zapisz zdjęcie"

#: pynicotine/gtkgui/userinfo.py:468
#, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Nie udało się załadować obrazu dla użytkownika %(user)s: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Nie można zażądać listy zasobów użytkownika. Możliwe, że użytkownik jest "
"rozłączony, obaj macie zamknięte porty albo jest tymczasowy problem z "
"połączeniem."

#: pynicotine/gtkgui/userinfo.py:577
msgid "Remove _Buddy"
msgstr "Usuń _znajomego"

#: pynicotine/gtkgui/userinfo.py:577
msgid "Add _Buddy"
msgstr "Dodaj _znajomego"

#: pynicotine/gtkgui/userinfo.py:581
msgid "Unban User"
msgstr "Odblokuj użytkownika"

#: pynicotine/gtkgui/userinfo.py:585
msgid "Unignore User"
msgstr "Nie ignoruj użytkownika"

#: pynicotine/gtkgui/userinfo.py:613
msgid "Yes"
msgstr "Tak"

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr "Nie"

#: pynicotine/gtkgui/userinfo.py:773
msgid "Please enter number of days."
msgstr "Proszę podać liczbę dni."

#: pynicotine/gtkgui/userinfo.py:787
#, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Podaruj dni swoich przywilejów Soulseek użytkownikowi %(user)s "
"(%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, python-format
msgid "%(days)s days left"
msgstr "%(days)s pozostało"

#: pynicotine/gtkgui/userinfo.py:795
msgid "Gift Privileges"
msgstr "Przywileje prezentowe"

#: pynicotine/gtkgui/userinfo.py:797
msgid "_Give Privileges"
msgstr "_Udziel pozwolenia"

#: pynicotine/gtkgui/widgets/dialogs.py:309
msgid "Close"
msgstr "Zamknij"

#: pynicotine/gtkgui/widgets/dialogs.py:488
msgid "_Yes"
msgstr "_Tak"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
msgid "_OK"
msgstr "_OK"

#: pynicotine/gtkgui/widgets/filechooser.py:39
msgid "Select a File"
msgstr "Wybierz plik"

#: pynicotine/gtkgui/widgets/filechooser.py:174
msgid "Select a Folder"
msgstr "Wybierz folder"

#: pynicotine/gtkgui/widgets/filechooser.py:179
msgid "_Select"
msgstr "_Wybierz"

#: pynicotine/gtkgui/widgets/filechooser.py:196
msgid "Select an Image"
msgstr "Wybierz obraz"

#: pynicotine/gtkgui/widgets/filechooser.py:203
msgid "All images"
msgstr "Wszystkie obrazy"

#: pynicotine/gtkgui/widgets/filechooser.py:241
msgid "Save as…"
msgstr "Zapisz jako…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
msgid "(None)"
msgstr "(Brak)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
msgid "Close Tab"
msgstr "Zamknij kartę"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
msgid "Close All Tabs?"
msgstr "Zamknąć wszystkie karty?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
msgid "Do you really want to close all tabs?"
msgstr "Jesteś pewien, że chcesz zamknąć wszystkie karty?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, python-format
msgid "%i Unread Tab(s)"
msgstr "%i nieprzeczytanych kart"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
msgid "All Tabs"
msgstr "Wszystkie karty"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
msgid "Re_open Closed Tab"
msgstr "Ot_wórz ponownie zamkniętą kartę"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, python-format
msgid "%s File(s) Selected"
msgstr "%s wybrany(ch) plik(ów)"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
msgid "_Browse Files"
msgstr "_Przeglądaj pliki"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
msgid "_Add Buddy"
msgstr "_Dodaj znajomego"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
msgid "Show IP A_ddress"
msgstr "Pokaż a_dres IP"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
msgid "Private Rooms"
msgstr "Prywatne pokoje"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, python-format
msgid "Remove from Private Room %s"
msgstr "Usuń z prywatnego pokoju %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, python-format
msgid "Add to Private Room %s"
msgstr "Dodaj do prywatnego pokoju %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, python-format
msgid "Remove as Operator of %s"
msgstr "Usuń jako operatora %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, python-format
msgid "Add as Operator of %s"
msgstr "Dodaj jako operatora %s"

#: pynicotine/gtkgui/widgets/textentry.py:37
msgid "Send message…"
msgstr "Wyślij wiadomość…"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
msgid "Find Previous Match"
msgstr "Znajdź poprzednie wystąpienie"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
msgid "Find Next Match"
msgstr "Znajdź następne wystąpienie"

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr "--- stare wiadomości powyżej ---"

#: pynicotine/gtkgui/widgets/theme.py:243
msgid "Executable"
msgstr "Wykonywalny"

#: pynicotine/gtkgui/widgets/theme.py:244
msgid "Audio"
msgstr "Audio"

#: pynicotine/gtkgui/widgets/theme.py:245
msgid "Image"
msgstr "Obraz"

#: pynicotine/gtkgui/widgets/theme.py:246
msgid "Archive"
msgstr "Archiwum"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
msgid "Miscellaneous"
msgstr "Różne"

#: pynicotine/gtkgui/widgets/theme.py:248
msgid "Video"
msgstr "Wideo"

#: pynicotine/gtkgui/widgets/theme.py:249
msgid "Document"
msgstr "Dokument"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr "Tekst"

#: pynicotine/gtkgui/widgets/theme.py:357
#, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "Błąd ładowania niestandardowej ikony %(path)s: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Hide Nicotine+"
msgstr "Ukryj Nicotine+"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Show Nicotine+"
msgstr "Pokaż Nicotine+"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, python-format
msgid "Column #%i"
msgstr "Kolumna #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
msgid "Ungrouped"
msgstr "Niepogrupowane"

#: pynicotine/gtkgui/widgets/treeview.py:960
msgid "Group by Folder"
msgstr "Grupuj folderami"

#: pynicotine/gtkgui/widgets/treeview.py:963
msgid "Group by User"
msgstr "Grupuj użytkownikami"

#: pynicotine/headless/application.py:77
#, python-format
msgid "Do you really want to exit? %s"
msgstr "Czy na pewno chcesz wyjść? %s"

#: pynicotine/headless/application.py:81
#, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr "Użytkownik %s już istnieje, a wprowadzone hasło jest nieprawidłowe."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr ""
"Wpisz %s, aby zalogować się przy użyciu innej nazwy użytkownika lub hasła."

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
msgid "Password: "
msgstr "Hasło: "

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"W celu utworzenia nowego użytkownika, podaj jego nazwę i hasło. Jeśli już "
"masz konto Soulseek, wypełnij poniższe dane."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr "Następujące zasoby nie są dostępne:"

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr "Ponowić próbę skanowania? %s"

#: pynicotine/logfacility.py:181
#, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr "Nie można zapisać do pliku dziennika \"%(filename)s\": %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Nie można uzyskać dostępu do pliku dziennika %(path)s: %(error)s"

#: pynicotine/networkfilter.py:40
msgid "Andorra"
msgstr "Andora"

#: pynicotine/networkfilter.py:41
msgid "United Arab Emirates"
msgstr "Zjednoczone Emiraty Arabskie"

#: pynicotine/networkfilter.py:42
msgid "Afghanistan"
msgstr "Afganistan"

#: pynicotine/networkfilter.py:43
msgid "Antigua & Barbuda"
msgstr "Antigua i Barbuda"

#: pynicotine/networkfilter.py:44
msgid "Anguilla"
msgstr "Anguilla"

#: pynicotine/networkfilter.py:45
msgid "Albania"
msgstr "Albania"

#: pynicotine/networkfilter.py:46
msgid "Armenia"
msgstr "Armenia"

#: pynicotine/networkfilter.py:47
msgid "Angola"
msgstr "Angola"

#: pynicotine/networkfilter.py:48
msgid "Antarctica"
msgstr "Antarktyda"

#: pynicotine/networkfilter.py:49
msgid "Argentina"
msgstr "Argentyna"

#: pynicotine/networkfilter.py:50
msgid "American Samoa"
msgstr "Samoa Amerykańskie"

#: pynicotine/networkfilter.py:51
msgid "Austria"
msgstr "Austria"

#: pynicotine/networkfilter.py:52
msgid "Australia"
msgstr "Australia"

#: pynicotine/networkfilter.py:53
msgid "Aruba"
msgstr "Aruba"

#: pynicotine/networkfilter.py:54
msgid "Åland Islands"
msgstr "Wyspy Alandzkie"

#: pynicotine/networkfilter.py:55
msgid "Azerbaijan"
msgstr "Azerbejdżan"

#: pynicotine/networkfilter.py:56
msgid "Bosnia & Herzegovina"
msgstr "Bośnia i Hercegowina"

#: pynicotine/networkfilter.py:57
msgid "Barbados"
msgstr "Barbados"

#: pynicotine/networkfilter.py:58
msgid "Bangladesh"
msgstr "Bangladesz"

#: pynicotine/networkfilter.py:59
msgid "Belgium"
msgstr "Belgia"

#: pynicotine/networkfilter.py:60
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: pynicotine/networkfilter.py:61
msgid "Bulgaria"
msgstr "Bułgaria"

#: pynicotine/networkfilter.py:62
msgid "Bahrain"
msgstr "Bahrajn"

#: pynicotine/networkfilter.py:63
msgid "Burundi"
msgstr "Burundi"

#: pynicotine/networkfilter.py:64
msgid "Benin"
msgstr "Benin"

#: pynicotine/networkfilter.py:65
msgid "Saint Barthelemy"
msgstr "Św Barthelemy"

#: pynicotine/networkfilter.py:66
msgid "Bermuda"
msgstr "Bermudy"

#: pynicotine/networkfilter.py:67
msgid "Brunei Darussalam"
msgstr "Brunei Darussalam"

#: pynicotine/networkfilter.py:68
msgid "Bolivia"
msgstr "Boliwia"

#: pynicotine/networkfilter.py:69
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius i Saba"

#: pynicotine/networkfilter.py:70
msgid "Brazil"
msgstr "Brazylia"

#: pynicotine/networkfilter.py:71
msgid "Bahamas"
msgstr "Bahamy"

#: pynicotine/networkfilter.py:72
msgid "Bhutan"
msgstr "Bhutan"

#: pynicotine/networkfilter.py:73
msgid "Bouvet Island"
msgstr "Wyspa Bouveta"

#: pynicotine/networkfilter.py:74
msgid "Botswana"
msgstr "Botswana"

#: pynicotine/networkfilter.py:75
msgid "Belarus"
msgstr "Białoruś"

#: pynicotine/networkfilter.py:76
msgid "Belize"
msgstr "Belize"

#: pynicotine/networkfilter.py:77
msgid "Canada"
msgstr "Kanada"

#: pynicotine/networkfilter.py:78
msgid "Cocos (Keeling) Islands"
msgstr "Wyspy Kokosowe (Keelinga)"

#: pynicotine/networkfilter.py:79
msgid "Democratic Republic of Congo"
msgstr "Demokratyczna Republika Konga"

#: pynicotine/networkfilter.py:80
msgid "Central African Republic"
msgstr "Republika Środkowoafrykańska"

#: pynicotine/networkfilter.py:81
msgid "Congo"
msgstr "Kongo"

#: pynicotine/networkfilter.py:82
msgid "Switzerland"
msgstr "Szwajcaria"

#: pynicotine/networkfilter.py:83
msgid "Ivory Coast"
msgstr "Wybrzeże Kości Słoniowej"

#: pynicotine/networkfilter.py:84
msgid "Cook Islands"
msgstr "Wyspy Cooka"

#: pynicotine/networkfilter.py:85
msgid "Chile"
msgstr "Chile"

#: pynicotine/networkfilter.py:86
msgid "Cameroon"
msgstr "Kamerun"

#: pynicotine/networkfilter.py:87
msgid "China"
msgstr "Chiny"

#: pynicotine/networkfilter.py:88
msgid "Colombia"
msgstr "Kolumbia"

#: pynicotine/networkfilter.py:89
msgid "Costa Rica"
msgstr "Kostaryka"

#: pynicotine/networkfilter.py:90
msgid "Cuba"
msgstr "Kuba"

#: pynicotine/networkfilter.py:91
msgid "Cabo Verde"
msgstr "Cabo Verde"

#: pynicotine/networkfilter.py:92
msgid "Curaçao"
msgstr "Curaçao"

#: pynicotine/networkfilter.py:93
msgid "Christmas Island"
msgstr "Wyspa Bożego Narodzenia"

#: pynicotine/networkfilter.py:94
msgid "Cyprus"
msgstr "Cypr"

#: pynicotine/networkfilter.py:95
msgid "Czechia"
msgstr "Czechy"

#: pynicotine/networkfilter.py:96
msgid "Germany"
msgstr "Niemcy"

#: pynicotine/networkfilter.py:97
msgid "Djibouti"
msgstr "Dżibuti"

#: pynicotine/networkfilter.py:98
msgid "Denmark"
msgstr "Dania"

#: pynicotine/networkfilter.py:99
msgid "Dominica"
msgstr "Dominika"

#: pynicotine/networkfilter.py:100
msgid "Dominican Republic"
msgstr "Dominikana"

#: pynicotine/networkfilter.py:101
msgid "Algeria"
msgstr "Algieria"

#: pynicotine/networkfilter.py:102
msgid "Ecuador"
msgstr "Ekwador"

#: pynicotine/networkfilter.py:103
msgid "Estonia"
msgstr "Estonia"

#: pynicotine/networkfilter.py:104
msgid "Egypt"
msgstr "Egipt"

#: pynicotine/networkfilter.py:105
msgid "Western Sahara"
msgstr "Sahara Zachodnia"

#: pynicotine/networkfilter.py:106
msgid "Eritrea"
msgstr "Erytrea"

#: pynicotine/networkfilter.py:107
msgid "Spain"
msgstr "Hiszpania"

#: pynicotine/networkfilter.py:108
msgid "Ethiopia"
msgstr "Etiopia"

#: pynicotine/networkfilter.py:109
msgid "Europe"
msgstr "Europa"

#: pynicotine/networkfilter.py:110
msgid "Finland"
msgstr "Finlandia"

#: pynicotine/networkfilter.py:111
msgid "Fiji"
msgstr "Fidżi"

#: pynicotine/networkfilter.py:112
msgid "Falkland Islands (Malvinas)"
msgstr "Wyspy Falklandzkie (Malwiny)"

#: pynicotine/networkfilter.py:113
msgid "Micronesia"
msgstr "Mikronezja"

#: pynicotine/networkfilter.py:114
msgid "Faroe Islands"
msgstr "Wyspy Owcze"

#: pynicotine/networkfilter.py:115
msgid "France"
msgstr "Francja"

#: pynicotine/networkfilter.py:116
msgid "Gabon"
msgstr "Gabon"

#: pynicotine/networkfilter.py:117
msgid "Great Britain"
msgstr "Wielka Brytania"

#: pynicotine/networkfilter.py:118
msgid "Grenada"
msgstr "Grenada"

#: pynicotine/networkfilter.py:119
msgid "Georgia"
msgstr "Gruzja"

#: pynicotine/networkfilter.py:120
msgid "French Guiana"
msgstr "Gujana Francuska"

#: pynicotine/networkfilter.py:121
msgid "Guernsey"
msgstr "Guernsey"

#: pynicotine/networkfilter.py:122
msgid "Ghana"
msgstr "Ghana"

#: pynicotine/networkfilter.py:123
msgid "Gibraltar"
msgstr "Gibraltar"

#: pynicotine/networkfilter.py:124
msgid "Greenland"
msgstr "Grenlandia"

#: pynicotine/networkfilter.py:125
msgid "Gambia"
msgstr "Gambia"

#: pynicotine/networkfilter.py:126
msgid "Guinea"
msgstr "Gwinea"

#: pynicotine/networkfilter.py:127
msgid "Guadeloupe"
msgstr "Gwadelupa"

#: pynicotine/networkfilter.py:128
msgid "Equatorial Guinea"
msgstr "Gwinea Równikowa"

#: pynicotine/networkfilter.py:129
msgid "Greece"
msgstr "Grecja"

#: pynicotine/networkfilter.py:130
msgid "South Georgia & South Sandwich Islands"
msgstr "Południowa Georgia i Południowe Wyspy Sandwicha"

#: pynicotine/networkfilter.py:131
msgid "Guatemala"
msgstr "Gwatemala"

#: pynicotine/networkfilter.py:132
msgid "Guam"
msgstr "Guam"

#: pynicotine/networkfilter.py:133
msgid "Guinea-Bissau"
msgstr "Gwinea Bissau"

#: pynicotine/networkfilter.py:134
msgid "Guyana"
msgstr "Gujana"

#: pynicotine/networkfilter.py:135
msgid "Hong Kong"
msgstr "Hongkong"

#: pynicotine/networkfilter.py:136
msgid "Heard & McDonald Islands"
msgstr "Wyspy Heard i McDonalda"

#: pynicotine/networkfilter.py:137
msgid "Honduras"
msgstr "Honduras"

#: pynicotine/networkfilter.py:138
msgid "Croatia"
msgstr "Chorwacja"

#: pynicotine/networkfilter.py:139
msgid "Haiti"
msgstr "Haiti"

#: pynicotine/networkfilter.py:140
msgid "Hungary"
msgstr "Węgry"

#: pynicotine/networkfilter.py:141
msgid "Indonesia"
msgstr "Indonezja"

#: pynicotine/networkfilter.py:142
msgid "Ireland"
msgstr "Irlandia"

#: pynicotine/networkfilter.py:143
msgid "Israel"
msgstr "Izrael"

#: pynicotine/networkfilter.py:144
msgid "Isle of Man"
msgstr "Wyspa Man"

#: pynicotine/networkfilter.py:145
msgid "India"
msgstr "Indie"

#: pynicotine/networkfilter.py:146
msgid "British Indian Ocean Territory"
msgstr "Brytyjskie Terytorium Oceanu Indyjskiego"

#: pynicotine/networkfilter.py:147
msgid "Iraq"
msgstr "Irak"

#: pynicotine/networkfilter.py:148
msgid "Iran"
msgstr "Iran"

#: pynicotine/networkfilter.py:149
msgid "Iceland"
msgstr "Islandia"

#: pynicotine/networkfilter.py:150
msgid "Italy"
msgstr "Włochy"

#: pynicotine/networkfilter.py:151
msgid "Jersey"
msgstr "Jersey"

#: pynicotine/networkfilter.py:152
msgid "Jamaica"
msgstr "Jamajka"

#: pynicotine/networkfilter.py:153
msgid "Jordan"
msgstr "Jordania"

#: pynicotine/networkfilter.py:154
msgid "Japan"
msgstr "Japonia"

#: pynicotine/networkfilter.py:155
msgid "Kenya"
msgstr "Kenia"

#: pynicotine/networkfilter.py:156
msgid "Kyrgyzstan"
msgstr "Kirgistan"

#: pynicotine/networkfilter.py:157
msgid "Cambodia"
msgstr "Kambodża"

#: pynicotine/networkfilter.py:158
msgid "Kiribati"
msgstr "Kiribati"

#: pynicotine/networkfilter.py:159
msgid "Comoros"
msgstr "Komory"

#: pynicotine/networkfilter.py:160
msgid "Saint Kitts & Nevis"
msgstr "Saint Kitts i Nevis"

#: pynicotine/networkfilter.py:161
msgid "North Korea"
msgstr "Korea Północna"

#: pynicotine/networkfilter.py:162
msgid "South Korea"
msgstr "Korea Południowa"

#: pynicotine/networkfilter.py:163
msgid "Kuwait"
msgstr "Kuwejt"

#: pynicotine/networkfilter.py:164
msgid "Cayman Islands"
msgstr "Kajmany"

#: pynicotine/networkfilter.py:165
msgid "Kazakhstan"
msgstr "Kazachstan"

#: pynicotine/networkfilter.py:166
msgid "Laos"
msgstr "Laos"

#: pynicotine/networkfilter.py:167
msgid "Lebanon"
msgstr "Liban"

#: pynicotine/networkfilter.py:168
msgid "Saint Lucia"
msgstr "Saint Lucia"

#: pynicotine/networkfilter.py:169
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: pynicotine/networkfilter.py:170
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: pynicotine/networkfilter.py:171
msgid "Liberia"
msgstr "Liberia"

#: pynicotine/networkfilter.py:172
msgid "Lesotho"
msgstr "Lesoto"

#: pynicotine/networkfilter.py:173
msgid "Lithuania"
msgstr "Litwa"

#: pynicotine/networkfilter.py:174
msgid "Luxembourg"
msgstr "Luksemburg"

#: pynicotine/networkfilter.py:175
msgid "Latvia"
msgstr "Łotwa"

#: pynicotine/networkfilter.py:176
msgid "Libya"
msgstr "Libia"

#: pynicotine/networkfilter.py:177
msgid "Morocco"
msgstr "Maroko"

#: pynicotine/networkfilter.py:178
msgid "Monaco"
msgstr "Monako"

#: pynicotine/networkfilter.py:179
msgid "Moldova"
msgstr "Mołdawia"

#: pynicotine/networkfilter.py:180
msgid "Montenegro"
msgstr "Czarnogóra"

#: pynicotine/networkfilter.py:181
msgid "Saint Martin"
msgstr "Saint Martin"

#: pynicotine/networkfilter.py:182
msgid "Madagascar"
msgstr "Madagaskar"

#: pynicotine/networkfilter.py:183
msgid "Marshall Islands"
msgstr "Wyspy Marshalla"

#: pynicotine/networkfilter.py:184
msgid "North Macedonia"
msgstr "Macedonia Północna"

#: pynicotine/networkfilter.py:185
msgid "Mali"
msgstr "Mali"

#: pynicotine/networkfilter.py:186
msgid "Myanmar"
msgstr "Myanmar"

#: pynicotine/networkfilter.py:187
msgid "Mongolia"
msgstr "Mongolia"

#: pynicotine/networkfilter.py:188
msgid "Macau"
msgstr "Makau"

#: pynicotine/networkfilter.py:189
msgid "Northern Mariana Islands"
msgstr "Mariany Północne"

#: pynicotine/networkfilter.py:190
msgid "Martinique"
msgstr "Martynika"

#: pynicotine/networkfilter.py:191
msgid "Mauritania"
msgstr "Mauretania"

#: pynicotine/networkfilter.py:192
msgid "Montserrat"
msgstr "Montserrat"

#: pynicotine/networkfilter.py:193
msgid "Malta"
msgstr "Malta"

#: pynicotine/networkfilter.py:194
msgid "Mauritius"
msgstr "Mauritius"

#: pynicotine/networkfilter.py:195
msgid "Maldives"
msgstr "Malediwy"

#: pynicotine/networkfilter.py:196
msgid "Malawi"
msgstr "Malawi"

#: pynicotine/networkfilter.py:197
msgid "Mexico"
msgstr "Meksyk"

#: pynicotine/networkfilter.py:198
msgid "Malaysia"
msgstr "Malezja"

#: pynicotine/networkfilter.py:199
msgid "Mozambique"
msgstr "Mozambik"

#: pynicotine/networkfilter.py:200
msgid "Namibia"
msgstr "Namibia"

#: pynicotine/networkfilter.py:201
msgid "New Caledonia"
msgstr "Nowa Kaledonia"

#: pynicotine/networkfilter.py:202
msgid "Niger"
msgstr "Niger"

#: pynicotine/networkfilter.py:203
msgid "Norfolk Island"
msgstr "Wyspa Norfolk"

#: pynicotine/networkfilter.py:204
msgid "Nigeria"
msgstr "Nigeria"

#: pynicotine/networkfilter.py:205
msgid "Nicaragua"
msgstr "Nikaragua"

#: pynicotine/networkfilter.py:206
msgid "Netherlands"
msgstr "Niderlandy"

#: pynicotine/networkfilter.py:207
msgid "Norway"
msgstr "Norwegia"

#: pynicotine/networkfilter.py:208
msgid "Nepal"
msgstr "Nepal"

#: pynicotine/networkfilter.py:209
msgid "Nauru"
msgstr "Nauru"

#: pynicotine/networkfilter.py:210
msgid "Niue"
msgstr "Niue"

#: pynicotine/networkfilter.py:211
msgid "New Zealand"
msgstr "Nowa Zelandia"

#: pynicotine/networkfilter.py:212
msgid "Oman"
msgstr "Oman"

#: pynicotine/networkfilter.py:213
msgid "Panama"
msgstr "Panama"

#: pynicotine/networkfilter.py:214
msgid "Peru"
msgstr "Peru"

#: pynicotine/networkfilter.py:215
msgid "French Polynesia"
msgstr "Polinezja Francuska"

#: pynicotine/networkfilter.py:216
msgid "Papua New Guinea"
msgstr "Papua Nowa Gwinea"

#: pynicotine/networkfilter.py:217
msgid "Philippines"
msgstr "Filipiny"

#: pynicotine/networkfilter.py:218
msgid "Pakistan"
msgstr "Pakistan"

#: pynicotine/networkfilter.py:219
msgid "Poland"
msgstr "Polska"

#: pynicotine/networkfilter.py:220
msgid "Saint Pierre & Miquelon"
msgstr "Saint Pierre i Miquelon"

#: pynicotine/networkfilter.py:221
msgid "Pitcairn"
msgstr "Pitcairn"

#: pynicotine/networkfilter.py:222
msgid "Puerto Rico"
msgstr "Portoryko"

#: pynicotine/networkfilter.py:223
msgid "State of Palestine"
msgstr "Państwo Palestyna"

#: pynicotine/networkfilter.py:224
msgid "Portugal"
msgstr "Portugalia"

#: pynicotine/networkfilter.py:225
msgid "Palau"
msgstr "Palau"

#: pynicotine/networkfilter.py:226
msgid "Paraguay"
msgstr "Paragwaj"

#: pynicotine/networkfilter.py:227
msgid "Qatar"
msgstr "Katar"

#: pynicotine/networkfilter.py:228
msgid "Réunion"
msgstr "Réunion"

#: pynicotine/networkfilter.py:229
msgid "Romania"
msgstr "Rumunia"

#: pynicotine/networkfilter.py:230
msgid "Serbia"
msgstr "Serbia"

#: pynicotine/networkfilter.py:231
msgid "Russia"
msgstr "Rosja"

#: pynicotine/networkfilter.py:232
msgid "Rwanda"
msgstr "Rwanda"

#: pynicotine/networkfilter.py:233
msgid "Saudi Arabia"
msgstr "Arabia Saudyjska"

#: pynicotine/networkfilter.py:234
msgid "Solomon Islands"
msgstr "Wyspy Salomona"

#: pynicotine/networkfilter.py:235
msgid "Seychelles"
msgstr "Seszele"

#: pynicotine/networkfilter.py:236
msgid "Sudan"
msgstr "Sudan"

#: pynicotine/networkfilter.py:237
msgid "Sweden"
msgstr "Szwecja"

#: pynicotine/networkfilter.py:238
msgid "Singapore"
msgstr "Singapur"

#: pynicotine/networkfilter.py:239
msgid "Saint Helena"
msgstr "Święta Helena"

#: pynicotine/networkfilter.py:240
msgid "Slovenia"
msgstr "Słowenia"

#: pynicotine/networkfilter.py:241
msgid "Svalbard & Jan Mayen Islands"
msgstr "Wyspy Svalbard i Jan Mayen"

#: pynicotine/networkfilter.py:242
msgid "Slovak Republic"
msgstr "Słowacja"

#: pynicotine/networkfilter.py:243
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: pynicotine/networkfilter.py:244
msgid "San Marino"
msgstr "San Marino"

#: pynicotine/networkfilter.py:245
msgid "Senegal"
msgstr "Senegal"

#: pynicotine/networkfilter.py:246
msgid "Somalia"
msgstr "Somalia"

#: pynicotine/networkfilter.py:247
msgid "Suriname"
msgstr "Surinam"

#: pynicotine/networkfilter.py:248
msgid "South Sudan"
msgstr "Sudan Południowy"

#: pynicotine/networkfilter.py:249
msgid "Sao Tome & Principe"
msgstr "Wyspy Świętego Tomasza i Książęca"

#: pynicotine/networkfilter.py:250
msgid "El Salvador"
msgstr "Salwador"

#: pynicotine/networkfilter.py:251
msgid "Sint Maarten"
msgstr "Sint Maarten"

#: pynicotine/networkfilter.py:252
msgid "Syria"
msgstr "Syria"

#: pynicotine/networkfilter.py:253
msgid "Eswatini"
msgstr "Eswatini"

#: pynicotine/networkfilter.py:254
msgid "Turks & Caicos Islands"
msgstr "Wyspy Turks i Caicos"

#: pynicotine/networkfilter.py:255
msgid "Chad"
msgstr "Czad"

#: pynicotine/networkfilter.py:256
msgid "French Southern Territories"
msgstr "Francuskie Terytoria Południowe"

#: pynicotine/networkfilter.py:257
msgid "Togo"
msgstr "Togo"

#: pynicotine/networkfilter.py:258
msgid "Thailand"
msgstr "Tajlandia"

#: pynicotine/networkfilter.py:259
msgid "Tajikistan"
msgstr "Tadżykistan"

#: pynicotine/networkfilter.py:260
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
msgid "Timor-Leste"
msgstr "Timor Wschodni"

#: pynicotine/networkfilter.py:262
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: pynicotine/networkfilter.py:263
msgid "Tunisia"
msgstr "Tunezja"

#: pynicotine/networkfilter.py:264
msgid "Tonga"
msgstr "Tonga"

#: pynicotine/networkfilter.py:265
msgid "Türkiye"
msgstr "Turcja"

#: pynicotine/networkfilter.py:266
msgid "Trinidad & Tobago"
msgstr "Trynidad i Tobago"

#: pynicotine/networkfilter.py:267
msgid "Tuvalu"
msgstr "Tuvalu"

#: pynicotine/networkfilter.py:268
msgid "Taiwan"
msgstr "Tajwan"

#: pynicotine/networkfilter.py:269
msgid "Tanzania"
msgstr "Tanzania"

#: pynicotine/networkfilter.py:270
msgid "Ukraine"
msgstr "Ukraina"

#: pynicotine/networkfilter.py:271
msgid "Uganda"
msgstr "Uganda"

#: pynicotine/networkfilter.py:272
msgid "U.S. Minor Outlying Islands"
msgstr "Dalekie Wyspy Mniejsze Stanów Zjednoczonych"

#: pynicotine/networkfilter.py:273
msgid "United States"
msgstr "Stany Zjednoczone"

#: pynicotine/networkfilter.py:274
msgid "Uruguay"
msgstr "Urugwaj"

#: pynicotine/networkfilter.py:275
msgid "Uzbekistan"
msgstr "Uzbekistan"

#: pynicotine/networkfilter.py:276
msgid "Holy See (Vatican City State)"
msgstr "Stolica Apostolska (Państwo Watykańskie)"

#: pynicotine/networkfilter.py:277
msgid "Saint Vincent & The Grenadines"
msgstr "Saint Vincent i Grenadyny"

#: pynicotine/networkfilter.py:278
msgid "Venezuela"
msgstr "Wenezuela"

#: pynicotine/networkfilter.py:279
msgid "British Virgin Islands"
msgstr "Brytyjskie Wyspy Dziewicze"

#: pynicotine/networkfilter.py:280
msgid "U.S. Virgin Islands"
msgstr "Wyspy Dziewicze Stanów Zjednoczonych"

#: pynicotine/networkfilter.py:281
msgid "Viet Nam"
msgstr "Wietnam"

#: pynicotine/networkfilter.py:282
msgid "Vanuatu"
msgstr "Vanuatu"

#: pynicotine/networkfilter.py:283
msgid "Wallis & Futuna"
msgstr "Wallis i Futuna"

#: pynicotine/networkfilter.py:284
msgid "Samoa"
msgstr "Samoa"

#: pynicotine/networkfilter.py:285
msgid "Yemen"
msgstr "Jemen"

#: pynicotine/networkfilter.py:286
msgid "Mayotte"
msgstr "Majotta"

#: pynicotine/networkfilter.py:287
msgid "South Africa"
msgstr "Republika Południowej Afryki"

#: pynicotine/networkfilter.py:288
msgid "Zambia"
msgstr "Zambia"

#: pynicotine/networkfilter.py:289
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "Błędna zamiana tekstu na mowę dla wiadomości: %s"

#: pynicotine/nowplaying.py:130
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr "Last.fm: Proszę podać nazwę użytkownika i klucz API"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
msgid "Now Playing Error"
msgstr "Błąd „Teraz odtwarzanie”"

#: pynicotine/nowplaying.py:140
#, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm: Nie można się połączyć z Audioscroblerem: %(error)s"

#: pynicotine/nowplaying.py:162
#, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr ""
"Last.fm: Nie można pobrać ostatnio odtwarzanego utworu z Audioscrobblera: "
"%(error)s"

#: pynicotine/nowplaying.py:196
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: Nie znaleziono odpowiedniego odtwarzacza MPRIS"

#: pynicotine/nowplaying.py:201
#, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr "Znaleziono kilka odtwarzaczy MPRIS: %(players)s. Używanie: %(player)s"

#: pynicotine/nowplaying.py:204
#, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Automatyczne wybieranie odtwarzacza MPRIS: %s"

#: pynicotine/nowplaying.py:219
#, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr ""
"MPRIS. Coś poszło nie tak podczas odpytywania %(player)s: %(exception)s"

#: pynicotine/nowplaying.py:266
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "ListenBrainz: Podaj nazwę użytkownika"

#: pynicotine/nowplaying.py:275
#, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "ListenBrainz: Nie można się połączyć z ListenBrainz: %(error)s"

#: pynicotine/nowplaying.py:283
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "ListenBrainz: Wygląda na to, że w tej chwili niczego nie słuchasz"

#: pynicotine/nowplaying.py:297
#, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz: Nie można pobrać aktualnie odtwarzanego utworu z ListenBrainz: "
"%(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
msgid "Network Filters"
msgstr "Filtry sieciowe"

#: pynicotine/plugins/core_commands/__init__.py:44
msgid "List available commands"
msgstr "Lista dostępnych komend"

#: pynicotine/plugins/core_commands/__init__.py:49
msgid "Connect to the server"
msgstr "Połącz z serwerem"

#: pynicotine/plugins/core_commands/__init__.py:53
msgid "Disconnect from the server"
msgstr "Rozłącz od serwera"

#: pynicotine/plugins/core_commands/__init__.py:58
msgid "Toggle away status"
msgstr "Przełącza status „Zaraz wracam”"

#: pynicotine/plugins/core_commands/__init__.py:62
msgid "Manage plugins"
msgstr "Zarządzaj wtyczkami"

#: pynicotine/plugins/core_commands/__init__.py:74
msgid "Clear chat window"
msgstr "Wyczyść okno czatu"

#: pynicotine/plugins/core_commands/__init__.py:80
msgid "Say something in the third-person"
msgstr "Powiedz coś w trzeciej osobie"

#: pynicotine/plugins/core_commands/__init__.py:87
msgid "Announce the song currently playing"
msgstr "Ogłoś aktualnie odtwarzany utwór"

#: pynicotine/plugins/core_commands/__init__.py:94
msgid "Join chat room"
msgstr "Dołącz do czatu"

#: pynicotine/plugins/core_commands/__init__.py:102
msgid "Leave chat room"
msgstr "Opuść ten pokój"

#: pynicotine/plugins/core_commands/__init__.py:110
msgid "Say message in specified chat room"
msgstr "Powiedz wiadomość w określonym pokoju rozmów"

#: pynicotine/plugins/core_commands/__init__.py:117
msgid "Open private chat"
msgstr "Otwórz czat prywatny"

#: pynicotine/plugins/core_commands/__init__.py:125
msgid "Close private chat"
msgstr "Zamknij czat prywatny"

#: pynicotine/plugins/core_commands/__init__.py:133
msgid "Request user's client version"
msgstr "Zapytaj o wersję klienta użytkownika"

#: pynicotine/plugins/core_commands/__init__.py:142
msgid "Send private message to user"
msgstr "Wyślij prywatną wiadomość do użytkownika"

#: pynicotine/plugins/core_commands/__init__.py:150
msgid "Add user to buddy list"
msgstr "Dodaj użytkownika do listy znajomych"

#: pynicotine/plugins/core_commands/__init__.py:158
msgid "Remove buddy from buddy list"
msgstr "Usuń użytkownika z listy znajomych"

#: pynicotine/plugins/core_commands/__init__.py:166
msgid "Browse files of user"
msgstr "Przeglądaj pliki użytkownika"

#: pynicotine/plugins/core_commands/__init__.py:175
msgid "Show user profile information"
msgstr "Pokaż informacje o profilu użytkownika"

#: pynicotine/plugins/core_commands/__init__.py:183
msgid "Show IP address or username"
msgstr "Pokaż adres IP lub nazwę użytkownika"

#: pynicotine/plugins/core_commands/__init__.py:190
msgid "Block connections from user or IP address"
msgstr "Blokuj połączenia od użytkownika lub adresu IP"

#: pynicotine/plugins/core_commands/__init__.py:197
msgid "Remove user or IP address from ban lists"
msgstr "Usuń użytkownika lub adres IP z listy zablokowanych"

#: pynicotine/plugins/core_commands/__init__.py:204
msgid "Silence messages from user or IP address"
msgstr "Wycisz wiadomości od użytkownika lub adresu IP"

#: pynicotine/plugins/core_commands/__init__.py:212
msgid "Remove user or IP address from ignore lists"
msgstr "Usuń użytkownika lub adres IP z listy ignorowanych"

#: pynicotine/plugins/core_commands/__init__.py:220
msgid "Add share"
msgstr "Dodaj zasób"

#: pynicotine/plugins/core_commands/__init__.py:226
msgid "Remove share"
msgstr "Usuń zasób"

#: pynicotine/plugins/core_commands/__init__.py:233
msgid "List shares"
msgstr "Wylistuj zasoby"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Przeskanuj zasoby"

#: pynicotine/plugins/core_commands/__init__.py:246
msgid "Start global file search"
msgstr "Rozpocznij globalne wyszukiwanie plików"

#: pynicotine/plugins/core_commands/__init__.py:254
msgid "Search files in joined rooms"
msgstr "Wyszukiwanie plików wśród dołączonych pokojów"

#: pynicotine/plugins/core_commands/__init__.py:262
msgid "Search files of all buddies"
msgstr "Wyszukiwanie plików wśród znajomych"

#: pynicotine/plugins/core_commands/__init__.py:270
msgid "Search a user's shared files"
msgstr "Wyszukiwanie wśród plików udostępnionych przez użytkownika"

#: pynicotine/plugins/core_commands/__init__.py:296
#, python-format
msgid "Listing %(num)i available commands:"
msgstr "Zestawienie %(num)i dostępnych poleceń:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr "Lista %(num)i dostępnych poleceń pasujących do \"%(query)s\":"

#: pynicotine/plugins/core_commands/__init__.py:311
#, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Wpisz %(command)s, aby wyświetlić listę podobnych poleceń"

#: pynicotine/plugins/core_commands/__init__.py:314
#, python-format
msgid "Type %(command)s to list available commands"
msgstr "Wpisz %(command)s, aby wyświetlić listę dostępnych poleceń"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, python-format
msgid "Not joined in room %s"
msgstr "Nie połączony w pokoju %s"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr "Brak wiadomości z użytkownikiem %s"

#: pynicotine/plugins/core_commands/__init__.py:408
#, python-format
msgid "Closed private chat of user %s"
msgstr "Zamknięty czat prywatny użytkownika %s"

#: pynicotine/plugins/core_commands/__init__.py:476
#, python-format
msgid "Banned %s"
msgstr "Zablokowany %s"

#: pynicotine/plugins/core_commands/__init__.py:490
#, python-format
msgid "Unbanned %s"
msgstr "Odblokowany %s"

#: pynicotine/plugins/core_commands/__init__.py:503
#, python-format
msgid "Ignored %s"
msgstr "Zignorowano %s"

#: pynicotine/plugins/core_commands/__init__.py:517
#, python-format
msgid "Unignored %s"
msgstr "Nieignorowany %s"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr "%(num_listed)s udostępnione pliki (%(num_total)s skonfigurowane)"

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr "Nie można udostępnić niedostępnego folderu „%s”"

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""
"Dodano %(group_name)s udostępnij \"%(virtual_name)s\" (wymagane ponowne "
"skanowanie)"

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr "Brak współdzielenia z \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr "Usunięto udział „%s” (wymagane ponowne skanowanie)"

#: pynicotine/pluginsystem.py:413
msgid "Loading plugin system"
msgstr "Ładowanie obsługi wtyczek"

#: pynicotine/pluginsystem.py:516
#, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Nie można załadować wtyczki %(name)s. Nazwa folderu wtyczki zawiera "
"nieprawidłowe znaki: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "Konfliktowe polecenie %(interface)s we wtyczce %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, python-format
msgid "Loaded plugin %s"
msgstr "Załadowana wtyczka %s"

#: pynicotine/pluginsystem.py:579
#, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Nie można załadować wtyczki %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, python-format
msgid "Unloaded plugin %s"
msgstr "Nieaktywna wtyczka %s"

#: pynicotine/pluginsystem.py:648
#, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Nie można dezaktywować wtyczki %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Wtyczka %(module)s z błędem %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"

#: pynicotine/pluginsystem.py:810
msgid "No description"
msgstr "Brak opisu"

#: pynicotine/pluginsystem.py:887
#, python-format
msgid "Missing %s argument"
msgstr "Brak argumentu %s"

#: pynicotine/pluginsystem.py:896
#, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Nieprawidłowy argument, możliwe opcje: %s"

#: pynicotine/pluginsystem.py:901
#, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Użycie: %(command)s %(parameters)s"

#: pynicotine/pluginsystem.py:940
#, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Nieznana komenda: %(command)s. Wpisz %(help_command)s, aby wyświetlić listę "
"dostępnych poleceń."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
msgid "No UPnP devices found"
msgstr "Nie znaleziono urządzeń UPnP"

#: pynicotine/portmapper.py:633
#, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"%(protocol)s: Nie udało się przekierować portu zewnętrznego "
"%(external_port)s: %(error)s"

#: pynicotine/portmapper.py:647
#, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"%(protocol)s: Zewnętrzny port %(external_port)s pomyślnie przekierowany na "
"lokalny adres IP %(ip_address)s port %(local_port)s"

#: pynicotine/privatechat.py:220
#, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Prywatna wiadomość od użytkownika '%(user)s': %(message)s"

#: pynicotine/search.py:368
#, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Wyszukiwanie pozycji listy życzeń \"%s\""

#: pynicotine/search.py:434
#, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Okres oczekiwania na listę życzeń ustawiony na %s sekund"

#: pynicotine/search.py:760
#, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr ""
"Użytkownik %(user)s wyszukuje \"%(query)s\", znaleziono %(num)i wyników"

#: pynicotine/shares.py:302
msgid "Rebuilding shares…"
msgstr "Ponowne budowanie zasobów…"

#: pynicotine/shares.py:302
msgid "Rescanning shares…"
msgstr "Ponowne skanowanie zasobów…"

#: pynicotine/shares.py:324
#, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Ponowne skanowanie zakończone: znaleziono %(num)s foldery/ów"

#: pynicotine/shares.py:334
#, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Wystąpił poważny błąd podczas ponownego skanowania zasobów. Jeśli problem "
"będzie się utrzymywać, usuń %(dir)s/*.dbn i spróbuj ponownie. Jeśli to nie "
"pomoże, utwórz plik z raportem błędu z danymi: %(trace)s"

#: pynicotine/shares.py:582
#, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Błąd podczas skanowania pliku %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Błąd podczas skanowania folderu %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "Błąd podczas skanowania metadanych dla pliku %(path)s: %(error)s"

#: pynicotine/shares.py:1046
#, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Ponowne skanowanie przerwane z powodu niedostępnych zasobów: %s"

#: pynicotine/shares.py:1184
#, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "Użytkownik %(user)s przegląda twoją listę udostępnionych zasobów"

#: pynicotine/slskmessages.py:3120
#, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Nie można odczytać bazy danych zasobów. Proszę ponownie przeskanować zasoby. "
"Błąd: %s"

#: pynicotine/slskproto.py:500
#, python-format
msgid "Specified network interface '%s' is not available"
msgstr "Określony interfejs sieciowy '%s' jest niedostępny"

#: pynicotine/slskproto.py:511
#, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Nie można nasłuchiwać na porcie %(port)s. Upewnij się, że żadna inna "
"aplikacja go nie używa, lub wybierz inny port. Błąd: %(error)s"

#: pynicotine/slskproto.py:523
#, python-format
msgid "Listening on port: %i"
msgstr "Nasłuchiwanie na porcie: %i"

#: pynicotine/slskproto.py:805
#, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "Nie można połączyć się z serwerem %(host)s:%(port)s: %(error)s"

#: pynicotine/slskproto.py:1095
#, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Ponowne połączenie z serwerem za %s sekund"

#: pynicotine/slskproto.py:1170
#, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Łączenie z %(host)s:%(port)s"

#: pynicotine/slskproto.py:1208
#, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Połączony do serwera %(host)s:%(port)s, logowanie…"

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Rozłączony od serwera %(host)s:%(port)s"

#: pynicotine/slskproto.py:1499
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Ktoś zalogował się na Twoje konto Soulseek gdzieś indziej"

#: pynicotine/uploads.py:382
#, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Wysyłanie zakończone: użytkownik %(user)s, adres IP %(ip)s, plik %(file)s"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Wysyłanie przerwane, użytkownik %(user)s plik %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, python-format
msgid "Upload I/O error: %s"
msgstr "Błąd wysyłania I/O: %s"

#: pynicotine/uploads.py:1103
#, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Wysyłanie rozpoczęte: użytkownik %(user)s, adres IP %(ip)s, plik %(file)s"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr "Nie można utworzyć katalogu '%(folder)s', błąd: %(error)s"

#: pynicotine/userbrowse.py:236
#, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Ładowanie zasobów z dysku nie powiodło się: %(error)s"

#: pynicotine/userbrowse.py:282
#, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr "Zapisano listę zasobów plików dla użytkownika '%(user)s' do %(dir)s"

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr "Nie można zapisać zasobów, '%(user)s', błąd: %(error)s"

#: pynicotine/userinfo.py:160
#, python-format
msgid "Picture saved to %s"
msgstr "Obraz zapisany do %s"

#: pynicotine/userinfo.py:163
#, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Nie można zapisać zdjęcia do %(filename)s: %(error)s"

#: pynicotine/userinfo.py:190
#, python-format
msgid "User %(user)s is viewing your profile"
msgstr "Użytkownik %(user)s wyświetla Twój profil"

#: pynicotine/users.py:272
#, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Nie można połączyć się z serwerem. Powód: %s"

#: pynicotine/users.py:272
msgid "Cannot Connect"
msgstr "Nie można połączyć"

#: pynicotine/users.py:306
#, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr "Adres IP użytkownika %s jest nieznany odkąd użytkownik jest rozłączony"

#: pynicotine/users.py:315
#, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr "Adres IP użytkownika %(user)s to %(ip)s, port %(port)i%(country)s"

#: pynicotine/users.py:433
msgid "Soulseek Announcement"
msgstr "Ogłoszenie Soulseek"

#: pynicotine/users.py:449
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"Nie masz już przywilejów Soulseek. Gdy są one aktywne, pliki do pobrania "
"będą umieszczane w kolejce przed plikami użytkowników nieuprzywilejowanych."

#: pynicotine/users.py:455
#, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"%(days)i dni, %(hours)i godzin, %(minutes)i minut, %(seconds)i sekund "
"przywilejów Soulseek"

#: pynicotine/users.py:473
msgid "Your password has been changed"
msgstr "Twoje hasło zostało zmienione"

#: pynicotine/users.py:473
msgid "Password Changed"
msgstr "Hasło zostało zmienione"

#: pynicotine/utils.py:574
#, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Nie można otworzyć ścieżki pliku %(path)s: %(error)s"

#: pynicotine/utils.py:621
#, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "Nie można otworzyć adresu URL %(url)s: %(error)s"

#: pynicotine/utils.py:646
#, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr "Coś poszło nie tak podczas czytania pliku %(filename)s: %(error)s"

#: pynicotine/utils.py:651
#, python-format
msgid "Attempting to load backup of file %s"
msgstr "Próba załadowania kopii zapasowej pliku %s"

#: pynicotine/utils.py:673
#, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Nie można utworzyć kopii pliku konfiguracyjnego w %(path)s: %(error)s"

#: pynicotine/utils.py:694
#, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Nie można zapisać pliku konfiguracyjnego w %(path)s: %(error)s"

#: pynicotine/utils.py:705
#, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Nie można przywrócić pliku konfiguracyjnego z %(path)s: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
msgid "Add buddy…"
msgstr "Dodaj znajomego…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
msgid "Toggle Text-to-Speech"
msgstr "Przełącznik zamiany tekstu na mowę"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
msgid "Chat Room Command Help"
msgstr "Lista komend w pokojach rozmów"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
msgid "_Log"
msgstr "_Log"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
msgid "Room Wall"
msgstr "Ściana pokoju"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
msgid "R_oom Wall"
msgstr "Ściana _pokoju"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
msgid "Created by"
msgstr "Stworzone przez"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
msgid "Translated by"
msgstr "Przetłumaczone przez"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
msgid "License"
msgstr "Licencja"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
msgid "Welcome to Nicotine+"
msgstr "Witaj w Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Jeśli żądana nazwa użytkownika jest już zajęta, zostaniesz poproszony o jej "
"zmianę."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""
"Aby połączyć się z innymi urządzeniami Soulseek, port nasłuchiwania w "
"routerze musi zostać przekierowany do komputera."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Jeśli port nasłuchiwania jest zamknięty, to będziesz mieć tylko możliwość "
"połączenia się do tych użytkowników, których port jest otwarty."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"W razie potrzeby wybierz inny port nasłuchu poniżej. Można to również zrobić "
"później w preferencjach."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
msgid "Download Files to Folder"
msgstr "Pobierz pliki do folderu"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
msgid "Share Folders"
msgstr "Udostępnianie folderów"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Użytkownicy Soulseek będą mogli pobierać z Twoich udziałów. Przyczyniaj się "
"do rozwoju sieci Soulseek, udostępniając własną kolekcję i udostępniając "
"ponownie to, co pobrałeś od innych użytkowników."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
msgid "You are ready to use Nicotine+!"
msgstr "Jesteś gotowy by używać Nicotine+!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""
"Soulseek to niezaszyfrowany protokół nieprzeznaczony do bezpiecznej "
"komunikacji."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Darowizna dla Soulseek daje Ci przywileje na określony czas. Jeśli posiadasz "
"przywileje, Twoje pobrania zostaną ustawione w kolejce przed "
"nieuprzywilejowanymi użytkownikami."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
msgid "Previous File"
msgstr "Poprzedni plik"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
msgid "Next File"
msgstr "Następny plik"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
msgid "Name"
msgstr "Nazwa"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
msgid "Last Speed"
msgstr "Ostatnia prędkość"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
msgid "_Export…"
msgstr "_Eksport…"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
msgid "Keyboard Shortcuts"
msgstr "Skróty klawiszowe"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
msgid "General"
msgstr "Ogólne"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr "Połącz"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr "Rozłącz"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
msgid "Rescan Shares"
msgstr "Przeskanuj zasoby"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
msgid "Show Log Pane"
msgstr "Pokaż okno logów"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
msgid "Confirm Quit"
msgstr "Potwierdź wyjście z programu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
msgid "Quit"
msgstr "Zamknij"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
msgid "Menus"
msgstr "Menu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
msgid "Open Main Menu"
msgstr "Otwórz główne menu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
msgid "Open Context Menu"
msgstr "Otwórz menu kontekstowe"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
msgid "Tabs"
msgstr "Karty"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
msgid "Change Main Tab"
msgstr "Zmień kartę główną"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
msgid "Go to Previous Secondary Tab"
msgstr "Przejdź do poprzedniej karty dodatkowej"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
msgid "Go to Next Secondary Tab"
msgstr "Przejdź do następnej karty dodatkowej"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
msgid "Reopen Closed Secondary Tab"
msgstr "Otwórz ponownie kartę dodatkową"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
msgid "Close Secondary Tab"
msgstr "Zamknij kartę dodatkową"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
msgid "Lists"
msgstr "Listy"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
msgid "Copy Selected Cell"
msgstr "Kopiuj zaznaczoną komórkę"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
msgid "Select All"
msgstr "Wybierz wszystko"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
msgid "Find"
msgstr "Szukaj"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
msgid "Remove Selected Row"
msgstr "Usuń zaznaczony wiersz"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
msgid "Editing"
msgstr "Edytowanie"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
msgid "Cut"
msgstr "Wytnij"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
msgid "Paste"
msgstr "Wklej"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
msgid "Insert Emoji"
msgstr "Wstaw emoji"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
msgid "File Transfers"
msgstr "Transfery"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
msgid "Resume / Retry Transfer"
msgstr "Ponów transfer"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
msgid "Pause / Abort Transfer"
msgstr "Przerwij transfer"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
msgid "Download / Upload To"
msgstr "Pobierz / Wyślij do"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
msgid "Save List to Disk"
msgstr "Zapisz listę na dysku"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
msgid "Refresh"
msgstr "Odśwież"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
msgid "Expand / Collapse All"
msgstr "Rozwiń / Zwiń wszystko"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
msgid "Back to Parent Folder"
msgstr "Powrót do folderu nadrzędnego"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
msgid "File Search"
msgstr "Wyszukiwanie plików"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
msgid "Result Filters"
msgstr "Filtrowanie wyników"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
msgid "Current Session"
msgstr "Aktualna sesja"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
msgid "Completed Downloads"
msgstr "Zakończone pobierania"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
msgid "Downloaded Size"
msgstr "Rozmiar pobranych"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
msgid "Completed Uploads"
msgstr "Zakończone wysyłania"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
msgid "Uploaded Size"
msgstr "Rozmiar wysłanych"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
msgid "Total"
msgstr "Łącznie"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
msgid "_Reset…"
msgstr "_Reset…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Pozycje listy życzeń są automatycznie przeszukiwane w regularnych odstępach "
"czasu, w celu wykrycia niecodziennych plików."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
msgid "Add Wish…"
msgstr "Dodaj życzenie…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
msgid "Clear All…"
msgstr "Wyczyść wszystko…"

#: pynicotine/gtkgui/ui/downloads.ui:138
msgid "Clear All Finished/Filtered Downloads"
msgstr "Wyczyść wszystkie zakończone/filtrowane pobrania"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
msgid "Clear Finished"
msgstr "Wyczyść zakończone"

#: pynicotine/gtkgui/ui/downloads.ui:169
msgid "Clear Specific Downloads"
msgstr "Wyczyść określone pobrane"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
msgid "Clear _All…"
msgstr "Wyczyść _wszystkie…"

#: pynicotine/gtkgui/ui/interests.ui:21
msgid "Personal Interests"
msgstr "Zainteresowania"

#: pynicotine/gtkgui/ui/interests.ui:40
msgid "Add something you like…"
msgstr "Dodaj coś, co lubisz…"

#: pynicotine/gtkgui/ui/interests.ui:62
msgid "Personal Dislikes"
msgstr "Osobiste niechęci"

#: pynicotine/gtkgui/ui/interests.ui:80
msgid "Add something you dislike…"
msgstr "Dodaj coś, czego nie lubisz…"

#: pynicotine/gtkgui/ui/interests.ui:143
msgid "Refresh Recommendations"
msgstr "Odśwież listę rekomendacji"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
msgid "Main Menu"
msgstr "Główne menu"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
msgid "Room…"
msgstr "Pokój…"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
msgid "Username…"
msgstr "Użytkownik…"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
msgid "Search term…"
msgstr "Wyszukiwana fraza…"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Wyczyść"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Wzory wyszukiwania: z wyrazem = wyrażenie, bez wyrazu = -wyrażenie, część "
"wyrazu = *erm"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
msgid "Search Scope"
msgstr "Zakres wyszukiwania"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
msgid "_Wishlist"
msgstr "_Lista życzeń"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
msgid "Configure Searches"
msgstr "Konfiguracja wyszukiwania"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Wprowadź frazę wyszukiwania, aby wyszukać pliki udostępnione przez innych "
"użytkowników w sieci Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
msgid "File Grouping Mode"
msgstr "Tryb grupowania plików"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
msgid "Configure Downloads"
msgstr "Konfiguracja pobierania"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Pliki pobierane od innych użytkowników są tutaj umieszczane w kolejce i mogą "
"być wstrzymywane i wznawiane na żądanie"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
msgid "Configure Uploads"
msgstr "Konfiguracja wysyłania"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Próby pobierania przez użytkowników Twoich udostępnionych plików są tutaj "
"kolejkowane i zarządzane"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
msgid "_Open List"
msgstr "_Otwórz listę"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Otwiera lokalną listę udostępnionych plików, która została wcześniej "
"zapisana na dysku"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
msgid "Configure Shares"
msgstr "Konfiguracja udostępnionych zasobów"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Wprowadź nazwę użytkownika, aby przeglądać jego listę udostępnionych plików. "
"Możesz również zapisać listę i przejrzeć ją później."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
msgid "_Personal Profile"
msgstr "_Profil personalny"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
msgid "Configure Account"
msgstr "Konfiguruj konto"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Wprowadź nazwę użytkownika, aby wyświetlić jego opis, informacje i zdjęcie"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
msgid "Chat _History"
msgstr "_Historia czatu"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
msgid "Configure Chats"
msgstr "Konfiguracja czatów"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr "Wprowadź nazwę użytkownika, aby rozpocząć z nim czat prywatny"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
msgid "_Message All"
msgstr "_Wiadomość do wszystkich"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
msgid "Configure Ignored Users"
msgstr "Konfiguracja zablokowanych użytkowników"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Dodaj użytkowników do swojej listy znajomych, aby udostępniać im określone "
"foldery i otrzymywać powiadomienia, gdy są dostępni"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
msgid "Join or create room…"
msgstr "Dołącz lub utwórz pokój…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Dołącz do istniejącego pokoju czatowego lub stwórz nowy pokój, aby rozmawiać "
"z innymi użytkownikami sieci Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
msgid "Configure User Profile"
msgstr "Konfiguruj profil użytkownika"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
msgid "Connections"
msgstr "Połączenia"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
msgid "Downloading (Speed / Active Users)"
msgstr "Pobieranie (Prędkość / Aktywni użytkownicy)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
msgid "Uploading (Speed / Active Users)"
msgstr "Wysyłanie (Prędkość / Aktywni użytkownicy)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
msgid "Search chat history…"
msgstr "Przeszukaj historię czatu…"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
msgid "Download Speed Limits"
msgstr "Limity prędkości pobierania"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
msgid "Unlimited download speed"
msgstr "Nieograniczona prędkość pobierania"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
msgid "Use download speed limit (KiB/s):"
msgstr "Limit prędkości pobierania (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Alternatywny limit prędkości pobierania (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
msgid "Search rooms…"
msgstr "Wyszukaj pokoje…"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
msgid "Refresh Rooms"
msgstr "Odśwież listę pokoi"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
msgid "_Show feed of public chat room messages"
msgstr "_Pokaż kanał wiadomości z publicznego pokoju rozmów"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
msgid "_Accept private room invitations"
msgstr "_Akceptuj zaproszenia do prywatnych pokoi"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Napisz pojedynczą wiadomość, którą inni użytkownicy pokoju będą mogli "
"przeczytać później. Ostatnie wiadomości są pokazywane na górze."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
msgid "Set wall message…"
msgstr "Ustaw wiadomość ściany pokoju…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
msgid "Search Result Filters"
msgstr "Filtry wyników wyszukiwania"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Filtry wyników wyszukiwania służą do precyzyjnego określania wyświetlanych "
"wyników wyszukiwania."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Każda lista wyników wyszukiwania ma swój własny filtr, którego można użyć "
"przełączając przycisk Filtry wyników. Filtr składa się z wielu pól, z "
"których wszystkie są stosowane po naciśnięciu Enter w którymś z jego pól. "
"Filtrowanie jest stosowane natychmiast do wyników już otrzymanych, a także "
"do tych, które dopiero nadejdą."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Jak sama nazwa wskazuje, filtr wyników wyszukiwania nie może rozszerzyć "
"pierwotnego wyszukiwania, może je jedynie zawęzić. Aby poszerzyć lub zmienić "
"wyszukiwane hasła, przeprowadź nowe wyszukiwanie."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
msgid "Result Filter Usage"
msgstr "Użycie filtra wyników"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
msgid "Include Text"
msgstr "Uwzględnij tekst"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
msgid "Files, folders and usernames containing this text will be shown."
msgstr ""
"Pliki, foldery i nazwy użytkowników zawierające ten tekst będą pokazane."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Wielkość liter nie ma znaczenia, ale ważna jest kolejność słów: "
"„Instrumental Remix” nie pokaże żadnego „Remix Instrumental”"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Użyj | (lub potoków), aby oddzielić kilka dokładnych fraz. Przykład:\n"
"    Remiksy|Dub Mix|Instrumentalne"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
msgid "Exclude Text"
msgstr "Pomiń tekst"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr "Jak wyżej, ale pliki i foldery są odfiltrowywane, jeśli tekst pasuje."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
msgid "Filters files based upon their file extension."
msgstr "Filtrowanie plików na podstawie rozszerzenia."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Można podać wiele rozszerzeń plików, co z kolei poszerzy listę wyników. "
"Przykład:\n"
"    flac|wav|ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"Możliwe jest również odwrócenie filtra, określając rozszerzenia plików, "
"których nie chcesz w wynikach za pomocą wykrzyknika! Przykład:\n"
"    !mp3 !jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
msgid "File Size"
msgstr "Rozmiar pliku"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
msgid "Filters files based upon their file size."
msgstr "Filtrowanie plików na podstawie rozmiaru."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"Domyślnie używaną jednostką jest bajt (B), a pliki większe lub równe (>=) "
"tej wartości będą dopasowywane."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Dodaj b, k, m lub g (alternatywnie kib, mib lub gib) by określić jednostki "
"bajtów, kibibajtów, mebibajtów lub gibibajtów:\n"
"    20m aby pokazać pliki większe niż 20 MiB (mebibajtów)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Poprzedź = do wartości, aby określić dokładne dopasowanie:\n"
"    =1024 dopasowuje tylko pliki, które mają rozmiar 1 KiB (kibibajt)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Przedrostek ! do wartości, aby wykluczyć pliki o określonym rozmiarze:\n"
"    !30,5m, aby ukryć pliki, które mają 30,5 MiB (mebibajtów)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Poprzedź < lub >, aby znaleźć pliki mniejsze/większe od podanej wartości. "
"Użyj spacji pomiędzy każdym warunkiem, aby zawrzeć zakres:\n"
"    >10,5m <1g, aby pokazać pliki większe niż 10,5 MiB, ale mniejsze niż 1 "
"GiB."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Bardziej znane warianty kb, mb i gb mogą być również używane dla jednostek "
"kilobajtowych, megabajtowych i gigabajtowych."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
msgid "Filters files based upon their bitrate."
msgstr "Filtrowanie plików na podstawie przepływności."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Wartości należy wprowadzać wyłącznie jako cyfry. Jednostką jest zawsze Kb/s "
"(kilobity na sekundę)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Podobnie jak rozmiar pliku (powyżej), operatory =, !, <, >, <= lub >= mogą "
"być używane, można również określić wiele warunków. Na przykład, aby "
"wyświetlić pliki o przepływności co najmniej 256 Kb/s i maksymalnie 1411 Kb/"
"s:\n"
"    256 <=1411"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
msgid "Filters files based upon their duration."
msgstr "Filtruje pliki na podstawie czasu ich trwania."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"Domyślnie dopasowywane będą pliki dłuższe lub równe (>=) wprowadzonemu "
"czasowi trwania, chyba że użyty zostanie operator (=, !, <=, < lub >)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Wprowadź surową wartość w sekundach lub użyj formatów czasu MM:SS i HH:MM:"
"SS:\n"
"    =53 pokazuje pliki, które mają około 53 sekundy długości.\n"
"    >5:30 pokazuje pliki o długości większej niż 5 i pół minuty.\n"
"    <5:30:00 pokazuje pliki o długości mniejszej niż 5 i pół godziny."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Wiele warunków można określić za pomocą separatora | : \n"
"    >6:00|<12:00, aby wyświetlić pliki o długości od 6 do 12 minut.\n"
"    !9:54|!8:43|!7:32, aby ukryć określone pliki w wynikach.\n"
"    =5:34|=4:23|=3:05, aby uwzględnić pliki o określonym czasie trwania."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Filtruje pliki na podstawie położenia geograficznego użytkowników zgodnie z "
"kodami krajów zdefiniowanymi przez ISO 3166-2:\n"
"    US pokaże tylko wyniki pochodzące od użytkowników z adresami IP w "
"Stanach Zjednoczonych.\n"
"    !GB ukryje wyniki pochodzące od użytkowników z Wielkiej Brytanii."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Można określić wiele krajów za pomocą przecinków lub spacji."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
msgid "Free Slot"
msgstr "Wolny slot"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Pokaż tylko te wyniki od użytkowników, którzy mają przynajmniej jeden wolny "
"slot. Ten filtr jest stosowany natychmiast."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
msgid "Upload Speed Limits"
msgstr "Limity prędkości wysyłania"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
msgid "Unlimited upload speed"
msgstr "Nieograniczona prędkość wysyłania"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
msgid "Use upload speed limit (KiB/s):"
msgstr "Limit prędkości wysyłania (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Alternatywny limit prędkości wysyłania (KiB/s):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
msgid "Private Chat Command Help"
msgstr "Lista komend w prywatnych rozmowach"

#: pynicotine/gtkgui/ui/search.ui:7
msgid "Include text…"
msgstr "Uwzględnij tekst…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtruj w wynikach wejściowych gdzie ścieżka pliku zawiera dany tekst. Można "
"podać wiele fraz i słów, np. dokładna fraza|muzyka|termin|dokładna kolejna "
"fraza"

#: pynicotine/gtkgui/ui/search.ui:18
msgid "Exclude text…"
msgstr "Pomiń tekst…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtruj w wynikach wyjściowych gdzie ścieżka pliku zawiera dany tekst. Można "
"podać wiele fraz i słów, np. dokładna fraza|muzyka|termin|dokładna kolejna "
"fraza"

#: pynicotine/gtkgui/ui/search.ui:29
msgid "File type…"
msgstr "Typ pliku…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Typ pliku, np. flac wav lub !mp3 !m4a"

#: pynicotine/gtkgui/ui/search.ui:40
msgid "File size…"
msgstr "Rozmiar pliku…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
msgid "File size, e.g. >10.5m <1g"
msgstr "Rozmiar pliku, np. >10.5m <1g"

#: pynicotine/gtkgui/ui/search.ui:51
msgid "Bitrate…"
msgstr "Przepływność…"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
msgid "Bitrate, e.g. 256 <1412"
msgstr "Przepływność, np. 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
msgid "Duration…"
msgstr "Czas trwania…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Czas trwania, np. >6:00 <12:00 !6:54"

#: pynicotine/gtkgui/ui/search.ui:73
msgid "Country code…"
msgstr "Kod kraju…"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Kod kraju, np. US ES lub !DE !GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Zabroń użytkownikom dostępu do udostępnionych plików na podstawie nazwy "
"użytkownika, adresu IP lub kraju."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Kody krajów do zablokowania (oddzielone przecinkami):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
msgid "Codes must be in ISO 3166-2 format."
msgstr "Kody muszą być w formacie ISO 3166-2."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
msgid "Use custom geo block message:"
msgstr "Użyj niestandardowej wiadomości geoblokowania:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Użyj niestandardowej wiadomości blokowania:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
msgid "IP Addresses"
msgstr "Adresy IP"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
msgid "Restore previously open private chats on startup"
msgstr "Ustaw ostatni prywatny czat na starcie"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
msgid "Enable spell checker"
msgstr "Włącz sprawdzanie pisowni"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
msgid "Enable CTCP-like private message responses (client version)"
msgstr ""
"Włączanie odpowiedzi na prywatne wiadomości w stylu CTCP (wersja kliencka)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
msgid "Number of recent private chat messages to show:"
msgstr "Liczba ostatnich prywatnych wiadomości czatu do wyświetlenia:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
msgid "Number of recent chat room messages to show:"
msgstr "Liczba ostatnich wiadomości w pokoju rozmów do wyświetlenia:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
msgid "Chat Completion"
msgstr "Dopełnianie czatu"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
msgid "Enable tab-key completion"
msgstr "Włącz dopełnianie przy pomocy klawisza TAB"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
msgid "Enable completion drop-down list"
msgstr "Pokazuj listę rozwijaną z dopełnieniami"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
msgid "Minimum characters required to display drop-down:"
msgstr "Minimalna ilość znaków wymagana do wyświetlenia listy rozwijanej:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
msgid "Allowed chat completions:"
msgstr "Dozwolone dopełnienia czatu:"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
msgid "Buddy names"
msgstr "Imiona znajomych"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
msgid "Chat room usernames"
msgstr "Nazwy użytkowników czatów"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
msgid "Room names"
msgstr "Nazwy pokoi"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
msgid "Commands"
msgstr "Komendy"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
msgid "Timestamps"
msgstr "Znaczniki czasu"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
msgid "Private chat format:"
msgstr "Format czatu prywatnego:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "Domyślnie"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
msgid "Chat room format:"
msgstr "Format pokoju rozmów:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
msgid "Text-to-Speech"
msgstr "Zamiana tekstu na mowę"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
msgid "Enable Text-to-Speech"
msgstr "Włącz zamianę tekstu na mowę"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
msgid "Text-to-Speech command:"
msgstr "Komenda zamiany tekstu na mowę:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
msgid "Private chat message:"
msgstr "Wiadomość prywatnego czatu:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
msgid "Chat room message:"
msgstr "Wiadomość pokoju rozmów:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
msgid "Censor"
msgstr "Cenzura"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
msgid "Enable censoring of text patterns"
msgstr "Włącz cenzurę dla następujących fraz"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
msgid "Auto-Replace"
msgstr "Automatyczna podmiana"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
msgid "Enable automatic replacement of words"
msgstr "Włącz automatyczną podmianę wyrazów"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr ""
"Automatyczne czyszczenie zakończonych / filtrowanych pobrań z listy "
"transferów"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
msgid "Store completed downloads in username subfolders"
msgstr "Przechowuj ukończone pobrania w podfolderach z nazwą użytkownika"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
msgid "Double-click action for downloads:"
msgstr "Akcja po dwukrotnym kliknięciu dla pobieranych:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
msgid "Allow users to send you any files:"
msgstr "Zezwalaj użytkownikom na wysyłanie dowolnych plików:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
msgid "Folders"
msgstr "Foldery"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
msgid "Finished downloads:"
msgstr "Zakończone pobierania:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
msgid "Incomplete downloads:"
msgstr "Niekompletne pobierania:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
msgid "Received files:"
msgstr "Otrzymane pliki:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "Zdarzenia"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
msgid "Run command after file download finishes ($ for file path):"
msgstr ""
"Uruchom komendę po zakończeniu pobierania pliku ($ dla ścieżki do pliku):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"Uruchom komendę po zakończeniu pobierania folderu ($ dla ścieżki folderu):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
msgid "Download Filters"
msgstr "Filtry pobierania"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
msgid "Enable download filters"
msgstr "Włącz filtry pobierania"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Dodaj"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Załaduj domyślne"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
msgid "Verify Filters"
msgstr "Sprawdź filtry"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
msgid "Unverified"
msgstr "Niezweryfikowane"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Ignoruj wiadomości czatu i wyniki wyszukiwania od użytkowników na podstawie "
"nazwy użytkownika lub adresu IP."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Domyślnie zapisuj logi z pokoi rozmów"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Domyślnie zapisuj logi z prywatnych rozmów"

#: pynicotine/gtkgui/ui/settings/log.ui:142
msgid "Log transfers to file"
msgstr "Zapisuj logi transferów do pliku"

#: pynicotine/gtkgui/ui/settings/log.ui:166
msgid "Log debug messages to file"
msgstr "Zapisuj logi dziennika debuggera do pliku"

#: pynicotine/gtkgui/ui/settings/log.ui:190
msgid "Log timestamp format:"
msgstr "Format znacznika czasu dziennika:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
msgid "Folder Locations"
msgstr "Lokalizacja folderów"

#: pynicotine/gtkgui/ui/settings/log.ui:245
msgid "Chatroom logs folder:"
msgstr "Folder logów pokoi rozmów:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
msgid "Private chat logs folder:"
msgstr "Folder logów prywatnych czatów:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
msgid "Transfer logs folder:"
msgstr "Folder logów transferów:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
msgid "Debug logs folder:"
msgstr "Folder logów dziennika debuggera:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Zaloguj się do istniejącego konta Soulseek lub utwórz nowe. W nazwach "
"użytkowników rozróżniana jest wielkość liter i są one unikatowe."

#: pynicotine/gtkgui/ui/settings/network.ui:118
msgid "Public IP address:"
msgstr "Publiczny adres IP:"

#: pynicotine/gtkgui/ui/settings/network.ui:159
msgid "Listening port:"
msgstr "Port nasłuchiwania:"

#: pynicotine/gtkgui/ui/settings/network.ui:185
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Automatycznie przekierowuj port nasłuchiwania (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
msgid "Away Status"
msgstr "Status „Zaraz wracam”"

#: pynicotine/gtkgui/ui/settings/network.ui:228
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Minuty bezczynności przed odejściem (0, aby wyłączyć):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
msgid "Auto-reply message when away:"
msgstr "Automatyczna odpowiedź podczas nieobecności:"

#: pynicotine/gtkgui/ui/settings/network.ui:299
msgid "Auto-connect to server on startup"
msgstr "Automatyczne łączenie z serwerem podczas uruchamiania"

#: pynicotine/gtkgui/ui/settings/network.ui:323
msgid "Soulseek server:"
msgstr "Serwer Soulseek:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Wiąże połączenia z określonym interfejsem sieciowym, przydatne np. do "
"zapewnienia, że VPN jest używany przez cały czas. Pozostaw puste, aby użyć "
"dowolnego dostępnego interfejsu. Zmień tę wartość tylko wtedy, gdy wiesz, co "
"robisz."

#: pynicotine/gtkgui/ui/settings/network.ui:351
msgid "Network interface:"
msgstr "Interfejs sieciowy:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"Funkcja „Teraz odtwarzanie” pozwala na wyświetlanie odtwarzanego utworu w "
"pokoju rozmów przy pomocy komendy /now."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
msgid "Other"
msgstr "Inne"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
msgid "Now Playing Format"
msgstr "Format „Teraz odtwarzanie”"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
msgid "Now Playing message format:"
msgstr "Format wiadomości „Teraz odtwarzanie”:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
msgid "Test Configuration"
msgstr "Przetestuj konfigurację"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
msgid "Enable plugins"
msgstr "Włącz wtyczki"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
msgid "Add Plugins"
msgstr "Dodaj wtyczki"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
msgid "_Add Plugins"
msgstr "_Dodaj wtyczki"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
msgid "Settings"
msgstr "Ustawienia"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
msgid "_Settings"
msgstr "U_stawienia"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
msgid "Version:"
msgstr "Wersja:"

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
msgid "Created by:"
msgstr "Stworzone przez:"

#: pynicotine/gtkgui/ui/settings/search.ui:51
msgid "Enable search history"
msgstr "Włącz historię wyszukiwań"

#: pynicotine/gtkgui/ui/settings/search.ui:70
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Prywatnie udostępnione pliki, które zostały uwidocznione dla wszystkich, "
"będą oznaczone przedrostkiem „[PRIVATE]” i nie będzie można ich pobrać, "
"dopóki osoba przesyłająca nie wyrazi na to wyraźnej zgody. Należy ją o to "
"uprzejmie poprosić."

#: pynicotine/gtkgui/ui/settings/search.ui:76
msgid "Show privately shared files in search results"
msgstr "Pokaż prywatnie udostępnione pliki w wynikach wyszukiwania"

#: pynicotine/gtkgui/ui/settings/search.ui:106
msgid "Limit number of results per search:"
msgstr "Maksymalna liczba zachowanych wyników na wyszukiwanie:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
msgid "Result Filter Help"
msgstr "Pomoc dotycząca filtrów wyszukiwania"

#: pynicotine/gtkgui/ui/settings/search.ui:177
msgid "Enable search result filters by default"
msgstr "Domyślnie włączaj filtrowanie"

#: pynicotine/gtkgui/ui/settings/search.ui:211
msgid "Include:"
msgstr "Uwzględnij:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
msgid "Exclude:"
msgstr "Pomiń:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
msgid "File Type:"
msgstr "Typ pliku:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "Rozmiar:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Przepływność:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
msgid "Duration:"
msgstr "Czas trwania:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
msgid "Country Code:"
msgstr "Kod kraju:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
msgid "Only show results from users with an available upload slot."
msgstr "Pokazuj tylko te rezultaty od użytkowników z wolnym slotem."

#: pynicotine/gtkgui/ui/settings/search.ui:430
msgid "Network Searches"
msgstr "Wyszukiwania sieciowe"

#: pynicotine/gtkgui/ui/settings/search.ui:452
msgid "Respond to search requests from other users"
msgstr "Odpowiadaj na żądania wyszukiwań od innych użytkowników"

#: pynicotine/gtkgui/ui/settings/search.ui:486
msgid "Searches shorter than this number of characters will be ignored:"
msgstr "Wyszukiwania krótsze niż ta liczba znaków będą ignorowane:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
msgid "Maximum search results to send per search request:"
msgstr "Maksymalna ilość wyników wyszukiwania do wysłania na jedno zapytanie:"

#: pynicotine/gtkgui/ui/settings/search.ui:578
msgid "Clear Search History"
msgstr "Wyczyść historię wyszukiwań"

#: pynicotine/gtkgui/ui/settings/search.ui:626
msgid "Clear Filter History"
msgstr "Wyczyść historię filtrowań"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Automatyczne ponowne skanowanie zawartości udostępnionych folderów podczas "
"startu. Jeśli wyłączone, udostępnione zasoby zostaną zaktualizowane dopiero "
"przy manualnym skanowaniu."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Przeskanuj udostępnione zasoby na starcie"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr "Widoczne dla wszystkich:"

#: pynicotine/gtkgui/ui/settings/shares.ui:82
msgid "Buddy shares"
msgstr "Zasoby znajomych"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
msgid "Trusted shares"
msgstr "Zasoby zaufanych"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr ""
"Automatyczne czyszczenie zakończonych / anulowanych wysyłań z listy "
"transferów"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
msgid "Double-click action for uploads:"
msgstr "Akcja po dwukrotnym kliknięciu dla wysyłanych:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
msgid "Limit upload speed:"
msgstr "Ogranicz prędkość wysyłania:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
msgid "Per transfer"
msgstr "Na transfer"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
msgid "Total transfers"
msgstr "Wszystkich transferów"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
msgid "Upload Slots"
msgstr "Sloty wysyłania"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Algorytm karuzelowy: Pliki będą przesyłane w sposób cykliczny do "
"użytkowników oczekujących w kolejce.\n"
"Pierwsze weszło, pierwsze wyszło: Pliki zostaną przesłane w kolejności, w "
"jakiej zostały umieszczone w kolejce."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
msgid "Upload queue type:"
msgstr "Typ kolejki wysyłania:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr "Przydzielaj sloty wysyłania, aż całkowita prędkość osiągnie (KiB/s):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
msgid "Fixed number of upload slots:"
msgstr "Ogranicz liczbę slotów wysyłania do:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
msgid "Prioritize all buddies"
msgstr "Nadaj priorytet wszystkim znajomym"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
msgid "Queue Limits"
msgstr "Limity kolejek"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
msgid "Maximum number of queued files per user:"
msgstr "Maksymalna liczba plików w kolejce na użytkownika:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr "Maksymalny łączny rozmiar plików w kolejce na użytkownika (MiB):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
msgid "Limits do not apply to buddies"
msgstr "Limity nie dotyczą znajomych"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"Wystąpienia $ są zastępowane przez adres URL. Domyślne aplikacje systemowe "
"są używane w przypadkach, gdy protokół nie został skonfigurowany."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
msgid "File manager command:"
msgstr "Komenda menedżera plików:"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
msgid "Reset Picture"
msgstr "Wyczyść zdjęcie"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
msgid "Self Description"
msgstr "Własny opis"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""
"Dodaj elementy, które chcesz, aby wszyscy widzieli, takie jak krótki opis, "
"pomocne wskazówki lub wytyczne dotyczące pobierania udostępnionych "
"materiałów."

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
msgid "Picture:"
msgstr "Zdjęcie:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
msgid "Prefer dark mode"
msgstr "Tryb nocny"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
msgid "Use header bar"
msgstr "Użyj paska nagłówka"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
msgid "Display tray icon"
msgstr "Pokaż ikonę w zasobniku"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
msgid "Minimize to tray on startup"
msgstr "Minimalizuj do zasobnika podczas startu"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
msgid "Language (requires a restart):"
msgstr "Język (wymaga ponownego uruchomienia):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
msgid "When closing window:"
msgstr "Podczas zamykania okna:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
msgid "Notifications"
msgstr "Powiadomienia"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
msgid "Enable sound for notifications"
msgstr "Włącz dźwięk powiadomień"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
msgid "Show notification for private chats and mentions in the window title"
msgstr "Pokaż powiadomienia o prywatnych czatach i wzmiankach w tytule okna"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
msgid "Show notifications for:"
msgstr "Pokaż powiadomienia dla:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
msgid "Finished file downloads"
msgstr "Zakończone pobieranie plików"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
msgid "Finished folder downloads"
msgstr "Zakończone pobieranie folderów"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
msgid "Private messages"
msgstr "Prywatne wiadomości"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
msgid "Chat room messages"
msgstr "Wiadomości z czatu"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
msgid "Chat room mentions"
msgstr "Wzmianki na czacie"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
msgid "Wishlist results found"
msgstr "Znaleziono wyniki listy życzeń"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
msgid "Restore the previously active main tab at startup"
msgstr "Przywróć poprzednio aktywną kartę główną podczas uruchamiania"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
msgid "Close-buttons on secondary tabs"
msgstr "Przyciski zamykania na dodatkowych kartach"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
msgid "Regular tab label color:"
msgstr "Kolor etykiety typowej karty:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
msgid "Changed tab label color:"
msgstr "Kolor etykiety zmienionej karty:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
msgid "Highlighted tab label color:"
msgstr "Kolor etykiety podświetlonej karty:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr "Pozycja listy znajomych:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
msgid "Visible main tabs:"
msgstr "Widoczne karty główne:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
msgid "Tab bar positions:"
msgstr "Pozycje paska kart:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
msgid "Main tabs"
msgstr "Główne karty"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
msgid "Show reverse file paths (requires a restart)"
msgstr "Pokaż odwrotne ścieżki plików (wymaga ponownego uruchomienia)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
msgid "Show exact file sizes (requires a restart)"
msgstr "Pokaż dokładne rozmiary plików (wymaga ponownego uruchomienia)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
msgid "List text color:"
msgstr "Kolor tekstu listy:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
msgid "Enable colored usernames"
msgstr "Włącz kolorowe nazwy użytkowników"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
msgid "Chat username appearance:"
msgstr "Wygląd nazwy użytkownika czatu:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
msgid "Remote text color:"
msgstr "Kolor tekstu zdalnego:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
msgid "Local text color:"
msgstr "Kolor tekstu lokalnego:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
msgid "Command output text color:"
msgstr "Kolor tekstu wyjściowego polecenia:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
msgid "/me action text color:"
msgstr "Kolor tekstu komendy /me:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
msgid "Highlighted text color:"
msgstr "Kolor tekstu podświetlanego:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
msgid "URL link text color:"
msgstr "Kolor tekstu linków URL:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
msgid "User Statuses"
msgstr "Statusy użytkowników"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
msgid "Online color:"
msgstr "Kolor online:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
msgid "Away color:"
msgstr "Kolor zaraz wracam:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
msgid "Offline color:"
msgstr "Kolor offline:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
msgid "Text Entries"
msgstr "Tekst"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
msgid "Text entry background color:"
msgstr "Tło tekstu:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
msgid "Text entry text color:"
msgstr "Kolor tekstu:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
msgid "Fonts"
msgstr "Czcionki"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
msgid "Global font:"
msgstr "Czcionka globalna:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
msgid "List font:"
msgstr "Czcionka listy:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
msgid "Text view font:"
msgstr "Czcionka widoku tekstu:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
msgid "Chat font:"
msgstr "Czcionka czatu:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
msgid "Transfers font:"
msgstr "Czcionka transferów:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
msgid "Search font:"
msgstr "Czcionka szukania:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
msgid "Browse font:"
msgstr "Czcionka przeglądania:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
msgid "Icons"
msgstr "Ikony"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
msgid "Icon theme folder:"
msgstr "Folder z motywami ikon:"

#: pynicotine/gtkgui/ui/uploads.ui:86
msgid "Abort User(s)"
msgstr "Przerwij użytkownikowi/om"

#: pynicotine/gtkgui/ui/uploads.ui:116
msgid "Ban User(s)"
msgstr "Zablokuj użytkownika/ów"

#: pynicotine/gtkgui/ui/uploads.ui:138
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Wyczyść wszystkie zakończone i anulowane wysyłania"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
msgid "Message All"
msgstr "Wyślij wiadomość do wszystkich"

#: pynicotine/gtkgui/ui/uploads.ui:199
msgid "Clear Specific Uploads"
msgstr "Wyczyść określone wysłane"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
msgid "Save Shares List to Disk"
msgstr "Zapisz listę zasobów na dysk"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
msgid "Refresh Files"
msgstr "Odśwież pliki"

#: pynicotine/gtkgui/ui/userinfo.ui:101
msgid "Edit Profile"
msgstr "Edytuj profil"

#: pynicotine/gtkgui/ui/userinfo.ui:149
msgid "Shared Files"
msgstr "Udostępnione pliki"

#: pynicotine/gtkgui/ui/userinfo.ui:203
msgid "Upload Speed"
msgstr "Prędkość wysyłania"

#: pynicotine/gtkgui/ui/userinfo.ui:230
msgid "Free Upload Slots"
msgstr "Wolne sloty wysyłania"

#: pynicotine/gtkgui/ui/userinfo.ui:284
msgid "Queued Uploads"
msgstr "Zakolejkowane wysyłania"

#: pynicotine/gtkgui/ui/userinfo.ui:354
msgid "Edit Interests"
msgstr "Edytuj zainteresowania"

#: pynicotine/gtkgui/ui/userinfo.ui:624
msgid "_Gift Privileges…"
msgstr "_Podaruj przywileje…"

#: pynicotine/gtkgui/ui/userinfo.ui:663
msgid "_Refresh Profile"
msgstr "_Odśwież profil"

#: pynicotine/plugins/core_commands/PLUGININFO:3
msgid "Nicotine+ Commands"
msgstr "Polecenia Nicotine+"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Zespół Nicotine+"

#~ msgid "Listening port (requires a restart):"
#~ msgstr "Port nasłuchiwania (wymaga ponownego uruchomienia):"

#~ msgid "Network interface (requires a restart):"
#~ msgstr "Interfejs sieciowy (wymaga ponownego uruchomienia):"

#~ msgid "Invalid Password"
#~ msgstr "Błędne hasło"

#~ msgid "Change _Login Details"
#~ msgstr "Zmień dane logowania"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i uprzywilejowanych użytkowników"

#~ msgid "_Set Up…"
#~ msgstr "Konfiguruj…"

#~ msgid "Queued search result text color:"
#~ msgstr "Kolor tekstu wyników wyszukiwania w kolejce:"

#, python-format
#~ msgid "Failed to fetch the shared folder %(folder)s: %(error)s"
#~ msgstr "Błąd podczas pobierania zawartości folderu %(folder)s: %(error)s"

#~ msgid "_Clear"
#~ msgstr "_Wyczyść"

#, python-format
#~ msgid "Can't save %(filename)s: %(error)s"
#~ msgstr "Nie można zapisać %(filename)s: %(error)s"

#~ msgid "Trusted Buddies"
#~ msgstr "Zaufani użytkownicy"

#~ msgid "Quit program"
#~ msgstr "Zamknij program"

#~ msgid "Username:"
#~ msgstr "Użytkownik:"

#~ msgid "Re_commendations for Item"
#~ msgstr "Re_komendacje dla tego obiektu"

#~ msgid "_Remove Item"
#~ msgstr "_Usuń ten obiekt"

#~ msgid "_Remove"
#~ msgstr "_Usuń"

#~ msgid "Send M_essage"
#~ msgstr "Wyślij wiadomość"

#~ msgid "Send Message"
#~ msgstr "Wyślij wiadomość"

#~ msgid "View User Profile"
#~ msgstr "Wyświetl profil użytkownika"

#~ msgid "Start Messaging"
#~ msgstr "Rozpocznij konwersacje"

#~ msgid "Enter the name of the user whom you want to send a message:"
#~ msgstr "Wprowadź nazwę użytkownika, do którego chcesz wysłać wiadomość:"

#~ msgid "_Message"
#~ msgstr "_Wiadomość"

#~ msgid "Enter the name of the user whose profile you want to see:"
#~ msgstr "Wprowadź nazwę użytkownika, którego profil chcesz wyświetlić:"

#~ msgid "_View Profile"
#~ msgstr "_Zobacz profil"

#~ msgid "Enter the name of the user whose shares you want to see:"
#~ msgstr "Wprowadź nazwę użytkownika, którego listę zasobów chcesz otrzymać:"

#~ msgid "_Browse"
#~ msgstr "_Przeglądaj"

#~ msgid "Password"
#~ msgstr "Hasło"

#~ msgid "Download File"
#~ msgstr "Pobierz plik"

#~ msgid "Refresh Similar Users"
#~ msgstr "Odśwież podobnych użytkowników"

#~ msgid "Enter the username of the person whose files you want to see"
#~ msgstr ""
#~ "Wprowadź nazwę użytkownika, którego chcesz zobaczyć udostępnione pliki"

#~ msgid "Enter the username of the person whose information you want to see"
#~ msgstr "Wprowadź nazwę użytkownika osoby, której informacje chcesz zobaczyć"

#~ msgid "Enter the username of the person you want to send a message to"
#~ msgstr ""
#~ "Wprowadź nazwę użytkownika, któremu chcesz wysłać prywatną wiadomość"

#~ msgid "Enter the username of the person you want to add to your buddy list"
#~ msgstr ""
#~ "Wprowadź nazwę użytkownika osoby, którą chcesz dodać do listy znajomych"

#~ msgid ""
#~ "Enter the name of a room you want to join. If the room doesn't exist, it "
#~ "will be created."
#~ msgstr ""
#~ "Wprowadź nazwę pokoju, do którego chcesz dołączyć. Jeśli taki pokój nie "
#~ "istnieje, to zostanie utworzony."

#~ msgid "Show Log History Pane"
#~ msgstr "Pokaż okno historii dziennika"

#~ msgid "Save _Picture"
#~ msgstr "Zapisz _zdjęcie"

#, python-format
#~ msgid ""
#~ "Filtered out incorrect search result %(filepath)s from user %(user)s for "
#~ "search query \"%(query)s\""
#~ msgstr ""
#~ "Odfiltrowano niepoprawny wynik wyszukiwania %(filepath)s od użytkownika "
#~ "%(user)s dla zapytania \"%(query)s\""

#~ msgid ""
#~ "Certain clients don't send search results if special characters are "
#~ "included."
#~ msgstr ""
#~ "Niektórzy klienci nie zwracają wyników wyszukiwania jeśli znaki specjalne "
#~ "są w nich zawarte."

#~ msgid "Remove special characters from search terms"
#~ msgstr "Usuń znaki specjalne z wyszukiwanych fraz"

#, python-format
#~ msgid "Trouble executing '%s'"
#~ msgstr "Problem z wywołaniem '%s'"

#, python-format
#~ msgid "Trouble executing on folder: %s"
#~ msgstr "Problem z wywołaniem na folderze: %s"

#~ msgid "Disallowed extension"
#~ msgstr "Niedozwolone rozszerzenie"

#~ msgid "Too many files"
#~ msgstr "Za dużo plików"

#~ msgid "Too many megabytes"
#~ msgstr "Za dużo megabajtów"

#~ msgid "Server does not permit performing wishlist searches at this time"
#~ msgstr "Serwer nie pozwala w tej chwili na przeszukiwanie listy życzeń"

#~ msgid ""
#~ "File transfer speeds depend on users you are downloading from. Certain "
#~ "users will be faster, while others will be slow."
#~ msgstr ""
#~ "Prędkość transferu zależy od użytkownika od którego pobierasz. Niektórzy "
#~ "użytkownicy będą szybsi od innych."

#~ msgid "Started Downloads"
#~ msgstr "Rozpoczęte pobierania"

#~ msgid "Started Uploads"
#~ msgstr "Rozpoczęte wysyłania"

#~ msgid "Replace censored letters with:"
#~ msgstr "Zastąp ocenzurowane słowa przez:"

#~ msgid "Censored Patterns"
#~ msgstr "Cenzurowane frazy"

#~ msgid "Replacements"
#~ msgstr "Zamienniki"

#~ msgid ""
#~ "If a user on the Soulseek network searches for a file that exists in your "
#~ "shares, search results will be sent to the user."
#~ msgstr ""
#~ "Jeśli użytkownik sieci Soulseek szuka pliku, który udostępniasz, wynik "
#~ "ten zostanie wysłany użytkownikowi."

#~ msgid "Send to Player"
#~ msgstr "Wyślij do odtwarzacza"

#~ msgid "Send to _Player"
#~ msgstr "Wyślij do _odtwarzacza"

#~ msgid "_Open in File Manager"
#~ msgstr "Otwórz w eksploratorze plików"

#~ msgid "Media player command:"
#~ msgstr "Komenda odtwarzacza multimedialnego:"

#, python-format
#~ msgid ""
#~ "Unable to save download to username subfolder, falling back to default "
#~ "download folder. Error: %s"
#~ msgstr ""
#~ "Nie można zapisać pliku do podfolderu nazwy użytkownika, powraca do "
#~ "domyślnego folderu pobierania. Błąd: %s"

#~ msgid "Buddy-only"
#~ msgstr "Tylko dla znajomych"

#~ msgid "Share with buddies only"
#~ msgstr "Współdziel tylko ze znajomymi"

#~ msgid "_Quit…"
#~ msgstr "_Zamknij…"

#~ msgid "_Configure Shares"
#~ msgstr "_Konfiguracja udostępnionych zasobów"

#~ msgid "Remote file error"
#~ msgstr "Błąd zdalnego pliku"

#, python-format
#~ msgid "Cannot find %(option1)s or %(option2)s, please install either one."
#~ msgstr ""
#~ "Nie można znaleźć %(option1)s lub %(option2)s, proszę zainstalować któryś "
#~ "z nich."

#, python-format
#~ msgid "Failed to process the following databases: %(names)s"
#~ msgstr "Błąd podczas procesowania następujących baz danych: %(names)s"

#, python-format
#~ msgid "Failed to send number of shared files to the server: %s"
#~ msgstr "Nie udało się wysłać liczby udostępnionych plików do serwera: %s"

#~ msgid "Quit / Run in Background"
#~ msgstr "Zamknij / Działaj w tle"

#~ msgid "Limit buddy-only shares to trusted buddies"
#~ msgstr "Ogranicz udostępnianie tylko dla znajomych do zaufanych znajomych"

#~ msgid "Shared"
#~ msgstr "Udostępnione zasoby"

#~ msgid "Search Files and Folders"
#~ msgstr "Wyszukiwanie plików i folderów"

#~ msgid "Out of Date"
#~ msgstr "Nieaktualna"

#, python-format
#~ msgid "Version %(version)s is available, released on %(date)s"
#~ msgstr "Dostępna jest wersja %(version)s, wydana w dniu %(date)s"

#, python-format
#~ msgid "You are using a development version of %s"
#~ msgstr "Używasz wersji rozwojowej %s"

#, python-format
#~ msgid "You are using the latest version of %s"
#~ msgstr "Używasz najnowszej wersji %s"

#~ msgid "Latest Version Unknown"
#~ msgstr "Najnowsza wersja Nieznana"

#~ msgid "Prefer Dark _Mode"
#~ msgstr "Preferuj ciemny _tryb"

#~ msgid "Show _Log History Pane"
#~ msgstr "Pokaż panel _dziennika"

#~ msgid "Buddy List in Separate Tab"
#~ msgstr "Lista znajomych w osobnej karcie"

#~ msgid "Buddy List Always Visible"
#~ msgstr "Lista znajomych zawsze widoczna"

#~ msgid "_View"
#~ msgstr "_Widok"

#~ msgid "_Open Log Folder"
#~ msgstr "_Otwórz folder dziennika"

#~ msgid "Open _Transfer Log"
#~ msgstr "Otwórz dziennik transferu"

#~ msgid "_Browse Folder(s)"
#~ msgstr "_Przeglądaj folder(y)"

#~ msgid "Kibibytes (2^10 bytes) per second."
#~ msgstr "Kibibajty (2^10 bajtów) na sekundę."

#~ msgid "Sent to users as the reason for being geo blocked."
#~ msgstr "Wyślij użytkownikom jako powód zablokowania."

#~ msgid "Sent to users as the reason for being banned."
#~ msgstr "Wyślij użytkownikom jako powód zablokowania."

#~ msgid ""
#~ "Once you interact with the application being away, status will be set to "
#~ "online."
#~ msgstr ""
#~ "Gdy wejdziesz w interakcję z aplikacją, status zostanie ustawiony na "
#~ "dostępny."

#~ msgid "Each user may queue a maximum of either:"
#~ msgstr "Każdy użytkownik może ustawić się w kolejce maksymalnie:"

#~ msgid "Mebibytes (2^20 bytes)."
#~ msgstr "Mebibajty (2^20 bajtów)."

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "files"
#~ msgstr "pliki"

#~ msgid "Queue Behavior"
#~ msgstr "Zachowanie kolejki"

#~ msgid ""
#~ "If disabled, slots will automatically be determined by available "
#~ "bandwidth limitations."
#~ msgstr ""
#~ "Jeśli wyłączone, sloty będą automatycznie ustawiane na podstawie "
#~ "dostępnych limitów przepustowości."

#~ msgid "Note that the operating system's theme may take precedence."
#~ msgstr ""
#~ "Należy pamiętać, że motyw systemu operacyjnego może mieć pierwszeństwo."

#~ msgid "By default the leftmost tab is activated at startup"
#~ msgstr "Domyślnie lewa karta jest aktywowana podczas uruchamiania"

#, python-format
#~ msgid "Quit %(program)s %(version)s, %(status)s!"
#~ msgstr "Zamknij %(program)s %(version)s, %(status)s!"

#~ msgid "terminated"
#~ msgstr "zakończono"

#~ msgid "done"
#~ msgstr "gotowe"

#~ msgid "Remember choice"
#~ msgstr "Zapamiętaj wybór"

#~ msgid "Kosovo"
#~ msgstr "Kosowo"

#~ msgid "Unknown Network Interface"
#~ msgstr "Nieznany interfejs sieciowy"

#~ msgid "Listening Port Unavailable"
#~ msgstr "Port nasłuchujący niedostępny"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr "Serwer jest rozłączony albo nie odpowiada, ponawianie za %i sekund"

#~ msgid ""
#~ "Nicotine+ uses peer-to-peer networking to connect to other users. In "
#~ "order to allow users to connect to you without trouble, an open listening "
#~ "port is crucial."
#~ msgstr ""
#~ "Nicotine+ używa sieci peer-to-peer do łączenia się z innymi "
#~ "użytkownikami. Otwarty port nasłuchiwania jest kluczowy, aby inni "
#~ "użytkownicy łączyli się z Tobą bez problemu."

#~ msgid "--- disconnected ---"
#~ msgstr "--- rozłączony ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- połączony ponownie ---"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Earth"
#~ msgstr "Ziemia"

#~ msgid "Czech Republic"
#~ msgstr "Czechy"

#~ msgid "Turkey"
#~ msgstr "Turcja"

#~ msgid "Joined Rooms "
#~ msgstr "Dołączony do pokoi "

#~ msgid "_Auto-join Room"
#~ msgstr "_Automatyczne dołączanie do pokoju"

#~ msgid "Escaped"
#~ msgstr "Uciekł"

#~ msgid "Escape filter"
#~ msgstr "Filtr ucieczki"

#~ msgid "Enter a text pattern and what to replace it with"
#~ msgstr "Wprowadź wzór tekstu i to, co ma go zastąpić"

#, python-format
#~ msgid "%(num)s folders found before rescan, rebuilding…"
#~ msgstr ""
#~ "Znaleziono %(num)s folderów przed ponownym skanowaniem, przebudowywanie…"

#, python-format
#~ msgid "No listening port is available in the specified port range %s–%s"
#~ msgstr ""
#~ "Żaden port nasłuchiwania nie jest dostępny w określonym zakresie portów "
#~ "%s–%s"

#~ msgid ""
#~ "Choose a range to select a listening port from. The first available port "
#~ "in the range will be used."
#~ msgstr ""
#~ "Wybierz zakres, z którego chcesz wybrać port nasłuchujący. Zostanie użyty "
#~ "pierwszy dostępny port w zakresie."

#~ msgid "First Port"
#~ msgstr "Pierwszy port"

#~ msgid "to"
#~ msgstr "do"

#~ msgid "Last Port"
#~ msgstr "Ostatni port"

#, python-format
#~ msgid "Cannot find %s or newer, please install it."
#~ msgstr "Nie można znaleźć %s lub nowszego, zainstaluj go."

#~ msgid ""
#~ "Cannot import the Gtk module. Bad install of the python-gobject module?"
#~ msgstr ""
#~ "Nie można zaimportować modułu Gtk. Niepoprawna instalacja modułu python-"
#~ "gobject?"

#, python-format
#~ msgid ""
#~ "You are using an unsupported version of GTK %(major_version)s. You should "
#~ "install GTK %(complete_version)s or newer."
#~ msgstr ""
#~ "Używasz niewspieranej wersji GTK %(major_version)s. Wymagana wersja, to "
#~ "GTK %(complete_version)s lub nowsza."

#~ msgid "User Info"
#~ msgstr "Informacje o użytkowniku"

#~ msgid "Zoom 1:1"
#~ msgstr "Powiększenie 1:1"

#~ msgid "Zoom In"
#~ msgstr "Powiększenie"

#~ msgid "Zoom Out"
#~ msgstr "Pomniejszenie"

#~ msgid "Show User I_nfo"
#~ msgstr "Pokaż informacje o użytkowniku"

#~ msgid "Request User's Info"
#~ msgstr "Pobierz informacje o użytkowniku"

#~ msgid "Request User's Shares"
#~ msgstr "Pobierz zasoby użytkownika"

#~ msgid "Request User Info"
#~ msgstr "Pobierz informacje o użytkowniku"

#~ msgid "Enter the name of the user whose info you want to see:"
#~ msgstr "Wprowadź nazwę użytkownika, którego informacje chcesz otrzymać:"

#~ msgid "Request Shares List"
#~ msgstr "Pobierz listę zasobów użytkownika"

#, python-format
#~ msgid "Invalid Soulseek URL: %s"
#~ msgstr "Niepoprawny URL Soulseek: %s"

#~ msgid ""
#~ "Users on the Soulseek network will be able to download files from folders "
#~ "you share. Sharing files is crucial for the health of the Soulseek "
#~ "network."
#~ msgstr ""
#~ "Użytkownicy sieci Soulseek będą mogli pobierać pliki z udostępnionych "
#~ "folderów. Udostępnianie zasobów jest kluczowym składnikiem działania "
#~ "sieci Soulseek."

#~ msgid "Update I_nfo"
#~ msgstr "Zaktualizuj I_nformacje"

#~ msgid "Chat Room Commands"
#~ msgstr "Lista komend w pokojach rozmów"

#~ msgid "/join /j 'room'"
#~ msgstr "/join /j 'pokój'"

#~ msgid "Join room 'room'"
#~ msgstr "Dołącz do pokoju 'pokój'"

#~ msgid "/me 'message'"
#~ msgstr "/me 'wiadomość'"

#~ msgid "Display the Now Playing script's output"
#~ msgstr "Wyświetl wynik Teraz odtwarzanie"

#~ msgid "/add /ad 'user'"
#~ msgstr "/add /ad 'użytkownik'"

#~ msgid "Add user 'user' to your buddy list"
#~ msgstr "Dodaj użytkownika 'user' do listy znajomych"

#~ msgid "/rem /unbuddy 'user'"
#~ msgstr "/rem /unbuddy 'użytkownik'"

#~ msgid "Remove user 'user' from your buddy list"
#~ msgstr "Usuń użytkownika 'user' z listy znajomych"

#~ msgid "/ban 'user'"
#~ msgstr "/ban 'użytkownik'"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "Dodaj użytkownika 'użytkownik' do listy zablokowanych"

#~ msgid "/unban 'user'"
#~ msgstr "/unban 'użytkownik'"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "Usuń użytkownika 'użytkownik' z zablokowanych"

#~ msgid "/ignore 'user'"
#~ msgstr "/ignore 'użytkownik'"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "Dodaj użytkownika 'użytkownik' do listy ignorowanych"

#~ msgid "/unignore 'user'"
#~ msgstr "/unignore 'użytkownik'"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "Usuń użytkownika 'użytkownik' z listy ignorowanych"

#~ msgid "/browse /b 'user'"
#~ msgstr "/browse /b 'użytkownik'"

#~ msgid "/whois /w 'user'"
#~ msgstr "/whois /w 'użytkownik'"

#~ msgid "Request info for 'user'"
#~ msgstr "Żądanie informacji dla 'user'"

#~ msgid "/ip 'user'"
#~ msgstr "/ip 'użytkownik'"

#~ msgid "Show IP for user 'user'"
#~ msgstr "Pokaż IP użytkownika 'użytkownik'"

#~ msgid "/search /s 'query'"
#~ msgstr "/search /s 'zapytanie'"

#~ msgid "Start a new search for 'query'"
#~ msgstr "Zacznij szukać frazy 'zapytanie'"

#~ msgid "/rsearch /rs 'query'"
#~ msgstr "/rsearch /rs 'zapytanie'"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "Zacznij szukać frazy 'zapytanie' wśród dołączonych pokoi"

#~ msgid "/bsearch /bs 'query'"
#~ msgstr "/bsearch /bs 'zapytanie'"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "Zacznij szukać frazy 'zapytanie' wśród Listy znajomych"

#~ msgid "/usearch /us 'user' 'query'"
#~ msgstr "/usearch /us 'użytkownik' 'zapytanie'"

#~ msgid "/msg 'user' 'message'"
#~ msgstr "/msg 'użytkownik' 'wiadomość'"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "Wyślij wiadomość 'wiadomość' do użytkownika 'użytkownik'"

#~ msgid "/pm 'user'"
#~ msgstr "/pm 'użytkownik'"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr "Otwórz prywatny czat z użytkownikiem 'użytkownik'"

#~ msgid "Private Chat Commands"
#~ msgstr "Lista komend w prywatnych rozmowach"

#~ msgid "Add user to your ban list"
#~ msgstr "Dodaj użytkownika do listy zablokowanych"

#~ msgid "Add user to your ignore list"
#~ msgstr "Dodaj użytkownika do listy ignorowanych"

#~ msgid "Browse shares of user"
#~ msgstr "Przeglądaj zasoby użytkownika"

#~ msgid ""
#~ "For order-insensitive filtering, as well as filtering several exact "
#~ "phrases, vertical bars can be used to separate phrases and words.\n"
#~ "    Example: Remix|Instrumental|Dub Mix"
#~ msgstr ""
#~ "W przypadku filtrowania bez względu na kolejność, jak również filtrowania "
#~ "kilku dokładnych fraz, można użyć pionowych pasków do oddzielenia fraz i "
#~ "słów.\n"
#~ "    Przykład: Remix|Instrumentalny|Dub Mix"

#~ msgid "To exclude non-audio files use !0 in the duration filter."
#~ msgstr ""
#~ "Aby wykluczyć pliki inne niż audio, użyj !0 w filtrze czasu trwania."

#~ msgid "Filters files based upon users' geographical location."
#~ msgstr "Filtruje pliki na podstawie położenia geograficznego użytkowników."

#~ msgid "To view the full results again, simply clear all active filters."
#~ msgstr ""
#~ "Aby ponownie wyświetlić pełne wyniki, po prostu wyczyść wszystkie aktywne "
#~ "filtry."

#~ msgid "See the preferences to set default search result filter options."
#~ msgstr ""
#~ "Zobacz preferencje, aby ustawić domyślne opcje filtrów wyników "
#~ "wyszukiwania."

#~ msgid "File size"
#~ msgstr "Rozmiar pliku"

#~ msgid "Clear Active Filters"
#~ msgstr "Wyczyść aktywne filtry"

#~ msgid "Cycle through completions when pressing tab-key"
#~ msgstr ""
#~ "Przełączanie pomiędzy uzupełnieniami po naciśnięciu klawisza tabulacji"

#~ msgid "Hide drop-down when only one matches"
#~ msgstr "Schowaj listę rozwijaną jeśli jest jedno dopasowanie"

#~ msgid "Download folders in reverse alphanumerical order"
#~ msgstr "Pobierz foldery w odwrotnej kolejności alfabetycznej"

#~ msgid "Incomplete file folder:"
#~ msgstr "Folder niekompletnych plików:"

#~ msgid "Download folder:"
#~ msgstr "Folder pobierania:"

#~ msgid "Save buddies' uploads to:"
#~ msgstr "Zapisz wysyłania znajomych do:"

#~ msgid "Use UPnP to forward listening port"
#~ msgstr "Użyj UPnP, aby przekierować port nasłuchujący"

#~ msgid ""
#~ "Share folders with every Soulseek user or buddies, allowing contents to "
#~ "be downloaded directly from your device. Hidden files are never shared."
#~ msgstr ""
#~ "Udostępniaj foldery każdemu użytkownikowi Soulseek lub znajomym, "
#~ "umożliwiając pobieranie zawartości bezpośrednio z Twojego urządzenia. "
#~ "Ukryte pliki nigdy nie są udostępniane."

#~ msgid "Secondary Tabs"
#~ msgstr "Karty dodatkowe"

#~ msgid "Chat room tab bar position:"
#~ msgstr "Pozycja paska kart w pokoju rozmów:"

#~ msgid "Private chat tab bar position:"
#~ msgstr "Pozycja paska w czacie prywatnym:"

#~ msgid "Search tab bar position:"
#~ msgstr "Pozycja karty szukania:"

#~ msgid "User info tab bar position:"
#~ msgstr "Pozycja karty informacji o użytkowniku:"

#~ msgid "User browse tab bar position:"
#~ msgstr "Pozycja karty przeglądania użytkownika:"

#~ msgid "Tab Labels"
#~ msgstr "Etykiety kart"

#~ msgid "_Refresh Info"
#~ msgstr "Odśwież informacje"

#~ msgid "Block IP Address"
#~ msgstr "Zablokuj adres IP"

#~ msgid "Connected"
#~ msgstr "Połączony"

#~ msgid "Disconnected"
#~ msgstr "Rozłączony"

#~ msgid "Disconnected (Tray)"
#~ msgstr "Rozłączony (zasobnik)"

#~ msgid "User(s)"
#~ msgstr "Użytkownicy"

#, python-format
#~ msgid "Alias \"%s\" returned nothing"
#~ msgstr "Alias \"%s\" nic nie zwrócił"

#~ msgid "Alternative Speed Limits"
#~ msgstr "Alternatywne limity prędkości"

#~ msgid "Last played"
#~ msgstr "Ostatnio odtwarzane"

#~ msgid "Playing now"
#~ msgstr "Teraz odtwarzanie"

#, python-format
#~ msgid "Cannot download file to %(path)s: %(error)s"
#~ msgstr "Nie można pobrać pliku do %(path)s: %(error)s"

#, python-format
#~ msgid "Error code %(code)s: %(description)s"
#~ msgstr "Kod błędu %(code)s: %(description)s"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "Brak podanego aliasu (%s)"

#~ msgid "Aliases:"
#~ msgstr "Aliasy:"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "Usunięto alias %(alias)s: %(action)s\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "Nie znaleziono aliasu (%(alias)s)\n"

#~ msgid "Aliases"
#~ msgstr "Aliasy"

#~ msgid "/alias /al 'command' 'definition'"
#~ msgstr "/alias /al 'komenda' 'definicja'"

#~ msgid "Add a new alias"
#~ msgstr "Dodaj nowy alias"

#~ msgid "/unalias /un 'command'"
#~ msgstr "/unalias /un 'komenda'"

#~ msgid "Remove an alias"
#~ msgstr "Usuń alias"

#~ msgid "Chat History"
#~ msgstr "Historia czatów"

#~ msgid "Command aliases"
#~ msgstr "Aliasy komend"

#~ msgid "Limit download speed to (KiB/s):"
#~ msgstr "Ogranicz prędkość pobierania do (KiB/s):"

#~ msgid "Author(s):"
#~ msgstr "Autorzy:"

#~ msgid "Limit upload speed to (KiB/s):"
#~ msgstr "Ogranicz prędkość wysyłania do (KiB/s):"

#~ msgid "Tabs show user status icons instead of status text"
#~ msgstr "Karty pokazują ikony statusu użytkownika zamiast tekstu statusu"

#~ msgid "Show file path tooltips in file list views"
#~ msgstr "Pokaż tooltip ze ścieżką pliku w widoku listy plików"

#~ msgid "Colored and clickable usernames"
#~ msgstr "Kolorowe i klikalne nazwy użytkowników"

#~ msgid "Notification changes the tab's text color"
#~ msgstr "Powiadomienie zmienia kolor tekstu karty"

#~ msgid "Cancel"
#~ msgstr "Anuluj"

#~ msgid "OK"
#~ msgstr "OK"

#~ msgid "_Add to Buddy List"
#~ msgstr "_Dodaj do Listy znajomych"

#~ msgid "use non-default user data directory for e.g. list of downloads"
#~ msgstr "użyj niestandardowego katalogu użytkownika dla pobieranych"

#, python-format
#~ msgid "Unknown config section '%s'"
#~ msgstr "Nieznana sekcja konfiguracji '%s'"

#, python-format
#~ msgid "Unknown config option '%(option)s' in section '%(section)s'"
#~ msgstr "Nieznana opcja konfiguracji '%(option)s' w sekcji '%(section)s'"

#, python-format
#~ msgid "Version %s is available"
#~ msgstr "Dostępna jest wersja %s"

#, python-format
#~ msgid "released on %s"
#~ msgstr "wydana %s"

#~ msgid "Nicotine+ is running in the background"
#~ msgstr "Nicotine+ działa w tle"

#, python-format
#~ msgid "Private message from %s"
#~ msgstr "Prywatna wiadomość od %s"

#~ msgid "Aborted"
#~ msgstr "Przerwane"

#~ msgid "Blocked country"
#~ msgstr "Zablokowany kraj"

#~ msgid "Finished / Aborted"
#~ msgstr "Zakończone / Przerwane"

#~ msgid "Close tab"
#~ msgstr "Zamknij kartę"

#, python-format
#~ msgid "You've been mentioned in the %(room)s room"
#~ msgstr "Ktoś wspomniał o tobie w pokoju %(room)s"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "Komenda %s nie została rozpoznana"

#, python-format
#~ msgid "(Warning: %(realuser)s is attempting to spoof %(fakeuser)s) "
#~ msgstr "(Uwaga: %(realuser)s próbuje podszyć się pod %(fakeuser)s) "

#, python-format
#~ msgid ""
#~ "The network interface you specified, '%s', does not exist. Change or "
#~ "remove the specified network interface and restart Nicotine+."
#~ msgstr ""
#~ "Wybrany interfejs sieciowy, '%s', nie istnieje. Zmień lub usuń interfejs "
#~ "i zrestartuj Nicotine+."

#~ msgid ""
#~ "The range you specified for client connection ports was {}-{}, but none "
#~ "of these were usable. Increase and/or "
#~ msgstr ""
#~ "Zakres portów jaki został wybrany to {}-{}, ale żaden z nich nie był do "
#~ "użytku. Zmień zakres "

#~ msgid ""
#~ "Note that part of your range lies below 1024, this is usually not allowed "
#~ "on most operating systems with the exception of Windows."
#~ msgstr ""
#~ "Miej na uwadze, że ten zakres jest poniżej 1024. Jest to zakres, który "
#~ "jest niezalecany w większości systemów operacyjnych."

#, python-format
#~ msgid "Rescan progress: %s"
#~ msgstr "Postęp ponownego skanowania: %s"

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "Błąd OS: %s"

#~ msgid "UPnP is not available on this network"
#~ msgstr "UPnP nie jest dostępny w tej sieci"

#, python-format
#~ msgid "Failed to map the external WAN port: %(error)s"
#~ msgstr "Błąd mapowania zewnętrznego portu WAN: %(error)s"

#~ msgid "Room wall"
#~ msgstr "Ściana pokoju"

#~ msgid ""
#~ "The default listening port '2234' works fine in most cases. If you need "
#~ "to use a different port, you will be able to modify it in the preferences "
#~ "later."
#~ msgstr ""
#~ "Domyślny port nasłuchiwania '2234' powinien wystarczyć w większości "
#~ "przypadków. Jeśli potrzebujesz użyć innego portu, możesz to zmienić w "
#~ "Ustawieniach."

#~ msgid "Show users with similar interests"
#~ msgstr "Pokaż użytkowników o podobnych zainteresowaniach"

#~ msgid "Menu"
#~ msgstr "Menu"

#~ msgid "Expand / Collapse all"
#~ msgstr "Rozwiń / Zwiń wszystko"

#~ msgid "Configure shares"
#~ msgstr "Konfiguracja udostępnionych zasobów"

#~ msgid "Create or join room…"
#~ msgstr "Utwórz lub dołącz do pokoju…"

#~ msgid "_Room List"
#~ msgstr "_Lista pokoi"

#~ msgid "Enable alternative download and upload speed limits"
#~ msgstr "Włącz alternatywne limity prędkości pobierania i wysyłania"

#~ msgid "Show log history"
#~ msgstr "Pokaż historię logów"

#~ msgid "Result grouping mode"
#~ msgstr "Tryb grupowania wyników"

#~ msgid "Free slot"
#~ msgstr "Wolny slot"

#~ msgid "Save shares list to disk"
#~ msgstr "Zapisz listę zasobów na dysk"

#~ msgid "_Away"
#~ msgstr "_Zaraz wracam"

#, python-format
#~ msgid "Failed to load ui file %(file)s: %(error)s"
#~ msgstr "Problem z załadowaniem pliku ui %(file)s: %(error)s"

#~ msgid ""
#~ "Attempting to reset index of shared files due to an error. Please rescan "
#~ "your shares."
#~ msgstr ""
#~ "Próba zresetowania indeksu udostępnionych plików z powodu błędu. "
#~ "Przeskanuj ponownie swoje udziały."

#~ msgid ""
#~ "File index of shared files could not be accessed. This could occur due to "
#~ "several instances of Nicotine+ being active simultaneously, file "
#~ "permission issues, or another issue in Nicotine+."
#~ msgstr ""
#~ "Nie można uzyskać dostępu do indeksu współdzielonych plików. Może się to "
#~ "zdarzyć z powodu kilku instancji Nicotine+ aktywnych jednocześnie, "
#~ "problemów z uprawnieniami do plików lub innego problemu w Nicotine+."

#~ msgid "Nicotine+ is a Soulseek client"
#~ msgstr "Nicotine+ jest klientem Soulseek"

#~ msgid "enable the tray icon"
#~ msgstr "włącz ikonę zasobnika"

#~ msgid "disable the tray icon"
#~ msgstr "wyłącz ikonę zasobnika"

#~ msgid "Get Soulseek Privileges…"
#~ msgstr "Zdobądź przywileje Soulseek…"

#, python-format
#~ msgid ""
#~ "Public IP address is <b>%(ip)s</b> and active listening port is "
#~ "<b>%(port)s</b>"
#~ msgstr ""
#~ "Publiczny adres IP to <b>%(ip)s</b> a aktywny port nasłuchiwania to "
#~ "<b>%(port)s</b>"

#~ msgid "unknown"
#~ msgstr "nieznane"

#~ msgid "Notification"
#~ msgstr "Powiadomienie"

#~ msgid "Length"
#~ msgstr "Długość"

#, python-format
#~ msgid "Picture not saved, %s already exists."
#~ msgstr "Obraz nie został zapisany, %s już istnieje."

#~ msgid "_Open"
#~ msgstr "_Otwórz"

#~ msgid "_Save"
#~ msgstr "_Zapisz"

#, python-format
#~ msgid "Failed to load plugin '%s', could not find it."
#~ msgstr "Nie udało się załadować wtyczki '%s', nie można jej znaleźć."

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "Błąd I/O: %s"

#, python-format
#~ msgid "Failed to open file path: %s"
#~ msgstr "Błąd otwierania ścieżki pliku: %s"

#, python-format
#~ msgid "Failed to open URL: %s"
#~ msgstr "Błąd otwierania URL: %s"

#~ msgid "_Log Conversation"
#~ msgstr "_Log konwersacji"

#~ msgid "Result Filter List"
#~ msgstr "Lista filtrowanych wyników"

#~ msgid "Prepend < or > to find files less/greater than the given value."
#~ msgstr ""
#~ "Dopisz < lub > aby znaleźć pliki mniejsze/większe od podanej wartości."

#~ msgid ""
#~ "VBR files display their average bitrate and are typically lower in "
#~ "bitrate than a compressed 320 kbps CBR file of the same audio quality."
#~ msgstr ""
#~ "Pliki VBR wyświetlają swój średni bitrate i zazwyczaj mają niższy bitrate "
#~ "niż skompresowany plik CBR 320 kbps o tej samej jakości dźwięku."

#~ msgid "Like Size above, =, <, and > can be used."
#~ msgstr ""
#~ "Podobnie jak w przypadku wielkości powyżej, można stosować =, < i >."

#~ msgid ""
#~ "Prevent write access by other programs for files being downloaded (turn "
#~ "off for NFS)"
#~ msgstr ""
#~ "Zapobieganie dostępowi do zapisu przez inne programy dla pobieranych "
#~ "plików (wyłącz dla NFS)"

#~ msgid "Where incomplete downloads are temporarily stored."
#~ msgstr "Gdzie niekompletne pobierania są tymczasowo przechowywane."

#~ msgid ""
#~ "Where buddies' uploads will be stored (with a subfolder created for each "
#~ "buddy)."
#~ msgstr ""
#~ "Gdzie pliki przesłane przez znajomych będą zachowane (w podfolderze dla "
#~ "każdego znajomego)."

#~ msgid "Toggle away status after minutes of inactivity:"
#~ msgstr "Przełącz w tryb nieobecny po (min):"

#~ msgid "Protocol:"
#~ msgstr "Protokół:"

#~ msgid "Icon theme folder (requires restart):"
#~ msgstr "Folder ze stylami ikon (wymaga restartu):"

#~ msgid "Establishing connection"
#~ msgstr "Nawiązywanie połączenia"

#~ msgid "Clear Groups"
#~ msgstr "Wyczyść grupy"

#~ msgid "User List"
#~ msgstr "Lista użytkowników"

#~ msgid "_Reset Statistics…"
#~ msgstr "_Zresetuj statystyki…"

#~ msgid "Clear _Downloads…"
#~ msgstr "Wyczyść pobierania…"

#~ msgid "Clear Uploa_ds…"
#~ msgstr "Wyczyść wysłane…"

#~ msgid "Block User's IP Address"
#~ msgstr "Z_ablokuj adres IP tego użytkownika"

#~ msgid "Ignore User's IP Address"
#~ msgstr "Ignoruj adres IP tego użytkownika"

#~ msgid "Usernames"
#~ msgstr "Nazwy użytkowników"

#~ msgid "Display logged chat room messages when a room is rejoined"
#~ msgstr ""
#~ "Wyświetlaj zalogowane wiadomości czatu gdy dołączasz ponownie do pokoju"

#~ msgid "Queue Position"
#~ msgstr "Pozycja w kolejce"

#, python-format
#~ msgid ""
#~ "User %(user)s is directly searching for \"%(query)s\", returning %(num)i "
#~ "results"
#~ msgstr ""
#~ "Użytkownik %(user)s szuka bezpośrednio \"%(query)s\", zwrócono %(num)i "
#~ "wyników"

#~ msgid "Room wall (personal message set)"
#~ msgstr "Ściana pokoju (własna wiadomość)"

#~ msgid "Your config file is corrupt"
#~ msgstr "Plik ustawień jest uszkodzony"

#, python-format
#~ msgid ""
#~ "We're sorry, but it seems your configuration file is corrupt. Please "
#~ "reconfigure Nicotine+.\n"
#~ "\n"
#~ "We renamed your old configuration file to\n"
#~ "%(corrupt)s\n"
#~ "If you open this file with a text editor you might be able to rescue some "
#~ "of your settings."
#~ msgstr ""
#~ "Przepraszamy, ale plik ustawień jest uszkodzony. Proszę ponownie "
#~ "skonfigurować Nicotine+.\n"
#~ "\n"
#~ "Zmieniliśmy nazwę pliku Twojej starej konfiguracji na\n"
#~ "%(corrupt)s\n"
#~ "Jeśli otworzysz ten plik przy pomocy edytora tekstu, to będziesz mógł "
#~ "odzyskać część swoich ustawień."

#~ msgid "User Description"
#~ msgstr "Opis użytkownika"

#~ msgid "User Interests"
#~ msgstr "Zainteresowania użytkownika"

#~ msgid "User Picture"
#~ msgstr "Zdjęcie użytkownika"

#~ msgid "Search Wishlist"
#~ msgstr "Szukaj na Liście życzeń"

#, python-format
#~ msgid "Loading Nicotine+ %(nic_version)s"
#~ msgstr "Ładowanie Nicotine+ %(nic_version)s"

#, python-format
#~ msgid "Using Python %(py_version)s"
#~ msgstr "Korzystanie z Pythona %(py_version)s"

#, python-format
#~ msgid "Quitting Nicotine+ %(version)s, %(status)s…"
#~ msgstr "Zamykanie Nicotine+ %(version)s, %(status)s…"

#, python-format
#~ msgid "Quit Nicotine+ %(version)s, %(status)s!"
#~ msgstr "Zamknij Nicotine+ %(version)s, %(status)s!"

#~ msgid "User:"
#~ msgstr "Użytkownik:"

#, python-format
#~ msgid "All %(ext)s"
#~ msgstr "Wszystkie %(ext)s"

#, python-format
#~ msgid "%(number)2s files "
#~ msgstr "%(number)2s pliki "

#~ msgid "Allow regular expressions for the filter's include and exclude"
#~ msgstr "Użyj wyrażeń regularnych dla filtrów wejściowych i wyjściowych"

#~ msgid "Change Primary Tab"
#~ msgstr "Zmień główną zakładkę"

#~ msgid "Remember previous primary tab on startup"
#~ msgstr "Zapamiętaj poprzednią główną kartę podczas uruchamiania"

#~ msgid "Start with Search Files by default."
#~ msgstr "Rozpocznij od domyślnego wyszukiwania plików."

#~ msgid "Visible primary tabs:"
#~ msgstr "Widoczne karty podstawowe:"

#~ msgid "Quit…"
#~ msgstr "Zamknij…"

#~ msgid "Close Nicotine+?"
#~ msgstr "Zamknąć Nicotine+?"

#~ msgid "Do you really want to exit Nicotine+?"
#~ msgstr "Czy na pewno chcesz wyjść z Nicotine+?"

#~ msgid "Run in Background"
#~ msgstr "Uruchom w tle"

#~ msgid "_Online Notify"
#~ msgstr "_Powiadomienie dostępności"

#~ msgid "_Prioritize User"
#~ msgstr "_Nadaj priorytet użytkownikowi"

#~ msgid "_Trust User"
#~ msgstr "_Zaufany użytkownik"

#~ msgid "Request User's IP Address"
#~ msgstr "Pobierz adres IP użytkownika"

#~ msgid "Request IP Address"
#~ msgstr "Pobierz adres IP"

#~ msgid "Enter the name of the user whose IP address you want to see:"
#~ msgstr "Wprowadź nazwę użytkownika, którego adres IP chcesz otrzymać:"

#~ msgid "Downloaded"
#~ msgstr "Pobrane"

#, python-format
#~ msgid "Failed to add download %(filename)s to shared files: %(error)s"
#~ msgstr ""
#~ "Błąd podczas dodawania pobierania %(filename)s do udostępnionych plików: "
#~ "%(error)s"

#~ msgid "Automatically share completed downloads"
#~ msgstr "Automatycznie udostępniaj pobrane"

#~ msgid ""
#~ "The equivalent of adding your download folder as a public share, however "
#~ "files downloaded to this folder will be automatically accessible to "
#~ "others (no rescan required)."
#~ msgstr ""
#~ "Jest to odpowiednik dodania folderu pobierania jako publiczne "
#~ "udostępnienia, jednak pliki pobrane do tego folderu będą automatycznie "
#~ "dostępne dla innych (nie jest wymagane ponowne skanowanie)."

#~ msgid "Unable to Share Folder"
#~ msgstr "Nie można udostępnić folderu"

#~ msgid "The chosen virtual name is empty"
#~ msgstr "Wybrana wirtualna nazwa jest pusta"

#~ msgid "The chosen virtual name already exists"
#~ msgstr "Wybrana wirtualna nazwa jest już w użyciu"

#~ msgid "The chosen folder is already shared"
#~ msgstr "Wybrany folder jest już udostępniony"

#~ msgid "Set Virtual Name"
#~ msgstr "Ustaw wirtualną nazwę"

#, python-format
#~ msgid "Enter virtual name for '%(dir)s':"
#~ msgstr "Wprowadź wirtualną nazwę dla '%(dir)s':"

#, python-format
#~ msgid "Unable to show notification popup: %s"
#~ msgstr "Nie można wyświetlić okienka powiadomień: %s"

#~ msgid "The chosen virtual name is either empty or already exists"
#~ msgstr "Wybrana wirtualna nazwa jest pusta lub jest już w użyciu"

#~ msgid "The chosen folder is already shared."
#~ msgstr "Wybrany folder jest już udostępniony."

#, python-format
#~ msgid "%s Properties"
#~ msgstr "%s Właściwości"

#~ msgid "Finished rescanning shares"
#~ msgstr "Zakończono ponowne skanowanie zasobów"

#, python-format
#~ msgid "Error while scanning %(path)s: %(error)s"
#~ msgstr "Błąd podczas skanowania %(path)s: %(error)s"

#~ msgid "Plugin List"
#~ msgstr "Lista wtyczek"

#~ msgid "Enable sound for notification popups"
#~ msgstr "Odtwórz dźwięk dla wyskakujących okienek powiadomień"

#~ msgid "Show notification popups for:"
#~ msgstr "Pokaż wyskakujące okienka powiadomień dla:"

#~ msgid "Complete buddy names"
#~ msgstr "Dopełniaj nazwy przyjaciół"

#~ msgid "Complete usernames in chat rooms"
#~ msgstr "Dopełniaj nazwy użytkowników w pokojach rozmów"

#~ msgid "Complete room names"
#~ msgstr "Dopełniaj nazwy pokoi"

#~ msgid "Drop-down List"
#~ msgstr "Lista rozwijana"

#~ msgid "Display timestamps"
#~ msgstr "Wyświetl znaczniki czasu"

#~ msgid "Notification Popups"
#~ msgstr "Okienka powiadomień"

#~ msgid "Show notification popup when a file has finished downloading"
#~ msgstr "Pokaż powiadomienie kiedy zakończono pobieranie pliku"

#~ msgid "Show notification popup when a folder has finished downloading"
#~ msgstr "Pokaż powiadomienie kiedy zakończono pobieranie folderu"

#~ msgid "Show notification popup when you receive a private message"
#~ msgstr "Pokaż powiadomienie kiedy otrzymasz prywatną wiadomość"

#~ msgid "Show notification popup when someone sends a message in a chat room"
#~ msgstr "Pokaż powiadomienie kiedy ktoś wyśle wiadomość w pokoju rozmów"

#~ msgid "Show notification popup when you are mentioned in a chat room"
#~ msgstr "Pokaż powiadomienie kiedy ktoś o Tobie wspomni"

#~ msgid "Tray"
#~ msgstr "Zasobnik"

#~ msgid "Primary Tabs"
#~ msgstr "Karty podstawowe"

#~ msgid "Show _Log Pane"
#~ msgstr "Pokaż panel _dziennika"

#~ msgid "Show _Debug Log Controls"
#~ msgstr "Pokaż opcje debuggera"

#~ msgid "Handler"
#~ msgstr "Obsługa"

#~ msgid "Could not enable plugin."
#~ msgstr "Nie można włączyć wtyczki."

#~ msgid "Could not disable plugin."
#~ msgstr "Nie można wyłączyć wtyczki."

#~ msgid "Transfers"
#~ msgstr "Transfery"

#~ msgid "Ban List"
#~ msgstr "Lista zablokowanych"

#~ msgid "Ignore List"
#~ msgstr "Lista ignorowanych"

#~ msgid "Categories"
#~ msgstr "Kategorie"

#~ msgid "Upload Folder Recursive To…"
#~ msgstr "Wyślij folder rekursywnie do…"

#~ msgid "Download _Recursive"
#~ msgstr "Pobieranie _rekursywne"

#~ msgid "Download R_ecursive To…"
#~ msgstr "Pobierz r_ekursywnie do…"

#, python-format
#~ msgid ""
#~ "Error while attempting to display folder '%(folder)s', reported error: "
#~ "%(error)s"
#~ msgstr ""
#~ "Błąd podczas próby wyświetlenia folderu '%(folder)s', błąd: %(error)s"

#~ msgid "Select Destination for Downloading Folder with Subfolders from User"
#~ msgstr ""
#~ "Wybierz miejsce docelowe do pobrania folderu z podfolderami od użytkownika"

#~ msgid "Wishes"
#~ msgstr "Życzenia"

#~ msgid "Debug Logging"
#~ msgstr "Logowanie zdarzeń debuggera"

#~ msgid "Blocked IP Addresses"
#~ msgstr "Zablokowane adresy IP"

#~ msgid "Ignored IP Addresses"
#~ msgstr "Ignorowane adresy IP"

#~ msgid "Show notification icons on tabs"
#~ msgstr "Pokaż ikony powiadomień na kartach"

#~ msgid "Handler:"
#~ msgstr "Obsługa:"

#~ msgid ""
#~ "Displays the complete file path of a search result or file transfer when "
#~ "you hover a folder path or file name with your cursor."
#~ msgstr ""
#~ "Wyświetla pełną ścieżkę pliku wyniku wyszukiwania lub transferu pliku po "
#~ "najechaniu kursorem na ścieżkę folderu lub nazwę pliku."

#~ msgid "Show privately shared files in user shares"
#~ msgstr "Pokaż prywatnie udostępnione pliki w zasobach użytkownika"

#~ msgid ""
#~ "Other clients may offer an option to send privately shared files when you "
#~ "browse their shares. Folders containing such files are prefixed with "
#~ "'[PRIVATE FOLDER]', and are not downloadable unless the uploader gives "
#~ "explicit permission."
#~ msgstr ""
#~ "Inni klienci mogą oferować opcję wysyłania prywatnie udostępnianych "
#~ "plików podczas przeglądania ich zasobów. Foldery zawierające takie pliki "
#~ "są poprzedzone \"[PRIVATE FOLDER]\" i nie można ich pobrać, chyba że "
#~ "wysyłający udzieli wyraźnej zgody."

#~ msgid "Virtual Name"
#~ msgstr "Wirtualna nazwa"

#~ msgid "Edit Virtual Name"
#~ msgstr "Edytuj wirtualną nazwę"

#~ msgid "Copy Folder URL"
#~ msgstr "Kopiuj URL folderu"

#~ msgid "Download _To…"
#~ msgstr "Pobierz _do…"

#~ msgid "Download"
#~ msgstr "Pobieranie"

#~ msgid "Upload"
#~ msgstr "Wysyłanie"

#~ msgid "Upload Folder's Contents"
#~ msgstr "Zawartość wysyłanego folderu"

#~ msgid "Rename"
#~ msgstr "Zmień nazwę"

#~ msgid "File Lists"
#~ msgstr "Listy plików"

#~ msgid "Copy File Path"
#~ msgstr "Kopiuj ścieżkę pliku"

#~ msgid "R_emove Wish"
#~ msgstr "Usuń życzenie"

#~ msgid "About _Nicotine+"
#~ msgstr "O _Nicotine+"

#, python-format
#~ msgid "It appears '%s' is not a directory, not loading plugins."
#~ msgstr ""
#~ "Prawdopodobnie '%s' nie jest katalogiem, wtyczki nie zostały załadowane."

#~ msgid "Rescanning buddy shares…"
#~ msgstr "Ponowne skanowanie zasobów znajomego…"

#~ msgid "Finished rescanning buddy shares"
#~ msgstr "Zakończono ponowne skanowanie zasobów znajomych"

#~ msgid "Buddy List"
#~ msgstr "Lista znajomych"

#~ msgid "No description provided"
#~ msgstr "Nie podano opisu"

#, python-format
#~ msgid "Your buddy, %s, is attempting to upload file(s) to you."
#~ msgstr "Twój znajomy, %s, próbuje wysłać Ci plik(i)."

#, python-format
#~ msgid ""
#~ "%s is not allowed to send you file(s), but is attempting to, anyway. "
#~ "Warning Sent."
#~ msgstr ""
#~ "%s nie może Ci wysyłać plików, ale wciąż próbuje. Ostrzeżenie wysłane."

#~ msgid "Client Version"
#~ msgstr "Wersja klienta"

#, python-format
#~ msgid "How many days of privileges should user %s be gifted?"
#~ msgstr "Przez ile dni użytkownik %s ma być uprzywilejowany?"

#~ msgid ""
#~ "Buddies will have higher priority in the queue, the same as globally "
#~ "privileged users."
#~ msgstr ""
#~ "Znajomi będą mieli wyższy priorytet w kolejce, tak samo jak globalni "
#~ "uprzywilejowani użytkownicy."

#~ msgid "Privileged"
#~ msgstr "Uprzywilejowany"

#~ msgid "_Privileged"
#~ msgstr "_Uprzywilejowany"

#~ msgid "Comments"
#~ msgstr "Komentarze"

#~ msgid "Edit _Comments…"
#~ msgstr "Edytuj komentarze…"

#~ msgid ""
#~ "Creates subfolders based on the user you are downloading from, and stores "
#~ "the downloaded file / folder there."
#~ msgstr ""
#~ "Tworzy foldery na podstawie nazw użytkowników od których pobierane są "
#~ "pliki."

#~ msgid "Login Details"
#~ msgstr "Konto użytkownika"

#~ msgid "Advanced"
#~ msgstr "Zaawansowane"

#~ msgid "Port mapping renewal interval in hours:"
#~ msgstr "Mapowanie portów odnawia się po (godziny):"

#~ msgid "Enable buddy-only shares"
#~ msgstr "Włącz udostępnienia tylko dla przyjaciół"

#~ msgid "Files will be uploaded in the order they were queued."
#~ msgstr "Pliki będą wysłane w kolejności w jakiej zostały zakolejkowane."

#~ msgid "Rescanning normal shares…"
#~ msgstr "Ponowne skanowanie zasobów…"

#~ msgid "Finished rescanning public shares"
#~ msgstr "Zakończono ponowne skanowanie publiczne udostępnienia zasobów"

#~ msgid "Scanning Buddy Shares"
#~ msgstr "Skanowanie zasobów znajomych"

#~ msgid "_Rescan Public Shares"
#~ msgstr "_Przeskanuj ponownie publiczne udostępnienia zasobów"

#~ msgid "Rescan B_uddy Shares"
#~ msgstr "Przeskanuj ponownie udostępnienia zasobów z_najomych"

#~ msgid "Rescan Public Shares"
#~ msgstr "Przeskanuj ponownie publiczne udostępnienia zasobów"

#~ msgid "Rescan Buddy Shares"
#~ msgstr "Przeskanuj udostępnienia zasobów znajomych"

#~ msgid "Enables buddy shares that only users on your buddy list can access."
#~ msgstr ""
#~ "Włącza prywatne udostępnienia zasobów użytkownikom z listy znajomych."

#~ msgid "Mark each shared folder as buddy-only"
#~ msgstr "Oznacz każdy udostępniony folder jako tylko ten dla znajomych"

#~ msgid ""
#~ "Overrides the per-share option, useful if you temporarily need to prevent "
#~ "public access to shares."
#~ msgstr ""
#~ "Zastępuje opcję na udział, co jest przydatne, jeśli tymczasowo musisz "
#~ "uniemożliwić publiczny dostęp do udziałów."

#~ msgid ""
#~ "Only users marked as trusted on your buddy list can access your buddy-"
#~ "only shares."
#~ msgstr ""
#~ "Tylko użytkownicy oznaczeni jako zaufani na twojej liście znajomych mogą "
#~ "uzyskać dostęp do udziałów tylko dla znajomych."

#~ msgid ""
#~ "Nicotine+ allows you to share folders directly from your computer. All "
#~ "the contents of these folders (with the exception of dotfiles) can be "
#~ "downloaded by other users on the Soulseek network. Public shares are "
#~ "available for every user, while users in your buddy list can access buddy-"
#~ "only shares in addition to public shares."
#~ msgstr ""
#~ "Nicotine+ pozwala udostępniać foldery bezpośrednio z Twojego komputera. "
#~ "Cała zawartość folderów (poza plikami ukrytymi) może zostać pobrana przez "
#~ "innych użytkowników sieci Soulseek. Publiczne udostępnienia są dostępne "
#~ "dla każdego użytkownika, podczas gdy użytkownicy z Listy znajomych mogą "
#~ "dodatkowo przeglądać zawartość tylko dla znajomych."

#~ msgid "Filtered out excluded search result "
#~ msgstr "Przefiltrowane wykluczone wyniki wyszukiwania "

#~ msgid "Filtered out inexact or incorrect search result "
#~ msgstr "Przefiltrowane niedokładne lub nieprawidłowe wyniki wyszukiwania "

#, python-format
#~ msgid ""
#~ "Stored setting '%(key)s' is no longer present in the '%(name)s' plugin"
#~ msgstr ""
#~ "Zapisane ustawienie '%(key)s' nie jest już obecne we wtyczce '%(name)s'"

#, python-format
#~ msgid "Plugin %(module)s returned something weird, '%(value)s', ignoring"
#~ msgstr "Wtyczka %(module)s zwróciła coś dziwnego, '%(value)s', ignorowanie"

#, python-format
#~ msgid "Inconsistent cache for '%(vdir)s', rebuilding '%(dir)s'"
#~ msgstr ""
#~ "Niespójna pamięć podręczna dla '%(vdir)s', ponowne budowanie '%(dir)s'"

#, python-format
#~ msgid "Dropping missing folder %(dir)s"
#~ msgstr "Porzucanie brakujących folderów %(dir)s"

#, python-format
#~ msgid "All tickers / wall messages for %(room)s:"
#~ msgstr "Wszystkie wiadomości ze ściany pokoju %(room)s:"

#~ msgid "Set your personal ticker"
#~ msgstr "Ustaw wiadomość na Ścianę pokoju"

#~ msgid "Show all the tickers"
#~ msgstr "Pokaż wszystkie wiadomości Ściany pokoju"

#~ msgid ""
#~ "Failed to scan shares. If Nicotine+ is currently running, please close "
#~ "the program before scanning."
#~ msgstr ""
#~ "Błąd skanowania udostępnionych zasobów. Jeśli Nicotine+ jest obecnie "
#~ "uruchomiony, proszę go wyłączyć przed skanowaniem."

#~ msgid "Are you sure you wish to exit Nicotine+ at this time?"
#~ msgstr "Czy jesteś pewien, że chcesz wyjść z Nicotine+ tym razem?"

#~ msgid "Receive a User's IP Address"
#~ msgstr "Pobierz adres IP użytkownika"

#~ msgid "Receive a User's Info"
#~ msgstr "Pobierz informacje o użytkowniku"

#~ msgid "The server forbid us from doing wishlist searches."
#~ msgstr "Serwer nie zezwala na wyszukiwanie przy pomocy Listy życzeń."

#, python-format
#~ msgid ""
#~ "Your shares database is corrupted. Please rescan your shares and report "
#~ "any potential scanning issues to the developers. Error: %s"
#~ msgstr ""
#~ "Twoja baza danych udostępnionych zasobów jest uszkodzona. Proszę ponownie "
#~ "przeskanować zasoby i zgłosić deweloperom wszelkie potencjalne problemy "
#~ "ze skanowaniem. Błąd: %s"

#~ msgid ""
#~ "Please keep in mind that certain usernames may be taken. If this is the "
#~ "case, you will be prompted to change your username when connecting to the "
#~ "network."
#~ msgstr ""
#~ "Miej na uwadze, że można ustawić różne nazwy użytkownika. Jeśli nie "
#~ "jesteś w stanie się połączyć, możesz zmienić nazwę użytkownika w "
#~ "ustawieniach."

#~ msgid "Enter the username of the person you to receive information about"
#~ msgstr "Wprowadź nazwę użytkownika, o którym chcesz otrzymać informację"

#~ msgid "Add user 'user' to your user list"
#~ msgstr "Dodaj użytkownika 'użytkownik' to Twojej listy użytkowników"

#~ msgid "Remove user 'user' from your user list"
#~ msgstr "Usuń użytkownika 'użytkownik' z Twojej listy użytkowników"

#~ msgid "Request user info for user 'user'"
#~ msgstr "Pobierz informacje o użytkowniku 'użytkownik'"

#~ msgid "Add user to your user list"
#~ msgstr "Dodaj użytkownika do twojej listy użytkowników"

#~ msgid "Remove user from your user list"
#~ msgstr "Usuń użytkownika z twojej listy użytkowników"

#~ msgid "Respond to search requests containing minimum character count:"
#~ msgstr ""
#~ "Odpowiadaj na zapytania o wyszukiwanie zawierające minimalną liczbę "
#~ "znaków:"

#~ msgid ""
#~ "Queued users will be uploaded one file at a time in a cyclical fashion."
#~ msgstr ""
#~ "Użytkownicy oczekujący w kolejce będą pobierać po jednym pliku na raz w "
#~ "sposób cykliczny."

#~ msgid ""
#~ "Could not execute now playing code. Are you sure you picked the right "
#~ "player?"
#~ msgstr ""
#~ "Nie można wywołać opcji Teraz odtwarzanie. Czy aby na pewno wybrałeś "
#~ "odtwarzacz?"

#~ msgid "/leave /l /part /p"
#~ msgstr "/leave /l /part /p"

#~ msgid "/clear /cl"
#~ msgstr "/clear /cl"

#~ msgid "/tick /t"
#~ msgstr "/tick /t"

#~ msgid "/tickers"
#~ msgstr "/tickers"

#~ msgid "/now"
#~ msgstr "/now"

#~ msgid "/away /a"
#~ msgstr "/away /a"

#~ msgid "/quit /q /exit"
#~ msgstr "/quit /q /exit"

#~ msgid "/close /c"
#~ msgstr "/close /c"

#~ msgid "/add /ad"
#~ msgstr "/add /ad"

#~ msgid "/rem /unbuddy"
#~ msgstr "/rem /unbuddy"

#~ msgid "/ban"
#~ msgstr "/ban"

#~ msgid "/unban"
#~ msgstr "/unban"

#~ msgid "/ignore"
#~ msgstr "/ignore"

#~ msgid "/unignore"
#~ msgstr "/unignore"

#~ msgid "/browse /b"
#~ msgstr "/browse /b"

#~ msgid "/whois /w"
#~ msgstr "/whois /w"

#~ msgid "/ip"
#~ msgstr "/ip"

#, python-format
#~ msgid "The password you've entered is invalid for user %s"
#~ msgstr "Wprowadzone hasło jest niepoprawne dla użytkownika %s"

#~ msgid ""
#~ "You can create a new Soulseek account or log in to an existing one by "
#~ "entering your desired details below. Please keep in mind that some "
#~ "usernames may already be taken. If you're unable to connect with your "
#~ "selected username, try choosing another one."
#~ msgstr ""
#~ "Możesz stworzyć nowe konto uzupełniając poniższe elementy. Miej na "
#~ "uwadze, że niektóre nazwy użytkowników mogą już być zajęte. Jeśli nie "
#~ "możesz połączyć się z wybraną nazwą użytkownika, wybierz inną. Jeśli "
#~ "nadal występują problemy z połączeniem, zweryfikuj poniższe informacje."

#~ msgid "Find..."
#~ msgstr "Szukaj..."

#~ msgid "Queued..."
#~ msgstr "Zakolejkowane..."

#~ msgid "Clear All..."
#~ msgstr "Wyczyść wszystko..."

#~ msgid "Close All Tabs..."
#~ msgstr "Zamknij wszystkie zakładki..."

#~ msgid "Edit _Comments..."
#~ msgstr "Edytuj _komentarze..."

#~ msgid "Gi_ve Privileges..."
#~ msgstr "Po_daruj przywileje..."

#~ msgid "_Add..."
#~ msgstr "_Dodaj..."

#~ msgid "Room..."
#~ msgstr "Pokój..."

#~ msgid "Username..."
#~ msgstr "Użytkownik..."

#~ msgid "Add Wish..."
#~ msgstr "Dodaj życzenie..."

#~ msgid "Include text..."
#~ msgstr "Uwzględnij tekst..."

#~ msgid "Exclude text..."
#~ msgstr "Pomiń tekst..."

#~ msgid "File type..."
#~ msgstr "Typ pliku..."

#~ msgid "Bitrate..."
#~ msgstr "Bitrate..."

#~ msgid "Add..."
#~ msgstr "Dodaj..."

#~ msgid "Edit..."
#~ msgstr "Edytuj..."

#~ msgid "_Search Files"
#~ msgstr "_Szukaj plików"

#~ msgid "_Downloads"
#~ msgstr "_Pobierane"

#~ msgid "User I_nfo"
#~ msgstr "I_nformacja o użytkowniku"

#~ msgid "_Private Chat"
#~ msgstr "_Czat prywatny"

#~ msgid "Buddy _List"
#~ msgstr "Lista znajomych"

#~ msgid "_Chat Rooms"
#~ msgstr "_Pokoje rozmów"

#~ msgid "_Interests"
#~ msgstr "_Zainteresowania"

#~ msgid "_Modes"
#~ msgstr "_Sekcje"

#, python-format
#~ msgid "Hide %(tab)s"
#~ msgstr "Ukryj %(tab)s"

#~ msgid "Your IP address has not been retrieved from the server"
#~ msgstr "Twój adres IP nie został pobrany z serwera"

#, python-format
#~ msgid "Your IP address is <b>%(ip)s</b>"
#~ msgstr "Twój adres IP to <b>%(ip)s</b>"

#~ msgid "Geo Block"
#~ msgstr "Geo blokada"

#~ msgid "Away Mode"
#~ msgstr "Tryb nieobecny"

#~ msgid "URL Catching"
#~ msgstr "Przechwytywanie URL"

#~ msgid "Check _Port Status"
#~ msgstr "Sprawdź status portu"

#~ msgid ""
#~ "Geo Block controls from which countries users are allowed access to your "
#~ "shares."
#~ msgstr ""
#~ "Geo blokada kontroluje, z których krajów użytkownicy mają możliwość "
#~ "dostępu do Twoich zasobów."

#~ msgid "Enable geographical blocker"
#~ msgstr "Włącz blokadę geograficzną"

#~ msgid ""
#~ "If the source country of an IP address cannot be determined, it will be "
#~ "blocked."
#~ msgstr ""
#~ "Jeśli nie będzie można ustalić kraju pochodzenia IP, zostanie ono "
#~ "zablokowane."

#~ msgid "Countries"
#~ msgstr "Państwa"

#~ msgid "last.fm"
#~ msgstr "last.fm"

#~ msgid "MPRIS (v2)"
#~ msgstr "MPRIS (v2)"

#~ msgid "ListenBrainz"
#~ msgstr "ListenBrainz"

#~ msgid "Enable URL catching"
#~ msgstr "Włącz przechwytywane URL"

#~ msgid "Humanize slsk:// URLs"
#~ msgstr "Humanizuj url-e slsk://"

#~ msgid "Protocol Handlers"
#~ msgstr "Obsługujący protokół"

#~ msgid "Decimal separator:"
#~ msgstr "Separator dziesiętny:"

#~ msgid "Clicked usernames reveal the user context menu."
#~ msgstr ""
#~ "Po kliknięciu na nazwę użytkownika pojawia się jego menu kontekstowe."

#~ msgid ""
#~ "Nicotine+ is a Soulseek client.\n"
#~ "Usage: nicotine [OPTION]...\n"
#~ "  -c file, --config=file      Use non-default configuration file\n"
#~ "  -p dir,  --plugins=dir      Use non-default directory for plugins\n"
#~ "  -t,      --enable-trayicon  Enable the tray icon\n"
#~ "  -d,      --disable-trayicon Disable the tray icon\n"
#~ "  -h,      --help             Show help and exit\n"
#~ "  -s,      --hidden           Start the program hidden so only the tray "
#~ "icon is shown\n"
#~ "  -b ip,   --bindip=ip        Bind sockets to the given IP (useful for "
#~ "VPN)\n"
#~ "  -l port, --port=port        Listen on the given port. Overrides the "
#~ "portrange configuration\n"
#~ "  -v,      --version          Display version and exit"
#~ msgstr ""
#~ "Nicotine+ to klient sieci Soulseek.\n"
#~ "Użytkowanie: nicotine [OPCJA]...\n"
#~ "  -c plik, --config=plik      Użyj niedomyślnego pliku konfiguracyjnego\n"
#~ "  -p katalog,  -plugins=katalog     Użyj niedomyślnego katalogu dla "
#~ "wtyczek\n"
#~ "  -t,     --enable-trayicon Włącz ikonę w zasobniku\n"
#~ "  -d,     --disable-trayicon Wyłącz ikonę w zasobniku\n"
#~ "  -h,     --help            Pokaż pomoc i wyjdź\n"
#~ "  -s,     --hidden          Uruchom program zminimalizowany do zasobnika\n"
#~ "  -b IP,  --bindip=ip     Nasłuchuj na konkretnym IP (przydatne w "
#~ "przypadku VPN-a)\n"
#~ "  -l port, --port=port     Nasłuchuj na konkretnym porcie. Nadpisuje "
#~ "konfiguracje zakresu portów\n"
#~ "  -v,     --version     Pokaż wersję programu i wyjdź"

#~ msgid ""
#~ "Can not find Nicotine+ modules.\n"
#~ "Perhaps they're installed in a directory which is not\n"
#~ "in an interpreter's module search path.\n"
#~ "(there could be a version mismatch between\n"
#~ "what version of python was used to build the Nicotine\n"
#~ "binary package and what you try to run Nicotine+ with)."
#~ msgstr ""
#~ "Nie można znaleźć modułów Nicotine+.\n"
#~ "Prawdopodobnie są zainstalowane w katalogu,\n"
#~ "który nie zawiera się w module ścieżki wyszukiwania\n"
#~ "interpretera. (Może nie zgadzać się wersja Pythona\n"
#~ "z tą użytą do zbudowania Nicotine+ i z tą, która ma\n"
#~ "być użyta przy uruchamianiu Nicotine+)."

#~ msgid "Cannot find the pynicotine.utils module."
#~ msgstr "Nie znaleziono modułu pynicotine.utils."

#~ msgid "Errors occured while trying to change process name:"
#~ msgstr "Zaistniałe błędy podczas próby zmiany procesu:"

#~ msgid ""
#~ "WARNING! Rescanning shares will not work if Nicotine+ is already open. If "
#~ "you need to rescan\n"
#~ "your shares this way, keep Nicotine+ closed until rescanning is done."
#~ msgstr ""
#~ "UWAGA! Ponownie skanowanie udostępnionych zasobów nie będzie działać "
#~ "jeśli Nicotine+ jest aktualnie otwarty. Jeśli potrzebujesz ponownie "
#~ "przeskanować\n"
#~ "udostępnione zasoby w ten sposób, utrzymuj Nicotine+ zamknięte do czasu "
#~ "zakończenia ponownego skanowania."

#, python-format
#~ msgid "Starting using the profiler (saving dump to %s)"
#~ msgstr "Start z profilerem (zapisywanie zrzutu do %s)"

#, python-format
#~ msgid "Exception in callback %s: %s"
#~ msgstr "Wyjątek w wywołaniu %s: %s"

#, python-format
#~ msgid "Can not log in, reason: %s"
#~ msgstr "Nie można zalogować się, uzasadnienie: %s"

#~ msgid ""
#~ "Someone else is logging in with the same nickname, server is going to "
#~ "disconnect us"
#~ msgstr ""
#~ "Ktoś inny próbuje zalogować się tą samą nazwą użytkownika, serwer będzie "
#~ "próbował nas rozłączyć"

#~ msgid "Server Message"
#~ msgstr "Wiadomość serwera"

#, python-format
#~ msgid "Password is %s"
#~ msgstr "Hasło to %s"

#, python-format
#~ msgid ""
#~ "%(user)s is making a BrowseShares request, blocking possible spoofing "
#~ "attempt from IP %(ip)s port %(port)s"
#~ msgstr ""
#~ "%(user)s chce przejrzeć udostępnione zasoby jednak zablokowano "
#~ "prawdopodobną próbę podszycia się z IP %(ip)s port %(port)s"

#, python-format
#~ msgid ""
#~ "%(user)s is making a BrowseShares request, blocking possible spoofing "
#~ "attempt from an unknown IP & port"
#~ msgstr ""
#~ "%(user)s chce przejrzeć udostępnione zasoby jednak zablokowano "
#~ "prawdopodobną próbę podszycia się z nieznanego IP i portu"

#, python-format
#~ msgid "%(user)s is making a BrowseShares request"
#~ msgstr "%(user)s chce przejrzeć udostępnione zasoby"

#, python-format
#~ msgid ""
#~ "Blocking %(user)s from making a UserInfo request, possible spoofing "
#~ "attempt from IP %(ip)s port %(port)s"
#~ msgstr ""
#~ "Blokowanie %(user)s przed pobraniem Informacji o użytkowniku, "
#~ "prawdopodobna próba podszycia się z IP %(ip)s port %(port)s"

#, python-format
#~ msgid ""
#~ "Blocking %s from making a UserInfo request, possible spoofing attempt "
#~ "from an unknown IP & port"
#~ msgstr ""
#~ "Blokowanie %s przed pobraniem Informacji o użytkowniku, prawdopodobna "
#~ "próba podszycia się pod nieznany adres IP i port"

#, python-format
#~ msgid "%(user)s is banned, but is making a UserInfo request"
#~ msgstr ""
#~ "%(user)s jest zablokowany, ale próbuje pobrać Informacje o użytkowniku"

#, python-format
#~ msgid "Unable to parse config file: %s"
#~ msgstr "Nie można przetworzyć pliku konfiguracyjnego: %s"

#, python-format
#~ msgid "Unable to enable plugin %s"
#~ msgstr "Nie można aktywować wtyczki %s"

#~ msgid "Disabled plugin {}"
#~ msgstr "Nieaktywna wtyczka {}"

#, python-format
#~ msgid "Unable to fully disable plugin %s"
#~ msgstr "Nie można w pełni dezaktywować wtyczki %s"

#, python-format
#~ msgid "Can't save %s: %s"
#~ msgstr "Nie można zapisać %s: %s"

#~ msgid "Shared files database seems to be corrupted, rescan your shares"
#~ msgstr ""
#~ "Baza danych udostępnionych zasobów wygląda na uszkodzoną, przeskanuj "
#~ "ponownie swoje zasoby"

#~ msgid "Could not bind to a local port, aborting connection"
#~ msgstr "Nie można powiązać z lokalnym portem, przerywanie połączenia"

#~ msgid "Unknown peer init code: {}, message contents "
#~ msgstr "Nieznany kod inicjalizacji połączenia: {}, zawartość wiadomości "

#, python-format
#~ msgid "Ignoring connection request from blocked IP Address %(ip)s:%(port)s"
#~ msgstr ""
#~ "Ignorowanie żądania połączenia z zablokowanego adresu IP %(ip)s:%(port)s"

#, python-format
#~ msgid "Exception during parsing %(area)s: %(exception)s"
#~ msgstr "Wyjątek podczas przetwarzania %(area)s: %(exception)s"

#~ msgid "[Automatic Message] "
#~ msgstr "[Wiadomość automatyczna] "

#~ msgid "You are not allowed to send me files."
#~ msgstr "Nie możesz wysyłać mi plików."

#~ msgid "Clear Aborted"
#~ msgstr "Wyczyść przerwane"

#~ msgid "Clear Queued"
#~ msgstr "Wyczyść zakolejkowane"

#~ msgid "Download directory error"
#~ msgstr "Błąd pobierania folderu"

#~ msgid "File P_roperties"
#~ msgstr "_Właściwości pliku"

#~ msgid "Abor_t"
#~ msgstr "Przerwi_j"

#~ msgid "Search ID was none when clicking tab"
#~ msgstr "ID szukania był pusty podczas kliknięcia w zakładkę"

#~ msgid "Immediate Download"
#~ msgstr "Natychmiastowe pobieranie"

#~ msgid "File _Properties"
#~ msgstr "_Właściwości pliku"

#, python-format
#~ msgid "Search row error: %(exception)s %(row)s"
#~ msgstr "Błąd wiersza wyszukiwania: %(exception)s %(row)s"

#~ msgid "Add a Shared Buddy Folder"
#~ msgstr "Dodaj katalog z zasobem dla znajomych"

#~ msgid "Ignore User..."
#~ msgstr "Ignoruj użytkownika..."

#~ msgid "IP:"
#~ msgstr "IP:"

#~ msgid "Ban User..."
#~ msgstr "Zablokuj użytkownika..."

#~ msgid "Server"
#~ msgstr "Serwer"

#~ msgid "Fonts & Colors"
#~ msgstr "Czcionki i kolory"

#~ msgid "I Like"
#~ msgstr "Lubię"

#, python-format
#~ msgid "%s mentioned you in the %s room"
#~ msgstr "%s wspomniał o Tobie w pokoju %s"

#~ msgid "Join Public Room"
#~ msgstr "Dołącz do publicznego pokoju"

#, python-format
#~ msgid ""
#~ "I remember the room %(room)s being owned by %(previous)s, but the server "
#~ "says its owned by %(new)s."
#~ msgstr ""
#~ "Pamiętam, że pokój %(room)s należy do %(previous)s, ale serwer zwraca, że "
#~ "należy on do %(new)s."

#, python-format
#~ msgid ""
#~ "I remember the room %(room)s being owned by %(old)s, but the server says "
#~ "that's not true."
#~ msgstr ""
#~ "Pamiętam, że pokój %(room)s należy do %(old)s, ale serwer zwraca, że to "
#~ "nie prawda."

#, python-format
#~ msgid "Speed: %s"
#~ msgstr "Prędkość: %s"

#, python-format
#~ msgid "Files: %s"
#~ msgstr "Pliki: %s"

#, python-format
#~ msgid "Directories: %s"
#~ msgstr "Katalogi: %s"

#~ msgid "Hates"
#~ msgstr "Znienawidzone"

#, python-format
#~ msgid "Queued uploads: %i"
#~ msgstr "Kolejka wysyłania: %i"

#, python-format
#~ msgid "%s"
#~ msgstr "%s"

#~ msgid "Edit comments"
#~ msgstr "Edytuj komentarze"

#~ msgid "Send _Message"
#~ msgstr "Wyślij wiadomość"

#~ msgid "Brow_se Files"
#~ msgstr "Przeglądaj pliki"

#~ msgid "_Ban User"
#~ msgstr "Zablokuj użytkownika"

#, python-format
#~ msgid "to %(user)s"
#~ msgstr "dla %(user)s"

#~ msgid "Lookup a User's IP"
#~ msgstr "Podejrzyj IP użytkownika"

#, python-format
#~ msgid "ERROR: tray menu, %(error)s"
#~ msgstr "BŁĄD: zasobnik, %(error)s"

#~ msgid "Enter the User who you wish to send a private message:"
#~ msgstr "Podaj użytkownika, któremu chcesz wysłać prywatną wiadomość:"

#~ msgid "Get A User's IP"
#~ msgstr "Pobierz IP użytkownika"

#, python-format
#~ msgid "ERROR: cannot set trayicon image: %(error)s"
#~ msgstr "BŁĄD: nie można ustawić ikony w zasobniku: %(error)s"

#, python-format
#~ msgid "UPnP exception: %(error)s"
#~ msgstr "Wyjątek UPnP: %(error)s"

#~ msgid "Failed to automate the creation of UPnP Port Mapping rule."
#~ msgstr "Błąd podczas automatycznej konfiguracji mapowania UPnP."

#~ msgid ""
#~ "Send the private message directly to the user (not supported on most "
#~ "clients)"
#~ msgstr ""
#~ "Wyślij prywatną wiadomość bezpośrednio do użytkownika (niewspierane przez "
#~ "większość klientów)"

#~ msgid "Log"
#~ msgstr "Log"

#~ msgid "Configure user info"
#~ msgstr "Konfiguruj informacje o użytkowniku"

#~ msgid "Load from Disk"
#~ msgstr "Załaduj z dysku"

#~ msgid "Room type"
#~ msgstr "Typ pokoju"

#~ msgid "Private"
#~ msgstr "Prywatny"

#~ msgid "Warnings"
#~ msgstr "Uwagi"

#~ msgid "Statistics"
#~ msgstr "Statystyki"

#~ msgid "Personal"
#~ msgstr "Personalne"

#~ msgid "Unrecommendations"
#~ msgstr "Nierekomendowane"

#~ msgid "This search term will be requested at regular intervals"
#~ msgstr "To wyszukiwane hasło będzie żądane w regularnych odstępach czasu"

#~ msgid "Search result filter help"
#~ msgstr "Pomoc dotycząca filtrów wyszukiwania"

#~ msgid "Abort User's Uploads"
#~ msgstr "Przerwij wysyłanie użytkownika"

#~ msgid "Upload slots: unknown"
#~ msgstr "Sloty wysyłania: nieznane"

#~ msgid "Free upload slots: unknown"
#~ msgstr "Wolne sloty wysyłania: nieznane"

#~ msgid "Queued uploads: unknown"
#~ msgstr "Kolejka wysyłania: nieznana"

#~ msgid "Speed: unknown"
#~ msgstr "Prędkość: nieznana"

#~ msgid "Files: unknown"
#~ msgstr "Pliki: nieznane"

#~ msgid "Directories: unknown"
#~ msgstr "Katalogi: nieznane"

#~ msgid "Accepts Uploads from:"
#~ msgstr "Akceptuj wysyłania od:"

#~ msgid "Leave room 'room'"
#~ msgstr "Opuść pokój 'pokój'"

#, python-format
#~ msgid "/bsearch /bs '%s'"
#~ msgstr "/bsearch /bs '%s'"

#~ msgid "Always run in the background when main window is closed"
#~ msgstr "Zawsze uruchamiaj w tle, kiedy główne okno jest zamknięte"

#~ msgid "Always quit when main window is closed"
#~ msgstr "Zawsze zamykaj, kiedy główne okno jest zamknięte"

#~ msgid "Icon Theme"
#~ msgstr "Styl ikon"

#~ msgid "Tray Icon"
#~ msgstr "Ikona zasobnika"

#~ msgid "Online:"
#~ msgstr "Dostępny:"

#~ msgid "Away:"
#~ msgstr "Nieobecny:"

#~ msgid "Offline:"
#~ msgstr "Niedostępny:"

#~ msgid "Nick hightlight:"
#~ msgstr "Podświetlony nick:"

#~ msgid "Notify:"
#~ msgstr "Powiadomienie:"

#~ msgid "When double-clicking on..."
#~ msgstr "Podwójne kliknięcie na..."

#~ msgid "Download transfers:"
#~ msgstr "Transferach pobierania:"

#~ msgid "Upload transfers:"
#~ msgstr "Transferach wysyłania:"

#~ msgid "Server:"
#~ msgstr "Serwer:"

#~ msgid "Ports"
#~ msgstr "Porty"

#~ msgid "Automatically renew port mappings every"
#~ msgstr "Automatycznie odnów mapowanie portów co"

#~ msgid "hour(s)"
#~ msgstr "godzin(y)"

#~ msgid "Player Command/Username"
#~ msgstr "Komenda odtwarzacza/użytkownik"

#~ msgid "Legend:"
#~ msgstr "Legenda:"

#~ msgid "Example:"
#~ msgstr "Przykład:"

#~ msgid "Text Appearance"
#~ msgstr "Wygląd tekstu"

#~ msgid "Chat Colors"
#~ msgstr "Kolory czatów"

#~ msgid "Usernames also indicate away status"
#~ msgstr "Nazwy użytkowników wskazują również na status niedostępności"

#~ msgid "/me text:"
#~ msgstr "/me Tekst:"

#~ msgid "List and Search Colors"
#~ msgstr "Kolory listy i wyszukiwań"

#~ msgid "With queue:"
#~ msgstr "Z kolejką:"

#~ msgid "Background:"
#~ msgstr "Tło:"

#~ msgid "Regular:"
#~ msgstr "Regularny:"

#~ msgid "Highlighted:"
#~ msgstr "Podświetlony:"

#~ msgid "Default Colors"
#~ msgstr "Domyślne kolory"

#~ msgid "Clear Colors"
#~ msgstr "Wyczyść kolory"

#~ msgid "Tabs can be reordered"
#~ msgstr "Zakładki mogą być przemieszczane"

#~ msgid "When disabled, tabs are locked in their current order."
#~ msgstr "Gdy wyłączone, karty są zablokowane w ich aktualnej kolejności."

#~ msgid "Main:"
#~ msgstr "Główna:"

#~ msgid "Label angle:"
#~ msgstr "Wyrównanie etykiety:"

#~ msgid "Chat rooms:"
#~ msgstr "Pokoje rozmów:"

#~ msgid "Search:"
#~ msgstr "Szukaj:"

#~ msgid "User info:"
#~ msgstr "Informacje o użytkowniku:"

#~ msgid "User browse:"
#~ msgstr "Przeglądanie użytkownika:"

#~ msgid "characters or more"
#~ msgstr "znaków lub więcej"

#~ msgid "Send out a max of"
#~ msgstr "Wyślij maksymalnie"

#~ msgid "results per search request"
#~ msgstr "wyników na zlecone wyszukiwanie"

#~ msgid "Your Searches"
#~ msgstr "Twoje wyszukiwania"

#~ msgid "Maximum results per search visible at a time:"
#~ msgstr "Maksymalna liczba wyników wyszukiwania:"

#~ msgid ""
#~ "When the maximum number of visible results is smaller than the maximum "
#~ "total results, use the search result filters to refine what is visible."
#~ msgstr ""
#~ "Jeśli maksymalna liczba widocznych wyników jest mniejsza niż maksymalna "
#~ "liczba wszystkich wyników, użyj filtrów wyników wyszukiwania, aby zawęzić "
#~ "widoczność."

#~ msgid "Plugin preferences"
#~ msgstr "Ustawienia wtyczek"

#~ msgid "Ignored users:"
#~ msgstr "Zignorowani użytkownicy:"

#~ msgid "Show the last"
#~ msgstr "Pokaż ostatnie"

#~ msgid "lines"
#~ msgstr "linie"

#~ msgid "minutes of inactivity"
#~ msgstr "minut(ach) nieaktywności"

#~ msgid "Show _Flag Columns in User Lists"
#~ msgstr "Pokaż kolumnę _flag na liście użytkowników"

#~ msgid "Show _Buttons in Transfer Tabs"
#~ msgstr "Pokaż _przyciski na zakładkach transferów"

#~ msgid "_Fast Configure"
#~ msgstr "_Szybka konfiguracja"

#~ msgid ""
#~ "The search wishlist can be useful when you are looking for rare content. "
#~ "Search terms in the wishlist are automatically requested at regular "
#~ "intervals."
#~ msgstr ""
#~ "Przeszukiwanie Listy życzeń może być przydatne kiedy szukasz rzadkich "
#~ "zasobów. Wyszukiwane terminy na liście życzeń są automatycznie żądane w "
#~ "regularnych odstępach czasu."

#~ msgid "Add search term..."
#~ msgstr "Dodaj frazę wyszukiwania..."

#~ msgid "Press \"Next\" to start configuring Nicotine+."
#~ msgstr ""
#~ "Naciśnij przycisk \"Dalej\" w celu rozpoczęcia konfiguracji Nicotine+."

#~ msgid ""
#~ "Nicotine+ will still work to some degree if your port is closed. However, "
#~ "do keep in mind that you won't be able to connect to users whose port is "
#~ "also closed."
#~ msgstr ""
#~ "Nicotine+ będzie działać do pewnego stopnia jeśli port jest zamknięty. "
#~ "Miej na uwadze, że nie będziesz miał możliwości połączenia się z innymi "
#~ "użytkownikami, którzy także mają zamknięty port."

#~ msgid ""
#~ "Sharing files is crucial for the health of the Soulseek network. Many "
#~ "people will ban you if you download from them without sharing anything "
#~ "yourself."
#~ msgstr ""
#~ "Nie udostępniasz publicznie żadnych plików. Udostępnianie jest kluczowe "
#~ "dla sieci Soulseek i wiele osób będzie Cię blokować jeśli będziesz coś od "
#~ "nich pobierać bez udostępniania."

#~ msgid "Show Room List"
#~ msgstr "Pokaż listę pokoi"

#~ msgid "Show Flag Columns in User Lists"
#~ msgstr "Pokaż kolumnę flag na liście użytkowników"

#~ msgid "Show Buttons in Transfer Tabs"
#~ msgstr "Pokaż przyciski na zakładkach transferów"

#, python-format
#~ msgid ""
#~ "IP %(ip)s:%(port)s is spoofing user %(user)s with a peer request, "
#~ "blocking because it does not match IP: %(real_ip)s"
#~ msgstr ""
#~ "IP %(ip)s:%(port)s podszywa się pod użytkownika %(user)s poprzez żądanie "
#~ "połączenia. Blokowanie, ponieważ nie pokrywa się z IP: %(real_ip)s"

#, python-format
#~ msgid "Unable to save config file: %s"
#~ msgstr "Nie można zapisać pliku konfiguracyjnego: %s"

#, python-format
#~ msgid "Retrying failed download: user %(user)s, file %(file)s"
#~ msgstr "Ponawianie błędnych pobrań: użytkownik %(user)s, plik %(file)s"

#~ msgid "(friend)"
#~ msgstr "(przyjaciel)"

#, python-format
#~ msgid "Something went wrong while writing your transfer list: %(error)s"
#~ msgstr "Coś poszło nie tak podczas zapisywania listy transferów: %(error)s"

#~ msgid "Clear Finished/Aborted"
#~ msgstr "Wyczyść Skończone / Wstrzymane"

#~ msgid "Clear Paused"
#~ msgstr "Wyczyść zapauzowane"

#~ msgid "Clear Finished/Erred"
#~ msgstr "Wyczyść skończone/błędne"

#~ msgid "Initializing transfer"
#~ msgstr "Inicjowanie transferu"

#~ msgid "Waiting for peer to connect"
#~ msgstr "Oczekiwanie na połączenie z klientem"

#~ msgid "Connecting"
#~ msgstr "Łączenie"

#~ msgid "Getting address"
#~ msgstr "Pobieranie adresu"

#~ msgid "Unknown search mode, not using plugin system. Fix me!"
#~ msgstr "Nieznany tryb szukania, nieużywanie systemu wtyczek. Napraw mnie!"

#~ msgid "Warning"
#~ msgstr "Uwaga"

#, python-format
#~ msgid ""
#~ "Failed to remove logged room messages for room '%(room)s'. Error: "
#~ "%(error)s"
#~ msgstr ""
#~ "Wystąpił błąd podczas usuwania zalogowanych wiadomości dla pokoju "
#~ "'%(room)s'. Błąd: %(error)s"

#, python-format
#~ msgid ""
#~ "Failed to remove logged chat messages for user '%(user)s'. Error: "
#~ "%(error)s"
#~ msgstr ""
#~ "Wystąpił błąd podczas usuwania zalogowanych wiadomości dla użytkownika "
#~ "'%(user)s'. Błąd: %(error)s"

#, python-format
#~ msgid "Total uploads allowed: %i"
#~ msgstr "Maksymalna ilość wysyłań: %i"

#, python-format
#~ msgid "Slots free: %s"
#~ msgstr "Wolne sloty: %s"

#~ msgid "Users in list"
#~ msgstr "Użytkownicy na liście"

#~ msgid "Slots free: unknown"
#~ msgstr "Wolne sloty: nieznane"

#~ msgid "Back Up Config File"
#~ msgstr "Kopia zapasowa pliku ustawień"

#~ msgid ""
#~ "By default, files sent by these users will be saved into a subfolder "
#~ "within your Download folder."
#~ msgstr ""
#~ "Domyślnie pliki wysyłane przez tych użytkowników będą zapisywane w "
#~ "podfolderze w folderze pobierania."

#~ msgid "KiB/s"
#~ msgstr "KiB/s"

#~ msgid "Plugin Properties"
#~ msgstr "Właściwości wtyczki"

#~ msgid "Complete room names in chat rooms"
#~ msgstr "Dopełniaj nazwy pokojów w pokojach rozmów"

#~ msgid "Can not find Nicotine+ modules."
#~ msgstr "Nie znaleziono modułów Nicotine+."

#, python-format
#~ msgid "Can't remove %s"
#~ msgstr "Nie można usunąć %s"

#, python-format
#~ msgid "Can't rename config file, error: %s"
#~ msgstr "Nie można zmienić nazwy pliku konfiguracyjnego, błąd: %s"

#, python-format
#~ msgid "Something went wrong while opening your alias file: %s"
#~ msgstr "Coś poszło nie tak podczas otwierania pliku aliasu: %s"

#, python-format
#~ msgid "Something went wrong while saving your alias file: %s"
#~ msgstr "Coś poszło nie tak podczas zapisywania pliku aliasu: %s"

#, python-format
#~ msgid "Warning: unknown object type %(obj_type)s in message %(msg_type)s"
#~ msgstr "Uwaga: nieznany typ obiektu %(obj_type)s w wiadomości %(msg_type)s"

#, python-format
#~ msgid "Empty message made, class %s"
#~ msgstr "Stworzono pustą wiadomość, klasa %s"

#, python-format
#~ msgid "Can't parse incoming messages, class %s"
#~ msgstr "Nie można przetworzyć przychodzącej wiadomości, klasa %s"

#, python-format
#~ msgid "Failed to set RLIMIT_NOFILE: %s"
#~ msgstr "Błąd z ustawieniem RLIMIT_NOFILE: %s"

#, python-format
#~ msgid ""
#~ "Server message type %(type)i size %(size)i contents %(msg_buffer)s unknown"
#~ msgstr ""
#~ "Typ wiadomości serwera %(type)i o wielkości %(size)i zawierający "
#~ "%(msg_buffer)s jest nieznany"

#, python-format
#~ msgid "Can't handle connection type %s"
#~ msgstr "Nie można obsłużyć połączenia typu %s"

#, python-format
#~ msgid ""
#~ "Can't send the message over the closed connection: %(type)s %(msg_obj)s"
#~ msgstr ""
#~ "Nie można wysłać wiadomości poprzez zamknięte połączenie: %(type)s "
#~ "%(msg_obj)s"

#, python-format
#~ msgid "Something went wrong while opening your transfer list: %(error)s"
#~ msgstr "Coś poszło nie tak podczas otwierania listy transferów: %(error)s"

#, python-format
#~ msgid ""
#~ "Download error formally known as 'Unknown file request': %(req)s "
#~ "(%(user)s: %(file)s)"
#~ msgstr ""
#~ "Błąd pobierania znany jako 'Żądanie nieznanego pliku': %(req)s (%(user)s: "
#~ "%(file)s)"

#, python-format
#~ msgid ""
#~ "Upload error formally known as 'Unknown file request': %(req)s (%(user)s: "
#~ "%(file)s)"
#~ msgstr ""
#~ "Błąd wysyłania znany jako 'Żądanie nieznanego pliku': %(req)s (%(user)s: "
#~ "%(file)s)"

#, python-format
#~ msgid "Upload finished: %(user)s, file %(file)s"
#~ msgstr "Wysyłanie zakończone: %(user)s, plik %(file)s"

#~ msgid "Enter the name of the private room you wish to create"
#~ msgstr "Podaj nazwę prywatnego pokoju, który chcesz utworzyć"

#~ msgid "Show IP a_ddress"
#~ msgstr "Pokaż a_dres IP"

#~ msgid "Get user i_nfo"
#~ msgstr "Pobierz I_nformacje o użytkowniku"

#~ msgid "Brow_se files"
#~ msgstr "Prz_eglądaj pliki"

#~ msgid "_Ban this user"
#~ msgstr "_Zablokuj tego użytkownika"

#~ msgid "_Ignore this user"
#~ msgstr "_Ignoruj tego użytkownika"

#~ msgid "Clear finished/aborted"
#~ msgstr "Wyczyść skończone/przerwane"

#~ msgid "Clear queued"
#~ msgstr "Wyczyść zakolejkowane"

#~ msgid "Rescanning started"
#~ msgstr "Ponowne skanowanie rozpoczęte"

#~ msgid "Rescanning Buddy Shares started"
#~ msgstr "Ponowne skanowanie zasobów znajomych rozpoczęte"

#~ msgid "Rescanning Buddy Shares finished"
#~ msgstr "Ponowne skanowanie zasobów znajomych zakończone"

#~ msgid "Rescanning finished"
#~ msgstr "Ponowne skanowanie zakończone"

#~ msgid "Send to tray"
#~ msgstr "Wyślij do zasobnika"

#~ msgid "I like"
#~ msgstr "Lubię"

#~ msgid "I _don't like this"
#~ msgstr "N_ie lubię tego"

#~ msgid "Add user to list"
#~ msgstr "Dodaj użytkownika do listy"

#~ msgid "Ban this user"
#~ msgstr "Zablokuj tego użytkownika"

#~ msgid "Ignore this user"
#~ msgstr "Ignoruj tego użytkownika"

#~ msgid "Close this tab"
#~ msgstr "Zamknij tę zakładkę"

#~ msgid "Warning: Bad Username"
#~ msgstr "Uwaga: Zła nazwa użytkownika"

#~ msgid "Username 'None' is not a good one, please pick another."
#~ msgstr "Użytkownik 'None' nie jest dobrym wyborem, wybierz inną nazwę."

#~ msgid "Warning: Invalid ports"
#~ msgstr "Uwaga: Nieprawidłowe porty"

#~ msgid "Client ports are invalid."
#~ msgstr "Porty klienta są niepoprawne."

#, python-format
#~ msgid "Security Risk: you should not share your %s folder!"
#~ msgstr "Naruszenie bezpieczeństwa: nie powinieneś udostępniać folderu %s!"

#~ msgid "Virtual name"
#~ msgstr "Wirtualna nazwa"

#~ msgid "Pick a color, any color"
#~ msgstr "Wybierz kolor, dowolny kolor"

#~ msgid "Notebook Tabs"
#~ msgstr "Zakładki"

#~ msgid "Directories"
#~ msgstr "Katalogi"

#~ msgid "_Close this tab"
#~ msgstr "Zamknij tę zakładkę"

#, python-format
#~ msgid "Unknown (%(countrycode)s)"
#~ msgstr "Nieznany (%(countrycode)s)"

#~ msgid "<b>Buddy List</b>"
#~ msgstr "<b>Lista znajomych</b>"

#~ msgid "<b>User List</b>"
#~ msgstr "<b>Użytkownicy</b>"

#~ msgid "Check my port status"
#~ msgstr "Sprawdź status portu"

#~ msgid "Download files to the following folder:"
#~ msgstr "Pobierz pliki do następującego katalogu:"

#~ msgid "Share the following folders:"
#~ msgstr "Udostępnij następujące katalogi:"

#~ msgid "Only share files with friends"
#~ msgstr "Współdziel tylko ze znajomymi"

#~ msgid "<b>My Interests</b>"
#~ msgstr "<b>Zainteresowania</b>"

#~ msgid "<b>Recommendations</b>"
#~ msgstr "<b>Rekomendacje</b>"

#~ msgid "<b>Similar Users</b>"
#~ msgstr "<b>Podobni użytkownicy</b>"

#~ msgid "<b>Users</b>"
#~ msgstr "<b>Użytkownicy</b>"

#~ msgid "<b>Files</b>"
#~ msgstr "<b>Pliki</b>"

#~ msgid "<b>Chat Rooms</b>"
#~ msgstr "<b>Pokoje rozmów</b>"

#~ msgid "<b>Results</b>"
#~ msgstr "<b>Wyniki</b>"

#~ msgid "Stop new search results from being displayed"
#~ msgstr "Zatrzymaj dalsze wyświetlanie znalezionych wyników"

#~ msgid "Abort Search"
#~ msgstr "Wstrzymaj wyszukiwanie"

#~ msgid "<b>Folders</b>"
#~ msgstr "<b>Foldery</b>"

#~ msgid "<b>Shared</b>"
#~ msgstr "<b>Udostępnione</b>"

#~ msgid "<b>Self Description</b>"
#~ msgstr "<b>Własny opis</b>"

#~ msgid "<b>Interests</b>"
#~ msgstr "<b>Zainteresowania</b>"

#~ msgid "<b>Picture</b>"
#~ msgstr "<b>Obraz</b>"

#~ msgid "/toggle 'plugin'"
#~ msgstr "/toggle 'wtyczka'"

#~ msgid "Toggle plugin on/off state"
#~ msgstr "Włącz lub wyłącz wtyczkę"

#~ msgid "About Search Filters"
#~ msgstr "Lista komend filtrów wyszukiwania"

#~ msgid "About _Chat Room Commands"
#~ msgstr "Lista komend w pokojach rozmów"

#~ msgid "About _Private Chat Commands"
#~ msgstr "Lista komend prywatnych rozmów"

#~ msgid "About _Search Filters"
#~ msgstr "Lista komend filtrów wyszukiwania"

#~ msgid "<b>General</b>"
#~ msgstr "<b>Ogólne</b>"

#~ msgid "<b>Chat Colors</b>"
#~ msgstr "<b>Kolory czatu</b>"

#~ msgid "Username Font Style:"
#~ msgstr "Styl czcionki użytkownika:"

#~ msgid "Username Colors and Hotspots"
#~ msgstr "Koloruj nazwy użytkowników"

#~ msgid "Display away colors"
#~ msgstr "Wyświetl kolory nieobecności"

#~ msgid "Cycle completions instead of showing a dropdown"
#~ msgstr "Uzupełnianie cykli zamiast wyświetlania rozwijanej listy"

#~ msgid "Download SFV/MD5 before anything else"
#~ msgstr "Pobierz SFV/MD5 przed wszystkim innym"

#~ msgid "Select a folder"
#~ msgstr "Wybierz folder"

#~ msgid "<b>Download Filters</b>"
#~ msgstr "<b>Filtry pobierania</b>"

#~ msgid "<b>Commands</b>"
#~ msgstr "<b>Komendy</b>"

#~ msgid "Run command after folder finishes ($ for folder path):"
#~ msgstr "Uruchom komendę po skończeniu folderu ($ dla ścieżki folderu)"

#~ msgid "<b>Tray</b>"
#~ msgstr "<b>Zasobnik</b>"

#~ msgid "Always send Nicotine+ to tray when main window is closed"
#~ msgstr ""
#~ "Zawsze minimalizuj do zasobnika, kiedy główne okno Nicotine+ jest "
#~ "zamknięte"

#~ msgid "<b>Trayicon</b>"
#~ msgstr "<b>Ikona w zasobniku</b>"

#~ msgid "<b>Status</b>"
#~ msgstr "<b>Status</b>"

#~ msgid "Read"
#~ msgstr "Czytaj"

#~ msgid "Reopen last Private Chat messages"
#~ msgstr "Otwórz ponownie ostatnie wiadomości rozmowy prywatnej"

#~ msgid ""
#~ "Reopen closed search tabs that haven't been ignored when new results come "
#~ "in"
#~ msgstr ""
#~ "Otwórz ponownie zamknięte zakładki wyszukiwania, które zostały "
#~ "zignorowane, jeśli pojawią się nowe wyniki"

#~ msgid "<b>Search Filters</b>"
#~ msgstr "<b>Filtry wyszukiwania</b>"

#~ msgid "<b>Ports</b>"
#~ msgstr "<b>Porty</b>"

#~ msgid "<b>Upload Queue</b>"
#~ msgstr "<b>Kolejka wysyłania</b>"

#~ msgid "Users will be sent one file and then another user will be selected"
#~ msgstr ""
#~ "Użytkownicy otrzymają jeden plik, a następnie zostanie wybrany inny "
#~ "użytkownik"

#~ msgid "KBytes/sec"
#~ msgstr "KB/s"

#~ msgid "Megabytes"
#~ msgstr "Megabajtów"

#, python-format
#~ msgid "ERROR: MPRIS: failed to load dbus module: %(error)s"
#~ msgstr "BŁĄD: MPRIS: błąd ładowanie modułu dbus: %(error)s"

#, python-format
#~ msgid "Connection closed by peer: %s"
#~ msgstr "Połączenie zakończone przez drugiego użytkownika: %s"

#, python-format
#~ msgid "Requesting address for user %(user)s"
#~ msgstr "Pobieranie adresu użytkownika %(user)s"

#, python-format
#~ msgid "Initialising direct connection of type %(type)s to user %(user)s"
#~ msgstr ""
#~ "Inicjowanie bezpośredniego połączenia typu %(type)s do użytkownika "
#~ "%(user)s"

#, python-format
#~ msgid ""
#~ "Direct connection of type %(type)s to user %(user)s failed, attempting "
#~ "indirect connection"
#~ msgstr ""
#~ "Bezpośrednie połączenie typu %(type)s do użytkownika %(user)s zakończone "
#~ "błędem, inicjacja połączenia pośredniego"

#, python-format
#~ msgid ""
#~ "Server reported port 0 for the 10th time for user %(user)s, giving up"
#~ msgstr ""
#~ "Serwer zwrócił port 0 dziesiąty raz dla użytkownika %(user)s, poddaje się"

#, python-format
#~ msgid ""
#~ "Server reported non-zero port for user %(user)s after %(tries)i retries"
#~ msgstr ""
#~ "Serwer zwrócił nie-zerowy port dla użytkownika %(user)s po %(tries)i "
#~ "próbach"

#, python-format
#~ msgid "Server reported port 0 for user %(user)s, retrying"
#~ msgstr "Serwer zwrócił port 0 dla użytkownika %(user)s, ponawianie"

#, python-format
#~ msgid "Can't connect to user %s neither directly nor indirectly, giving up"
#~ msgstr ""
#~ "Nie można połączyć się z użytkownikiem %s bezpośrednio jak i pośrednio, "
#~ "poddaje się"

#, python-format
#~ msgid ""
#~ "Indirect connect request of type %(type)s to user %(user)s expired, "
#~ "giving up"
#~ msgstr ""
#~ "Pośrednie połączenie typu %(type)s do użytkownika %(user)s wygasło, "
#~ "poddaje się"

#, python-format
#~ msgid ""
#~ "Can't connect to user %s neither directly nor indirectly, informing user "
#~ "via the server"
#~ msgstr ""
#~ "Nie można połączyć się z użytkownikiem %s bezpośrednio jak i pośrednio, "
#~ "informowanie użytkownika poprzez serwer"

#, python-format
#~ msgid "Unknown tunneled message: %s"
#~ msgstr "Nieznana tunelowana wiadomość %s"

#, python-format
#~ msgid ""
#~ "Found meta data that couldn't be encoded, possible corrupt file: "
#~ "'%(file)s' has a bitrate of %(bitrate)s kbs, a length of %(length)s "
#~ "seconds and a VBR of %(vbr)s"
#~ msgstr ""
#~ "Znaleziono metadane, które nie mogą zostać przetworzone, prawdopodobnie "
#~ "uszkodzony plik: %(file)s' ma bitrate %(bitrate)s kbs, o długości "
#~ "%(length)s sekund i VBR równym %(vbr)s"

#, python-format
#~ msgid "Filtering: %s"
#~ msgstr "Filtrowanie: %s"

#, python-format
#~ msgid "Got transfer request %s but cannot determine requestor"
#~ msgstr "Otrzymano żądanie transferu %s ale nie ustalono żądającego"

#, python-format
#~ msgid "Denied file request: User %(user)s, %(msg)s"
#~ msgstr "Zablokowane żądanie pliku: Użytkownik %(user)s, %(msg)s"

#, python-format
#~ msgid "Upload request: %(req)s Response: %(resp)s"
#~ msgstr "Żądanie wysyłania: %(req)s Odpowiedź: %(resp)s"

#, python-format
#~ msgid "Queued upload request: User %(user)s, %(msg)s"
#~ msgstr "Żądanie zakolejkowanego wysyłania: Użytkownik %(user)s, %(msg)s"

#, python-format
#~ msgid "Got unknown transfer response: %s"
#~ msgstr "Otrzymano nieznaną odpowiedź transferu: %s"

#, python-format
#~ msgid "Download started: %s"
#~ msgstr "Pobieranie rozpoczęte: %s"

#, python-format
#~ msgid "Download finished: %(file)s"
#~ msgstr "Pobieranie zakończone: %(file)s"

#~ msgid "Virtual Directory"
#~ msgstr "Wirtualny katalog"

#~ msgid "Directory"
#~ msgstr "Katalog"

#~ msgid "Dirs"
#~ msgstr "Katalogi"

#~ msgid "Counting files..."
#~ msgstr "Liczenie plików...."

#, python-format
#~ msgid "Down: %(num)i users, %(speed)s"
#~ msgstr "Pobieranie: %(num)i użytkowników, %(speed)s"

#, python-format
#~ msgid "Up: %(num)i users, %(speed)s"
#~ msgstr "Wysyłanie: %(num)i użytkowników, %(speed)s"

#, python-format
#~ msgid "%(current)s/%(limit)s Connections"
#~ msgstr "%(current)s/%(limit)s połączeń"

#~ msgid "Search files"
#~ msgstr "Szukaj plików"

#~ msgid "In queue"
#~ msgstr "W kolejce"

#~ msgid "_Download directory"
#~ msgstr "Katalog _pobierania"

#~ msgid "Download directory _to..."
#~ msgstr "Pobierz folder _do..."

#~ msgid "Up_load file(s)"
#~ msgstr "Wy_ślij plik(i)"

#~ msgid "<b>Your Interests</b>"
#~ msgstr "<b>Twoje zainteresowania</b>"

#~ msgid "Autoclear finished / aborted"
#~ msgstr "Automatycznie czyszczenie skończonych / przerwanych"

#~ msgid "Show Info"
#~ msgstr "Pokaż informacje"

#~ msgid "Debug output:"
#~ msgstr "Wyniki debuggera:"

#~ msgid "Lock incoming files (turn off for NFS)"
#~ msgstr "Zablokuj przychodzące pliki (wyłącz gdy używasz NFS)"

#~ msgid "Select a directory"
#~ msgstr "Wybierz katalog"

#~ msgid "Run command after download finishes ($ for filename):"
#~ msgstr "Uruchom komendę po skończonym pobieraniu ($ dla nazwy pliku):"

#~ msgid "Open Directory"
#~ msgstr "Otwórz katalog"

#~ msgid "Filter out:"
#~ msgstr "Filtr wyjścia:"

#~ msgid "Filter in:"
#~ msgstr "Filtr wejścia:"

#~ msgid "<b>Privileges</b>"
#~ msgstr "<b>Przywileje</b>"

#~ msgid "Use the first available port in the range from "
#~ msgstr "Użyj pierwszego wolnego portu w przedziale od "

#~ msgid "Download to the following directory:"
#~ msgstr "Pobieraj do następującego katalogu:"

#~ msgid "Use the first available listening port from the following range:"
#~ msgstr "Użyj pierwszego wolnego portu nasłuchu z następującej rangi:"
