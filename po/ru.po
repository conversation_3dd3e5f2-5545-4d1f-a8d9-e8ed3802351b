# SPDX-FileCopyrightText: 2022-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2025-05-18 03:59+0000\n"
"Last-Translator: SnIPeRSnIPeR <<EMAIL>>\n"
"Language-Team: Russian <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/ru/>\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Generator: Weblate 5.12-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr "Клиент Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Графический клиент для одноранговой сети Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr ""
"Soulseek;Nicotine;обмен;чат;обмен сообщениями;P2P;одноранговая сеть;GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
msgid "Browse the Soulseek network"
msgstr "Просмотреть сеть Soulseek"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+ — это графический клиент сети Soulseek."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ стремится быть удобной, свободной альтернативой с открытым "
"исходным кодом официальному клиенту Soulseek, предоставляя дополнительную "
"функциональность и сохраняя при этом совместимость с протоколом Soulseek."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
msgid "Search Files"
msgstr "Поиск файлов"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Загрузки"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
msgid "Browse Shares"
msgstr "Просмотр общих каталогов"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
msgid "Private Chat"
msgstr "Приватный чат"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Команда Nicotine+"

#: pynicotine/__init__.py:50
#, python-format
msgid "Website: %s"
msgstr "Сайт: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "показать эту подсказку и выйти"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "файл"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr "использовать пользовательский файл конфигурации"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "каталог"

#: pynicotine/__init__.py:64
msgid "alternative directory for user data and plugins"
msgstr "альтернативный каталог для персональных данных и плагинов"

#: pynicotine/__init__.py:68
msgid "start the program without showing window"
msgstr "запускать Nicotine+ свёрнутым"

#: pynicotine/__init__.py:71
msgid "ip"
msgstr "IP-адрес"

#: pynicotine/__init__.py:72
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "привязать сокеты к заданному IP-адресу (полезно для VPN)"

#: pynicotine/__init__.py:75
msgid "port"
msgstr "порт"

#: pynicotine/__init__.py:76
msgid "listen on the given port"
msgstr "слушать на этом порту"

#: pynicotine/__init__.py:80
msgid "rescan shared files"
msgstr "пересканировать общие файлы"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr "запустить программу в фоновом режиме (без графического интерфейса)"

#: pynicotine/__init__.py:88
msgid "display version and exit"
msgstr "отобразить версию программы и выйти"

#: pynicotine/__init__.py:121
#, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Вы используете неподдерживаемую версию Python (%(old_version)s).\n"
"Вам следует установить Python %(min_version)s или новее."

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Не удалось просканировать общие каталоги. Закройте другие экземпляры "
"Nicotine+ и повторите попытку."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, python-format
msgid "%(user)s is away"
msgstr "%(user)s отсутствует"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, python-format
msgid "%(user)s is online"
msgstr "%(user)s в сети"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, python-format
msgid "%(user)s is offline"
msgstr "%(user)s не в сети"

#: pynicotine/buddies.py:316
msgid "Buddy Status"
msgstr "Статус друга"

#: pynicotine/chatrooms.py:383
#, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Вы были добавлены в чат: %(room)s"

#: pynicotine/chatrooms.py:478
#, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr ""
"Сообщение в чате от пользователя '%(user)s' в комнате '%(room)s': %(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr "Не удаётся создать каталог '%(path)s', ошибка: %(error)s"

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr "Ошибка резервного копирования файла конфигурации: %s"

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr "Файл конфигурации сохранён в: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Загружаю %(program)s %(version)s"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Выхожу из %(program)s %(version)s %(status)s…"

#: pynicotine/core.py:246
msgid "terminating"
msgstr "завершение"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "закрытие программы"

#: pynicotine/core.py:259
#, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Выход %(program)s %(version)s!"

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr "Перед подключением необходимо указать имя пользователя и пароль…"

#: pynicotine/downloads.py:239
#, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""
"Ошибка: фильтры загрузки повреждены! Проверьте ваши фильтры. Причина: %s"

#: pynicotine/downloads.py:254
#, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Ошибка: %(num)d Фильтры загрузки повреждены! %(error)s "

#: pynicotine/downloads.py:366
#, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s загружен у %(user)s"

#: pynicotine/downloads.py:370
msgid "File Downloaded"
msgstr "Файл загружен"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Выполнено: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "Ошибка выполнения \"%(command)s\" команды: %(error)s"

#: pynicotine/downloads.py:407
#, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s загружен у %(user)s"

#: pynicotine/downloads.py:411
msgid "Folder Downloaded"
msgstr "Каталог загружен"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Выполнено в каталоге: %s"

#: pynicotine/downloads.py:443
#, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "Не удалось переместить '%(tempfile)s' в '%(file)s': %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
msgid "Download Folder Error"
msgstr "Ошибка загрузки папки"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Загрузка завершена: пользователь %(user)s, файл %(file)s"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Загрузка прервана, пользователь %(user)s файл %(file)s"

#: pynicotine/downloads.py:1150
#, python-format
msgid "Download I/O error: %s"
msgstr "Ошибка ввода-вывода при загрузке: %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Не удалось эксклюзивно блокировать файл - ошибка ввода-вывода: %s"

#: pynicotine/downloads.py:1202
#, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Не удаётся сохранить файл в %(folder_path)s: %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Загрузка началась: пользователь %(user)s, файл %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, python-format
msgid "Cannot find %s, please install it."
msgstr "Не удаётся найти %s, установите его."

#: pynicotine/gtkgui/__init__.py:162
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr ""
"Графическая среда отсутствует, используется режим без графического интерфейса"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "_Подключиться"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "_Отсоединиться"

#: pynicotine/gtkgui/application.py:308
msgid "Soulseek _Privileges"
msgstr "_Привилегии Soulseek"

#: pynicotine/gtkgui/application.py:314
msgid "_Preferences"
msgstr "_Настройки"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
msgid "_Quit"
msgstr "_Выйти"

#: pynicotine/gtkgui/application.py:337
msgid "Browse _Public Shares"
msgstr "Просмотр общедоступных раздач"

#: pynicotine/gtkgui/application.py:338
msgid "Browse _Buddy Shares"
msgstr "Просмотр раздач друга"

#: pynicotine/gtkgui/application.py:339
msgid "Browse _Trusted Shares"
msgstr "Просмотр доверенных раздач"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
msgid "_Rescan Shares"
msgstr "_Пересканировать общие каталоги"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
msgid "Configure _Shares"
msgstr "Настроить раздачи"

#: pynicotine/gtkgui/application.py:371
msgid "_Keyboard Shortcuts"
msgstr "_Горячие клавиши"

#: pynicotine/gtkgui/application.py:372
msgid "_Setup Assistant"
msgstr "_Мастер настройки"

#: pynicotine/gtkgui/application.py:373
msgid "_Transfer Statistics"
msgstr "_Статистика"

#: pynicotine/gtkgui/application.py:378
msgid "Report a _Bug"
msgstr "Сообщить об _ошибке"

#: pynicotine/gtkgui/application.py:379
msgid "Improve T_ranslations"
msgstr "Предложить _перевод"

#: pynicotine/gtkgui/application.py:383
msgid "_About Nicotine+"
msgstr "_О Nicotine+"

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "_Файл"

#: pynicotine/gtkgui/application.py:395
msgid "_Shares"
msgstr "_Общие каталоги"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
msgid "_Help"
msgstr "_Помощь"

#: pynicotine/gtkgui/application.py:410
msgid "_Browse Shares"
msgstr "Просмотр раздач"

#: pynicotine/gtkgui/application.py:465
#, python-format
msgid "Unable to show notification: %s"
msgstr "Невозможно показать уведомление: %s"

#: pynicotine/gtkgui/application.py:526
msgid "You are still uploading files. Do you really want to exit?"
msgstr ""
"У вас есть активные раздачи. Вы действительно хотите выйти из Nicotine+?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr "Подождать завершения раздач"

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr "Вы действительно хотите выйти из Nicotine+?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
msgid "_No"
msgstr "_Нет"

#: pynicotine/gtkgui/application.py:535
msgid "_Run in Background"
msgstr "_В фоновый режим"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr "Выйти из Nicotine+"

#: pynicotine/gtkgui/application.py:561
msgid "Shares Not Available"
msgstr "Общие каталоги не найдены"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr "Убедитесь, что внешние диски смонтированы и доступ к папкам разрешён."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
msgid "_Cancel"
msgstr "_Отмена"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "_Повторить"

#: pynicotine/gtkgui/application.py:567
msgid "_Force Rescan"
msgstr "_Проверить принудительно"

#: pynicotine/gtkgui/application.py:742
msgid "Message Downloading Users"
msgstr "Написать скачивающим пользователям"

#: pynicotine/gtkgui/application.py:743
msgid "Send private message to all users who are downloading from you:"
msgstr ""
"Отправить личное сообщение всем пользователям, которые скачивали файлы у вас:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
msgid "_Send Message"
msgstr "Отправить сообщение"

#: pynicotine/gtkgui/application.py:756
msgid "Message Buddies"
msgstr "Отправить сообщение друзьям"

#: pynicotine/gtkgui/application.py:757
msgid "Send private message to all online buddies:"
msgstr "Отправить личное сообщение всем друзьям, которые находятся в сети:"

#: pynicotine/gtkgui/application.py:786
msgid "Select a Saved Shares List File"
msgstr "Выберите сохранённый файл списка раздач"

#: pynicotine/gtkgui/application.py:877
msgid "Critical Error"
msgstr "Критическая ошибка"

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ столкнулся с критической ошибкой, и его необходимо закрыть. "
"Скопируйте следующее сообщение и добавьте его в отчет об ошибке:"

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr "_Выйти из Nicotine+"

#: pynicotine/gtkgui/application.py:883
msgid "_Copy & Report Bug"
msgstr "_Скопировать и сообщить об ошибке"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Статус"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr "Страна"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Пользователь"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Скорость"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Файлы"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted"
msgstr "Доверенный"

#: pynicotine/gtkgui/buddies.py:108
msgid "Notify"
msgstr "Оповестить"

#: pynicotine/gtkgui/buddies.py:114
msgid "Prioritized"
msgstr "Приоритетный"

#: pynicotine/gtkgui/buddies.py:120
msgid "Last Seen"
msgstr "Был в сети"

#: pynicotine/gtkgui/buddies.py:126
msgid "Note"
msgstr "Заметка"

#: pynicotine/gtkgui/buddies.py:143
msgid "Add User _Note…"
msgstr "Добавить _заметку…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Удалить"

#: pynicotine/gtkgui/buddies.py:364
msgid "Never seen"
msgstr "Никогда"

#: pynicotine/gtkgui/buddies.py:529
msgid "Add User Note"
msgstr "Добавить заметку"

#: pynicotine/gtkgui/buddies.py:530
#, python-format
msgid "Add a note about user %s:"
msgstr "Добавить заметку о пользователе %s:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
msgid "_Add"
msgstr "Добавить"

#: pynicotine/gtkgui/chatrooms.py:224
msgid "Create New Room?"
msgstr "Создать новую комнату?"

#: pynicotine/gtkgui/chatrooms.py:225
#, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Вы действительно хотите создать новую комнату «%s»?"

#: pynicotine/gtkgui/chatrooms.py:226
msgid "Make room private"
msgstr "Сделать комнату частной"

#: pynicotine/gtkgui/chatrooms.py:515
msgid "Search activity log…"
msgstr "Поиск журнала активностей…"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
msgid "Search chat log…"
msgstr "Поиск журнала чата…"

#: pynicotine/gtkgui/chatrooms.py:597
msgid "Sear_ch User's Files"
msgstr "Иск_ать в файлах пользователя"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
msgid "Find…"
msgstr "Найти…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Копировать"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr "Копировать всё"

#: pynicotine/gtkgui/chatrooms.py:608
msgid "Clear Activity View"
msgstr "Очистить историю активности"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
msgid "_Leave Room"
msgstr "_Покинуть комнату"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
msgid "Copy Link"
msgstr "Копировать ссылку"

#: pynicotine/gtkgui/chatrooms.py:624
msgid "View Room Log"
msgstr "Посмотреть журнал комнаты"

#: pynicotine/gtkgui/chatrooms.py:627
msgid "Delete Room Log…"
msgstr "Удалить журнал комнаты…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
msgid "Clear Message View"
msgstr "Очистить историю сообщений"

#: pynicotine/gtkgui/chatrooms.py:832
#, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s упомянул вас в комнате %(room)s"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "Вас упомянул %(user)s в комнате %(room)s"

#: pynicotine/gtkgui/chatrooms.py:855
#, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "Вам пришло сообщение от %(user)s в комнате %(room)s"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s присоединился(-ась) к комнате"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s покинул(-а) комнату"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s отошёл(-ла)"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s вернулся(-ась)"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
msgid "Delete Logged Messages?"
msgstr "Удалить журнал сообщений?"

#: pynicotine/gtkgui/chatrooms.py:1197
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"Вы действительно хотите навсегда удалить все сообщения из этой комнаты?"

#: pynicotine/gtkgui/dialogs/about.py:405
msgid "About"
msgstr "О Nicotine+"

#: pynicotine/gtkgui/dialogs/about.py:415
msgid "Website"
msgstr "Веб-сайт"

#: pynicotine/gtkgui/dialogs/about.py:457
#, python-format
msgid "Error checking latest version: %s"
msgstr "Ошибка проверки последней версии: %s"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr "Доступен новый выпуск: %s"

#: pynicotine/gtkgui/dialogs/about.py:467
msgid "Up to date"
msgstr "Актуальная версия"

#: pynicotine/gtkgui/dialogs/about.py:496
msgid "Checking latest version…"
msgstr "Проверка актуальной версии…"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
msgid "Setup Assistant"
msgstr "Мастер настройки"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
msgid "Virtual Folder"
msgstr "Имя"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
msgid "Folder"
msgstr "Каталог"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
msgid "_Previous"
msgstr "Назад"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
msgid "_Finish"
msgstr "Закончить"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
msgid "_Next"
msgstr "_Далее"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
msgid "Add a Shared Folder"
msgstr "Добавить каталог в общий доступ"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
msgid "Edit Shared Folder"
msgstr "Изменить общий каталог"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Введите новое виртуальное имя для '%(dir)s':"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
msgid "_Edit"
msgstr "Редактировать"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"Пользователь %s уже существует, но введенный вами пароль неверен. Если вы "
"входите в первый раз, пожалуйста, выберите другое имя пользователя."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
msgid "File Properties"
msgstr "Свойства файла"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "Свойства файла (%(num)i из %(total)i  /  %(size)s  /  %(length)s)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Свойства файла (%(num)i из %(total)i  /  %(size)s)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
msgid "_Apply"
msgstr "Применить"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
msgid "Add…"
msgstr "Добавить…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
msgid "Edit…"
msgstr "Редактировать…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, python-format
msgid "%s Settings"
msgstr "%s Настройки"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
msgid "Add Item"
msgstr "Добавить элемент"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
msgid "Edit Item"
msgstr "Изменить элемент"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "Неизвестный"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
msgid "Check Port Status"
msgstr "Проверить порт"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, порт %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
msgid "Password Change Rejected"
msgstr "Изменение пароля отклонено"

#: pynicotine/gtkgui/dialogs/preferences.py:218
msgid "Enter a new password for your Soulseek account:"
msgstr "Введите новый пароль для своей учетной записи Soulseek:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"В настоящее время вы вышли из сети Soulseek. Если вы хотите изменить пароль "
"существующей учетной записи Soulseek, вам необходимо войти в эту учетную "
"запись."

#: pynicotine/gtkgui/dialogs/preferences.py:223
msgid "Enter password to use when logging in:"
msgstr "Введите пароль, который будет использоваться при входе:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
msgid "Change Password"
msgstr "Изменить пароль"

#: pynicotine/gtkgui/dialogs/preferences.py:230
msgid "_Change"
msgstr "Изменить"

#: pynicotine/gtkgui/dialogs/preferences.py:274
msgid "No one"
msgstr "Никому"

#: pynicotine/gtkgui/dialogs/preferences.py:275
msgid "Everyone"
msgstr "Всем"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Друзья"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted buddies"
msgstr "Доверенные друзья"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
msgid "Nothing"
msgstr "Нет действия"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
msgid "Open File"
msgstr "Открыть файл"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
msgid "Open in File Manager"
msgstr "Открыть в файловом менеджере"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Поиск"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
msgid "Pause"
msgstr "Пауза"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
msgid "Resume"
msgstr "Продолжить"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
msgid "Browse Folder"
msgstr "Просмотр каталога"

#: pynicotine/gtkgui/dialogs/preferences.py:317
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Синтаксис</b>: без учета регистра. Если этот параметр включен, можно "
"использовать регулярные выражения Python, иначе только символ подстановки."

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr "Фильтр"

#: pynicotine/gtkgui/dialogs/preferences.py:334
msgid "Regex"
msgstr "Регулярное выражение"

#: pynicotine/gtkgui/dialogs/preferences.py:468
msgid "Add Download Filter"
msgstr "Добавить фильтр загрузки"

#: pynicotine/gtkgui/dialogs/preferences.py:469
msgid "Enter a new download filter:"
msgstr "Введите новый фильтр загрузки:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
msgid "Enable regular expressions"
msgstr "Включить регулярные выражения"

#: pynicotine/gtkgui/dialogs/preferences.py:498
msgid "Edit Download Filter"
msgstr "Изменить фильтр загрузки"

#: pynicotine/gtkgui/dialogs/preferences.py:499
msgid "Modify the following download filter:"
msgstr "Измените следующий фильтр загрузки:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d Ошибка! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
msgid "Filters Successful"
msgstr "Фильтры успешно применены"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr "Публичный"

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr "Доступно для"

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "Прервать"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
msgid "Retry"
msgstr "Повторить"

#: pynicotine/gtkgui/dialogs/preferences.py:828
msgid "Round Robin"
msgstr "Циклический"

#: pynicotine/gtkgui/dialogs/preferences.py:829
msgid "First In, First Out"
msgstr "Первым пришёл — первым ушёл"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
msgid "Username"
msgstr "Имя пользователя"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
msgid "IP Address"
msgstr "IP-адрес"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
msgid "Ignore User"
msgstr "Игнорировать пользователя"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
msgid "Enter the name of the user you want to ignore:"
msgstr "Введите имя пользователя, которого хотите игнорировать:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
msgid "Ignore IP Address"
msgstr "Игнорировать IP-адрес"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
msgid "Enter an IP address you want to ignore:"
msgstr "Введите IP-адрес, который вы хотите игнорировать:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "* is a wildcard"
msgstr "* — это символ подстановки"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
msgid "Ban User"
msgstr "Заблокировать пользователя"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
msgid "Enter the name of the user you want to ban:"
msgstr "Введите имя пользователя, которого хотите заблокировать:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
msgid "Ban IP Address"
msgstr "Заблокировать IP-адрес"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "Enter an IP address you want to ban:"
msgstr "Введите IP-адрес для блокировки:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr "Расшифровка кодов формата"

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
msgid "Pattern"
msgstr "Шаблон"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
msgid "Replacement"
msgstr "Замена"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
msgid "Censor Pattern"
msgstr "Шаблон цензуры"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Введите шаблон, который вы хотите подвергнуть цензуре. Добавьте пробелы "
"вокруг шаблона, если вы не хотите сопоставлять строки внутри слов (может "
"произойти сбой в начале и конце строк)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
msgid "Edit Censored Pattern"
msgstr "Редактировать шаблон цензуры"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
msgid "Add Replacement"
msgstr "Добавить замену"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
msgid "Enter a text pattern and what to replace it with:"
msgstr "Введите текстовый шаблон для замены:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
msgid "Edit Replacement"
msgstr "Редактировать замену"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
msgid "System default"
msgstr "Как в системе"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
msgid "Show confirmation dialog"
msgstr "Показать диалог подтверждения"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
msgid "Run in the background"
msgstr "Работать в фоновом режиме"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
msgid "bold"
msgstr "жирный"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
msgid "italic"
msgstr "курсив"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
msgid "underline"
msgstr "подчёркивание"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
msgid "normal"
msgstr "обычный"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
msgid "Separate Buddies tab"
msgstr "Отделить вкладку \"Друзья\""

#: pynicotine/gtkgui/dialogs/preferences.py:1771
msgid "Sidebar in Chat Rooms tab"
msgstr "Боковая панель на вкладке \"Комнаты чата\""

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr "Всегда видимая боковая панель"

#: pynicotine/gtkgui/dialogs/preferences.py:1777
msgid "Top"
msgstr "От верхнего края"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
msgid "Bottom"
msgstr "От нижнего края"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
msgid "Left"
msgstr "От левого края"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
msgid "Right"
msgstr "От правого края"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "В сети"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Отсутствую"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Не в сети"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
msgid "Tab Changed"
msgstr "Вкладка изменена"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
msgid "Tab Highlight"
msgstr "Выделение вкладки"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
msgid "Window"
msgstr "Окно"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
msgid "Online (Tray)"
msgstr "В сети (иконка)"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
msgid "Away (Tray)"
msgstr "Отсутствую (иконка)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
msgid "Offline (Tray)"
msgstr "Не в сети (иконка)"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
msgid "Message (Tray)"
msgstr "Сообщение (иконка)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Протокол"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
msgid "Command"
msgstr "Команда"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
msgid "Add URL Handler"
msgstr "Добавить обработчик URL"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Введите протокол и команду для обработчика URL:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
msgid "Edit Command"
msgstr "Редактировать команду"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Введите новую команду для протокола \"%s\":"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
msgid "Username;APIKEY"
msgstr "Имя пользователя;ключ API"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr ""
"Музыкальный плеер (например, amarok, audacious, exaile). Оставьте поле "
"пустым для автовыбора:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
msgid "Username: "
msgstr "Имя пользователя: "

#: pynicotine/gtkgui/dialogs/preferences.py:2767
msgid "Command:"
msgstr "Команда:"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
msgid "Title"
msgstr "Название"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Сейчас играет (обычно \"%(artist)s - %(title)s\")"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
msgid "Artist"
msgstr "Исполнитель"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
msgid "Duration"
msgstr "Продолжительность"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "Битрейт"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
msgid "Comment"
msgstr "Комментарий"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
msgid "Album"
msgstr "Альбом"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
msgid "Track Number"
msgstr "Номер трека"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
msgid "Year"
msgstr "Год"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
msgid "Filename (URI)"
msgstr "Имя файла (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
msgid "Program"
msgstr "Программа"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
msgid "Enabled"
msgstr "Включено"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
msgid "Plugin"
msgstr "Плагин"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
msgid "No Plugin Selected"
msgstr "Плагин не выбран"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
msgid "Preferences"
msgstr "Настройки"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
msgid "Network"
msgstr "Сеть"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
msgid "User Interface"
msgstr "Поведение"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Общие файлы"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Раздачи"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Поисковые запросы"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
msgid "User Profile"
msgstr "Профиль"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
msgid "Chats"
msgstr "Чаты"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
msgid "Now Playing"
msgstr "Сейчас играет"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Журналирование"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
msgid "Banned Users"
msgstr "Заблокированные пользователи"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
msgid "Ignored Users"
msgstr "Игнорируемые пользователи"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
msgid "URL Handlers"
msgstr "Обработчики URL"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr "Плагины"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
msgid "Pick a File Name for Config Backup"
msgstr "Выберите имя файла конфигурации"

#: pynicotine/gtkgui/dialogs/statistics.py:75
msgid "Transfer Statistics"
msgstr "Статистика"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, python-format
msgid "Total Since %(date)s"
msgstr "Всего с %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
msgid "Reset Transfer Statistics?"
msgstr "Сбросить статистику?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
msgid "Do you really want to reset transfer statistics?"
msgstr "Вы действительно хотите сбросить статистику?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
msgid "Wishlist"
msgstr "Список желаемого"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
msgid "Wish"
msgstr "Желаемое"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
msgid "_Search for Item"
msgstr "_Поиск по метке"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
msgid "Edit Wish"
msgstr "Изменить список желаемого"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, python-format
msgid "Enter new value for wish '%s':"
msgstr "Введите новое значение для списка желаемого \"%s\":"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
msgid "Clear Wishlist?"
msgstr "Очистить список желаемого?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
msgid "Do you really want to clear your wishlist?"
msgstr "Вы действительно хотите очистить свой список желаемого?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Путь"

#: pynicotine/gtkgui/downloads.py:47
msgid "_Resume"
msgstr "_Возобновить"

#: pynicotine/gtkgui/downloads.py:48
msgid "P_ause"
msgstr "П_ауза"

#: pynicotine/gtkgui/downloads.py:72
msgid "Finished / Filtered"
msgstr "Завершено / Отфильтровано"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Завершено"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
msgid "Paused"
msgstr "Приостановлено"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
msgid "Filtered"
msgstr "Отфильтровано"

#: pynicotine/gtkgui/downloads.py:77
msgid "Deleted"
msgstr "Удалено"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
msgid "Queued…"
msgstr "В очереди…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
msgid "Everything…"
msgstr "Всё…"

#: pynicotine/gtkgui/downloads.py:132
#, python-format
msgid "Downloads: %(speed)s"
msgstr "Загрузка: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
msgid "Clear Queued Downloads"
msgstr "Очистить очередь загрузки"

#: pynicotine/gtkgui/downloads.py:139
msgid "Do you really want to clear all queued downloads?"
msgstr "Вы действительно хотите очистить все загрузки из очереди?"

#: pynicotine/gtkgui/downloads.py:151
msgid "Clear All Downloads"
msgstr "Очистить все загрузки"

#: pynicotine/gtkgui/downloads.py:152
msgid "Do you really want to clear all downloads?"
msgstr "Вы действительно хотите очистить все загрузки?"

#: pynicotine/gtkgui/downloads.py:169
#, python-format
msgid "Download %(num)i files?"
msgstr "Скачать файлы %(num)i?"

#: pynicotine/gtkgui/downloads.py:170
#, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"Вы действительно хотите скачать файлы %(num)i из каталога %(user)s "
"%(folder)s?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
msgid "_Download Folder"
msgstr "_Скачать каталог"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
msgid "Likes"
msgstr "Предпочитаю"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
msgid "Dislikes"
msgstr "Безразлично"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Рейтинг"

#: pynicotine/gtkgui/interests.py:111
msgid "Item"
msgstr "Метка"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
msgid "_Recommendations for Item"
msgstr "_Рекомендации"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
msgid "I _Like This"
msgstr "_Мне нравится"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
msgid "I _Dislike This"
msgstr "М_не не нравится"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
msgid "Recommendations"
msgstr "Рекомендации"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
msgid "Similar Users"
msgstr "Похожие пользователи"

#: pynicotine/gtkgui/interests.py:427
#, python-format
msgid "Recommendations (%s)"
msgstr "Рекомендации (%s)"

#: pynicotine/gtkgui/interests.py:451
#, python-format
msgid "Similar Users (%s)"
msgstr "Похожие пользователи (%s)"

#: pynicotine/gtkgui/mainwindow.py:238
msgid "Search log…"
msgstr "Поиск журнала…"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, python-format
msgid "Private Message from %(user)s"
msgstr "Личное сообщение от %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
msgid "Wishlist Results Found"
msgstr "Найдено из списка желаемого"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
msgid "User Profiles"
msgstr "Профили"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
msgid "Chat Rooms"
msgstr "Комнаты"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "Интересы"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
msgid "Chat"
msgstr "Чат"

#: pynicotine/gtkgui/mainwindow.py:1111
msgid "[Debug] Connections"
msgstr "[Отладка] Подключения"

#: pynicotine/gtkgui/mainwindow.py:1112
msgid "[Debug] Messages"
msgstr "[Отладка] Сообщения"

#: pynicotine/gtkgui/mainwindow.py:1113
msgid "[Debug] Transfers"
msgstr "[Отладка] Передачи"

#: pynicotine/gtkgui/mainwindow.py:1114
msgid "[Debug] Miscellaneous"
msgstr "[Отладка] Разное"

#: pynicotine/gtkgui/mainwindow.py:1119
msgid "_Find…"
msgstr "_Найти…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
msgid "_Copy"
msgstr "_Скопировать"

#: pynicotine/gtkgui/mainwindow.py:1122
msgid "Copy _All"
msgstr "Скопировать _всё"

#: pynicotine/gtkgui/mainwindow.py:1127
msgid "View _Debug Logs"
msgstr "Посмотреть отладочный журнал"

#: pynicotine/gtkgui/mainwindow.py:1128
msgid "View _Transfer Logs"
msgstr "Посмотреть журнал передач"

#: pynicotine/gtkgui/mainwindow.py:1132
msgid "_Log Categories"
msgstr "_Категории журналов"

#: pynicotine/gtkgui/mainwindow.py:1134
msgid "Clear Log View"
msgstr "Очистить журнал"

#: pynicotine/gtkgui/mainwindow.py:1199
msgid "Preparing Shares"
msgstr "Подготовка раздач"

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr "Сканирование общих файлов"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
msgid "Shared Folders"
msgstr "Общие каталоги"

#: pynicotine/gtkgui/popovers/chathistory.py:77
msgid "Latest Message"
msgstr "Последнее сообщение"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Комната"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Пользователи"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
msgid "Join Room"
msgstr "Войти в комнату"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
msgid "Leave Room"
msgstr "Покинуть комнату"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
msgid "Disown Private Room"
msgstr "Удалить комнату"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
msgid "Cancel Room Membership"
msgstr "Отменить членство в комнате"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
msgid "Close All Tabs…"
msgstr "Закрыть все вкладки…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
msgid "_Close Tab"
msgstr "_Закрыть вкладку"

#: pynicotine/gtkgui/privatechat.py:379
msgid "View Chat Log"
msgstr "Просмотр журнала"

#: pynicotine/gtkgui/privatechat.py:382
msgid "Delete Chat Log…"
msgstr "Очистить журнал…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
msgid "User Actions"
msgstr "Действия пользователя"

#: pynicotine/gtkgui/privatechat.py:477
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Вы действительно хотите удалить все входящие сообщения для этого "
"пользователя?"

#: pynicotine/gtkgui/privatechat.py:528
msgid "* Messages sent while you were offline"
msgstr "* Сообщения, которые вы получили пока были не в сети"

#: pynicotine/gtkgui/search.py:90
msgid "_Global"
msgstr "_Глобальный"

#: pynicotine/gtkgui/search.py:91
msgid "_Buddies"
msgstr "_Друзья"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
msgid "_Rooms"
msgstr "_Комнаты"

#: pynicotine/gtkgui/search.py:93
msgid "_User"
msgstr "_Пользователь"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
msgid "In Queue"
msgstr "В очереди"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
msgid "File Type"
msgstr "Тип файла"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Имя файла"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Размер"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
msgid "Quality"
msgstr "Качество"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
msgid "Copy _File Path"
msgstr "Скопировать _путь к файлу"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "Скопировать _ссылку"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
msgid "Copy Folder U_RL"
msgstr "Cкопировать ссылку _каталога"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
msgid "_Download File(s)"
msgstr "_Скачать файл(ы)"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
msgid "Download File(s) _To…"
msgstr "Скачать файл(ы) _в…"

#: pynicotine/gtkgui/search.py:615
msgid "Download _Folder(s)"
msgstr "Скачать _каталог(и)"

#: pynicotine/gtkgui/search.py:616
msgid "Download F_older(s) To…"
msgstr "Скачать _каталог(и) в…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
msgid "View User _Profile"
msgstr "Посмотреть профиль пользователя"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
msgid "_Browse Folder"
msgstr "Просмотр каталога"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
msgid "F_ile Properties"
msgstr "Свойства _файла"

#: pynicotine/gtkgui/search.py:629
msgid "Copy Search Term"
msgstr "Скопировать поисковый запрос"

#: pynicotine/gtkgui/search.py:631
msgid "Clear All Results"
msgstr "Очистить все результаты"

#: pynicotine/gtkgui/search.py:718
msgid "Clear Filters"
msgstr "Очистить фильтры"

#: pynicotine/gtkgui/search.py:721
msgid "Restore Filters"
msgstr "Восстановить фильтры"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, python-format
msgid "[PRIVATE]  %s"
msgstr "[ЧАСТНЫЙ]  %s"

#: pynicotine/gtkgui/search.py:1273
#, python-format
msgid "_Result Filters [%d]"
msgstr "_Фильтры [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
msgid "_Result Filters"
msgstr "_Фильтры"

#: pynicotine/gtkgui/search.py:1277
#, python-format
msgid "%d active filter(s)"
msgstr "%d активных(ый)"

#: pynicotine/gtkgui/search.py:1329
msgid "Add Wi_sh"
msgstr "Добавить в желаемое"

#: pynicotine/gtkgui/search.py:1332
msgid "Remove Wi_sh"
msgstr "Удалить из желаемого"

#: pynicotine/gtkgui/search.py:1349
msgid "Select User's Results"
msgstr "Выбрать результаты пользователя"

#: pynicotine/gtkgui/search.py:1472
#, python-format
msgid "Total: %s"
msgstr "Всего: %s"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
msgid "Results"
msgstr "Результаты"

#: pynicotine/gtkgui/search.py:1590
msgid "Select Destination Folder for File(s)"
msgstr "Выбрать каталог назначения для файлов"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
msgid "Select Destination Folder"
msgstr "Выбрать каталог назначения"

#: pynicotine/gtkgui/transfers.py:61
msgid "Queued"
msgstr "В очереди"

#: pynicotine/gtkgui/transfers.py:62
msgid "Queued (prioritized)"
msgstr "В очереди (с приоритетом)"

#: pynicotine/gtkgui/transfers.py:63
msgid "Queued (privileged)"
msgstr "В очереди (привилегированный)"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Получение статуса"

#: pynicotine/gtkgui/transfers.py:65
msgid "Transferring"
msgstr "Передача"

#: pynicotine/gtkgui/transfers.py:66
msgid "Connection closed"
msgstr "Соединение закрыто"

#: pynicotine/gtkgui/transfers.py:67
msgid "Connection timeout"
msgstr "Превышено время ожидания"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Пользователь вышел из сети"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
msgid "Cancelled"
msgstr "Отменено"

#: pynicotine/gtkgui/transfers.py:73
msgid "Download folder error"
msgstr "Ошибка загрузки папки"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Ошибка локального файла"

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr "Заблокировано"

#: pynicotine/gtkgui/transfers.py:76
msgid "File not shared"
msgstr "Файл не расшарен"

#: pynicotine/gtkgui/transfers.py:77
msgid "Pending shutdown"
msgstr "Ожидает завершения работы"

#: pynicotine/gtkgui/transfers.py:78
msgid "File read error"
msgstr "Ошибка чтения файла"

#: pynicotine/gtkgui/transfers.py:182
msgid "Queue"
msgstr "В очередь"

#: pynicotine/gtkgui/transfers.py:188
msgid "Percent"
msgstr "Прогресс"

#: pynicotine/gtkgui/transfers.py:208
msgid "Time Elapsed"
msgstr "Прошло"

#: pynicotine/gtkgui/transfers.py:215
msgid "Time Left"
msgstr "Осталось"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
msgid "_Open File"
msgstr "Открыть файл"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
msgid "Open in File _Manager"
msgstr "Открыть в файловом _менеджере"

#: pynicotine/gtkgui/transfers.py:286
msgid "_Search"
msgstr "_Поиск"

#: pynicotine/gtkgui/transfers.py:289
msgid "Clear All"
msgstr "Очистить всё"

#: pynicotine/gtkgui/transfers.py:953
msgid "Select User's Transfers"
msgstr "Выбрать передачи пользователей"

#: pynicotine/gtkgui/uploads.py:50
msgid "_Abort"
msgstr "_Прервать"

#: pynicotine/gtkgui/uploads.py:74
msgid "Finished / Cancelled / Failed"
msgstr "Завершено / Прервано / Ошибка"

#: pynicotine/gtkgui/uploads.py:75
msgid "Finished / Cancelled"
msgstr "Завершено / Отменено"

#: pynicotine/gtkgui/uploads.py:79
msgid "Failed"
msgstr "Ошибка"

#: pynicotine/gtkgui/uploads.py:80
msgid "User Logged Off"
msgstr "Пользователь вышел из сети"

#: pynicotine/gtkgui/uploads.py:142
#, python-format
msgid "Uploads: %(speed)s"
msgstr "Отдача: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr "Выход..."

#: pynicotine/gtkgui/uploads.py:164
msgid "Clear Queued Uploads"
msgstr "Очистить очередь раздач"

#: pynicotine/gtkgui/uploads.py:165
msgid "Do you really want to clear all queued uploads?"
msgstr "Вы действительно хотите очистить очередь раздач?"

#: pynicotine/gtkgui/uploads.py:177
msgid "Clear All Uploads"
msgstr "Очистить все раздачи"

#: pynicotine/gtkgui/uploads.py:178
msgid "Do you really want to clear all uploads?"
msgstr "Вы действительно хотите очистить все раздачи?"

#: pynicotine/gtkgui/userbrowse.py:282
msgid "_Save Shares List to Disk"
msgstr "Сохранить список общих раздач на диск"

#: pynicotine/gtkgui/userbrowse.py:292
msgid "Upload Folder & Subfolders…"
msgstr "Раздать каталог и подкаталог…"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
msgid "Copy _Folder Path"
msgstr "Скопировать путь до _каталога"

#: pynicotine/gtkgui/userbrowse.py:309
msgid "_Download Folder & Subfolders"
msgstr "Скачать каталог и подкаталог(и)"

#: pynicotine/gtkgui/userbrowse.py:310
msgid "Download Folder & Subfolders _To…"
msgstr "Скачать каталог и подкаталог(и) в…"

#: pynicotine/gtkgui/userbrowse.py:334
msgid "File Name"
msgstr "Имя файла"

#: pynicotine/gtkgui/userbrowse.py:373
msgid "Up_load File(s)…"
msgstr "Раз_дать файл(ы)…"

#: pynicotine/gtkgui/userbrowse.py:374
msgid "Upload Folder…"
msgstr "Раздать каталог…"

#: pynicotine/gtkgui/userbrowse.py:396
msgid "Download Folder _To…"
msgstr "Скачать каталог _в…"

#: pynicotine/gtkgui/userbrowse.py:600
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"Список общих файлов пользователя пуст. Пользователь не хочет ничем делиться, "
"либо делится файлами персонально."

#: pynicotine/gtkgui/userbrowse.py:615
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Невозможно получить общие файлы пользователя. Пользователь не в сети, у вас "
"обоих закрыты порты, либо возникла временная проблема с соединением."

#: pynicotine/gtkgui/userbrowse.py:953
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Выберите место для загрузки нескольких каталогов"

#: pynicotine/gtkgui/userbrowse.py:997
msgid "Upload Folder (with Subfolders) To User"
msgstr "Раздать каталог (с подкаталогами) для пользователя"

#: pynicotine/gtkgui/userbrowse.py:999
msgid "Upload Folder To User"
msgstr "Раздать каталог пользователю"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
msgid "Enter the name of the user you want to upload to:"
msgstr "Введите имя пользователя, которому вы хотите раздать файлы:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
msgid "_Upload"
msgstr "Раздать"

#: pynicotine/gtkgui/userbrowse.py:1139
msgid "Select Destination Folder for Files"
msgstr "Выбрать каталог назначения для файлов"

#: pynicotine/gtkgui/userbrowse.py:1161
msgid "Upload File(s) To User"
msgstr "Раздать файл(ы) пользователю"

#: pynicotine/gtkgui/userinfo.py:376
msgid "Copy Picture"
msgstr "Скопировать изображение"

#: pynicotine/gtkgui/userinfo.py:377
msgid "Save Picture"
msgstr "Сохранить изображение"

#: pynicotine/gtkgui/userinfo.py:468
#, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Не удалось загрузить аватар пользователя %(user)s: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Невозможно получить информацию о пользователе. Либо у вас обоих закрыты "
"порты, либо пользователь не в сети, либо возникла временная проблема с "
"соединением."

#: pynicotine/gtkgui/userinfo.py:577
msgid "Remove _Buddy"
msgstr "Удалить из списка друзей"

#: pynicotine/gtkgui/userinfo.py:577
msgid "Add _Buddy"
msgstr "Добавить в друзья"

#: pynicotine/gtkgui/userinfo.py:581
msgid "Unban User"
msgstr "Разблокировать пользователя"

#: pynicotine/gtkgui/userinfo.py:585
msgid "Unignore User"
msgstr "Перестать игнорировать пользователя"

#: pynicotine/gtkgui/userinfo.py:613
msgid "Yes"
msgstr "Да"

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr "Нет"

#: pynicotine/gtkgui/userinfo.py:773
msgid "Please enter number of days."
msgstr "Пожалуйста, введите количество дней."

#: pynicotine/gtkgui/userinfo.py:787
#, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Подарить дни ваших привилегий Soulseek пользователю %(user)s (%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, python-format
msgid "%(days)s days left"
msgstr "Осталось %(days)s дней"

#: pynicotine/gtkgui/userinfo.py:795
msgid "Gift Privileges"
msgstr "Сделать подарок"

#: pynicotine/gtkgui/userinfo.py:797
msgid "_Give Privileges"
msgstr "Дать привилегии"

#: pynicotine/gtkgui/widgets/dialogs.py:309
msgid "Close"
msgstr "Закрыть"

#: pynicotine/gtkgui/widgets/dialogs.py:488
msgid "_Yes"
msgstr "_Да"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
msgid "_OK"
msgstr "ОК"

#: pynicotine/gtkgui/widgets/filechooser.py:39
msgid "Select a File"
msgstr "Выбрать файл"

#: pynicotine/gtkgui/widgets/filechooser.py:174
msgid "Select a Folder"
msgstr "Выбрать каталог"

#: pynicotine/gtkgui/widgets/filechooser.py:179
msgid "_Select"
msgstr "Выбрать"

#: pynicotine/gtkgui/widgets/filechooser.py:196
msgid "Select an Image"
msgstr "Выбрать изображение"

#: pynicotine/gtkgui/widgets/filechooser.py:203
msgid "All images"
msgstr "Все изображения"

#: pynicotine/gtkgui/widgets/filechooser.py:241
msgid "Save as…"
msgstr "Сохранить как…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
msgid "(None)"
msgstr "(Пусто)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
msgid "Close Tab"
msgstr "Закрыть вкладку"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
msgid "Close All Tabs?"
msgstr "Закрыть все вкладки?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
msgid "Do you really want to close all tabs?"
msgstr "Вы действительно хотите закрыть все вкладки?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, python-format
msgid "%i Unread Tab(s)"
msgstr "%i Непрочитанные вкладки"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
msgid "All Tabs"
msgstr "Все вкладки"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
msgid "Re_open Closed Tab"
msgstr "Открыть заново закрытую вкладку"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, python-format
msgid "%s File(s) Selected"
msgstr "%s выделенных файла"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
msgid "_Browse Files"
msgstr "_Просмотр файлов"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
msgid "_Add Buddy"
msgstr "_Добавить в друзья"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
msgid "Show IP A_ddress"
msgstr "Показать IP-а_дрес"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
msgid "Private Rooms"
msgstr "Комнаты"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, python-format
msgid "Remove from Private Room %s"
msgstr "Удалить из комнаты %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, python-format
msgid "Add to Private Room %s"
msgstr "Пригласить в комнату %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, python-format
msgid "Remove as Operator of %s"
msgstr "Отобрать статус «Оператор» в %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, python-format
msgid "Add as Operator of %s"
msgstr "Назначить «Оператором» в %s"

#: pynicotine/gtkgui/widgets/textentry.py:37
msgid "Send message…"
msgstr "Отправить сообщение…"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
msgid "Find Previous Match"
msgstr "Найти предыдущее совпадение"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
msgid "Find Next Match"
msgstr "Найти следующее совпадение"

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr "--- старые сообщения выше ---"

#: pynicotine/gtkgui/widgets/theme.py:243
msgid "Executable"
msgstr "Исполняемый"

#: pynicotine/gtkgui/widgets/theme.py:244
msgid "Audio"
msgstr "Аудио"

#: pynicotine/gtkgui/widgets/theme.py:245
msgid "Image"
msgstr "Изображение"

#: pynicotine/gtkgui/widgets/theme.py:246
msgid "Archive"
msgstr "Архив"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
msgid "Miscellaneous"
msgstr "Разное"

#: pynicotine/gtkgui/widgets/theme.py:248
msgid "Video"
msgstr "Видео"

#: pynicotine/gtkgui/widgets/theme.py:249
msgid "Document"
msgstr "Документ"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr "Текст"

#: pynicotine/gtkgui/widgets/theme.py:357
#, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "Ошибка при загрузке пользовательского значка %(path)s: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Hide Nicotine+"
msgstr "Скрыть"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Show Nicotine+"
msgstr "Показать"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, python-format
msgid "Column #%i"
msgstr "Столбец №%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
msgid "Ungrouped"
msgstr "по умолчанию"

#: pynicotine/gtkgui/widgets/treeview.py:960
msgid "Group by Folder"
msgstr "по каталогу"

#: pynicotine/gtkgui/widgets/treeview.py:963
msgid "Group by User"
msgstr "по пользователю"

#: pynicotine/headless/application.py:77
#, python-format
msgid "Do you really want to exit? %s"
msgstr "Вы действительно хотите выйти? %s"

#: pynicotine/headless/application.py:81
#, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr "Пользователь %s уже существует, а введенный пароль некорректный."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr ""
"Введите %s для входа в систему с другим именем пользователя или паролем."

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
msgid "Password: "
msgstr "Пароль: "

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Чтобы создать новую учетную запись Soulseek, введите желаемое имя "
"пользователя и пароль. Если у вас уже есть учетная запись, введите "
"существующие данные для входа."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr "Следующие раздачи не доступны:"

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr "Пересканировать? %s"

#: pynicotine/logfacility.py:181
#, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr "Не удалось записать в файл журнала «%(filename)s»: %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Не удается получить доступ к файлу журнала %(path)s: %(error)s"

#: pynicotine/networkfilter.py:40
msgid "Andorra"
msgstr "Андорра"

#: pynicotine/networkfilter.py:41
msgid "United Arab Emirates"
msgstr "Объединенные Арабские Эмираты"

#: pynicotine/networkfilter.py:42
msgid "Afghanistan"
msgstr "Афганистан"

#: pynicotine/networkfilter.py:43
msgid "Antigua & Barbuda"
msgstr "Антигуа и Барбуда"

#: pynicotine/networkfilter.py:44
msgid "Anguilla"
msgstr "Ангилья"

#: pynicotine/networkfilter.py:45
msgid "Albania"
msgstr "Албания"

#: pynicotine/networkfilter.py:46
msgid "Armenia"
msgstr "Армения"

#: pynicotine/networkfilter.py:47
msgid "Angola"
msgstr "Ангола"

#: pynicotine/networkfilter.py:48
msgid "Antarctica"
msgstr "Антарктида"

#: pynicotine/networkfilter.py:49
msgid "Argentina"
msgstr "Аргентина"

#: pynicotine/networkfilter.py:50
msgid "American Samoa"
msgstr "Американское Самоа"

#: pynicotine/networkfilter.py:51
msgid "Austria"
msgstr "Австрия"

#: pynicotine/networkfilter.py:52
msgid "Australia"
msgstr "Австралия"

#: pynicotine/networkfilter.py:53
msgid "Aruba"
msgstr "Аруба"

#: pynicotine/networkfilter.py:54
msgid "Åland Islands"
msgstr "Аландские острова"

#: pynicotine/networkfilter.py:55
msgid "Azerbaijan"
msgstr "Азербайджан"

#: pynicotine/networkfilter.py:56
msgid "Bosnia & Herzegovina"
msgstr "Босния и Герцеговина"

#: pynicotine/networkfilter.py:57
msgid "Barbados"
msgstr "Барбадос"

#: pynicotine/networkfilter.py:58
msgid "Bangladesh"
msgstr "Бангладеш"

#: pynicotine/networkfilter.py:59
msgid "Belgium"
msgstr "Бельгия"

#: pynicotine/networkfilter.py:60
msgid "Burkina Faso"
msgstr "Буркина-Фасо"

#: pynicotine/networkfilter.py:61
msgid "Bulgaria"
msgstr "Болгария"

#: pynicotine/networkfilter.py:62
msgid "Bahrain"
msgstr "Бахрейн"

#: pynicotine/networkfilter.py:63
msgid "Burundi"
msgstr "Бурунди"

#: pynicotine/networkfilter.py:64
msgid "Benin"
msgstr "Бенин"

#: pynicotine/networkfilter.py:65
msgid "Saint Barthelemy"
msgstr "Сен-Бартелеми"

#: pynicotine/networkfilter.py:66
msgid "Bermuda"
msgstr "Бермуды"

#: pynicotine/networkfilter.py:67
msgid "Brunei Darussalam"
msgstr "Бруней-Даруссалам"

#: pynicotine/networkfilter.py:68
msgid "Bolivia"
msgstr "Боливия"

#: pynicotine/networkfilter.py:69
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Бонайре, Синт-Эстатиус и Саба"

#: pynicotine/networkfilter.py:70
msgid "Brazil"
msgstr "Бразилия"

#: pynicotine/networkfilter.py:71
msgid "Bahamas"
msgstr "Багамы"

#: pynicotine/networkfilter.py:72
msgid "Bhutan"
msgstr "Бутан"

#: pynicotine/networkfilter.py:73
msgid "Bouvet Island"
msgstr "Остров Буве"

#: pynicotine/networkfilter.py:74
msgid "Botswana"
msgstr "Ботсвана"

#: pynicotine/networkfilter.py:75
msgid "Belarus"
msgstr "Беларусь"

#: pynicotine/networkfilter.py:76
msgid "Belize"
msgstr "Белиз"

#: pynicotine/networkfilter.py:77
msgid "Canada"
msgstr "Канада"

#: pynicotine/networkfilter.py:78
msgid "Cocos (Keeling) Islands"
msgstr "Кокосовые (Килинг) острова"

#: pynicotine/networkfilter.py:79
msgid "Democratic Republic of Congo"
msgstr "Демократическая Республика Конго"

#: pynicotine/networkfilter.py:80
msgid "Central African Republic"
msgstr "Центрально-Африканская Республика"

#: pynicotine/networkfilter.py:81
msgid "Congo"
msgstr "Конго"

#: pynicotine/networkfilter.py:82
msgid "Switzerland"
msgstr "Швейцария"

#: pynicotine/networkfilter.py:83
msgid "Ivory Coast"
msgstr "Кот-д'Ивуар"

#: pynicotine/networkfilter.py:84
msgid "Cook Islands"
msgstr "Острова Кука"

#: pynicotine/networkfilter.py:85
msgid "Chile"
msgstr "Чили"

#: pynicotine/networkfilter.py:86
msgid "Cameroon"
msgstr "Камерун"

#: pynicotine/networkfilter.py:87
msgid "China"
msgstr "Китай"

#: pynicotine/networkfilter.py:88
msgid "Colombia"
msgstr "Колумбия"

#: pynicotine/networkfilter.py:89
msgid "Costa Rica"
msgstr "Коста-Рика"

#: pynicotine/networkfilter.py:90
msgid "Cuba"
msgstr "Куба"

#: pynicotine/networkfilter.py:91
msgid "Cabo Verde"
msgstr "Кабо-Верде"

#: pynicotine/networkfilter.py:92
msgid "Curaçao"
msgstr "Кюрасао"

#: pynicotine/networkfilter.py:93
msgid "Christmas Island"
msgstr "Остров Рождества"

#: pynicotine/networkfilter.py:94
msgid "Cyprus"
msgstr "Кипр"

#: pynicotine/networkfilter.py:95
msgid "Czechia"
msgstr "Чехия"

#: pynicotine/networkfilter.py:96
msgid "Germany"
msgstr "Германия"

#: pynicotine/networkfilter.py:97
msgid "Djibouti"
msgstr "Джибути"

#: pynicotine/networkfilter.py:98
msgid "Denmark"
msgstr "Дания"

#: pynicotine/networkfilter.py:99
msgid "Dominica"
msgstr "Доминика"

#: pynicotine/networkfilter.py:100
msgid "Dominican Republic"
msgstr "Доминиканская Республика"

#: pynicotine/networkfilter.py:101
msgid "Algeria"
msgstr "Алжир"

#: pynicotine/networkfilter.py:102
msgid "Ecuador"
msgstr "Эквадор"

#: pynicotine/networkfilter.py:103
msgid "Estonia"
msgstr "Эстония"

#: pynicotine/networkfilter.py:104
msgid "Egypt"
msgstr "Египет"

#: pynicotine/networkfilter.py:105
msgid "Western Sahara"
msgstr "Западная Сахара"

#: pynicotine/networkfilter.py:106
msgid "Eritrea"
msgstr "Эритрея"

#: pynicotine/networkfilter.py:107
msgid "Spain"
msgstr "Испания"

#: pynicotine/networkfilter.py:108
msgid "Ethiopia"
msgstr "Эфиопия"

#: pynicotine/networkfilter.py:109
msgid "Europe"
msgstr "Европа"

#: pynicotine/networkfilter.py:110
msgid "Finland"
msgstr "Финляндия"

#: pynicotine/networkfilter.py:111
msgid "Fiji"
msgstr "Фиджи"

#: pynicotine/networkfilter.py:112
msgid "Falkland Islands (Malvinas)"
msgstr "Фолклендские (Мальвинские) острова"

#: pynicotine/networkfilter.py:113
msgid "Micronesia"
msgstr "Микронезия"

#: pynicotine/networkfilter.py:114
msgid "Faroe Islands"
msgstr "Фарерские острова"

#: pynicotine/networkfilter.py:115
msgid "France"
msgstr "Франция"

#: pynicotine/networkfilter.py:116
msgid "Gabon"
msgstr "Габон"

#: pynicotine/networkfilter.py:117
msgid "Great Britain"
msgstr "Великобритания"

#: pynicotine/networkfilter.py:118
msgid "Grenada"
msgstr "Гренада"

#: pynicotine/networkfilter.py:119
msgid "Georgia"
msgstr "Грузия"

#: pynicotine/networkfilter.py:120
msgid "French Guiana"
msgstr "Французская Гвиана"

#: pynicotine/networkfilter.py:121
msgid "Guernsey"
msgstr "Гернси"

#: pynicotine/networkfilter.py:122
msgid "Ghana"
msgstr "Гана"

#: pynicotine/networkfilter.py:123
msgid "Gibraltar"
msgstr "Гибралтар"

#: pynicotine/networkfilter.py:124
msgid "Greenland"
msgstr "Гренландия"

#: pynicotine/networkfilter.py:125
msgid "Gambia"
msgstr "Гамбия"

#: pynicotine/networkfilter.py:126
msgid "Guinea"
msgstr "Гвинея"

#: pynicotine/networkfilter.py:127
msgid "Guadeloupe"
msgstr "Гваделупа"

#: pynicotine/networkfilter.py:128
msgid "Equatorial Guinea"
msgstr "Экваториальная Гвинея"

#: pynicotine/networkfilter.py:129
msgid "Greece"
msgstr "Греция"

#: pynicotine/networkfilter.py:130
msgid "South Georgia & South Sandwich Islands"
msgstr "Южная Георгия и Южные Сандвичевы острова"

#: pynicotine/networkfilter.py:131
msgid "Guatemala"
msgstr "Гватемала"

#: pynicotine/networkfilter.py:132
msgid "Guam"
msgstr "Гуам"

#: pynicotine/networkfilter.py:133
msgid "Guinea-Bissau"
msgstr "Гвинея-Бисау"

#: pynicotine/networkfilter.py:134
msgid "Guyana"
msgstr "Гайана"

#: pynicotine/networkfilter.py:135
msgid "Hong Kong"
msgstr "Гонконг"

#: pynicotine/networkfilter.py:136
msgid "Heard & McDonald Islands"
msgstr "Острова Херд и Макдональд"

#: pynicotine/networkfilter.py:137
msgid "Honduras"
msgstr "Гондурас"

#: pynicotine/networkfilter.py:138
msgid "Croatia"
msgstr "Хорватия"

#: pynicotine/networkfilter.py:139
msgid "Haiti"
msgstr "Гаити"

#: pynicotine/networkfilter.py:140
msgid "Hungary"
msgstr "Венгрия"

#: pynicotine/networkfilter.py:141
msgid "Indonesia"
msgstr "Индонезия"

#: pynicotine/networkfilter.py:142
msgid "Ireland"
msgstr "Ирландия"

#: pynicotine/networkfilter.py:143
msgid "Israel"
msgstr "Израиль"

#: pynicotine/networkfilter.py:144
msgid "Isle of Man"
msgstr "Остров Мэн"

#: pynicotine/networkfilter.py:145
msgid "India"
msgstr "Индия"

#: pynicotine/networkfilter.py:146
msgid "British Indian Ocean Territory"
msgstr "Британская территория Индийского океана"

#: pynicotine/networkfilter.py:147
msgid "Iraq"
msgstr "Ирак"

#: pynicotine/networkfilter.py:148
msgid "Iran"
msgstr "Иран"

#: pynicotine/networkfilter.py:149
msgid "Iceland"
msgstr "Исландия"

#: pynicotine/networkfilter.py:150
msgid "Italy"
msgstr "Италия"

#: pynicotine/networkfilter.py:151
msgid "Jersey"
msgstr "Джерси"

#: pynicotine/networkfilter.py:152
msgid "Jamaica"
msgstr "Ямайка"

#: pynicotine/networkfilter.py:153
msgid "Jordan"
msgstr "Иордания"

#: pynicotine/networkfilter.py:154
msgid "Japan"
msgstr "Япония"

#: pynicotine/networkfilter.py:155
msgid "Kenya"
msgstr "Кения"

#: pynicotine/networkfilter.py:156
msgid "Kyrgyzstan"
msgstr "Кыргызстан"

#: pynicotine/networkfilter.py:157
msgid "Cambodia"
msgstr "Камбоджа"

#: pynicotine/networkfilter.py:158
msgid "Kiribati"
msgstr "Кирибати"

#: pynicotine/networkfilter.py:159
msgid "Comoros"
msgstr "Коморские острова"

#: pynicotine/networkfilter.py:160
msgid "Saint Kitts & Nevis"
msgstr "Сент-Китс и Невис"

#: pynicotine/networkfilter.py:161
msgid "North Korea"
msgstr "Северная Корея"

#: pynicotine/networkfilter.py:162
msgid "South Korea"
msgstr "Южная Корея"

#: pynicotine/networkfilter.py:163
msgid "Kuwait"
msgstr "Кувейт"

#: pynicotine/networkfilter.py:164
msgid "Cayman Islands"
msgstr "Каймановы острова"

#: pynicotine/networkfilter.py:165
msgid "Kazakhstan"
msgstr "Казахстан"

#: pynicotine/networkfilter.py:166
msgid "Laos"
msgstr "Лаос"

#: pynicotine/networkfilter.py:167
msgid "Lebanon"
msgstr "Ливан"

#: pynicotine/networkfilter.py:168
msgid "Saint Lucia"
msgstr "Санкт-Люсия"

#: pynicotine/networkfilter.py:169
msgid "Liechtenstein"
msgstr "Лихтенштейн"

#: pynicotine/networkfilter.py:170
msgid "Sri Lanka"
msgstr "Шри-Ланка"

#: pynicotine/networkfilter.py:171
msgid "Liberia"
msgstr "Либерия"

#: pynicotine/networkfilter.py:172
msgid "Lesotho"
msgstr "Лесото"

#: pynicotine/networkfilter.py:173
msgid "Lithuania"
msgstr "Литва"

#: pynicotine/networkfilter.py:174
msgid "Luxembourg"
msgstr "Люксембург"

#: pynicotine/networkfilter.py:175
msgid "Latvia"
msgstr "Латвия"

#: pynicotine/networkfilter.py:176
msgid "Libya"
msgstr "Ливия"

#: pynicotine/networkfilter.py:177
msgid "Morocco"
msgstr "Марокко"

#: pynicotine/networkfilter.py:178
msgid "Monaco"
msgstr "Монако"

#: pynicotine/networkfilter.py:179
msgid "Moldova"
msgstr "Молдова"

#: pynicotine/networkfilter.py:180
msgid "Montenegro"
msgstr "Черногория"

#: pynicotine/networkfilter.py:181
msgid "Saint Martin"
msgstr "Сен-Мартен"

#: pynicotine/networkfilter.py:182
msgid "Madagascar"
msgstr "Мадагаскар"

#: pynicotine/networkfilter.py:183
msgid "Marshall Islands"
msgstr "Маршалловы острова"

#: pynicotine/networkfilter.py:184
msgid "North Macedonia"
msgstr "Северная Македония"

#: pynicotine/networkfilter.py:185
msgid "Mali"
msgstr "Мали"

#: pynicotine/networkfilter.py:186
msgid "Myanmar"
msgstr "Мьянма"

#: pynicotine/networkfilter.py:187
msgid "Mongolia"
msgstr "Монголия"

#: pynicotine/networkfilter.py:188
msgid "Macau"
msgstr "Макао"

#: pynicotine/networkfilter.py:189
msgid "Northern Mariana Islands"
msgstr "Северные Марианские острова"

#: pynicotine/networkfilter.py:190
msgid "Martinique"
msgstr "Мартиника"

#: pynicotine/networkfilter.py:191
msgid "Mauritania"
msgstr "Мавритания"

#: pynicotine/networkfilter.py:192
msgid "Montserrat"
msgstr "Монтсеррат"

#: pynicotine/networkfilter.py:193
msgid "Malta"
msgstr "Мальта"

#: pynicotine/networkfilter.py:194
msgid "Mauritius"
msgstr "Маврикий"

#: pynicotine/networkfilter.py:195
msgid "Maldives"
msgstr "Мальдивы"

#: pynicotine/networkfilter.py:196
msgid "Malawi"
msgstr "Малави"

#: pynicotine/networkfilter.py:197
msgid "Mexico"
msgstr "Мексика"

#: pynicotine/networkfilter.py:198
msgid "Malaysia"
msgstr "Малайзия"

#: pynicotine/networkfilter.py:199
msgid "Mozambique"
msgstr "Мозамбик"

#: pynicotine/networkfilter.py:200
msgid "Namibia"
msgstr "Намибия"

#: pynicotine/networkfilter.py:201
msgid "New Caledonia"
msgstr "Новая Каледония"

#: pynicotine/networkfilter.py:202
msgid "Niger"
msgstr "Нигер"

#: pynicotine/networkfilter.py:203
msgid "Norfolk Island"
msgstr "Остров Норфолк"

#: pynicotine/networkfilter.py:204
msgid "Nigeria"
msgstr "Нигерия"

#: pynicotine/networkfilter.py:205
msgid "Nicaragua"
msgstr "Никарагуа"

#: pynicotine/networkfilter.py:206
msgid "Netherlands"
msgstr "Нидерланды"

#: pynicotine/networkfilter.py:207
msgid "Norway"
msgstr "Норвегия"

#: pynicotine/networkfilter.py:208
msgid "Nepal"
msgstr "Непал"

#: pynicotine/networkfilter.py:209
msgid "Nauru"
msgstr "Науру"

#: pynicotine/networkfilter.py:210
msgid "Niue"
msgstr "Ниуэ"

#: pynicotine/networkfilter.py:211
msgid "New Zealand"
msgstr "Новая Зеландия"

#: pynicotine/networkfilter.py:212
msgid "Oman"
msgstr "Оман"

#: pynicotine/networkfilter.py:213
msgid "Panama"
msgstr "Панама"

#: pynicotine/networkfilter.py:214
msgid "Peru"
msgstr "Перу"

#: pynicotine/networkfilter.py:215
msgid "French Polynesia"
msgstr "Французская Полинезия"

#: pynicotine/networkfilter.py:216
msgid "Papua New Guinea"
msgstr "Папуа - Новая Гвинея"

#: pynicotine/networkfilter.py:217
msgid "Philippines"
msgstr "Филиппины"

#: pynicotine/networkfilter.py:218
msgid "Pakistan"
msgstr "Пакистан"

#: pynicotine/networkfilter.py:219
msgid "Poland"
msgstr "Польша"

#: pynicotine/networkfilter.py:220
msgid "Saint Pierre & Miquelon"
msgstr "Сен-Пьер и Микелон"

#: pynicotine/networkfilter.py:221
msgid "Pitcairn"
msgstr "Питкэрн"

#: pynicotine/networkfilter.py:222
msgid "Puerto Rico"
msgstr "Пуэрто-Рико"

#: pynicotine/networkfilter.py:223
msgid "State of Palestine"
msgstr "Государство Палестина"

#: pynicotine/networkfilter.py:224
msgid "Portugal"
msgstr "Португалия"

#: pynicotine/networkfilter.py:225
msgid "Palau"
msgstr "Палау"

#: pynicotine/networkfilter.py:226
msgid "Paraguay"
msgstr "Парагвай"

#: pynicotine/networkfilter.py:227
msgid "Qatar"
msgstr "Катар"

#: pynicotine/networkfilter.py:228
msgid "Réunion"
msgstr "Реюньон"

#: pynicotine/networkfilter.py:229
msgid "Romania"
msgstr "Румыния"

#: pynicotine/networkfilter.py:230
msgid "Serbia"
msgstr "Сербия"

#: pynicotine/networkfilter.py:231
msgid "Russia"
msgstr "Россия"

#: pynicotine/networkfilter.py:232
msgid "Rwanda"
msgstr "Руанда"

#: pynicotine/networkfilter.py:233
msgid "Saudi Arabia"
msgstr "Саудовская Аравия"

#: pynicotine/networkfilter.py:234
msgid "Solomon Islands"
msgstr "Соломоновы острова"

#: pynicotine/networkfilter.py:235
msgid "Seychelles"
msgstr "Сейшельские острова"

#: pynicotine/networkfilter.py:236
msgid "Sudan"
msgstr "Судан"

#: pynicotine/networkfilter.py:237
msgid "Sweden"
msgstr "Швеция"

#: pynicotine/networkfilter.py:238
msgid "Singapore"
msgstr "Сингапур"

#: pynicotine/networkfilter.py:239
msgid "Saint Helena"
msgstr "Святой Елены"

#: pynicotine/networkfilter.py:240
msgid "Slovenia"
msgstr "Словения"

#: pynicotine/networkfilter.py:241
msgid "Svalbard & Jan Mayen Islands"
msgstr "Острова Шпицберген и Ян-Майен"

#: pynicotine/networkfilter.py:242
msgid "Slovak Republic"
msgstr "Словацкая Республика"

#: pynicotine/networkfilter.py:243
msgid "Sierra Leone"
msgstr "Сьерра-Леоне"

#: pynicotine/networkfilter.py:244
msgid "San Marino"
msgstr "Сан-Марино"

#: pynicotine/networkfilter.py:245
msgid "Senegal"
msgstr "Сенегал"

#: pynicotine/networkfilter.py:246
msgid "Somalia"
msgstr "Сомали"

#: pynicotine/networkfilter.py:247
msgid "Suriname"
msgstr "Суринам"

#: pynicotine/networkfilter.py:248
msgid "South Sudan"
msgstr "Южный Судан"

#: pynicotine/networkfilter.py:249
msgid "Sao Tome & Principe"
msgstr "Сан-Томе и Принсипи"

#: pynicotine/networkfilter.py:250
msgid "El Salvador"
msgstr "Сальвадор"

#: pynicotine/networkfilter.py:251
msgid "Sint Maarten"
msgstr "Синт-Мартен"

#: pynicotine/networkfilter.py:252
msgid "Syria"
msgstr "Сирия"

#: pynicotine/networkfilter.py:253
msgid "Eswatini"
msgstr "Эсватини"

#: pynicotine/networkfilter.py:254
msgid "Turks & Caicos Islands"
msgstr "Острова Теркс и Кайкос"

#: pynicotine/networkfilter.py:255
msgid "Chad"
msgstr "Чад"

#: pynicotine/networkfilter.py:256
msgid "French Southern Territories"
msgstr "Южные Французские Территории"

#: pynicotine/networkfilter.py:257
msgid "Togo"
msgstr "Того"

#: pynicotine/networkfilter.py:258
msgid "Thailand"
msgstr "Таиланд"

#: pynicotine/networkfilter.py:259
msgid "Tajikistan"
msgstr "Таджикистан"

#: pynicotine/networkfilter.py:260
msgid "Tokelau"
msgstr "Токелау"

#: pynicotine/networkfilter.py:261
msgid "Timor-Leste"
msgstr "Тимор-Лешти"

#: pynicotine/networkfilter.py:262
msgid "Turkmenistan"
msgstr "Туркменистан"

#: pynicotine/networkfilter.py:263
msgid "Tunisia"
msgstr "Тунис"

#: pynicotine/networkfilter.py:264
msgid "Tonga"
msgstr "Тонга"

#: pynicotine/networkfilter.py:265
msgid "Türkiye"
msgstr "Турция"

#: pynicotine/networkfilter.py:266
msgid "Trinidad & Tobago"
msgstr "Тринидад и Тобаго"

#: pynicotine/networkfilter.py:267
msgid "Tuvalu"
msgstr "Тувалу"

#: pynicotine/networkfilter.py:268
msgid "Taiwan"
msgstr "Тайвань"

#: pynicotine/networkfilter.py:269
msgid "Tanzania"
msgstr "Танзания"

#: pynicotine/networkfilter.py:270
msgid "Ukraine"
msgstr "Украина"

#: pynicotine/networkfilter.py:271
msgid "Uganda"
msgstr "Уганда"

#: pynicotine/networkfilter.py:272
msgid "U.S. Minor Outlying Islands"
msgstr "Внешние малые острова США"

#: pynicotine/networkfilter.py:273
msgid "United States"
msgstr "Соединенные Штаты"

#: pynicotine/networkfilter.py:274
msgid "Uruguay"
msgstr "Уругвай"

#: pynicotine/networkfilter.py:275
msgid "Uzbekistan"
msgstr "Узбекистан"

#: pynicotine/networkfilter.py:276
msgid "Holy See (Vatican City State)"
msgstr "Святой Престол (государство-город Ватикан)"

#: pynicotine/networkfilter.py:277
msgid "Saint Vincent & The Grenadines"
msgstr "Сент-Винсент и Гренадины"

#: pynicotine/networkfilter.py:278
msgid "Venezuela"
msgstr "Венесуэла"

#: pynicotine/networkfilter.py:279
msgid "British Virgin Islands"
msgstr "Британские Виргинские острова"

#: pynicotine/networkfilter.py:280
msgid "U.S. Virgin Islands"
msgstr "Виргинские острова США"

#: pynicotine/networkfilter.py:281
msgid "Viet Nam"
msgstr "Вьетнам"

#: pynicotine/networkfilter.py:282
msgid "Vanuatu"
msgstr "Вануату"

#: pynicotine/networkfilter.py:283
msgid "Wallis & Futuna"
msgstr "Уоллис и Футуна"

#: pynicotine/networkfilter.py:284
msgid "Samoa"
msgstr "Самоа"

#: pynicotine/networkfilter.py:285
msgid "Yemen"
msgstr "Йемен"

#: pynicotine/networkfilter.py:286
msgid "Mayotte"
msgstr "Майотта"

#: pynicotine/networkfilter.py:287
msgid "South Africa"
msgstr "Южная Африка"

#: pynicotine/networkfilter.py:288
msgid "Zambia"
msgstr "Замбия"

#: pynicotine/networkfilter.py:289
msgid "Zimbabwe"
msgstr "Зимбабве"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "Ошибка преобразования текста в речь: %s"

#: pynicotine/nowplaying.py:130
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr "Last.fm: укажите имя пользователя и ключ API"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
msgid "Now Playing Error"
msgstr "Ошибка воспроизведения"

#: pynicotine/nowplaying.py:140
#, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm: не удалось подключиться к Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:162
#, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr ""
"Last.fm: Не удалось получить последний трек от Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:196
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: не удалось найти подходящий проигрыватель MPRIS"

#: pynicotine/nowplaying.py:201
#, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr ""
"Найдено несколько плееров MPRIS: %(players)s. Будет использован: %(player)s"

#: pynicotine/nowplaying.py:204
#, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Автоопределение плеера MPRIS: %s"

#: pynicotine/nowplaying.py:219
#, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr "MPRIS: Что-то пошло не так при запросе %(player)s: %(exception)s"

#: pynicotine/nowplaying.py:266
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "ListenBrainz: укажите свое имя пользователя"

#: pynicotine/nowplaying.py:275
#, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "ListenBrainz: не удалось подключиться к ListenBrainz: %(error)s"

#: pynicotine/nowplaying.py:283
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "ListenBrainz: Похоже, вы сейчас ничего не слушаете"

#: pynicotine/nowplaying.py:297
#, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz: Не удалось получить текущий трек из ListenBrainz: %(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
msgid "Network Filters"
msgstr "Сетевые фильтры"

#: pynicotine/plugins/core_commands/__init__.py:44
msgid "List available commands"
msgstr "Список доступных команд"

#: pynicotine/plugins/core_commands/__init__.py:49
msgid "Connect to the server"
msgstr "Подключение к серверу"

#: pynicotine/plugins/core_commands/__init__.py:53
msgid "Disconnect from the server"
msgstr "Отключение от сервера"

#: pynicotine/plugins/core_commands/__init__.py:58
msgid "Toggle away status"
msgstr "Переключить статус на \"Отсутствую\""

#: pynicotine/plugins/core_commands/__init__.py:62
msgid "Manage plugins"
msgstr "Управление плагинами"

#: pynicotine/plugins/core_commands/__init__.py:74
msgid "Clear chat window"
msgstr "Очистить окно чата"

#: pynicotine/plugins/core_commands/__init__.py:80
msgid "Say something in the third-person"
msgstr "Сказать что-нибудь от третьего лица"

#: pynicotine/plugins/core_commands/__init__.py:87
msgid "Announce the song currently playing"
msgstr "Сообщить песню, которая сейчас играет"

#: pynicotine/plugins/core_commands/__init__.py:94
msgid "Join chat room"
msgstr "Присоединиться к чату"

#: pynicotine/plugins/core_commands/__init__.py:102
msgid "Leave chat room"
msgstr "Покинуть чат"

#: pynicotine/plugins/core_commands/__init__.py:110
msgid "Say message in specified chat room"
msgstr "Отправить сообщение в указанный чат"

#: pynicotine/plugins/core_commands/__init__.py:117
msgid "Open private chat"
msgstr "Открыть приватный чат"

#: pynicotine/plugins/core_commands/__init__.py:125
msgid "Close private chat"
msgstr "Закрыть приватный чат"

#: pynicotine/plugins/core_commands/__init__.py:133
msgid "Request user's client version"
msgstr "Запросить версию клиента пользователя"

#: pynicotine/plugins/core_commands/__init__.py:142
msgid "Send private message to user"
msgstr "Отправить приватное сообщение пользователю"

#: pynicotine/plugins/core_commands/__init__.py:150
msgid "Add user to buddy list"
msgstr "Добавить пользователя в список друзей"

#: pynicotine/plugins/core_commands/__init__.py:158
msgid "Remove buddy from buddy list"
msgstr "Убрать пользователя из списка друзей"

#: pynicotine/plugins/core_commands/__init__.py:166
msgid "Browse files of user"
msgstr "Посмотреть общие файлы пользователя"

#: pynicotine/plugins/core_commands/__init__.py:175
msgid "Show user profile information"
msgstr "Показать информацию о профиле"

#: pynicotine/plugins/core_commands/__init__.py:183
msgid "Show IP address or username"
msgstr "Показать IP-адрес или псевдоним пользователя"

#: pynicotine/plugins/core_commands/__init__.py:190
msgid "Block connections from user or IP address"
msgstr "Блокировать подключения от пользователя или IP-адреса"

#: pynicotine/plugins/core_commands/__init__.py:197
msgid "Remove user or IP address from ban lists"
msgstr "Убрать пользователя или IP-адрес из списка блокировки"

#: pynicotine/plugins/core_commands/__init__.py:204
msgid "Silence messages from user or IP address"
msgstr "Заглушить сообщения от пользователя или IP-адреса"

#: pynicotine/plugins/core_commands/__init__.py:212
msgid "Remove user or IP address from ignore lists"
msgstr "Убрать пользователя из игнора"

#: pynicotine/plugins/core_commands/__init__.py:220
msgid "Add share"
msgstr "Добавить обмен"

#: pynicotine/plugins/core_commands/__init__.py:226
msgid "Remove share"
msgstr "Убрать обмен"

#: pynicotine/plugins/core_commands/__init__.py:233
msgid "List shares"
msgstr "Список общих файлов"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Пересканировать общие файлы"

#: pynicotine/plugins/core_commands/__init__.py:246
msgid "Start global file search"
msgstr "Начать глобальный поиск файлов"

#: pynicotine/plugins/core_commands/__init__.py:254
msgid "Search files in joined rooms"
msgstr "Поиск файлов в присоединенных комнатах"

#: pynicotine/plugins/core_commands/__init__.py:262
msgid "Search files of all buddies"
msgstr "Поиск файлов у друзей"

#: pynicotine/plugins/core_commands/__init__.py:270
msgid "Search a user's shared files"
msgstr "Поиск по общим файлам пользователей"

#: pynicotine/plugins/core_commands/__init__.py:296
#, python-format
msgid "Listing %(num)i available commands:"
msgstr "Список %(num)i доступных команд:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr "Список %(num)i доступных команд, соответствующих «%(query)s»:"

#: pynicotine/plugins/core_commands/__init__.py:311
#, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Введите %(command)s, чтобы получить похожие команды"

#: pynicotine/plugins/core_commands/__init__.py:314
#, python-format
msgid "Type %(command)s to list available commands"
msgstr "Введите %(command)s, чтобы просмотреть список доступных команд"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, python-format
msgid "Not joined in room %s"
msgstr "Не присоединился к комнате %s"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr "Не обмениваться сообщениями с пользователем %s"

#: pynicotine/plugins/core_commands/__init__.py:408
#, python-format
msgid "Closed private chat of user %s"
msgstr "Закрыт приватный чат %s"

#: pynicotine/plugins/core_commands/__init__.py:476
#, python-format
msgid "Banned %s"
msgstr "Заблокирован %s"

#: pynicotine/plugins/core_commands/__init__.py:490
#, python-format
msgid "Unbanned %s"
msgstr "Разблокирован %s"

#: pynicotine/plugins/core_commands/__init__.py:503
#, python-format
msgid "Ignored %s"
msgstr "Игнорируется %s"

#: pynicotine/plugins/core_commands/__init__.py:517
#, python-format
msgid "Unignored %s"
msgstr "Больше не игнорируется %s"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr "%(num_listed)s перечисленные раздачи (%(num_total)s сконфигурированы)"

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr "Невозможно предоставить общий доступ к недоступной папке \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""
"Добавлено %(group_name)s общий доступ \"%(virtual_name)s\" (требуется "
"повторное сканирование)"

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr "Нет общего доступа с именем \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr "Удален общий доступ \"%s\" (требуется повторное сканирование)"

#: pynicotine/pluginsystem.py:413
msgid "Loading plugin system"
msgstr "Плагин загружается"

#: pynicotine/pluginsystem.py:516
#, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Не удалось загрузить плагин %(name)s. Имя каталога плагина содержит "
"недопустимые символы: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "Конфликтующая команда %(interface)s в плагине %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, python-format
msgid "Loaded plugin %s"
msgstr "Плагин загружен %s"

#: pynicotine/pluginsystem.py:579
#, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Не удалось загрузить плагин %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, python-format
msgid "Unloaded plugin %s"
msgstr "Плагин выгружен %s"

#: pynicotine/pluginsystem.py:648
#, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Не удалось выгрузить плагин %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Плагин %(module)s завершился с ошибкой %(errortype)s: %(error)s.\n"
"Отчёт: %(trace)s"

#: pynicotine/pluginsystem.py:810
msgid "No description"
msgstr "Без описания"

#: pynicotine/pluginsystem.py:887
#, python-format
msgid "Missing %s argument"
msgstr "Отсутствует %s аргумент"

#: pynicotine/pluginsystem.py:896
#, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Недопустимый аргумент, возможный вариант: %s"

#: pynicotine/pluginsystem.py:901
#, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Использование: %(command)s %(parameters)s"

#: pynicotine/pluginsystem.py:940
#, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Неизвестная команда: %(command)s. Введите %(help_command)s, чтобы "
"просмотреть список доступных команд."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
msgid "No UPnP devices found"
msgstr "Устройства с UPnP не найдены"

#: pynicotine/portmapper.py:633
#, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"%(protocol)s: не удалось пробросить внешний порт %(external_port)s: %(error)s"

#: pynicotine/portmapper.py:647
#, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"%(protocol)s: внешний порт %(external_port)s успешно переброшен на локальный "
"IP-адрес %(ip_address)s порт %(local_port)s"

#: pynicotine/privatechat.py:220
#, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Личное сообщение от пользователя \"%(user)s\": %(message)s"

#: pynicotine/search.py:368
#, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Поиск предмета в Списке желаемого \"%s\""

#: pynicotine/search.py:434
#, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Период ожидания списка желаемого установлен на %s секунд"

#: pynicotine/search.py:760
#, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr "Пользователь %(user)s ищет \"%(query)s\", находит %(num)i результатов"

#: pynicotine/shares.py:302
msgid "Rebuilding shares…"
msgstr "Перестроение списка раздач…"

#: pynicotine/shares.py:302
msgid "Rescanning shares…"
msgstr "Пересканирование списка раздач…"

#: pynicotine/shares.py:324
#, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Повторное сканирование завершено: найдено %(num)s каталогов"

#: pynicotine/shares.py:334
#, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"При повторном сканировании списка раздач произошла серьёзная ошибка. Если "
"проблема не исчезает, удалите файл %(dir)s/*.dbn и повторите попытку. Если "
"это не помогло, отправьте отчёт об ошибке: %(trace)s"

#: pynicotine/shares.py:582
#, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Ошибка при сканировании файла %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Ошибка при сканировании каталога %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "Ошибка при сканировании метаданных для файла %(path)s: %(error)s"

#: pynicotine/shares.py:1046
#, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr ""
"Повторное сканирование прервано из-за недоступности общих каталогов: %s"

#: pynicotine/shares.py:1184
#, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "%(user)s просматривает ваш список раздач"

#: pynicotine/slskmessages.py:3120
#, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Невозможно прочитать базу данных списка раздач. Пожалуйста, пересканируйте "
"список раздач. Ошибка: %s"

#: pynicotine/slskproto.py:500
#, python-format
msgid "Specified network interface '%s' is not available"
msgstr "Указанный сетевой интерфейс '%s' не существует"

#: pynicotine/slskproto.py:511
#, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Невозможно прослушивать порт %(port)s. Убедитесь, что его не использует "
"другое приложение, или выберите другой порт. Ошибка: %(error)s"

#: pynicotine/slskproto.py:523
#, python-format
msgid "Listening on port: %i"
msgstr "Прослушивание на порту %i"

#: pynicotine/slskproto.py:805
#, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "Не удаётся подключиться к серверу %(host)s:%(port)s: %(error)s"

#: pynicotine/slskproto.py:1095
#, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Повторное подключение к серверу через %s секунд"

#: pynicotine/slskproto.py:1170
#, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Подключение к %(host)s:%(port)s"

#: pynicotine/slskproto.py:1208
#, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Подключён к серверу %(host)s:%(port)s, вход в систему…"

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Отключён от сервера %(host)s:%(port)s"

#: pynicotine/slskproto.py:1499
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Вход в вашу учетную запись произведен в другом месте"

#: pynicotine/uploads.py:382
#, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Раздача завершена: пользователь %(user)s, IP-адрес %(ip)s, файл %(file)s"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Раздача прервана, пользователь %(user)s файл %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, python-format
msgid "Upload I/O error: %s"
msgstr "Ошибка ввода-вывода при отдаче: %s"

#: pynicotine/uploads.py:1103
#, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Раздача началась: пользователь %(user)s, IP-адрес %(ip)s, файл %(file)s"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr "Не удаётся создать каталог '%(folder)s', ошибка: %(error)s"

#: pynicotine/userbrowse.py:236
#, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Ошибка загрузки раздач с диска: %(error)s"

#: pynicotine/userbrowse.py:282
#, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr "Список раздач пользователя \"%(user)s\" сохранён в %(dir)s"

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr "Не удаётся сохранить раздачи, \"%(user)s\", ошибка: %(error)s"

#: pynicotine/userinfo.py:160
#, python-format
msgid "Picture saved to %s"
msgstr "Изображение сохранено в %s"

#: pynicotine/userinfo.py:163
#, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Не удается сохранить изображение в %(filename)s: %(error)s"

#: pynicotine/userinfo.py:190
#, python-format
msgid "User %(user)s is viewing your profile"
msgstr "Пользователь %(user)s просматривает ваш профиль"

#: pynicotine/users.py:272
#, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Невозможно подключиться к серверу. Причина: %s"

#: pynicotine/users.py:272
msgid "Cannot Connect"
msgstr "Не удаётся подключиться"

#: pynicotine/users.py:306
#, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr ""
"Невозможно получить IP-адрес пользователя %s, так как этот пользователь не в "
"сети"

#: pynicotine/users.py:315
#, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr "IP-адрес пользователя %(user)s: %(ip)s, порт %(port)i%(country)s"

#: pynicotine/users.py:433
msgid "Soulseek Announcement"
msgstr "Анонс Soulseek"

#: pynicotine/users.py:449
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"У вас нет привилегий. Привилегии не обязательны, но они позволяют вам "
"скачивать файлы быстрее, в отличие от непривилегированных пользователей."

#: pynicotine/users.py:455
#, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"Осталось %(days)i дней, %(hours)i часов, %(minutes)i минут, %(seconds)i "
"секунд до окончания привилегий Soulseek"

#: pynicotine/users.py:473
msgid "Your password has been changed"
msgstr "Ваш пароль был изменён"

#: pynicotine/users.py:473
msgid "Password Changed"
msgstr "Пароль изменён"

#: pynicotine/utils.py:574
#, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Не удается открыть путь к файлу %(path)s: %(error)s"

#: pynicotine/utils.py:621
#, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "Не удается открыть URL %(url)s: %(error)s"

#: pynicotine/utils.py:646
#, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr "Что-то пошло не так при чтении файла %(filename)s: %(error)s"

#: pynicotine/utils.py:651
#, python-format
msgid "Attempting to load backup of file %s"
msgstr "Попытка загрузить резервную копию файла %s"

#: pynicotine/utils.py:673
#, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Невозможно создать резервную копию файла %(path)s: %(error)s"

#: pynicotine/utils.py:694
#, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Невозможно сохранить файл %(path)s: %(error)s"

#: pynicotine/utils.py:705
#, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Невозможно восстановить предыдущий файл %(path)s: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
msgid "Add buddy…"
msgstr "Добавить в друзья…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
msgid "Toggle Text-to-Speech"
msgstr "Переключить преобразование текста в речь"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
msgid "Chat Room Command Help"
msgstr "Справка по командам комнаты"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
msgid "_Log"
msgstr "Журнал"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
msgid "Room Wall"
msgstr "Стена комнаты"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
msgid "R_oom Wall"
msgstr "Стена комнаты"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
msgid "Created by"
msgstr "Создатели"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
msgid "Translated by"
msgstr "Перевёл"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
msgid "License"
msgstr "Лицензия"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
msgid "Welcome to Nicotine+"
msgstr "Добро пожаловать в Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Если желаемое имя пользователя уже занято, вам будет предложено его изменить."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""
"Чтобы установить связь с другими участниками Soulseek, необходимо пробросить "
"на компьютер прослушивающий порт маршрутизатора."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Если ваш порт прослушивания закрыт, вы сможете подключаться только к "
"пользователям, чьи порты прослушивания открыты."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"При необходимости выберите другой порт для прослушивания ниже. Это также "
"можно сделать позже в настройках."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
msgid "Download Files to Folder"
msgstr "Загружать файлы в каталог"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
msgid "Share Folders"
msgstr "Раздать каталоги"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Пользователи Soulseek смогут скачивать ваши файлы, если вы добавите их в "
"общий доступ. Внесите свой вклад в сеть Soulseek, поделившись своей "
"собственной коллекцией, а также опубликовав то, что вы скачали у других "
"пользователей."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
msgid "You are ready to use Nicotine+!"
msgstr "Вы готовы к использованию Nicotine+!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""
"Soulseek - это незашифрованный протокол, не предназначенный для безопасного "
"общения."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Пожертвование для Soulseek дает вам привилегии на определенный период "
"времени. Если у вас есть привилегии, ваши загрузки будут поставлены в "
"очередь перед непривилегированными пользователями."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
msgid "Previous File"
msgstr "Предыдущий файл"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
msgid "Next File"
msgstr "Следующий файл"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
msgid "Name"
msgstr "Имя"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
msgid "Last Speed"
msgstr "Последняя скорость"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
msgid "_Export…"
msgstr "Экспорт…"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
msgid "Keyboard Shortcuts"
msgstr "Горячие клавиши"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
msgid "General"
msgstr "Общий"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr "Подключиться"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr "Отключиться"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
msgid "Rescan Shares"
msgstr "Пересканировать общие каталоги"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
msgid "Show Log Pane"
msgstr "Показать журнал событий"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
msgid "Confirm Quit"
msgstr "Подтверждать выход"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
msgid "Quit"
msgstr "Выход"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
msgid "Menus"
msgstr "Меню"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
msgid "Open Main Menu"
msgstr "Открыть главное меню"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
msgid "Open Context Menu"
msgstr "Открыть контекстное меню"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
msgid "Tabs"
msgstr "Вкладки"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
msgid "Change Main Tab"
msgstr "Изменить начальную вкладку"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
msgid "Go to Previous Secondary Tab"
msgstr "Перейти к предыдущей дополнительной вкладке"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
msgid "Go to Next Secondary Tab"
msgstr "Перейти к следующей дополнительной вкладке"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
msgid "Reopen Closed Secondary Tab"
msgstr "Повторно открыть закрытую дополнительную вкладку"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
msgid "Close Secondary Tab"
msgstr "Закрыть дополнительную вкладку"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
msgid "Lists"
msgstr "Списки"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
msgid "Copy Selected Cell"
msgstr "Скопировать выбранную ячейку"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
msgid "Select All"
msgstr "Выделить всё"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
msgid "Find"
msgstr "Найти"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
msgid "Remove Selected Row"
msgstr "Удалить выбранную строку"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
msgid "Editing"
msgstr "Редактирование"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
msgid "Cut"
msgstr "Вырезать"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
msgid "Paste"
msgstr "Вставить"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
msgid "Insert Emoji"
msgstr "Вставить эмодзи"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
msgid "File Transfers"
msgstr "Передача файлов"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
msgid "Resume / Retry Transfer"
msgstr "Возобновить / Повторить"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
msgid "Pause / Abort Transfer"
msgstr "Приостановить / Прервать передачу"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
msgid "Download / Upload To"
msgstr "Загрузить / Раздать"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
msgid "Save List to Disk"
msgstr "Сохранить список на диск"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
msgid "Refresh"
msgstr "Обновить"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
msgid "Expand / Collapse All"
msgstr "Развернуть / Свернуть всё"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
msgid "Back to Parent Folder"
msgstr "Вернуться в корневой каталог"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
msgid "File Search"
msgstr "Поиск файла"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
msgid "Result Filters"
msgstr "Фильтры"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
msgid "Current Session"
msgstr "Текущий сеанс"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
msgid "Completed Downloads"
msgstr "Завершённые загрузки"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
msgid "Downloaded Size"
msgstr "Размер загрузок"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
msgid "Completed Uploads"
msgstr "Завершённые раздачи"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
msgid "Uploaded Size"
msgstr "Размер раздач"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
msgid "Total"
msgstr "Всего"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
msgid "_Reset…"
msgstr "Сброс…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Список желаемого автоматически просматривается через регулярные промежутки "
"времени для обнаружения файлов."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
msgid "Add Wish…"
msgstr "Добавить желаемое…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
msgid "Clear All…"
msgstr "Очистить всё…"

#: pynicotine/gtkgui/ui/downloads.ui:138
msgid "Clear All Finished/Filtered Downloads"
msgstr "Очистить список загруженных файлов"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
msgid "Clear Finished"
msgstr "Очистить завершённые"

#: pynicotine/gtkgui/ui/downloads.ui:169
msgid "Clear Specific Downloads"
msgstr "Очистить конкретные загрузки"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
msgid "Clear _All…"
msgstr "Очистить всё…"

#: pynicotine/gtkgui/ui/interests.ui:21
msgid "Personal Interests"
msgstr "Личные интересы"

#: pynicotine/gtkgui/ui/interests.ui:40
msgid "Add something you like…"
msgstr "Добавьте то, что вам нравится…"

#: pynicotine/gtkgui/ui/interests.ui:62
msgid "Personal Dislikes"
msgstr "Личная неприязнь"

#: pynicotine/gtkgui/ui/interests.ui:80
msgid "Add something you dislike…"
msgstr "Добавьте то, что вам не нравится…"

#: pynicotine/gtkgui/ui/interests.ui:143
msgid "Refresh Recommendations"
msgstr "Обновить Рекомендации"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
msgid "Main Menu"
msgstr "Главное меню"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
msgid "Room…"
msgstr "Комната…"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
msgid "Username…"
msgstr "Имя пользователя…"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
msgid "Search term…"
msgstr "Условие поиска…"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Очистить"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Шаблоны поиска: со словом = слово, исключить слово = -слово, частичное слово "
"= *лово"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
msgid "Search Scope"
msgstr "Область поиска"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
msgid "_Wishlist"
msgstr "_Список желаемого"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
msgid "Configure Searches"
msgstr "Настроить поиск"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Введите запрос для поиска файлов, которыми поделились другие пользователи в "
"сети Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
msgid "File Grouping Mode"
msgstr "Группировать"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
msgid "Configure Downloads"
msgstr "Настроить загрузки"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Файлы, которые вы загружаете от других пользователей, помещаются здесь в "
"очередь и их можно приостанавливать и возобновлять по запросу"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
msgid "Configure Uploads"
msgstr "Настроить раздачи"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Попытки пользователей загрузить ваши общие файлы ставятся в очередь и "
"управляются здесь"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
msgid "_Open List"
msgstr "_Открыть список"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Открывает локальный список общих файлов, которые ранее были сохранены на диск"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
msgid "Configure Shares"
msgstr "Настроить общие каталоги"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Введите имя пользователя, общие файлы которого вы хотите просмотреть. Вы "
"также можете сохранить список на диск и просмотреть его позже."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
msgid "_Personal Profile"
msgstr "Профиль"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
msgid "Configure Account"
msgstr "Настройка учетной записи"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Введите имя пользователя, чтобы увидеть его описание, информацию и аватар"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
msgid "Chat _History"
msgstr "История чата"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
msgid "Configure Chats"
msgstr "Настроить чаты"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr "Введите имя пользователя, чтобы начать с ним диалог"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
msgid "_Message All"
msgstr "Сообщение (иконка)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
msgid "Configure Ignored Users"
msgstr "Настройка игнорируемых пользователей"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Добавляйте пользователей в список друзей, чтобы делиться с ними общими "
"каталогами и получать уведомления, когда они в сети"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
msgid "Join or create room…"
msgstr "Присоединяйтесь к комнатам или создайте свою…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Присоединяйтесь к существующим комнатам или создайте новую, чтобы общаться с "
"другими пользователями в сети Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
msgid "Configure User Profile"
msgstr "Настройка профиля пользователя"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
msgid "Connections"
msgstr "Узлы"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
msgid "Downloading (Speed / Active Users)"
msgstr "Загрузка (Скорость / Активные пользователи)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
msgid "Uploading (Speed / Active Users)"
msgstr "Отдача (Cкорость / Aктивные пользователи)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
msgid "Search chat history…"
msgstr "Поиск истории чата…"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
msgid "Download Speed Limits"
msgstr "Ограничения скорости загрузки"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
msgid "Unlimited download speed"
msgstr "Неограниченная скорость загрузки"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
msgid "Use download speed limit (KiB/s):"
msgstr "Использовать ограничения скорости загрузки (КиБ/с):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Использовать особые ограничения скорости загрузки (КиБ/с):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
msgid "Search rooms…"
msgstr "Поиск комнат…"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
msgid "Refresh Rooms"
msgstr "Обновить комнаты"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
msgid "_Show feed of public chat room messages"
msgstr "_Показать ленту сообщений публичного чата"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
msgid "_Accept private room invitations"
msgstr "Принять приглашение в комнату"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Напишите единое сообщение, которое другие пользователи комнаты смогут "
"прочитать позже. Последние сообщения отображаются вверху."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
msgid "Set wall message…"
msgstr "Оставить сообщение на стене…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
msgid "Search Result Filters"
msgstr "Фильтры результата поиска"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr "Используются для уточнения отображаемых результатов."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Каждый список результатов поиска имеет свой собственный фильтр, который "
"можно открыть, нажав кнопку «Фильтры результата». Фильтр состоит из "
"нескольких полей, каждое из которых применяется при нажатии клавиши «Enter». "
"Фильтрация применяется к полученным и не полученным результатам."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Как следует из названия, фильтр результата поиска не может расширить "
"исходный поиск, он может только сузить его. Чтобы расширить или изменить "
"условия поиска, выполните новый поиск."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
msgid "Result Filter Usage"
msgstr "Фильтры результата"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
msgid "Include Text"
msgstr "Название содержит"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
msgid "Files, folders and usernames containing this text will be shown."
msgstr "Будут показаны файлы, каталоги и пользователи, содержащие этот текст."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Регистр нечувствителен, но порядок слов важен: 'Instrumental Remix' не "
"покажет никаких 'Remix Instrumental'"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Используйте символ |, для разделения нескольких точных фраз. Пример:\n"
"Remix|Dub Mix|Instrumental"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
msgid "Exclude Text"
msgstr "Исключить содержимое"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"То же, что и выше, но файлы, каталоги и пользователи отфильтровываются, если "
"содержимое совпадает."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
msgid "Filters files based upon their file extension."
msgstr "Фильтрует файлы по их расширению."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Можно указать несколько расширений файлов, что, в свою очередь, расширит "
"список результатов.\n"
"Пример: flac|wav|ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"Также можно инвертировать фильтр, указав расширения файлов, которые вам не "
"нужны в результатах.\n"
"Пример: !mp3|!jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
msgid "File Size"
msgstr "Размер файла"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
msgid "Filters files based upon their file size."
msgstr "Фильтрует файлы в зависимости от их размера."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"По умолчанию используются единица измерения — байт (Б), и файлы, которые "
"больше или равны (>=) сопоставимому значению."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Добавьте b, k, m или g (альтернатива kib, mib или gib), чтобы указать "
"единицы байт, кибибайт, мебибайт или гибибайт:\n"
"    20m для отображения файлов размером более 20 MiB (мебибайт)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Добавьте знак рaвенства (=) к значению, чтобы указать точное соответствие:\n"
"=1024 соответствует только файлам размером 1 КиБ (Кибибайт)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Добавьте ! к значению, чтобы исключить файлы определенного размера:\n"
"    !30.5m для скрытия файлов размером 30.5 MiB (Мебибайт)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Добавьте символ меньше (<) или больше (>), чтобы найти файлы меньше или "
"больше заданного значения:\n"
">10.5m <1g для отображения файлов размером более 10.5 МиБ (мебибайт), но "
"меньше 1 ГиБ (гибибайт)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Более известные варианты kb, mb и gb также могут использоваться для "
"обозначения единиц измерения Килобайт, Мегабайт и Гигабайт."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
msgid "Filters files based upon their bitrate."
msgstr "Фильтрует файлы по их битрейту."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Значения должны быть введены только в виде цифр. Единицей всегда является "
"кбит/с (килобиты в секунду)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Как и в случае с размером файла (см. выше), можно использовать операторы "
"=, !, <, >, <= или >=, а также задавать несколько условий, например, "
"показывать файлы с битрейтом не менее 256 кбит/с при максимальном битрейте "
"1411 кбит/с:\n"
"    256 <= 1411"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
msgid "Filters files based upon their duration."
msgstr "Фильтрует файлы по их продолжительности."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"По умолчанию будут сопоставлены файлы, длина которых больше или равна (>=) "
"введенной длительности, если не используется оператор (=, !, <=, < или >)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Введите значение в секундах или используйте форматы времени MM:SS и HH:MM:\n"
"        =53 показывает файлы продолжительностью приблизительно 53 секунд.\n"
"        >5:30 для показа файлов продолжительностью не менее 5 с половиной "
"минут.\n"
"        <5:30:00 показывает файлы продолжительностью менее 5 с половиной "
"часов."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Можно указать несколько условий:\n"
"    >6:00 <12:00 для отображения файлов продолжительностью от 6 до 12 "
"минут.\n"
"    !9:54 !8:43 !7:32, чтобы скрыть некоторые файлы из результатов.\n"
"    =5:34 =4:23 =3:05, чтобы включить файлы с определенной "
"продолжительностью."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Фильтрация файлов на основе географического положения пользователей в "
"соответствии с кодами стран, определенными стандартом ISO 3166-2:\n"
"    US будет показывать результаты только от пользователей с IP-адресами в "
"США.\n"
"    !GB будет скрывать результаты, полученные от пользователей из "
"Великобритании."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Несколько стран можно указать через запятую или пробел."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
msgid "Free Slot"
msgstr "Свободный слот"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Показывать только те результаты от пользователей, у которых свободен хотя бы "
"один слот раздачи, т.е. файлы, которые доступны немедленно."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
msgid "Upload Speed Limits"
msgstr "Ограничения скорости отдачи"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
msgid "Unlimited upload speed"
msgstr "Неограниченная скорость отдачи"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
msgid "Use upload speed limit (KiB/s):"
msgstr "Использовать ограничение скорости отдачи (КиБ/сек):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Использовать особое ограничение скорости отдачи (КиБ/сек):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
msgid "Private Chat Command Help"
msgstr "Справка по командам чата"

#: pynicotine/gtkgui/ui/search.ui:7
msgid "Include text…"
msgstr "Название содержит…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Отфильтровать результаты, пути к которым содержат указанный текст. Можно "
"указать несколько фраз и слов, например: exact phrase|music|term|exact "
"phrase two"

#: pynicotine/gtkgui/ui/search.ui:18
msgid "Exclude text…"
msgstr "Исключить текст…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Отфильтровать результаты, пути к файлам которых содержат указанный текст. "
"Можно указать несколько фраз и слов, например: phrase|music|term|exact "
"phrase two"

#: pynicotine/gtkgui/ui/search.ui:29
msgid "File type…"
msgstr "Тип файла…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Тип файла, например flac wav или !mp3 !m4a"

#: pynicotine/gtkgui/ui/search.ui:40
msgid "File size…"
msgstr "Размер файла…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
msgid "File size, e.g. >10.5m <1g"
msgstr "Размер файла, например >10.5МиБ <1ГиБ"

#: pynicotine/gtkgui/ui/search.ui:51
msgid "Bitrate…"
msgstr "Битрейт…"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
msgid "Bitrate, e.g. 256 <1412"
msgstr "Битрейт, например 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
msgid "Duration…"
msgstr "Продолжительность…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Продолжительность, например >6:00 <12:00 !6:54"

#: pynicotine/gtkgui/ui/search.ui:73
msgid "Country code…"
msgstr "Код страны…"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Код страны, например US ES или !DE !GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Запретить пользователям доступ к вашим общим файлам на основе имени "
"пользователя, IP-адреса или страны."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Коды стран, которые необходимо заблокировать (через запятую):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
msgid "Codes must be in ISO 3166-2 format."
msgstr "Коды должны быть в формате ISO 3166-2."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
msgid "Use custom geo block message:"
msgstr "Использовать особое сообщение блокировки по странам:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Использовать особое сообщение о блокировке:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
msgid "IP Addresses"
msgstr "IP-адреса"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
msgid "Restore previously open private chats on startup"
msgstr "Восстанавливать ранее открытые чаты при запуске"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
msgid "Enable spell checker"
msgstr "Включить проверку орфографии"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
msgid "Enable CTCP-like private message responses (client version)"
msgstr "Включить ответы на личные сообщения, подобные CTCP (клиентская версия)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
msgid "Number of recent private chat messages to show:"
msgstr "Количество личных сообщений, которые нужно показать:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
msgid "Number of recent chat room messages to show:"
msgstr "Количество сообщений чата, которые нужно показать:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
msgid "Chat Completion"
msgstr "Автодополнение чата"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
msgid "Enable tab-key completion"
msgstr "Включить автодополнение с помощью клавиши «Tab»"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
msgid "Enable completion drop-down list"
msgstr "Включить раскрывающийся список автодополнения"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
msgid "Minimum characters required to display drop-down:"
msgstr ""
"Минимальное количество символов, необходимых для отображения раскрывающегося "
"списка:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
msgid "Allowed chat completions:"
msgstr "Допустимые автодополнения чата:"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
msgid "Buddy names"
msgstr "Имена друзей"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
msgid "Chat room usernames"
msgstr "Имена пользователей в чатах"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
msgid "Room names"
msgstr "Названия комнат"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
msgid "Commands"
msgstr "Команды"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
msgid "Timestamps"
msgstr "Отметки времени"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
msgid "Private chat format:"
msgstr "Тема чата:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "По умолчанию"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
msgid "Chat room format:"
msgstr "Формат чата:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
msgid "Text-to-Speech"
msgstr "Преобразовать текст в речь"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
msgid "Enable Text-to-Speech"
msgstr "Включить преобразование текста в речь"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
msgid "Text-to-Speech command:"
msgstr "Команда для преобразования текста в речь:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
msgid "Private chat message:"
msgstr "Сообщение в чате:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
msgid "Chat room message:"
msgstr "Сообщение в чате:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
msgid "Censor"
msgstr "Цензура"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
msgid "Enable censoring of text patterns"
msgstr "Цензурировать текст по шаблону"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
msgid "Auto-Replace"
msgstr "Автозамена"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
msgid "Enable automatic replacement of words"
msgstr "Включить автоматическую замену слов"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr "Автоочистка завершённых/отфильтрованных загрузок из списка передачи"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
msgid "Store completed downloads in username subfolders"
msgstr "Сохранять завершённые загрузки в подкаталогах с именами пользователей"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
msgid "Double-click action for downloads:"
msgstr "Двойной щелчок для загрузок:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
msgid "Allow users to send you any files:"
msgstr "Кому разрешается отправлять вам файлы:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
msgid "Folders"
msgstr "Каталоги"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
msgid "Finished downloads:"
msgstr "Завершенные загрузки:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
msgid "Incomplete downloads:"
msgstr "Незавершенные загрузки:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
msgid "Received files:"
msgstr "Полученные файлы:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "События"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
msgid "Run command after file download finishes ($ for file path):"
msgstr "После завершения загрузки файла выполнить команду ($ путь к файлу):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"После завершения загрузки каталога выполнить команду ($ путь к каталогу):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
msgid "Download Filters"
msgstr "Фильтры загрузки"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
msgid "Enable download filters"
msgstr "Включить фильтры загрузки"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Добавить"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Восстановить по умолчанию"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
msgid "Verify Filters"
msgstr "Проверить фильтры"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
msgid "Unverified"
msgstr "Не проверено"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Игнорировать сообщения и результаты поиска, основываясь на имени "
"пользователя или IP-адресе."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Журналировать историю публичных чатов по умолчанию"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Журналировать историю приватных чатов по умолчанию"

#: pynicotine/gtkgui/ui/settings/log.ui:142
msgid "Log transfers to file"
msgstr "Журналировать историю передач в файл"

#: pynicotine/gtkgui/ui/settings/log.ui:166
msgid "Log debug messages to file"
msgstr "Журналировать отладочные сообщения в файл"

#: pynicotine/gtkgui/ui/settings/log.ui:190
msgid "Log timestamp format:"
msgstr "Формат меток времени журналов:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
msgid "Folder Locations"
msgstr "Место"

#: pynicotine/gtkgui/ui/settings/log.ui:245
msgid "Chatroom logs folder:"
msgstr "Каталог журналов публичных чатов:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
msgid "Private chat logs folder:"
msgstr "Каталог журналов приватных чатов:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
msgid "Transfer logs folder:"
msgstr "Каталог журналов истории передач:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
msgid "Debug logs folder:"
msgstr "Каталог журналов отладки:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Войдите в существующую учетную запись Soulseek или создайте новую. Имена "
"пользователей уникальны и чувствительны к регистру."

#: pynicotine/gtkgui/ui/settings/network.ui:118
msgid "Public IP address:"
msgstr "Публичный IP-адрес:"

#: pynicotine/gtkgui/ui/settings/network.ui:159
msgid "Listening port:"
msgstr "Порт:"

#: pynicotine/gtkgui/ui/settings/network.ui:185
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Автоматически перенаправлять прослушиваемый порт (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
msgid "Away Status"
msgstr "Статус «Отсутствую»"

#: pynicotine/gtkgui/ui/settings/network.ui:228
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Установить статус «Отсутствую» после минут (0 чтобы отключить):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
msgid "Auto-reply message when away:"
msgstr "Автоответчик при отсутствии:"

#: pynicotine/gtkgui/ui/settings/network.ui:299
msgid "Auto-connect to server on startup"
msgstr "Автоматическое подключение к серверу при запуске"

#: pynicotine/gtkgui/ui/settings/network.ui:323
msgid "Soulseek server:"
msgstr "Адрес сервера Soulseek:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Связывает соединения с определенным сетевым интерфейсом, например, для "
"обеспечение постоянного использования VPN. Оставьте пустым, чтобы "
"использовать любой доступный интерфейс. Изменяйте это значение, только если "
"вы знаете, что делаете."

#: pynicotine/gtkgui/ui/settings/network.ui:351
msgid "Network interface:"
msgstr "Сетевой интерфейс:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"«Сейчас играет» позволяет отображать то, что воспроизводит ваш медиаплеер, с "
"помощью команды /now в чате."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
msgid "Other"
msgstr "Другое"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
msgid "Now Playing Format"
msgstr "Формат"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
msgid "Now Playing message format:"
msgstr "Формат сообщения \"Сейчас играет\":"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
msgid "Test Configuration"
msgstr "Проверить настройки"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
msgid "Enable plugins"
msgstr "Включить плагины"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
msgid "Add Plugins"
msgstr "Добавить плагины"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
msgid "_Add Plugins"
msgstr "Добавить"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
msgid "Settings"
msgstr "Настройки"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
msgid "_Settings"
msgstr "_Настройки"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
msgid "Version:"
msgstr "Версия:"

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
msgid "Created by:"
msgstr "Создано:"

#: pynicotine/gtkgui/ui/settings/search.ui:51
msgid "Enable search history"
msgstr "Включить историю поиска"

#: pynicotine/gtkgui/ui/settings/search.ui:70
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Клиенты сети Soulseek могут обмениваться файлами конфиденциально. Такие "
"файлы имеют префикс «[ЧАСТНЫЙ ФАЙЛ]» и не могут быть загружены, если "
"владелец не дал явного разрешения."

#: pynicotine/gtkgui/ui/settings/search.ui:76
msgid "Show privately shared files in search results"
msgstr "Показывать частные файлы в результатах поиска"

#: pynicotine/gtkgui/ui/settings/search.ui:106
msgid "Limit number of results per search:"
msgstr "Ограничить количество результатов поиска:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
msgid "Result Filter Help"
msgstr "Справка по фильтру результатов"

#: pynicotine/gtkgui/ui/settings/search.ui:177
msgid "Enable search result filters by default"
msgstr "Включить фильтры результатов поиска по умолчанию"

#: pynicotine/gtkgui/ui/settings/search.ui:211
msgid "Include:"
msgstr "Содержит:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
msgid "Exclude:"
msgstr "Исключить:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
msgid "File Type:"
msgstr "Тип файла:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "Размер:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Битрейт:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
msgid "Duration:"
msgstr "Продолжительность:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
msgid "Country Code:"
msgstr "Код страны:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
msgid "Only show results from users with an available upload slot."
msgstr ""
"Показывать результаты только от пользователей с доступным слотом для отдачи."

#: pynicotine/gtkgui/ui/settings/search.ui:430
msgid "Network Searches"
msgstr "Поиск файлов в сети"

#: pynicotine/gtkgui/ui/settings/search.ui:452
msgid "Respond to search requests from other users"
msgstr "Отвечать на поисковые запросы других пользователей"

#: pynicotine/gtkgui/ui/settings/search.ui:486
msgid "Searches shorter than this number of characters will be ignored:"
msgstr ""
"Поисковые запросы короче указанного количества символов будут игнорироваться:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
msgid "Maximum search results to send per search request:"
msgstr "Максимальное количество результатов на странице поиска:"

#: pynicotine/gtkgui/ui/settings/search.ui:578
msgid "Clear Search History"
msgstr "Очистить историю поиска"

#: pynicotine/gtkgui/ui/settings/search.ui:626
msgid "Clear Filter History"
msgstr "Очистить историю фильтров"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Nicotine+ автоматически пересканирует содержимое ваших общих каталогов при "
"запуске. Если этот параметр отключен, ваши общие файлы обновляются только "
"при пересканировании вручную."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Пересканировать общие файлы при запуске программы"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr "Показывать для всех:"

#: pynicotine/gtkgui/ui/settings/shares.ui:82
msgid "Buddy shares"
msgstr "Раздачи друзей"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
msgid "Trusted shares"
msgstr "Доверенные раздачи"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr "Автоочистка завершённых/отменённых раздач из списка передачи"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
msgid "Double-click action for uploads:"
msgstr "Действие двойного щелчка для раздачи:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
msgid "Limit upload speed:"
msgstr "Ограничить скорость отдачи:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
msgid "Per transfer"
msgstr "За одну передачу"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
msgid "Total transfers"
msgstr "Всего передач"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
msgid "Upload Slots"
msgstr "Слоты для отдачи"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Циклический: файлы будут загружаться в циклическом режиме пользователями, "
"ожидающими в очереди.\n"
"Первым пришёл — первым ушёл: файлы будут загружены в том порядке, в каком "
"они были поставлены в очередь."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
msgid "Upload queue type:"
msgstr "Тип очереди отдачи:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr ""
"Распределять слоты раздачи до тех пор, пока общая скорость не достигнет (КиБ/"
"сек):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
msgid "Fixed number of upload slots:"
msgstr "Фиксированное количество слотов отдачи:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
msgid "Prioritize all buddies"
msgstr "Приоритет для всех друзей"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
msgid "Queue Limits"
msgstr "Лимиты очереди"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
msgid "Maximum number of queued files per user:"
msgstr "Максимальное количество файлов в очереди на одного пользователя:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr ""
"Максимальный суммарный размер файлов в очереди на одного пользователя (МиБ):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
msgid "Limits do not apply to buddies"
msgstr "Ограничения не распространяются на друзей"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"Символ $ заменяется URL-адресом. По умолчанию будут использованы системные "
"приложения, если протокол не был сконфигурирован."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
msgid "File manager command:"
msgstr "Команда файлового менеджера:"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
msgid "Reset Picture"
msgstr "Сбросить изображение"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
msgid "Self Description"
msgstr "Информация отображаемая всем пользователям, зашедшим в ваш профиль"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""
"Добавьте информацию, которую увидят все зашедшие в ваш профиль люди, "
"например: краткое описание, полезные советы или рекомендации по загрузке "
"ваших раздач."

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
msgid "Picture:"
msgstr "Изображение:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
msgid "Prefer dark mode"
msgstr "Использовать тёмный режим"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
msgid "Use header bar"
msgstr "Скрыть панель меню"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
msgid "Display tray icon"
msgstr "Отображать значок"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
msgid "Minimize to tray on startup"
msgstr "Сворачивать Nicotine+ в область уведомлений при запуске"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
msgid "Language (requires a restart):"
msgstr "Язык (требуется перезапуск программы):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
msgid "When closing window:"
msgstr "При закрытии окна:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
msgid "Notifications"
msgstr "Уведомления"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
msgid "Enable sound for notifications"
msgstr "Включить звук для уведомлений"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
msgid "Show notification for private chats and mentions in the window title"
msgstr "Показывать уведомление в заголовке окна при упоминании в чатах"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
msgid "Show notifications for:"
msgstr "Показ уведомлений:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
msgid "Finished file downloads"
msgstr "Загрузка файла завершена"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
msgid "Finished folder downloads"
msgstr "Загрузка каталога завершена"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
msgid "Private messages"
msgstr "Личные сообщения"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
msgid "Chat room messages"
msgstr "Сообщения в чате"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
msgid "Chat room mentions"
msgstr "Упоминания в чате"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
msgid "Wishlist results found"
msgstr "Найдено из списка желаемого"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
msgid "Restore the previously active main tab at startup"
msgstr "Восстановить ранее активную главную вкладку при запуске"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
msgid "Close-buttons on secondary tabs"
msgstr "Кнопки закрытия на дополнительных вкладках"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
msgid "Regular tab label color:"
msgstr "Обычный цвет метки вкладки:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
msgid "Changed tab label color:"
msgstr "Изменённый цвет метки вкладки:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
msgid "Highlighted tab label color:"
msgstr "Цвет выделенной метки вкладки:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr "Позиция в списке друзей:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
msgid "Visible main tabs:"
msgstr "Отображать вкладки:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
msgid "Tab bar positions:"
msgstr "Положение панели вкладок:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
msgid "Main tabs"
msgstr "Основные вкладки"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
msgid "Show reverse file paths (requires a restart)"
msgstr "Показывать обратные пути файлов (требуется перезапуск)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
msgid "Show exact file sizes (requires a restart)"
msgstr "Показывать точные размеры файлов (требуется перезапуск программы)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
msgid "List text color:"
msgstr "Цвет списка текста:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
msgid "Enable colored usernames"
msgstr "Включить цветные имена пользователей"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
msgid "Chat username appearance:"
msgstr "Внешний вид имени пользователя в чате:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
msgid "Remote text color:"
msgstr "Цвет удалённого текста:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
msgid "Local text color:"
msgstr "Цвет локального текста:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
msgid "Command output text color:"
msgstr "Цвет текста вывода команды:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
msgid "/me action text color:"
msgstr "/me «действие» «цвет текста»:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
msgid "Highlighted text color:"
msgstr "Цвет выделенного текста:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
msgid "URL link text color:"
msgstr "Цвет ссылок:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
msgid "User Statuses"
msgstr "Статусы пользователей"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
msgid "Online color:"
msgstr "Цвет текста «В сети»:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
msgid "Away color:"
msgstr "Цвет текста «Отсутствую»:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
msgid "Offline color:"
msgstr "Цвет текста «Не в сети»:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
msgid "Text Entries"
msgstr "Ввод текста"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
msgid "Text entry background color:"
msgstr "Цвет фона ввода текста:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
msgid "Text entry text color:"
msgstr "Цвет текста ввода:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
msgid "Fonts"
msgstr "Шрифты"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
msgid "Global font:"
msgstr "Глобальный шрифт:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
msgid "List font:"
msgstr "Шрифт списка:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
msgid "Text view font:"
msgstr "Шрифт при просмотре текста:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
msgid "Chat font:"
msgstr "Шрифт чата:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
msgid "Transfers font:"
msgstr "Шрифт передач:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
msgid "Search font:"
msgstr "Шрифт поиска:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
msgid "Browse font:"
msgstr "Шрифт просмотра:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
msgid "Icons"
msgstr "Иконки"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
msgid "Icon theme folder:"
msgstr "Каталог темы иконок:"

#: pynicotine/gtkgui/ui/uploads.ui:86
msgid "Abort User(s)"
msgstr "Прервать пользователя(ей)"

#: pynicotine/gtkgui/ui/uploads.ui:116
msgid "Ban User(s)"
msgstr "Заблокировать пользователя(ей)"

#: pynicotine/gtkgui/ui/uploads.ui:138
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Очистить список завершённых раздач"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
msgid "Message All"
msgstr "Сообщение (иконка)"

#: pynicotine/gtkgui/ui/uploads.ui:199
msgid "Clear Specific Uploads"
msgstr "Очистить конкретные раздачи"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
msgid "Save Shares List to Disk"
msgstr "Сохранить список общих каталогов на диск"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
msgid "Refresh Files"
msgstr "Обновить файлы"

#: pynicotine/gtkgui/ui/userinfo.ui:101
msgid "Edit Profile"
msgstr "Изменить профиль"

#: pynicotine/gtkgui/ui/userinfo.ui:149
msgid "Shared Files"
msgstr "Общие файлы"

#: pynicotine/gtkgui/ui/userinfo.ui:203
msgid "Upload Speed"
msgstr "Скорость отдачи"

#: pynicotine/gtkgui/ui/userinfo.ui:230
msgid "Free Upload Slots"
msgstr "Свободные слоты для отдачи"

#: pynicotine/gtkgui/ui/userinfo.ui:284
msgid "Queued Uploads"
msgstr "Раздач в очереди"

#: pynicotine/gtkgui/ui/userinfo.ui:354
msgid "Edit Interests"
msgstr "Изменить интересы"

#: pynicotine/gtkgui/ui/userinfo.ui:624
msgid "_Gift Privileges…"
msgstr "_Подарить привилегию…"

#: pynicotine/gtkgui/ui/userinfo.ui:663
msgid "_Refresh Profile"
msgstr "Обновить профиль"

#: pynicotine/plugins/core_commands/PLUGININFO:3
msgid "Nicotine+ Commands"
msgstr "Команда Nicotine+"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Команда Nicotine+"

#~ msgid "Listening port (requires a restart):"
#~ msgstr "Порт (требуется перезапуск программы):"

#~ msgid "Network interface (requires a restart):"
#~ msgstr "Сетевой интерфейс (требуется перезапуск программы):"

#~ msgid "Invalid Password"
#~ msgstr "Неверный пароль"

#~ msgid "Change _Login Details"
#~ msgstr "Изменить _данные для входа"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i привилегированных пользователей"

#~ msgid "_Set Up…"
#~ msgstr "Мастер настройки…"

#~ msgid "Queued search result text color:"
#~ msgstr "Цвет текста результата поиска в очереди:"

#, python-format
#~ msgid "Failed to fetch the shared folder %(folder)s: %(error)s"
#~ msgstr "Не удалось получить список раздач %(folder)s: %(error)s"

#~ msgid "_Clear"
#~ msgstr "_Очистить"

#, python-format
#~ msgid "Can't save %(filename)s: %(error)s"
#~ msgstr "Не могу сохранить %(filename)s: %(error)s"

#~ msgid "Trusted Buddies"
#~ msgstr "Доверенные друзья"

#~ msgid "Quit program"
#~ msgstr "Выйти из программы"

#~ msgid "Username:"
#~ msgstr "Имя пользователя:"

#~ msgid "Re_commendations for Item"
#~ msgstr "Ре_комендации"

#~ msgid "_Remove Item"
#~ msgstr "_Удалить метку"

#~ msgid "_Remove"
#~ msgstr "_Удалить из списка"

#~ msgid "Send M_essage"
#~ msgstr "Отправить _сообщение"

#~ msgid "Send Message"
#~ msgstr "Отправить сообщение"

#~ msgid "View User Profile"
#~ msgstr "Посмотреть профиль пользователя"

#~ msgid "Start Messaging"
#~ msgstr "Начать диалог"

#~ msgid "Enter the name of the user whom you want to send a message:"
#~ msgstr "Введите имя пользователя, которому вы хотите отправить сообщение:"

#~ msgid "_Message"
#~ msgstr "Сообщение"

#~ msgid "Enter the name of the user whose profile you want to see:"
#~ msgstr ""
#~ "Введите имя пользователя, общие каталоги которого вы хотите увидеть:"

#~ msgid "_View Profile"
#~ msgstr "Посмотреть профиль пользователя"

#~ msgid "Enter the name of the user whose shares you want to see:"
#~ msgstr ""
#~ "Введите имя пользователя, общие каталоги которого вы хотите увидеть:"

#~ msgid "_Browse"
#~ msgstr "Просмотр"

#~ msgid "Password"
#~ msgstr "Пароль"

#~ msgid "Download File"
#~ msgstr "Загрузить файл"

#~ msgid "Refresh Similar Users"
#~ msgstr "Обновить похожих пользователей"

#~ msgid "Enter the username of the person whose files you want to see"
#~ msgstr "Введите имя пользователя, файлы которого вы хотите просмотреть"

#~ msgid "Enter the username of the person whose information you want to see"
#~ msgstr "Введите имя пользователя, информацию о котором вы хотите видеть"

#~ msgid "Enter the username of the person you want to send a message to"
#~ msgstr "Введите имя пользователя, которому вы хотите отправить сообщение"

#~ msgid "Enter the username of the person you want to add to your buddy list"
#~ msgstr "Введите имя пользователя, которого хотите добавить в список друзей"

#~ msgid ""
#~ "Enter the name of a room you want to join. If the room doesn't exist, it "
#~ "will be created."
#~ msgstr ""
#~ "Введите название комнаты, к которой хотите присоединиться. Если комнаты "
#~ "не существует, она будет создана."

#~ msgid "Show Log History Pane"
#~ msgstr "Показать историю журнала"

#~ msgid "Save _Picture"
#~ msgstr "Сохранить _аватар"

#, python-format
#~ msgid ""
#~ "Filtered out incorrect search result %(filepath)s from user %(user)s for "
#~ "search query \"%(query)s\""
#~ msgstr ""
#~ "Отфильтрован неверный результат поиска %(filepath)s от пользователя "
#~ "%(user)s по запросу \"%(query)s\""

#~ msgid ""
#~ "Certain clients don't send search results if special characters are "
#~ "included."
#~ msgstr ""
#~ "Некоторые клиенты не отправляют результаты поиска, если в них включены "
#~ "специальные символы."

#~ msgid "Remove special characters from search terms"
#~ msgstr "Удалять специальные символы из условий поиска"

#, python-format
#~ msgid "Trouble executing '%s'"
#~ msgstr "Не удалось выполнить \"%s\""

#, python-format
#~ msgid "Trouble executing on folder: %s"
#~ msgstr "Не удалось выполнить в каталоге: %s"

#~ msgid "Disallowed extension"
#~ msgstr "Запрещенное расширение"

#~ msgid "Too many files"
#~ msgstr "Слишком много файлов"

#~ msgid "Too many megabytes"
#~ msgstr "Слишком много мегабайт"

#~ msgid "Server does not permit performing wishlist searches at this time"
#~ msgstr ""
#~ "Сервер не позволяет выполнять поиск по списку желаемого в настоящее время"

#~ msgid ""
#~ "File transfer speeds depend on users you are downloading from. Certain "
#~ "users will be faster, while others will be slow."
#~ msgstr ""
#~ "Скорость передачи файлов зависит от пользователей, у которых вы "
#~ "загружаете. Некоторые пользователи будут быстрее, в то время как другие "
#~ "будут медленнее."

#~ msgid "Started Downloads"
#~ msgstr "Начатые загрузки"

#~ msgid "Started Uploads"
#~ msgstr "Начатые раздачи"

#~ msgid "Replace censored letters with:"
#~ msgstr "Заменять ненормативную лексику символами:"

#~ msgid "Censored Patterns"
#~ msgstr "Шаблоны"

#~ msgid "Replacements"
#~ msgstr "Замены"

#~ msgid ""
#~ "If a user on the Soulseek network searches for a file that exists in your "
#~ "shares, search results will be sent to the user."
#~ msgstr ""
#~ "Если пользователь в сети Soulseek ищет файл, который находится в ваших "
#~ "общих файлах, результаты поиска будут отправлены пользователю."

#~ msgid "Send to Player"
#~ msgstr "Отправить в медиаплеер"

#~ msgid "Send to _Player"
#~ msgstr "Отправить в _медиаплеер"

#~ msgid "_Open in File Manager"
#~ msgstr "_Открыть в файловом менеджере"

#~ msgid ""
#~ "Privately shared files that have been made visible to everyone will be "
#~ "prefixed with '[PRIVATE]', and can not be downloaded until the uploader "
#~ "gives explicit permission. Ask them kindly."
#~ msgstr ""
#~ "Клиенты сети Soulseek могут обмениваться файлами конфиденциально. Такие "
#~ "файлы имеют префикс «[ЧАСТНЫЙ ФАЙЛ]» и не могут быть загружены, если "
#~ "владелец не дал явного разрешения."

#~ msgid "Media player command:"
#~ msgstr "Команда медиаплеера:"

#, python-format
#~ msgid ""
#~ "Unable to save download to username subfolder, falling back to default "
#~ "download folder. Error: %s"
#~ msgstr ""
#~ "Невозможно сохранить загрузку в подкаталог с именем пользователя, откат к "
#~ "папке загрузки по умолчанию. Ошибка: %s"

#~ msgid "Buddy-only"
#~ msgstr "Только для друзей"

#~ msgid "Share with buddies only"
#~ msgstr "Обмен только для друзей"

#~ msgid "_Quit…"
#~ msgstr "_Выйти…"

#~ msgid "_Configure Shares"
#~ msgstr "_Настроить общие каталоги"

#~ msgid "Remote file error"
#~ msgstr "Ошибка удаленного файла"

#, python-format
#~ msgid "Cannot find %(option1)s or %(option2)s, please install either one."
#~ msgstr ""
#~ "Не удаётся найти %(option1)s или %(option2)s, установите любой из них."

#, python-format
#~ msgid "%(num)s folders found before rescan"
#~ msgstr "%(num)s каталогов найдено перед повторным сканированием"

#, python-format
#~ msgid "Failed to process the following databases: %(names)s"
#~ msgstr "Не удалось обработать следующие базы данных: %(names)s"

#, python-format
#~ msgid "Failed to send number of shared files to the server: %s"
#~ msgstr "Не удалось отправить на сервер количество раздач: %s"

#~ msgid "Quit / Run in Background"
#~ msgstr "Выйти / В фоновый режим"

#~ msgid "Limit buddy-only shares to trusted buddies"
#~ msgstr "Разрешить доступ только доверенным друзьям"

#~ msgid "Shared"
#~ msgstr "Размер раздачи"

#~ msgid "Search Files and Folders"
#~ msgstr "Найти файлы и каталоги"

#~ msgid "Out of Date"
#~ msgstr "Устаревшая версия"

#, python-format
#~ msgid "Version %(version)s is available, released on %(date)s"
#~ msgstr "Доступна версия %(version)s, выпущенная %(date)s"

#, python-format
#~ msgid "You are using a development version of %s"
#~ msgstr "Вы используете версию %s для разработчиков"

#, python-format
#~ msgid "You are using the latest version of %s"
#~ msgstr "Вы используете последнюю версию %s"

#~ msgid "Latest Version Unknown"
#~ msgstr "Версия неизвестна"

#~ msgid "Prefer Dark _Mode"
#~ msgstr "Использовать тёмный _режим"

#~ msgid "Show _Log History Pane"
#~ msgstr "Показать _журнал событий"

#~ msgid "Buddy List in Separate Tab"
#~ msgstr "Список друзей в отдельной вкладке"

#~ msgid "Buddy List Always Visible"
#~ msgstr "Список друзей всегда виден"

#~ msgid "_View"
#~ msgstr "_Вид"

#~ msgid "_Open Log Folder"
#~ msgstr "_Открыть каталог журнала"

#~ msgid "_Browse Folder(s)"
#~ msgstr "_Просмотр каталогов"

#~ msgid "Kibibytes (2^10 bytes) per second."
#~ msgstr "Кибибайт (2^10 байт) в секунду."

#~ msgid "Sent to users as the reason for being geo blocked."
#~ msgstr "Отправлять пользователям как причина блокировки по странам."

#~ msgid "Sent to users as the reason for being banned."
#~ msgstr "Отправлять пользователям как причину блокировки."

#~ msgid ""
#~ "Once you interact with the application being away, status will be set to "
#~ "online."
#~ msgstr ""
#~ "После вашего взаимодействия с программой, статус будет установлен на «В "
#~ "сети»."

#~ msgid "Each user may queue a maximum of either:"
#~ msgstr "Каждый пользователь может поставить в очередь максимум:"

#~ msgid "Mebibytes (2^20 bytes)."
#~ msgstr "Мебибайты (2^20 байт)."

#~ msgid "MiB"
#~ msgstr "МиБ"

#~ msgid "files"
#~ msgstr "файлы"

#~ msgid "Queue Behavior"
#~ msgstr "Поведение очереди"

#~ msgid ""
#~ "If disabled, slots will automatically be determined by available "
#~ "bandwidth limitations."
#~ msgstr ""
#~ "Если этот параметр отключен, слоты будут автоматически определяться "
#~ "доступными ограничениями пропускной способности."

#~ msgid "Note that the operating system's theme may take precedence."
#~ msgstr ""
#~ "Обратите внимание, что тема операционной системы может иметь приоритет."

#~ msgid "By default the leftmost tab is activated at startup"
#~ msgstr "По умолчанию крайняя левая вкладка активируется при запуске"

#, python-format
#~ msgid "Quit %(program)s %(version)s, %(status)s!"
#~ msgstr "Выход из %(program)s %(version)s %(status)s!"

#~ msgid "terminated"
#~ msgstr "завершено"

#~ msgid "done"
#~ msgstr "готово"

#~ msgid "Remember choice"
#~ msgstr "Запомнить выбор"

#~ msgid "Kosovo"
#~ msgstr "Косово"

#~ msgid "Unknown Network Interface"
#~ msgstr "Неизвестный сетевой интерфейс"

#~ msgid "Listening Port Unavailable"
#~ msgstr "Прослушиваемый порт недоступен"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr ""
#~ "Сервер не работает или не отвечает, попытка подключения через %i секунд"

#~ msgid ""
#~ "Nicotine+ uses peer-to-peer networking to connect to other users. In "
#~ "order to allow users to connect to you without trouble, an open listening "
#~ "port is crucial."
#~ msgstr ""
#~ "Nicotine+ использует одноранговую сеть для подключения к другим "
#~ "пользователям. Открытый порт прослушивания имеет решающее значение для "
#~ "того, чтобы пользователи могли без проблем подключаться к вам."

#~ msgid "--- disconnected ---"
#~ msgstr "--- соединение потеряно ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- соединение восстановлено ---"

#~ msgid "ID"
#~ msgstr "Идентификатор"

#~ msgid "Earth"
#~ msgstr "Планета Земля"

#~ msgid "Czech Republic"
#~ msgstr "Чехия"

#~ msgid "Turkey"
#~ msgstr "Турция"

#~ msgid "Joined Rooms "
#~ msgstr "находимся "

#~ msgid "_Auto-join Room"
#~ msgstr "_Автоматическое присоединение к комнате"

#~ msgid "Escaped"
#~ msgstr "Сбежавший"

#~ msgid "Escape filter"
#~ msgstr "Отключить"

#~ msgid "Enter a text pattern and what to replace it with"
#~ msgstr "Введите текстовый шаблон для замены"

#, python-format
#~ msgid "%(num)s folders found before rescan, rebuilding…"
#~ msgstr ""
#~ "%(num)s каталогов обнаружено перед повторным сканированием, перестроение…"

#, python-format
#~ msgid "No listening port is available in the specified port range %s–%s"
#~ msgstr "Нет доступных прослушиваемых портов в указанном диапазоне %s–%s"

#~ msgid ""
#~ "Choose a range to select a listening port from. The first available port "
#~ "in the range will be used."
#~ msgstr ""
#~ "Выберите диапазон для подбора порта прослушивания. Будет использован "
#~ "первый доступный порт."

#~ msgid "First Port"
#~ msgstr "Начальный порт"

#~ msgid "to"
#~ msgstr "до"

#~ msgid "Last Port"
#~ msgstr "Конечный порт"

#, python-format
#~ msgid "Cannot find %s or newer, please install it."
#~ msgstr "Не удаётся найти %s или новее, установите его."

#~ msgid ""
#~ "Cannot import the Gtk module. Bad install of the python-gobject module?"
#~ msgstr ""
#~ "Невозможно импортировать модуль Gtk. Некорректная установка модуля python-"
#~ "gobject?"

#, python-format
#~ msgid ""
#~ "You are using an unsupported version of GTK %(major_version)s. You should "
#~ "install GTK %(complete_version)s or newer."
#~ msgstr ""
#~ "Вы используете неподдерживаемую версию GTK %(major_version)s. Вам следует "
#~ "установить GTK %(complete_version)s или новее."

#~ msgid "User Info"
#~ msgstr "Информация о пользователе"

#~ msgid "Zoom 1:1"
#~ msgstr "Масштаб 1:1"

#~ msgid "Zoom In"
#~ msgstr "Увеличить"

#~ msgid "Zoom Out"
#~ msgstr "Уменьшить"

#~ msgid "Show User I_nfo"
#~ msgstr "Получить _информацию"

#~ msgid "Request User's Info"
#~ msgstr "Информация о пользователе"

#~ msgid "Request User's Shares"
#~ msgstr "Общие каталоги пользователя"

#~ msgid "Request User Info"
#~ msgstr "Информация о пользователе"

#~ msgid "Enter the name of the user whose info you want to see:"
#~ msgstr "Введите имя пользователя, информацию о котором вы хотите видеть:"

#~ msgid "Request Shares List"
#~ msgstr "Общие каталоги пользователя"

#, python-format
#~ msgid "Invalid Soulseek URL: %s"
#~ msgstr "Неправильная схема URL Soulseek: %s"

#~ msgid ""
#~ "Users on the Soulseek network will be able to download files from folders "
#~ "you share. Sharing files is crucial for the health of the Soulseek "
#~ "network."
#~ msgstr ""
#~ "Пользователи сети Soulseek смогут скачивать файлы из общих папок. Обмен "
#~ "файлами имеет решающее значение для работоспособности сети Soulseek."

#~ msgid "Update I_nfo"
#~ msgstr "Обновить и_нформацию"

#~ msgid "Chat Room Commands"
#~ msgstr "Команды чата"

#~ msgid "/join /j 'room'"
#~ msgstr "/join /j 'комната'"

#~ msgid "Join room 'room'"
#~ msgstr "Присоединиться к комнате 'комната'"

#~ msgid "/me 'message'"
#~ msgstr "/me 'сообщение'"

#~ msgid "Display the Now Playing script's output"
#~ msgstr "Показать вывод скрипта \"Сейчас играет\""

#~ msgid "/add /ad 'user'"
#~ msgstr "/add /ad 'пользователь'"

#~ msgid "Add user 'user' to your buddy list"
#~ msgstr "Добавить пользователя 'user' в список друзей"

#~ msgid "/rem /unbuddy 'user'"
#~ msgstr "/rem /unbuddy 'пользователь'"

#~ msgid "Remove user 'user' from your buddy list"
#~ msgstr "Удалить пользователя 'user' из списка друзей"

#~ msgid "/ban 'user'"
#~ msgstr "/ban 'пользователь'"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "Добавить пользователя 'user' в чёрный список"

#~ msgid "/unban 'user'"
#~ msgstr "/unban 'пользователь'"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "Удалить пользователя 'user' из чёрного списка"

#~ msgid "/ignore 'user'"
#~ msgstr "/ignore 'пользователь'"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "Добавить пользователя 'user' к вашему списку игнорирования"

#~ msgid "/unignore 'user'"
#~ msgstr "/unignore 'пользователь'"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "Удалить пользователя 'user' из вашего списка игнорирования"

#~ msgid "/browse /b 'user'"
#~ msgstr "/browse /b 'пользователь'"

#~ msgid "/whois /w 'user'"
#~ msgstr "/whois /w 'пользователь'"

#~ msgid "Request info for 'user'"
#~ msgstr "Сделает запрос информации для 'пользователь'"

#~ msgid "/ip 'user'"
#~ msgstr "/ip 'пользователь'"

#~ msgid "Show IP for user 'user'"
#~ msgstr "Показать IP-адрес 'пользователя'"

#~ msgid "/search /s 'query'"
#~ msgstr "/search /s 'поисковый запрос'"

#~ msgid "Start a new search for 'query'"
#~ msgstr "Искать по 'поисковому запросу'"

#~ msgid "/rsearch /rs 'query'"
#~ msgstr "/rsearch /rs 'запрос'"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "Искать в присоединённых комнатах 'запрос'"

#~ msgid "/bsearch /bs 'query'"
#~ msgstr "/bsearch /bs 'запрос'"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "Искать по списку друзей 'запрос'"

#~ msgid "/usearch /us 'user' 'query'"
#~ msgstr "/usearch /us 'пользователь' 'запрос'"

#~ msgid "/msg 'user' 'message'"
#~ msgstr "/msg 'пользователь' 'текст сообщения'"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "Отправить сообщение 'message' пользователю 'user'"

#~ msgid "/pm 'user'"
#~ msgstr "/pm 'пользователь'"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr "Открыть окно чата с пользователем 'пользователь'"

#~ msgid "Private Chat Commands"
#~ msgstr "Команды чата"

#~ msgid "Add user to your ban list"
#~ msgstr "Добавить пользователя в список блокировки"

#~ msgid "Add user to your ignore list"
#~ msgstr "Игнорировать пользователя"

#~ msgid "Browse shares of user"
#~ msgstr "Посмотреть раздачи пользователя"

#~ msgid ""
#~ "For order-insensitive filtering, as well as filtering several exact "
#~ "phrases, vertical bars can be used to separate phrases and words.\n"
#~ "    Example: Remix|Instrumental|Dub Mix"
#~ msgstr ""
#~ "Для фильтрации с учетом порядка, а также для нескольких точных фраз можно "
#~ "использовать вертикальные полосы.\n"
#~ "Пример: Remix|Instrumental|Dub Mix"

#~ msgid "To exclude non-audio files use !0 in the duration filter."
#~ msgstr ""
#~ "Чтобы исключить не аудиофайлы, используйте !0 в фильтре продолжительности."

#~ msgid "Filters files based upon users' geographical location."
#~ msgstr "Фильтрует файлы на основе географического положения пользователей."

#~ msgid "To view the full results again, simply clear all active filters."
#~ msgstr ""
#~ "Чтобы снова просмотреть полные результаты, просто очистите все активные "
#~ "фильтры."

#~ msgid "See the preferences to set default search result filter options."
#~ msgstr "Дополнительные параметры фильтрации смотрите в настройках."

#~ msgid "File size"
#~ msgstr "Размер файла"

#~ msgid "Cycle through completions when pressing tab-key"
#~ msgstr "Цикл выполнения автодополнений при нажатии клавиши табуляции"

#~ msgid "Hide drop-down when only one matches"
#~ msgstr "Скрыть раскрывающийся список, если совпадает только один"

#~ msgid "Download folders in reverse alphanumerical order"
#~ msgstr "Загружать каталоги в обратном алфавитно-цифровом порядке"

#~ msgid "Incomplete file folder:"
#~ msgstr "Каталог для незавершённых загрузок:"

#~ msgid "Download folder:"
#~ msgstr "Каталог для загрузок:"

#~ msgid "Save buddies' uploads to:"
#~ msgstr "Сохранять раздачи друзей в:"

#~ msgid "Use UPnP to forward listening port"
#~ msgstr "Использовать UPnP для проброса прослушиваемых портов"

#~ msgid ""
#~ "Share folders with every Soulseek user or buddies, allowing contents to "
#~ "be downloaded directly from your device. Hidden files are never shared."
#~ msgstr ""
#~ "Делитесь общими каталогами с пользователями или друзьями в сети Soulseek, "
#~ "позволяя загружать содержимое непосредственно с вашего устройства. "
#~ "Скрытые файлы не будут доступны для загрузки."

#~ msgid "Secondary Tabs"
#~ msgstr "Дополнительные вкладки"

#~ msgid "Chat room tab bar position:"
#~ msgstr "Расположение панели вкладок комнаты чата:"

#~ msgid "Private chat tab bar position:"
#~ msgstr "Расположение панели вкладок чата:"

#~ msgid "Search tab bar position:"
#~ msgstr "Расположение панели поиска:"

#~ msgid "User info tab bar position:"
#~ msgstr "Расположение панели вкладки информации о пользователе:"

#~ msgid "User browse tab bar position:"
#~ msgstr "Расположение панели просмотра пользователя:"

#~ msgid "Tab Labels"
#~ msgstr "Метки на вкладках"

#~ msgid "_Refresh Info"
#~ msgstr "_Обновить информацию"

#~ msgid "Block IP Address"
#~ msgstr "Заблокировать IP-адрес"

#~ msgid "Connected"
#~ msgstr "Соединён"

#~ msgid "Disconnected"
#~ msgstr "Отключён"

#~ msgid "Disconnected (Tray)"
#~ msgstr "Отключён (Иконка)"

#~ msgid "User(s)"
#~ msgstr "Пользователь(ы)"

#, python-format
#~ msgid "Alias \"%s\" returned nothing"
#~ msgstr "Псевдоним \"%s\" ничего не вернул"

#~ msgid "Alternative Speed Limits"
#~ msgstr "Особые ограничения скорости"

#~ msgid "Last played"
#~ msgstr "Последний раз играл"

#~ msgid "Playing now"
#~ msgstr "Играет сейчас"

#, python-format
#~ msgid "Error code %(code)s: %(description)s"
#~ msgstr "Код ошибки %(code)s: %(description)s"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "Нет такого псевдонима (%s)"

#~ msgid "Aliases:"
#~ msgstr "Псевдонимы:"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "Удален псевдоним %(alias)s: %(action)s\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "Нет такого псевдонима (%(alias)s)\n"

#~ msgid "Aliases"
#~ msgstr "Псевдонимы"

#~ msgid "/alias /al 'command' 'definition'"
#~ msgstr "/alias /al 'команда' 'определение'"

#~ msgid "Add a new alias"
#~ msgstr "Добавить новый псевдоним"

#~ msgid "/unalias /un 'command'"
#~ msgstr "/unalias /un 'команда'"

#~ msgid "Remove an alias"
#~ msgstr "Удалить псевдоним"

#~ msgid "Chat History"
#~ msgstr "История чата"

#~ msgid "Command aliases"
#~ msgstr "Псевдонимы команд"

#~ msgid "Limit download speed to (KiB/s):"
#~ msgstr "Ограничить скорость загрузки (КиБ/сек):"

#~ msgid "Author(s):"
#~ msgstr "Авторы:"

#~ msgid "Limit upload speed to (KiB/s):"
#~ msgstr "Ограничить скорость отдачи до (КиБ/сек):"

#~ msgid "Tabs show user status icons instead of status text"
#~ msgstr ""
#~ "На вкладках отображаются значки статуса пользователя вместо текста статуса"

#~ msgid "Show file path tooltips in file list views"
#~ msgstr ""
#~ "Показывать всплывающие подсказки пути к файлу в представлениях списка "
#~ "файлов"

#~ msgid "Colored and clickable usernames"
#~ msgstr "Цветные и интерактивные имена пользователей"

#~ msgid "Notification changes the tab's text color"
#~ msgstr "Менять цвет текста вкладки при уведомлениях"

#~ msgid "Cancel"
#~ msgstr "Отмена"

#~ msgid "OK"
#~ msgstr "ОК"

#~ msgid "_Add to Buddy List"
#~ msgstr "_Добавить в список друзей"

#~ msgid "use non-default user data directory for e.g. list of downloads"
#~ msgstr ""
#~ "использовать каталог для пользовательских данных. Например, списка "
#~ "загрузок"

#, python-format
#~ msgid "Unknown config section '%s'"
#~ msgstr "Неизвестный раздел конфигурации \"%s\""

#, python-format
#~ msgid "Unknown config option '%(option)s' in section '%(section)s'"
#~ msgstr ""
#~ "Неизвестный параметр конфигурации \"%(option)s\" в разделе \"%(section)s\""

#, python-format
#~ msgid "Version %s is available"
#~ msgstr "Доступна новая версия %s"

#, python-format
#~ msgid "released on %s"
#~ msgstr "выпущена %s"

#~ msgid "Nicotine+ is running in the background"
#~ msgstr "Nicotine+ работает в фоновом режиме"

#, python-format
#~ msgid "Private message from %s"
#~ msgstr "Личное сообщение от %s"

#~ msgid "Aborted"
#~ msgstr "Прервано"

#~ msgid "Blocked country"
#~ msgstr "Заблокированная страна"

#~ msgid "Finished / Aborted"
#~ msgstr "Завершено / Прервано"

#~ msgid "Close tab"
#~ msgstr "Закрыть вкладку"

#, python-format
#~ msgid "You've been mentioned in the %(room)s room"
#~ msgstr "Вас упомянули в комнате %(room)s"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "Неизвестная команда %s"

#, python-format
#~ msgid "(Warning: %(realuser)s is attempting to spoof %(fakeuser)s) "
#~ msgstr ""
#~ "(Предупреждение: %(realuser)s пытается подделать личное сообщение "
#~ "%(fakeuser)s) "

#, python-format
#~ msgid ""
#~ "The network interface you specified, '%s', does not exist. Change or "
#~ "remove the specified network interface and restart Nicotine+."
#~ msgstr ""
#~ "Указанный вами сетевой интерфейс «%s» не существует. Измените или удалите "
#~ "указанный сетевой интерфейс и перезапустите Nicotine+."

#~ msgid ""
#~ "The range you specified for client connection ports was {}-{}, but none "
#~ "of these were usable. Increase and/or "
#~ msgstr ""
#~ "Диапазон портов {} - {}, не пригоден для использования. Необходимо "
#~ "увеличить и/или сместить диапазон портов "

#~ msgid ""
#~ "Note that part of your range lies below 1024, this is usually not allowed "
#~ "on most operating systems with the exception of Windows."
#~ msgstr ""
#~ "Обратите внимание, что часть вашего диапазона находится ниже 1024, это "
#~ "обычно не разрешено в большинстве операционных систем, за исключением "
#~ "Windows."

#, python-format
#~ msgid "Rescan progress: %s"
#~ msgstr "Прогресс: %s"

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "Ошибка ОС: %s"

#~ msgid "UPnP is not available on this network"
#~ msgstr "UPnP недоступен в этой сети"

#, python-format
#~ msgid "Failed to map the external WAN port: %(error)s"
#~ msgstr "Не удалось сопоставить внешний порт WAN: %(error)s"

#~ msgid "Room wall"
#~ msgstr "Стена комнаты"

#~ msgid ""
#~ "The default listening port '2234' works fine in most cases. If you need "
#~ "to use a different port, you will be able to modify it in the preferences "
#~ "later."
#~ msgstr ""
#~ "Порт прослушивания по умолчанию «2234» в большинстве случаев работает "
#~ "нормально. Если вам нужно использовать другой порт, вы сможете изменить "
#~ "его в настройках позже."

#~ msgid "Show users with similar interests"
#~ msgstr "Показать пользователей со схожими интересами"

#~ msgid "Menu"
#~ msgstr "Меню"

#~ msgid "Expand / Collapse all"
#~ msgstr "Развернуть / Свернуть всё"

#~ msgid "Configure shares"
#~ msgstr "Настроить общие файлы"

#~ msgid "Create or join room…"
#~ msgstr "Создать комнату или присоединиться к ней…"

#~ msgid "_Room List"
#~ msgstr "_Список комнат"

#~ msgid "Enable alternative download and upload speed limits"
#~ msgstr "Активировать особые ограничения скорости раздачи"

#~ msgid "Show log history"
#~ msgstr "Показать журнал событий"

#~ msgid "Result grouping mode"
#~ msgstr "Группировать результаты"

#~ msgid "Free slot"
#~ msgstr "Свободный слот"

#~ msgid "Save shares list to disk"
#~ msgstr "Сохранить список раздач на диск"

#~ msgid "_Away"
#~ msgstr "_Отсутствую"

#, python-format
#~ msgid "Failed to load ui file %(file)s: %(error)s"
#~ msgstr ""
#~ "Не удалось загрузить файл пользовательского интерфейса %(file)s: %(error)s"

#~ msgid ""
#~ "Attempting to reset index of shared files due to an error. Please rescan "
#~ "your shares."
#~ msgstr ""
#~ "Попытка сбросить индекс списка раздач из-за ошибки. Пожалуйста, "
#~ "пересканируйте список раздач."

#~ msgid ""
#~ "File index of shared files could not be accessed. This could occur due to "
#~ "several instances of Nicotine+ being active simultaneously, file "
#~ "permission issues, or another issue in Nicotine+."
#~ msgstr ""
#~ "Невозможно получить доступ к файловому индексу списка раздач. Это могло "
#~ "произойти из-за одновременной активности нескольких экземпляров "
#~ "Nicotine+, проблем с правами доступа к файлам или другой проблемы в "
#~ "Nicotine+."

#~ msgid "Nicotine+ is a Soulseek client"
#~ msgstr "Nicotine+ — клиент Soulseek"

#~ msgid "enable the tray icon"
#~ msgstr "добавить значок в область уведомлений"

#~ msgid "disable the tray icon"
#~ msgstr "не добавлять значок в область уведомлений"

#~ msgid "Get Soulseek Privileges…"
#~ msgstr "Получить привилегии Soulseek…"

#, python-format
#~ msgid ""
#~ "Public IP address is <b>%(ip)s</b> and active listening port is "
#~ "<b>%(port)s</b>"
#~ msgstr ""
#~ "Публичный IP-адрес: <b>%(ip)s</b>, порт прослушивания: <b>%(port)s</b>"

#~ msgid "unknown"
#~ msgstr "неизвестный"

#~ msgid "Notification"
#~ msgstr "Уведомление"

#~ msgid "Length"
#~ msgstr "Продолжительность"

#, python-format
#~ msgid "Picture not saved, %s already exists."
#~ msgstr "Изображение не сохранено, %s уже существует."

#~ msgid "_Open"
#~ msgstr "_Открыть"

#~ msgid "_Save"
#~ msgstr "_Сохранить"

#, python-format
#~ msgid "Failed to load plugin '%s', could not find it."
#~ msgstr "Не удалось загрузить плагин «%s», не был найден."

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "Ошибка ввода-вывода: %s"

#, python-format
#~ msgid "Failed to open file path: %s"
#~ msgstr "Не удалось открыть путь к файлу: %s"

#, python-format
#~ msgid "Failed to open URL: %s"
#~ msgstr "Не удалось открыть URL: %s"

#~ msgid "_Log Conversation"
#~ msgstr "_Журнал диалога"

#~ msgid "Result Filter List"
#~ msgstr "Список фильтров результата"

#~ msgid "Prepend < or > to find files less/greater than the given value."
#~ msgstr "Вставьте <or>, чтобы найти файлы меньше/больше заданного значения."

#~ msgid ""
#~ "VBR files display their average bitrate and are typically lower in "
#~ "bitrate than a compressed 320 kbps CBR file of the same audio quality."
#~ msgstr ""
#~ "Файлы VBR отображают свой средний битрейт и обычно имеют меньший битрейт, "
#~ "чем сжатый файл CBR 320 кбит/сек с тем же качеством звука."

#~ msgid "Like Size above, =, <, and > can be used."
#~ msgstr ""
#~ "Как и с фильтром для размера файла, можно использовать «>» больше, «<» "
#~ "меньше или «=» равно."

#~ msgid ""
#~ "Prevent write access by other programs for files being downloaded (turn "
#~ "off for NFS)"
#~ msgstr ""
#~ "Запрещать другим программам доступ на запись для загружаемых файлов "
#~ "(отключить для NFS)"

#~ msgid "Where incomplete downloads are temporarily stored."
#~ msgstr "Здесь будут храниться незавершённые загрузки."

#~ msgid ""
#~ "Where buddies' uploads will be stored (with a subfolder created for each "
#~ "buddy)."
#~ msgstr ""
#~ "Здесь будут храниться раздачи друзей (подкаталог для каждого отдельного "
#~ "друга)."

#~ msgid "Toggle away status after minutes of inactivity:"
#~ msgstr "Переключать статус после нескольких минут бездействия:"

#~ msgid "Protocol:"
#~ msgstr "Протокол:"

#~ msgid "Icon theme folder (requires restart):"
#~ msgstr "Каталог темы значков (требуется перезапуск):"

#~ msgid "Establishing connection"
#~ msgstr "Установка соединения"

#~ msgid "Clear Groups"
#~ msgstr "Очистить группы"

#~ msgid "User List"
#~ msgstr "Список пользователей"

#~ msgid "_Reset Statistics…"
#~ msgstr "_Сбросить статистику…"

#~ msgid "Clear _Downloads…"
#~ msgstr "Очистить _загрузки…"

#~ msgid "Clear Uploa_ds…"
#~ msgstr "Очистить разд_ачи…"

#~ msgid "Block User's IP Address"
#~ msgstr "Заблокировать IP-адрес пользователя"

#~ msgid "Ignore User's IP Address"
#~ msgstr "Игнорировать IP-адрес пользователя"

#~ msgid "Usernames"
#~ msgstr "Имена"

#~ msgid "Display logged chat room messages when a room is rejoined"
#~ msgstr ""
#~ "Отображать историю сообщений чата при повторном присоединении к комнате"

#~ msgid "Queue Position"
#~ msgstr "Позиция"

#, python-format
#~ msgid ""
#~ "User %(user)s is directly searching for \"%(query)s\", returning %(num)i "
#~ "results"
#~ msgstr ""
#~ "%(user)s напрямую ищет \"%(query)s\", возвращает %(num)i результатов"

#~ msgid "Room wall (personal message set)"
#~ msgstr "Стена комнаты"

#~ msgid "Search Wishlist"
#~ msgstr "Искать в списке желаемого"

#~ msgid "Your config file is corrupt"
#~ msgstr "Файл конфигурации повреждён"

#, python-format
#~ msgid ""
#~ "We're sorry, but it seems your configuration file is corrupt. Please "
#~ "reconfigure Nicotine+.\n"
#~ "\n"
#~ "We renamed your old configuration file to\n"
#~ "%(corrupt)s\n"
#~ "If you open this file with a text editor you might be able to rescue some "
#~ "of your settings."
#~ msgstr ""
#~ "К сожалению, файл конфигурации был повреждён. Пожалуйста, перенастройте "
#~ "Nicotine+.\n"
#~ "\n"
#~ "Мы переименовали ваш старый файл конфигурации в\n"
#~ "%(corrupt)s\n"
#~ "Если вы откроете этот файл в текстовом редакторе, есть вероятность "
#~ "сохранить некоторые настройки."

#~ msgid "User Interests"
#~ msgstr "Интересы"

#, python-format
#~ msgid "Quitting Nicotine+ %(version)s, %(status)s…"
#~ msgstr "Выхожу из Nicotine+ %(version)s, %(status)s…"

#, python-format
#~ msgid "Quit Nicotine+ %(version)s, %(status)s!"
#~ msgstr "Выход из Nicotine+ %(version)s, %(status)s!"

#~ msgid "User:"
#~ msgstr "Пользователь:"

#, python-format
#~ msgid "All %(ext)s"
#~ msgstr "Все %(ext)s"

#, python-format
#~ msgid "%(number)2s files "
#~ msgstr "%(number)2s файлы "

#~ msgid "Allow regular expressions for the filter's include and exclude"
#~ msgstr ""
#~ "Разрешить регулярные выражения для содержащих и исключающих фильтров"

#~ msgid "Close Nicotine+?"
#~ msgstr "Закрыть Nicotine+?"

#~ msgid "Do you really want to exit Nicotine+?"
#~ msgstr "Вы действительно хотите выйти из Nicotine+?"

#~ msgid "Run in Background"
#~ msgstr "В фоновый режим"

#~ msgid "_Online Notify"
#~ msgstr "_Оповещать о появлении в сети"

#~ msgid "_Prioritize User"
#~ msgstr "_Отдать приоритет"

#~ msgid "_Trust User"
#~ msgstr "_Надёжный"

#~ msgid "Request User's IP Address"
#~ msgstr "IP-адрес пользователя"

#~ msgid "Request IP Address"
#~ msgstr "IP-адрес пользователя"

#~ msgid "Enter the name of the user whose IP address you want to see:"
#~ msgstr "Введите имя пользователя, чей IP-адрес вы хотите видеть:"

#~ msgid "Downloaded"
#~ msgstr "Загружено"

#, python-format
#~ msgid "Failed to add download %(filename)s to shared files: %(error)s"
#~ msgstr ""
#~ "Не удалось добавить загрузку %(filename)s в cписок раздач: %(error)s"

#~ msgid "Unable to Share Folder"
#~ msgstr "Невозможно предоставить общий доступ к каталогу"

#~ msgid "The chosen virtual name is empty"
#~ msgstr "Имя"

#~ msgid "The chosen virtual name already exists"
#~ msgstr "Виртуальное имя уже существует"

#~ msgid "The chosen folder is already shared"
#~ msgstr "Общий доступ к каталогу уже существует"

#~ msgid "Set Virtual Name"
#~ msgstr "Установить имя"

#, python-format
#~ msgid "Enter virtual name for '%(dir)s':"
#~ msgstr "Введите имя для '%(dir)s':"

#~ msgid "The chosen virtual name is either empty or already exists"
#~ msgstr "Выбранное виртуальное имя пусто или уже существует"

#~ msgid "The chosen folder is already shared."
#~ msgstr "К выбранному каталогу уже предоставлен общий доступ."

#, python-format
#~ msgid "%s Properties"
#~ msgstr "%s Свойства"

#~ msgid "Finished rescanning shares"
#~ msgstr "Пересканирование списка раздач завершено"

#, python-format
#~ msgid "Error while scanning %(path)s: %(error)s"
#~ msgstr "Ошибка при сканировании %(path)s: %(error)s"

#~ msgid "Plugin List"
#~ msgstr "Список плагинов"

#~ msgid "Enable sound for notification popups"
#~ msgstr "Включить звук для уведомлений"
