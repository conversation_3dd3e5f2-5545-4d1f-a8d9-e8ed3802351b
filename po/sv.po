# SPDX-FileCopyrightText: 2003-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2024-01-08 18:27+0000\n"
"Last-Translator: OpenAI <<EMAIL>>\n"
"Language-Team: Swedish <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/sv/>\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.4-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr "Soulseek-klient"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Grafisk klient för peer-to-peer-nätverket Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
#, fuzzy
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr "Soulseek;Nicotine;delning;musik;P2P;peer-to-peer;GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
#, fuzzy
msgid "Browse the Soulseek network"
msgstr "Grafisk klient för Soulseek-nätverket"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Grafisk klient för peer-to-peer-nätverket Soulseek"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
#, fuzzy
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ syftar till att vara ett trevligt alternativ till den officiella "
"Soulseek-klienten som är gratis och har öppen källkod (FOSS) och erbjuder "
"ytterligare funktioner samtidigt som det håller sig uppdaterat med Soulseek-"
"protokollet."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
#, fuzzy
msgid "Search Files"
msgstr "Sök filer"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Nedladdningar"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
#, fuzzy
msgid "Browse Shares"
msgstr "Bro_wse Buddy-andelar"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
#, fuzzy
msgid "Private Chat"
msgstr "Privat chatt"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
#, fuzzy
msgid "Nicotine+ Team"
msgstr "Nicotine+ Team"

#: pynicotine/__init__.py:50
#, python-format
msgid "Website: %s"
msgstr "Webbplats: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "visa det här hjälpmeddelandet och avsluta"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "fil"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr "använda en konfigurationsfil som inte är standard"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "katalog"

#: pynicotine/__init__.py:64
#, fuzzy
msgid "alternative directory for user data and plugins"
msgstr "använda en annan katalog än standardkatalogen för plugins"

#: pynicotine/__init__.py:68
#, fuzzy
msgid "start the program without showing window"
msgstr "starta programmet utan att visa fönstret"

#: pynicotine/__init__.py:71
#, fuzzy
msgid "ip"
msgstr "ip"

#: pynicotine/__init__.py:72
#, fuzzy
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "binder sockets till den angivna IP:n (användbart för VPN)"

#: pynicotine/__init__.py:75
#, fuzzy
msgid "port"
msgstr "port"

#: pynicotine/__init__.py:76
#, fuzzy
msgid "listen on the given port"
msgstr "lyssna på den angivna porten"

#: pynicotine/__init__.py:80
#, fuzzy
msgid "rescan shared files"
msgstr "Indexera om delade filer"

#: pynicotine/__init__.py:84
#, fuzzy
msgid "start the program in headless mode (no GUI)"
msgstr "starta programmet i huvudlöst läge (inget grafiskt gränssnitt)"

#: pynicotine/__init__.py:88
#, fuzzy
msgid "display version and exit"
msgstr "visa version och avsluta"

#: pynicotine/__init__.py:121
#, fuzzy, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Du använder version av Python (%s) som saknar stöd.\n"
"Du borde installera Python 3.5 eller senare versioner."

#: pynicotine/__init__.py:192
#, fuzzy
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Det gick inte att skanna aktier. Stäng andra Nicotine+-instanser och försök "
"igen."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, fuzzy, python-format
msgid "%(user)s is away"
msgstr "Användaren %s är away"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, fuzzy, python-format
msgid "%(user)s is online"
msgstr "Användaren %s är online"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, fuzzy, python-format
msgid "%(user)s is offline"
msgstr "Användaren %s är offline"

#: pynicotine/buddies.py:316
#, fuzzy
msgid "Buddy Status"
msgstr "Buddy-lista"

#: pynicotine/chatrooms.py:383
#, fuzzy, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Du har lagts till i ett privat rum: %(room)s"

#: pynicotine/chatrooms.py:478
#, fuzzy, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr ""
"Chattmeddelande från användare '%(user)s' i rummet '%(room)s': %(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr "Kunde inte skapa katalogen '%(path)s', rapporterat fel: %(error)s"

#: pynicotine/config.py:816
#, fuzzy, python-format
msgid "Error backing up config: %s"
msgstr "Fel vid säkerhetskopiering av konfigurationen: %s"

#: pynicotine/config.py:819
#, fuzzy, python-format
msgid "Config backed up to: %s"
msgstr "Konfigurationen säkerhetskopieras till: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, fuzzy, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Sluta med Nicotine+ %(version)s, %(status)s…"

#: pynicotine/core.py:243
#, fuzzy, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Sluta med Nicotine+ %(version)s, %(status)s…"

#: pynicotine/core.py:246
#, fuzzy
msgid "terminating"
msgstr "avslutande av"

#: pynicotine/core.py:246
#, fuzzy
msgid "application closing"
msgstr "Ansökningsavslutning."

#: pynicotine/core.py:259
#, fuzzy, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Sluta med Nicotine+ %(version)s, %(status)s…"

#: pynicotine/core.py:267
#, fuzzy
msgid "You need to specify a username and password before connecting…"
msgstr "Du måste ange ett användarnamn och lösenord innan du ansluter…"

#: pynicotine/downloads.py:239
#, fuzzy, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""
"Fel: Download Filter misslyckades! Kontrollera dina filter. Anledning: %s"

#: pynicotine/downloads.py:254
#, fuzzy, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Fel: %(num)d Nedladdningsfilter misslyckades! %(error)s "

#: pynicotine/downloads.py:366
#, fuzzy, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s hämtad från %(user)s"

#: pynicotine/downloads.py:370
#, fuzzy
msgid "File Downloaded"
msgstr "Fil nerladdad"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Körde: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, fuzzy, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "Exekvering av '%(command)s' misslyckades: %(error)s"

#: pynicotine/downloads.py:407
#, fuzzy, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s hämtad från %(user)s"

#: pynicotine/downloads.py:411
#, fuzzy
msgid "Folder Downloaded"
msgstr "Hämtad mapp"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Körde på mapp: %s"

#: pynicotine/downloads.py:443
#, fuzzy, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "Kunde inte flytta \"%(tempfile)s\" till \"%(file)s\": %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
#, fuzzy
msgid "Download Folder Error"
msgstr "Fel på nedladdningskatalog"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Nedladdning slutförd: användare %(user)s, fil %(file)s"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Nedladdning avbruten: användare %(user)s, fil %(file)s"

#: pynicotine/downloads.py:1150
#, fuzzy, python-format
msgid "Download I/O error: %s"
msgstr "Fel i nedladdningen av I/O: %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Kunde inte upprätta ett exklusivt lås på fil - I/O-fel: %s"

#: pynicotine/downloads.py:1202
#, fuzzy, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Det går inte att spara filen %(path)s: %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Nedladdning påbörjad: användare %(user)s, fil %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, python-format
msgid "Cannot find %s, please install it."
msgstr "Kan inte hitta %s, var snäll installera det."

#: pynicotine/gtkgui/__init__.py:162
#, fuzzy
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr "Ingen grafisk miljö tillgänglig, använder headless (inget GUI) läge"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "Anslut"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "Koppla ned"

#: pynicotine/gtkgui/application.py:308
#, fuzzy
msgid "Soulseek _Privileges"
msgstr "_Privilegierad"

#: pynicotine/gtkgui/application.py:314
#, fuzzy
msgid "_Preferences"
msgstr "_Inställningar"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
#, fuzzy
msgid "_Quit"
msgstr "_Avsluta"

#: pynicotine/gtkgui/application.py:337
#, fuzzy
msgid "Browse _Public Shares"
msgstr "_Bläddra bland offentliga aktier"

#: pynicotine/gtkgui/application.py:338
#, fuzzy
msgid "Browse _Buddy Shares"
msgstr "Bro_wse Buddy-andelar"

#: pynicotine/gtkgui/application.py:339
#, fuzzy
msgid "Browse _Trusted Shares"
msgstr "Bro_wse Buddy-andelar"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
#, fuzzy
msgid "_Rescan Shares"
msgstr "Indexera om delade filer"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
#, fuzzy
msgid "Configure _Shares"
msgstr "_Konfigurera andelar"

#: pynicotine/gtkgui/application.py:371
#, fuzzy
msgid "_Keyboard Shortcuts"
msgstr "_Tangentbordsgenvägar"

#: pynicotine/gtkgui/application.py:372
#, fuzzy
msgid "_Setup Assistant"
msgstr "_Inställningsassistent"

#: pynicotine/gtkgui/application.py:373
#, fuzzy
msgid "_Transfer Statistics"
msgstr "_Överföringsstatistik"

#: pynicotine/gtkgui/application.py:378
#, fuzzy
msgid "Report a _Bug"
msgstr "Rapportera en bugg"

#: pynicotine/gtkgui/application.py:379
#, fuzzy
msgid "Improve T_ranslations"
msgstr "Förbättra T_översättningar"

#: pynicotine/gtkgui/application.py:383
#, fuzzy
msgid "_About Nicotine+"
msgstr "Om _Nicotine+"

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "Arkiv"

#: pynicotine/gtkgui/application.py:395
#, fuzzy
msgid "_Shares"
msgstr "_Aktier"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
#, fuzzy
msgid "_Help"
msgstr "_Hjälp"

#: pynicotine/gtkgui/application.py:410
#, fuzzy
msgid "_Browse Shares"
msgstr "Bro_wse Buddy-andelar"

#: pynicotine/gtkgui/application.py:465
#, fuzzy, python-format
msgid "Unable to show notification: %s"
msgstr "Det går inte att visa popup-fönster för meddelanden: %s"

#: pynicotine/gtkgui/application.py:526
#, fuzzy
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Vill du verkligen sluta med Nicotine+?"

#: pynicotine/gtkgui/application.py:527
#, fuzzy
msgid "Wait for uploads to finish"
msgstr "Vänta på att uploads ska avslutas"

#: pynicotine/gtkgui/application.py:529
#, fuzzy
msgid "Do you really want to exit?"
msgstr "Vill du verkligen sluta med Nicotine+?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
#, fuzzy
msgid "_No"
msgstr "Nej"

#: pynicotine/gtkgui/application.py:535
#, fuzzy
msgid "_Run in Background"
msgstr "Kör i bakgrunden"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
#, fuzzy
msgid "Quit Nicotine+"
msgstr "Sluta med Nicotine+"

#: pynicotine/gtkgui/application.py:561
#, fuzzy
msgid "Shares Not Available"
msgstr "Aktier ej tillgängliga"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
#, fuzzy
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Kontrollera att externa diskar är monterade och att mappbehörigheterna är "
"korrekta."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
#, fuzzy
msgid "_Cancel"
msgstr "_Avbryt"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "Försök igen"

#: pynicotine/gtkgui/application.py:567
#, fuzzy
msgid "_Force Rescan"
msgstr "_Tvinga omsökning"

#: pynicotine/gtkgui/application.py:742
#, fuzzy
msgid "Message Downloading Users"
msgstr "Användare som laddar ned meddelanden"

#: pynicotine/gtkgui/application.py:743
#, fuzzy
msgid "Send private message to all users who are downloading from you:"
msgstr "Skicka privat meddelande till alla användare som laddar ner från dig:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
#, fuzzy
msgid "_Send Message"
msgstr "Skicka meddelande"

#: pynicotine/gtkgui/application.py:756
#, fuzzy
msgid "Message Buddies"
msgstr "Meddelanden"

#: pynicotine/gtkgui/application.py:757
#, fuzzy
msgid "Send private message to all online buddies:"
msgstr "Skicka privat meddelande till alla onlinekompisar:"

#: pynicotine/gtkgui/application.py:786
#, fuzzy
msgid "Select a Saved Shares List File"
msgstr "Välj en fil med en lista över sparade andelar"

#: pynicotine/gtkgui/application.py:877
#, fuzzy
msgid "Critical Error"
msgstr "Kritiskt fel"

#: pynicotine/gtkgui/application.py:878
#, fuzzy
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ har stött på ett kritiskt fel och måste avslutas. Vänligen kopiera "
"följande meddelande och inkludera det i en felrapport:"

#: pynicotine/gtkgui/application.py:882
#, fuzzy
msgid "_Quit Nicotine+"
msgstr "Sluta med Nicotine+"

#: pynicotine/gtkgui/application.py:883
#, fuzzy
msgid "_Copy & Report Bug"
msgstr "Kopiera och rapportera fel"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Status"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
#, fuzzy
msgid "Country"
msgstr "Land"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Användare"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Hastighet"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Filer"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
#, fuzzy
msgid "Trusted"
msgstr "Betrodd"

#: pynicotine/gtkgui/buddies.py:108
#, fuzzy
msgid "Notify"
msgstr "Avisera"

#: pynicotine/gtkgui/buddies.py:114
#, fuzzy
msgid "Prioritized"
msgstr "Prioriterat"

#: pynicotine/gtkgui/buddies.py:120
#, fuzzy
msgid "Last Seen"
msgstr "Senast använd"

#: pynicotine/gtkgui/buddies.py:126
#, fuzzy
msgid "Note"
msgstr "Notera"

#: pynicotine/gtkgui/buddies.py:143
#, fuzzy
msgid "Add User _Note…"
msgstr "Lägg till användare _Obs…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Ta bort"

#: pynicotine/gtkgui/buddies.py:364
#, fuzzy
msgid "Never seen"
msgstr "Aldrig varit inloggad"

#: pynicotine/gtkgui/buddies.py:529
#, fuzzy
msgid "Add User Note"
msgstr "Lägg till användarnotering"

#: pynicotine/gtkgui/buddies.py:530
#, fuzzy, python-format
msgid "Add a note about user %s:"
msgstr "Lägg till några anteckningar om användaren %s:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
#, fuzzy
msgid "_Add"
msgstr "_Lägg till…"

#: pynicotine/gtkgui/chatrooms.py:224
#, fuzzy
msgid "Create New Room?"
msgstr "Skapa ett nytt rum?"

#: pynicotine/gtkgui/chatrooms.py:225
#, fuzzy, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Vill du verkligen skapa ett nytt rum \"%s\"?"

#: pynicotine/gtkgui/chatrooms.py:226
#, fuzzy
msgid "Make room private"
msgstr "Gör rummet privat"

#: pynicotine/gtkgui/chatrooms.py:515
#, fuzzy
msgid "Search activity log…"
msgstr "Sökningar"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
#, fuzzy
msgid "Search chat log…"
msgstr "Sökningar"

#: pynicotine/gtkgui/chatrooms.py:597
#, fuzzy
msgid "Sear_ch User's Files"
msgstr "Sear_ch Användarfiler"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
#, fuzzy
msgid "Find…"
msgstr "Sök…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
#, fuzzy
msgid "Copy"
msgstr "Kopiera"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
#, fuzzy
msgid "Copy All"
msgstr "Kopiera alla"

#: pynicotine/gtkgui/chatrooms.py:608
#, fuzzy
msgid "Clear Activity View"
msgstr "Rensa aktivitetsvyn"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
#, fuzzy
msgid "_Leave Room"
msgstr "_Lämna rummet"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
#, fuzzy
msgid "Copy Link"
msgstr "Kopiera _URL"

#: pynicotine/gtkgui/chatrooms.py:624
#, fuzzy
msgid "View Room Log"
msgstr "Visa loggbok för rummet"

#: pynicotine/gtkgui/chatrooms.py:627
#, fuzzy
msgid "Delete Room Log…"
msgstr "Radera rumslogg…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
#, fuzzy
msgid "Clear Message View"
msgstr "Visa ett meddelande"

#: pynicotine/gtkgui/chatrooms.py:832
#, fuzzy, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s nämnde dig i %(room)s-rummet."

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, fuzzy, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "Meddelande från %(user)s i %(room)s-rummet"

#: pynicotine/gtkgui/chatrooms.py:855
#, fuzzy, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "Meddelande från %(user)s i %(room)s-rummet"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s anslöt till rummet"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s lämnade rummet"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s är away"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s är tillbaka"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
#, fuzzy
msgid "Delete Logged Messages?"
msgstr "Radera loggade meddelanden?"

#: pynicotine/gtkgui/chatrooms.py:1197
#, fuzzy
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"Vill du verkligen radera alla loggade meddelanden för det här rummet "
"permanent?"

#: pynicotine/gtkgui/dialogs/about.py:405
#, fuzzy
msgid "About"
msgstr "Djibouti"

#: pynicotine/gtkgui/dialogs/about.py:415
#, fuzzy
msgid "Website"
msgstr "Webbplats: %s"

#: pynicotine/gtkgui/dialogs/about.py:457
#, fuzzy, python-format
msgid "Error checking latest version: %s"
msgstr "Fel vid hämtning av senaste versionen"

#: pynicotine/gtkgui/dialogs/about.py:462
#, fuzzy, python-format
msgid "New release available: %s"
msgstr "Ny version tillgänglig: %s"

#: pynicotine/gtkgui/dialogs/about.py:467
#, fuzzy
msgid "Up to date"
msgstr "Uppdaterad"

#: pynicotine/gtkgui/dialogs/about.py:496
#, fuzzy
msgid "Checking latest version…"
msgstr "Kontrollera _den senaste versionen"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
#, fuzzy
msgid "Setup Assistant"
msgstr "_Inställningsassistent"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
#, fuzzy
msgid "Virtual Folder"
msgstr "Virtuell mapp"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
#, fuzzy
msgid "Folder"
msgstr "Katalog"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#, fuzzy
msgid "_Previous"
msgstr "Föregående fil"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#, fuzzy
msgid "_Finish"
msgstr "Klar"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
#, fuzzy
msgid "_Next"
msgstr "_Nästa"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
#, fuzzy
msgid "Add a Shared Folder"
msgstr "Lägg till en delad mapp"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
#, fuzzy
msgid "Edit Shared Folder"
msgstr "Delade mappar"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, fuzzy, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Ange ett nytt virtuellt namn för \"%(dir)s\":"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
#, fuzzy
msgid "_Edit"
msgstr "Betyg"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, fuzzy, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"Användaren %s finns redan och det lösenord som du angett är ogiltigt. Välj "
"ett annat användarnamn om det är första gången du loggar in."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
#, fuzzy
msgid "File Properties"
msgstr "Fil Egenskaper"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, fuzzy, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "Filegenskaper (%(num)i eller %(total)i)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, fuzzy, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Filegenskaper (%(num)i eller %(total)i)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
#, fuzzy
msgid "_Apply"
msgstr "Verkställ"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
#, fuzzy
msgid "Add…"
msgstr "Lägg till…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
#, fuzzy
msgid "Edit…"
msgstr "Redigera…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, fuzzy, python-format
msgid "%s Settings"
msgstr "%s Inställningar"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
#, fuzzy
msgid "Add Item"
msgstr "Komponent"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
#, fuzzy
msgid "Edit Item"
msgstr "Intressen"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
#, fuzzy
msgid "Unknown"
msgstr "Okänt"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
#, fuzzy
msgid "Check Port Status"
msgstr "Kontrollera portstatus"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, fuzzy, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, port %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
#, fuzzy
msgid "Password Change Rejected"
msgstr "Lösenordsändring avvisas"

#: pynicotine/gtkgui/dialogs/preferences.py:218
#, fuzzy
msgid "Enter a new password for your Soulseek account:"
msgstr "Ange ett nytt lösenord för ditt Soulseek-konto:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
#, fuzzy
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"Du är för närvarande utloggad från Soulseek-nätverket. Om du vill ändra "
"lösenordet för ett befintligt Soulseek-konto måste du vara inloggad på det "
"kontot."

#: pynicotine/gtkgui/dialogs/preferences.py:223
#, fuzzy
msgid "Enter password to use when logging in:"
msgstr "Ange det lösenord som ska användas när du loggar in:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
#, fuzzy
msgid "Change Password"
msgstr "Lösenord:"

#: pynicotine/gtkgui/dialogs/preferences.py:230
#, fuzzy
msgid "_Change"
msgstr "Lösenordsändring avvisas"

#: pynicotine/gtkgui/dialogs/preferences.py:274
#, fuzzy
msgid "No one"
msgstr "Ingen"

#: pynicotine/gtkgui/dialogs/preferences.py:275
#, fuzzy
msgid "Everyone"
msgstr "Alla"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Kompisar"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
#, fuzzy
msgid "Trusted buddies"
msgstr "Förtroendevalda kompisar"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
#, fuzzy
msgid "Nothing"
msgstr "Ingenting"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
#, fuzzy
msgid "Open File"
msgstr "Nästa fil"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
#, fuzzy
msgid "Open in File Manager"
msgstr "_Öppna i filhanteraren"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Sök"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
#, fuzzy
msgid "Pause"
msgstr "Pausa"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
#, fuzzy
msgid "Resume"
msgstr "Återuppta"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
#, fuzzy
msgid "Browse Folder"
msgstr "_Bläddra i mapp(ar)"

#: pynicotine/gtkgui/dialogs/preferences.py:317
#, fuzzy
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Syntax</b>: Skiftlägesokänslig. Om det är aktiverat kan Python reguljära "
"uttryck användas, annars stöds endast jokertecken *-matchningar."

#: pynicotine/gtkgui/dialogs/preferences.py:327
#, fuzzy
msgid "Filter"
msgstr "Filter"

#: pynicotine/gtkgui/dialogs/preferences.py:334
#, fuzzy
msgid "Regex"
msgstr "Regex"

#: pynicotine/gtkgui/dialogs/preferences.py:468
#, fuzzy
msgid "Add Download Filter"
msgstr "Lägg till filter för nedladdning"

#: pynicotine/gtkgui/dialogs/preferences.py:469
#, fuzzy
msgid "Enter a new download filter:"
msgstr "Ange ett nytt nedladdningsfilter:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
#, fuzzy
msgid "Enable regular expressions"
msgstr "Aktivera reguljära uttryck"

#: pynicotine/gtkgui/dialogs/preferences.py:498
#, fuzzy
msgid "Edit Download Filter"
msgstr "Redigera filter för nedladdning"

#: pynicotine/gtkgui/dialogs/preferences.py:499
#, fuzzy
msgid "Modify the following download filter:"
msgstr "Ändra följande nedladdningsfilter:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, fuzzy, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d Misslyckades! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
#, fuzzy
msgid "Filters Successful"
msgstr "Filter Framgångsrikt"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
#, fuzzy
msgid "Public"
msgstr "Offentlig"

#: pynicotine/gtkgui/dialogs/preferences.py:626
#, fuzzy
msgid "Accessible To"
msgstr "Tillgänglig för"

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "Avbryt"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
#, fuzzy
msgid "Retry"
msgstr "Försök igen"

#: pynicotine/gtkgui/dialogs/preferences.py:828
#, fuzzy
msgid "Round Robin"
msgstr "Round Robin"

#: pynicotine/gtkgui/dialogs/preferences.py:829
#, fuzzy
msgid "First In, First Out"
msgstr "Först in, först ut"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
#, fuzzy
msgid "Username"
msgstr "Användarnamn"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
#, fuzzy
msgid "IP Address"
msgstr "Blockera IP-adress"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
#, fuzzy
msgid "Ignore User"
msgstr "Ignorera användare"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
#, fuzzy
msgid "Enter the name of the user you want to ignore:"
msgstr "Ange namnet på den användare som du vill ignorera:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
#, fuzzy
msgid "Ignore IP Address"
msgstr "Ignorera IP-adress"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#, fuzzy
msgid "Enter an IP address you want to ignore:"
msgstr "Ange en IP-adress som du vill ignorera:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
#, fuzzy
msgid "* is a wildcard"
msgstr "* är ett jokertecken"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
#, fuzzy
msgid "Ban User"
msgstr "Förbjud användare"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
#, fuzzy
msgid "Enter the name of the user you want to ban:"
msgstr "Ange namnet på den användare som du vill spärra:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
#, fuzzy
msgid "Ban IP Address"
msgstr "Blockera IP-adress"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
#, fuzzy
msgid "Enter an IP address you want to ban:"
msgstr "Ange en IP-adress som du vill blockera:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
#, fuzzy
msgid "Format codes"
msgstr "Formatkoder"

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
#, fuzzy
msgid "Pattern"
msgstr "Mönster"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
#, fuzzy
msgid "Replacement"
msgstr "Ersättning"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
#, fuzzy
msgid "Censor Pattern"
msgstr "Censurmönster"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
#, fuzzy
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Ange ett mönster som du vill censurera. Lägg till blanksteg runt mönstret om "
"du inte vill matcha strängar inom ord (kan misslyckas i början och slutet av "
"rader)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
#, fuzzy
msgid "Edit Censored Pattern"
msgstr "Censurerade mönster"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
#, fuzzy
msgid "Add Replacement"
msgstr "Ersättning"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
#, fuzzy
msgid "Enter a text pattern and what to replace it with:"
msgstr "Ange textmönstret respektive ersättning:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
#, fuzzy
msgid "Edit Replacement"
msgstr "Ersättning"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
#, fuzzy
msgid "System default"
msgstr "Standard"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
#, fuzzy
msgid "Show confirmation dialog"
msgstr "Visa dialogrutan för bekräftelse"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
#, fuzzy
msgid "Run in the background"
msgstr "kör i bakgrunden"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
#, fuzzy
msgid "bold"
msgstr "fetstil"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
#, fuzzy
msgid "italic"
msgstr "kursiv"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
#, fuzzy
msgid "underline"
msgstr "Understrykning"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
#, fuzzy
msgid "normal"
msgstr "normal"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
#, fuzzy
msgid "Separate Buddies tab"
msgstr "Meddelanden"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
#, fuzzy
msgid "Sidebar in Chat Rooms tab"
msgstr "Kompislista i chattrum"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
#, fuzzy
msgid "Always visible sidebar"
msgstr "Alltid synlig sidopanel"

#: pynicotine/gtkgui/dialogs/preferences.py:1777
#, fuzzy
msgid "Top"
msgstr "Topp"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
#, fuzzy
msgid "Bottom"
msgstr "Botten"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
#, fuzzy
msgid "Left"
msgstr "Vänster"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
#, fuzzy
msgid "Right"
msgstr "Höger"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "Online"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Away"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Offline"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
#, fuzzy
msgid "Tab Changed"
msgstr "Lösenordsändring avvisas"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
#, fuzzy
msgid "Tab Highlight"
msgstr "Markera"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
#, fuzzy
msgid "Window"
msgstr "Fönster"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
#, fuzzy
msgid "Online (Tray)"
msgstr "Ansluten:"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
#, fuzzy
msgid "Away (Tray)"
msgstr "Away (bricka)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
#, fuzzy
msgid "Offline (Tray)"
msgstr "Offline"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
#, fuzzy
msgid "Message (Tray)"
msgstr "Meddelande (fack)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Protokoll"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
#, fuzzy
msgid "Command"
msgstr "Kommentarer"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
#, fuzzy
msgid "Add URL Handler"
msgstr "Hanterare"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
#, fuzzy
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Ange protokollet och kommandot för URL-handern, respektive:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
#, fuzzy
msgid "Edit Command"
msgstr "Redigera kommentarer"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, fuzzy, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Ange ett nytt virtuellt namn för \"%(dir)s\":"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
#, fuzzy
msgid "Username;APIKEY"
msgstr "Användarnamn:"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
#, fuzzy
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr "Klientnamn (t.ex. amarok, audacious, exaile) eller tomt för auto:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
#, fuzzy
msgid "Username: "
msgstr "Användarnamn:"

#: pynicotine/gtkgui/dialogs/preferences.py:2767
#, fuzzy
msgid "Command:"
msgstr "Kommentarer"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
#, fuzzy
msgid "Title"
msgstr "Titel"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, fuzzy, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Spela nu (vanligtvis \"%(artist)s - %(title)s\")"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
#, fuzzy
msgid "Artist"
msgstr "Artist"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
#, fuzzy
msgid "Duration"
msgstr "Varaktighet"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "Bitrate"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
#, fuzzy
msgid "Comment"
msgstr "Kommentar"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
#, fuzzy
msgid "Album"
msgstr "Album"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
#, fuzzy
msgid "Track Number"
msgstr "Spårnummer"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
#, fuzzy
msgid "Year"
msgstr "År"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
#, fuzzy
msgid "Filename (URI)"
msgstr "Filnamn (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
#, fuzzy
msgid "Program"
msgstr "Program"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
#, fuzzy
msgid "Enabled"
msgstr "Aktiverat"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
#, fuzzy
msgid "Plugin"
msgstr "Plugin"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
#, fuzzy
msgid "No Plugin Selected"
msgstr "Ingen insticksmodul vald"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
#, fuzzy
msgid "Preferences"
msgstr "Inställningar"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
#, fuzzy
msgid "Network"
msgstr "Nätverk"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
#, fuzzy
msgid "User Interface"
msgstr "Gränssnitt"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Delade mappar"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Uppladdningar"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Sökningar"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
#, fuzzy
msgid "User Profile"
msgstr "Användare Bläddra"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
#, fuzzy
msgid "Chats"
msgstr "_Chattrum"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
#, fuzzy
msgid "Now Playing"
msgstr "Spelas nu"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Loggar"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
#, fuzzy
msgid "Banned Users"
msgstr "Banna"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
#, fuzzy
msgid "Ignored Users"
msgstr "Ignorerade användare:"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
#, fuzzy
msgid "URL Handlers"
msgstr "Hanterare"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
#, fuzzy
msgid "Plugins"
msgstr "Plugin"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
#, fuzzy
msgid "Pick a File Name for Config Backup"
msgstr "Välj ett filnamn för konfigurationsbackupen"

#: pynicotine/gtkgui/dialogs/statistics.py:75
#, fuzzy
msgid "Transfer Statistics"
msgstr "Överföringsstatistik"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, fuzzy, python-format
msgid "Total Since %(date)s"
msgstr "Totalt sedan %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
#, fuzzy
msgid "Reset Transfer Statistics?"
msgstr "Återställa överföringsstatistik?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
#, fuzzy
msgid "Do you really want to reset transfer statistics?"
msgstr "Vill du verkligen återställa överföringsstatistiken?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
#, fuzzy
msgid "Wishlist"
msgstr "_Vänslista"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
#, fuzzy
msgid "Wish"
msgstr "Önskemål"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
#, fuzzy
msgid "_Search for Item"
msgstr "_Söka efter objekt"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
#, fuzzy
msgid "Edit Wish"
msgstr "Betyg"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, fuzzy, python-format
msgid "Enter new value for wish '%s':"
msgstr "Ange ett nytt virtuellt namn för \"%(dir)s\":"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
#, fuzzy
msgid "Clear Wishlist?"
msgstr "Klar önskelista?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
#, fuzzy
msgid "Do you really want to clear your wishlist?"
msgstr "Vill du verkligen rensa din önskelista?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Sökväg"

#: pynicotine/gtkgui/downloads.py:47
#, fuzzy
msgid "_Resume"
msgstr "_Resumé"

#: pynicotine/gtkgui/downloads.py:48
#, fuzzy
msgid "P_ause"
msgstr "P_ause"

#: pynicotine/gtkgui/downloads.py:72
#, fuzzy
msgid "Finished / Filtered"
msgstr "Färdiga / filtrerade"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Klar"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
#, fuzzy
msgid "Paused"
msgstr "Pausad"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
#, fuzzy
msgid "Filtered"
msgstr "Filtrerad"

#: pynicotine/gtkgui/downloads.py:77
#, fuzzy
msgid "Deleted"
msgstr "raderade"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
#, fuzzy
msgid "Queued…"
msgstr "I kö…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
#, fuzzy
msgid "Everything…"
msgstr "Allt…"

#: pynicotine/gtkgui/downloads.py:132
#, fuzzy, python-format
msgid "Downloads: %(speed)s"
msgstr "Nedladdningar: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
#, fuzzy
msgid "Clear Queued Downloads"
msgstr "Rensa köade nedladdningar"

#: pynicotine/gtkgui/downloads.py:139
#, fuzzy
msgid "Do you really want to clear all queued downloads?"
msgstr "Vill du verkligen rensa alla nedladdningar som står i kö?"

#: pynicotine/gtkgui/downloads.py:151
#, fuzzy
msgid "Clear All Downloads"
msgstr "Rensa alla nedladdningar"

#: pynicotine/gtkgui/downloads.py:152
#, fuzzy
msgid "Do you really want to clear all downloads?"
msgstr "Vill du verkligen rensa alla nedladdningar?"

#: pynicotine/gtkgui/downloads.py:169
#, fuzzy, python-format
msgid "Download %(num)i files?"
msgstr "Ladda ner %(num)i filer?"

#: pynicotine/gtkgui/downloads.py:170
#, fuzzy, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"Vill du verkligen ladda ner %(num)i-filer från %(user)ss mapp %(folder)s?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
#, fuzzy
msgid "_Download Folder"
msgstr "_Hämtningsmapp"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
#, fuzzy
msgid "Likes"
msgstr "Gillar"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
#, fuzzy
msgid "Dislikes"
msgstr "Ogillar"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Betyg"

#: pynicotine/gtkgui/interests.py:111
#, fuzzy
msgid "Item"
msgstr "Komponent"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
#, fuzzy
msgid "_Recommendations for Item"
msgstr "_Rekommendationer för punkt"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
#, fuzzy
msgid "I _Like This"
msgstr "Jag _gillar det här"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
#, fuzzy
msgid "I _Dislike This"
msgstr "Jag _Obillar mig detta"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
#, fuzzy
msgid "Recommendations"
msgstr "Rekommendationer"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
#, fuzzy
msgid "Similar Users"
msgstr "Liknande användare"

#: pynicotine/gtkgui/interests.py:427
#, fuzzy, python-format
msgid "Recommendations (%s)"
msgstr "Rekommendationer för %s"

#: pynicotine/gtkgui/interests.py:451
#, fuzzy, python-format
msgid "Similar Users (%s)"
msgstr "Liknande användare"

#: pynicotine/gtkgui/mainwindow.py:238
#, fuzzy
msgid "Search log…"
msgstr "Sökningar"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, fuzzy, python-format
msgid "Private Message from %(user)s"
msgstr "Privat meddelande från %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
#, fuzzy
msgid "Wishlist Results Found"
msgstr "Resultat för önskelista hittades"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
#, fuzzy
msgid "User Profiles"
msgstr "Användare Bläddra"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
#, fuzzy
msgid "Chat Rooms"
msgstr "Chattrum"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "Intressen"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
#, fuzzy
msgid "Chat"
msgstr "Chatt"

#: pynicotine/gtkgui/mainwindow.py:1111
#, fuzzy
msgid "[Debug] Connections"
msgstr "Anslutningar"

#: pynicotine/gtkgui/mainwindow.py:1112
#, fuzzy
msgid "[Debug] Messages"
msgstr "Meddelanden"

#: pynicotine/gtkgui/mainwindow.py:1113
#, fuzzy
msgid "[Debug] Transfers"
msgstr "Överföringar"

#: pynicotine/gtkgui/mainwindow.py:1114
#, fuzzy
msgid "[Debug] Miscellaneous"
msgstr "Diverse"

#: pynicotine/gtkgui/mainwindow.py:1119
#, fuzzy
msgid "_Find…"
msgstr "Sök…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
#, fuzzy
msgid "_Copy"
msgstr "Kopiera"

#: pynicotine/gtkgui/mainwindow.py:1122
#, fuzzy
msgid "Copy _All"
msgstr "Kopiera alla"

#: pynicotine/gtkgui/mainwindow.py:1127
#, fuzzy
msgid "View _Debug Logs"
msgstr "Visa felsökningsloggar"

#: pynicotine/gtkgui/mainwindow.py:1128
#, fuzzy
msgid "View _Transfer Logs"
msgstr "Visa överföringslogg"

#: pynicotine/gtkgui/mainwindow.py:1132
#, fuzzy
msgid "_Log Categories"
msgstr "Kategorier"

#: pynicotine/gtkgui/mainwindow.py:1134
#, fuzzy
msgid "Clear Log View"
msgstr "Rensa loggvyn"

#: pynicotine/gtkgui/mainwindow.py:1199
#, fuzzy
msgid "Preparing Shares"
msgstr "Indexering påbörjad"

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr "Indexering påbörjad"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
#, fuzzy
msgid "Shared Folders"
msgstr "Delade mappar"

#: pynicotine/gtkgui/popovers/chathistory.py:77
#, fuzzy
msgid "Latest Message"
msgstr "Skicka meddelande"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Rum"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Användare"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
#, fuzzy
msgid "Join Room"
msgstr "Gå in i rummet"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
#, fuzzy
msgid "Leave Room"
msgstr "Lämna rummet"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
#, fuzzy
msgid "Disown Private Room"
msgstr "Disown privat rum"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
#, fuzzy
msgid "Cancel Room Membership"
msgstr "Avbryta medlemskap i rummet"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
#, fuzzy
msgid "Close All Tabs…"
msgstr "Stäng alla flikar…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
#, fuzzy
msgid "_Close Tab"
msgstr "_Stäng fliken"

#: pynicotine/gtkgui/privatechat.py:379
#, fuzzy
msgid "View Chat Log"
msgstr "Visa chattlogg"

#: pynicotine/gtkgui/privatechat.py:382
#, fuzzy
msgid "Delete Chat Log…"
msgstr "Ta bort chattlogg…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
#, fuzzy
msgid "User Actions"
msgstr "Överföringar"

#: pynicotine/gtkgui/privatechat.py:477
#, fuzzy
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Vill du verkligen radera alla loggade meddelanden för den här användaren "
"permanent?"

#: pynicotine/gtkgui/privatechat.py:528
#, fuzzy
msgid "* Messages sent while you were offline"
msgstr ""
"* Meddelanden som skickades medan du var offline. Tidsstämplar rapporteras "
"av servern och kan vara avstängda."

#: pynicotine/gtkgui/search.py:90
#, fuzzy
msgid "_Global"
msgstr "Global"

#: pynicotine/gtkgui/search.py:91
#, fuzzy
msgid "_Buddies"
msgstr "Kompisar"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
#, fuzzy
msgid "_Rooms"
msgstr "Rum"

#: pynicotine/gtkgui/search.py:93
#, fuzzy
msgid "_User"
msgstr "Användare"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
#, fuzzy
msgid "In Queue"
msgstr "I kö"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
#, fuzzy
msgid "File Type"
msgstr "Filtyp"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Filnamn"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Storlek"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
#, fuzzy
msgid "Quality"
msgstr "Kvalitet"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
#, fuzzy
msgid "Copy _File Path"
msgstr "Kopiera _Filens sökväg"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "Kopiera _URL"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
#, fuzzy
msgid "Copy Folder U_RL"
msgstr "Kopiera mapp U_RL"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
#, fuzzy
msgid "_Download File(s)"
msgstr "_Ladda ner fil(er)"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
#, fuzzy
msgid "Download File(s) _To…"
msgstr "Ladda ner fil(er) _till…"

#: pynicotine/gtkgui/search.py:615
#, fuzzy
msgid "Download _Folder(s)"
msgstr "Ladda ner _mapp(ar)"

#: pynicotine/gtkgui/search.py:616
#, fuzzy
msgid "Download F_older(s) To…"
msgstr "Ladda ner F_older(s) till…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
#, fuzzy
msgid "View User _Profile"
msgstr "_Bläddra filer"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
#, fuzzy
msgid "_Browse Folder"
msgstr "_Bläddra i mapp(ar)"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
#, fuzzy
msgid "F_ile Properties"
msgstr "F_ile Egenskaper"

#: pynicotine/gtkgui/search.py:629
#, fuzzy
msgid "Copy Search Term"
msgstr "Kopiera sökbegrepp"

#: pynicotine/gtkgui/search.py:631
#, fuzzy
msgid "Clear All Results"
msgstr "Rensa alla resultat"

#: pynicotine/gtkgui/search.py:718
#, fuzzy
msgid "Clear Filters"
msgstr "Rensa filterhistorik"

#: pynicotine/gtkgui/search.py:721
#, fuzzy
msgid "Restore Filters"
msgstr "_Resultatfilter"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, fuzzy, python-format
msgid "[PRIVATE]  %s"
msgstr "[PRIVAT]"

#: pynicotine/gtkgui/search.py:1273
#, fuzzy, python-format
msgid "_Result Filters [%d]"
msgstr "_Result Filters [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
#, fuzzy
msgid "_Result Filters"
msgstr "_Resultatfilter"

#: pynicotine/gtkgui/search.py:1277
#, fuzzy, python-format
msgid "%d active filter(s)"
msgstr "Rensa alla aktiva filter"

#: pynicotine/gtkgui/search.py:1329
#, fuzzy
msgid "Add Wi_sh"
msgstr "Lägg till Wi_sh"

#: pynicotine/gtkgui/search.py:1332
#, fuzzy
msgid "Remove Wi_sh"
msgstr "Ta bort ett alias"

#: pynicotine/gtkgui/search.py:1349
#, fuzzy
msgid "Select User's Results"
msgstr "Välj användarens överföringar"

#: pynicotine/gtkgui/search.py:1472
#, fuzzy, python-format
msgid "Total: %s"
msgstr "Totalt"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
#, fuzzy
msgid "Results"
msgstr "Resultat"

#: pynicotine/gtkgui/search.py:1590
#, fuzzy
msgid "Select Destination Folder for File(s)"
msgstr "Välj destination för nedladdning av fil(er) från användare"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
#, fuzzy
msgid "Select Destination Folder"
msgstr "Välj en mapp"

#: pynicotine/gtkgui/transfers.py:61
#, fuzzy
msgid "Queued"
msgstr "Köade"

#: pynicotine/gtkgui/transfers.py:62
#, fuzzy
msgid "Queued (prioritized)"
msgstr "prioriteras"

#: pynicotine/gtkgui/transfers.py:63
#, fuzzy
msgid "Queued (privileged)"
msgstr "privilegierad"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Hämtar status"

#: pynicotine/gtkgui/transfers.py:65
#, fuzzy
msgid "Transferring"
msgstr "Överför"

#: pynicotine/gtkgui/transfers.py:66
#, fuzzy
msgid "Connection closed"
msgstr "Anslutningen stängd av annan användare"

#: pynicotine/gtkgui/transfers.py:67
#, fuzzy
msgid "Connection timeout"
msgstr "Anslut"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Användaren utloggad"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
#, fuzzy
msgid "Cancelled"
msgstr "Avbryt"

#: pynicotine/gtkgui/transfers.py:73
#, fuzzy
msgid "Download folder error"
msgstr "Fel på nedladdningskatalog"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Lokalt filfel"

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr "Banna"

#: pynicotine/gtkgui/transfers.py:76
#, fuzzy
msgid "File not shared"
msgstr "Filen är inte delad"

#: pynicotine/gtkgui/transfers.py:77
#, fuzzy
msgid "Pending shutdown"
msgstr "Väntar på avstängning"

#: pynicotine/gtkgui/transfers.py:78
#, fuzzy
msgid "File read error"
msgstr "Överföringar"

#: pynicotine/gtkgui/transfers.py:182
#, fuzzy
msgid "Queue"
msgstr "Köade"

#: pynicotine/gtkgui/transfers.py:188
#, fuzzy
msgid "Percent"
msgstr "Procent"

#: pynicotine/gtkgui/transfers.py:208
#, fuzzy
msgid "Time Elapsed"
msgstr "Förfluten tid"

#: pynicotine/gtkgui/transfers.py:215
#, fuzzy
msgid "Time Left"
msgstr "Tid kvar"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
#, fuzzy
msgid "_Open File"
msgstr "_Öppen lista"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
#, fuzzy
msgid "Open in File _Manager"
msgstr "Öppna i File _Manager"

#: pynicotine/gtkgui/transfers.py:286
#, fuzzy
msgid "_Search"
msgstr "_Sök"

#: pynicotine/gtkgui/transfers.py:289
#, fuzzy
msgid "Clear All"
msgstr "Rensa alla"

#: pynicotine/gtkgui/transfers.py:953
#, fuzzy
msgid "Select User's Transfers"
msgstr "Välj användarens överföringar"

#: pynicotine/gtkgui/uploads.py:50
#, fuzzy
msgid "_Abort"
msgstr "Avbryt"

#: pynicotine/gtkgui/uploads.py:74
#, fuzzy
msgid "Finished / Cancelled / Failed"
msgstr "Avslutad / avbruten / misslyckad"

#: pynicotine/gtkgui/uploads.py:75
#, fuzzy
msgid "Finished / Cancelled"
msgstr "Färdiga / filtrerade"

#: pynicotine/gtkgui/uploads.py:79
#, fuzzy
msgid "Failed"
msgstr "Misslyckades"

#: pynicotine/gtkgui/uploads.py:80
#, fuzzy
msgid "User Logged Off"
msgstr "Användaren utloggad"

#: pynicotine/gtkgui/uploads.py:142
#, fuzzy, python-format
msgid "Uploads: %(speed)s"
msgstr "Uppladdningar: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
#, fuzzy
msgid "Quitting..."
msgstr "Avslutar..."

#: pynicotine/gtkgui/uploads.py:164
#, fuzzy
msgid "Clear Queued Uploads"
msgstr "Rensa köade uppladdningar"

#: pynicotine/gtkgui/uploads.py:165
#, fuzzy
msgid "Do you really want to clear all queued uploads?"
msgstr "Vill du verkligen rensa alla uppladdningar som står i kö?"

#: pynicotine/gtkgui/uploads.py:177
#, fuzzy
msgid "Clear All Uploads"
msgstr "Rensa alla uppladdningar"

#: pynicotine/gtkgui/uploads.py:178
#, fuzzy
msgid "Do you really want to clear all uploads?"
msgstr "Vill du verkligen rensa alla uppladdningar?"

#: pynicotine/gtkgui/userbrowse.py:282
#, fuzzy
msgid "_Save Shares List to Disk"
msgstr "_Spara listan över delningar på disk"

#: pynicotine/gtkgui/userbrowse.py:292
#, fuzzy
msgid "Upload Folder & Subfolders…"
msgstr "Ladda upp mapp (med undermappar) till användare"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
#, fuzzy
msgid "Copy _Folder Path"
msgstr "Kopiera _Folder Path"

#: pynicotine/gtkgui/userbrowse.py:309
#, fuzzy
msgid "_Download Folder & Subfolders"
msgstr "Ladda ner _mapp(ar)"

#: pynicotine/gtkgui/userbrowse.py:310
#, fuzzy
msgid "Download Folder & Subfolders _To…"
msgstr "Ladda ner F_older(s) till…"

#: pynicotine/gtkgui/userbrowse.py:334
#, fuzzy
msgid "File Name"
msgstr "Filnamn"

#: pynicotine/gtkgui/userbrowse.py:373
#, fuzzy
msgid "Up_load File(s)…"
msgstr "Ladda upp fil(er)"

#: pynicotine/gtkgui/userbrowse.py:374
#, fuzzy
msgid "Upload Folder…"
msgstr "Ladda upp mapp till…"

#: pynicotine/gtkgui/userbrowse.py:396
#, fuzzy
msgid "Download Folder _To…"
msgstr "Fel på nedladdningskatalog"

#: pynicotine/gtkgui/userbrowse.py:600
#, fuzzy
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"Användarens lista över delade filer är tom. Antingen delar användaren "
"ingenting eller så delar de filer privat."

#: pynicotine/gtkgui/userbrowse.py:615
#, fuzzy
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Det går inte att begära delade filer från användaren. Antingen är användaren "
"offline, ni har båda en stängd lyssnarport eller så är det ett tillfälligt "
"anslutningsproblem."

#: pynicotine/gtkgui/userbrowse.py:953
#, fuzzy
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Välj destination för nedladdning av en mapp från användare"

#: pynicotine/gtkgui/userbrowse.py:997
#, fuzzy
msgid "Upload Folder (with Subfolders) To User"
msgstr "Ladda upp mapp (med undermappar) till användare"

#: pynicotine/gtkgui/userbrowse.py:999
#, fuzzy
msgid "Upload Folder To User"
msgstr "Ladda upp mapp till…"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
#, fuzzy
msgid "Enter the name of the user you want to upload to:"
msgstr "Ange namnet på den användare som du vill ladda upp till:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
#, fuzzy
msgid "_Upload"
msgstr "_Uppladdningar"

#: pynicotine/gtkgui/userbrowse.py:1139
#, fuzzy
msgid "Select Destination Folder for Files"
msgstr "Välj destination för nedladdning av fil(er) från användare"

#: pynicotine/gtkgui/userbrowse.py:1161
#, fuzzy
msgid "Upload File(s) To User"
msgstr "Ladda upp fil(er)"

#: pynicotine/gtkgui/userinfo.py:376
#, fuzzy
msgid "Copy Picture"
msgstr "Bild:"

#: pynicotine/gtkgui/userinfo.py:377
#, fuzzy
msgid "Save Picture"
msgstr "Spara bild"

#: pynicotine/gtkgui/userinfo.py:468
#, fuzzy, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Det gick inte att läsa in bilden för användaren %(user)s: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
#, fuzzy
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Det går inte att begära information från användaren. Antingen har ni båda en "
"stängd lyssnarport, användaren är offline eller så är det ett tillfälligt "
"anslutningsproblem."

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Remove _Buddy"
msgstr "Ta bort ett alias"

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Add _Buddy"
msgstr "Lägg till kompis…"

#: pynicotine/gtkgui/userinfo.py:581
#, fuzzy
msgid "Unban User"
msgstr "Förbjud användare"

#: pynicotine/gtkgui/userinfo.py:585
#, fuzzy
msgid "Unignore User"
msgstr "Ignorera användare"

#: pynicotine/gtkgui/userinfo.py:613
#, fuzzy
msgid "Yes"
msgstr "Ja"

#: pynicotine/gtkgui/userinfo.py:613
#, fuzzy
msgid "No"
msgstr "Nej"

#: pynicotine/gtkgui/userinfo.py:773
#, fuzzy
msgid "Please enter number of days."
msgstr "Ange ett helt tal!"

#: pynicotine/gtkgui/userinfo.py:787
#, fuzzy, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Gåva dagar av dina Soulseek-privilegier till användare %(user)s "
"(%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, fuzzy, python-format
msgid "%(days)s days left"
msgstr "%(days)s dagar kvar"

#: pynicotine/gtkgui/userinfo.py:795
#, fuzzy
msgid "Gift Privileges"
msgstr "Ge privilegier"

#: pynicotine/gtkgui/userinfo.py:797
#, fuzzy
msgid "_Give Privileges"
msgstr "Ge privilegier"

#: pynicotine/gtkgui/widgets/dialogs.py:309
#, fuzzy
msgid "Close"
msgstr "Stäng flik"

#: pynicotine/gtkgui/widgets/dialogs.py:488
#, fuzzy
msgid "_Yes"
msgstr "_Ja"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
#, fuzzy
msgid "_OK"
msgstr "OK"

#: pynicotine/gtkgui/widgets/filechooser.py:39
#, fuzzy
msgid "Select a File"
msgstr "Välj en Fil"

#: pynicotine/gtkgui/widgets/filechooser.py:174
#, fuzzy
msgid "Select a Folder"
msgstr "Välj en mapp"

#: pynicotine/gtkgui/widgets/filechooser.py:179
#, fuzzy
msgid "_Select"
msgstr "Välj Alla"

#: pynicotine/gtkgui/widgets/filechooser.py:196
#, fuzzy
msgid "Select an Image"
msgstr "Välj en bild"

#: pynicotine/gtkgui/widgets/filechooser.py:203
#, fuzzy
msgid "All images"
msgstr "Alla bilder"

#: pynicotine/gtkgui/widgets/filechooser.py:241
#, fuzzy
msgid "Save as…"
msgstr "Spara som…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
#, fuzzy
msgid "(None)"
msgstr "(ingen)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
#, fuzzy
msgid "Close Tab"
msgstr "_Stäng fliken"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
#, fuzzy
msgid "Close All Tabs?"
msgstr "Stänga alla flikar?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
#, fuzzy
msgid "Do you really want to close all tabs?"
msgstr "Vill du verkligen stänga alla flikar?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, fuzzy, python-format
msgid "%i Unread Tab(s)"
msgstr "Olästa flikar"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
#, fuzzy
msgid "All Tabs"
msgstr "Stänga alla flikar?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
#, fuzzy
msgid "Re_open Closed Tab"
msgstr "_Stäng fliken"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, fuzzy, python-format
msgid "%s File(s) Selected"
msgstr "%s Fil(er) vald(a)"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
#, fuzzy
msgid "_Browse Files"
msgstr "_Sök"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
#, fuzzy
msgid "_Add Buddy"
msgstr "Lägg till kompis…"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
#, fuzzy
msgid "Show IP A_ddress"
msgstr "Visa IP A_ddress"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
#, fuzzy
msgid "Private Rooms"
msgstr "Privata rum"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, fuzzy, python-format
msgid "Remove from Private Room %s"
msgstr "Ta bort från ett privat rum %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, fuzzy, python-format
msgid "Add to Private Room %s"
msgstr "Lägg till privat rum %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, fuzzy, python-format
msgid "Remove as Operator of %s"
msgstr "Ta bort som Operatör av %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, fuzzy, python-format
msgid "Add as Operator of %s"
msgstr "Lägg till som Operatör av %s"

#: pynicotine/gtkgui/widgets/textentry.py:37
#, fuzzy
msgid "Send message…"
msgstr "Skicka meddelande"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
#, fuzzy
msgid "Find Previous Match"
msgstr "Hitta tidigare matchning"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
#, fuzzy
msgid "Find Next Match"
msgstr "Hitta nästa matchning"

#: pynicotine/gtkgui/widgets/textview.py:443
#, fuzzy
msgid "--- old messages above ---"
msgstr "--- gamla meddelanden ovan ---"

#: pynicotine/gtkgui/widgets/theme.py:243
#, fuzzy
msgid "Executable"
msgstr "Körde: %s"

#: pynicotine/gtkgui/widgets/theme.py:244
#, fuzzy
msgid "Audio"
msgstr "Audio"

#: pynicotine/gtkgui/widgets/theme.py:245
#, fuzzy
msgid "Image"
msgstr "Bild:"

#: pynicotine/gtkgui/widgets/theme.py:246
#, fuzzy
msgid "Archive"
msgstr "Arkiv"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
#, fuzzy
msgid "Miscellaneous"
msgstr "Diverse"

#: pynicotine/gtkgui/widgets/theme.py:248
#, fuzzy
msgid "Video"
msgstr "Video"

#: pynicotine/gtkgui/widgets/theme.py:249
#, fuzzy
msgid "Document"
msgstr "Dokument/text"

#: pynicotine/gtkgui/widgets/theme.py:250
#, fuzzy
msgid "Text"
msgstr "Text"

#: pynicotine/gtkgui/widgets/theme.py:357
#, fuzzy, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "Fel vid laddning av anpassad ikon %(path)s: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
#, fuzzy
msgid "Hide Nicotine+"
msgstr "Dölj Nicotine+"

#: pynicotine/gtkgui/widgets/trayicon.py:114
#, fuzzy
msgid "Show Nicotine+"
msgstr "Visa Nicotine+"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, fuzzy, python-format
msgid "Column #%i"
msgstr "Kolumn #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
#, fuzzy
msgid "Ungrouped"
msgstr "Ogrupperade"

#: pynicotine/gtkgui/widgets/treeview.py:960
#, fuzzy
msgid "Group by Folder"
msgstr "Gruppera efter mapp"

#: pynicotine/gtkgui/widgets/treeview.py:963
#, fuzzy
msgid "Group by User"
msgstr "Gruppera efter användare"

#: pynicotine/headless/application.py:77
#, fuzzy, python-format
msgid "Do you really want to exit? %s"
msgstr "Vill du verkligen sluta med Nicotine+?"

#: pynicotine/headless/application.py:81
#, fuzzy, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr ""
"Användaren %s finns redan och det lösenord som du angett är ogiltigt. Välj "
"ett annat användarnamn om det är första gången du loggar in."

#: pynicotine/headless/application.py:83
#, fuzzy, python-format
msgid "Type %s to log in with another username or password."
msgstr "Skriv %s för att logga in med ett annat användarnamn eller lösenord."

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
#, fuzzy
msgid "Password: "
msgstr "Lösenord"

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
#, fuzzy
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Om du vill skapa ett nytt Soulseek-konto fyller du i önskat användarnamn och "
"lösenord. Om du redan har ett konto fyller du i dina befintliga "
"inloggningsuppgifter."

#: pynicotine/headless/application.py:119
#, fuzzy
msgid "The following shares are unavailable:"
msgstr "Följande shares är inte tillgängliga:"

#: pynicotine/headless/application.py:125
#, fuzzy, python-format
msgid "Retry rescan? %s"
msgstr "Försök med omskanning? %s"

#: pynicotine/logfacility.py:181
#, fuzzy, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr "Kunde inte skriva till loggfilen \"%(filename)s\": %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, fuzzy, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Det går inte att spara filen %(path)s: %(error)s"

#: pynicotine/networkfilter.py:40
#, fuzzy
msgid "Andorra"
msgstr "Andorra"

#: pynicotine/networkfilter.py:41
#, fuzzy
msgid "United Arab Emirates"
msgstr "Förenade Arabemiraten"

#: pynicotine/networkfilter.py:42
#, fuzzy
msgid "Afghanistan"
msgstr "Afghanistan"

#: pynicotine/networkfilter.py:43
#, fuzzy
msgid "Antigua & Barbuda"
msgstr "Antigua och Barbuda"

#: pynicotine/networkfilter.py:44
#, fuzzy
msgid "Anguilla"
msgstr "Anguilla"

#: pynicotine/networkfilter.py:45
#, fuzzy
msgid "Albania"
msgstr "Albanien"

#: pynicotine/networkfilter.py:46
#, fuzzy
msgid "Armenia"
msgstr "Armenien"

#: pynicotine/networkfilter.py:47
#, fuzzy
msgid "Angola"
msgstr "Angola"

#: pynicotine/networkfilter.py:48
#, fuzzy
msgid "Antarctica"
msgstr "Antarktis"

#: pynicotine/networkfilter.py:49
#, fuzzy
msgid "Argentina"
msgstr "Argentina"

#: pynicotine/networkfilter.py:50
#, fuzzy
msgid "American Samoa"
msgstr "Amerikanska Samoa"

#: pynicotine/networkfilter.py:51
#, fuzzy
msgid "Austria"
msgstr "Österrike"

#: pynicotine/networkfilter.py:52
#, fuzzy
msgid "Australia"
msgstr "Australien"

#: pynicotine/networkfilter.py:53
#, fuzzy
msgid "Aruba"
msgstr "Aruba"

#: pynicotine/networkfilter.py:54
#, fuzzy
msgid "Åland Islands"
msgstr "Åland"

#: pynicotine/networkfilter.py:55
#, fuzzy
msgid "Azerbaijan"
msgstr "Azerbajdzjan"

#: pynicotine/networkfilter.py:56
#, fuzzy
msgid "Bosnia & Herzegovina"
msgstr "Bosnien och Hercegovina"

#: pynicotine/networkfilter.py:57
#, fuzzy
msgid "Barbados"
msgstr "Barbados"

#: pynicotine/networkfilter.py:58
#, fuzzy
msgid "Bangladesh"
msgstr "Bangladesh"

#: pynicotine/networkfilter.py:59
#, fuzzy
msgid "Belgium"
msgstr "Belgien"

#: pynicotine/networkfilter.py:60
#, fuzzy
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: pynicotine/networkfilter.py:61
#, fuzzy
msgid "Bulgaria"
msgstr "Bulgarien"

#: pynicotine/networkfilter.py:62
#, fuzzy
msgid "Bahrain"
msgstr "Bahrain"

#: pynicotine/networkfilter.py:63
#, fuzzy
msgid "Burundi"
msgstr "Burundi"

#: pynicotine/networkfilter.py:64
#, fuzzy
msgid "Benin"
msgstr "Benin"

#: pynicotine/networkfilter.py:65
#, fuzzy
msgid "Saint Barthelemy"
msgstr "Saint Barthelemy"

#: pynicotine/networkfilter.py:66
#, fuzzy
msgid "Bermuda"
msgstr "Bermuda"

#: pynicotine/networkfilter.py:67
#, fuzzy
msgid "Brunei Darussalam"
msgstr "Brunei"

#: pynicotine/networkfilter.py:68
#, fuzzy
msgid "Bolivia"
msgstr "Bolivien"

#: pynicotine/networkfilter.py:69
#, fuzzy
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius och Saba"

#: pynicotine/networkfilter.py:70
#, fuzzy
msgid "Brazil"
msgstr "Brasilien"

#: pynicotine/networkfilter.py:71
#, fuzzy
msgid "Bahamas"
msgstr "Bahamas"

#: pynicotine/networkfilter.py:72
#, fuzzy
msgid "Bhutan"
msgstr "Bhutan"

#: pynicotine/networkfilter.py:73
#, fuzzy
msgid "Bouvet Island"
msgstr "Bouvetön"

#: pynicotine/networkfilter.py:74
#, fuzzy
msgid "Botswana"
msgstr "Botswana"

#: pynicotine/networkfilter.py:75
#, fuzzy
msgid "Belarus"
msgstr "Vitryssland"

#: pynicotine/networkfilter.py:76
#, fuzzy
msgid "Belize"
msgstr "Belize"

#: pynicotine/networkfilter.py:77
#, fuzzy
msgid "Canada"
msgstr "Kanada"

#: pynicotine/networkfilter.py:78
#, fuzzy
msgid "Cocos (Keeling) Islands"
msgstr "Kokosöarna"

#: pynicotine/networkfilter.py:79
#, fuzzy
msgid "Democratic Republic of Congo"
msgstr "Demokratiska republiken Kongo"

#: pynicotine/networkfilter.py:80
#, fuzzy
msgid "Central African Republic"
msgstr "Centralafrikanska republiken"

#: pynicotine/networkfilter.py:81
#, fuzzy
msgid "Congo"
msgstr "Kongo"

#: pynicotine/networkfilter.py:82
#, fuzzy
msgid "Switzerland"
msgstr "Schweiz"

#: pynicotine/networkfilter.py:83
#, fuzzy
msgid "Ivory Coast"
msgstr "Elfenbenskusten"

#: pynicotine/networkfilter.py:84
#, fuzzy
msgid "Cook Islands"
msgstr "Cooköarna"

#: pynicotine/networkfilter.py:85
#, fuzzy
msgid "Chile"
msgstr "Chile"

#: pynicotine/networkfilter.py:86
#, fuzzy
msgid "Cameroon"
msgstr "Kamerun"

#: pynicotine/networkfilter.py:87
#, fuzzy
msgid "China"
msgstr "Kina"

#: pynicotine/networkfilter.py:88
#, fuzzy
msgid "Colombia"
msgstr "Colombia"

#: pynicotine/networkfilter.py:89
#, fuzzy
msgid "Costa Rica"
msgstr "Costa Rica"

#: pynicotine/networkfilter.py:90
#, fuzzy
msgid "Cuba"
msgstr "Kuba"

#: pynicotine/networkfilter.py:91
#, fuzzy
msgid "Cabo Verde"
msgstr "Cabo Verde"

#: pynicotine/networkfilter.py:92
#, fuzzy
msgid "Curaçao"
msgstr "Curaçao"

#: pynicotine/networkfilter.py:93
#, fuzzy
msgid "Christmas Island"
msgstr "Julön"

#: pynicotine/networkfilter.py:94
#, fuzzy
msgid "Cyprus"
msgstr "Cypern"

#: pynicotine/networkfilter.py:95
#, fuzzy
msgid "Czechia"
msgstr "Tjeckien"

#: pynicotine/networkfilter.py:96
#, fuzzy
msgid "Germany"
msgstr "Tyskland"

#: pynicotine/networkfilter.py:97
#, fuzzy
msgid "Djibouti"
msgstr "Djibouti"

#: pynicotine/networkfilter.py:98
#, fuzzy
msgid "Denmark"
msgstr "Danmark"

#: pynicotine/networkfilter.py:99
#, fuzzy
msgid "Dominica"
msgstr "Dominique"

#: pynicotine/networkfilter.py:100
#, fuzzy
msgid "Dominican Republic"
msgstr "Dominikanska Republiken"

#: pynicotine/networkfilter.py:101
#, fuzzy
msgid "Algeria"
msgstr "Algeriet"

#: pynicotine/networkfilter.py:102
#, fuzzy
msgid "Ecuador"
msgstr "Equador"

#: pynicotine/networkfilter.py:103
#, fuzzy
msgid "Estonia"
msgstr "Estland"

#: pynicotine/networkfilter.py:104
#, fuzzy
msgid "Egypt"
msgstr "Egypten"

#: pynicotine/networkfilter.py:105
#, fuzzy
msgid "Western Sahara"
msgstr "Västsahara"

#: pynicotine/networkfilter.py:106
#, fuzzy
msgid "Eritrea"
msgstr "Eritrea"

#: pynicotine/networkfilter.py:107
#, fuzzy
msgid "Spain"
msgstr "Spanien"

#: pynicotine/networkfilter.py:108
#, fuzzy
msgid "Ethiopia"
msgstr "Etiopien"

#: pynicotine/networkfilter.py:109
#, fuzzy
msgid "Europe"
msgstr "Europa"

#: pynicotine/networkfilter.py:110
#, fuzzy
msgid "Finland"
msgstr "Finland"

#: pynicotine/networkfilter.py:111
#, fuzzy
msgid "Fiji"
msgstr "Fiji"

#: pynicotine/networkfilter.py:112
#, fuzzy
msgid "Falkland Islands (Malvinas)"
msgstr "Falklandsöarna (Malvinas)"

#: pynicotine/networkfilter.py:113
#, fuzzy
msgid "Micronesia"
msgstr "Mikronesien"

#: pynicotine/networkfilter.py:114
#, fuzzy
msgid "Faroe Islands"
msgstr "Färöarna"

#: pynicotine/networkfilter.py:115
#, fuzzy
msgid "France"
msgstr "Frankrike"

#: pynicotine/networkfilter.py:116
#, fuzzy
msgid "Gabon"
msgstr "Gabon"

#: pynicotine/networkfilter.py:117
#, fuzzy
msgid "Great Britain"
msgstr "Storbritannien"

#: pynicotine/networkfilter.py:118
#, fuzzy
msgid "Grenada"
msgstr "Grenada"

#: pynicotine/networkfilter.py:119
#, fuzzy
msgid "Georgia"
msgstr "Georgien"

#: pynicotine/networkfilter.py:120
#, fuzzy
msgid "French Guiana"
msgstr "Franska Guyana"

#: pynicotine/networkfilter.py:121
#, fuzzy
msgid "Guernsey"
msgstr "Guernsey"

#: pynicotine/networkfilter.py:122
#, fuzzy
msgid "Ghana"
msgstr "Ghana"

#: pynicotine/networkfilter.py:123
#, fuzzy
msgid "Gibraltar"
msgstr "Gibraltar"

#: pynicotine/networkfilter.py:124
#, fuzzy
msgid "Greenland"
msgstr "Grönland"

#: pynicotine/networkfilter.py:125
#, fuzzy
msgid "Gambia"
msgstr "Gambia"

#: pynicotine/networkfilter.py:126
#, fuzzy
msgid "Guinea"
msgstr "Guinea"

#: pynicotine/networkfilter.py:127
#, fuzzy
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: pynicotine/networkfilter.py:128
#, fuzzy
msgid "Equatorial Guinea"
msgstr "Ekvatorialguinea"

#: pynicotine/networkfilter.py:129
#, fuzzy
msgid "Greece"
msgstr "Grekland"

#: pynicotine/networkfilter.py:130
#, fuzzy
msgid "South Georgia & South Sandwich Islands"
msgstr "Sydgeorgien och södra Sandwichöarna"

#: pynicotine/networkfilter.py:131
#, fuzzy
msgid "Guatemala"
msgstr "Guatemala"

#: pynicotine/networkfilter.py:132
#, fuzzy
msgid "Guam"
msgstr "Guam"

#: pynicotine/networkfilter.py:133
#, fuzzy
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: pynicotine/networkfilter.py:134
#, fuzzy
msgid "Guyana"
msgstr "Guyana"

#: pynicotine/networkfilter.py:135
#, fuzzy
msgid "Hong Kong"
msgstr "Hong Kong"

#: pynicotine/networkfilter.py:136
#, fuzzy
msgid "Heard & McDonald Islands"
msgstr "Heard- och McDonaldöarna"

#: pynicotine/networkfilter.py:137
#, fuzzy
msgid "Honduras"
msgstr "Honduras"

#: pynicotine/networkfilter.py:138
#, fuzzy
msgid "Croatia"
msgstr "Kroatien"

#: pynicotine/networkfilter.py:139
#, fuzzy
msgid "Haiti"
msgstr "Haiti"

#: pynicotine/networkfilter.py:140
#, fuzzy
msgid "Hungary"
msgstr "Ungern"

#: pynicotine/networkfilter.py:141
#, fuzzy
msgid "Indonesia"
msgstr "Indonesien"

#: pynicotine/networkfilter.py:142
#, fuzzy
msgid "Ireland"
msgstr "Irland"

#: pynicotine/networkfilter.py:143
#, fuzzy
msgid "Israel"
msgstr "Israel"

#: pynicotine/networkfilter.py:144
#, fuzzy
msgid "Isle of Man"
msgstr "Isle of Man"

#: pynicotine/networkfilter.py:145
#, fuzzy
msgid "India"
msgstr "Indien"

#: pynicotine/networkfilter.py:146
#, fuzzy
msgid "British Indian Ocean Territory"
msgstr "Brittiska territoriet i Indiska oceanen"

#: pynicotine/networkfilter.py:147
#, fuzzy
msgid "Iraq"
msgstr "Irak"

#: pynicotine/networkfilter.py:148
#, fuzzy
msgid "Iran"
msgstr "Iran"

#: pynicotine/networkfilter.py:149
#, fuzzy
msgid "Iceland"
msgstr "Island"

#: pynicotine/networkfilter.py:150
#, fuzzy
msgid "Italy"
msgstr "Italien"

#: pynicotine/networkfilter.py:151
#, fuzzy
msgid "Jersey"
msgstr "Jersey"

#: pynicotine/networkfilter.py:152
#, fuzzy
msgid "Jamaica"
msgstr "Jamaica"

#: pynicotine/networkfilter.py:153
#, fuzzy
msgid "Jordan"
msgstr "Jordanien"

#: pynicotine/networkfilter.py:154
#, fuzzy
msgid "Japan"
msgstr "Japan"

#: pynicotine/networkfilter.py:155
#, fuzzy
msgid "Kenya"
msgstr "Kenya"

#: pynicotine/networkfilter.py:156
#, fuzzy
msgid "Kyrgyzstan"
msgstr "Kirgizistan"

#: pynicotine/networkfilter.py:157
#, fuzzy
msgid "Cambodia"
msgstr "Kambodja"

#: pynicotine/networkfilter.py:158
#, fuzzy
msgid "Kiribati"
msgstr "Kiribati"

#: pynicotine/networkfilter.py:159
#, fuzzy
msgid "Comoros"
msgstr "Komorerna"

#: pynicotine/networkfilter.py:160
#, fuzzy
msgid "Saint Kitts & Nevis"
msgstr "Saint Kitts och Nevis"

#: pynicotine/networkfilter.py:161
#, fuzzy
msgid "North Korea"
msgstr "Nordkorea"

#: pynicotine/networkfilter.py:162
#, fuzzy
msgid "South Korea"
msgstr "Sydkorea"

#: pynicotine/networkfilter.py:163
#, fuzzy
msgid "Kuwait"
msgstr "Kuwait"

#: pynicotine/networkfilter.py:164
#, fuzzy
msgid "Cayman Islands"
msgstr "Caymanöarna"

#: pynicotine/networkfilter.py:165
#, fuzzy
msgid "Kazakhstan"
msgstr "Kazakstan"

#: pynicotine/networkfilter.py:166
#, fuzzy
msgid "Laos"
msgstr "Laos"

#: pynicotine/networkfilter.py:167
#, fuzzy
msgid "Lebanon"
msgstr "Libanon"

#: pynicotine/networkfilter.py:168
#, fuzzy
msgid "Saint Lucia"
msgstr "St Lucia"

#: pynicotine/networkfilter.py:169
#, fuzzy
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: pynicotine/networkfilter.py:170
#, fuzzy
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: pynicotine/networkfilter.py:171
#, fuzzy
msgid "Liberia"
msgstr "Liberia"

#: pynicotine/networkfilter.py:172
#, fuzzy
msgid "Lesotho"
msgstr "Lesotho"

#: pynicotine/networkfilter.py:173
#, fuzzy
msgid "Lithuania"
msgstr "Litauen"

#: pynicotine/networkfilter.py:174
#, fuzzy
msgid "Luxembourg"
msgstr "Luxemburg"

#: pynicotine/networkfilter.py:175
#, fuzzy
msgid "Latvia"
msgstr "Lettland"

#: pynicotine/networkfilter.py:176
#, fuzzy
msgid "Libya"
msgstr "Libyen"

#: pynicotine/networkfilter.py:177
#, fuzzy
msgid "Morocco"
msgstr "Marocko"

#: pynicotine/networkfilter.py:178
#, fuzzy
msgid "Monaco"
msgstr "Monaco"

#: pynicotine/networkfilter.py:179
#, fuzzy
msgid "Moldova"
msgstr "Moldavien"

#: pynicotine/networkfilter.py:180
#, fuzzy
msgid "Montenegro"
msgstr "Montenegro"

#: pynicotine/networkfilter.py:181
#, fuzzy
msgid "Saint Martin"
msgstr "Saint Martin"

#: pynicotine/networkfilter.py:182
#, fuzzy
msgid "Madagascar"
msgstr "Madagaskar"

#: pynicotine/networkfilter.py:183
#, fuzzy
msgid "Marshall Islands"
msgstr "Marshallöarna"

#: pynicotine/networkfilter.py:184
#, fuzzy
msgid "North Macedonia"
msgstr "Nordmakedonien"

#: pynicotine/networkfilter.py:185
#, fuzzy
msgid "Mali"
msgstr "Mali"

#: pynicotine/networkfilter.py:186
#, fuzzy
msgid "Myanmar"
msgstr "Myanmar (Burma)"

#: pynicotine/networkfilter.py:187
#, fuzzy
msgid "Mongolia"
msgstr "Mongoliet"

#: pynicotine/networkfilter.py:188
#, fuzzy
msgid "Macau"
msgstr "Macao"

#: pynicotine/networkfilter.py:189
#, fuzzy
msgid "Northern Mariana Islands"
msgstr "Norra Marianeröarna"

#: pynicotine/networkfilter.py:190
#, fuzzy
msgid "Martinique"
msgstr "Martinique"

#: pynicotine/networkfilter.py:191
#, fuzzy
msgid "Mauritania"
msgstr "Mauretanien"

#: pynicotine/networkfilter.py:192
#, fuzzy
msgid "Montserrat"
msgstr "Montserrat"

#: pynicotine/networkfilter.py:193
#, fuzzy
msgid "Malta"
msgstr "Malta"

#: pynicotine/networkfilter.py:194
#, fuzzy
msgid "Mauritius"
msgstr "Mauritius"

#: pynicotine/networkfilter.py:195
#, fuzzy
msgid "Maldives"
msgstr "Maldiverna"

#: pynicotine/networkfilter.py:196
#, fuzzy
msgid "Malawi"
msgstr "Malawi"

#: pynicotine/networkfilter.py:197
#, fuzzy
msgid "Mexico"
msgstr "Mexiko"

#: pynicotine/networkfilter.py:198
#, fuzzy
msgid "Malaysia"
msgstr "Malaysia"

#: pynicotine/networkfilter.py:199
#, fuzzy
msgid "Mozambique"
msgstr "Moçambique"

#: pynicotine/networkfilter.py:200
#, fuzzy
msgid "Namibia"
msgstr "Namibia"

#: pynicotine/networkfilter.py:201
#, fuzzy
msgid "New Caledonia"
msgstr "Nya Kaledonien"

#: pynicotine/networkfilter.py:202
#, fuzzy
msgid "Niger"
msgstr "Niger"

#: pynicotine/networkfilter.py:203
#, fuzzy
msgid "Norfolk Island"
msgstr "Norfolköarna"

#: pynicotine/networkfilter.py:204
#, fuzzy
msgid "Nigeria"
msgstr "Nigeria"

#: pynicotine/networkfilter.py:205
#, fuzzy
msgid "Nicaragua"
msgstr "Nicaragua"

#: pynicotine/networkfilter.py:206
#, fuzzy
msgid "Netherlands"
msgstr "Nederländerna"

#: pynicotine/networkfilter.py:207
#, fuzzy
msgid "Norway"
msgstr "Norge"

#: pynicotine/networkfilter.py:208
#, fuzzy
msgid "Nepal"
msgstr "Nepal"

#: pynicotine/networkfilter.py:209
#, fuzzy
msgid "Nauru"
msgstr "Nauru"

#: pynicotine/networkfilter.py:210
#, fuzzy
msgid "Niue"
msgstr "Niue"

#: pynicotine/networkfilter.py:211
#, fuzzy
msgid "New Zealand"
msgstr "Nya Zeeland"

#: pynicotine/networkfilter.py:212
#, fuzzy
msgid "Oman"
msgstr "Oman"

#: pynicotine/networkfilter.py:213
#, fuzzy
msgid "Panama"
msgstr "Panama"

#: pynicotine/networkfilter.py:214
#, fuzzy
msgid "Peru"
msgstr "Peru"

#: pynicotine/networkfilter.py:215
#, fuzzy
msgid "French Polynesia"
msgstr "Franska Polynesien"

#: pynicotine/networkfilter.py:216
#, fuzzy
msgid "Papua New Guinea"
msgstr "Papua Nya Guinea"

#: pynicotine/networkfilter.py:217
#, fuzzy
msgid "Philippines"
msgstr "Filippinerna"

#: pynicotine/networkfilter.py:218
#, fuzzy
msgid "Pakistan"
msgstr "Pakistan"

#: pynicotine/networkfilter.py:219
#, fuzzy
msgid "Poland"
msgstr "Polen"

#: pynicotine/networkfilter.py:220
#, fuzzy
msgid "Saint Pierre & Miquelon"
msgstr "Saint Pierre och Miquelon"

#: pynicotine/networkfilter.py:221
#, fuzzy
msgid "Pitcairn"
msgstr "Pitcairn"

#: pynicotine/networkfilter.py:222
#, fuzzy
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: pynicotine/networkfilter.py:223
#, fuzzy
msgid "State of Palestine"
msgstr "Staten Palestina"

#: pynicotine/networkfilter.py:224
#, fuzzy
msgid "Portugal"
msgstr "Portugal"

#: pynicotine/networkfilter.py:225
#, fuzzy
msgid "Palau"
msgstr "Palau"

#: pynicotine/networkfilter.py:226
#, fuzzy
msgid "Paraguay"
msgstr "Paraguay"

#: pynicotine/networkfilter.py:227
#, fuzzy
msgid "Qatar"
msgstr "Qatar"

#: pynicotine/networkfilter.py:228
#, fuzzy
msgid "Réunion"
msgstr "Réunion"

#: pynicotine/networkfilter.py:229
#, fuzzy
msgid "Romania"
msgstr "Rumänien"

#: pynicotine/networkfilter.py:230
#, fuzzy
msgid "Serbia"
msgstr "Serbien"

#: pynicotine/networkfilter.py:231
#, fuzzy
msgid "Russia"
msgstr "Ryssland"

#: pynicotine/networkfilter.py:232
#, fuzzy
msgid "Rwanda"
msgstr "Rwanda"

#: pynicotine/networkfilter.py:233
#, fuzzy
msgid "Saudi Arabia"
msgstr "Saudiarabien"

#: pynicotine/networkfilter.py:234
#, fuzzy
msgid "Solomon Islands"
msgstr "Solomonöarna"

#: pynicotine/networkfilter.py:235
#, fuzzy
msgid "Seychelles"
msgstr "Seychellerna"

#: pynicotine/networkfilter.py:236
#, fuzzy
msgid "Sudan"
msgstr "Sudan"

#: pynicotine/networkfilter.py:237
#, fuzzy
msgid "Sweden"
msgstr "Sverige"

#: pynicotine/networkfilter.py:238
#, fuzzy
msgid "Singapore"
msgstr "Singapore"

#: pynicotine/networkfilter.py:239
#, fuzzy
msgid "Saint Helena"
msgstr "St. Helena"

#: pynicotine/networkfilter.py:240
#, fuzzy
msgid "Slovenia"
msgstr "Slovenien"

#: pynicotine/networkfilter.py:241
#, fuzzy
msgid "Svalbard & Jan Mayen Islands"
msgstr "Svalbard och Jan Mayen-öarna"

#: pynicotine/networkfilter.py:242
#, fuzzy
msgid "Slovak Republic"
msgstr "Slovakien"

#: pynicotine/networkfilter.py:243
#, fuzzy
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: pynicotine/networkfilter.py:244
#, fuzzy
msgid "San Marino"
msgstr "San Marino"

#: pynicotine/networkfilter.py:245
#, fuzzy
msgid "Senegal"
msgstr "Senegal"

#: pynicotine/networkfilter.py:246
#, fuzzy
msgid "Somalia"
msgstr "Somalia"

#: pynicotine/networkfilter.py:247
#, fuzzy
msgid "Suriname"
msgstr "Surinam"

#: pynicotine/networkfilter.py:248
#, fuzzy
msgid "South Sudan"
msgstr "Sydsudan"

#: pynicotine/networkfilter.py:249
#, fuzzy
msgid "Sao Tome & Principe"
msgstr "Sao Tome och Principe"

#: pynicotine/networkfilter.py:250
#, fuzzy
msgid "El Salvador"
msgstr "El Salvador"

#: pynicotine/networkfilter.py:251
#, fuzzy
msgid "Sint Maarten"
msgstr "Sint Maarten"

#: pynicotine/networkfilter.py:252
#, fuzzy
msgid "Syria"
msgstr "Syrien"

#: pynicotine/networkfilter.py:253
#, fuzzy
msgid "Eswatini"
msgstr "Eswatini"

#: pynicotine/networkfilter.py:254
#, fuzzy
msgid "Turks & Caicos Islands"
msgstr "Turks- och Caicosöarna"

#: pynicotine/networkfilter.py:255
#, fuzzy
msgid "Chad"
msgstr "Tchad"

#: pynicotine/networkfilter.py:256
#, fuzzy
msgid "French Southern Territories"
msgstr "Franska Sydterritorierna"

#: pynicotine/networkfilter.py:257
#, fuzzy
msgid "Togo"
msgstr "Togo"

#: pynicotine/networkfilter.py:258
#, fuzzy
msgid "Thailand"
msgstr "Thailand"

#: pynicotine/networkfilter.py:259
#, fuzzy
msgid "Tajikistan"
msgstr "Tadzjikistan"

#: pynicotine/networkfilter.py:260
#, fuzzy
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
#, fuzzy
msgid "Timor-Leste"
msgstr "Östtimor"

#: pynicotine/networkfilter.py:262
#, fuzzy
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: pynicotine/networkfilter.py:263
#, fuzzy
msgid "Tunisia"
msgstr "Tunisien"

#: pynicotine/networkfilter.py:264
#, fuzzy
msgid "Tonga"
msgstr "Tonga"

#: pynicotine/networkfilter.py:265
#, fuzzy
msgid "Türkiye"
msgstr "Turkiet"

#: pynicotine/networkfilter.py:266
#, fuzzy
msgid "Trinidad & Tobago"
msgstr "Trinidad och Tobago"

#: pynicotine/networkfilter.py:267
#, fuzzy
msgid "Tuvalu"
msgstr "Tuvalu"

#: pynicotine/networkfilter.py:268
#, fuzzy
msgid "Taiwan"
msgstr "Taiwan"

#: pynicotine/networkfilter.py:269
#, fuzzy
msgid "Tanzania"
msgstr "Tanzania"

#: pynicotine/networkfilter.py:270
#, fuzzy
msgid "Ukraine"
msgstr "Ukraina"

#: pynicotine/networkfilter.py:271
#, fuzzy
msgid "Uganda"
msgstr "Uganda"

#: pynicotine/networkfilter.py:272
#, fuzzy
msgid "U.S. Minor Outlying Islands"
msgstr "USA:s mindre avlägset belägna öar"

#: pynicotine/networkfilter.py:273
#, fuzzy
msgid "United States"
msgstr "USA"

#: pynicotine/networkfilter.py:274
#, fuzzy
msgid "Uruguay"
msgstr "Uruguay"

#: pynicotine/networkfilter.py:275
#, fuzzy
msgid "Uzbekistan"
msgstr "Uzbekistan"

#: pynicotine/networkfilter.py:276
#, fuzzy
msgid "Holy See (Vatican City State)"
msgstr "Heliga stolen (Vatikanstaten)"

#: pynicotine/networkfilter.py:277
#, fuzzy
msgid "Saint Vincent & The Grenadines"
msgstr "Saint Vincent och Grenadinerna"

#: pynicotine/networkfilter.py:278
#, fuzzy
msgid "Venezuela"
msgstr "Venezuela"

#: pynicotine/networkfilter.py:279
#, fuzzy
msgid "British Virgin Islands"
msgstr "Brittiska Jungfruöarna"

#: pynicotine/networkfilter.py:280
#, fuzzy
msgid "U.S. Virgin Islands"
msgstr "Amerikanska Jungfruöarna"

#: pynicotine/networkfilter.py:281
#, fuzzy
msgid "Viet Nam"
msgstr "Vietnam"

#: pynicotine/networkfilter.py:282
#, fuzzy
msgid "Vanuatu"
msgstr "Vanuatu"

#: pynicotine/networkfilter.py:283
#, fuzzy
msgid "Wallis & Futuna"
msgstr "Wallis och Futuna"

#: pynicotine/networkfilter.py:284
#, fuzzy
msgid "Samoa"
msgstr "Samoa"

#: pynicotine/networkfilter.py:285
#, fuzzy
msgid "Yemen"
msgstr "Jemen"

#: pynicotine/networkfilter.py:286
#, fuzzy
msgid "Mayotte"
msgstr "Mayotte"

#: pynicotine/networkfilter.py:287
#, fuzzy
msgid "South Africa"
msgstr "Sydafrika"

#: pynicotine/networkfilter.py:288
#, fuzzy
msgid "Zambia"
msgstr "Zambia"

#: pynicotine/networkfilter.py:289
#, fuzzy
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, fuzzy, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "Text-till-tal för meddelandet misslyckades: %s"

#: pynicotine/nowplaying.py:130
#, fuzzy
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr "Last.fm: Ange både ditt användarnamn och din API-nyckel för Last.fm."

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
#, fuzzy
msgid "Now Playing Error"
msgstr "Format som spelas nu"

#: pynicotine/nowplaying.py:140
#, fuzzy, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm: Det gick inte att ansluta till Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:162
#, fuzzy, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr ""
"Last.fm: Det går inte att hämta det senaste spåret från Audioscrobbler: "
"%(error)s"

#: pynicotine/nowplaying.py:196
#, fuzzy
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: Kunde inte hitta en lämplig MPRIS-spelare"

#: pynicotine/nowplaying.py:201
#, fuzzy, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr "Hittade flera MPRIS-spelare: %(players)s. Använder: %(player)s"

#: pynicotine/nowplaying.py:204
#, fuzzy, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Automatisk identifiering av MPRIS-spelare: %s"

#: pynicotine/nowplaying.py:219
#, fuzzy, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr "MPRIS: Något gick fel när du sökte efter %(player)s: %(exception)s"

#: pynicotine/nowplaying.py:266
#, fuzzy
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "LyssnaBrainz: Ange ditt ListenBrainz-användarnamn"

#: pynicotine/nowplaying.py:275
#, fuzzy, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "LyssnaBrainz: Kunde inte ansluta till ListenBrainz: %(error)s"

#: pynicotine/nowplaying.py:283
#, fuzzy
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "LyssnaBrainz: Du verkar inte lyssna på något just nu."

#: pynicotine/nowplaying.py:297
#, fuzzy, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""
"LyssnaBrainz: ListenBrainz: Kunde inte hämta aktuellt spår från "
"ListenBrainz: %(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
#, fuzzy
msgid "Network Filters"
msgstr "Sökningar i nätverket"

#: pynicotine/plugins/core_commands/__init__.py:44
#, fuzzy
msgid "List available commands"
msgstr "Kompletta inbyggda kommandon"

#: pynicotine/plugins/core_commands/__init__.py:49
#, fuzzy
msgid "Connect to the server"
msgstr "Det går inte att ansluta till servern. Orsak: %s"

#: pynicotine/plugins/core_commands/__init__.py:53
#, fuzzy
msgid "Disconnect from the server"
msgstr "Nedkopplad från servern %(host)s:%(port)s"

#: pynicotine/plugins/core_commands/__init__.py:58
#, fuzzy
msgid "Toggle away status"
msgstr "Ändrar din away-status"

#: pynicotine/plugins/core_commands/__init__.py:62
#, fuzzy
msgid "Manage plugins"
msgstr "Aktivera insticksmoduler"

#: pynicotine/plugins/core_commands/__init__.py:74
#, fuzzy
msgid "Clear chat window"
msgstr "Rensa chattfönstret"

#: pynicotine/plugins/core_commands/__init__.py:80
#, fuzzy
msgid "Say something in the third-person"
msgstr "Säg något i tredje person"

#: pynicotine/plugins/core_commands/__init__.py:87
#, fuzzy
msgid "Announce the song currently playing"
msgstr "Tillkännage låten som spelas just nu"

#: pynicotine/plugins/core_commands/__init__.py:94
#, fuzzy
msgid "Join chat room"
msgstr "Gå med eller skapa rum…"

#: pynicotine/plugins/core_commands/__init__.py:102
#, fuzzy
msgid "Leave chat room"
msgstr "Lämna rummet 'rum'"

#: pynicotine/plugins/core_commands/__init__.py:110
#, fuzzy
msgid "Say message in specified chat room"
msgstr "Säg meddelande i angivet chattrum"

#: pynicotine/plugins/core_commands/__init__.py:117
#, fuzzy
msgid "Open private chat"
msgstr "Privat chatt"

#: pynicotine/plugins/core_commands/__init__.py:125
#, fuzzy
msgid "Close private chat"
msgstr "Stäng den privata chatten"

#: pynicotine/plugins/core_commands/__init__.py:133
#, fuzzy
msgid "Request user's client version"
msgstr "Begär användarinfo från användaren 'användare'"

#: pynicotine/plugins/core_commands/__init__.py:142
#, fuzzy
msgid "Send private message to user"
msgstr "Skicka privat meddelande till alla onlinekompisar:"

#: pynicotine/plugins/core_commands/__init__.py:150
#, fuzzy
msgid "Add user to buddy list"
msgstr "Lägg användaren 'användare' till din ban-lista"

#: pynicotine/plugins/core_commands/__init__.py:158
#, fuzzy
msgid "Remove buddy from buddy list"
msgstr "Ta bort användaren 'användare' från din ban-lista"

#: pynicotine/plugins/core_commands/__init__.py:166
#, fuzzy
msgid "Browse files of user"
msgstr "Bläddra filer hos användaren 'användare'"

#: pynicotine/plugins/core_commands/__init__.py:175
#, fuzzy
msgid "Show user profile information"
msgstr "Användari_nfo"

#: pynicotine/plugins/core_commands/__init__.py:183
#, fuzzy
msgid "Show IP address or username"
msgstr "Visa IP-adress för användaren 'användare'"

#: pynicotine/plugins/core_commands/__init__.py:190
#, fuzzy
msgid "Block connections from user or IP address"
msgstr "Blockera anslutningar från användare eller IP-adress"

#: pynicotine/plugins/core_commands/__init__.py:197
#, fuzzy
msgid "Remove user or IP address from ban lists"
msgstr "Ta bort användaren 'användare' från din ban-lista"

#: pynicotine/plugins/core_commands/__init__.py:204
#, fuzzy
msgid "Silence messages from user or IP address"
msgstr "Tysta meddelanden från användare eller IP-adress"

#: pynicotine/plugins/core_commands/__init__.py:212
#, fuzzy
msgid "Remove user or IP address from ignore lists"
msgstr "Ta bort användaren 'användare' från din ignore-lista"

#: pynicotine/plugins/core_commands/__init__.py:220
#, fuzzy
msgid "Add share"
msgstr "Lägg till Wi_sh"

#: pynicotine/plugins/core_commands/__init__.py:226
#, fuzzy
msgid "Remove share"
msgstr "Ta bort ett alias"

#: pynicotine/plugins/core_commands/__init__.py:233
#, fuzzy
msgid "List shares"
msgstr "Indexera om delade filer"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Indexera om delade filer"

#: pynicotine/plugins/core_commands/__init__.py:246
#, fuzzy
msgid "Start global file search"
msgstr "Starta global filsökning"

#: pynicotine/plugins/core_commands/__init__.py:254
#, fuzzy
msgid "Search files in joined rooms"
msgstr "Sök i filer och mappar (exakt matchning)"

#: pynicotine/plugins/core_commands/__init__.py:262
#, fuzzy
msgid "Search files of all buddies"
msgstr "Sök i filer och mappar (exakt matchning)"

#: pynicotine/plugins/core_commands/__init__.py:270
#, fuzzy
msgid "Search a user's shared files"
msgstr "Sök i en användares delade filer efter 'förfrågan'"

#: pynicotine/plugins/core_commands/__init__.py:296
#, fuzzy, python-format
msgid "Listing %(num)i available commands:"
msgstr "Listar %(num)i tillgängliga kommandon:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, fuzzy, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr "Listar %(num)i tillgängliga kommandon som matchar \"%(query)s\":"

#: pynicotine/plugins/core_commands/__init__.py:311
#, fuzzy, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Skriv %(command)s för att lista liknande kommandon"

#: pynicotine/plugins/core_commands/__init__.py:314
#, fuzzy, python-format
msgid "Type %(command)s to list available commands"
msgstr "Skriv %(command)s för att lista tillgängliga kommandon"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, fuzzy, python-format
msgid "Not joined in room %s"
msgstr "%s anslöt till rummet"

#: pynicotine/plugins/core_commands/__init__.py:404
#, fuzzy, python-format
msgid "Not messaging with user %s"
msgstr "Inte i meddelandeutbyte med användare %s"

#: pynicotine/plugins/core_commands/__init__.py:408
#, fuzzy, python-format
msgid "Closed private chat of user %s"
msgstr "Stäng den privata chatten"

#: pynicotine/plugins/core_commands/__init__.py:476
#, fuzzy, python-format
msgid "Banned %s"
msgstr "Banna"

#: pynicotine/plugins/core_commands/__init__.py:490
#, fuzzy, python-format
msgid "Unbanned %s"
msgstr "Förbjud användare"

#: pynicotine/plugins/core_commands/__init__.py:503
#, fuzzy, python-format
msgid "Ignored %s"
msgstr "Ignorerade användare:"

#: pynicotine/plugins/core_commands/__init__.py:517
#, fuzzy, python-format
msgid "Unignored %s"
msgstr "Ignorera användare"

#: pynicotine/plugins/core_commands/__init__.py:553
#, fuzzy, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr "%(num_listed)s shares listade (%(num_total)s konfigurerade)"

#: pynicotine/plugins/core_commands/__init__.py:565
#, fuzzy, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr "Kan inte dela otillgänglig mapp \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:568
#, fuzzy, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr "Lade till %(group_name)s share \"%(virtual_name)s\" (omskanning krävs)"

#: pynicotine/plugins/core_commands/__init__.py:579
#, fuzzy, python-format
msgid "No share with name \"%s\""
msgstr "Ingen share med namnet \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:582
#, fuzzy, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr "Tog bort share \"%s\" (omskanning krävs)"

#: pynicotine/pluginsystem.py:413
#, fuzzy
msgid "Loading plugin system"
msgstr "Laddat insticksprogram %s"

#: pynicotine/pluginsystem.py:516
#, fuzzy, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Det går inte att ladda plugin %(name)s. Namnet på mappen för "
"insticksprogrammet innehåller ogiltiga tecken: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, fuzzy, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "Motstridiga %(interface)s kommando i plugin %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, fuzzy, python-format
msgid "Loaded plugin %s"
msgstr "Laddat insticksprogram %s"

#: pynicotine/pluginsystem.py:579
#, fuzzy, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Det går inte att ladda plugin %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, fuzzy, python-format
msgid "Unloaded plugin %s"
msgstr "Avladdat insticksprogram %s"

#: pynicotine/pluginsystem.py:648
#, fuzzy, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Det går inte att avlasta insticksprogrammet %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, fuzzy, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Plugin %(module)s misslyckades med fel %(errortype)s: %(error)s.\n"
"Spårning: %(trace)s"

#: pynicotine/pluginsystem.py:810
#, fuzzy
msgid "No description"
msgstr "Beskrivning:"

#: pynicotine/pluginsystem.py:887
#, fuzzy, python-format
msgid "Missing %s argument"
msgstr "Saknar %s-argument"

#: pynicotine/pluginsystem.py:896
#, fuzzy, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Felaktig SoulSeek-meta-url: %s"

#: pynicotine/pluginsystem.py:901
#, fuzzy, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Användning: %(command)s %(args)s"

#: pynicotine/pluginsystem.py:940
#, fuzzy, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Okänt kommando: %(command)s. Skriv %(help_command)s för att lista "
"tillgängliga kommandon."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
#, fuzzy
msgid "No UPnP devices found"
msgstr "Inga UPnP-enheter hittades"

#: pynicotine/portmapper.py:633
#, fuzzy, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"UPnP: Det gick inte att vidarebefordra extern port %(external_port)s: "
"%(error)s"

#: pynicotine/portmapper.py:647
#, fuzzy, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"UPnP: Extern port %(external_port)s vidarebefordras framgångsrikt till lokal "
"IP-adress %(ip_address)s port %(local_port)s"

#: pynicotine/privatechat.py:220
#, fuzzy, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Privat meddelande från %(user)s"

#: pynicotine/search.py:368
#, fuzzy, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Söker efter objektet \"%s\" på önskelistan"

#: pynicotine/search.py:434
#, fuzzy, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Väntetiden för önskelistan är inställd på %s sekunder."

#: pynicotine/search.py:760
#, fuzzy, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr ""
"Användaren %(user)s söker efter \"%(query)s\" och får %(num)i resultat."

#: pynicotine/shares.py:302
#, fuzzy
msgid "Rebuilding shares…"
msgstr "Indexering påbörjad"

#: pynicotine/shares.py:302
#, fuzzy
msgid "Rescanning shares…"
msgstr "Indexering påbörjad"

#: pynicotine/shares.py:324
#, fuzzy, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Omsökning slutförd: %(num)s mappar hittades"

#: pynicotine/shares.py:334
#, fuzzy, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Ett allvarligt fel inträffade vid omskanning av aktier. Om problemet "
"kvarstår tar du bort %(dir)s/*.db och försöker igen. Om det inte hjälper kan "
"du skicka in en felrapport med denna stacktrace inkluderad: %(trace)s"

#: pynicotine/shares.py:582
#, fuzzy, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Fel vid skanning av filen %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, fuzzy, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Fel vid skanning av mapp %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, fuzzy, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "Fel vid skanning av metadata för filen %(path)s: %(error)s"

#: pynicotine/shares.py:1046
#, fuzzy, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Omsökningen avbröts på grund av otillgängliga delningar"

#: pynicotine/shares.py:1184
#, fuzzy, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "Användaren %(user)s bläddrar i din lista över delade filer"

#: pynicotine/slskmessages.py:3120
#, fuzzy, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Det går inte att läsa aktiedatabasen. Vänligen skanna om dina aktier. Fel: %s"

#: pynicotine/slskproto.py:500
#, fuzzy, python-format
msgid "Specified network interface '%s' is not available"
msgstr "Det specificerade nätverksgränssnittet '%s' finns inte"

#: pynicotine/slskproto.py:511
#, fuzzy, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Kan inte lyssna på port %(port)s. Se till att inget annat program använder "
"det, eller välj en annan port. Fel: %(error)s"

#: pynicotine/slskproto.py:523
#, fuzzy, python-format
msgid "Listening on port: %i"
msgstr "Lyssnar på port %i"

#: pynicotine/slskproto.py:805
#, fuzzy, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "Kunde inte ansluta till servern %(host)s:%(port)s: %(error)s"

#: pynicotine/slskproto.py:1095
#, fuzzy, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Automatisk anslutning till servern vid start"

#: pynicotine/slskproto.py:1170
#, fuzzy, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Anslutning till %(host)s:%(port)s"

#: pynicotine/slskproto.py:1208
#, fuzzy, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Ansluten till servern %(host)s:%(port)s, loggar in..."

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Nedkopplad från servern %(host)s:%(port)s"

#: pynicotine/slskproto.py:1499
#, fuzzy
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Någon har loggat in på ditt Soulseek-konto någon annanstans."

#: pynicotine/uploads.py:382
#, fuzzy, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Uppladdning slutförd: användare %(user)s, IP-adress %(ip)s, fil %(file)s"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Uppladdning avbruten: användare %(user)s, fil %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, fuzzy, python-format
msgid "Upload I/O error: %s"
msgstr "Fel vid uppladdning av I/O: %s"

#: pynicotine/uploads.py:1103
#, fuzzy, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Uppladdning påbörjad: användare %(user)s, IP-adress %(ip)s, fil %(file)s"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr "Kunde inte skapa katalogen '%(folder)s', rapporterat fel: %(error)s"

#: pynicotine/userbrowse.py:236
#, fuzzy, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Det gick inte att ladda Shares från disk: %(error)s"

#: pynicotine/userbrowse.py:282
#, fuzzy, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr ""
"Listan över delade filer för användaren \"%(user)s\" sparades till %(dir)s."

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr "Kunde inte skapa katalogen '%(user)s', rapporterat fel: %(error)s"

#: pynicotine/userinfo.py:160
#, fuzzy, python-format
msgid "Picture saved to %s"
msgstr "Bild sparad till %s"

#: pynicotine/userinfo.py:163
#, fuzzy, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Kunde inte skapa katalogen '%(user)s', rapporterat fel: %(error)s"

#: pynicotine/userinfo.py:190
#, fuzzy, python-format
msgid "User %(user)s is viewing your profile"
msgstr "Användaren %(user)s läser din användarinformation"

#: pynicotine/users.py:272
#, fuzzy, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Det går inte att ansluta till servern. Orsak: %s"

#: pynicotine/users.py:272
#, fuzzy
msgid "Cannot Connect"
msgstr "Kunde inte ansluta"

#: pynicotine/users.py:306
#, fuzzy, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr ""
"Det går inte att hämta IP för användaren %s, eftersom användaren är offline."

#: pynicotine/users.py:315
#, fuzzy, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr ""
"IP-adressen för användaren %(user)s är %(ip)s, port %(port)i%(country)s."

#: pynicotine/users.py:433
#, fuzzy
msgid "Soulseek Announcement"
msgstr "Soulseek-klient"

#: pynicotine/users.py:449
#, fuzzy
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"Du har inga privilegier. Privilegier krävs inte, men gör att dina "
"nedladdningar kan köas före icke-priviligierade användare."

#: pynicotine/users.py:455
#, fuzzy, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"%(days)i dagar, %(hours)i timmar, %(minutes)i minuter, %(seconds)i sekunder "
"av nedladdningsrättigheter kvar."

#: pynicotine/users.py:473
#, fuzzy
msgid "Your password has been changed"
msgstr "Ditt lösenord har ändrats. Lösenordet är %s"

#: pynicotine/users.py:473
#, fuzzy
msgid "Password Changed"
msgstr "Lösenordsändring avvisas"

#: pynicotine/utils.py:574
#, fuzzy, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Det går inte att spara filen %(path)s: %(error)s"

#: pynicotine/utils.py:621
#, fuzzy, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "Kunde inte skapa katalogen '%(user)s', rapporterat fel: %(error)s"

#: pynicotine/utils.py:646
#, fuzzy, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr "Något gick fel när du läste filen %(filename)s: %(error)s"

#: pynicotine/utils.py:651
#, fuzzy, python-format
msgid "Attempting to load backup of file %s"
msgstr "Försök att ladda säkerhetskopiering av fil %s"

#: pynicotine/utils.py:673
#, fuzzy, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Det går inte att säkerhetskopiera filen %(path)s: %(error)s"

#: pynicotine/utils.py:694
#, fuzzy, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Det går inte att spara filen %(path)s: %(error)s"

#: pynicotine/utils.py:705
#, fuzzy, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Det går inte att återställa tidigare fil %(path)s: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
#, fuzzy
msgid "Add buddy…"
msgstr "Lägg till kompis…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
#, fuzzy
msgid "Toggle Text-to-Speech"
msgstr "Växla Text-till-Tal"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
#, fuzzy
msgid "Chat Room Command Help"
msgstr "Hjälp med kommandot för chattrum"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
#, fuzzy
msgid "_Log"
msgstr "_Logg"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
#, fuzzy
msgid "Room Wall"
msgstr "Vägg i rummet"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
#, fuzzy
msgid "R_oom Wall"
msgstr "Vägg i rummet"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
#, fuzzy
msgid "Created by"
msgstr "Skapad av"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
#, fuzzy
msgid "Translated by"
msgstr "Översatt av"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
#, fuzzy
msgid "License"
msgstr "Licens"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
#, fuzzy
msgid "Welcome to Nicotine+"
msgstr "Välkommen till Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
#, fuzzy
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Om det önskade användarnamnet redan är upptaget uppmanas du att ändra det."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
#, fuzzy
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""
"För att ansluta till andra Soulseek-peers måste en lyssningsport på din "
"router vidarebefordras till din dator."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
#, fuzzy
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Om din lyssnarport är stängd kan du bara ansluta till användare vars "
"lyssnarportar är öppna."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
#, fuzzy
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"Om det behövs, välj en annan lyssningsport nedan. Detta kan även göras "
"senare i inställningarna."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
#, fuzzy
msgid "Download Files to Folder"
msgstr "Ladda ner filer till en mapp"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
#, fuzzy
msgid "Share Folders"
msgstr "Mappar för aktier"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
#, fuzzy
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Soulseek-användare kommer att kunna ladda ner från dina andelar. Bidra till "
"Soulseek-nätverket genom att dela din egen samling och genom att dela vidare "
"det du laddat ner från andra användare."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
#, fuzzy
msgid "You are ready to use Nicotine+!"
msgstr "Du är redo att använda Nicotine+!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
#, fuzzy
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""
"Soulseek är ett okrypterat protokoll som inte är avsett för säker "
"kommunikation."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
#, fuzzy
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Genom att donera till Soulseek får du privilegier under en viss tidsperiod. "
"Om du har privilegier kommer dina nedladdningar att köas före icke-"
"priviligierade användare."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
#, fuzzy
msgid "Previous File"
msgstr "Föregående fil"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
#, fuzzy
msgid "Next File"
msgstr "Nästa fil"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
#, fuzzy
msgid "Name"
msgstr "Filnamn"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
#, fuzzy
msgid "Last Speed"
msgstr "Senaste hastigheten"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
#, fuzzy
msgid "_Export…"
msgstr "Exportera"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
#, fuzzy
msgid "Keyboard Shortcuts"
msgstr "Keyboard Shortcuts"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
#, fuzzy
msgid "General"
msgstr "Allmänna inställningar"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
#, fuzzy
msgid "Connect"
msgstr "Anslut"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
#, fuzzy
msgid "Disconnect"
msgstr "Koppla bort"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
#, fuzzy
msgid "Rescan Shares"
msgstr "Indexera om delade filer"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
#, fuzzy
msgid "Show Log Pane"
msgstr "Visa loggfönstret"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
#, fuzzy
msgid "Confirm Quit"
msgstr "Konfigurera loggning"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
#, fuzzy
msgid "Quit"
msgstr "Avsluta"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
#, fuzzy
msgid "Menus"
msgstr "Menyer"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
#, fuzzy
msgid "Open Main Menu"
msgstr "Öppna huvudmenyn"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
#, fuzzy
msgid "Open Context Menu"
msgstr "Öppna kontextmenyn"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
#, fuzzy
msgid "Tabs"
msgstr "Flikar"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
#, fuzzy
msgid "Change Main Tab"
msgstr "Ändra huvudfliken"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
#, fuzzy
msgid "Go to Previous Secondary Tab"
msgstr "Gå till föregående sekundär flik"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
#, fuzzy
msgid "Go to Next Secondary Tab"
msgstr "Gå till nästa sekundära flik"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
#, fuzzy
msgid "Reopen Closed Secondary Tab"
msgstr "Stäng sekundär flik"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
#, fuzzy
msgid "Close Secondary Tab"
msgstr "Stäng sekundär flik"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
#, fuzzy
msgid "Lists"
msgstr "Listor"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
#, fuzzy
msgid "Copy Selected Cell"
msgstr "Välj Alla"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
#, fuzzy
msgid "Select All"
msgstr "Välj Alla"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
#, fuzzy
msgid "Find"
msgstr "Hitta"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
#, fuzzy
msgid "Remove Selected Row"
msgstr "Ta bort vald rad"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
#, fuzzy
msgid "Editing"
msgstr "Betyg"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
#, fuzzy
msgid "Cut"
msgstr "Klipp"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
#, fuzzy
msgid "Paste"
msgstr "Klistra in"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
#, fuzzy
msgid "Insert Emoji"
msgstr "Infoga en emoji"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
#, fuzzy
msgid "File Transfers"
msgstr "Överföringar"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
#, fuzzy
msgid "Resume / Retry Transfer"
msgstr "Återuppta/försöka överföring"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
#, fuzzy
msgid "Pause / Abort Transfer"
msgstr "Pausa/avbryta överföringen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
#, fuzzy
msgid "Download / Upload To"
msgstr "Fel på nedladdningskatalog"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
#, fuzzy
msgid "Save List to Disk"
msgstr "_Spara listan över delningar på disk"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
#, fuzzy
msgid "Refresh"
msgstr "Uppdatera filer."

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
#, fuzzy
msgid "Expand / Collapse All"
msgstr "Expandera / kollapsa alla"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
#, fuzzy
msgid "Back to Parent Folder"
msgstr "Det går inte att dela mapp"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
#, fuzzy
msgid "File Search"
msgstr "Sök"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
#, fuzzy
msgid "Result Filters"
msgstr "_Resultatfilter"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
#, fuzzy
msgid "Current Session"
msgstr "Nuvarande sammanträde"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
#, fuzzy
msgid "Completed Downloads"
msgstr "Färdiga nedladdningar"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
#, fuzzy
msgid "Downloaded Size"
msgstr "Nedladdad storlek"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
#, fuzzy
msgid "Completed Uploads"
msgstr "Fullbordade uppladdningar"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
#, fuzzy
msgid "Uploaded Size"
msgstr "Uppladdad storlek"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
#, fuzzy
msgid "Total"
msgstr "Totalt"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
#, fuzzy
msgid "_Reset…"
msgstr "_Resumé"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
#, fuzzy
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Objekt på önskelistan genomsöks automatiskt med jämna mellanrum för att "
"upptäcka ovanliga filer."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
#, fuzzy
msgid "Add Wish…"
msgstr "Lägg till önskemål…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
#, fuzzy
msgid "Clear All…"
msgstr "Rensa alla…"

#: pynicotine/gtkgui/ui/downloads.ui:138
#, fuzzy
msgid "Clear All Finished/Filtered Downloads"
msgstr "Fil nerladdad"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
#, fuzzy
msgid "Clear Finished"
msgstr "Klar"

#: pynicotine/gtkgui/ui/downloads.ui:169
#, fuzzy
msgid "Clear Specific Downloads"
msgstr "Rensa alla nedladdningar"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
#, fuzzy
msgid "Clear _All…"
msgstr "Rensa alla…"

#: pynicotine/gtkgui/ui/interests.ui:21
#, fuzzy
msgid "Personal Interests"
msgstr "Intressen"

#: pynicotine/gtkgui/ui/interests.ui:40
#, fuzzy
msgid "Add something you like…"
msgstr "Lägg till något du gillar…"

#: pynicotine/gtkgui/ui/interests.ui:62
#, fuzzy
msgid "Personal Dislikes"
msgstr "Intressen"

#: pynicotine/gtkgui/ui/interests.ui:80
#, fuzzy
msgid "Add something you dislike…"
msgstr "Lägg till något som du inte gillar…"

#: pynicotine/gtkgui/ui/interests.ui:143
#, fuzzy
msgid "Refresh Recommendations"
msgstr "Rekommendationer"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
#, fuzzy
msgid "Main Menu"
msgstr "Öppna huvudmenyn"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
#, fuzzy
msgid "Room…"
msgstr "Rum"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
#, fuzzy
msgid "Username…"
msgstr "Användarnamn:"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
#, fuzzy
msgid "Search term…"
msgstr "Sökningar"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Rensa"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
#, fuzzy
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr "Sökmönster: med ett ord = term, utan ord = -term, delvis ord = *erm"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
#, fuzzy
msgid "Search Scope"
msgstr "Sökning av omfattning"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
#, fuzzy
msgid "_Wishlist"
msgstr "_Vänslista"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
#, fuzzy
msgid "Configure Searches"
msgstr "Konfigurera sökningar"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
#, fuzzy
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Ange en sökterm för att söka efter filer som delas av andra användare i "
"Soulseek-nätverket."

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
#, fuzzy
msgid "File Grouping Mode"
msgstr "Grupperingsläge för filer"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
#, fuzzy
msgid "Configure Downloads"
msgstr "Konfigurera nedladdningar"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
#, fuzzy
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Filer som du laddar ner från andra användare köas här och kan pausas och "
"återupptas på begäran."

#: pynicotine/gtkgui/ui/mainwindow.ui:643
#, fuzzy
msgid "Configure Uploads"
msgstr "Konfigurera uppladdningar"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
#, fuzzy
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Användarnas försök att ladda ner dina delade filer köas och hanteras här."

#: pynicotine/gtkgui/ui/mainwindow.ui:788
#, fuzzy
msgid "_Open List"
msgstr "_Öppen lista"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
#, fuzzy
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Öppnar en lokal lista över delade filer som tidigare har sparats på disk."

#: pynicotine/gtkgui/ui/mainwindow.ui:811
#, fuzzy
msgid "Configure Shares"
msgstr "_Konfigurera andelar"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
#, fuzzy
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Ange namnet på en användare vars delade filer du vill bläddra bland. Du kan "
"också spara listan på disk och granska den senare."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
#, fuzzy
msgid "_Personal Profile"
msgstr "Intressen"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
#, fuzzy
msgid "Configure Account"
msgstr "Konfigurera loggning"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
#, fuzzy
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Ange en användares namn för att visa dennes användarbeskrivning, information "
"och personliga bild."

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
#, fuzzy
msgid "Chat _History"
msgstr "Chat-historik"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
#, fuzzy
msgid "Configure Chats"
msgstr "Konfigurera andelar"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
#, fuzzy
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr ""
"Ange namnet på en användare för att starta en privat textsamtal med honom "
"eller henne."

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
#, fuzzy
msgid "_Message All"
msgstr "Meddelanden"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
#, fuzzy
msgid "Configure Ignored Users"
msgstr "Ignorerade användare:"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
#, fuzzy
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Lägg till användare i din kompislista för att dela specifika mappar med dem "
"och få meddelanden när de är online."

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
#, fuzzy
msgid "Join or create room…"
msgstr "Gå med eller skapa rum…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
#, fuzzy
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Gå med i ett befintligt chattrum eller skapa ett nytt rum för att chatta med "
"andra användare i Soulseek-nätverket."

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
#, fuzzy
msgid "Configure User Profile"
msgstr "Visa användarprofil"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
#, fuzzy
msgid "Connections"
msgstr "Anslutningar"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
#, fuzzy
msgid "Downloading (Speed / Active Users)"
msgstr "Nedladdning (hastighet/aktiva användare)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
#, fuzzy
msgid "Uploading (Speed / Active Users)"
msgstr "Uppladdning (hastighet/aktiva användare)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
#, fuzzy
msgid "Search chat history…"
msgstr "Sökningar"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
#, fuzzy
msgid "Download Speed Limits"
msgstr "Nedladdningar"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
#, fuzzy
msgid "Unlimited download speed"
msgstr "Begränsa uppladdningshastigheten:"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
#, fuzzy
msgid "Use download speed limit (KiB/s):"
msgstr "Alternativ hastighetsgräns för nedladdning (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
#, fuzzy
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Alternativ hastighetsgräns för nedladdning (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
#, fuzzy
msgid "Search rooms…"
msgstr "Sökningar"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
#, fuzzy
msgid "Refresh Rooms"
msgstr "Uppdatera listan över rum"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
#, fuzzy
msgid "_Show feed of public chat room messages"
msgstr "_Visa flöde av meddelanden från offentliga chattrum"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
#, fuzzy
msgid "_Accept private room invitations"
msgstr "_Acceptera inbjudningar till privata rum"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
#, fuzzy
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Med hjälp av rumsväggen kan användare i ett rum ange ett unikt meddelande "
"som ska visas för andra. De senaste meddelandena visas överst."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
#, fuzzy
msgid "Set wall message…"
msgstr "Ange ett meddelande på väggen…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
#, fuzzy
msgid "Search Result Filters"
msgstr "Filter för sökresultat"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
#, fuzzy
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Filter för sökresultat används för att förfina vilka sökresultat som visas."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
#, fuzzy
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Varje lista med sökresultat har sitt eget filter som kan visas genom att "
"växla mellan knappen Resultatfilter. Ett filter består av flera fält, som "
"alla tillämpas när du trycker på Enter i något av fälten. Filtreringen "
"tillämpas omedelbart på redan erhållna resultat och även på de resultat som "
"ännu inte har kommit in. Om du vill se de fullständiga resultaten igen, "
"rensar du helt enkelt filtret från alla termer och tillämpar det på nytt. "
"Som namnet antyder kan ett filter för sökresultat inte utvidga din "
"ursprungliga sökning, utan endast begränsa den. Om du vill bredda eller "
"ändra dina söktermer, gör en ny sökning."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
#, fuzzy
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Som namnet antyder kan ett sökresultatfilter inte utöka din ursprungliga "
"sökning, det kan bara begränsa den. För att bredda eller ändra dina "
"söktermer, gör en ny sökning."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
#, fuzzy
msgid "Result Filter Usage"
msgstr "_Resultatfilter"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
#, fuzzy
msgid "Include Text"
msgstr "Inkludera text"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
#, fuzzy
msgid "Files, folders and usernames containing this text will be shown."
msgstr "Filer och mappar som innehåller denna text visas."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
#, fuzzy
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Storleken är okänslig, men ordföljden är viktig: 'Instrumental Remix' kommer "
"inte att visa några 'Remix Instrumental'."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
#, fuzzy
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Använd | (eller rör) för att separera flera exakta fraser. Exempel:\n"
"    Remix|Dub Mix|Instrumentell"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
#, fuzzy
msgid "Exclude Text"
msgstr "Exkludera text"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
#, fuzzy
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr "Som ovan, men filer och mappar filtreras bort om texten stämmer."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
#, fuzzy
msgid "Filters files based upon their file extension."
msgstr "Filtrerar filer baserat på deras filändelse."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
#, fuzzy
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Flera filändelser kan anges, vilket i sin tur breddar listan med resultat.\n"
"    Exempel: flac|wav|ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
#, fuzzy
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"Det är också möjligt att vända på filtret och ange filtillägg som du inte "
"vill ha med i resultatet.\n"
"    Exempel: !mp3|!jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
#, fuzzy
msgid "File Size"
msgstr "Filstorlek"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
#, fuzzy
msgid "Filters files based upon their file size."
msgstr "Filtrerar filer baserat på deras filstorlek."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
#, fuzzy
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"Som standard används enheten bytes och filer som är större än eller lika med "
"värdet matchas."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
#, fuzzy
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Lägg till b, k, m eller g (alternativt kib, mib eller gib) för att ange "
"byte-, kibibyte-, mebibyte- eller gibibyte-enheter:\n"
"    <1024k hittar filer som är 1024 kibabyte (dvs. 1 mbyte) eller mindre."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
#, fuzzy
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Sätt = före ett värde för att ange en exakt matchning:\n"
"    =1024 matchar endast filer som är 1024 byte stora (dvs. 1 kibibyte)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
#, fuzzy
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Sätt = före ett värde för att ange en exakt matchning:\n"
"    =1024 matchar endast filer som är 1024 byte stora (dvs. 1 kibibyte)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
#, fuzzy
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Lägg till < eller > för att hitta filer som är mindre/större än det angivna "
"värdet:\n"
"    >10,5m|<1g för att visa filer större än 10,5 MiB (mebibyte),\n"
"    men mindre än 1 GiB (gibibyte), använd en | mellan förhållanden."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
#, fuzzy
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"För enkelhetens skull kan varianterna kb, mb och gb för de mer kända "
"enheterna kilo-, mega- och gigabyte också användas."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
#, fuzzy
msgid "Filters files based upon their bitrate."
msgstr "Filtrerar filer baserat på deras bithastighet."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
#, fuzzy
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Värden måste endast anges som numeriska siffror. Enheten är alltid Kb/s "
"(kilobit per sekund)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
#, fuzzy
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Liksom filstorlek (ovan) kan operatorerna =, !, < och > användas, och flera "
"villkor kan anges med | rör:\n"
"    >256|<1411 för att visa filer med en bithastighet på minst 256 Kb/s\n"
"    med en maximal bithastighet på 1411 Kb/s."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
#, fuzzy
msgid "Filters files based upon their duration."
msgstr "Filtrerar filer baserat på deras bithastighet."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
#, fuzzy
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"Som standard kommer filer längre än eller lika med den angivna varaktigheten "
"att matchas, om inte en operator (=, !, < eller >) används."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
#, fuzzy
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Ange ett råvärde i sekunder eller använd tidsformaten MM:SS och TT:MM:SS:\n"
"    >5:30 för att visa filer som är minst 5 och en halv minut långa.\n"
"    <5:30:00 visar filer som är mindre än 5 och en halv timme långa."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
#, fuzzy
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Flera villkor kan anges med | röravskiljare:\n"
"    >6:00|<12:00 för att visa filer mellan 6 och 12 minuter långa.\n"
"    !9:54|!8:43|!7:32 för att dölja vissa specifika filer från resultaten.\n"
"    =5:34|=4:23|=3:05 för att inkludera filer med specifik varaktighet."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
#, fuzzy
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Använder landskoder enligt ISO 3166-2 (se Wikipedia):\n"
"    \"US\" returnerar endast filer från användare som är anslutna via USA. "
"På samma sätt returnerar \"GB\" filer från användare med IP-adresser i "
"Storbritannien."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
#, fuzzy
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Flera länder kan anges med kommatecken eller mellanslag."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
#, fuzzy
msgid "Free Slot"
msgstr "Gratis slot"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
#, fuzzy
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Visa endast resultat från användare som har minst en ledig "
"uppladdningsplats. Detta filter tillämpas omedelbart."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
#, fuzzy
msgid "Upload Speed Limits"
msgstr "Begränsningar för uppladdningshastighet"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
#, fuzzy
msgid "Unlimited upload speed"
msgstr "Begränsa uppladdningshastigheten:"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
#, fuzzy
msgid "Use upload speed limit (KiB/s):"
msgstr "Alternativ hastighetsgräns för uppladdning (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
#, fuzzy
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Alternativ hastighetsgräns för uppladdning (KiB/s):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
#, fuzzy
msgid "Private Chat Command Help"
msgstr "Hjälp med kommandot för privat chatt"

#: pynicotine/gtkgui/ui/search.ui:7
#, fuzzy
msgid "Include text…"
msgstr "Inkludera text…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
#, fuzzy
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtrera resultat vars filvägar innehåller den angivna texten. Flera fraser "
"och ord kan anges, t.ex. exakt fras|musik|term|exakt fras två."

#: pynicotine/gtkgui/ui/search.ui:18
#, fuzzy
msgid "Exclude text…"
msgstr "Exkludera text…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
#, fuzzy
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtrera bort resultat vars filvägar innehåller den angivna texten. Flera "
"fraser och ord kan anges, t.ex. exakt fras|musik|term|exakt fras två."

#: pynicotine/gtkgui/ui/search.ui:29
#, fuzzy
msgid "File type…"
msgstr "Filtyp…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
#, fuzzy
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Filtyp, t.ex. flac|wav|ape eller !mp3|!m4a"

#: pynicotine/gtkgui/ui/search.ui:40
#, fuzzy
msgid "File size…"
msgstr "Filstorlek…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
#, fuzzy
msgid "File size, e.g. >10.5m <1g"
msgstr "Filstorlek, t.ex. >10,5m <1g"

#: pynicotine/gtkgui/ui/search.ui:51
#, fuzzy
msgid "Bitrate…"
msgstr "Bitrate"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
#, fuzzy
msgid "Bitrate, e.g. 256 <1412"
msgstr "Bithastighet, t.ex. 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
#, fuzzy
msgid "Duration…"
msgstr "Varaktighet…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
#, fuzzy
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Varaktighet, t.ex. 2:20|!3:30|=4:40"

#: pynicotine/gtkgui/ui/search.ui:73
#, fuzzy
msgid "Country code…"
msgstr "Landskod"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
#, fuzzy
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Landskod, t.ex. US|GB|ES eller !DE|!GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
#, fuzzy
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Förbjuda användare från att komma åt dina delade filer, baserat på "
"användarnamn, IP-adress eller land."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Landskoder att blockera (avgränsa med komma)"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
#, fuzzy
msgid "Codes must be in ISO 3166-2 format."
msgstr "Koderna måste vara i ISO 3166-2 format."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
#, fuzzy
msgid "Use custom geo block message:"
msgstr "Använd eget ban-meddelande:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Använd eget ban-meddelande:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
#, fuzzy
msgid "IP Addresses"
msgstr "Adresser"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
#, fuzzy
msgid "Restore previously open private chats on startup"
msgstr "Återställ tidigare öppna privata chattar vid uppstart"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
#, fuzzy
msgid "Enable spell checker"
msgstr "Aktivera stavningskontroll (kräver omstart)"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
#, fuzzy
msgid "Enable CTCP-like private message responses (client version)"
msgstr "Aktivera CTCP-liknande svar på privata meddelanden (klientversion)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
#, fuzzy
msgid "Number of recent private chat messages to show:"
msgstr "Antal senaste chattlinjer som ska visas:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
#, fuzzy
msgid "Number of recent chat room messages to show:"
msgstr "Antal senaste chattlinjer som ska visas:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
#, fuzzy
msgid "Chat Completion"
msgstr "Slutförande"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
#, fuzzy
msgid "Enable tab-key completion"
msgstr "Aktivera komplettering med tabbtangenter"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
#, fuzzy
msgid "Enable completion drop-down list"
msgstr "Rullgardinslistan Aktivera komplettering"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
#, fuzzy
msgid "Minimum characters required to display drop-down:"
msgstr "Minsta antal tecken som krävs för att visa rullgardinsmenyn:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
#, fuzzy
msgid "Allowed chat completions:"
msgstr "Tillåtna fullbordanden"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
#, fuzzy
msgid "Buddy names"
msgstr "Buddy-lista"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
#, fuzzy
msgid "Chat room usernames"
msgstr "Meddelande från chattrummet:"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
#, fuzzy
msgid "Room names"
msgstr "Rum"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
#, fuzzy
msgid "Commands"
msgstr "Kommandon"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
#, fuzzy
msgid "Timestamps"
msgstr "Tidsstämplar"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
#, fuzzy
msgid "Private chat format:"
msgstr "Privatrumsformat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "Standard"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
#, fuzzy
msgid "Chat room format:"
msgstr "Format för chattrum:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
#, fuzzy
msgid "Text-to-Speech"
msgstr "Text-till-tal"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
#, fuzzy
msgid "Enable Text-to-Speech"
msgstr "Aktivera text till tal"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
#, fuzzy
msgid "Text-to-Speech command:"
msgstr "Kommando för text till tal:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
#, fuzzy
msgid "Private chat message:"
msgstr "Privat chattmeddelande:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
#, fuzzy
msgid "Chat room message:"
msgstr "Meddelande från chattrummet:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
#, fuzzy
msgid "Censor"
msgstr "Censurera"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
#, fuzzy
msgid "Enable censoring of text patterns"
msgstr "Aktivera censurering av textmönster"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
#, fuzzy
msgid "Auto-Replace"
msgstr "Automatisk ersättning"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
#, fuzzy
msgid "Enable automatic replacement of words"
msgstr "Aktivera automatisk ersättning av ord"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
#, fuzzy
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr ""
"Automatisk rensning av avslutade/filtrerade nedladdningar från "
"överföringslistan"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
#, fuzzy
msgid "Store completed downloads in username subfolders"
msgstr "Lagra nedladdningar i undermappar med användarnamn"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
#, fuzzy
msgid "Double-click action for downloads:"
msgstr "Dubbelklicka för nedladdningar:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
#, fuzzy
msgid "Allow users to send you any files:"
msgstr "Tillåt dessa användare att skicka filer till dig:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
#, fuzzy
msgid "Folders"
msgstr "Mappar"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
#, fuzzy
msgid "Finished downloads:"
msgstr "Fil nerladdad"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
#, fuzzy
msgid "Incomplete downloads:"
msgstr "Färdiga nedladdningar"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
#, fuzzy
msgid "Received files:"
msgstr "Mottagna filer:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "Händelser"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
#, fuzzy
msgid "Run command after file download finishes ($ for file path):"
msgstr "Kör kommandot när nedladdningen av filen är klar ($ för filsökväg):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
#, fuzzy
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"Kör kommandot när nedladdningen av mappen är klar ($ för mappens sökväg):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
#, fuzzy
msgid "Download Filters"
msgstr "Ladda ner filter"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
#, fuzzy
msgid "Enable download filters"
msgstr "Aktivera nedladdningsfilter"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Lägg till"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Standard"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
#, fuzzy
msgid "Verify Filters"
msgstr "Verifiera filter"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
#, fuzzy
msgid "Unverified"
msgstr "Obekräftad"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
#, fuzzy
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Ignorera chattmeddelanden och sökresultat från användare, baserat på "
"användarnamn eller IP-adress."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Logga chattrum"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Logga privata chatter"

#: pynicotine/gtkgui/ui/settings/log.ui:142
#, fuzzy
msgid "Log transfers to file"
msgstr "Logga överföringar till en fil"

#: pynicotine/gtkgui/ui/settings/log.ui:166
#, fuzzy
msgid "Log debug messages to file"
msgstr "Logga felsökningsmeddelanden till en fil"

#: pynicotine/gtkgui/ui/settings/log.ui:190
#, fuzzy
msgid "Log timestamp format:"
msgstr "Format för loggfilen:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
#, fuzzy
msgid "Folder Locations"
msgstr "Placering av mappar"

#: pynicotine/gtkgui/ui/settings/log.ui:245
#, fuzzy
msgid "Chatroom logs folder:"
msgstr "Mapp med chattrumsloggar:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
#, fuzzy
msgid "Private chat logs folder:"
msgstr "Privat mapp för chattloggar:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
#, fuzzy
msgid "Transfer logs folder:"
msgstr "Överför mappen med loggar:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
#, fuzzy
msgid "Debug logs folder:"
msgstr "Mapp för felsökningsloggar:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
#, fuzzy
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Logga in på ett befintligt Soulseek-konto eller skapa ett nytt. Användarnamn "
"är skiftlägeskänsliga och unika."

#: pynicotine/gtkgui/ui/settings/network.ui:118
#, fuzzy
msgid "Public IP address:"
msgstr "Blockera IP-adress"

#: pynicotine/gtkgui/ui/settings/network.ui:159
#, fuzzy
msgid "Listening port:"
msgstr "Lyssnar på port %i"

#: pynicotine/gtkgui/ui/settings/network.ui:185
#, fuzzy
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Automatiskt vidarebefordra lyssningsport (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
#, fuzzy
msgid "Away Status"
msgstr "Status"

#: pynicotine/gtkgui/ui/settings/network.ui:228
#, fuzzy
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Minuter av inaktivitet innan du går iväg (0 för att inaktivera):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
#, fuzzy
msgid "Auto-reply message when away:"
msgstr "Automatiskt svar när du är borta:"

#: pynicotine/gtkgui/ui/settings/network.ui:299
#, fuzzy
msgid "Auto-connect to server on startup"
msgstr "Automatisk anslutning till servern vid start"

#: pynicotine/gtkgui/ui/settings/network.ui:323
#, fuzzy
msgid "Soulseek server:"
msgstr "Server:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
#, fuzzy
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Binder anslutningar till ett specifikt nätverksgränssnitt, vilket är "
"användbart för att t.ex. se till att en VPN används hela tiden. Lämna tomt "
"för att använda alla tillgängliga gränssnitt. Ändra bara det här värdet om "
"du vet vad du gör."

#: pynicotine/gtkgui/ui/settings/network.ui:351
#, fuzzy
msgid "Network interface:"
msgstr "Sökningar i nätverket"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
#, fuzzy
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"Med Now Playing kan du visa vad din mediespelare spelar genom att använda "
"kommandot /now i chatten."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
#, fuzzy
msgid "Other"
msgstr "Annat"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
#, fuzzy
msgid "Now Playing Format"
msgstr "Format som spelas nu"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
#, fuzzy
msgid "Now Playing message format:"
msgstr "Meddelandeformat för \"Nu spelas upp\":"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
#, fuzzy
msgid "Test Configuration"
msgstr "Testkonfiguration"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
#, fuzzy
msgid "Enable plugins"
msgstr "Aktivera insticksmoduler"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
#, fuzzy
msgid "Add Plugins"
msgstr "_Add Plugins"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
#, fuzzy
msgid "_Add Plugins"
msgstr "_Add Plugins"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
#, fuzzy
msgid "Settings"
msgstr "Hämtar status"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
#, fuzzy
msgid "_Settings"
msgstr "Hämtar status"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
#, fuzzy
msgid "Version:"
msgstr "Version: "

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
#, fuzzy
msgid "Created by:"
msgstr "Skapad av"

#: pynicotine/gtkgui/ui/settings/search.ui:51
#, fuzzy
msgid "Enable search history"
msgstr "Aktivera sökhistorik"

#: pynicotine/gtkgui/ui/settings/search.ui:70
#, fuzzy
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Andra klienter kan erbjuda ett alternativ för att skicka privat delade filer "
"som svar på sökförfrågningar. Sådana filer har prefixet \"[PRIVATE FILE]\" "
"och kan inte laddas ner om inte uppladdaren ger uttryckligt tillstånd."

#: pynicotine/gtkgui/ui/settings/search.ui:76
#, fuzzy
msgid "Show privately shared files in search results"
msgstr "Visa privat delade filer i sökresultaten"

#: pynicotine/gtkgui/ui/settings/search.ui:106
#, fuzzy
msgid "Limit number of results per search:"
msgstr "Begränsa antalet resultat per sökning:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
#, fuzzy
msgid "Result Filter Help"
msgstr "Hjälp med resultatfilter"

#: pynicotine/gtkgui/ui/settings/search.ui:177
#, fuzzy
msgid "Enable search result filters by default"
msgstr "Aktivera filter"

#: pynicotine/gtkgui/ui/settings/search.ui:211
#, fuzzy
msgid "Include:"
msgstr "Inkludera:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
#, fuzzy
msgid "Exclude:"
msgstr "Exkludera:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
#, fuzzy
msgid "File Type:"
msgstr "Filtyp:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "Storlek:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Bitrate:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
#, fuzzy
msgid "Duration:"
msgstr "Varaktighet:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
#, fuzzy
msgid "Country Code:"
msgstr "Landskod"

#: pynicotine/gtkgui/ui/settings/search.ui:407
#, fuzzy
msgid "Only show results from users with an available upload slot."
msgstr "Visa endast resultat från användare som har en ledig uppladdningstid."

#: pynicotine/gtkgui/ui/settings/search.ui:430
#, fuzzy
msgid "Network Searches"
msgstr "Sökningar i nätverket"

#: pynicotine/gtkgui/ui/settings/search.ui:452
#, fuzzy
msgid "Respond to search requests from other users"
msgstr "Svara på sökförfrågningar från andra användare"

#: pynicotine/gtkgui/ui/settings/search.ui:486
#, fuzzy
msgid "Searches shorter than this number of characters will be ignored:"
msgstr "Sökningar som är kortare än detta antal tecken ignoreras:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
#, fuzzy
msgid "Maximum search results to send per search request:"
msgstr "sökresultat per sökning"

#: pynicotine/gtkgui/ui/settings/search.ui:578
#, fuzzy
msgid "Clear Search History"
msgstr "Rensa sökhistorik"

#: pynicotine/gtkgui/ui/settings/search.ui:626
#, fuzzy
msgid "Clear Filter History"
msgstr "Rensa filterhistorik"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
#, fuzzy
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Skannar automatiskt om innehållet i dina delade mappar när du startar. Om "
"den är inaktiverad uppdateras dina delade mappar endast när du manuellt "
"initierar en ny genomsökning."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Indexera om dina dela filer vid programstart"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
#, fuzzy
msgid "Visible to everyone:"
msgstr "Synlig för alla:"

#: pynicotine/gtkgui/ui/settings/shares.ui:82
#, fuzzy
msgid "Buddy shares"
msgstr "Buddy-lista"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
#, fuzzy
msgid "Trusted shares"
msgstr "Indexera om delade filer"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
#, fuzzy
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr ""
"Automatisk rensning av avslutade/avbrutna uppladdningar från "
"överföringslistan"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
#, fuzzy
msgid "Double-click action for uploads:"
msgstr "Dubbelklicka för uppladdningar:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
#, fuzzy
msgid "Limit upload speed:"
msgstr "Begränsa uppladdningshastigheten:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
#, fuzzy
msgid "Per transfer"
msgstr "Per överföring"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
#, fuzzy
msgid "Total transfers"
msgstr "Totala överföringar"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
#, fuzzy
msgid "Upload Slots"
msgstr "Uppladdningar"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
#, fuzzy
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr "Filerna laddas upp cykliskt till de användare som står i kö."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
#, fuzzy
msgid "Upload queue type:"
msgstr "Typ av kö för uppladdning:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
#, fuzzy
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr ""
"Ställ uppladdningar i kö om den totala överföringshastigheten når (KiB/s):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
#, fuzzy
msgid "Fixed number of upload slots:"
msgstr "Begränsa antalet uppladdningsplatser till:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
#, fuzzy
msgid "Prioritize all buddies"
msgstr "Mina kompisar går före i nedladdningskön"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
#, fuzzy
msgid "Queue Limits"
msgstr "Begränsningar för köer"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
#, fuzzy
msgid "Maximum number of queued files per user:"
msgstr "Begränsa antalet resultat per sökning:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
#, fuzzy
msgid "Maximum total size of queued files per user (MiB):"
msgstr "Maximal total storlek på köade filer per användare (MiB):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
#, fuzzy
msgid "Limits do not apply to buddies"
msgstr "Mina kompisar slipper körestriktioner"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
#, fuzzy
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"Om $ förekommer kommer länken att ersättas med länken. Systemets "
"standardwebbläsare kommer att användas om inget protokoll har konfigurerats."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
#, fuzzy
msgid "File manager command:"
msgstr "File Manager-kommando ($ för filsökväg):"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
#, fuzzy
msgid "Reset Picture"
msgstr "Återställ bild"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
#, fuzzy
msgid "Self Description"
msgstr "Självbeskrivning"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
#, fuzzy
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""
"Lägg till saker du vill att alla ska se, som en kort beskrivning, hjälpsamma "
"tips eller riktlinjer för att ladda ner dina shares."

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
#, fuzzy
msgid "Picture:"
msgstr "Bild:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
#, fuzzy
msgid "Prefer dark mode"
msgstr "Föredrar mörkt läge"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
#, fuzzy
msgid "Use header bar"
msgstr "Använd _Header Bar"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
#, fuzzy
msgid "Display tray icon"
msgstr "Ikonen för faktarutan på displayen"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
#, fuzzy
msgid "Minimize to tray on startup"
msgstr "Minimera till facket vid start"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
#, fuzzy
msgid "Language (requires a restart):"
msgstr "Portintervall för lyssning (kräver omstart):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
#, fuzzy
msgid "When closing window:"
msgstr "När du stänger Nicotine+:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
#, fuzzy
msgid "Notifications"
msgstr "anmälningar"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
#, fuzzy
msgid "Enable sound for notifications"
msgstr "Aktivera ljud för popupmeddelanden"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
#, fuzzy
msgid "Show notification for private chats and mentions in the window title"
msgstr "Visa meddelanden om privata chattar och omnämnanden i fönstrets titel"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
#, fuzzy
msgid "Show notifications for:"
msgstr "Visa ikoner för meddelanden på flikar"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
#, fuzzy
msgid "Finished file downloads"
msgstr "Fil nerladdad"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
#, fuzzy
msgid "Finished folder downloads"
msgstr "Hämtad mapp"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
#, fuzzy
msgid "Private messages"
msgstr "Privat meddelande från %s"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
#, fuzzy
msgid "Chat room messages"
msgstr "Meddelande från chattrummet:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
#, fuzzy
msgid "Chat room mentions"
msgstr "Slutförande"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
#, fuzzy
msgid "Wishlist results found"
msgstr "Resultat för önskelista hittades"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
#, fuzzy
msgid "Restore the previously active main tab at startup"
msgstr "Återställ tidigare öppna privata chattar vid uppstart"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
#, fuzzy
msgid "Close-buttons on secondary tabs"
msgstr "Stäng-knappar på sekundära flikar"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
#, fuzzy
msgid "Regular tab label color:"
msgstr "Färg på etiketten för vanliga flikar:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
#, fuzzy
msgid "Changed tab label color:"
msgstr "Ändrade färgen på fliken:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
#, fuzzy
msgid "Highlighted tab label color:"
msgstr "Färg på etiketten för markerade flikar:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
#, fuzzy
msgid "Buddy list position:"
msgstr "Position i kompislistan:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
#, fuzzy
msgid "Visible main tabs:"
msgstr "Synliga primära flikar:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
#, fuzzy
msgid "Tab bar positions:"
msgstr "Fältfältets position:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
#, fuzzy
msgid "Main tabs"
msgstr "Huvudflikar"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
#, fuzzy
msgid "Show reverse file paths (requires a restart)"
msgstr "Visa omvända filvägar i sök- och överföringsvyer (kräver omstart)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
#, fuzzy
msgid "Show exact file sizes (requires a restart)"
msgstr "Nätverksgränssnitt (kräver omstart):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
#, fuzzy
msgid "List text color:"
msgstr "Färg på listans text:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
#, fuzzy
msgid "Enable colored usernames"
msgstr "Meddelande från chattrummet:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
#, fuzzy
msgid "Chat username appearance:"
msgstr "Användarnamnet för chatt visas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
#, fuzzy
msgid "Remote text color:"
msgstr "Färg på fjärrtexten:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
#, fuzzy
msgid "Local text color:"
msgstr "Lokalt filfel"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
#, fuzzy
msgid "Command output text color:"
msgstr "Färg på fjärrtexten:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
#, fuzzy
msgid "/me action text color:"
msgstr "/me action textfärg:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
#, fuzzy
msgid "Highlighted text color:"
msgstr "Färg på markerad text:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
#, fuzzy
msgid "URL link text color:"
msgstr "URL-länkens textfärg:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
#, fuzzy
msgid "User Statuses"
msgstr "USA"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
#, fuzzy
msgid "Online color:"
msgstr "Textfärg på nätet:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
#, fuzzy
msgid "Away color:"
msgstr "Färg på texten:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
#, fuzzy
msgid "Offline color:"
msgstr "Offline textfärg:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
#, fuzzy
msgid "Text Entries"
msgstr "Textuppgifter"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
#, fuzzy
msgid "Text entry background color:"
msgstr "Bakgrundsfärg för textinmatning:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
#, fuzzy
msgid "Text entry text color:"
msgstr "Färg för textinmatning:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
#, fuzzy
msgid "Fonts"
msgstr "Typsnitt"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
#, fuzzy
msgid "Global font:"
msgstr "Globalt typsnitt:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
#, fuzzy
msgid "List font:"
msgstr "Lista teckensnitt:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
#, fuzzy
msgid "Text view font:"
msgstr "Teckensnitt för textvy:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
#, fuzzy
msgid "Chat font:"
msgstr "Chattfont:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
#, fuzzy
msgid "Transfers font:"
msgstr "Överför teckensnitt:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
#, fuzzy
msgid "Search font:"
msgstr "Sök typsnitt"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
#, fuzzy
msgid "Browse font:"
msgstr "Bläddra bland teckensnitten:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
#, fuzzy
msgid "Icons"
msgstr "Ikoner"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
#, fuzzy
msgid "Icon theme folder:"
msgstr "Ofullständig mapp:"

#: pynicotine/gtkgui/ui/uploads.ui:86
#, fuzzy
msgid "Abort User(s)"
msgstr "Avbryt användare(s)"

#: pynicotine/gtkgui/ui/uploads.ui:116
#, fuzzy
msgid "Ban User(s)"
msgstr "Förbjuda användare"

#: pynicotine/gtkgui/ui/uploads.ui:138
#, fuzzy
msgid "Clear All Finished/Cancelled Uploads"
msgstr ""
"Automatisk rensning av avslutade/avbrutna uppladdningar från "
"överföringslistan"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
#, fuzzy
msgid "Message All"
msgstr "Meddelanden"

#: pynicotine/gtkgui/ui/uploads.ui:199
#, fuzzy
msgid "Clear Specific Uploads"
msgstr "Rensa alla uppladdningar"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
#, fuzzy
msgid "Save Shares List to Disk"
msgstr "_Spara listan över delningar på disk"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
#, fuzzy
msgid "Refresh Files"
msgstr "Uppdatera filer."

#: pynicotine/gtkgui/ui/userinfo.ui:101
#, fuzzy
msgid "Edit Profile"
msgstr "Delade mappar"

#: pynicotine/gtkgui/ui/userinfo.ui:149
#, fuzzy
msgid "Shared Files"
msgstr "_Sök"

#: pynicotine/gtkgui/ui/userinfo.ui:203
#, fuzzy
msgid "Upload Speed"
msgstr "Uppladdningar"

#: pynicotine/gtkgui/ui/userinfo.ui:230
#, fuzzy
msgid "Free Upload Slots"
msgstr "Gratis uppladdning av spelautomater"

#: pynicotine/gtkgui/ui/userinfo.ui:284
#, fuzzy
msgid "Queued Uploads"
msgstr "Uppladdningar"

#: pynicotine/gtkgui/ui/userinfo.ui:354
#, fuzzy
msgid "Edit Interests"
msgstr "Intressen"

#: pynicotine/gtkgui/ui/userinfo.ui:624
#, fuzzy
msgid "_Gift Privileges…"
msgstr "_Privilegierad"

#: pynicotine/gtkgui/ui/userinfo.ui:663
#, fuzzy
msgid "_Refresh Profile"
msgstr "Uppdatera filer."

#: pynicotine/plugins/core_commands/PLUGININFO:3
#, fuzzy
msgid "Nicotine+ Commands"
msgstr "Nicotine+ Team"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Nicotine+ Team"

#, fuzzy
#~ msgid "Listening port (requires a restart):"
#~ msgstr "Portintervall för lyssning (kräver omstart):"

#, fuzzy
#~ msgid "Network interface (requires a restart):"
#~ msgstr "Portintervall för lyssning (kräver omstart):"

#, fuzzy
#~ msgid "Invalid Password"
#~ msgstr "Lösenord:"

#, fuzzy
#~ msgid "Change _Login Details"
#~ msgstr "Ändra inloggningsuppgifter"

#, fuzzy, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i privilegierade användare"

#, fuzzy
#~ msgid "_Set Up…"
#~ msgstr "_Set Up…"

#, fuzzy
#~ msgid "Queued search result text color:"
#~ msgstr "Färg på texten för sökresultatet i kö:"

#~ msgid "_Clear"
#~ msgstr "Rensa"

#~ msgid "Username:"
#~ msgstr "Användarnamn:"

#~ msgid "_Remove"
#~ msgstr "Ta bo_rt"

#, python-format
#~ msgid "Cannot find %(option1)s or %(option2)s, please install either one."
#~ msgstr ""
#~ "Kan inte finna %(option1)s eller %(option2)s, var snäll installera någon "
#~ "av de två."

#, python-format
#~ msgid "Quit %(program)s %(version)s, %(status)s!"
#~ msgstr "Avsluta %(program)s %(version)s, %(status)s!"

#~ msgid "terminated"
#~ msgstr "avslutad"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr ""
#~ "Servern verkar vara nere eller svarar inte, försöker igen om %i sekunder"

#~ msgid "--- disconnected ---"
#~ msgstr "--- nedkopplad ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- återansluten ---"

#~ msgid ""
#~ "Cannot import the Gtk module. Bad install of the python-gobject module?"
#~ msgstr ""
#~ "Kan inte installera GTK-modulen. Har python-gobject modulen blivit "
#~ "felaktigt installerad?"

#~ msgid "Join room 'room'"
#~ msgstr "Anslut till rummet 'rum'"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "Lägg användaren 'användare' till din ban-lista"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "Ta bort användaren 'användare' från din ban-lista"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "Lägg användaren 'användare' till din ignore-lista"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "Ta bort användaren 'användare' från din ignore-lista"

#~ msgid "Show IP for user 'user'"
#~ msgstr "Visa IP-adress för användaren 'användare'"

#~ msgid "Start a new search for 'query'"
#~ msgstr "Påbörja sökning efter 'förfrågan'"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "Sök i anslutna rum efter 'förfrågan'"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "Sök i kompislistan efter 'förfrågan'"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "Skicka meddelandet 'message' till användaren 'användare'"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr "Öppna privat chattfönster för användaren 'användare'"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "Kunde inte hitta alias (%s)"

#~ msgid "Aliases:"
#~ msgstr "Alias:"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "Tog bort aliaset %(alias)s: %(action)s\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "Kunde inte hitta alias (%(alias)s)\n"

#~ msgid "Add a new alias"
#~ msgstr "Lägg till ett nytt alias"

#~ msgid "Remove an alias"
#~ msgstr "Ta bort ett alias"

#~ msgid "Aborted"
#~ msgstr "Avbruten"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "Kände inte igen kommandot %s"

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "OS-fel: %s"

#~ msgid "Length"
#~ msgstr "Längd"

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "I/O-fel: %s"

#~ msgid "Protocol:"
#~ msgstr "Protokoll:"

#~ msgid "Establishing connection"
#~ msgstr "Upprättar anslutning"

#~ msgid "Handler"
#~ msgstr "Hanterare"

#~ msgid "Transfers"
#~ msgstr "Överföringar"

#~ msgid "Categories"
#~ msgstr "Kategorier"

#~ msgid "_Privileged"
#~ msgstr "_Privilegierad"

#~ msgid "Comments"
#~ msgstr "Kommentarer"

#~ msgid "Scanning Buddy Shares"
#~ msgstr "Indexering påbörjad"

#~ msgid "Set your personal ticker"
#~ msgstr "Ställ in din egna ticker"

#~ msgid "Add user 'user' to your user list"
#~ msgstr "Lägg användaren 'användare' till din användarlista"

#~ msgid "Request user info for user 'user'"
#~ msgstr "Begär användarinfo från användaren 'användare'"

#, python-format
#~ msgid "Can not log in, reason: %s"
#~ msgstr "Kunde inte logga in, anledning: %s"

#~ msgid ""
#~ "Someone else is logging in with the same nickname, server is going to "
#~ "disconnect us"
#~ msgstr ""
#~ "Någon annan loggar in med samma användarnamn, servern stänger din "
#~ "anslutning"

#~ msgid "Shared files database seems to be corrupted, rescan your shares"
#~ msgstr ""
#~ "Databasen för delade filer verkar vara skadad, indexera om dina delade "
#~ "filer"

#~ msgid "Immediate Download"
#~ msgstr "Direkt nedladdning"

#~ msgid "Server"
#~ msgstr "Server"

#~ msgid "Geo Block"
#~ msgstr "Geo-block"

#~ msgid "URL Catching"
#~ msgstr "URL-catching"

#~ msgid "Abor_t"
#~ msgstr "Avbry_t"

#, python-format
#~ msgid "Directories: %s"
#~ msgstr "Katalog: %s"

#~ msgid "Directories: unknown"
#~ msgstr "Katalog: okänt"

#~ msgid "_Private Chat"
#~ msgstr "_Privat chatt"

#~ msgid "Buddy _List"
#~ msgstr "Kompisar"

#~ msgid "_Interests"
#~ msgstr "_Intressen"

#~ msgid "Add..."
#~ msgstr "Lägg till..."

#~ msgid "Away:"
#~ msgstr "Borta:"

#~ msgid "Offline:"
#~ msgstr "Offline:"

#~ msgid "Online:"
#~ msgstr "Online:"

#~ msgid "Enable geographical blocker"
#~ msgstr "Aktivera geografisk blockerare"

#~ msgid "lines"
#~ msgstr "rader"

#~ msgid "Send out a max of"
#~ msgstr "Returnera maximalt"

#~ msgid "Enable URL catching"
#~ msgstr "Aktivera URL-catching"
