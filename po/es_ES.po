# SPDX-FileCopyrightText: 2003-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2024-11-19 09:03+0000\n"
"Last-Translator: Tagomago <<EMAIL>>\n"
"Language-Team: Spanish <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/es/>\n"
"Language: es_ES\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.9-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr "Cliente de Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Cliente gráfico para la red P2P de Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr "Soulseek;Nicotine;compartir;chat;mensajería;P2P;peer-to-peer;GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
msgid "Browse the Soulseek network"
msgstr "Navega por la red de Soulseek"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+ es un cliente gráfico para la red P2P de Soulseek."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ pretende ser una alternativa ligera, agradable, libre y de código "
"abierto (FOSS) al cliente oficial de Soulseek, ofreciendo además un amplio "
"conjunto de funcionalidades."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
msgid "Search Files"
msgstr "Buscar ficheros"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Descargas"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
msgid "Browse Shares"
msgstr "Explorar compartidos"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
msgid "Private Chat"
msgstr "Chat privado"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Equipo de Nicotine+"

#: pynicotine/__init__.py:50
#, python-format
msgid "Website: %s"
msgstr "Sitio web: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "mostrar este mensaje de ayuda y salir"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "fichero"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr "usar un fichero de configuración no predeterminado"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "dir"

#: pynicotine/__init__.py:64
msgid "alternative directory for user data and plugins"
msgstr "directorio alternativo para los datos de usuario y los plugins"

#: pynicotine/__init__.py:68
msgid "start the program without showing window"
msgstr "iniciar el programa sin mostrar la ventana"

#: pynicotine/__init__.py:71
msgid "ip"
msgstr "ip"

#: pynicotine/__init__.py:72
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "enlazar los sockets a la IP especificada (útil para las VPN)"

#: pynicotine/__init__.py:75
msgid "port"
msgstr "puerto"

#: pynicotine/__init__.py:76
msgid "listen on the given port"
msgstr "escuchar en el puerto indicado"

#: pynicotine/__init__.py:80
msgid "rescan shared files"
msgstr "reescanear los ficheros compartidos"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr "iniciar el programa en modo «headless» (sin interfaz gráfica)"

#: pynicotine/__init__.py:88
msgid "display version and exit"
msgstr "mostrar la versión y salir"

#: pynicotine/__init__.py:121
#, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Estás usando una versión no soportada de Pyhton (%(old_version)s).\n"
"Deberías instalar Python %(min_version)s o superior."

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Fallo al escanear los compartidos. Por favor cierra otras instancias de "
"Nicotine+ e inténtalo de nuevo."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, python-format
msgid "%(user)s is away"
msgstr "%(user)s está ausente"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, python-format
msgid "%(user)s is online"
msgstr "%(user)s está conectado"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, python-format
msgid "%(user)s is offline"
msgstr "%(user)s está desconectado"

#: pynicotine/buddies.py:316
msgid "Buddy Status"
msgstr "Estado de los amigos"

#: pynicotine/chatrooms.py:383
#, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Has sido añadido a una sala privada: %(room)s"

#: pynicotine/chatrooms.py:478
#, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr ""
"Mensaje de chat del usuario «%(user)s» en la sala «%(room)s»: %(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr "No se puede crear el directorio «%(path)s», error reportado: %(error)s"

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr "Error al respaldar la configuración: %s"

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr "Configuración respaldada en: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Cargando %(program)s %(version)s"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Cerrando %(program)s %(version)s, %(status)s…"

#: pynicotine/core.py:246
msgid "terminating"
msgstr "cancelando"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "cierre de aplicación"

#: pynicotine/core.py:259
#, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "¡Salir de %(program)s %(version)s!"

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr ""
"Debes especificar un nombre de usuario y una contraseña antes de conectarte…"

#: pynicotine/downloads.py:239
#, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr "Error: ¡Falló el filtro de descarga! Verifica tus filtros. Razón: %s"

#: pynicotine/downloads.py:254
#, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Error: ¡%(num)d filtros de descarga fallaron! %(error)s "

#: pynicotine/downloads.py:366
#, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s descargado de %(user)s"

#: pynicotine/downloads.py:370
msgid "File Downloaded"
msgstr "Fichero descargado"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Ejecutado: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "La ejecución de «%(command)s» ha fallado: %(error)s"

#: pynicotine/downloads.py:407
#, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s descargado de %(user)s"

#: pynicotine/downloads.py:411
msgid "Folder Downloaded"
msgstr "Carpeta descargada"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Ejecutado en la carpeta: %s"

#: pynicotine/downloads.py:443
#, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "No se pudo mover «%(tempfile)s» a «%(file)s»: %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
msgid "Download Folder Error"
msgstr "Error al descargar la carpeta"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Descarga finalizada: usuario %(user)s, fichero %(file)s"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Descarga interrumpida: usuario %(user)s, fichero %(file)s"

#: pynicotine/downloads.py:1150
#, python-format
msgid "Download I/O error: %s"
msgstr "Error de E/S en la descarga: %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr ""
"No se puede obtener un bloqueo exclusivo sobre el fichero - Error de E/S: %s"

#: pynicotine/downloads.py:1202
#, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "No se puede guardar el fichero en %(folder_path)s: %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Descarga iniciada: usuario %(user)s, fichero %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, python-format
msgid "Cannot find %s, please install it."
msgstr "%s no encontrado, por favor, instálalo."

#: pynicotine/gtkgui/__init__.py:162
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr ""
"No hay entorno gráfico disponible, utilizando el modo headless (sin GUI)"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "_Conectar"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "_Desconectar"

#: pynicotine/gtkgui/application.py:308
msgid "Soulseek _Privileges"
msgstr "_Privilegios de Soulseek"

#: pynicotine/gtkgui/application.py:314
msgid "_Preferences"
msgstr "_Configuración"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
msgid "_Quit"
msgstr "_Salir"

#: pynicotine/gtkgui/application.py:337
msgid "Browse _Public Shares"
msgstr "Examinar los _compartidos públicos"

#: pynicotine/gtkgui/application.py:338
msgid "Browse _Buddy Shares"
msgstr "Examinar los _compartidos de amigos"

#: pynicotine/gtkgui/application.py:339
msgid "Browse _Trusted Shares"
msgstr "Examinar los _compartidos de amigos de confianza"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
msgid "_Rescan Shares"
msgstr "_Reescanear compartidos"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
msgid "Configure _Shares"
msgstr "Configurar _compartidos"

#: pynicotine/gtkgui/application.py:371
msgid "_Keyboard Shortcuts"
msgstr "Atajos de _teclado"

#: pynicotine/gtkgui/application.py:372
msgid "_Setup Assistant"
msgstr "_Asistente de configuración"

#: pynicotine/gtkgui/application.py:373
msgid "_Transfer Statistics"
msgstr "Estadísticas de _transferencias"

#: pynicotine/gtkgui/application.py:378
msgid "Report a _Bug"
msgstr "Informar de un _error"

#: pynicotine/gtkgui/application.py:379
msgid "Improve T_ranslations"
msgstr "Mejorar t_raducciones"

#: pynicotine/gtkgui/application.py:383
msgid "_About Nicotine+"
msgstr "_Acerca de Nicotine+"

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "_Fichero"

#: pynicotine/gtkgui/application.py:395
msgid "_Shares"
msgstr "_Compartidos"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
msgid "_Help"
msgstr "_Ayuda"

#: pynicotine/gtkgui/application.py:410
msgid "_Browse Shares"
msgstr "_Examinar compartidos"

#: pynicotine/gtkgui/application.py:465
#, python-format
msgid "Unable to show notification: %s"
msgstr "No ha sido posible mostrar la notificación: %s"

#: pynicotine/gtkgui/application.py:526
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Todavía tienes ficheros subiendo. ¿De verdad quieres salir?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr "Esperar a que finalicen las subidas"

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr "¿De verdad quieres salir?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
msgid "_No"
msgstr "_No"

#: pynicotine/gtkgui/application.py:535
msgid "_Run in Background"
msgstr "Ejecutar en segundo _plano"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr "Salir de Nicotine+"

#: pynicotine/gtkgui/application.py:561
msgid "Shares Not Available"
msgstr "Compartidos no disponibles"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Verifica que los discos externos estén montados y que los permisos de las "
"carpetas sean correctos."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
msgid "_Cancel"
msgstr "_Cancelar"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "_Reintentar"

#: pynicotine/gtkgui/application.py:567
msgid "_Force Rescan"
msgstr "_Forzar reescaneo"

#: pynicotine/gtkgui/application.py:742
msgid "Message Downloading Users"
msgstr "Enviar un mensaje a los usuarios que están descargando"

#: pynicotine/gtkgui/application.py:743
msgid "Send private message to all users who are downloading from you:"
msgstr ""
"Enviar un mensaje privado a todos los usuarios que están descargando de ti:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
msgid "_Send Message"
msgstr "_Enviar un mensaje"

#: pynicotine/gtkgui/application.py:756
msgid "Message Buddies"
msgstr "Enviar un mensaje a mis amigos"

#: pynicotine/gtkgui/application.py:757
msgid "Send private message to all online buddies:"
msgstr "Enviar un mensaje privado a todos los amigos que estén conectados:"

#: pynicotine/gtkgui/application.py:786
msgid "Select a Saved Shares List File"
msgstr "Seleccione una lista guardada de archivos compartidos"

#: pynicotine/gtkgui/application.py:877
msgid "Critical Error"
msgstr "Error crítico"

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ ha encontrado un error crítico y necesita salir. Por favor, copie "
"el siguiente mensaje e inclúyalo en un informe de error:"

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr "_Salir de Nicotine+"

#: pynicotine/gtkgui/application.py:883
msgid "_Copy & Report Bug"
msgstr "_Copiar y reportar error"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Estado"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr "País"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Usuario"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Velocidad"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Archivos"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted"
msgstr "De confianza"

#: pynicotine/gtkgui/buddies.py:108
msgid "Notify"
msgstr "Notificar"

#: pynicotine/gtkgui/buddies.py:114
msgid "Prioritized"
msgstr "Con prioridad"

#: pynicotine/gtkgui/buddies.py:120
msgid "Last Seen"
msgstr "Visto por última vez"

#: pynicotine/gtkgui/buddies.py:126
msgid "Note"
msgstr "Nota"

#: pynicotine/gtkgui/buddies.py:143
msgid "Add User _Note…"
msgstr "Añadir _nota de usuario…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Eliminar"

#: pynicotine/gtkgui/buddies.py:364
msgid "Never seen"
msgstr "Nunca visto"

#: pynicotine/gtkgui/buddies.py:529
msgid "Add User Note"
msgstr "Añadir nota de usuario"

#: pynicotine/gtkgui/buddies.py:530
#, python-format
msgid "Add a note about user %s:"
msgstr "Añadir una nota acerca del usuario %s:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
msgid "_Add"
msgstr "_Añadir"

#: pynicotine/gtkgui/chatrooms.py:224
msgid "Create New Room?"
msgstr "¿Crear nueva sala?"

#: pynicotine/gtkgui/chatrooms.py:225
#, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "¿De verdad quieres crear la nueva sala «%s»?"

#: pynicotine/gtkgui/chatrooms.py:226
msgid "Make room private"
msgstr "Hacer la sala privada"

#: pynicotine/gtkgui/chatrooms.py:515
msgid "Search activity log…"
msgstr "Buscar el registro de la actividad…"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
msgid "Search chat log…"
msgstr "Buscar en el registro del chat…"

#: pynicotine/gtkgui/chatrooms.py:597
msgid "Sear_ch User's Files"
msgstr "_Buscar archivos del usuario"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
msgid "Find…"
msgstr "Buscar…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Copiar"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr "Copiar todo"

#: pynicotine/gtkgui/chatrooms.py:608
msgid "Clear Activity View"
msgstr "Limpiar la vista de actividad"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
msgid "_Leave Room"
msgstr "_Salir de la sala"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
msgid "Copy Link"
msgstr "Copiar enlace"

#: pynicotine/gtkgui/chatrooms.py:624
msgid "View Room Log"
msgstr "Ver registro de la sala"

#: pynicotine/gtkgui/chatrooms.py:627
msgid "Delete Room Log…"
msgstr "Borrar registro de la sala…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
msgid "Clear Message View"
msgstr "Limpiar vista de mensajes"

#: pynicotine/gtkgui/chatrooms.py:832
#, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "El usuario %(user)s te mencionó en la sala %(room)s"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "Mencionado por %(user)s en la sala %(room)s"

#: pynicotine/gtkgui/chatrooms.py:855
#, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "Mensaje de %(user)s en la sala %(room)s"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s se unió a la sala"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s abandonó la sala"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s está ausente"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s ha vuelto"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
msgid "Delete Logged Messages?"
msgstr "¿Borrar mensajes registrados?"

#: pynicotine/gtkgui/chatrooms.py:1197
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"¿De verdad quieres borrar permanentemente todos los mensajes registrados en "
"esta sala?"

#: pynicotine/gtkgui/dialogs/about.py:405
msgid "About"
msgstr "Acerca de"

#: pynicotine/gtkgui/dialogs/about.py:415
msgid "Website"
msgstr "Sitio web"

#: pynicotine/gtkgui/dialogs/about.py:457
#, python-format
msgid "Error checking latest version: %s"
msgstr "Error al comprobar la última versión: %s"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr "Nueva versión disponible: %s"

#: pynicotine/gtkgui/dialogs/about.py:467
msgid "Up to date"
msgstr "Actualizado"

#: pynicotine/gtkgui/dialogs/about.py:496
msgid "Checking latest version…"
msgstr "Comprobando la última versión…"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
msgid "Setup Assistant"
msgstr "_Asistente de configuración"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
msgid "Virtual Folder"
msgstr "Carpeta virtual"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
msgid "Folder"
msgstr "Carpeta"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
msgid "_Previous"
msgstr "_Anterior"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
msgid "_Finish"
msgstr "_Terminar"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
msgid "_Next"
msgstr "_Siguiente"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
msgid "Add a Shared Folder"
msgstr "Añadir una carpeta compartida"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
msgid "Edit Shared Folder"
msgstr "Modificar carpeta compartida"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Introduzca el nuevo nombre virtual de «%(dir)s»:"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
msgid "_Edit"
msgstr "_Editar"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"El usuario %s ya existe y la contraseña que has introducido no es válida. "
"Por favor, elige otro nombre de usuario si es la primera vez que te conectas."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
msgid "File Properties"
msgstr "Propiedades del archivo"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr ""
"Propiedades del archivo (%(num)i de %(total)i  /  %(size)s  /  %(length)s)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Propiedades del archivo (%(num)i de %(total)i  /  %(size)s)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
msgid "_Apply"
msgstr "_Aplicar"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
msgid "Add…"
msgstr "Añadir…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
msgid "Edit…"
msgstr "Editar…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, python-format
msgid "%s Settings"
msgstr "Configuración de %s"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
msgid "Add Item"
msgstr "Añadir un elemento"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
msgid "Edit Item"
msgstr "Editar un elemento"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "Desconocido"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
msgid "Check Port Status"
msgstr "Comprobar el estado del puerto"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, puerto %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
msgid "Password Change Rejected"
msgstr "Cambio de contraseña rechazado"

#: pynicotine/gtkgui/dialogs/preferences.py:218
msgid "Enter a new password for your Soulseek account:"
msgstr "Introduce una nueva contraseña para tu cuenta de Soulseek:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"En este momento no estás conectado a la red de Soulseek. Si deseas cambiar "
"la contraseña de una cuenta existente de Soulseek, debes estar conectado a "
"la cuenta en cuestión."

#: pynicotine/gtkgui/dialogs/preferences.py:223
msgid "Enter password to use when logging in:"
msgstr "Introduzca la contraseña que utilizará para iniciar sesión:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
msgid "Change Password"
msgstr "Cambiar la contraseña"

#: pynicotine/gtkgui/dialogs/preferences.py:230
msgid "_Change"
msgstr "_Cambiar"

#: pynicotine/gtkgui/dialogs/preferences.py:274
msgid "No one"
msgstr "Nadie"

#: pynicotine/gtkgui/dialogs/preferences.py:275
msgid "Everyone"
msgstr "Todos"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Amigos"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted buddies"
msgstr "Compañeros de confianza"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
msgid "Nothing"
msgstr "Nada"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
msgid "Open File"
msgstr "Abrir un archivo"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
msgid "Open in File Manager"
msgstr "Mostrar en el gestor de archivos"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Buscar"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
msgid "Pause"
msgstr "Pausar"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
msgid "Resume"
msgstr "Reanudar"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
msgid "Browse Folder"
msgstr "Explorar carpeta"

#: pynicotine/gtkgui/dialogs/preferences.py:317
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Sintaxis</b>: Insensible a mayúsculas y minúsculas. Si está activada, se "
"pueden utilizar expresiones regulares de Python; de lo contrario, sólo se "
"admiten * coincidencias con comodines."

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr "Filtro"

#: pynicotine/gtkgui/dialogs/preferences.py:334
msgid "Regex"
msgstr "Regex (Expresión regular)"

#: pynicotine/gtkgui/dialogs/preferences.py:468
msgid "Add Download Filter"
msgstr "Añadir filtro de descarga"

#: pynicotine/gtkgui/dialogs/preferences.py:469
msgid "Enter a new download filter:"
msgstr "Introducir un nuevo filtro de descarga:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
msgid "Enable regular expressions"
msgstr "Habilitar las expresiones regulares"

#: pynicotine/gtkgui/dialogs/preferences.py:498
msgid "Edit Download Filter"
msgstr "Editar filtro de descarga"

#: pynicotine/gtkgui/dialogs/preferences.py:499
msgid "Modify the following download filter:"
msgstr "Modifique el siguiente filtro de descarga:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "¡%(num)d ha fallado! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
msgid "Filters Successful"
msgstr "Filtros con Éxito"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr "Público"

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr "Accesible para"

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "Interrumpir"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
msgid "Retry"
msgstr "Reintentar"

#: pynicotine/gtkgui/dialogs/preferences.py:828
msgid "Round Robin"
msgstr "Por turnos"

#: pynicotine/gtkgui/dialogs/preferences.py:829
msgid "First In, First Out"
msgstr "Por orden de llegada"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
msgid "Username"
msgstr "Nombre de usuario"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
msgid "IP Address"
msgstr "Dirección IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
msgid "Ignore User"
msgstr "Ignorar usuario"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
msgid "Enter the name of the user you want to ignore:"
msgstr "Introduce el nombre del usuario que deseas ignorar:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
msgid "Ignore IP Address"
msgstr "Ignorar dirección IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
msgid "Enter an IP address you want to ignore:"
msgstr "Introduce la dirección IP que deseas ignorar:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "* is a wildcard"
msgstr "* es un comodín"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
msgid "Ban User"
msgstr "Vetar usuario"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
msgid "Enter the name of the user you want to ban:"
msgstr "Introduce el nombre del usuario que deseas vetar:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
msgid "Ban IP Address"
msgstr "Vetar dirección IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "Enter an IP address you want to ban:"
msgstr "Introduzca la dirección IP que deseas bloquear:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr "Códigos del formato"

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
msgid "Pattern"
msgstr "Patrón"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
msgid "Replacement"
msgstr "Recambio"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
msgid "Censor Pattern"
msgstr "Patrón de censura"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Introduce el patrón que deseas censurar. Añade espacios alrededor del patrón "
"si no deseas que coincida con cadenas que estén dentro de palabras (puede "
"fallar al principio y al final de las líneas)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
msgid "Edit Censored Pattern"
msgstr "Modificar el patrón censurado"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
msgid "Add Replacement"
msgstr "Añadir sustitución"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
msgid "Enter a text pattern and what to replace it with:"
msgstr "Introduce un patrón de texto y su reemplazo:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
msgid "Edit Replacement"
msgstr "Modificar reemplazo"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
msgid "System default"
msgstr "Predeterminado del sistema"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
msgid "Show confirmation dialog"
msgstr "Mostrar diálogo de confirmación"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
msgid "Run in the background"
msgstr "Ejecutar en segundo plano"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
msgid "bold"
msgstr "en negrita"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
msgid "italic"
msgstr "en cursiva"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
msgid "underline"
msgstr "subrayado"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
msgid "normal"
msgstr "normal"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
msgid "Separate Buddies tab"
msgstr "Pestaña de amigos separada"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
msgid "Sidebar in Chat Rooms tab"
msgstr "Barra lateral en la pestaña Salas del chat"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr "Barra lateral siempre visible"

#: pynicotine/gtkgui/dialogs/preferences.py:1777
msgid "Top"
msgstr "Arriba"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
msgid "Bottom"
msgstr "Abajo"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
msgid "Left"
msgstr "Izquierda"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
msgid "Right"
msgstr "Derecha"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "En línea"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Ausente"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Fuera de línea"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
msgid "Tab Changed"
msgstr "Pestaña cambiada"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
msgid "Tab Highlight"
msgstr "Resaltar pestaña"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
msgid "Window"
msgstr "Ventana"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
msgid "Online (Tray)"
msgstr "Conectado (Bandeja)"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
msgid "Away (Tray)"
msgstr "Ausente (Bandeja)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
msgid "Offline (Tray)"
msgstr "Fuera de línea (Bandeja)"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
msgid "Message (Tray)"
msgstr "Mensaje (Bandeja)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Protocolo"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
msgid "Command"
msgstr "Comando"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
msgid "Add URL Handler"
msgstr "Añadir un manejador de URL"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Introduce el protocolo y el comando para el manejador de URL:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
msgid "Edit Command"
msgstr "Modificar comando"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Introduce un nuevo comando para el protocolo %s:"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
msgid "Username;APIKEY"
msgstr "Nombre de usuario;APIKEY"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr ""
"Reproductor de música (p. ej., Amarok, Audacious, Exaile); dejar vacío para "
"autodetectar:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
msgid "Username: "
msgstr "Nombre de usuario: "

#: pynicotine/gtkgui/dialogs/preferences.py:2767
msgid "Command:"
msgstr "Comando:"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
msgid "Title"
msgstr "Título"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Reproduciendo ahora (típicamente «%(artist)s - %(title)s»)"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
msgid "Artist"
msgstr "Artista"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
msgid "Duration"
msgstr "Duración"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "Bitrate"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
msgid "Comment"
msgstr "Comentario"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
msgid "Album"
msgstr "Álbum"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
msgid "Track Number"
msgstr "Número de pista"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
msgid "Year"
msgstr "Año"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
msgid "Filename (URI)"
msgstr "Nombre del archivo (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
msgid "Program"
msgstr "Programa"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
msgid "Enabled"
msgstr "Habilitado"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
msgid "Plugin"
msgstr "Complemento"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
msgid "No Plugin Selected"
msgstr "No se ha seleccionado ningún complemento"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
msgid "Preferences"
msgstr "Preferencias"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
msgid "Network"
msgstr "Red"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
msgid "User Interface"
msgstr "Interfaz de usuario"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Compartidos"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Subidas"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Búsquedas"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
msgid "User Profile"
msgstr "Perfil del usuario"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
msgid "Chats"
msgstr "Chats"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
msgid "Now Playing"
msgstr "Reproduciendo ahora"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Registros"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
msgid "Banned Users"
msgstr "Usuarios vetados"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
msgid "Ignored Users"
msgstr "Usuarios ignorados"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
msgid "URL Handlers"
msgstr "Manejadores de URL"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr "Complementos"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
msgid "Pick a File Name for Config Backup"
msgstr ""
"Elija un nombre de archivo para la copia de seguridad de la configuración"

#: pynicotine/gtkgui/dialogs/statistics.py:75
msgid "Transfer Statistics"
msgstr "Estadísticas de transferencia"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, python-format
msgid "Total Since %(date)s"
msgstr "Total desde %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
msgid "Reset Transfer Statistics?"
msgstr "¿Reiniciar las estadísticas de transferencia?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
msgid "Do you really want to reset transfer statistics?"
msgstr "¿De verdad quieres reiniciar las estadísticas de transferencia?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
msgid "Wishlist"
msgstr "Lista de deseos"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
msgid "Wish"
msgstr "Deseo"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
msgid "_Search for Item"
msgstr "_Buscar elemento"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
msgid "Edit Wish"
msgstr "Editar deseo"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, python-format
msgid "Enter new value for wish '%s':"
msgstr "Introduzca un nuevo valor para el deseo «%s»:"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
msgid "Clear Wishlist?"
msgstr "¿Limpiar la lista de deseos?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
msgid "Do you really want to clear your wishlist?"
msgstr "¿De verdad quieres limpiar tu lista de deseos?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Ruta"

#: pynicotine/gtkgui/downloads.py:47
msgid "_Resume"
msgstr "_Reanudar"

#: pynicotine/gtkgui/downloads.py:48
msgid "P_ause"
msgstr "_Pausar"

#: pynicotine/gtkgui/downloads.py:72
msgid "Finished / Filtered"
msgstr "Terminadas/filtradas"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Terminada"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
msgid "Paused"
msgstr "En pausa"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
msgid "Filtered"
msgstr "Filtrada"

#: pynicotine/gtkgui/downloads.py:77
msgid "Deleted"
msgstr "Eliminada"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
msgid "Queued…"
msgstr "En cola…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
msgid "Everything…"
msgstr "Todo…"

#: pynicotine/gtkgui/downloads.py:132
#, python-format
msgid "Downloads: %(speed)s"
msgstr "Descargas: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
msgid "Clear Queued Downloads"
msgstr "Limpiar descargas en cola"

#: pynicotine/gtkgui/downloads.py:139
msgid "Do you really want to clear all queued downloads?"
msgstr "¿De verdad quieres limpiar todas las descargas en cola?"

#: pynicotine/gtkgui/downloads.py:151
msgid "Clear All Downloads"
msgstr "Limpiar todas las descargas"

#: pynicotine/gtkgui/downloads.py:152
msgid "Do you really want to clear all downloads?"
msgstr "¿De verdad quieres limpiar todas las descargas?"

#: pynicotine/gtkgui/downloads.py:169
#, python-format
msgid "Download %(num)i files?"
msgstr "¿Descargar %(num)i archivos?"

#: pynicotine/gtkgui/downloads.py:170
#, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"¿De verdad quieres descargar %(num)i archivos de la carpeta %(folder)s de "
"%(user)s?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
msgid "_Download Folder"
msgstr "_Descargar carpeta"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
msgid "Likes"
msgstr "Le gusta"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
msgid "Dislikes"
msgstr "Aversiones"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Valoración"

#: pynicotine/gtkgui/interests.py:111
msgid "Item"
msgstr "Elemento"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
msgid "_Recommendations for Item"
msgstr "_Recomendaciones para el Ítem"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
msgid "I _Like This"
msgstr "Me _gusta esto"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
msgid "I _Dislike This"
msgstr "_No me gusta esto"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
msgid "Recommendations"
msgstr "Recomendaciones"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
msgid "Similar Users"
msgstr "Usuarios similares"

#: pynicotine/gtkgui/interests.py:427
#, python-format
msgid "Recommendations (%s)"
msgstr "Recomendaciones (%s)"

#: pynicotine/gtkgui/interests.py:451
#, python-format
msgid "Similar Users (%s)"
msgstr "Usuarios similares (%s)"

#: pynicotine/gtkgui/mainwindow.py:238
msgid "Search log…"
msgstr "Registro de la búsqueda…"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, python-format
msgid "Private Message from %(user)s"
msgstr "Mensaje privado de %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
msgid "Wishlist Results Found"
msgstr "Encontrados resultados de la lista de deseos"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
msgid "User Profiles"
msgstr "Perfiles de los usuarios"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
msgid "Chat Rooms"
msgstr "Salas de chat"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "Intereses"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
msgid "Chat"
msgstr "Chat"

#: pynicotine/gtkgui/mainwindow.py:1111
msgid "[Debug] Connections"
msgstr "[Debug] Conexiones"

#: pynicotine/gtkgui/mainwindow.py:1112
msgid "[Debug] Messages"
msgstr "[Debug] Mensajes"

#: pynicotine/gtkgui/mainwindow.py:1113
msgid "[Debug] Transfers"
msgstr "[Debug] Transferencias"

#: pynicotine/gtkgui/mainwindow.py:1114
msgid "[Debug] Miscellaneous"
msgstr "[Debug] Varios"

#: pynicotine/gtkgui/mainwindow.py:1119
msgid "_Find…"
msgstr "_Buscar…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
msgid "_Copy"
msgstr "_Copiar"

#: pynicotine/gtkgui/mainwindow.py:1122
msgid "Copy _All"
msgstr "Copiar _Todo"

#: pynicotine/gtkgui/mainwindow.py:1127
msgid "View _Debug Logs"
msgstr "Ver los _Registros de depuración"

#: pynicotine/gtkgui/mainwindow.py:1128
msgid "View _Transfer Logs"
msgstr "Ver los _Registros de transferencia"

#: pynicotine/gtkgui/mainwindow.py:1132
msgid "_Log Categories"
msgstr "Categorías de _Registro"

#: pynicotine/gtkgui/mainwindow.py:1134
msgid "Clear Log View"
msgstr "Limpiar la vista de registro"

#: pynicotine/gtkgui/mainwindow.py:1199
msgid "Preparing Shares"
msgstr "Preparando compartidos"

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr "Escaneando compartidos"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
msgid "Shared Folders"
msgstr "Carpetas compartidas"

#: pynicotine/gtkgui/popovers/chathistory.py:77
msgid "Latest Message"
msgstr "Último mensaje"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Sala"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Usuarios"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
msgid "Join Room"
msgstr "Unirse a la sala"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
msgid "Leave Room"
msgstr "Dejar la sala"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
msgid "Disown Private Room"
msgstr "Hacer pública la sala privada"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
msgid "Cancel Room Membership"
msgstr "Cancelar adhesión a la sala"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
msgid "Close All Tabs…"
msgstr "Cerrar todas las pestañas…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
msgid "_Close Tab"
msgstr "_Cerrar pestaña"

#: pynicotine/gtkgui/privatechat.py:379
msgid "View Chat Log"
msgstr "Ver registro de charla"

#: pynicotine/gtkgui/privatechat.py:382
msgid "Delete Chat Log…"
msgstr "Borrar registro de charla…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
msgid "User Actions"
msgstr "Acciones del usuario"

#: pynicotine/gtkgui/privatechat.py:477
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"¿De verdad quieres eliminar permanentemente todos los mensajes registrados "
"de este usuario?"

#: pynicotine/gtkgui/privatechat.py:528
msgid "* Messages sent while you were offline"
msgstr "* Mensajes enviados mientras estabas desconectado"

#: pynicotine/gtkgui/search.py:90
msgid "_Global"
msgstr "_Global"

#: pynicotine/gtkgui/search.py:91
msgid "_Buddies"
msgstr "_Amigos"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
msgid "_Rooms"
msgstr "_Salas"

#: pynicotine/gtkgui/search.py:93
msgid "_User"
msgstr "_Usuario"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
msgid "In Queue"
msgstr "En espera"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
msgid "File Type"
msgstr "Tipo de archivo"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Nombre de archivo"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Tamaño"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
msgid "Quality"
msgstr "Calidad"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
msgid "Copy _File Path"
msgstr "Copiar ruta del _archivo"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "Copiar _URL"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
msgid "Copy Folder U_RL"
msgstr "Copiar U_RL de la carpeta"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
msgid "_Download File(s)"
msgstr "_Descargar archivo(s)"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
msgid "Download File(s) _To…"
msgstr "Descargar archivo(s) _a…"

#: pynicotine/gtkgui/search.py:615
msgid "Download _Folder(s)"
msgstr "Descargar _carpeta(s)"

#: pynicotine/gtkgui/search.py:616
msgid "Download F_older(s) To…"
msgstr "Descargar c_arpeta(s) a…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
msgid "View User _Profile"
msgstr "Ver perfil del _usuario"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
msgid "_Browse Folder"
msgstr "_Explorar la carpeta"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
msgid "F_ile Properties"
msgstr "Propiedades del a_rchivo"

#: pynicotine/gtkgui/search.py:629
msgid "Copy Search Term"
msgstr "Copiar término de búsqueda"

#: pynicotine/gtkgui/search.py:631
msgid "Clear All Results"
msgstr "Limpiar todos los resultados"

#: pynicotine/gtkgui/search.py:718
msgid "Clear Filters"
msgstr "Limpiar los filtros"

#: pynicotine/gtkgui/search.py:721
msgid "Restore Filters"
msgstr "Restaurar los filtros"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, python-format
msgid "[PRIVATE]  %s"
msgstr "[PRIVADO]  %s"

#: pynicotine/gtkgui/search.py:1273
#, python-format
msgid "_Result Filters [%d]"
msgstr "Filtros de _resultados [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
msgid "_Result Filters"
msgstr "Filtros de _resultados"

#: pynicotine/gtkgui/search.py:1277
#, python-format
msgid "%d active filter(s)"
msgstr "%d filtro(s) activo(s)"

#: pynicotine/gtkgui/search.py:1329
msgid "Add Wi_sh"
msgstr "Añadir de_seo"

#: pynicotine/gtkgui/search.py:1332
msgid "Remove Wi_sh"
msgstr "Eliminar de_seo"

#: pynicotine/gtkgui/search.py:1349
msgid "Select User's Results"
msgstr "Seleccionar resultados del usuario"

#: pynicotine/gtkgui/search.py:1472
#, python-format
msgid "Total: %s"
msgstr "Total: %s"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
msgid "Results"
msgstr "Resultados"

#: pynicotine/gtkgui/search.py:1590
msgid "Select Destination Folder for File(s)"
msgstr "Seleccione la carpeta de destino para el(los) archivo(s)"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
msgid "Select Destination Folder"
msgstr "Seleccionar la carpeta de destino"

#: pynicotine/gtkgui/transfers.py:61
msgid "Queued"
msgstr "A la cola"

#: pynicotine/gtkgui/transfers.py:62
msgid "Queued (prioritized)"
msgstr "En cola (con prioridad)"

#: pynicotine/gtkgui/transfers.py:63
msgid "Queued (privileged)"
msgstr "En cola (con privilegio)"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Obteniendo estado"

#: pynicotine/gtkgui/transfers.py:65
msgid "Transferring"
msgstr "Transfiriendo"

#: pynicotine/gtkgui/transfers.py:66
msgid "Connection closed"
msgstr "Conexión cerrada"

#: pynicotine/gtkgui/transfers.py:67
msgid "Connection timeout"
msgstr "Conexión fuera de tiempo"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Usuario desconectado"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
msgid "Cancelled"
msgstr "Cancelada"

#: pynicotine/gtkgui/transfers.py:73
msgid "Download folder error"
msgstr "Error en la carpeta de descarga"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Error de archivo local"

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr "Vetado"

#: pynicotine/gtkgui/transfers.py:76
msgid "File not shared"
msgstr "Archivo no compartido"

#: pynicotine/gtkgui/transfers.py:77
msgid "Pending shutdown"
msgstr "Apagado en curso"

#: pynicotine/gtkgui/transfers.py:78
msgid "File read error"
msgstr "Error de lectura del archivo"

#: pynicotine/gtkgui/transfers.py:182
msgid "Queue"
msgstr "Posición en la cola"

#: pynicotine/gtkgui/transfers.py:188
msgid "Percent"
msgstr "Porcentaje"

#: pynicotine/gtkgui/transfers.py:208
msgid "Time Elapsed"
msgstr "Tiempo transcurrido"

#: pynicotine/gtkgui/transfers.py:215
msgid "Time Left"
msgstr "Tiempo restante"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
msgid "_Open File"
msgstr "_Abrir un archivo"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
msgid "Open in File _Manager"
msgstr "Mostrar en el _gestor de archivos"

#: pynicotine/gtkgui/transfers.py:286
msgid "_Search"
msgstr "_Buscar"

#: pynicotine/gtkgui/transfers.py:289
msgid "Clear All"
msgstr "Borrar todo"

#: pynicotine/gtkgui/transfers.py:953
msgid "Select User's Transfers"
msgstr "Seleccionar las transferencias del usuario"

#: pynicotine/gtkgui/uploads.py:50
msgid "_Abort"
msgstr "_Interrumpir"

#: pynicotine/gtkgui/uploads.py:74
msgid "Finished / Cancelled / Failed"
msgstr "Terminadas / interrumpidas / fallidas"

#: pynicotine/gtkgui/uploads.py:75
msgid "Finished / Cancelled"
msgstr "Terminadas / interrumpidas"

#: pynicotine/gtkgui/uploads.py:79
msgid "Failed"
msgstr "Fallida"

#: pynicotine/gtkgui/uploads.py:80
msgid "User Logged Off"
msgstr "Usuario desconectado"

#: pynicotine/gtkgui/uploads.py:142
#, python-format
msgid "Uploads: %(speed)s"
msgstr "Subidas: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr "Saliendo..."

#: pynicotine/gtkgui/uploads.py:164
msgid "Clear Queued Uploads"
msgstr "Limpiar subidas en cola"

#: pynicotine/gtkgui/uploads.py:165
msgid "Do you really want to clear all queued uploads?"
msgstr "¿De verdad quieres limpiar todas las subidas en cola?"

#: pynicotine/gtkgui/uploads.py:177
msgid "Clear All Uploads"
msgstr "Limpiar todas las subidas"

#: pynicotine/gtkgui/uploads.py:178
msgid "Do you really want to clear all uploads?"
msgstr "¿De verdad quieres limpiar todas las subidas?"

#: pynicotine/gtkgui/userbrowse.py:282
msgid "_Save Shares List to Disk"
msgstr "_Guardar la lista de compartidos en el disco"

#: pynicotine/gtkgui/userbrowse.py:292
msgid "Upload Folder & Subfolders…"
msgstr "Subir carpeta y subcarpeta(s)…"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
msgid "Copy _Folder Path"
msgstr "Copiar _ruta de la carpeta"

#: pynicotine/gtkgui/userbrowse.py:309
msgid "_Download Folder & Subfolders"
msgstr "_Descargar carpeta y subcarpeta(s)"

#: pynicotine/gtkgui/userbrowse.py:310
msgid "Download Folder & Subfolders _To…"
msgstr "Descargar carpeta y subcarpeta(s) en…"

#: pynicotine/gtkgui/userbrowse.py:334
msgid "File Name"
msgstr "Nombre del archivo"

#: pynicotine/gtkgui/userbrowse.py:373
msgid "Up_load File(s)…"
msgstr "_Enviar archivo(s)…"

#: pynicotine/gtkgui/userbrowse.py:374
msgid "Upload Folder…"
msgstr "Subir carpeta…"

#: pynicotine/gtkgui/userbrowse.py:396
msgid "Download Folder _To…"
msgstr "Descargar carpeta _en…"

#: pynicotine/gtkgui/userbrowse.py:600
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"La lista de archivos compartidos del usuario está vacía. Bien porque el "
"usuario no está compartiendo nada, o bien porque está compartiendo archivos "
"de forma privada."

#: pynicotine/gtkgui/userbrowse.py:615
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"No ha sido posible solicitar los archivos compartidos al usuario. Bien "
"porque el usuario está desconectado, bien porque ambos tenéis el puerto de "
"escucha cerrado, o bien porque hay un problema temporal de conectividad."

#: pynicotine/gtkgui/userbrowse.py:953
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Seleccione el destino para descargar múltiples carpetas"

#: pynicotine/gtkgui/userbrowse.py:997
msgid "Upload Folder (with Subfolders) To User"
msgstr "Subir carpeta (con subcarpetas) al usuario"

#: pynicotine/gtkgui/userbrowse.py:999
msgid "Upload Folder To User"
msgstr "Subir carpeta al usuario"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
msgid "Enter the name of the user you want to upload to:"
msgstr "Introduce el nombre del usuario al que deseas subir archivos:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
msgid "_Upload"
msgstr "_Subir"

#: pynicotine/gtkgui/userbrowse.py:1139
msgid "Select Destination Folder for Files"
msgstr "Seleccione carpeta de destino para el/los archivo(s)"

#: pynicotine/gtkgui/userbrowse.py:1161
msgid "Upload File(s) To User"
msgstr "Subir archivo(s) al usuario"

#: pynicotine/gtkgui/userinfo.py:376
msgid "Copy Picture"
msgstr "Copiar la imagen"

#: pynicotine/gtkgui/userinfo.py:377
msgid "Save Picture"
msgstr "Guardar imagen"

#: pynicotine/gtkgui/userinfo.py:468
#, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "No se ha podido cargar la imagen del usuario %(user)s: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"No ha sido posible solicitar la información del usuario. Bien porque ambos "
"tenéis el puerto de escucha cerrado, bien porque el usuario está "
"desconectado, o bien porque hay un problema temporal de conectividad."

#: pynicotine/gtkgui/userinfo.py:577
msgid "Remove _Buddy"
msgstr "Eliminar _amigo"

#: pynicotine/gtkgui/userinfo.py:577
msgid "Add _Buddy"
msgstr "Añadir _amigo"

#: pynicotine/gtkgui/userinfo.py:581
msgid "Unban User"
msgstr "Retirar veto al usuario"

#: pynicotine/gtkgui/userinfo.py:585
msgid "Unignore User"
msgstr "Dejar de ignorar al usuario"

#: pynicotine/gtkgui/userinfo.py:613
msgid "Yes"
msgstr "Sí"

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr "No"

#: pynicotine/gtkgui/userinfo.py:773
msgid "Please enter number of days."
msgstr "Por favor ingrese el número de días."

#: pynicotine/gtkgui/userinfo.py:787
#, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Regalar días de tus privilegios de Soulseek al usuario %(user)s "
"(%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, python-format
msgid "%(days)s days left"
msgstr "%(days)s días restantes"

#: pynicotine/gtkgui/userinfo.py:795
msgid "Gift Privileges"
msgstr "Regalar privilegios"

#: pynicotine/gtkgui/userinfo.py:797
msgid "_Give Privileges"
msgstr "_Otorgar privilegios"

#: pynicotine/gtkgui/widgets/dialogs.py:309
msgid "Close"
msgstr "Cerrar"

#: pynicotine/gtkgui/widgets/dialogs.py:488
msgid "_Yes"
msgstr "_Sí"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
msgid "_OK"
msgstr "_Aceptar"

#: pynicotine/gtkgui/widgets/filechooser.py:39
msgid "Select a File"
msgstr "Seleccionar un archivo"

#: pynicotine/gtkgui/widgets/filechooser.py:174
msgid "Select a Folder"
msgstr "Seleccionar una carpeta"

#: pynicotine/gtkgui/widgets/filechooser.py:179
msgid "_Select"
msgstr "_Seleccionar"

#: pynicotine/gtkgui/widgets/filechooser.py:196
msgid "Select an Image"
msgstr "Seleccionar una imagen"

#: pynicotine/gtkgui/widgets/filechooser.py:203
msgid "All images"
msgstr "Todas las imágenes"

#: pynicotine/gtkgui/widgets/filechooser.py:241
msgid "Save as…"
msgstr "Guardar como…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
msgid "(None)"
msgstr "(Ninguno)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
msgid "Close Tab"
msgstr "Cerrar pestaña"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
msgid "Close All Tabs?"
msgstr "¿Cerrar todas las pestañas?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
msgid "Do you really want to close all tabs?"
msgstr "¿De verdad quieres cerrar todas las pestañas?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, python-format
msgid "%i Unread Tab(s)"
msgstr "%i Pestaña(s) no leída(s)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
msgid "All Tabs"
msgstr "Todas las pestañas"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
msgid "Re_open Closed Tab"
msgstr "Re_abrir la pestaña cerrada"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, python-format
msgid "%s File(s) Selected"
msgstr "%s Archivo(s) seleccionado(s)"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
msgid "_Browse Files"
msgstr "_Explorar archivos"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
msgid "_Add Buddy"
msgstr "_Añadir amigo"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
msgid "Show IP A_ddress"
msgstr "Ver _dirección IP"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
msgid "Private Rooms"
msgstr "Salas privadas"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, python-format
msgid "Remove from Private Room %s"
msgstr "Eliminar de la sala privada %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, python-format
msgid "Add to Private Room %s"
msgstr "Añadir a la sala privada %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, python-format
msgid "Remove as Operator of %s"
msgstr "Eliminar como operador de %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, python-format
msgid "Add as Operator of %s"
msgstr "Añadir como operador de %s"

#: pynicotine/gtkgui/widgets/textentry.py:37
msgid "Send message…"
msgstr "Enviar mensaje…"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
msgid "Find Previous Match"
msgstr "Buscar coincidencia anterior"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
msgid "Find Next Match"
msgstr "Buscar siguiente coincidencia"

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr "--- mensajes antiguos arriba ---"

#: pynicotine/gtkgui/widgets/theme.py:243
msgid "Executable"
msgstr "Ejecutable"

#: pynicotine/gtkgui/widgets/theme.py:244
msgid "Audio"
msgstr "Audio"

#: pynicotine/gtkgui/widgets/theme.py:245
msgid "Image"
msgstr "Imagen"

#: pynicotine/gtkgui/widgets/theme.py:246
msgid "Archive"
msgstr "Archivo"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
msgid "Miscellaneous"
msgstr "Varios"

#: pynicotine/gtkgui/widgets/theme.py:248
msgid "Video"
msgstr "Video"

#: pynicotine/gtkgui/widgets/theme.py:249
msgid "Document"
msgstr "Documento"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr "Texto"

#: pynicotine/gtkgui/widgets/theme.py:357
#, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "Error al cargar el icono personalizado %(path)s: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Hide Nicotine+"
msgstr "Ocultar Nicotine+"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Show Nicotine+"
msgstr "Mostrar Nicotine+"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, python-format
msgid "Column #%i"
msgstr "Columna #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
msgid "Ungrouped"
msgstr "Desagrupado"

#: pynicotine/gtkgui/widgets/treeview.py:960
msgid "Group by Folder"
msgstr "Agrupar por carpeta"

#: pynicotine/gtkgui/widgets/treeview.py:963
msgid "Group by User"
msgstr "Agrupar por usuario"

#: pynicotine/headless/application.py:77
#, python-format
msgid "Do you really want to exit? %s"
msgstr "¿Realmente quieres salir? %s"

#: pynicotine/headless/application.py:81
#, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr "El usuario %s ya existe y la contraseña introducida no es válida."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr ""
"Escriba %s para iniciar sesión con otro nombre de usuario o contraseña."

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
msgid "Password: "
msgstr "Contraseña: "

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Para crear una nueva cuenta de Soulseek, introduce el nombre de usuario y la "
"contraseña que desees. Si ya tienes una cuenta, introduce tus datos de "
"acceso."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr "Los siguientes compartidos no están disponibles:"

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr "¿Volver a escanear? %s"

#: pynicotine/logfacility.py:181
#, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr ""
"No se pudo escribir en el archivo de registro «%(filename)s»: %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "No se ha podido acceder al archivo de registro %(path)s: %(error)s"

#: pynicotine/networkfilter.py:40
msgid "Andorra"
msgstr "Andorra"

#: pynicotine/networkfilter.py:41
msgid "United Arab Emirates"
msgstr "Emiratos Árabes Unidos"

#: pynicotine/networkfilter.py:42
msgid "Afghanistan"
msgstr "Afganistán"

#: pynicotine/networkfilter.py:43
msgid "Antigua & Barbuda"
msgstr "Antigua y Barbuda"

#: pynicotine/networkfilter.py:44
msgid "Anguilla"
msgstr "Anguila"

#: pynicotine/networkfilter.py:45
msgid "Albania"
msgstr "Albania"

#: pynicotine/networkfilter.py:46
msgid "Armenia"
msgstr "Armenia"

#: pynicotine/networkfilter.py:47
msgid "Angola"
msgstr "Angola"

#: pynicotine/networkfilter.py:48
msgid "Antarctica"
msgstr "Antártida"

#: pynicotine/networkfilter.py:49
msgid "Argentina"
msgstr "Argentina"

#: pynicotine/networkfilter.py:50
msgid "American Samoa"
msgstr "Samoa Americana"

#: pynicotine/networkfilter.py:51
msgid "Austria"
msgstr "Austria"

#: pynicotine/networkfilter.py:52
msgid "Australia"
msgstr "Australia"

#: pynicotine/networkfilter.py:53
msgid "Aruba"
msgstr "Aruba"

#: pynicotine/networkfilter.py:54
msgid "Åland Islands"
msgstr "Aland"

#: pynicotine/networkfilter.py:55
msgid "Azerbaijan"
msgstr "Azerbaiyán"

#: pynicotine/networkfilter.py:56
msgid "Bosnia & Herzegovina"
msgstr "Bosnia y Herzegovina"

#: pynicotine/networkfilter.py:57
msgid "Barbados"
msgstr "Barbados"

#: pynicotine/networkfilter.py:58
msgid "Bangladesh"
msgstr "Bangladés"

#: pynicotine/networkfilter.py:59
msgid "Belgium"
msgstr "Bélgica"

#: pynicotine/networkfilter.py:60
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: pynicotine/networkfilter.py:61
msgid "Bulgaria"
msgstr "Bulgaria"

#: pynicotine/networkfilter.py:62
msgid "Bahrain"
msgstr "Baréin"

#: pynicotine/networkfilter.py:63
msgid "Burundi"
msgstr "Burundi"

#: pynicotine/networkfilter.py:64
msgid "Benin"
msgstr "Benín"

#: pynicotine/networkfilter.py:65
msgid "Saint Barthelemy"
msgstr "San Bartolomé"

#: pynicotine/networkfilter.py:66
msgid "Bermuda"
msgstr "Bermudas"

#: pynicotine/networkfilter.py:67
msgid "Brunei Darussalam"
msgstr "Brunéi"

#: pynicotine/networkfilter.py:68
msgid "Bolivia"
msgstr "Bolivia"

#: pynicotine/networkfilter.py:69
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Caribe Neerlandés"

#: pynicotine/networkfilter.py:70
msgid "Brazil"
msgstr "Brasil"

#: pynicotine/networkfilter.py:71
msgid "Bahamas"
msgstr "Bahamas"

#: pynicotine/networkfilter.py:72
msgid "Bhutan"
msgstr "Bután"

#: pynicotine/networkfilter.py:73
msgid "Bouvet Island"
msgstr "Isla Bouvet"

#: pynicotine/networkfilter.py:74
msgid "Botswana"
msgstr "Botsuana"

#: pynicotine/networkfilter.py:75
msgid "Belarus"
msgstr "Bielorrusia"

#: pynicotine/networkfilter.py:76
msgid "Belize"
msgstr "Belice"

#: pynicotine/networkfilter.py:77
msgid "Canada"
msgstr "Canadá"

#: pynicotine/networkfilter.py:78
msgid "Cocos (Keeling) Islands"
msgstr "Islas Cocos"

#: pynicotine/networkfilter.py:79
msgid "Democratic Republic of Congo"
msgstr "República Democrática del Congo"

#: pynicotine/networkfilter.py:80
msgid "Central African Republic"
msgstr "República Centroafricana"

#: pynicotine/networkfilter.py:81
msgid "Congo"
msgstr "Congo"

#: pynicotine/networkfilter.py:82
msgid "Switzerland"
msgstr "Suiza"

#: pynicotine/networkfilter.py:83
msgid "Ivory Coast"
msgstr "Costa de Marfil"

#: pynicotine/networkfilter.py:84
msgid "Cook Islands"
msgstr "Islas Cook"

#: pynicotine/networkfilter.py:85
msgid "Chile"
msgstr "Chile"

#: pynicotine/networkfilter.py:86
msgid "Cameroon"
msgstr "Camerún"

#: pynicotine/networkfilter.py:87
msgid "China"
msgstr "China"

#: pynicotine/networkfilter.py:88
msgid "Colombia"
msgstr "Colombia"

#: pynicotine/networkfilter.py:89
msgid "Costa Rica"
msgstr "Costa Rica"

#: pynicotine/networkfilter.py:90
msgid "Cuba"
msgstr "Cuba"

#: pynicotine/networkfilter.py:91
msgid "Cabo Verde"
msgstr "Cabo Verde"

#: pynicotine/networkfilter.py:92
msgid "Curaçao"
msgstr "Curazao"

#: pynicotine/networkfilter.py:93
msgid "Christmas Island"
msgstr "Isla de Navidad"

#: pynicotine/networkfilter.py:94
msgid "Cyprus"
msgstr "Chipre"

#: pynicotine/networkfilter.py:95
msgid "Czechia"
msgstr "República Checa"

#: pynicotine/networkfilter.py:96
msgid "Germany"
msgstr "Alemania"

#: pynicotine/networkfilter.py:97
msgid "Djibouti"
msgstr "Yibuti"

#: pynicotine/networkfilter.py:98
msgid "Denmark"
msgstr "Dinamarca"

#: pynicotine/networkfilter.py:99
msgid "Dominica"
msgstr "Dominica"

#: pynicotine/networkfilter.py:100
msgid "Dominican Republic"
msgstr "República Dominicana"

#: pynicotine/networkfilter.py:101
msgid "Algeria"
msgstr "Argelia"

#: pynicotine/networkfilter.py:102
msgid "Ecuador"
msgstr "Ecuador"

#: pynicotine/networkfilter.py:103
msgid "Estonia"
msgstr "Estonia"

#: pynicotine/networkfilter.py:104
msgid "Egypt"
msgstr "Egipto"

#: pynicotine/networkfilter.py:105
msgid "Western Sahara"
msgstr "Sahara Occidental"

#: pynicotine/networkfilter.py:106
msgid "Eritrea"
msgstr "Eritrea"

#: pynicotine/networkfilter.py:107
msgid "Spain"
msgstr "España"

#: pynicotine/networkfilter.py:108
msgid "Ethiopia"
msgstr "Etiopía"

#: pynicotine/networkfilter.py:109
msgid "Europe"
msgstr "Europa"

#: pynicotine/networkfilter.py:110
msgid "Finland"
msgstr "Finlandia"

#: pynicotine/networkfilter.py:111
msgid "Fiji"
msgstr "Fiyi"

#: pynicotine/networkfilter.py:112
msgid "Falkland Islands (Malvinas)"
msgstr "Islas Malvinas"

#: pynicotine/networkfilter.py:113
msgid "Micronesia"
msgstr "Micronesia"

#: pynicotine/networkfilter.py:114
msgid "Faroe Islands"
msgstr "Islas Feroe"

#: pynicotine/networkfilter.py:115
msgid "France"
msgstr "Francia"

#: pynicotine/networkfilter.py:116
msgid "Gabon"
msgstr "Gabón"

#: pynicotine/networkfilter.py:117
msgid "Great Britain"
msgstr "Gran Bretaña"

#: pynicotine/networkfilter.py:118
msgid "Grenada"
msgstr "Granada"

#: pynicotine/networkfilter.py:119
msgid "Georgia"
msgstr "Georgia"

#: pynicotine/networkfilter.py:120
msgid "French Guiana"
msgstr "Guayana Francesa"

#: pynicotine/networkfilter.py:121
msgid "Guernsey"
msgstr "Guernsey"

#: pynicotine/networkfilter.py:122
msgid "Ghana"
msgstr "Ghana"

#: pynicotine/networkfilter.py:123
msgid "Gibraltar"
msgstr "Gibraltar"

#: pynicotine/networkfilter.py:124
msgid "Greenland"
msgstr "Groenlandia"

#: pynicotine/networkfilter.py:125
msgid "Gambia"
msgstr "Gambia"

#: pynicotine/networkfilter.py:126
msgid "Guinea"
msgstr "Guinea"

#: pynicotine/networkfilter.py:127
msgid "Guadeloupe"
msgstr "Guadalupe"

#: pynicotine/networkfilter.py:128
msgid "Equatorial Guinea"
msgstr "Guinea Ecuatorial"

#: pynicotine/networkfilter.py:129
msgid "Greece"
msgstr "Grecia"

#: pynicotine/networkfilter.py:130
msgid "South Georgia & South Sandwich Islands"
msgstr "Islas Georgias del Sur y Sandwich del Sur"

#: pynicotine/networkfilter.py:131
msgid "Guatemala"
msgstr "Guatemala"

#: pynicotine/networkfilter.py:132
msgid "Guam"
msgstr "Guam"

#: pynicotine/networkfilter.py:133
msgid "Guinea-Bissau"
msgstr "Guinea-Bisáu"

#: pynicotine/networkfilter.py:134
msgid "Guyana"
msgstr "Guyana"

#: pynicotine/networkfilter.py:135
msgid "Hong Kong"
msgstr "Hong Kong"

#: pynicotine/networkfilter.py:136
msgid "Heard & McDonald Islands"
msgstr "Islas Heard y McDonald"

#: pynicotine/networkfilter.py:137
msgid "Honduras"
msgstr "Honduras"

#: pynicotine/networkfilter.py:138
msgid "Croatia"
msgstr "Croacia"

#: pynicotine/networkfilter.py:139
msgid "Haiti"
msgstr "Haití"

#: pynicotine/networkfilter.py:140
msgid "Hungary"
msgstr "Hungría"

#: pynicotine/networkfilter.py:141
msgid "Indonesia"
msgstr "Indonesia"

#: pynicotine/networkfilter.py:142
msgid "Ireland"
msgstr "Irlanda"

#: pynicotine/networkfilter.py:143
msgid "Israel"
msgstr "Israel"

#: pynicotine/networkfilter.py:144
msgid "Isle of Man"
msgstr "Isla de Man"

#: pynicotine/networkfilter.py:145
msgid "India"
msgstr "India"

#: pynicotine/networkfilter.py:146
msgid "British Indian Ocean Territory"
msgstr "Territorio Británico del Océano Índico"

#: pynicotine/networkfilter.py:147
msgid "Iraq"
msgstr "Irak"

#: pynicotine/networkfilter.py:148
msgid "Iran"
msgstr "Irán"

#: pynicotine/networkfilter.py:149
msgid "Iceland"
msgstr "Islandia"

#: pynicotine/networkfilter.py:150
msgid "Italy"
msgstr "Italia"

#: pynicotine/networkfilter.py:151
msgid "Jersey"
msgstr "Jersey"

#: pynicotine/networkfilter.py:152
msgid "Jamaica"
msgstr "Jamaica"

#: pynicotine/networkfilter.py:153
msgid "Jordan"
msgstr "Jordania"

#: pynicotine/networkfilter.py:154
msgid "Japan"
msgstr "Japón"

#: pynicotine/networkfilter.py:155
msgid "Kenya"
msgstr "Kenia"

#: pynicotine/networkfilter.py:156
msgid "Kyrgyzstan"
msgstr "Kirguistán"

#: pynicotine/networkfilter.py:157
msgid "Cambodia"
msgstr "Camboya"

#: pynicotine/networkfilter.py:158
msgid "Kiribati"
msgstr "Kiribati"

#: pynicotine/networkfilter.py:159
msgid "Comoros"
msgstr "Comoras"

#: pynicotine/networkfilter.py:160
msgid "Saint Kitts & Nevis"
msgstr "San Cristóbal y Nieves"

#: pynicotine/networkfilter.py:161
msgid "North Korea"
msgstr "Corea del Norte"

#: pynicotine/networkfilter.py:162
msgid "South Korea"
msgstr "Corea del Sur"

#: pynicotine/networkfilter.py:163
msgid "Kuwait"
msgstr "Kuwait"

#: pynicotine/networkfilter.py:164
msgid "Cayman Islands"
msgstr "Islas Caimán"

#: pynicotine/networkfilter.py:165
msgid "Kazakhstan"
msgstr "Kazajistán"

#: pynicotine/networkfilter.py:166
msgid "Laos"
msgstr "Laos"

#: pynicotine/networkfilter.py:167
msgid "Lebanon"
msgstr "Líbano"

#: pynicotine/networkfilter.py:168
msgid "Saint Lucia"
msgstr "Santa Lucía"

#: pynicotine/networkfilter.py:169
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: pynicotine/networkfilter.py:170
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: pynicotine/networkfilter.py:171
msgid "Liberia"
msgstr "Liberia"

#: pynicotine/networkfilter.py:172
msgid "Lesotho"
msgstr "Lesoto"

#: pynicotine/networkfilter.py:173
msgid "Lithuania"
msgstr "Lituania"

#: pynicotine/networkfilter.py:174
msgid "Luxembourg"
msgstr "Luxemburgo"

#: pynicotine/networkfilter.py:175
msgid "Latvia"
msgstr "Letonia"

#: pynicotine/networkfilter.py:176
msgid "Libya"
msgstr "Libia"

#: pynicotine/networkfilter.py:177
msgid "Morocco"
msgstr "Marruecos"

#: pynicotine/networkfilter.py:178
msgid "Monaco"
msgstr "Mónaco"

#: pynicotine/networkfilter.py:179
msgid "Moldova"
msgstr "Moldavia"

#: pynicotine/networkfilter.py:180
msgid "Montenegro"
msgstr "Montenegro"

#: pynicotine/networkfilter.py:181
msgid "Saint Martin"
msgstr "San Martín"

#: pynicotine/networkfilter.py:182
msgid "Madagascar"
msgstr "Madagascar"

#: pynicotine/networkfilter.py:183
msgid "Marshall Islands"
msgstr "Islas Marshall"

#: pynicotine/networkfilter.py:184
msgid "North Macedonia"
msgstr "Macedonia del Norte"

#: pynicotine/networkfilter.py:185
msgid "Mali"
msgstr "Mali"

#: pynicotine/networkfilter.py:186
msgid "Myanmar"
msgstr "Birmania"

#: pynicotine/networkfilter.py:187
msgid "Mongolia"
msgstr "Mongolia"

#: pynicotine/networkfilter.py:188
msgid "Macau"
msgstr "Macao"

#: pynicotine/networkfilter.py:189
msgid "Northern Mariana Islands"
msgstr "Islas Marianas del Norte"

#: pynicotine/networkfilter.py:190
msgid "Martinique"
msgstr "Martinica"

#: pynicotine/networkfilter.py:191
msgid "Mauritania"
msgstr "Mauritania"

#: pynicotine/networkfilter.py:192
msgid "Montserrat"
msgstr "Montserrat"

#: pynicotine/networkfilter.py:193
msgid "Malta"
msgstr "Malta"

#: pynicotine/networkfilter.py:194
msgid "Mauritius"
msgstr "Mauricio"

#: pynicotine/networkfilter.py:195
msgid "Maldives"
msgstr "Maldivas"

#: pynicotine/networkfilter.py:196
msgid "Malawi"
msgstr "Malaui"

#: pynicotine/networkfilter.py:197
msgid "Mexico"
msgstr "México"

#: pynicotine/networkfilter.py:198
msgid "Malaysia"
msgstr "Malasia"

#: pynicotine/networkfilter.py:199
msgid "Mozambique"
msgstr "Mozambique"

#: pynicotine/networkfilter.py:200
msgid "Namibia"
msgstr "Namibia"

#: pynicotine/networkfilter.py:201
msgid "New Caledonia"
msgstr "Nueva Caledonia"

#: pynicotine/networkfilter.py:202
msgid "Niger"
msgstr "Níger"

#: pynicotine/networkfilter.py:203
msgid "Norfolk Island"
msgstr "Isla Norfolk"

#: pynicotine/networkfilter.py:204
msgid "Nigeria"
msgstr "Nigeria"

#: pynicotine/networkfilter.py:205
msgid "Nicaragua"
msgstr "Nicaragua"

#: pynicotine/networkfilter.py:206
msgid "Netherlands"
msgstr "Países Bajos"

#: pynicotine/networkfilter.py:207
msgid "Norway"
msgstr "Noruega"

#: pynicotine/networkfilter.py:208
msgid "Nepal"
msgstr "Nepal"

#: pynicotine/networkfilter.py:209
msgid "Nauru"
msgstr "Nauru"

#: pynicotine/networkfilter.py:210
msgid "Niue"
msgstr "Niue"

#: pynicotine/networkfilter.py:211
msgid "New Zealand"
msgstr "Nueva Zelanda"

#: pynicotine/networkfilter.py:212
msgid "Oman"
msgstr "Omán"

#: pynicotine/networkfilter.py:213
msgid "Panama"
msgstr "Panamá"

#: pynicotine/networkfilter.py:214
msgid "Peru"
msgstr "Perú"

#: pynicotine/networkfilter.py:215
msgid "French Polynesia"
msgstr "Polinesia Francesa"

#: pynicotine/networkfilter.py:216
msgid "Papua New Guinea"
msgstr "Papúa Nueva Guinea"

#: pynicotine/networkfilter.py:217
msgid "Philippines"
msgstr "Filipinas"

#: pynicotine/networkfilter.py:218
msgid "Pakistan"
msgstr "Pakistán"

#: pynicotine/networkfilter.py:219
msgid "Poland"
msgstr "Polonia"

#: pynicotine/networkfilter.py:220
msgid "Saint Pierre & Miquelon"
msgstr "San Pedro y Miquelón"

#: pynicotine/networkfilter.py:221
msgid "Pitcairn"
msgstr "Islas Pitcairn"

#: pynicotine/networkfilter.py:222
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: pynicotine/networkfilter.py:223
msgid "State of Palestine"
msgstr "Palestina"

#: pynicotine/networkfilter.py:224
msgid "Portugal"
msgstr "Portugal"

#: pynicotine/networkfilter.py:225
msgid "Palau"
msgstr "Palaos"

#: pynicotine/networkfilter.py:226
msgid "Paraguay"
msgstr "Paraguay"

#: pynicotine/networkfilter.py:227
msgid "Qatar"
msgstr "Catar"

#: pynicotine/networkfilter.py:228
msgid "Réunion"
msgstr "Reunión"

#: pynicotine/networkfilter.py:229
msgid "Romania"
msgstr "Rumanía"

#: pynicotine/networkfilter.py:230
msgid "Serbia"
msgstr "Serbia"

#: pynicotine/networkfilter.py:231
msgid "Russia"
msgstr "Rusia"

#: pynicotine/networkfilter.py:232
msgid "Rwanda"
msgstr "Ruanda"

#: pynicotine/networkfilter.py:233
msgid "Saudi Arabia"
msgstr "Arabia Saudí"

#: pynicotine/networkfilter.py:234
msgid "Solomon Islands"
msgstr "Islas Salomón"

#: pynicotine/networkfilter.py:235
msgid "Seychelles"
msgstr "Seychelles"

#: pynicotine/networkfilter.py:236
msgid "Sudan"
msgstr "Sudán"

#: pynicotine/networkfilter.py:237
msgid "Sweden"
msgstr "Suecia"

#: pynicotine/networkfilter.py:238
msgid "Singapore"
msgstr "Singapur"

#: pynicotine/networkfilter.py:239
msgid "Saint Helena"
msgstr "Santa Elena"

#: pynicotine/networkfilter.py:240
msgid "Slovenia"
msgstr "Eslovenia"

#: pynicotine/networkfilter.py:241
msgid "Svalbard & Jan Mayen Islands"
msgstr "Svalbard y Jan Mayen"

#: pynicotine/networkfilter.py:242
msgid "Slovak Republic"
msgstr "Eslovaquia"

#: pynicotine/networkfilter.py:243
msgid "Sierra Leone"
msgstr "Sierra Leona"

#: pynicotine/networkfilter.py:244
msgid "San Marino"
msgstr "San Marino"

#: pynicotine/networkfilter.py:245
msgid "Senegal"
msgstr "Senegal"

#: pynicotine/networkfilter.py:246
msgid "Somalia"
msgstr "Somalia"

#: pynicotine/networkfilter.py:247
msgid "Suriname"
msgstr "Surinam"

#: pynicotine/networkfilter.py:248
msgid "South Sudan"
msgstr "Sudán del Sur"

#: pynicotine/networkfilter.py:249
msgid "Sao Tome & Principe"
msgstr "Santo Tomé y Príncipe"

#: pynicotine/networkfilter.py:250
msgid "El Salvador"
msgstr "El Salvador"

#: pynicotine/networkfilter.py:251
msgid "Sint Maarten"
msgstr "San Martín"

#: pynicotine/networkfilter.py:252
msgid "Syria"
msgstr "Siria"

#: pynicotine/networkfilter.py:253
msgid "Eswatini"
msgstr "Suazilandia"

#: pynicotine/networkfilter.py:254
msgid "Turks & Caicos Islands"
msgstr "Islas Turcas y Caicos"

#: pynicotine/networkfilter.py:255
msgid "Chad"
msgstr "Chad"

#: pynicotine/networkfilter.py:256
msgid "French Southern Territories"
msgstr "Tierras Australes y Antárticas Francesas"

#: pynicotine/networkfilter.py:257
msgid "Togo"
msgstr "Togo"

#: pynicotine/networkfilter.py:258
msgid "Thailand"
msgstr "Tailandia"

#: pynicotine/networkfilter.py:259
msgid "Tajikistan"
msgstr "Tayikistán"

#: pynicotine/networkfilter.py:260
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
msgid "Timor-Leste"
msgstr "Timor Oriental"

#: pynicotine/networkfilter.py:262
msgid "Turkmenistan"
msgstr "Turkmenistán"

#: pynicotine/networkfilter.py:263
msgid "Tunisia"
msgstr "Túnez"

#: pynicotine/networkfilter.py:264
msgid "Tonga"
msgstr "Tonga"

#: pynicotine/networkfilter.py:265
msgid "Türkiye"
msgstr "Turquía"

#: pynicotine/networkfilter.py:266
msgid "Trinidad & Tobago"
msgstr "Trinidad y Tobago"

#: pynicotine/networkfilter.py:267
msgid "Tuvalu"
msgstr "Tuvalu"

#: pynicotine/networkfilter.py:268
msgid "Taiwan"
msgstr "Taiwán"

#: pynicotine/networkfilter.py:269
msgid "Tanzania"
msgstr "Tanzania"

#: pynicotine/networkfilter.py:270
msgid "Ukraine"
msgstr "Ucrania"

#: pynicotine/networkfilter.py:271
msgid "Uganda"
msgstr "Uganda"

#: pynicotine/networkfilter.py:272
msgid "U.S. Minor Outlying Islands"
msgstr "Islas Ultramarinas Menores de los Estados Unidos"

#: pynicotine/networkfilter.py:273
msgid "United States"
msgstr "Estados Unidos"

#: pynicotine/networkfilter.py:274
msgid "Uruguay"
msgstr "Uruguay"

#: pynicotine/networkfilter.py:275
msgid "Uzbekistan"
msgstr "Uzbekistán"

#: pynicotine/networkfilter.py:276
msgid "Holy See (Vatican City State)"
msgstr "Ciudad del Vaticano"

#: pynicotine/networkfilter.py:277
msgid "Saint Vincent & The Grenadines"
msgstr "San Vicente y las Granadinas"

#: pynicotine/networkfilter.py:278
msgid "Venezuela"
msgstr "Venezuela"

#: pynicotine/networkfilter.py:279
msgid "British Virgin Islands"
msgstr "Islas Vírgenes Británicas"

#: pynicotine/networkfilter.py:280
msgid "U.S. Virgin Islands"
msgstr "Islas Vírgenes de los Estados Unidos"

#: pynicotine/networkfilter.py:281
msgid "Viet Nam"
msgstr "Vietnam"

#: pynicotine/networkfilter.py:282
msgid "Vanuatu"
msgstr "Vanuatu"

#: pynicotine/networkfilter.py:283
msgid "Wallis & Futuna"
msgstr "Wallis y Futuna"

#: pynicotine/networkfilter.py:284
msgid "Samoa"
msgstr "Samoa"

#: pynicotine/networkfilter.py:285
msgid "Yemen"
msgstr "Yemen"

#: pynicotine/networkfilter.py:286
msgid "Mayotte"
msgstr "Mayotte"

#: pynicotine/networkfilter.py:287
msgid "South Africa"
msgstr "Sudáfrica"

#: pynicotine/networkfilter.py:288
msgid "Zambia"
msgstr "Zambia"

#: pynicotine/networkfilter.py:289
msgid "Zimbabwe"
msgstr "Zimbabue"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "La síntesis de voz para el mensaje ha fallado: %s"

#: pynicotine/nowplaying.py:130
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr ""
"Last.fm: Por favor, proporciona tu nombre de usuario de Last.fm y tu clave "
"API"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
msgid "Now Playing Error"
msgstr "Error de «Reproduciendo ahora»"

#: pynicotine/nowplaying.py:140
#, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm: No se pudo conectar con Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:162
#, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr ""
"Last.fm: No se pudo obtener la pista reciente de Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:196
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: No se ha podido encontrar un reproductor MPRIS adecuado"

#: pynicotine/nowplaying.py:201
#, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr ""
"Encontrados múltiples reproductores MPRIS: %(players)s. Usando: %(player)s"

#: pynicotine/nowplaying.py:204
#, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Reproductor MPRIS autodetectado: %s"

#: pynicotine/nowplaying.py:219
#, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr "MPRIS: Algo ha ido mal al consultar %(player)s: %(exception)s"

#: pynicotine/nowplaying.py:266
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr ""
"ListenBrainz: Por favor, proporciona tu nombre de usuario de ListenBrainz"

#: pynicotine/nowplaying.py:275
#, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "ListenBrainz: No se pudo conectar con ListenBrainz: %(error)s"

#: pynicotine/nowplaying.py:283
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "ListenBrainz: Parece que no estás escuchando nada en este momento"

#: pynicotine/nowplaying.py:297
#, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz: No se pudo obtener la pista actual de ListenBrainz: %(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
msgid "Network Filters"
msgstr "Filtros de la red"

#: pynicotine/plugins/core_commands/__init__.py:44
msgid "List available commands"
msgstr "Lista de comandos disponibles"

#: pynicotine/plugins/core_commands/__init__.py:49
msgid "Connect to the server"
msgstr "Conectarse al servidor"

#: pynicotine/plugins/core_commands/__init__.py:53
msgid "Disconnect from the server"
msgstr "Desconectarse del servidor"

#: pynicotine/plugins/core_commands/__init__.py:58
msgid "Toggle away status"
msgstr "Alternar el estado"

#: pynicotine/plugins/core_commands/__init__.py:62
msgid "Manage plugins"
msgstr "Administrar complementos"

#: pynicotine/plugins/core_commands/__init__.py:74
msgid "Clear chat window"
msgstr "Limpiar la ventana del chat"

#: pynicotine/plugins/core_commands/__init__.py:80
msgid "Say something in the third-person"
msgstr "Decir algo en tercera persona"

#: pynicotine/plugins/core_commands/__init__.py:87
msgid "Announce the song currently playing"
msgstr "Informa la canción que se está reproduciendo actualmente"

#: pynicotine/plugins/core_commands/__init__.py:94
msgid "Join chat room"
msgstr "Únete a la sala del chat"

#: pynicotine/plugins/core_commands/__init__.py:102
msgid "Leave chat room"
msgstr "Abandonar la sala del chat"

#: pynicotine/plugins/core_commands/__init__.py:110
msgid "Say message in specified chat room"
msgstr "Decir un mensaje a la sala de chat especificada"

#: pynicotine/plugins/core_commands/__init__.py:117
msgid "Open private chat"
msgstr "Abrir un chat privado"

#: pynicotine/plugins/core_commands/__init__.py:125
msgid "Close private chat"
msgstr "Cerrar el chat privado"

#: pynicotine/plugins/core_commands/__init__.py:133
msgid "Request user's client version"
msgstr "Solicitar información del usuario"

#: pynicotine/plugins/core_commands/__init__.py:142
msgid "Send private message to user"
msgstr "Enviar un mensaje privado al usuario"

#: pynicotine/plugins/core_commands/__init__.py:150
msgid "Add user to buddy list"
msgstr "Añadir al usuario a tu lista de amigos"

#: pynicotine/plugins/core_commands/__init__.py:158
msgid "Remove buddy from buddy list"
msgstr "Eliminar al usuario de tu lista de amigos"

#: pynicotine/plugins/core_commands/__init__.py:166
msgid "Browse files of user"
msgstr "Explorar los archivos del usuario"

#: pynicotine/plugins/core_commands/__init__.py:175
msgid "Show user profile information"
msgstr "Mostrar la información del perfil del usuario"

#: pynicotine/plugins/core_commands/__init__.py:183
msgid "Show IP address or username"
msgstr "Mostrar la dirección IP o el nombre del usuario"

#: pynicotine/plugins/core_commands/__init__.py:190
msgid "Block connections from user or IP address"
msgstr "Bloquear las conexiones del usuario o la dirección IP"

#: pynicotine/plugins/core_commands/__init__.py:197
msgid "Remove user or IP address from ban lists"
msgstr "Eliminar al usuario o la dirección IP de las listas de baneados"

#: pynicotine/plugins/core_commands/__init__.py:204
msgid "Silence messages from user or IP address"
msgstr "Silenciar los mensajes del usuario o la dirección IP"

#: pynicotine/plugins/core_commands/__init__.py:212
msgid "Remove user or IP address from ignore lists"
msgstr "Eliminar al usuario o la dirección IP de las listas de ignorados"

#: pynicotine/plugins/core_commands/__init__.py:220
msgid "Add share"
msgstr "Añadir una acción"

#: pynicotine/plugins/core_commands/__init__.py:226
msgid "Remove share"
msgstr "Eliminar una acción"

#: pynicotine/plugins/core_commands/__init__.py:233
msgid "List shares"
msgstr "Lista de compartidos"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Reescanear los compartidos"

#: pynicotine/plugins/core_commands/__init__.py:246
msgid "Start global file search"
msgstr "Iniciar la búsqueda global de archivos"

#: pynicotine/plugins/core_commands/__init__.py:254
msgid "Search files in joined rooms"
msgstr "Buscar los archivos en salas conectadas"

#: pynicotine/plugins/core_commands/__init__.py:262
msgid "Search files of all buddies"
msgstr "Buscar los archivos de todos los amigos"

#: pynicotine/plugins/core_commands/__init__.py:270
msgid "Search a user's shared files"
msgstr "Buscar los archivos compartidos de un usuario"

#: pynicotine/plugins/core_commands/__init__.py:296
#, python-format
msgid "Listing %(num)i available commands:"
msgstr "Listado de %(num)i comandos disponibles:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr ""
"Listado de %(num)i comandos disponibles que coinciden con \"%(query)s\":"

#: pynicotine/plugins/core_commands/__init__.py:311
#, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Escribe %(command)s para enumerar los comandos similares"

#: pynicotine/plugins/core_commands/__init__.py:314
#, python-format
msgid "Type %(command)s to list available commands"
msgstr "Escribe los %(command)s para listar los comandos disponibles"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, python-format
msgid "Not joined in room %s"
msgstr "No esta conectado a la sala %s"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr "No enviar ningún mensajes al usuario %s"

#: pynicotine/plugins/core_commands/__init__.py:408
#, python-format
msgid "Closed private chat of user %s"
msgstr "Cerrado el chat privado con el usuario %s"

#: pynicotine/plugins/core_commands/__init__.py:476
#, python-format
msgid "Banned %s"
msgstr "Baneado %s"

#: pynicotine/plugins/core_commands/__init__.py:490
#, python-format
msgid "Unbanned %s"
msgstr "Desbaneado %s"

#: pynicotine/plugins/core_commands/__init__.py:503
#, python-format
msgid "Ignored %s"
msgstr "Ignorado %s"

#: pynicotine/plugins/core_commands/__init__.py:517
#, python-format
msgid "Unignored %s"
msgstr "Dejar de ignorar %s"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr "%(num_listed)s compartidos listados (%(num_total)s configuradas)"

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr "No se puede acceder a la carpeta \"%s\" y no se puede compartir"

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""
"Añadido %(group_name)s y compartir \"%(virtual_name)s\" (es necesario volver "
"a escanear)"

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr "No hay ninguna acción con el nombre \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr ""
"Se ha eliminado el recurso compartido \"%s\" (es necesario volver a "
"escanearlo)"

#: pynicotine/pluginsystem.py:413
msgid "Loading plugin system"
msgstr "Cargando sistema de complementos"

#: pynicotine/pluginsystem.py:516
#, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"No ha sido posible cargar el complemento %(name)s. El nombre de la carpeta "
"del complemento contiene caracteres inválidos: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "Comando %(interface)s conflictivo en el plugin %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, python-format
msgid "Loaded plugin %s"
msgstr "Complemento «%s» habilitado"

#: pynicotine/pluginsystem.py:579
#, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"No ha sido posible cargar el complemento %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, python-format
msgid "Unloaded plugin %s"
msgstr "Complemento «%s» deshabilitado"

#: pynicotine/pluginsystem.py:648
#, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"No ha sido posible descargar el complemento %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"El complemento «%(module)s» falló con el error %(errortype)s: %(error)s.\n"
"Traza: %(trace)s"

#: pynicotine/pluginsystem.py:810
msgid "No description"
msgstr "Sin descripción"

#: pynicotine/pluginsystem.py:887
#, python-format
msgid "Missing %s argument"
msgstr "Falta el argumento %s"

#: pynicotine/pluginsystem.py:896
#, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Argumento no válido, opciones posibles: %s"

#: pynicotine/pluginsystem.py:901
#, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Uso: %(command)s %(parameters)s"

#: pynicotine/pluginsystem.py:940
#, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Comando desconocido: %(command)s. Escribe %(help_command)s para ver los "
"comandos disponibles."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
msgid "No UPnP devices found"
msgstr "No se han encontrado dispositivos UPnP"

#: pynicotine/portmapper.py:633
#, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"%(protocol)s: Error al reenviar el puerto externo %(external_port)s : "
"%(error)s"

#: pynicotine/portmapper.py:647
#, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"%(protocol)s: Puerto externo %(external_port)s reenviado con éxito a la "
"dirección IP local %(ip_address)s puerto %(local_port)s"

#: pynicotine/privatechat.py:220
#, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Mensaje privado del usuario «%(user)s»: %(message)s"

#: pynicotine/search.py:368
#, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Buscando el elemento de la lista de deseos \"%s\""

#: pynicotine/search.py:434
#, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr ""
"El período de espera de la lista de deseos se ha establecido en %s segundos"

#: pynicotine/search.py:760
#, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr ""
"El usuario %(user)s está buscando «%(query)s», se han encontrado %(num)i "
"resultados"

#: pynicotine/shares.py:302
msgid "Rebuilding shares…"
msgstr "Reconstruyendo los compartidos…"

#: pynicotine/shares.py:302
msgid "Rescanning shares…"
msgstr "Reescaneando compartidos…"

#: pynicotine/shares.py:324
#, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Reescaneo completo: %(num)s carpetas encontradas"

#: pynicotine/shares.py:334
#, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Se ha producido un error grave al reescanear los compartidos. Si el problema "
"persiste, borra %(dir)s/*.dbn e inténtalo de nuevo. Si eso no lo soluciona, "
"por favor, envía un informe de error con este stack trace incluido: %(trace)s"

#: pynicotine/shares.py:582
#, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Error al escanear el archivo %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Error al escanear la carpeta %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "Error al escanear los metadatos del archivo %(path)s: %(error)s"

#: pynicotine/shares.py:1046
#, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Reescaneo interrumpido debido a compartidos no disponibles: %s"

#: pynicotine/shares.py:1184
#, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "El usuario %(user)s está explorando tu lista de archivos compartidos"

#: pynicotine/slskmessages.py:3120
#, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"No ha sido posible leer la base de datos de compartidos. Por favor, "
"reescanea tus compartidos. Error: %s"

#: pynicotine/slskproto.py:500
#, python-format
msgid "Specified network interface '%s' is not available"
msgstr "La interfaz de red especificada '%s' no está disponible"

#: pynicotine/slskproto.py:511
#, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"No se puede escuchar en el puerto %(port)s. Asegúrate de que ningún otro "
"programa esté utilizando ese puerto o elige un puerto diferente. Error: "
"%(error)s"

#: pynicotine/slskproto.py:523
#, python-format
msgid "Listening on port: %i"
msgstr "Escuchando en el puerto %i"

#: pynicotine/slskproto.py:805
#, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "No se puede conectar al servidor %(host)s:%(port)s: %(error)s"

#: pynicotine/slskproto.py:1095
#, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Reconexión al servidor en %s segundo(s)"

#: pynicotine/slskproto.py:1170
#, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Conectando a %(host)s:%(port)s"

#: pynicotine/slskproto.py:1208
#, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Conectado al servidor %(host)s:%(port)s, iniciando sesión…"

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Desconectado del servidor %(host)s:%(port)s"

#: pynicotine/slskproto.py:1499
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Alguien ha iniciado sesión con tu cuenta de Soulseek en otro lugar"

#: pynicotine/uploads.py:382
#, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Subida terminada: usuario %(user)s, dirección IP %(ip)s, archivo %(file)s"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Subida interrumpida: usuario %(user)s, archivo %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, python-format
msgid "Upload I/O error: %s"
msgstr "Error de E/S en la subida: %s"

#: pynicotine/uploads.py:1103
#, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Subida iniciada: usuario %(user)s, dirección IP %(ip)s, archivo %(file)s"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr "No se puede crear el directorio «%(folder)s», error: %(error)s"

#: pynicotine/userbrowse.py:236
#, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "La carga de compartidos desde el disco ha fallado: %(error)s"

#: pynicotine/userbrowse.py:282
#, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr ""
"Lista de archivos compartidos del usuario «%(user)s» guardada en %(dir)s"

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr ""
"No se pueden guardar los compartidos. Error de «%(user)s» reportado: "
"%(error)s"

#: pynicotine/userinfo.py:160
#, python-format
msgid "Picture saved to %s"
msgstr "Imagen guardada en %s"

#: pynicotine/userinfo.py:163
#, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "No se ha podido guardar la imagen en %(filename)s: %(error)s"

#: pynicotine/userinfo.py:190
#, python-format
msgid "User %(user)s is viewing your profile"
msgstr "Usuario %(user)s está viendo tu perfil"

#: pynicotine/users.py:272
#, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "No ha sido posible conectar al servidor. Razón: %s"

#: pynicotine/users.py:272
msgid "Cannot Connect"
msgstr "No se puede conectar"

#: pynicotine/users.py:306
#, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr ""
"No se puede obtener la IP del usuario %s, ya que el usuario está desconectado"

#: pynicotine/users.py:315
#, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr ""
"La dirección IP del usuario %(user)s: %(ip)s, puerto %(port)i%(country)s"

#: pynicotine/users.py:433
msgid "Soulseek Announcement"
msgstr "Anuncio de Soulseek"

#: pynicotine/users.py:449
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"No tienes privilegios en Soulseek. Mientras los privilegios estén activos, "
"tus descargas se pondrán en cola antes que las de los usuarios sin "
"privilegios."

#: pynicotine/users.py:455
#, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"Quedan %(days)i días, %(hours)i horas, %(minutes)i minutos y %(seconds)i "
"segundos de privilegios de Soulseek"

#: pynicotine/users.py:473
msgid "Your password has been changed"
msgstr "Tu contraseña ha sido modificada"

#: pynicotine/users.py:473
msgid "Password Changed"
msgstr "Contraseña modificada"

#: pynicotine/utils.py:574
#, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "No se ha podido abrir la ruta del archivo %(path)s: %(error)s"

#: pynicotine/utils.py:621
#, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "No se ha podido abrir la URL %(url)s: %(error)s"

#: pynicotine/utils.py:646
#, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr "Algo salió mal al leer el archivo %(filename)s: %(error)s"

#: pynicotine/utils.py:651
#, python-format
msgid "Attempting to load backup of file %s"
msgstr "Intentando cargar la copia de seguridad del archivo %s"

#: pynicotine/utils.py:673
#, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr ""
"No ha sido posible hacer una copia de seguridad del archivo %(path)s: "
"%(error)s"

#: pynicotine/utils.py:694
#, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "No ha sido posible guardar el archivo %(path)s: %(error)s"

#: pynicotine/utils.py:705
#, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "No ha sido posible restaurar el archivo anterior %(path)s: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
msgid "Add buddy…"
msgstr "Añadir amigo…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
msgid "Toggle Text-to-Speech"
msgstr "Activar/desactivar la conversión de texto a voz"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
msgid "Chat Room Command Help"
msgstr "Ayuda con los comandos de la sala del chat"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
msgid "_Log"
msgstr "_Registro"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
msgid "Room Wall"
msgstr "Pared de la habitación"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
msgid "R_oom Wall"
msgstr "Mur_o de la sala"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
msgid "Created by"
msgstr "Creado por"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
msgid "Translated by"
msgstr "Traducido por"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
msgid "License"
msgstr "Licencia"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
msgid "Welcome to Nicotine+"
msgstr "Bienvenido a Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Si el nombre de usuario que elijas ya está ocupado, se te pedirá que lo "
"cambies."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""
"Para conectarte con otros usuarios de Soulseek, debes redirigir un puerto de "
"escucha de tu router a tu ordenador."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Si tu puerto de escucha está cerrado solo podrás conectarte a los usuarios "
"cuyos puertos de escucha estén abiertos."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"Si es necesario, elige otro puerto de escucha a continuación o hazlo más "
"tarde en las preferencias."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
msgid "Download Files to Folder"
msgstr "Descargar archivos a esta carpeta"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
msgid "Share Folders"
msgstr "Compartir carpetas"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Los usuarios de Soulseek podrán descargar tus compartidos. Contribuye a la "
"red Soulseek compartiendo tus propios ficheros y los que hayas descargado de "
"otros usuarios."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
msgid "You are ready to use Nicotine+!"
msgstr "¡Estás listo para usar Nicotine+!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""
"Soulseek es un protocolo no cifrado que no está pensado para la comunicación "
"segura."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Donar a Soulseek te otorga privilegios durante un determinado periodo de "
"tiempo. Si tienes privilegios tus descargas se pondrán en cola antes que las "
"de los usuarios sin privilegios."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
msgid "Previous File"
msgstr "Archivo anterior"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
msgid "Next File"
msgstr "Archivo siguiente"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
msgid "Name"
msgstr "Nombre"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
msgid "Last Speed"
msgstr "Última velocidad"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
msgid "_Export…"
msgstr "_Exportar…"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
msgid "Keyboard Shortcuts"
msgstr "Atajos de teclado"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
msgid "General"
msgstr "General"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr "Conectar"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr "Desconectar"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
msgid "Rescan Shares"
msgstr "Reescanear compartidos"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
msgid "Show Log Pane"
msgstr "Mostrar panel del registro"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
msgid "Confirm Quit"
msgstr "Confirma que quieres salir"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
msgid "Quit"
msgstr "Salir"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
msgid "Menus"
msgstr "Menús"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
msgid "Open Main Menu"
msgstr "Abrir menú principal"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
msgid "Open Context Menu"
msgstr "Abrir menú contextual"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
msgid "Tabs"
msgstr "Pestañas"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
msgid "Change Main Tab"
msgstr "Cambiar pestaña principal"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
msgid "Go to Previous Secondary Tab"
msgstr "Ir a pestaña secundaria anterior"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
msgid "Go to Next Secondary Tab"
msgstr "Ir a la pestaña secundaria siguiente"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
msgid "Reopen Closed Secondary Tab"
msgstr "Reabrir una pestaña secundaria cerrada"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
msgid "Close Secondary Tab"
msgstr "Cerrar pestaña secundaria"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
msgid "Lists"
msgstr "Listas"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
msgid "Copy Selected Cell"
msgstr "Copiar celda seleccionada"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
msgid "Select All"
msgstr "Seleccionar todo"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
msgid "Find"
msgstr "Encontrar"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
msgid "Remove Selected Row"
msgstr "Eliminar fila seleccionada"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
msgid "Editing"
msgstr "Edición"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
msgid "Cut"
msgstr "Cortar"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
msgid "Paste"
msgstr "Pegar"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
msgid "Insert Emoji"
msgstr "Insertar emoji"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
msgid "File Transfers"
msgstr "Transferencias de archivos"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
msgid "Resume / Retry Transfer"
msgstr "Reanudar/reintentar transferencia"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
msgid "Pause / Abort Transfer"
msgstr "Pausar/interrumpir transferencia"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
msgid "Download / Upload To"
msgstr "Descargar/Subir a"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
msgid "Save List to Disk"
msgstr "Guardar lista en disco"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
msgid "Refresh"
msgstr "Actualizar"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
msgid "Expand / Collapse All"
msgstr "Expandir/Contraer todo"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
msgid "Back to Parent Folder"
msgstr "Regresar a la carpeta superior"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
msgid "File Search"
msgstr "Búsqueda de archivos"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
msgid "Result Filters"
msgstr "Filtros de resultados"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
msgid "Current Session"
msgstr "Sesión actual"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
msgid "Completed Downloads"
msgstr "Descargas completadas"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
msgid "Downloaded Size"
msgstr "Datos descargados"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
msgid "Completed Uploads"
msgstr "Subidas completadas"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
msgid "Uploaded Size"
msgstr "Datos subidos"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
msgid "Total"
msgstr "Total"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
msgid "_Reset…"
msgstr "_Reiniciar…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Los elementos de la lista de deseos se buscan automáticamente a intervalos "
"regulares para descubrir archivos poco comunes."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
msgid "Add Wish…"
msgstr "Añadir deseo…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
msgid "Clear All…"
msgstr "Limpiar todo…"

#: pynicotine/gtkgui/ui/downloads.ui:138
msgid "Clear All Finished/Filtered Downloads"
msgstr "Borrar todas las descargas finalizadas/filtradas"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
msgid "Clear Finished"
msgstr "Limpiar terminadas"

#: pynicotine/gtkgui/ui/downloads.ui:169
msgid "Clear Specific Downloads"
msgstr "Borrar las descargas específicadas"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
msgid "Clear _All…"
msgstr "_Limpiar todo…"

#: pynicotine/gtkgui/ui/interests.ui:21
msgid "Personal Interests"
msgstr "Intereses personales"

#: pynicotine/gtkgui/ui/interests.ui:40
msgid "Add something you like…"
msgstr "Añade algo que te guste…"

#: pynicotine/gtkgui/ui/interests.ui:62
msgid "Personal Dislikes"
msgstr "Aversiones personales"

#: pynicotine/gtkgui/ui/interests.ui:80
msgid "Add something you dislike…"
msgstr "Añade algo que no te guste…"

#: pynicotine/gtkgui/ui/interests.ui:143
msgid "Refresh Recommendations"
msgstr "Actualizar las recomendaciones"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
msgid "Main Menu"
msgstr "Menú principal"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
msgid "Room…"
msgstr "Sala…"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
msgid "Username…"
msgstr "Nombre de usuario…"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
msgid "Search term…"
msgstr "Término de búsqueda…"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Limpiar"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Patrones de búsqueda: con una palabra = término, sin palabra = -término, "
"palabra parcial = *érmino"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
msgid "Search Scope"
msgstr "Rango de la búsqueda"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
msgid "_Wishlist"
msgstr "Lista de _deseos"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
msgid "Configure Searches"
msgstr "Configurar las búsquedas"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Introduzca un término de búsqueda para buscar archivos compartidos por otros "
"usuarios en la red Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
msgid "File Grouping Mode"
msgstr "Modo de agrupación de los archivos"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
msgid "Configure Downloads"
msgstr "Configurar las descargas"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Los archivos que descargues de otros usuarios se ponen en cola aquí, y se "
"pueden pausar y reanudar cuando se desee"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
msgid "Configure Uploads"
msgstr "Configurar las subidas"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Los intentos de otros usuarios de descargar tus archivos compartidos se "
"ponen en cola y se gestionan aquí"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
msgid "_Open List"
msgstr "_Abrir lista"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Abre una lista local de archivos compartidos que se ha guardado previamente "
"en el disco"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
msgid "Configure Shares"
msgstr "Configurar compartidos"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Introduce el nombre del usuario cuya lista de archivos compartidos desees "
"explorar. También puedes guardar la lista en el disco e inspeccionarla "
"posteriormente."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
msgid "_Personal Profile"
msgstr "_Perfil personal"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
msgid "Configure Account"
msgstr "Configurar la cuenta"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Introduzca el nombre de un usuario para ver su descripción, información y "
"foto personal"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
msgid "Chat _History"
msgstr "_Historial de chat"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
msgid "Configure Chats"
msgstr "Configurar los chats"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr ""
"Introduzca el nombre de un usuario para iniciar una conversación de texto "
"con él en privado"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
msgid "_Message All"
msgstr "_Todos los mensajes"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
msgid "Configure Ignored Users"
msgstr "Configurar los usuarios ignorados"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Añade usuarios a tu lista de amigos para compartir con ellos carpetas "
"específicas y recibir notificaciones cuando estén conectados"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
msgid "Join or create room…"
msgstr "Únete o crea una sala…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Únete a una sala de chat existente, o crea una nueva sala para chatear con "
"otros usuarios de la red Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
msgid "Configure User Profile"
msgstr "Configurar el perfil de usuario"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
msgid "Connections"
msgstr "Conexiones"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
msgid "Downloading (Speed / Active Users)"
msgstr "Descarga (Velocidad / Usuarios activos)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
msgid "Uploading (Speed / Active Users)"
msgstr "Subiendo (Velocidad / Usuarios activos)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
msgid "Search chat history…"
msgstr "Buscar en el historial del chat…"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
msgid "Download Speed Limits"
msgstr "Límites de velocidad de descarga"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
msgid "Unlimited download speed"
msgstr "Velocidad de descarga ilimitada"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
msgid "Use download speed limit (KiB/s):"
msgstr "Utilice el límite de velocidad de descarga (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Utiliza un límite de velocidad de descarga alternativo (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
msgid "Search rooms…"
msgstr "Buscar las salas…"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
msgid "Refresh Rooms"
msgstr "Actualizar las salas"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
msgid "_Show feed of public chat room messages"
msgstr "_Mostrar el flujo de mensajes de las salas de chat públicas"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
msgid "_Accept private room invitations"
msgstr "_Aceptar invitaciones a salas privadas"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Escribe un único mensaje que otros usuarios de la sala podrán leer más "
"tarde. Los mensajes recientes se muestran en la parte superior."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
msgid "Set wall message…"
msgstr "Establecer mensaje en el muro…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
msgid "Search Result Filters"
msgstr "Filtros de resultados de búsqueda"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Los filtros de resultados de búsqueda se utilizan para refinar los "
"resultados de búsqueda mostrados."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Cada lista de resultados de búsqueda tiene su propio filtro, que puede "
"mostrarse pulsando el botón Filtros de resultados. Un filtro se compone de "
"múltiples campos, los cuales se aplican al pulsar «Intro» en cualquiera de "
"sus campos. El filtrado se aplica inmediatamente a los resultados ya "
"recibidos, y también a los que estén por llegar."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Como su propio nombre indica, un filtro de resultados de búsqueda no puede "
"expandir tu búsqueda original, solo puede reducirla. Para ampliar o cambiar "
"tus términos de búsqueda, haz una nueva búsqueda."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
msgid "Result Filter Usage"
msgstr "Utilización de los filtros de resultados"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
msgid "Include Text"
msgstr "Incluir texto"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
msgid "Files, folders and usernames containing this text will be shown."
msgstr ""
"Se mostrarán los archivos, carpetas y nombres de usuario que contienen este "
"texto."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Las mayúsculas o minúsculas son indiferentes, pero el orden de las palabras "
"es importante: 'Instrumental Remix' no mostrará ningún 'Remix Instrumental'"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Utiliza | (o tubos) para separar varias frases exactas. Ejemplo:\n"
"    Remix|Dub Mix|Instrumental"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
msgid "Exclude Text"
msgstr "Excluir texto"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"Como arriba, pero los archivos, carpetas y nombres de usuario se filtran si "
"el texto coincide."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
msgid "Filters files based upon their file extension."
msgstr "Filtra los archivos en función de su extensión."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Se pueden especificar varias extensiones de archivo, lo que a su vez "
"revelará más de la lista de resultados. Ejemplo:\n"
"    flac wav ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"También es posible invertir el filtro, especificando con un signo de "
"exclamación las extensiones de archivo que no desea que aparezcan en los "
"resultados. Por ejemplo\n"
"    !mp3 !jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
msgid "File Size"
msgstr "Tamaño del archivo"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
msgid "Filters files based upon their file size."
msgstr "Filtra los archivos en función de su tamaño."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"Por defecto, la unidad utilizada es bytes (B) y los archivos mayores o "
"iguales a (>=) el valor serán coincidentes."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Añada b, k, m, g (o bien kib, mib, gib) para especificar unidades en bytes, "
"kibibytes, mebibytes o gibibytes:\n"
"    20m para mostrar archivos mayores que 20 MiB (mebibytes)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Antepon = a un valor para especificar una coincidencia exacta:\n"
"    =1024 coincide con archivos que tengan exactamente 1 KiB (kibibyte)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Anteponga ! a un valor para excluir archivos de un tamaño específico:\n"
"    !30.5m para ocultar archivos cuyo tamaño es 30,5 MiB (mebibytes)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Antepon < o > para buscar los archivos menores/mayores que el valor dado. "
"Utiliza un espacio entre cada condición para incluir un rango:\n"
"    >10.5m <1g para mostrar archivos mayores de 10.5 MiB, pero menores de 1 "
"GiB."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Las variantes kb, mb y gb, más conocidas, también pueden utilizarse en lugar "
"de las unidades de kilobyte, megabyte y gigabyte."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
msgid "Filters files based upon their bitrate."
msgstr "Filtra los archivos en función de su tasa de bits."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Los valores deben introducirse solo como dígitos numéricos. La unidad es "
"siempre Kb/s (Kilobits por segundo)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Al igual que en el Tamaño del archivo (arriba), se pueden utilizar los "
"operadores =, !, <, >, <= o >=, y se pueden especificar múltiples "
"condiciones, por ejemplo para mostrar archivos con una tasa de bits de al "
"menos 256 Kb/s con una tasa de bits máxima de 1411 Kb/s:\n"
"    256 <=1411"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
msgid "Filters files based upon their duration."
msgstr "Filtra archivos en función de su duración."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"Por defecto, se buscarán los archivos cuya duración sea mayor o igual (>=) a "
"la introducida, a menos que se utilice un operador (=, !, <=, < o >)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Introduzca un valor bruto en segundos o utilice los formatos de tiempo MM:SS "
"y HH:MM:SS:\n"
"    =53 muestra archivos de unos 53 segundos de duración.\n"
"    >5:30 muestra archivos de más de 5 minutos y medio de duración.\n"
"    <5:30:00 muestra archivos de menos de 5 horas y media de duración."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Se pueden especificar varias condiciones:\n"
"    >6:00 <12:00 para mostrar ficheros de entre 6 y 12 minutos de duración.\n"
"    !9:54 !8:43 !7:32 para ocultar algunos archivos específicos de los "
"resultados.\n"
"    =5:34 =4:23 =3:05 para incluir archivos con duraciones específicas."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Filtra los archivos en función de la localización geográfica de los usuarios "
"según los códigos del país definidos por la norma ISO 3166-2:\n"
"    US sólo mostrará resultados de usuarios con direcciones IP en Estados "
"Unidos.\n"
"    GB ocultará los resultados procedentes de usuarios de Gran Bretaña."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Se pueden especificar varios países con comas o espacios."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
msgid "Free Slot"
msgstr "Puesto disponible"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Mostrar sólo los resultados de los usuarios que tienen al menos una ranura "
"de carga libre, es decir, los archivos que están disponibles inmediatamente."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
msgid "Upload Speed Limits"
msgstr "Límites de velocidad de subida"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
msgid "Unlimited upload speed"
msgstr "Velocidad de carga ilimitada"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
msgid "Use upload speed limit (KiB/s):"
msgstr "Utiliza el límite de velocidad de subida (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Usar el límite de velocidad de subida alternativo (KiB/s):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
msgid "Private Chat Command Help"
msgstr "Ayuda de las órdenes del chat privado"

#: pynicotine/gtkgui/ui/search.ui:7
msgid "Include text…"
msgstr "Incluir texto…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtra los resultados cuyas rutas de archivo contienen el texto "
"especificado. Se pueden especificar múltiples frases y palabras, por "
"ejemplo, frase exacta|música|término|frase exacta dos"

#: pynicotine/gtkgui/ui/search.ui:18
msgid "Exclude text…"
msgstr "Excluir texto…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Excluye los resultados cuyas rutas de archivo contienen el texto "
"especificado. Se pueden especificar múltiples frases y palabras, por "
"ejemplo, frase exacta|música|término|frase exacta dos"

#: pynicotine/gtkgui/ui/search.ui:29
msgid "File type…"
msgstr "Tipo de archivo…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Tipo de archivo, por ejemplo, flac|wav|ape o !mp3|!m4a"

#: pynicotine/gtkgui/ui/search.ui:40
msgid "File size…"
msgstr "Tamaño del archivo…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
msgid "File size, e.g. >10.5m <1g"
msgstr "Tamaño del archivo, por ejemplo >10,5 m <1 g"

#: pynicotine/gtkgui/ui/search.ui:51
msgid "Bitrate…"
msgstr "Tasa de bits…"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
msgid "Bitrate, e.g. 256 <1412"
msgstr "Tasa de bits, por ejemplo 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
msgid "Duration…"
msgstr "Duración…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Duración, por ejemplo >6:00 <12:00 !6:54"

#: pynicotine/gtkgui/ui/search.ui:73
msgid "Country code…"
msgstr "Código de país…"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Código de país, por ejemplo US ES o !DE !GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Prohibir a los usuarios el acceso a los archivos que compartas, basándose en "
"el nombre de usuario, la dirección IP o el país."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Códigos de países a bloquear (separados por comas):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
msgid "Codes must be in ISO 3166-2 format."
msgstr "Los códigos deben estar en formato ISO 3166-2."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
msgid "Use custom geo block message:"
msgstr "Utilizar un mensaje de bloqueo geográfico personalizado:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Utilizar un mensaje personalizado para los vetos:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
msgid "IP Addresses"
msgstr "Direcciones IP"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
msgid "Restore previously open private chats on startup"
msgstr "Restaurar al arranque los chats privados abiertos previamente"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
msgid "Enable spell checker"
msgstr "Activar el corrector ortográfico"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
msgid "Enable CTCP-like private message responses (client version)"
msgstr "Habilitar respuestas de mensajes privados tipo CTCP (versión cliente)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
msgid "Number of recent private chat messages to show:"
msgstr "Número de mensajes privados de chat recientes a mostrar:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
msgid "Number of recent chat room messages to show:"
msgstr "Número de mensajes recientes de las salas de chat a mostrar:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
msgid "Chat Completion"
msgstr "Autocompletar el chat"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
msgid "Enable tab-key completion"
msgstr "Habilitar la tecla de tabulación para el autocompletado"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
msgid "Enable completion drop-down list"
msgstr "Activar la lista desplegable de autocompletar"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
msgid "Minimum characters required to display drop-down:"
msgstr "Caracteres mínimos requeridos para mostrar el desplegable:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
msgid "Allowed chat completions:"
msgstr "Autocompletados permitidos:"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
msgid "Buddy names"
msgstr "Nombres de amigos"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
msgid "Chat room usernames"
msgstr "Nombres de usuario de las salas de chat"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
msgid "Room names"
msgstr "Nombres de las salas"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
msgid "Commands"
msgstr "Comandos"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
msgid "Timestamps"
msgstr "Marcas temporales"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
msgid "Private chat format:"
msgstr "Formato de chat privado:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "Por defecto"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
msgid "Chat room format:"
msgstr "Formato de sala de chat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
msgid "Text-to-Speech"
msgstr "Texto a voz"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
msgid "Enable Text-to-Speech"
msgstr "Activar la conversión de texto a voz"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
msgid "Text-to-Speech command:"
msgstr "Comando de texto a voz:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
msgid "Private chat message:"
msgstr "Mensaje de chat privado:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
msgid "Chat room message:"
msgstr "Mensaje de la sala de chat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
msgid "Censor"
msgstr "Censurar"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
msgid "Enable censoring of text patterns"
msgstr "Habilitar la censura de patrones de texto"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
msgid "Auto-Replace"
msgstr "Sustitución automática"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
msgid "Enable automatic replacement of words"
msgstr "Activar la sustitución automática de palabras"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr ""
"Limpiar automáticamente las descargas terminadas/filtradas de la lista de "
"transferencias"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
msgid "Store completed downloads in username subfolders"
msgstr ""
"Almacenar las descargas completadas en subcarpetas con los nombres de usuario"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
msgid "Double-click action for downloads:"
msgstr "Acción de doble clic para las descargas:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
msgid "Allow users to send you any files:"
msgstr "Permite que los usuarios te envíen cualquier archivo:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
msgid "Folders"
msgstr "Carpetas"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
msgid "Finished downloads:"
msgstr "Descargas finalizadas:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
msgid "Incomplete downloads:"
msgstr "Descargas incompletas:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
msgid "Received files:"
msgstr "Archivos recibidos:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "Eventos"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
msgid "Run command after file download finishes ($ for file path):"
msgstr ""
"Ejecutear el comando después de que termine la descarga del archivo ($ para "
"la ruta del archivo):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"Ejecutar el comando después de que la descarga de la carpeta termine ($ para "
"la ruta de la carpeta):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
msgid "Download Filters"
msgstr "Filtros de descarga"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
msgid "Enable download filters"
msgstr "Activar los filtros de descarga"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Añadir"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Cargar valores por defecto"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
msgid "Verify Filters"
msgstr "Verificar filtros"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
msgid "Unverified"
msgstr "No verificado"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Ignorar los mensajes de chat y los resultados de búsqueda de los usuarios "
"basándose en el nombre de usuario o en la dirección IP."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Guardar registro de las salas de chat por defecto"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Registrar chats privados por defecto"

#: pynicotine/gtkgui/ui/settings/log.ui:142
msgid "Log transfers to file"
msgstr "Registrar las transferencias en un archivo"

#: pynicotine/gtkgui/ui/settings/log.ui:166
msgid "Log debug messages to file"
msgstr "Registrar los mensajes de depuración en un archivo"

#: pynicotine/gtkgui/ui/settings/log.ui:190
msgid "Log timestamp format:"
msgstr "Formato de la marca temporal del archivo de registro:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
msgid "Folder Locations"
msgstr "Ubicaciones de las carpetas"

#: pynicotine/gtkgui/ui/settings/log.ui:245
msgid "Chatroom logs folder:"
msgstr "Carpeta de registros de las salas de chat:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
msgid "Private chat logs folder:"
msgstr "Carpeta de registros de los chats privados:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
msgid "Transfer logs folder:"
msgstr "Carpeta del registro de las transferencias:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
msgid "Debug logs folder:"
msgstr "Carpeta de registros de depuración:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Inicia sesión en una cuenta existente de Soulseek o crea una nueva. Los "
"nombres de usuario distinguen entre mayúsculas y minúsculas y son únicos."

#: pynicotine/gtkgui/ui/settings/network.ui:118
msgid "Public IP address:"
msgstr "Dirección IP pública:"

#: pynicotine/gtkgui/ui/settings/network.ui:159
msgid "Listening port:"
msgstr "Puerto de escucha:"

#: pynicotine/gtkgui/ui/settings/network.ui:185
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Reenviar automáticamente el puerto de la escucha (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
msgid "Away Status"
msgstr "Estado de ausencia"

#: pynicotine/gtkgui/ui/settings/network.ui:228
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Minutos de inactividad antes de ausentarse (0 para deshabilitar):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
msgid "Auto-reply message when away:"
msgstr "Mensaje de respuesta automática en caso de ausencia:"

#: pynicotine/gtkgui/ui/settings/network.ui:299
msgid "Auto-connect to server on startup"
msgstr "Conexión automática al servidor al arranque"

#: pynicotine/gtkgui/ui/settings/network.ui:323
msgid "Soulseek server:"
msgstr "Servidor de Soulseek:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Vincula las conexiones a una interfaz de red específica, útil para, por "
"ejemplo, garantizar que se utilice una VPN en todo momento. Déjalo vacío "
"para utilizar cualquier interfaz disponible. Cambia este valor solo si sabes "
"lo que estás haciendo."

#: pynicotine/gtkgui/ui/settings/network.ui:351
msgid "Network interface:"
msgstr "Interfaz de red:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"«Reproduciendo ahora» te permite mostrar lo que tu reproductor multimedia "
"está reproduciendo utilizando el comando /now en el chat."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
msgid "Other"
msgstr "Otro"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
msgid "Now Playing Format"
msgstr "Formato de «Reproduciendo ahora»"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
msgid "Now Playing message format:"
msgstr "Formato del mensaje de «Reproduciendo ahora»:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
msgid "Test Configuration"
msgstr "Probar la configuración"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
msgid "Enable plugins"
msgstr "Habilitar los complementos"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
msgid "Add Plugins"
msgstr "Añadir complementos"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
msgid "_Add Plugins"
msgstr "_Añadir complementos"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
msgid "Settings"
msgstr "Configuración"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
msgid "_Settings"
msgstr "_Configuración"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
msgid "Version:"
msgstr "Versión:"

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
msgid "Created by:"
msgstr "Creado por:"

#: pynicotine/gtkgui/ui/settings/search.ui:51
msgid "Enable search history"
msgstr "Activar el historial de búsqueda"

#: pynicotine/gtkgui/ui/settings/search.ui:70
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Los archivos compartidos en privado que se hayan hecho visibles para todo el "
"mundo llevarán el prefijo '[PRIVATE]', y no podrán descargarse hasta que "
"quien los haya subido dé su permiso explícito. Pídeselo amablemente."

#: pynicotine/gtkgui/ui/settings/search.ui:76
msgid "Show privately shared files in search results"
msgstr ""
"Mostrar los archivos compartidos de forma privada en los resultados de la "
"búsqueda"

#: pynicotine/gtkgui/ui/settings/search.ui:106
msgid "Limit number of results per search:"
msgstr "Limitar el número de resultados por búsqueda:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
msgid "Result Filter Help"
msgstr "Ayuda del filtro de resultados"

#: pynicotine/gtkgui/ui/settings/search.ui:177
msgid "Enable search result filters by default"
msgstr "Activar los filtros de resultados de búsqueda por defecto"

#: pynicotine/gtkgui/ui/settings/search.ui:211
msgid "Include:"
msgstr "Incluir:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
msgid "Exclude:"
msgstr "Excluir:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
msgid "File Type:"
msgstr "Tipo de archivo:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "Tamaño:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Tasa de bits:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
msgid "Duration:"
msgstr "Duración:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
msgid "Country Code:"
msgstr "Código de país:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
msgid "Only show results from users with an available upload slot."
msgstr ""
"Mostrar solo resultados de usuarios con algún puesto de subida disponible."

#: pynicotine/gtkgui/ui/settings/search.ui:430
msgid "Network Searches"
msgstr "Búsquedas en la red"

#: pynicotine/gtkgui/ui/settings/search.ui:452
msgid "Respond to search requests from other users"
msgstr "Responder a las solicitudes de búsqueda de otros usuarios"

#: pynicotine/gtkgui/ui/settings/search.ui:486
msgid "Searches shorter than this number of characters will be ignored:"
msgstr ""
"Las búsquedas más cortas que este número de caracteres serán ignoradas:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
msgid "Maximum search results to send per search request:"
msgstr ""
"Número máximo de resultados de búsqueda a enviar por solicitud de búsqueda:"

#: pynicotine/gtkgui/ui/settings/search.ui:578
msgid "Clear Search History"
msgstr "Limpiar el historial de búsqueda"

#: pynicotine/gtkgui/ui/settings/search.ui:626
msgid "Clear Filter History"
msgstr "Limpiar el historial de filtros"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Reescanea automáticamente el contenido de tus carpetas compartidas al "
"arranque. Si se desactiva, tus carpetas compartidas solo se actualizarán "
"cuando inicies manualmente un reescaneo."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Reescanear compartidos al inicio"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr "Visible para todos:"

#: pynicotine/gtkgui/ui/settings/shares.ui:82
msgid "Buddy shares"
msgstr "Compartidos de amigos"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
msgid "Trusted shares"
msgstr "Compartidos de confianza"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr ""
"Limpiar automáticamente las subidas terminadas/canceladas de la lista de "
"transferencias"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
msgid "Double-click action for uploads:"
msgstr "Acción de doble clic para las subidas:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
msgid "Limit upload speed:"
msgstr "Limitar velocidad de subida:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
msgid "Per transfer"
msgstr "Por transferencia"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
msgid "Total transfers"
msgstr "Total de transferencias"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
msgid "Upload Slots"
msgstr "Puestos de subida"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Por turnos: Los archivos se subirán de forma cíclica a los usuarios que "
"están esperando en la cola.\n"
"Por orden de llegada: Los archivos se subirán en el orden en que estaban en "
"la cola."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
msgid "Upload queue type:"
msgstr "Tipo de cola de subida:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr ""
"Asigna las ranuras de subida hasta que la velocidad total alcance (KiB/s):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
msgid "Fixed number of upload slots:"
msgstr "Número fijo de las ranuras de la carga:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
msgid "Prioritize all buddies"
msgstr "Dar prioridad a todos los amigos"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
msgid "Queue Limits"
msgstr "Límites de cola"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
msgid "Maximum number of queued files per user:"
msgstr "Número máximo de archivos en la cola por usuario:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr "Tamaño máximo total de los archivos en la cola por usuario (MiB):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
msgid "Limits do not apply to buddies"
msgstr "Los límites no se aplican a los amigos"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"Las apariciones de $ son sustituidas por la URL. Se utilizan las "
"aplicaciones por defecto del sistema en los casos en que no se haya "
"configurado un protocolo."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
msgid "File manager command:"
msgstr "Comando del gestor de archivos:"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
msgid "Reset Picture"
msgstr "Reiniciar imagen"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
msgid "Self Description"
msgstr "Presentación"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""
"Añade cosas que quieres que todo el mundo vea, como una breve descripción, "
"consejos útiles o directrices para descargar tus compartidos."

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
msgid "Picture:"
msgstr "Imagen:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
msgid "Prefer dark mode"
msgstr "Modo oscuro"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
msgid "Use header bar"
msgstr "Utilizar la barra de cabecera"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
msgid "Display tray icon"
msgstr "Mostrar el icono de la bandeja de sistema"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
msgid "Minimize to tray on startup"
msgstr "Minimizar a la bandeja de sistema al arranque"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
msgid "Language (requires a restart):"
msgstr "Idioma (requiere reiniciar):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
msgid "When closing window:"
msgstr "Al cerrar la ventana:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
msgid "Notifications"
msgstr "Notificaciones"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
msgid "Enable sound for notifications"
msgstr "Activar el sonido de las notificaciones"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
msgid "Show notification for private chats and mentions in the window title"
msgstr ""
"Mostrar la notificación de los chats privados y las menciones en el título "
"de la ventana"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
msgid "Show notifications for:"
msgstr "Mostrar notificaciones para:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
msgid "Finished file downloads"
msgstr "Descargas de archivos terminadas"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
msgid "Finished folder downloads"
msgstr "Descargas de carpetas terminadas"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
msgid "Private messages"
msgstr "Mensajes privados"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
msgid "Chat room messages"
msgstr "Mensajes de las salas de chat"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
msgid "Chat room mentions"
msgstr "Menciones de las salas de chat"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
msgid "Wishlist results found"
msgstr "Resultados de la lista de deseos encontrados"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
msgid "Restore the previously active main tab at startup"
msgstr "Restaurar al arranque la pestaña principal previamente activa"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
msgid "Close-buttons on secondary tabs"
msgstr "Botones de cierre en las pestañas secundarias"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
msgid "Regular tab label color:"
msgstr "Color de la etiqueta de la pestaña normal:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
msgid "Changed tab label color:"
msgstr "Color de la etiqueta de la pestaña cambiada:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
msgid "Highlighted tab label color:"
msgstr "Color de la etiqueta de la pestaña resaltada:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr "Posición en la lista de amigos:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
msgid "Visible main tabs:"
msgstr "Pestañas principales visibles:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
msgid "Tab bar positions:"
msgstr "Posiciones de la barra de pestañas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
msgid "Main tabs"
msgstr "Pestañas principales"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
msgid "Show reverse file paths (requires a restart)"
msgstr "Mostrar las rutas de los archivo a la inversa (requiere reiniciar)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
msgid "Show exact file sizes (requires a restart)"
msgstr "Mostrar los tamaños de exactos de los archivo (requiere reiniciar)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
msgid "List text color:"
msgstr "Color del texto de lista:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
msgid "Enable colored usernames"
msgstr "Habilitar los nombres de usuario de colores"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
msgid "Chat username appearance:"
msgstr "Aspecto del nombre de usuario del chat:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
msgid "Remote text color:"
msgstr "Color del texto remoto:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
msgid "Local text color:"
msgstr "Color del texto local:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
msgid "Command output text color:"
msgstr "Color del texto de salida del comando:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
msgid "/me action text color:"
msgstr "Color del texto de la acción /me:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
msgid "Highlighted text color:"
msgstr "Color del texto resaltado:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
msgid "URL link text color:"
msgstr "Color del texto de los enlaces URL:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
msgid "User Statuses"
msgstr "Estado de los usuarios"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
msgid "Online color:"
msgstr "Color en línea:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
msgid "Away color:"
msgstr "Color del visitante:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
msgid "Offline color:"
msgstr "Color sin conexión:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
msgid "Text Entries"
msgstr "Entradas de texto"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
msgid "Text entry background color:"
msgstr "Color de fondo de la entrada de texto:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
msgid "Text entry text color:"
msgstr "Color del texto de la entrada de texto:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
msgid "Fonts"
msgstr "Tipografías"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
msgid "Global font:"
msgstr "Tipografía general:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
msgid "List font:"
msgstr "Tipografía para las listas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
msgid "Text view font:"
msgstr "Fuente de la vista de texto:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
msgid "Chat font:"
msgstr "Tipografía para los chats:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
msgid "Transfers font:"
msgstr "Tipografía para las transferencias:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
msgid "Search font:"
msgstr "Tipografía para las búsquedas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
msgid "Browse font:"
msgstr "Tipografía para las exploraciones:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
msgid "Icons"
msgstr "Iconos"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
msgid "Icon theme folder:"
msgstr "Carpeta del tema de iconos:"

#: pynicotine/gtkgui/ui/uploads.ui:86
msgid "Abort User(s)"
msgstr "Interrumpir usuario(s)"

#: pynicotine/gtkgui/ui/uploads.ui:116
msgid "Ban User(s)"
msgstr "Vetar usuario(s)"

#: pynicotine/gtkgui/ui/uploads.ui:138
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Borrar todas las subidas finalizadas/canceladas"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
msgid "Message All"
msgstr "Todos los mensajes"

#: pynicotine/gtkgui/ui/uploads.ui:199
msgid "Clear Specific Uploads"
msgstr "Eliminar subidas específicas"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
msgid "Save Shares List to Disk"
msgstr "Guardar la lista de compartidos en el disco"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
msgid "Refresh Files"
msgstr "Actualizar archivos"

#: pynicotine/gtkgui/ui/userinfo.ui:101
msgid "Edit Profile"
msgstr "Editar perfil"

#: pynicotine/gtkgui/ui/userinfo.ui:149
msgid "Shared Files"
msgstr "Archivos compartidos"

#: pynicotine/gtkgui/ui/userinfo.ui:203
msgid "Upload Speed"
msgstr "Velocidad de subida"

#: pynicotine/gtkgui/ui/userinfo.ui:230
msgid "Free Upload Slots"
msgstr "Puestos de subida disponibles"

#: pynicotine/gtkgui/ui/userinfo.ui:284
msgid "Queued Uploads"
msgstr "Subidas en cola"

#: pynicotine/gtkgui/ui/userinfo.ui:354
msgid "Edit Interests"
msgstr "Editar los intereses"

#: pynicotine/gtkgui/ui/userinfo.ui:624
msgid "_Gift Privileges…"
msgstr "_Regalar privilegios…"

#: pynicotine/gtkgui/ui/userinfo.ui:663
msgid "_Refresh Profile"
msgstr "_Actualizar el perfil"

#: pynicotine/plugins/core_commands/PLUGININFO:3
msgid "Nicotine+ Commands"
msgstr "Comandos para Nicotine+"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Equipo de Nicotine+"

#~ msgid "Listening port (requires a restart):"
#~ msgstr "Puerto de escucha (requiere un reinicio):"

#~ msgid "Network interface (requires a restart):"
#~ msgstr "Interfaz de la red (requiere un reinicio):"

#~ msgid "Invalid Password"
#~ msgstr "Contraseña no válida"

#~ msgid "Change _Login Details"
#~ msgstr "Cambiar _los detalles del inicio de sesión"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i usuarios con privilegios"

#~ msgid "_Set Up…"
#~ msgstr "_Configurar…"

#~ msgid "Queued search result text color:"
#~ msgstr "Color del texto de los resultados de búsqueda en cola de espera:"

#, python-format
#~ msgid "Failed to fetch the shared folder %(folder)s: %(error)s"
#~ msgstr ""
#~ "Fallo en la obtención de la carpeta compartida %(folder)s: %(error)s"

#~ msgid "_Clear"
#~ msgstr "_Limpiar"

#, python-format
#~ msgid "Can't save %(filename)s: %(error)s"
#~ msgstr "No se puede guardar %(filename)s: %(error)s"

#~ msgid "Trusted Buddies"
#~ msgstr "Amigos de confianza"

#~ msgid "Quit program"
#~ msgstr "Cerrar el programa"

#~ msgid "Username:"
#~ msgstr "Nombre de usuario:"

#~ msgid "Re_commendations for Item"
#~ msgstr "_Recomendaciones para el elemento"

#~ msgid "_Remove Item"
#~ msgstr "_Eliminar elemento"

#~ msgid "_Remove"
#~ msgstr "Elimina_r"

#~ msgid "Send M_essage"
#~ msgstr "Enviar m_ensaje"

#~ msgid "Send Message"
#~ msgstr "Enviar mensaje"

#~ msgid "View User Profile"
#~ msgstr "Ver el perfil del usuario"

#~ msgid "Start Messaging"
#~ msgstr "Iniciar mensajes"

#~ msgid "Enter the name of the user whom you want to send a message:"
#~ msgstr "Introduce el nombre del usuario al que deseas enviar un mensaje:"

#~ msgid "_Message"
#~ msgstr "_Mensaje"

#~ msgid "Enter the name of the user whose profile you want to see:"
#~ msgstr "Introduce el nombre del usuario cuyo perfil quieres ver:"

#~ msgid "_View Profile"
#~ msgstr "_Ver el perfil"

#~ msgid "Enter the name of the user whose shares you want to see:"
#~ msgstr ""
#~ "Introduce el nombre del usuario cuya lista de compartidos deseas ver:"

#~ msgid "_Browse"
#~ msgstr "_Explorar"

#~ msgid "Password"
#~ msgstr "Contraseña"

#~ msgid "Download File"
#~ msgstr "Descargar el archivo"

#~ msgid "Refresh Similar Users"
#~ msgstr "Actualizar los usuarios similares"

#~ msgid "Enter the username of the person whose files you want to see"
#~ msgstr ""
#~ "Introduce el nombre de usuario de la persona cuyos archivos deseas ver"

#~ msgid "Enter the username of the person whose information you want to see"
#~ msgstr ""
#~ "Introduce el nombre de usuario de la persona cuya información deseas ver"

#~ msgid "Enter the username of the person you want to send a message to"
#~ msgstr ""
#~ "Introduce el nombre de usuario de la persona a la que deseas enviar un "
#~ "mensaje"

#~ msgid "Enter the username of the person you want to add to your buddy list"
#~ msgstr ""
#~ "Introduce el nombre de usuario de la persona que deseas añadir a tu lista "
#~ "de amigos"

#~ msgid ""
#~ "Enter the name of a room you want to join. If the room doesn't exist, it "
#~ "will be created."
#~ msgstr ""
#~ "Introduce el nombre de la sala a la que deseas unirte. Si la sala no "
#~ "existe, se creará."

#~ msgid "Show Log History Pane"
#~ msgstr "Mostrar el panel del historial de los registros"

#~ msgid "Save _Picture"
#~ msgstr "Guardar _Imagen"

#, python-format
#~ msgid ""
#~ "Filtered out incorrect search result %(filepath)s from user %(user)s for "
#~ "search query \"%(query)s\""
#~ msgstr ""
#~ "Se descartó el resultado de búsqueda incorrecto %(filepath)s del usuario "
#~ "%(user)s para la consulta de búsqueda «%(query)s»"

#~ msgid ""
#~ "Certain clients don't send search results if special characters are "
#~ "included."
#~ msgstr ""
#~ "Algunos clientes no envían resultados de búsqueda si se incluyen "
#~ "caracteres especiales."

#~ msgid "Remove special characters from search terms"
#~ msgstr "Eliminar los caracteres especiales de los términos de búsqueda"

#, python-format
#~ msgid "Trouble executing '%s'"
#~ msgstr "Problema ejecutando «%s»"

#, python-format
#~ msgid "Trouble executing on folder: %s"
#~ msgstr "Problemas de ejecución en la carpeta: %s"

#~ msgid "Disallowed extension"
#~ msgstr "Extensión no permitida"

#~ msgid "Too many files"
#~ msgstr "Demasiados archivos"

#~ msgid "Too many megabytes"
#~ msgstr "Demasiados megabytes"

#~ msgid "Server does not permit performing wishlist searches at this time"
#~ msgstr ""
#~ "El servidor no permite realizar búsquedas de la lista de deseos en este "
#~ "momento"

#~ msgid ""
#~ "File transfer speeds depend on users you are downloading from. Certain "
#~ "users will be faster, while others will be slow."
#~ msgstr ""
#~ "La velocidad de transferencia de archivos depende de los usuarios desde "
#~ "los que estés descargando. Algunos usuarios serán más rápidos, mientras "
#~ "que otros serán más lentos."

#~ msgid "Started Downloads"
#~ msgstr "Descargas iniciadas"

#~ msgid "Started Uploads"
#~ msgstr "Subidas iniciadas"

#~ msgid "Replace censored letters with:"
#~ msgstr "Sustituir las letras censuradas por:"

#~ msgid "Censored Patterns"
#~ msgstr "Patrones censurados"

#~ msgid "Replacements"
#~ msgstr "Sustituciones"

#~ msgid ""
#~ "If a user on the Soulseek network searches for a file that exists in your "
#~ "shares, search results will be sent to the user."
#~ msgstr ""
#~ "Si un usuario de la red Soulseek busca un archivo que existe en tus "
#~ "compartidos, los resultados de la búsqueda se enviarán a dicho usuario."

#~ msgid "Send to Player"
#~ msgstr "Enviar al reproductor"

#~ msgid "Send to _Player"
#~ msgstr "Enviar al _Reproductor"

#~ msgid "_Open in File Manager"
#~ msgstr "_Mostrar en el gestor de archivos"

#~ msgid "Media player command:"
#~ msgstr "Comando del reproductor multimedia:"

#, python-format
#~ msgid ""
#~ "Unable to save download to username subfolder, falling back to default "
#~ "download folder. Error: %s"
#~ msgstr ""
#~ "No ha sido posible guardar la descarga en la subcarpeta del nombre de "
#~ "usuario, volviendo a la carpeta de descarga por defecto. Error: %s"

#~ msgid "Buddy-only"
#~ msgstr "Solo para amigos"

#~ msgid "Share with buddies only"
#~ msgstr "Compartir solo con amigos"

#~ msgid "_Quit…"
#~ msgstr "_Salir…"

#~ msgid "_Configure Shares"
#~ msgstr "_Configurar compartidos"

#~ msgid "Remote file error"
#~ msgstr "Error de archivo remoto"

#, python-format
#~ msgid "Cannot find %(option1)s or %(option2)s, please install either one."
#~ msgstr ""
#~ "No se encuentra %(option1)s ni %(option2)s, por favor, instale cualquiera "
#~ "de los dos."

#, python-format
#~ msgid "%(num)s folders found before rescan"
#~ msgstr "%(num)s carpetas encontradas después de un nuevo escaneo"

#, python-format
#~ msgid "Failed to process the following databases: %(names)s"
#~ msgstr "No se han podido procesar las siguientes bases de datos: %(names)s"

#, python-format
#~ msgid "Failed to send number of shared files to the server: %s"
#~ msgstr "Fallo al enviar el número de archivos compartidos al servidor: %s"

#~ msgid "Quit / Run in Background"
#~ msgstr "Salir/ejecutar en segundo plano"

#~ msgid "Limit buddy-only shares to trusted buddies"
#~ msgstr ""
#~ "Limitar los compartidos de solo-para-amigos a los amigos de confianza"

#~ msgid "Shared"
#~ msgstr "Compartido"

#~ msgid "Search Files and Folders"
#~ msgstr "Buscar en archivos y carpetas"

#~ msgid "Out of Date"
#~ msgstr "No actualizado"

#, python-format
#~ msgid "Version %(version)s is available, released on %(date)s"
#~ msgstr "La versión %(version)s está disponible, publicada el %(date)s"

#, python-format
#~ msgid "You are using a development version of %s"
#~ msgstr "Está utilizando una versión de desarrollo de %s"

#, python-format
#~ msgid "You are using the latest version of %s"
#~ msgstr "Está utilizando la última versión de %s"

#~ msgid "Latest Version Unknown"
#~ msgstr "Última versión desconocida"

#~ msgid "Prefer Dark _Mode"
#~ msgstr "Modo _oscuro"

#~ msgid "Show _Log History Pane"
#~ msgstr "Mostrar el panel del historial del _registro"

#~ msgid "Buddy List in Separate Tab"
#~ msgstr "Lista de amigos en una pestaña separada"

#~ msgid "Buddy List Always Visible"
#~ msgstr "Lista de amigos siempre visible"

#~ msgid "_View"
#~ msgstr "_Ver"

#~ msgid "_Open Log Folder"
#~ msgstr "_Abrir carpeta del registro"

#~ msgid "_Browse Folder(s)"
#~ msgstr "_Explorar carpeta(s)"

#~ msgid "Kibibytes (2^10 bytes) per second."
#~ msgstr "Kibibytes (2^10 bytes) por segundo."

#~ msgid "Sent to users as the reason for being geo blocked."
#~ msgstr "Se envía a los usuarios como razón del bloqueo geográfico."

#~ msgid "Sent to users as the reason for being banned."
#~ msgstr "Se envía a los usuarios como razón por la cual se les ha vetado."

#~ msgid ""
#~ "Once you interact with the application being away, status will be set to "
#~ "online."
#~ msgstr ""
#~ "Una vez que interactúe con la aplicación estando ausente, el estado "
#~ "cambiará a «en línea»."

#~ msgid "Each user may queue a maximum of either:"
#~ msgstr "Cada usuario puede poner en cola un máximo de:"

#~ msgid "Mebibytes (2^20 bytes)."
#~ msgstr "Mebibytes (2^20 bytes)."

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "files"
#~ msgstr "archivos"

#~ msgid "Queue Behavior"
#~ msgstr "Comportamiento de la cola"

#~ msgid ""
#~ "If disabled, slots will automatically be determined by available "
#~ "bandwidth limitations."
#~ msgstr ""
#~ "Si se desactiva, los puestos se determinarán automáticamente según las "
#~ "limitaciones del ancho de banda disponible."

#~ msgid "Note that the operating system's theme may take precedence."
#~ msgstr ""
#~ "Tenga en cuenta que el tema del sistema operativo puede tener prioridad."

#~ msgid "By default the leftmost tab is activated at startup"
#~ msgstr "La pestaña más a la izquierda es activada por defecto al arranque"

#, python-format
#~ msgid "Quit %(program)s %(version)s, %(status)s!"
#~ msgstr "¡Cerrar %(program)s %(version)s, %(status)s!"

#~ msgid "terminated"
#~ msgstr "cancelado"

#~ msgid "done"
#~ msgstr "hecho"

#~ msgid "Remember choice"
#~ msgstr "Recordar la elección"

#~ msgid "Kosovo"
#~ msgstr "Kosovo"

#~ msgid "Unknown Network Interface"
#~ msgstr "Interfaz de red desconocida"

#~ msgid "Listening Port Unavailable"
#~ msgstr "Puerto de escucha no disponible"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr ""
#~ "El servidor parece caído o sin respuesta, reintentando en %i segundos"

#~ msgid ""
#~ "Nicotine+ uses peer-to-peer networking to connect to other users. In "
#~ "order to allow users to connect to you without trouble, an open listening "
#~ "port is crucial."
#~ msgstr ""
#~ "Nicotine+ utiliza una red peer-to-peer para conectarse a otros usuarios. "
#~ "Para que los usuarios puedan conectarse a ti sin problemas es crucial "
#~ "tener un puerto de escucha abierto."

#~ msgid "--- disconnected ---"
#~ msgstr "--- desconectado ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- conectado de nuevo ---"

#~ msgid "ID"
#~ msgstr "Identificador"

#~ msgid "Earth"
#~ msgstr "Tierra"

#~ msgid "Czech Republic"
#~ msgstr "República Checa"

#~ msgid "Turkey"
#~ msgstr "Turquía"

#~ msgid "Joined Rooms "
#~ msgstr "Salas a las que te has unido "

#~ msgid "_Auto-join Room"
#~ msgstr "Unirse _automáticamente a la sala"

#~ msgid ""
#~ "<b>Syntax</b>: Letters are case-insensitive. All Python regular "
#~ "expressions are supported if escaping is disabled. For simple filters, "
#~ "keeping escaping enabled is recommended."
#~ msgstr ""
#~ "<b>Sintaxis:</b> Las letras no distinguen entre mayúsculas y minúsculas. "
#~ "Todas las expresiones regulares de Python son compatibles si el escape "
#~ "está desactivado. Para los filtros simples, se recomienda mantener el "
#~ "escape activado."

#~ msgid "Escaped"
#~ msgstr "Escape activado"

#~ msgid "Escape filter"
#~ msgstr "Filtro de escape"

#~ msgid "Enter a text pattern and what to replace it with"
#~ msgstr ""
#~ "Introduce un patrón de texto y aquello por lo vaya a ser reemplazado"

#, python-format
#~ msgid "%(num)s folders found before rescan, rebuilding…"
#~ msgstr "%(num)s carpetas encontradas antes del reescaneo, reconstruyendo…"

#, python-format
#~ msgid "No listening port is available in the specified port range %s–%s"
#~ msgstr ""
#~ "No hay ningún puerto de recepción disponible en el intervalo de puertos "
#~ "especificado %s–%s"

#~ msgid ""
#~ "Choose a range to select a listening port from. The first available port "
#~ "in the range will be used."
#~ msgstr ""
#~ "Elija un rango para el puerto de recepción. Se utiliza el primer puerto "
#~ "disponible en el rango."

#~ msgid "First Port"
#~ msgstr "Puerto Principal"

#~ msgid "to"
#~ msgstr "a"

#~ msgid "Last Port"
#~ msgstr "Último Puerto"

#, python-format
#~ msgid "Cannot find %s or newer, please install it."
#~ msgstr "No se encuentra %s, por favor instálalo."

#~ msgid ""
#~ "Cannot import the Gtk module. Bad install of the python-gobject module?"
#~ msgstr ""
#~ "No se puede importar el módulo Gtk. ¿Mala instalación del módulo python-"
#~ "gobject?"

#, python-format
#~ msgid ""
#~ "You are using an unsupported version of GTK %(major_version)s. You should "
#~ "install GTK %(complete_version)s or newer."
#~ msgstr ""
#~ "Estás utilizando una versión no soportada de GTK %(major_version)s. "
#~ "Deberías instalar GTK %(complete_version)s o superior."

#~ msgid "User Info"
#~ msgstr "Información de usuario"

#~ msgid "Zoom 1:1"
#~ msgstr "Zoom 1:1"

#~ msgid "Zoom In"
#~ msgstr "Acercarse"

#~ msgid "Zoom Out"
#~ msgstr "Alejarse"

#~ msgid "Show User I_nfo"
#~ msgstr "Ver i_nformación de usuario"

#~ msgid "Request User's Info"
#~ msgstr "Solicitar información del usuario"

#~ msgid "Request User's Shares"
#~ msgstr "Consultar los compartidos del usuario"

#~ msgid "Request User Info"
#~ msgstr "Solicitar información de usuario"

#~ msgid "Enter the name of the user whose info you want to see:"
#~ msgstr "Introduce el nombre del usuario cuya información deseas consultar:"

#~ msgid "Request Shares List"
#~ msgstr "Solicitar la lista de compartidos"

#, python-format
#~ msgid "Invalid Soulseek URL: %s"
#~ msgstr "URL de Soulseek no válida: %s"

#~ msgid ""
#~ "Users on the Soulseek network will be able to download files from folders "
#~ "you share. Sharing files is crucial for the health of the Soulseek "
#~ "network."
#~ msgstr ""
#~ "Los usuarios de la red Soulseek podrán descargar archivos de las carpetas "
#~ "que compartas. Compartir archivos es crucial para la salud de la red "
#~ "Soulseek."

#~ msgid "Update I_nfo"
#~ msgstr "Actualizar i_nfo"

#~ msgid "Chat Room Commands"
#~ msgstr "Comandos de la sala de chat"

#~ msgid "/join /j 'room'"
#~ msgstr "/join /j «sala»"

#~ msgid "Join room 'room'"
#~ msgstr "Unirse a la sala «sala»"

#~ msgid "/me 'message'"
#~ msgstr "/me «mensaje»"

#~ msgid "Display the Now Playing script's output"
#~ msgstr "Mostrar la salida del script de «Reproduciendo ahora»"

#~ msgid "/add /ad 'user'"
#~ msgstr "/add /ad «usuario»"

#~ msgid "Add user 'user' to your buddy list"
#~ msgstr "Añadir usuario «usuario» a tu lista de amigos"

#~ msgid "/rem /unbuddy 'user'"
#~ msgstr "/rem /unbuddy «usuario»"

#~ msgid "Remove user 'user' from your buddy list"
#~ msgstr "Eliminar usuario «usuario» de tu lista de amigos"

#~ msgid "/ban 'user'"
#~ msgstr "/ban «usuario»"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "Añadir usuario «usuario» a tu lista de vetados"

#~ msgid "/unban 'user'"
#~ msgstr "/unban «usuario»"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "Eliminar al usuario «usuario» de tu lista de vetados"

#~ msgid "/ignore 'user'"
#~ msgstr "/ignore «usuario»"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "Añadir usuario «usuario» a tu lista de ignorados"

#~ msgid "/unignore 'user'"
#~ msgstr "/unignore «usuario»"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "Eliminar usuario «usuario» de tu lista de ignorados"

#~ msgid "/browse /b 'user'"
#~ msgstr "/browse /b «usuario»"

#~ msgid "/whois /w 'user'"
#~ msgstr "/whois /w «usuario»"

#~ msgid "Request info for 'user'"
#~ msgstr "Solicitar información de «usuario»"

#~ msgid "/ip 'user'"
#~ msgstr "/ip «usuario»"

#~ msgid "Show IP for user 'user'"
#~ msgstr "Mostrar la IP del usuario «usuario»"

#~ msgid "/search /s 'query'"
#~ msgstr "/search /s «consulta»"

#~ msgid "Start a new search for 'query'"
#~ msgstr "Comenzar una nueva búsqueda para «consulta»"

#~ msgid "/rsearch /rs 'query'"
#~ msgstr "/rsearch /rs «consulta»"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "Buscar «consulta» en las salas a las que se ha unido"

#~ msgid "/bsearch /bs 'query'"
#~ msgstr "/bsearch /bs «consulta»"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "Buscar «consulta» en la lista de amigos"

#~ msgid "/usearch /us 'user' 'query'"
#~ msgstr "/usearch /us «usuario» «consulta»"

#~ msgid "/msg 'user' 'message'"
#~ msgstr "/msg «usuario» «mensaje»"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "Enviar mensaje «mensaje» al usuario «usuario»"

#~ msgid "/pm 'user'"
#~ msgstr "/pm «usuario»"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr "Abre una ventana de chat privado con el usuario «usuario»"

#~ msgid "Private Chat Commands"
#~ msgstr "Comandos de chat privado"

#~ msgid "Add user to your ban list"
#~ msgstr "Añadir usuario a tu lista de vetados"

#~ msgid "Add user to your ignore list"
#~ msgstr "Añadir usuario a tu lista de ignorados"

#~ msgid "Browse shares of user"
#~ msgstr "Explorar los compartidos del usuario"

#~ msgid ""
#~ "For order-insensitive filtering, as well as filtering several exact "
#~ "phrases, vertical bars can be used to separate phrases and words.\n"
#~ "    Example: Remix|Instrumental|Dub Mix"
#~ msgstr ""
#~ "Para un filtrado no sensible al orden, así como para filtrar varias "
#~ "frases exactas, se pueden utilizar barras verticales para separar frases "
#~ "y palabras.\n"
#~ "    Ejemplo: Instrumental|Remix|Dub Mix"

#~ msgid "To exclude non-audio files use !0 in the duration filter."
#~ msgstr ""
#~ "Para excluir archivos que no sean de audio, use !0 en el filtro de "
#~ "duración."

#~ msgid "Filters files based upon users' geographical location."
#~ msgstr ""
#~ "Filtra los archivos en función de la posición geográfica de los usuarios."

#~ msgid "To view the full results again, simply clear all active filters."
#~ msgstr ""
#~ "Para volver a ver los resultados completos, simplemente borre todos los "
#~ "filtros activos."

#~ msgid "See the preferences to set default search result filter options."
#~ msgstr ""
#~ "Vaya a Configuración para definir las opciones por defecto del filtro de "
#~ "resultados."

#~ msgid "File size"
#~ msgstr "Tamaño del archivo"

#~ msgid "Clear Active Filters"
#~ msgstr "Borrar filtros activos"

#~ msgid "Cycle through completions when pressing tab-key"
#~ msgstr ""
#~ "Recorrer cíclicamente las opciones de autocompletado al pulsar la tecla "
#~ "de tabulación"

#~ msgid "Hide drop-down when only one matches"
#~ msgstr "Ocultar el desplegable cuando solo haya una coincidencia"

#~ msgid "Download folders in reverse alphanumerical order"
#~ msgstr "Descargar carpetas en orden alfanumérico inverso"

#~ msgid "Incomplete file folder:"
#~ msgstr "Carpeta de archivos incompletos:"

#~ msgid "Download folder:"
#~ msgstr "Carpeta de descarga:"

#~ msgid "Save buddies' uploads to:"
#~ msgstr "Guardar las subidas de los amigos en:"

#~ msgid "Use UPnP to forward listening port"
#~ msgstr "Utilizar UPnP para reenviar el puerto de recepción"

#~ msgid ""
#~ "Share folders with every Soulseek user or buddies, allowing contents to "
#~ "be downloaded directly from your device. Hidden files are never shared."
#~ msgstr ""
#~ "Comparte tus carpetas con todos los usuarios o amigos de Soulseek, "
#~ "permitiendo que los contenidos se descarguen directamente desde tu "
#~ "dispositivo. Los archivos ocultos nunca se comparten."

#~ msgid "Secondary Tabs"
#~ msgstr "Pestañas secundarias"

#~ msgid "Chat room tab bar position:"
#~ msgstr "Posición de la barra de pestañas de la sala de chat:"

#~ msgid "Private chat tab bar position:"
#~ msgstr "Posición de la barra de pestañas del chat privado:"

#~ msgid "Search tab bar position:"
#~ msgstr "Posición de la barra de pestañas de la búsqueda:"

#~ msgid "User info tab bar position:"
#~ msgstr "Posición de la barra de pestañas de la información de usuario:"

#~ msgid "User browse tab bar position:"
#~ msgstr "Posición de la barra de pestañas en la exploración de usuarios:"

#~ msgid "Tab Labels"
#~ msgstr "Etiquetas de las pestañas"

#~ msgid "_Refresh Info"
#~ msgstr "_Refrescar información"

#~ msgid "Block IP Address"
#~ msgstr "Bloquear dirección IP"

#~ msgid "Connected"
#~ msgstr "Conectado"

#~ msgid "Disconnected"
#~ msgstr "Desconectado"

#~ msgid "Disconnected (Tray)"
#~ msgstr "Desconectado (Bandeja)"

#~ msgid "User(s)"
#~ msgstr "Usuario(s)"

#, python-format
#~ msgid "Alias \"%s\" returned nothing"
#~ msgstr "El alias «%s» no ha devuelto nada"

#~ msgid "Alternative Speed Limits"
#~ msgstr "Límites de velocidad alternativos"

#~ msgid "Last played"
#~ msgstr "Última reproducción"

#~ msgid "Playing now"
#~ msgstr "Reproduciendo ahora"

#, python-format
#~ msgid "Error code %(code)s: %(description)s"
#~ msgstr "Código de error %(code)s: %(description)s"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "No existe tal alias (%s)"

#~ msgid "Aliases:"
#~ msgstr "Alias:"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "Alias %(alias)s eliminado: %(action)s\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "No existe el alias (%(alias)s)\n"

#~ msgid "Aliases"
#~ msgstr "Alias"

#~ msgid "/alias /al 'command' 'definition'"
#~ msgstr "/alias /al «comando» «definición»"

#~ msgid "Add a new alias"
#~ msgstr "Añadir un nuevo alias"

#~ msgid "/unalias /un 'command'"
#~ msgstr "/unalias /un «comando»"

#~ msgid "Remove an alias"
#~ msgstr "Eliminar un alias"

#~ msgid "Chat History"
#~ msgstr "Historial del chat"

#~ msgid "Command aliases"
#~ msgstr "Alias de comandos"

#~ msgid "Limit download speed to (KiB/s):"
#~ msgstr "Limitar la velocidad de descarga a (KiB/s):"

#~ msgid "Author(s):"
#~ msgstr "Autor(es):"

#~ msgid "Limit upload speed to (KiB/s):"
#~ msgstr "Limitar la velocidad de subida a (KiB/s):"

#~ msgid "Tabs show user status icons instead of status text"
#~ msgstr ""
#~ "Las pestañas muestran los iconos de estado del usuario en lugar del texto "
#~ "de estado"

#~ msgid "Show file path tooltips in file list views"
#~ msgstr ""
#~ "Mostrar rutas de archivo emergentes en las vistas de lista de archivos"

#~ msgid "Colored and clickable usernames"
#~ msgstr "Nombres de usuario coloreados y clicables"

#~ msgid "Notification changes the tab's text color"
#~ msgstr "La notificación cambia el color del texto de la pestaña"

#~ msgid "Cancel"
#~ msgstr "Cancelar"

#~ msgid "OK"
#~ msgstr "Aceptar"

#~ msgid "_Add to Buddy List"
#~ msgstr "_Añadir a la lista de amigos"

#~ msgid "use non-default user data directory for e.g. list of downloads"
#~ msgstr ""
#~ "usar un directorio de datos de usuario no predeterminado para p.e. lista "
#~ "de descargas"

#, python-format
#~ msgid "Unknown config section '%s'"
#~ msgstr "Sección de configuración desconocida «%s»"

#, python-format
#~ msgid "Unknown config option '%(option)s' in section '%(section)s'"
#~ msgstr ""
#~ "Opción de configuración desconocida «%(option)s» en la sección "
#~ "«%(section)s»"

#, python-format
#~ msgid "Version %s is available"
#~ msgstr "La versión %s está disponible"

#, python-format
#~ msgid "released on %s"
#~ msgstr "publicada el %s"

#~ msgid "Nicotine+ is running in the background"
#~ msgstr "Nicotine+ está ejecutándose en segundo plano"

#, python-format
#~ msgid "Private message from %s"
#~ msgstr "Mensaje privado de %s"

#~ msgid "Aborted"
#~ msgstr "Interrumpida"

#~ msgid "Blocked country"
#~ msgstr "País bloqueado"

#~ msgid "Finished / Aborted"
#~ msgstr "Terminadas/interrumpidas"

#~ msgid "Close tab"
#~ msgstr "Cerrar pestaña"

#, python-format
#~ msgid "You've been mentioned in the %(room)s room"
#~ msgstr "Has sido mencionado en la sala %(room)s"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "Comando %s no reconocido"

#, python-format
#~ msgid "(Warning: %(realuser)s is attempting to spoof %(fakeuser)s) "
#~ msgstr ""
#~ "(Advertencia: %(realuser)s está intentando suplantar a %(fakeuser)s) "

#, python-format
#~ msgid ""
#~ "The network interface you specified, '%s', does not exist. Change or "
#~ "remove the specified network interface and restart Nicotine+."
#~ msgstr ""
#~ "La interfaz de red que has especificado, «%s», no existe. Cambia o "
#~ "elimina la interfaz de red especificada y reinicia Nicotine+."

#~ msgid ""
#~ "The range you specified for client connection ports was {}-{}, but none "
#~ "of these were usable. Increase and/or "
#~ msgstr ""
#~ "El rango especificado para los puertos de conexión del cliente era {}-{}, "
#~ "pero ninguno de ellos era utilizable. Aumenta o "

#~ msgid ""
#~ "Note that part of your range lies below 1024, this is usually not allowed "
#~ "on most operating systems with the exception of Windows."
#~ msgstr ""
#~ "Ten en cuenta que parte de tu rango se encuentra por debajo de 1024, esto "
#~ "no suele estar permitido en la mayoría de los sistemas operativos, a "
#~ "excepción de Windows."

#, python-format
#~ msgid "Rescan progress: %s"
#~ msgstr "Progreso del reescaneo: %s"

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "Error del SO: %s"

#~ msgid "UPnP is not available on this network"
#~ msgstr "UPnP no está disponible en esta red"

#, python-format
#~ msgid "Failed to map the external WAN port: %(error)s"
#~ msgstr "Fallo en la asignación del puerto WAN externo: %(error)s"

#~ msgid "Room wall"
#~ msgstr "Muro de la sala"

#~ msgid ""
#~ "The default listening port '2234' works fine in most cases. If you need "
#~ "to use a different port, you will be able to modify it in the preferences "
#~ "later."
#~ msgstr ""
#~ "El puerto de escucha por defecto, «2234», funciona bien en la mayoría de "
#~ "los casos. Si necesitas usar un puerto diferente podrás modificarlo en la "
#~ "configuración más adelante."

#~ msgid "Show users with similar interests"
#~ msgstr "Mostrar usuarios con intereses similares"

#~ msgid "Menu"
#~ msgstr "Menú"

#~ msgid "Expand / Collapse all"
#~ msgstr "Expandir/Contraer todo"

#~ msgid "Configure shares"
#~ msgstr "Configurar los compartidos"

#~ msgid "Create or join room…"
#~ msgstr "Crear o unirse a la sala…"

#~ msgid "_Room List"
#~ msgstr "Lista de _Salas"

#~ msgid "Enable alternative download and upload speed limits"
#~ msgstr "Habilitar límites alternativos de velocidad de descarga y subida"

#~ msgid "Show log history"
#~ msgstr "Mostrar el historial del registro"

#~ msgid "Result grouping mode"
#~ msgstr "Modo de agrupación de resultados"

#~ msgid "Free slot"
#~ msgstr "Puesto disponible"

#~ msgid "Save shares list to disk"
#~ msgstr "Guardar lista de compartidos en el disco"

#~ msgid "_Away"
#~ msgstr "_Ausente"

#, python-format
#~ msgid "Failed to load ui file %(file)s: %(error)s"
#~ msgstr "Fallo al cargar el archivo interfaz %(file)s: %(error)s"

#~ msgid ""
#~ "Attempting to reset index of shared files due to an error. Please rescan "
#~ "your shares."
#~ msgstr ""
#~ "Intentando reiniciar el índice de archivos compartidos debido a un error. "
#~ "Por favor, reescanea tus archivos compartidos."

#~ msgid ""
#~ "File index of shared files could not be accessed. This could occur due to "
#~ "several instances of Nicotine+ being active simultaneously, file "
#~ "permission issues, or another issue in Nicotine+."
#~ msgstr ""
#~ "No se pudo acceder al índice de archivos compartidos. Esto podría deberse "
#~ "a que varias instancias de Nicotine+ estén activas simultáneamente, a "
#~ "problemas de permisos de archivos o a otro problema en Nicotine+."

#~ msgid "Nicotine+ is a Soulseek client"
#~ msgstr "Nicotine+ es un cliente de Soulseek"

#~ msgid "enable the tray icon"
#~ msgstr "habilitar el icono de la bandeja de sistema"

#~ msgid "disable the tray icon"
#~ msgstr "deshabilitar el icono de la bandeja de sistema"

#~ msgid "Get Soulseek Privileges…"
#~ msgstr "Obtener privilegios de Soulseek…"

#, python-format
#~ msgid ""
#~ "Public IP address is <b>%(ip)s</b> and active listening port is "
#~ "<b>%(port)s</b>"
#~ msgstr ""
#~ "La dirección IP pública es <b>%(ip)s</b> y el puerto de escucha activo es "
#~ "<b>%(port)s</b>"

#~ msgid "unknown"
#~ msgstr "desconocido"

#~ msgid "Notification"
#~ msgstr "Notificación"

#~ msgid "Length"
#~ msgstr "Duración"

#, python-format
#~ msgid "Picture not saved, %s already exists."
#~ msgstr "Imagen no guardada, %s ya existe."

#~ msgid "_Open"
#~ msgstr "_Abrir"

#~ msgid "_Save"
#~ msgstr "_Guardar"

#, python-format
#~ msgid "Failed to load plugin '%s', could not find it."
#~ msgstr "Fallo al cargar complemento «%s», no se ha podido localizar."

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "Error de E/S: %s"

#, python-format
#~ msgid "Failed to open file path: %s"
#~ msgstr "Fallo al abrir la ruta del archivo: %s"

#, python-format
#~ msgid "Failed to open URL: %s"
#~ msgstr "Fallo al abrir la URL: %s"

#~ msgid "_Log Conversation"
#~ msgstr "_Registrar conversación"

#~ msgid "Result Filter List"
#~ msgstr "Lista de filtros de resultados"

#~ msgid "Prepend < or > to find files less/greater than the given value."
#~ msgstr ""
#~ "Anteponga ‘<’ o ‘>’ para encontrar archivos menores o mayores que el "
#~ "valor dado."

#~ msgid ""
#~ "VBR files display their average bitrate and are typically lower in "
#~ "bitrate than a compressed 320 kbps CBR file of the same audio quality."
#~ msgstr ""
#~ "Los archivos VBR muestran su tasa de bits media y suelen tener una tasa "
#~ "de bits inferior a la de un archivo CBR comprimido de 320 kbps con la "
#~ "misma calidad de audio."

#~ msgid "Like Size above, =, <, and > can be used."
#~ msgstr "Como en el caso del tamaño de arriba, se pueden utilizar =, < y >."

#~ msgid ""
#~ "Prevent write access by other programs for files being downloaded (turn "
#~ "off for NFS)"
#~ msgstr ""
#~ "Impedir el acceso de escritura de otros programas a los archivos que se "
#~ "descargan (desactivar para NFS)"

#~ msgid "Where incomplete downloads are temporarily stored."
#~ msgstr "Donde se almacenan temporalmente las descargas incompletas."

#~ msgid ""
#~ "Where buddies' uploads will be stored (with a subfolder created for each "
#~ "buddy)."
#~ msgstr ""
#~ "Donde se almacenarán las subidas de los amigos (con una subcarpeta creada "
#~ "para cada amigo)."

#~ msgid "Toggle away status after minutes of inactivity:"
#~ msgstr "Activar estado de ausencia tras estos minutos de inactividad:"

#~ msgid "Protocol:"
#~ msgstr "Protocolo:"

#~ msgid "Icon theme folder (requires restart):"
#~ msgstr "Carpeta de temas de iconos (requiere reinicio):"

#~ msgid "Establishing connection"
#~ msgstr "Estableciendo conexión"

#~ msgid "Clear Groups"
#~ msgstr "Limpiar grupos"

#~ msgid "User List"
#~ msgstr "Lista de usuarios"

#~ msgid "_Reset Statistics…"
#~ msgstr "_Reiniciar estadísticas…"

#~ msgid "Clear _Downloads…"
#~ msgstr "Limpiar _descargas…"

#~ msgid "Clear Uploa_ds…"
#~ msgstr "Limpiar _subidas…"

#, python-format
#~ msgid "File Properties (%(num)i of %(total)i)"
#~ msgstr "Propiedades del archivo (%(num)i de %(total)i)"

#~ msgid ""
#~ "* Message(s) sent while you were offline. Timestamps are reported by the "
#~ "server and can be off."
#~ msgstr ""
#~ "* Mensaje(s) recibidos(s) mientras estabas desconectado. Las marcas "
#~ "temporales provienen del servidor y pueden estar desajustadas."

#~ msgid "Block User's IP Address"
#~ msgstr "Bloquear dirección IP del usuario"

#~ msgid "Ignore User's IP Address"
#~ msgstr "Ignorar dirección IP del usuario"

#~ msgid "Usernames"
#~ msgstr "Nombres de usuario"

#~ msgid "Display logged chat room messages when a room is rejoined"
#~ msgstr ""
#~ "Mostrar el registro de mensajes de las salas de chat cuando se vuelva a "
#~ "entrar a una sala"

#~ msgid ""
#~ "Clear every download that has finished transferring or been caught by a "
#~ "filter."
#~ msgstr ""
#~ "Limpiar todas las descargas que hayan terminado de transferirse o que "
#~ "hayan sido descartadas por algún filtro."

#~ msgid ""
#~ "Clear every upload that has either finished transferring, or been "
#~ "cancelled by the remote user."
#~ msgstr ""
#~ "Limpiar todas las subidas que hayan terminado de transferirse o que hayan "
#~ "sido interrumpidas por el usuario remoto."

#~ msgid "Queue Position"
#~ msgstr "Lugar en la cola"

#, python-format
#~ msgid ""
#~ "User %(user)s is directly searching for \"%(query)s\", returning %(num)i "
#~ "results"
#~ msgstr ""
#~ "El usuario %(user)s está buscando directamente «%(query)s», devolviéndose "
#~ "%(num)i resultados"

#~ msgid "Room wall (personal message set)"
#~ msgstr "Muro de la sala (mensaje personal definido)"

#~ msgid "Your config file is corrupt"
#~ msgstr "Tu archivo de configuración está corrupto"

#, python-format
#~ msgid ""
#~ "We're sorry, but it seems your configuration file is corrupt. Please "
#~ "reconfigure Nicotine+.\n"
#~ "\n"
#~ "We renamed your old configuration file to\n"
#~ "%(corrupt)s\n"
#~ "If you open this file with a text editor you might be able to rescue some "
#~ "of your settings."
#~ msgstr ""
#~ "Lo sentimos, pero parece que su archivo de configuración está corrupto. "
#~ "Por favor, reconfigure Nicotine+.\n"
#~ "\n"
#~ "Hemos renombrado su antiguo archivo de configuración a\n"
#~ "%(corrupt)s\n"
#~ "Si abre este archivo con un editor de texto podrá rescatar algunos de sus "
#~ "ajustes."

#~ msgid "User Description"
#~ msgstr "Descripción de usuario"

#~ msgid "User Interests"
#~ msgstr "Intereses de usuario"

#~ msgid "User Picture"
#~ msgstr "Imagen del Usuario"

#~ msgid "Search Wishlist"
#~ msgstr "Buscar en la Lista de Deseos"

#, python-format
#~ msgid "Loading Nicotine+ %(nic_version)s"
#~ msgstr "Cargando Nicotine+ %(nic_version)s"

#, python-format
#~ msgid "Using Python %(py_version)s"
#~ msgstr "Usando Python %(py_version)s"

#, python-format
#~ msgid "Quitting Nicotine+ %(version)s, %(status)s…"
#~ msgstr "Cerrando Nicotine+ %(version)s, %(status)s…"

#, python-format
#~ msgid "Quit Nicotine+ %(version)s, %(status)s!"
#~ msgstr "¡Cerrar Nicotine+ %(version)s, %(status)s!"

#~ msgid "User:"
#~ msgstr "Usuario:"

#~ msgid "Close Nicotine+?"
#~ msgstr "Cerrar Nicotine+?"

#~ msgid "Do you really want to exit Nicotine+?"
#~ msgstr "¿Realmente quieres salir de Nicotine+?"

#~ msgid "Run in Background"
#~ msgstr "Ejecutar en segundo plano"

#~ msgid "_Prioritize User"
#~ msgstr "_Priorizar usuario"

#~ msgid "_Trust User"
#~ msgstr "_Usuario de confianza"

#~ msgid "Request User's IP Address"
#~ msgstr "Solicitar dirección IP de usuario"

#~ msgid "Request IP Address"
#~ msgstr "Solicitar dirección IP"

#~ msgid "Downloaded"
#~ msgstr "Descargado"

#~ msgid "Unable to Share Folder"
#~ msgstr "No es posible compartir carpeta"

#~ msgid "The chosen virtual name is empty"
#~ msgstr "El nombre virtual elegido está vacío"

#~ msgid "The chosen virtual name already exists"
#~ msgstr "El nombre virtual elegido ya existe"

#~ msgid "The chosen folder is already shared"
#~ msgstr "La carpeta elegida ya ha sido compartida"

#, python-format
#~ msgid "Enter virtual name for '%(dir)s':"
#~ msgstr "Introduce nombre virtual para '%(dir)s':"

#~ msgid "Addresses"
#~ msgstr "Direcciones"

#~ msgid "Handler"
#~ msgstr "Manejador"

#~ msgid "Transfers"
#~ msgstr "Transferencias"

#~ msgid "Ban List"
#~ msgstr "Lista de vetos"

#~ msgid "Ignore List"
#~ msgstr "Lista de ignorados"

#~ msgid "Censor & Replace"
#~ msgstr "Censurar y reemplazar"

#~ msgid "Categories"
#~ msgstr "Categorías"

#~ msgid "Wishes"
#~ msgstr "Deseos"

#~ msgid "privileged"
#~ msgstr "privilegiado"

#~ msgid "prioritized"
#~ msgstr "priorizado"

#~ msgid "Handler:"
#~ msgstr "Manejador:"

#~ msgid "Virtual Name"
#~ msgstr "Nombre virtual"

#~ msgid "Download"
#~ msgstr "Bajar"

#~ msgid "Upload"
#~ msgstr "Subir"

#~ msgid "Rename"
#~ msgstr "Renombrar"

#~ msgid "File Lists"
#~ msgstr "Listas de archivos"

#~ msgid "Copy File Path"
#~ msgstr "Copiar ruta de archivo"

#~ msgid "R_emove Wish"
#~ msgstr "R_emover deseo"

#, python-format
#~ msgid "Your buddy, %s, is attempting to upload file(s) to you."
#~ msgstr "Su amigo, %s, está intentando enviarle archivos"

#, python-format
#~ msgid ""
#~ "%s is not allowed to send you file(s), but is attempting to, anyway. "
#~ "Warning Sent."
#~ msgstr ""
#~ "%s no tiene permiso para enviarte archivos, pero está intentándolo de "
#~ "todas formas. Advertencia enviada."

#~ msgid "Client Version"
#~ msgstr "Versión del cliente"

#~ msgid "_Privileged"
#~ msgstr "_Privilegiado"

#~ msgid "Comments"
#~ msgstr "Comentarios"

#~ msgid "Scanning Buddy Shares"
#~ msgstr "Comenzando a leer los compartidos de amigos"

#~ msgid "Set your personal ticker"
#~ msgstr "Establecer su marcador personal"

#~ msgid "Are you sure you wish to exit Nicotine+ at this time?"
#~ msgstr "Está seguro de que quiere salir de Nicotine+ ahora?"

#~ msgid "Add user 'user' to your user list"
#~ msgstr "Añadir usuario 'usuario' a la lista de amigos"

#~ msgid "Remove user 'user' from your user list"
#~ msgstr "Eliminar al usuario 'usuario' de su lista de amigos"

#~ msgid "Request user info for user 'user'"
#~ msgstr "Solicitar info de usuario para 'usuario'"

#~ msgid "Find..."
#~ msgstr "Buscar..."

#~ msgid "Add..."
#~ msgstr "Añadir..."

#~ msgid "_Modes"
#~ msgstr "_Modos"

#~ msgid "_Chat Rooms"
#~ msgstr "_Habitaciones"

#~ msgid "_Private Chat"
#~ msgstr "_Privados"

#~ msgid "_Downloads"
#~ msgstr "_Descargas"

#~ msgid "_Search Files"
#~ msgstr "Bú_squeda de ficheros"

#~ msgid "User I_nfo"
#~ msgstr "I_nfo de usuario"

#~ msgid "_Interests"
#~ msgstr "_Intereses"

#~ msgid "Buddy _List"
#~ msgstr "_Lista de amigos"

#~ msgid "Enable geographical blocker"
#~ msgstr "Activar bloqueo por región geográfica"

#~ msgid "Enable URL catching"
#~ msgstr "Activar captura de URL"

#~ msgid "Geo Block"
#~ msgstr "Bloqueo Geográfico"

#~ msgid "URL Catching"
#~ msgstr "Captura de URL"
