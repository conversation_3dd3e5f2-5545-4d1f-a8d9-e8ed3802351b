# SPDX-FileCopyrightText: 2021-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2023-07-25 18:31+0000\n"
"Last-Translator: Anonymous <<EMAIL>>\n"
"Language-Team: Esperanto <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/eo/>\n"
"Language: eo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.0-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
#, fuzzy
msgid "Soulseek Client"
msgstr "Soulseek Kliento"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
#, fuzzy
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Grafika kliento por la kunul-al-kunula reto Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
#, fuzzy
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr "Soulseek;Nicotine;dividado;muziko;P2P;kunulo-al-kunulo;GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
#, fuzzy
msgid "Browse the Soulseek network"
msgstr "Grafika kliento por la kunul-al-kunula reto Soulseek"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
#, fuzzy
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+ estas grafika kliento por la kunula reto Soulseek."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
#, fuzzy
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ celas esti agrabla, senpaga kaj malferma fonto (FOSS) alternativo "
"al la oficiala Soulseek-kliento, provizante plian funkciecon konservante "
"aktuala kun la Soulseek-protokolo."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
#, fuzzy
msgid "Search Files"
msgstr "Serĉu Dosieroj"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
#, fuzzy
msgid "Downloads"
msgstr "Elŝutoj"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
#, fuzzy
msgid "Browse Shares"
msgstr "Foliumi Akciojn"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
#, fuzzy
msgid "Private Chat"
msgstr "Privata Babilejo"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Teamo Nicotine+"

#: pynicotine/__init__.py:50
#, fuzzy, python-format
msgid "Website: %s"
msgstr "Efektive: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "Montri ĉi tiun helpmesaĝon kaj eliri"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "dosiero"

#: pynicotine/__init__.py:60
#, fuzzy
msgid "use non-default configuration file"
msgstr "uzu ne-defaŭltan agordan dosieron"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "dosierujo"

#: pynicotine/__init__.py:64
#, fuzzy
msgid "alternative directory for user data and plugins"
msgstr "uzu ne-defaŭltan dosierujon por kromaĵojn"

#: pynicotine/__init__.py:68
#, fuzzy
msgid "start the program without showing window"
msgstr "lanĉu la programon sen montri fenestron"

#: pynicotine/__init__.py:71
msgid "ip"
msgstr "IP-adreso"

#: pynicotine/__init__.py:72
#, fuzzy
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "ligi ingojn al la donita IP (utila por VPN)"

#: pynicotine/__init__.py:75
msgid "port"
msgstr "pordo"

#: pynicotine/__init__.py:76
#, fuzzy
msgid "listen on the given port"
msgstr "aŭskultu sur la donita haveno"

#: pynicotine/__init__.py:80
#, fuzzy
msgid "rescan shared files"
msgstr "reskani komunajn dosierojn"

#: pynicotine/__init__.py:84
#, fuzzy
msgid "start the program in headless mode (no GUI)"
msgstr "lanĉu la programon en senkapa reĝimo (neniu GUI)"

#: pynicotine/__init__.py:88
#, fuzzy
msgid "display version and exit"
msgstr "montri versio kaj eliro"

#: pynicotine/__init__.py:121
#, fuzzy, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Vi uzas nesubtenan version de Python (%(old_version)s).\n"
"Vi devus instali Python %(min_version)s aŭ pli novan."

#: pynicotine/__init__.py:192
#, fuzzy
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Malsukcesis skani akciojn. Bonvolu fermi aliajn okazojn de Nicotine+ kaj "
"provi denove."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, fuzzy, python-format
msgid "%(user)s is away"
msgstr "Uzanto %s estas for"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, fuzzy, python-format
msgid "%(user)s is online"
msgstr "Uzanto %s estas interreta"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, fuzzy, python-format
msgid "%(user)s is offline"
msgstr "Uzanto %s estas eksterreta"

#: pynicotine/buddies.py:316
#, fuzzy
msgid "Buddy Status"
msgstr "Kamaradoj"

#: pynicotine/chatrooms.py:383
#, fuzzy, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Vi estis aldonita al privata ĉambro: %(room)s"

#: pynicotine/chatrooms.py:478
#, fuzzy, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr "Babilmesaĝo de uzanto '%(user)s' en ĉambro '%(room)s': %(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, fuzzy, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr "Ne povas krei dosierujon '%(path)s', raportita eraro: %(error)s"

#: pynicotine/config.py:816
#, fuzzy, python-format
msgid "Error backing up config: %s"
msgstr "Eraro dum sekurkopio de agordo: %s"

#: pynicotine/config.py:819
#, fuzzy, python-format
msgid "Config backed up to: %s"
msgstr "Agordo konservita al: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, fuzzy, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Forlasante Nicotine+ %(version)s, %(status)s…"

#: pynicotine/core.py:243
#, fuzzy, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Forlasante Nicotine+ %(version)s, %(status)s…"

#: pynicotine/core.py:246
#, fuzzy
msgid "terminating"
msgstr "finiĝanta"

#: pynicotine/core.py:246
#, fuzzy
msgid "application closing"
msgstr "aplikaĵo fermo"

#: pynicotine/core.py:259
#, fuzzy, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Forlasante Nicotine+ %(version)s, %(status)s…"

#: pynicotine/core.py:267
#, fuzzy
msgid "You need to specify a username and password before connecting…"
msgstr "Vi devas specifi uzantnomon kaj pasvorton antaŭ ol konekti…"

#: pynicotine/downloads.py:239
#, fuzzy, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""
"Eraro: Elŝuta Filtrilo malsukcesis! Kontrolu viajn filtrilojn. Kialo: %s"

#: pynicotine/downloads.py:254
#, fuzzy, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Eraro: %(num)d Elŝutaj filtriloj malsukcesis! %(error)s "

#: pynicotine/downloads.py:366
#, fuzzy, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s elŝutita de %(user)s"

#: pynicotine/downloads.py:370
#, fuzzy
msgid "File Downloaded"
msgstr "Dosiero elŝutita"

#: pynicotine/downloads.py:378
#, fuzzy, python-format
msgid "Executed: %s"
msgstr "Efektive: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, fuzzy, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "Ekzekutado de '%(command)s' malsukcesis: %(error)s"

#: pynicotine/downloads.py:407
#, fuzzy, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s elŝutita de %(user)s"

#: pynicotine/downloads.py:411
#, fuzzy
msgid "Folder Downloaded"
msgstr "Dosierujo elŝutita"

#: pynicotine/downloads.py:419
#, fuzzy, python-format
msgid "Executed on folder: %s"
msgstr "Efektive en dosierujo: %s"

#: pynicotine/downloads.py:443
#, fuzzy, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "Ne eblis movi '%(tempfile)s' al '%(file)s': %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
#, fuzzy
msgid "Download Folder Error"
msgstr "Elŝuta dosierujo eraro"

#: pynicotine/downloads.py:489
#, fuzzy, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Elŝuto finita: uzanto %(user)s, dosiero %(file)s"

#: pynicotine/downloads.py:499
#, fuzzy, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Elŝuto ĉesigita, uzanto %(user)s dosiero %(file)s"

#: pynicotine/downloads.py:1150
#, fuzzy, python-format
msgid "Download I/O error: %s"
msgstr "Elŝuta I/O-eraro: %s"

#: pynicotine/downloads.py:1189
#, fuzzy, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Ne povas ricevi ekskluzivan ŝlosilon en dosiero - I/O-eraro: %s"

#: pynicotine/downloads.py:1202
#, fuzzy, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Ne eblas konservi dosieron %(path)s: %(error)s"

#: pynicotine/downloads.py:1221
#, fuzzy, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Elŝuto komencita: uzanto %(user)s, dosiero %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, fuzzy, python-format
msgid "Cannot find %s, please install it."
msgstr "Ne povas trovi %s, bonvolu instali ĝin."

#: pynicotine/gtkgui/__init__.py:162
#, fuzzy
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr "Neniu grafika medio havebla, uzante senkapan (sen GUI) reĝimon"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
#, fuzzy
msgid "_Connect"
msgstr "_Konekti"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
#, fuzzy
msgid "_Disconnect"
msgstr "_Malkonekti"

#: pynicotine/gtkgui/application.py:308
#, fuzzy
msgid "Soulseek _Privileges"
msgstr "Soulseek _Privilegioj"

#: pynicotine/gtkgui/application.py:314
#, fuzzy
msgid "_Preferences"
msgstr "_Preferoj"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
#, fuzzy
msgid "_Quit"
msgstr "_Forlasu"

#: pynicotine/gtkgui/application.py:337
#, fuzzy
msgid "Browse _Public Shares"
msgstr "_Fumu Publikaj Akcioj"

#: pynicotine/gtkgui/application.py:338
#, fuzzy
msgid "Browse _Buddy Shares"
msgstr "Fol_vidu Buddy Shares"

#: pynicotine/gtkgui/application.py:339
#, fuzzy
msgid "Browse _Trusted Shares"
msgstr "Fol_vidu Buddy Shares"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
#, fuzzy
msgid "_Rescan Shares"
msgstr "_Reskanu Akciojn"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
#, fuzzy
msgid "Configure _Shares"
msgstr "_Agordu Akciojn"

#: pynicotine/gtkgui/application.py:371
#, fuzzy
msgid "_Keyboard Shortcuts"
msgstr "_Klavaraj ŝparvojoj"

#: pynicotine/gtkgui/application.py:372
#, fuzzy
msgid "_Setup Assistant"
msgstr "_Agorda Asistanto"

#: pynicotine/gtkgui/application.py:373
#, fuzzy
msgid "_Transfer Statistics"
msgstr "_Transdona Statistiko"

#: pynicotine/gtkgui/application.py:378
#, fuzzy
msgid "Report a _Bug"
msgstr "Raporti _Cimon"

#: pynicotine/gtkgui/application.py:379
#, fuzzy
msgid "Improve T_ranslations"
msgstr "Plibonigi T_tradukojn"

#: pynicotine/gtkgui/application.py:383
#, fuzzy
msgid "_About Nicotine+"
msgstr "_Pri Nicotine+"

#: pynicotine/gtkgui/application.py:394
#, fuzzy
msgid "_File"
msgstr "_Dosiero"

#: pynicotine/gtkgui/application.py:395
#, fuzzy
msgid "_Shares"
msgstr "_Agoj"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
#, fuzzy
msgid "_Help"
msgstr "_Helpu"

#: pynicotine/gtkgui/application.py:410
#, fuzzy
msgid "_Browse Shares"
msgstr "Foliumi Akciojn"

#: pynicotine/gtkgui/application.py:465
#, fuzzy, python-format
msgid "Unable to show notification: %s"
msgstr "Ne eblas montri sciigan ŝprucfenestron: %s"

#: pynicotine/gtkgui/application.py:526
#, fuzzy
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Ĉu vi vere volas eliri Nicotine+?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr ""

#: pynicotine/gtkgui/application.py:529
#, fuzzy
msgid "Do you really want to exit?"
msgstr "Ĉu vi vere volas eliri Nicotine+?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
#, fuzzy
msgid "_No"
msgstr "Ne"

#: pynicotine/gtkgui/application.py:535
#, fuzzy
msgid "_Run in Background"
msgstr "Kuru en Fono"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
#, fuzzy
msgid "Quit Nicotine+"
msgstr "Forlasu Nicotine+"

#: pynicotine/gtkgui/application.py:561
#, fuzzy
msgid "Shares Not Available"
msgstr "Akcioj Ne Disponeblaj"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
#, fuzzy
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Kontrolu, ke eksteraj diskoj estas muntitaj kaj ke permesoj de dosierujo "
"estas ĝustaj."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
#, fuzzy
msgid "_Cancel"
msgstr "_Nuligi"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
#, fuzzy
msgid "_Retry"
msgstr "_Reprovu"

#: pynicotine/gtkgui/application.py:567
#, fuzzy
msgid "_Force Rescan"
msgstr "_Forto Rescan"

#: pynicotine/gtkgui/application.py:742
#, fuzzy
msgid "Message Downloading Users"
msgstr "Mesaĝo Elŝutanta Uzantoj"

#: pynicotine/gtkgui/application.py:743
#, fuzzy
msgid "Send private message to all users who are downloading from you:"
msgstr "Sendu privatan mesaĝon al ĉiuj uzantoj, kiuj elŝutas de vi:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
#, fuzzy
msgid "_Send Message"
msgstr "Sendi mesaĝon"

#: pynicotine/gtkgui/application.py:756
#, fuzzy
msgid "Message Buddies"
msgstr "Mesaĝoj"

#: pynicotine/gtkgui/application.py:757
#, fuzzy
msgid "Send private message to all online buddies:"
msgstr "Sendu privatan mesaĝon al ĉiuj interretaj amikoj:"

#: pynicotine/gtkgui/application.py:786
#, fuzzy
msgid "Select a Saved Shares List File"
msgstr "Elektu Konservitan Kundividan Liston Dosieron"

#: pynicotine/gtkgui/application.py:877
#, fuzzy
msgid "Critical Error"
msgstr "Kritika Eraro"

#: pynicotine/gtkgui/application.py:878
#, fuzzy
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ renkontis kritikan eraron kaj devas eliri. Bonvolu kopii la sekvan "
"mesaĝon kaj inkluzivi ĝin en cimraporton:"

#: pynicotine/gtkgui/application.py:882
#, fuzzy
msgid "_Quit Nicotine+"
msgstr "Forlasu Nicotine+"

#: pynicotine/gtkgui/application.py:883
#, fuzzy
msgid "_Copy & Report Bug"
msgstr "Kopiu & Raporti Cimon"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
#, fuzzy
msgid "Status"
msgstr "Statuso"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
#, fuzzy
msgid "Country"
msgstr "Lando"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
#, fuzzy
msgid "User"
msgstr "Uzanto"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
#, fuzzy
msgid "Speed"
msgstr "Rapido"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
#, fuzzy
msgid "Files"
msgstr "Dosieroj"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
#, fuzzy
msgid "Trusted"
msgstr "Fidinda"

#: pynicotine/gtkgui/buddies.py:108
#, fuzzy
msgid "Notify"
msgstr "Sciigi"

#: pynicotine/gtkgui/buddies.py:114
#, fuzzy
msgid "Prioritized"
msgstr "Prioritatigita"

#: pynicotine/gtkgui/buddies.py:120
#, fuzzy
msgid "Last Seen"
msgstr "Laste Vidita"

#: pynicotine/gtkgui/buddies.py:126
#, fuzzy
msgid "Note"
msgstr "Notu"

#: pynicotine/gtkgui/buddies.py:143
#, fuzzy
msgid "Add User _Note…"
msgstr "Aldoni Uzanton _Noton…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
#, fuzzy
msgid "Remove"
msgstr "Forigi"

#: pynicotine/gtkgui/buddies.py:364
#, fuzzy
msgid "Never seen"
msgstr "Neniam vidite"

#: pynicotine/gtkgui/buddies.py:529
#, fuzzy
msgid "Add User Note"
msgstr "Aldonu Uzantan Noton"

#: pynicotine/gtkgui/buddies.py:530
#, fuzzy, python-format
msgid "Add a note about user %s:"
msgstr "Aldonu noton pri uzanto %s:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
#, fuzzy
msgid "_Add"
msgstr "_Aldonu…"

#: pynicotine/gtkgui/chatrooms.py:224
#, fuzzy
msgid "Create New Room?"
msgstr "Ĉu krei novan ĉambron?"

#: pynicotine/gtkgui/chatrooms.py:225
#, fuzzy, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Ĉu vi vere volas krei novan ĉambron \"%s\"?"

#: pynicotine/gtkgui/chatrooms.py:226
#, fuzzy
msgid "Make room private"
msgstr "Faru ĉambron privata"

#: pynicotine/gtkgui/chatrooms.py:515
#, fuzzy
msgid "Search activity log…"
msgstr "Serĉa termino…"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
#, fuzzy
msgid "Search chat log…"
msgstr "Serĉa termino…"

#: pynicotine/gtkgui/chatrooms.py:597
#, fuzzy
msgid "Sear_ch User's Files"
msgstr "Serĉu la dosierojn de la uzanto"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
#, fuzzy
msgid "Find…"
msgstr "Trovu…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
#, fuzzy
msgid "Copy"
msgstr "Kopiu"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
#, fuzzy
msgid "Copy All"
msgstr "Kopiu Ĉion"

#: pynicotine/gtkgui/chatrooms.py:608
#, fuzzy
msgid "Clear Activity View"
msgstr "Klara Agado-Vido"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
#, fuzzy
msgid "_Leave Room"
msgstr "_Forlasu Ĉambron"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
#, fuzzy
msgid "Copy Link"
msgstr "Kopiu Ligo"

#: pynicotine/gtkgui/chatrooms.py:624
#, fuzzy
msgid "View Room Log"
msgstr "Rigardu Ĉambran Protokolon"

#: pynicotine/gtkgui/chatrooms.py:627
#, fuzzy
msgid "Delete Room Log…"
msgstr "Forigi ĉambran protokolon…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
#, fuzzy
msgid "Clear Message View"
msgstr "Klara Mesaĝa Vido"

#: pynicotine/gtkgui/chatrooms.py:832
#, fuzzy, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s menciis vin en la %(room)s ĉambro"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, fuzzy, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "Mesaĝo de %(user)s en la %(room)s ĉambro"

#: pynicotine/gtkgui/chatrooms.py:855
#, fuzzy, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "Mesaĝo de %(user)s en la %(room)s ĉambro"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, fuzzy, python-format
msgid "%s joined the room"
msgstr "%s aliĝis al la ĉambro"

#: pynicotine/gtkgui/chatrooms.py:937
#, fuzzy, python-format
msgid "%s left the room"
msgstr "%s forlasis la ĉambron"

#: pynicotine/gtkgui/chatrooms.py:1095
#, fuzzy, python-format
msgid "%s has gone away"
msgstr "%s foriris"

#: pynicotine/gtkgui/chatrooms.py:1098
#, fuzzy, python-format
msgid "%s has returned"
msgstr "%s revenis"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
#, fuzzy
msgid "Delete Logged Messages?"
msgstr "Ĉu forigi ensalutitajn mesaĝojn?"

#: pynicotine/gtkgui/chatrooms.py:1197
#, fuzzy
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"Ĉu vi vere volas konstante forigi ĉiujn registritajn mesaĝojn por ĉi tiu "
"ĉambro?"

#: pynicotine/gtkgui/dialogs/about.py:405
#, fuzzy
msgid "About"
msgstr "Ĝibutio"

#: pynicotine/gtkgui/dialogs/about.py:415
#, fuzzy
msgid "Website"
msgstr "Efektive: %s"

#: pynicotine/gtkgui/dialogs/about.py:457
#, fuzzy, python-format
msgid "Error checking latest version: %s"
msgstr "Eraro dum reakiro de la plej nova versio"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr ""

#: pynicotine/gtkgui/dialogs/about.py:467
#, fuzzy
msgid "Up to date"
msgstr "Ĝisdata"

#: pynicotine/gtkgui/dialogs/about.py:496
#, fuzzy
msgid "Checking latest version…"
msgstr "Kontrolu _Lastan Version"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
#, fuzzy
msgid "Setup Assistant"
msgstr "_Agorda Asistanto"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
#, fuzzy
msgid "Virtual Folder"
msgstr "Virtuala Dosierujo"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
#, fuzzy
msgid "Folder"
msgstr "Dosierujo"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#, fuzzy
msgid "_Previous"
msgstr "Antaŭa dosiero"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#, fuzzy
msgid "_Finish"
msgstr "Finita"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
#, fuzzy
msgid "_Next"
msgstr "_Sekva"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
#, fuzzy
msgid "Add a Shared Folder"
msgstr "Aldonu Komunan Dosierujon"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
#, fuzzy
msgid "Edit Shared Folder"
msgstr "Komunaj Dosierujoj"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, fuzzy, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Enigu novan virtualan nomon por '%(dir)s':"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
#, fuzzy
msgid "_Edit"
msgstr "Redaktado"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, fuzzy, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"Uzanto %s jam ekzistas, kaj la pasvorto, kiun vi enigis, estas malvalida. "
"Bonvolu elekti alian uzantnomon se ĉi tio estas via unua fojo ensalutanta."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
#, fuzzy
msgid "File Properties"
msgstr "Dosieraj Propraĵoj"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, fuzzy, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "Dosieraj Propraĵoj (%(num)i de %(total)i)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, fuzzy, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Dosieraj Propraĵoj (%(num)i de %(total)i)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
#, fuzzy
msgid "_Apply"
msgstr "Apliki"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
#, fuzzy
msgid "Add…"
msgstr "Aldoni…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
#, fuzzy
msgid "Edit…"
msgstr "Redaktu…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, fuzzy, python-format
msgid "%s Settings"
msgstr "%s Agordoj"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
#, fuzzy
msgid "Add Item"
msgstr "Ero"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
#, fuzzy
msgid "Edit Item"
msgstr "Interesoj"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
#, fuzzy
msgid "Unknown"
msgstr "Nekonata"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
#, fuzzy
msgid "Check Port Status"
msgstr "Kontrolu Havenan Statuon"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, fuzzy, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, haveno %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
#, fuzzy
msgid "Password Change Rejected"
msgstr "Pasvorto Ŝanĝo Malakceptita"

#: pynicotine/gtkgui/dialogs/preferences.py:218
#, fuzzy
msgid "Enter a new password for your Soulseek account:"
msgstr "Enigu novan pasvorton por via Soulseek-konto:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
#, fuzzy
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"Vi estas nuntempe elsalutita el la Soulseek-reto. Se vi volas ŝanĝi la "
"pasvorton de ekzistanta Soulseek-konto, vi devas esti ensalutinta en tiun "
"konton."

#: pynicotine/gtkgui/dialogs/preferences.py:223
#, fuzzy
msgid "Enter password to use when logging in:"
msgstr "Enigu pasvorton por uzi dum ensaluto:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
#, fuzzy
msgid "Change Password"
msgstr "Ŝanĝi Pasvorton"

#: pynicotine/gtkgui/dialogs/preferences.py:230
#, fuzzy
msgid "_Change"
msgstr "Pasvorto Ŝanĝo Malakceptita"

#: pynicotine/gtkgui/dialogs/preferences.py:274
#, fuzzy
msgid "No one"
msgstr "Neniu"

#: pynicotine/gtkgui/dialogs/preferences.py:275
#, fuzzy
msgid "Everyone"
msgstr "Ĉiuj"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
#, fuzzy
msgid "Buddies"
msgstr "Kamaradoj"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
#, fuzzy
msgid "Trusted buddies"
msgstr "Fidindaj Kamaradoj"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
#, fuzzy
msgid "Nothing"
msgstr "Nenio"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
#, fuzzy
msgid "Open File"
msgstr "Sekva dosiero"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
#, fuzzy
msgid "Open in File Manager"
msgstr "_Malfermu en Dosiera Administranto"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
#, fuzzy
msgid "Search"
msgstr "Serĉu"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
#, fuzzy
msgid "Pause"
msgstr "Paŭzo"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
#, fuzzy
msgid "Resume"
msgstr "Rekomenci"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
#, fuzzy
msgid "Browse Folder"
msgstr "_Frumu dosierujo(j)n"

#: pynicotine/gtkgui/dialogs/preferences.py:317
#, fuzzy
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Sintakso</b>: Majuskle nedistinga. Se enŝaltite, Python-regulaj esprimoj "
"povas esti uzataj, alie nur ĵokeraj * kongruoj estas subtenataj."

#: pynicotine/gtkgui/dialogs/preferences.py:327
#, fuzzy
msgid "Filter"
msgstr "Filtrilo"

#: pynicotine/gtkgui/dialogs/preferences.py:334
#, fuzzy
msgid "Regex"
msgstr "Regex"

#: pynicotine/gtkgui/dialogs/preferences.py:468
#, fuzzy
msgid "Add Download Filter"
msgstr "Aldonu Elŝutan Filtrilon"

#: pynicotine/gtkgui/dialogs/preferences.py:469
#, fuzzy
msgid "Enter a new download filter:"
msgstr "Enigu novan elŝutan filtrilon:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
#, fuzzy
msgid "Enable regular expressions"
msgstr "Ebligu regulajn esprimojn"

#: pynicotine/gtkgui/dialogs/preferences.py:498
#, fuzzy
msgid "Edit Download Filter"
msgstr "Redaktu Elŝutan Filtrilon"

#: pynicotine/gtkgui/dialogs/preferences.py:499
#, fuzzy
msgid "Modify the following download filter:"
msgstr "Modifi la sekvan elŝutan filtrilon:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, fuzzy, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d Malsukcesis! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
#, fuzzy
msgid "Filters Successful"
msgstr "Filtriloj Sukcesaj"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
#, fuzzy
msgid "Abort"
msgstr "Ĉesigi"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
#, fuzzy
msgid "Retry"
msgstr "_Reprovu"

#: pynicotine/gtkgui/dialogs/preferences.py:828
#, fuzzy
msgid "Round Robin"
msgstr "Ĉirkaŭvojo"

#: pynicotine/gtkgui/dialogs/preferences.py:829
#, fuzzy
msgid "First In, First Out"
msgstr "First In, First Out"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
#, fuzzy
msgid "Username"
msgstr "Uzantnomo"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
#, fuzzy
msgid "IP Address"
msgstr "Bloki IP-adreson"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
#, fuzzy
msgid "Ignore User"
msgstr "Ignoru Uzanton"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
#, fuzzy
msgid "Enter the name of the user you want to ignore:"
msgstr "Enigu la nomon de la uzanto, kiun vi volas ignori:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
#, fuzzy
msgid "Ignore IP Address"
msgstr "Ignoru IP-adreson"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#, fuzzy
msgid "Enter an IP address you want to ignore:"
msgstr "Enigu IP-adreson, kiun vi volas ignori:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
#, fuzzy
msgid "* is a wildcard"
msgstr "* estas ĵokero"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
#, fuzzy
msgid "Ban User"
msgstr "Malpermesu Uzanton"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
#, fuzzy
msgid "Enter the name of the user you want to ban:"
msgstr "Enigu la nomon de la uzanto, kiun vi volas malpermesi:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
#, fuzzy
msgid "Ban IP Address"
msgstr "Bloki IP-adreson"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
#, fuzzy
msgid "Enter an IP address you want to ban:"
msgstr "Enigu IP-adreson, kiun vi volas bloki:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
#, fuzzy
msgid "Pattern"
msgstr "Ŝablono"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
#, fuzzy
msgid "Replacement"
msgstr "Anstataŭaĵo"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
#, fuzzy
msgid "Censor Pattern"
msgstr "Cenzura Ŝablono"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
#, fuzzy
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Enigu ŝablonon, kiun vi volas cenzuri. Aldonu spacojn ĉirkaŭ la ŝablono se "
"vi ne volas kongrui ŝnurojn ene de vortoj (povas malsukcesi ĉe la komenco "
"kaj fino de linioj)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
#, fuzzy
msgid "Edit Censored Pattern"
msgstr "Cenzuritaj Ŝablonoj"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
#, fuzzy
msgid "Add Replacement"
msgstr "Anstataŭaĵo"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
#, fuzzy
msgid "Enter a text pattern and what to replace it with:"
msgstr "Enigu la tekstan ŝablonon kaj anstataŭaĵon, respektive:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
#, fuzzy
msgid "Edit Replacement"
msgstr "Anstataŭaĵo"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
#, fuzzy
msgid "System default"
msgstr "Defaŭlte"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
#, fuzzy
msgid "Show confirmation dialog"
msgstr "Montru konfirman dialogon"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
#, fuzzy
msgid "Run in the background"
msgstr "Kuru en la fono"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
#, fuzzy
msgid "bold"
msgstr "aŭdaca"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
#, fuzzy
msgid "italic"
msgstr "kursivo"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
#, fuzzy
msgid "underline"
msgstr "substreki"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
#, fuzzy
msgid "normal"
msgstr "normala"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
#, fuzzy
msgid "Separate Buddies tab"
msgstr "Mesaĝoj"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
#, fuzzy
msgid "Sidebar in Chat Rooms tab"
msgstr "Listo de amikoj en Babilejoj"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr ""

#: pynicotine/gtkgui/dialogs/preferences.py:1777
#, fuzzy
msgid "Top"
msgstr "Supre"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
#, fuzzy
msgid "Bottom"
msgstr "Fundo"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
#, fuzzy
msgid "Left"
msgstr "Maldekstre"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
#, fuzzy
msgid "Right"
msgstr "Ĝuste"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
#, fuzzy
msgid "Online"
msgstr "Rete"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
#, fuzzy
msgid "Away"
msgstr "For"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
#, fuzzy
msgid "Offline"
msgstr "Senrete"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
#, fuzzy
msgid "Tab Changed"
msgstr "Pasvorto Ŝanĝo Malakceptita"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
#, fuzzy
msgid "Tab Highlight"
msgstr "Emfazu"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
#, fuzzy
msgid "Window"
msgstr "Fenestro"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
#, fuzzy
msgid "Online (Tray)"
msgstr "Konektita (Pleto)"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
#, fuzzy
msgid "Away (Tray)"
msgstr "For (Pleto)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
#, fuzzy
msgid "Offline (Tray)"
msgstr "Senrete"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
#, fuzzy
msgid "Message (Tray)"
msgstr "Mesaĝo (Pleto)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
#, fuzzy
msgid "Protocol"
msgstr "Protokolo"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
#, fuzzy
msgid "Command"
msgstr "Komando:"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
#, fuzzy
msgid "Add URL Handler"
msgstr "URL-traktiloj"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
#, fuzzy
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Enigu la protokolon kaj komandon por la URL-manilo, respektive:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
#, fuzzy
msgid "Edit Command"
msgstr "Komando:"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, fuzzy, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Enigu novan virtualan nomon por '%(dir)s':"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
#, fuzzy
msgid "Username;APIKEY"
msgstr "Uzantnomo;APIKEY:"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
#, fuzzy
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr "Klientonomo (ekz. amarok, aŭdaca, exaile) aŭ malplena por aŭto:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
#, fuzzy
msgid "Username: "
msgstr "Uzantnomo:"

#: pynicotine/gtkgui/dialogs/preferences.py:2767
#, fuzzy
msgid "Command:"
msgstr "Komando:"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
#, fuzzy
msgid "Title"
msgstr "Titolo"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, fuzzy, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Nun Ludante (tipe \"%(artist)s - %(title)s\")"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
#, fuzzy
msgid "Artist"
msgstr "Artisto"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
#, fuzzy
msgid "Duration"
msgstr "Daŭro"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
#, fuzzy
msgid "Bitrate"
msgstr "Bitrate"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
#, fuzzy
msgid "Comment"
msgstr "Komento"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
#, fuzzy
msgid "Album"
msgstr "Albumo"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
#, fuzzy
msgid "Track Number"
msgstr "Voja Nombro"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
#, fuzzy
msgid "Year"
msgstr "Jaro"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
#, fuzzy
msgid "Filename (URI)"
msgstr "Dosiernomo (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
#, fuzzy
msgid "Program"
msgstr "Programo"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
#, fuzzy
msgid "Enabled"
msgstr "Ebligita"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
#, fuzzy
msgid "Plugin"
msgstr "Kromaĵoj"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
#, fuzzy
msgid "No Plugin Selected"
msgstr "Neniu Kromaĵo Elektita"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
#, fuzzy
msgid "Preferences"
msgstr "Preferoj"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
#, fuzzy
msgid "Network"
msgstr "Reto"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
#, fuzzy
msgid "User Interface"
msgstr "Uzantinterfaco"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
#, fuzzy
msgid "Shares"
msgstr "Akcioj"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
#, fuzzy
msgid "Uploads"
msgstr "Alŝutoj"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
#, fuzzy
msgid "Searches"
msgstr "Serĉoj"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
#, fuzzy
msgid "User Profile"
msgstr "Bildo de uzanto"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
#, fuzzy
msgid "Chats"
msgstr "Babilejoj"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
#, fuzzy
msgid "Now Playing"
msgstr "Nun Ludante"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
#, fuzzy
msgid "Logging"
msgstr "Enhavo"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
#, fuzzy
msgid "Banned Users"
msgstr "Malpermesitaj Uzantoj"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
#, fuzzy
msgid "Ignored Users"
msgstr "Ignoritaj Uzantoj"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
#, fuzzy
msgid "URL Handlers"
msgstr "URL-traktiloj"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
#, fuzzy
msgid "Plugins"
msgstr "Kromaĵoj"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
#, fuzzy
msgid "Pick a File Name for Config Backup"
msgstr "Elektu Dosiernomon por Agorda Rezervo"

#: pynicotine/gtkgui/dialogs/statistics.py:75
#, fuzzy
msgid "Transfer Statistics"
msgstr "Transiga Statistiko"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, fuzzy, python-format
msgid "Total Since %(date)s"
msgstr "Sumo ekde %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
#, fuzzy
msgid "Reset Transfer Statistics?"
msgstr "Restarigi Transigajn Statistikojn?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
#, fuzzy
msgid "Do you really want to reset transfer statistics?"
msgstr "Ĉu vi vere volas restarigi transigajn statistikojn?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
#, fuzzy
msgid "Wishlist"
msgstr "_Dezirlisto"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
#, fuzzy
msgid "Wish"
msgstr "Deziru"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
#, fuzzy
msgid "_Search for Item"
msgstr "_Serĉi Eron"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
#, fuzzy
msgid "Edit Wish"
msgstr "Redaktado"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, fuzzy, python-format
msgid "Enter new value for wish '%s':"
msgstr "Enigu novan virtualan nomon por '%(dir)s':"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
#, fuzzy
msgid "Clear Wishlist?"
msgstr "Ĉu forigi dezirliston?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
#, fuzzy
msgid "Do you really want to clear your wishlist?"
msgstr "Ĉu vi vere volas forigi vian dezirliston?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
#, fuzzy
msgid "Path"
msgstr "Vojo"

#: pynicotine/gtkgui/downloads.py:47
#, fuzzy
msgid "_Resume"
msgstr "_Rekomenci"

#: pynicotine/gtkgui/downloads.py:48
#, fuzzy
msgid "P_ause"
msgstr "P_aŭzo"

#: pynicotine/gtkgui/downloads.py:72
#, fuzzy
msgid "Finished / Filtered"
msgstr "Finita / Filtrita"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
#, fuzzy
msgid "Finished"
msgstr "Finita"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
#, fuzzy
msgid "Paused"
msgstr "Paŭzita"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
#, fuzzy
msgid "Filtered"
msgstr "Filtrita"

#: pynicotine/gtkgui/downloads.py:77
#, fuzzy
msgid "Deleted"
msgstr "Forigita"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
#, fuzzy
msgid "Queued…"
msgstr "Envico…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
#, fuzzy
msgid "Everything…"
msgstr "Ĉio…"

#: pynicotine/gtkgui/downloads.py:132
#, fuzzy, python-format
msgid "Downloads: %(speed)s"
msgstr "Elŝutoj: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
#, fuzzy
msgid "Clear Queued Downloads"
msgstr "Forigi Vidovicajn Elŝutojn"

#: pynicotine/gtkgui/downloads.py:139
#, fuzzy
msgid "Do you really want to clear all queued downloads?"
msgstr "Ĉu vi vere volas forigi ĉiujn vicajn elŝutojn?"

#: pynicotine/gtkgui/downloads.py:151
#, fuzzy
msgid "Clear All Downloads"
msgstr "Forigi Ĉiuj Elŝutoj"

#: pynicotine/gtkgui/downloads.py:152
#, fuzzy
msgid "Do you really want to clear all downloads?"
msgstr "Ĉu vi vere volas forigi ĉiujn elŝutojn?"

#: pynicotine/gtkgui/downloads.py:169
#, fuzzy, python-format
msgid "Download %(num)i files?"
msgstr "Ĉu elŝuti %(num)i dosierojn?"

#: pynicotine/gtkgui/downloads.py:170
#, fuzzy, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"Ĉu vi vere volas elŝuti %(num)i dosierojn el la dosierujo de %(user)s "
"%(folder)s?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
#, fuzzy
msgid "_Download Folder"
msgstr "_Elŝutu Dosierujon"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
#, fuzzy
msgid "Likes"
msgstr "Ŝatas"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
#, fuzzy
msgid "Dislikes"
msgstr "Malŝatas"

#: pynicotine/gtkgui/interests.py:104
#, fuzzy
msgid "Rating"
msgstr "Taksado"

#: pynicotine/gtkgui/interests.py:111
#, fuzzy
msgid "Item"
msgstr "Ero"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
#, fuzzy
msgid "_Recommendations for Item"
msgstr "_Rekomendoj por Item"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
#, fuzzy
msgid "I _Like This"
msgstr "Mi ŝatas tion ĉi"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
#, fuzzy
msgid "I _Dislike This"
msgstr "Mi _Malŝatas Ĉi tion"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
#, fuzzy
msgid "Recommendations"
msgstr "Rekomendoj"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
#, fuzzy
msgid "Similar Users"
msgstr "Similaj Uzantoj"

#: pynicotine/gtkgui/interests.py:427
#, fuzzy, python-format
msgid "Recommendations (%s)"
msgstr "Rekomendoj"

#: pynicotine/gtkgui/interests.py:451
#, fuzzy, python-format
msgid "Similar Users (%s)"
msgstr "Similaj Uzantoj"

#: pynicotine/gtkgui/mainwindow.py:238
#, fuzzy
msgid "Search log…"
msgstr "Serĉa termino…"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, fuzzy, python-format
msgid "Private Message from %(user)s"
msgstr "Privata Mesaĝo de %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
#, fuzzy
msgid "Wishlist Results Found"
msgstr "Dezirlisto Rezultoj Trovita"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
#, fuzzy
msgid "User Profiles"
msgstr "Bildo de uzanto"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
#, fuzzy
msgid "Chat Rooms"
msgstr "Babilejoj"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
#, fuzzy
msgid "Interests"
msgstr "Interesoj"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
#, fuzzy
msgid "Chat"
msgstr "Babilado"

#: pynicotine/gtkgui/mainwindow.py:1111
#, fuzzy
msgid "[Debug] Connections"
msgstr "Konektoj"

#: pynicotine/gtkgui/mainwindow.py:1112
#, fuzzy
msgid "[Debug] Messages"
msgstr "Mesaĝoj"

#: pynicotine/gtkgui/mainwindow.py:1113
#, fuzzy
msgid "[Debug] Transfers"
msgstr "Translokigoj"

#: pynicotine/gtkgui/mainwindow.py:1114
#, fuzzy
msgid "[Debug] Miscellaneous"
msgstr "Diversaj"

#: pynicotine/gtkgui/mainwindow.py:1119
#, fuzzy
msgid "_Find…"
msgstr "Trovu…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
#, fuzzy
msgid "_Copy"
msgstr "Kopiu"

#: pynicotine/gtkgui/mainwindow.py:1122
#, fuzzy
msgid "Copy _All"
msgstr "Kopiu Ĉion"

#: pynicotine/gtkgui/mainwindow.py:1127
#, fuzzy
msgid "View _Debug Logs"
msgstr "Vidi Sencimigajn Protokolojn"

#: pynicotine/gtkgui/mainwindow.py:1128
#, fuzzy
msgid "View _Transfer Logs"
msgstr "Vidi Translokigan Protokolon"

#: pynicotine/gtkgui/mainwindow.py:1132
#, fuzzy
msgid "_Log Categories"
msgstr "Kategorioj"

#: pynicotine/gtkgui/mainwindow.py:1134
#, fuzzy
msgid "Clear Log View"
msgstr "Klara Ŝtipvido"

#: pynicotine/gtkgui/mainwindow.py:1199
#, fuzzy
msgid "Preparing Shares"
msgstr "Skanado de Akcioj"

#: pynicotine/gtkgui/mainwindow.py:1210
#, fuzzy
msgid "Scanning Shares"
msgstr "Skanado de Akcioj"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
#, fuzzy
msgid "Shared Folders"
msgstr "Komunaj Dosierujoj"

#: pynicotine/gtkgui/popovers/chathistory.py:77
#, fuzzy
msgid "Latest Message"
msgstr "Sendi mesaĝon"

#: pynicotine/gtkgui/popovers/roomlist.py:66
#, fuzzy
msgid "Room"
msgstr "Ĉambro"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
#, fuzzy
msgid "Users"
msgstr "Uzantoj"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
#, fuzzy
msgid "Join Room"
msgstr "Aliĝu al Ĉambro"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
#, fuzzy
msgid "Leave Room"
msgstr "Forlasu Ĉambron"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
#, fuzzy
msgid "Disown Private Room"
msgstr "Disown Privata Ĉambro"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
#, fuzzy
msgid "Cancel Room Membership"
msgstr "Nuligi Ĉambron Membrecon"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
#, fuzzy
msgid "Close All Tabs…"
msgstr "Fermu ĉiujn langetojn…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
#, fuzzy
msgid "_Close Tab"
msgstr "_Fermu Tab"

#: pynicotine/gtkgui/privatechat.py:379
#, fuzzy
msgid "View Chat Log"
msgstr "Rigardu Babilejan Protokolon"

#: pynicotine/gtkgui/privatechat.py:382
#, fuzzy
msgid "Delete Chat Log…"
msgstr "Forigi babilejan protokolon…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
#, fuzzy
msgid "User Actions"
msgstr "Translokigaj Agoj"

#: pynicotine/gtkgui/privatechat.py:477
#, fuzzy
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Ĉu vi vere volas konstante forigi ĉiujn registritajn mesaĝojn por ĉi tiu "
"uzanto?"

#: pynicotine/gtkgui/privatechat.py:528
#, fuzzy
msgid "* Messages sent while you were offline"
msgstr ""
"* Mesaĝo(j) sendita(j) dum vi estis eksterrete. Tempmarkoj estas raportitaj "
"de la servilo kaj povas esti malŝaltitaj."

#: pynicotine/gtkgui/search.py:90
#, fuzzy
msgid "_Global"
msgstr "Tutmonda"

#: pynicotine/gtkgui/search.py:91
#, fuzzy
msgid "_Buddies"
msgstr "Kamaradoj"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
#, fuzzy
msgid "_Rooms"
msgstr "Ĉambroj"

#: pynicotine/gtkgui/search.py:93
#, fuzzy
msgid "_User"
msgstr "Uzanto"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
#, fuzzy
msgid "In Queue"
msgstr "En Vico"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
#, fuzzy
msgid "File Type"
msgstr "Dosiertipo"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
#, fuzzy
msgid "Filename"
msgstr "Dosiernomo"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
#, fuzzy
msgid "Size"
msgstr "Grandeco"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
#, fuzzy
msgid "Quality"
msgstr "Kvalito"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
#, fuzzy
msgid "Copy _File Path"
msgstr "Kopiu _Dosiera Vojo"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
#, fuzzy
msgid "Copy _URL"
msgstr "Kopiu _URL"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
#, fuzzy
msgid "Copy Folder U_RL"
msgstr "Kopiu Dosierujo U_RL"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
#, fuzzy
msgid "_Download File(s)"
msgstr "_Elŝutu dosiero(j)n"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
#, fuzzy
msgid "Download File(s) _To…"
msgstr "Elŝutu dosiero(j)n _Al…"

#: pynicotine/gtkgui/search.py:615
#, fuzzy
msgid "Download _Folder(s)"
msgstr "Elŝutu _dosierujo(j)n"

#: pynicotine/gtkgui/search.py:616
#, fuzzy
msgid "Download F_older(s) To…"
msgstr "Elŝutu F_older(j) Al…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
#, fuzzy
msgid "View User _Profile"
msgstr "Rigardu Uzantan _Profilon"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
#, fuzzy
msgid "_Browse Folder"
msgstr "_Frumu dosierujo(j)n"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
#, fuzzy
msgid "F_ile Properties"
msgstr "Propraĵoj de dosierujo"

#: pynicotine/gtkgui/search.py:629
#, fuzzy
msgid "Copy Search Term"
msgstr "Kopiu Serĉtermino"

#: pynicotine/gtkgui/search.py:631
#, fuzzy
msgid "Clear All Results"
msgstr "Forigi Ĉiuj Rezultoj"

#: pynicotine/gtkgui/search.py:718
#, fuzzy
msgid "Clear Filters"
msgstr "Malplenigi Filtrilan Historion"

#: pynicotine/gtkgui/search.py:721
#, fuzzy
msgid "Restore Filters"
msgstr "_Rezultaj Filtriloj"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, fuzzy, python-format
msgid "[PRIVATE]  %s"
msgstr "[PRIVATA]"

#: pynicotine/gtkgui/search.py:1273
#, fuzzy, python-format
msgid "_Result Filters [%d]"
msgstr "_Rezultaj Filtriloj [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
#, fuzzy
msgid "_Result Filters"
msgstr "_Rezultaj Filtriloj"

#: pynicotine/gtkgui/search.py:1277
#, fuzzy, python-format
msgid "%d active filter(s)"
msgstr "Forigi ĉiujn aktivajn filtrilojn"

#: pynicotine/gtkgui/search.py:1329
#, fuzzy
msgid "Add Wi_sh"
msgstr "Aldonu Wi_sh"

#: pynicotine/gtkgui/search.py:1332
#, fuzzy
msgid "Remove Wi_sh"
msgstr "Forigi Wi_sh"

#: pynicotine/gtkgui/search.py:1349
#, fuzzy
msgid "Select User's Results"
msgstr "Elektu Uzantajn Translokiĝojn"

#: pynicotine/gtkgui/search.py:1472
#, fuzzy, python-format
msgid "Total: %s"
msgstr "Entute"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
#, fuzzy
msgid "Results"
msgstr "Rezulto"

#: pynicotine/gtkgui/search.py:1590
#, fuzzy
msgid "Select Destination Folder for File(s)"
msgstr "Elektu Celon por Elŝuti Dosieron(j)n de Uzanto"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
#, fuzzy
msgid "Select Destination Folder"
msgstr "Elektu Dosierujon"

#: pynicotine/gtkgui/transfers.py:61
#, fuzzy
msgid "Queued"
msgstr "Envico"

#: pynicotine/gtkgui/transfers.py:62
#, fuzzy
msgid "Queued (prioritized)"
msgstr "prioritatita"

#: pynicotine/gtkgui/transfers.py:63
#, fuzzy
msgid "Queued (privileged)"
msgstr "privilegiita"

#: pynicotine/gtkgui/transfers.py:64
#, fuzzy
msgid "Getting status"
msgstr "Akirante statuson"

#: pynicotine/gtkgui/transfers.py:65
#, fuzzy
msgid "Transferring"
msgstr "Transdono"

#: pynicotine/gtkgui/transfers.py:66
#, fuzzy
msgid "Connection closed"
msgstr "Konekto fermita de samulo"

#: pynicotine/gtkgui/transfers.py:67
#, fuzzy
msgid "Connection timeout"
msgstr "Konektoj"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
#, fuzzy
msgid "User logged off"
msgstr "Uzanto malsalutis"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
#, fuzzy
msgid "Cancelled"
msgstr "Nuligite"

#: pynicotine/gtkgui/transfers.py:73
#, fuzzy
msgid "Download folder error"
msgstr "Elŝuta dosierujo eraro"

#: pynicotine/gtkgui/transfers.py:74
#, fuzzy
msgid "Local file error"
msgstr "Loka dosiera eraro"

#: pynicotine/gtkgui/transfers.py:75
#, fuzzy
msgid "Banned"
msgstr "Malpermesita"

#: pynicotine/gtkgui/transfers.py:76
#, fuzzy
msgid "File not shared"
msgstr "Dosiero ne dividita"

#: pynicotine/gtkgui/transfers.py:77
#, fuzzy
msgid "Pending shutdown"
msgstr "Pritraktata ĉesigo"

#: pynicotine/gtkgui/transfers.py:78
#, fuzzy
msgid "File read error"
msgstr "Translokigoj"

#: pynicotine/gtkgui/transfers.py:182
#, fuzzy
msgid "Queue"
msgstr "Envico"

#: pynicotine/gtkgui/transfers.py:188
#, fuzzy
msgid "Percent"
msgstr "Procento"

#: pynicotine/gtkgui/transfers.py:208
#, fuzzy
msgid "Time Elapsed"
msgstr "Tempo Pasis"

#: pynicotine/gtkgui/transfers.py:215
#, fuzzy
msgid "Time Left"
msgstr "Tempo restas"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
#, fuzzy
msgid "_Open File"
msgstr "_Malferma Listo"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
#, fuzzy
msgid "Open in File _Manager"
msgstr "Malfermu en Dosiero_Manaĝero"

#: pynicotine/gtkgui/transfers.py:286
#, fuzzy
msgid "_Search"
msgstr "_Serĉu"

#: pynicotine/gtkgui/transfers.py:289
#, fuzzy
msgid "Clear All"
msgstr "Forigi Ĉion"

#: pynicotine/gtkgui/transfers.py:953
#, fuzzy
msgid "Select User's Transfers"
msgstr "Elektu Uzantajn Translokiĝojn"

#: pynicotine/gtkgui/uploads.py:50
#, fuzzy
msgid "_Abort"
msgstr "_Aborti"

#: pynicotine/gtkgui/uploads.py:74
#, fuzzy
msgid "Finished / Cancelled / Failed"
msgstr "Finita / Abortita / Malsukcesita"

#: pynicotine/gtkgui/uploads.py:75
#, fuzzy
msgid "Finished / Cancelled"
msgstr "Finita / Filtrita"

#: pynicotine/gtkgui/uploads.py:79
#, fuzzy
msgid "Failed"
msgstr "Malsukcesis"

#: pynicotine/gtkgui/uploads.py:80
#, fuzzy
msgid "User Logged Off"
msgstr "Uzanto malsalutis"

#: pynicotine/gtkgui/uploads.py:142
#, fuzzy, python-format
msgid "Uploads: %(speed)s"
msgstr "Alŝutoj: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr ""

#: pynicotine/gtkgui/uploads.py:164
#, fuzzy
msgid "Clear Queued Uploads"
msgstr "Forigi Vidovicajn Alŝutojn"

#: pynicotine/gtkgui/uploads.py:165
#, fuzzy
msgid "Do you really want to clear all queued uploads?"
msgstr "Ĉu vi vere volas forigi ĉiujn envicigitajn alŝutojn?"

#: pynicotine/gtkgui/uploads.py:177
#, fuzzy
msgid "Clear All Uploads"
msgstr "Forigi Ĉiuj Alŝutoj"

#: pynicotine/gtkgui/uploads.py:178
#, fuzzy
msgid "Do you really want to clear all uploads?"
msgstr "Ĉu vi vere volas forigi ĉiujn alŝutojn?"

#: pynicotine/gtkgui/userbrowse.py:282
#, fuzzy
msgid "_Save Shares List to Disk"
msgstr "_Konservu Liston de Akcioj al Disko"

#: pynicotine/gtkgui/userbrowse.py:292
#, fuzzy
msgid "Upload Folder & Subfolders…"
msgstr "Alŝutu Dosierujon (kun Subdosierujoj) Al Uzanto"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
#, fuzzy
msgid "Copy _Folder Path"
msgstr "Kopiu _Dosiervojon"

#: pynicotine/gtkgui/userbrowse.py:309
#, fuzzy
msgid "_Download Folder & Subfolders"
msgstr "Elŝutu _dosierujo(j)n"

#: pynicotine/gtkgui/userbrowse.py:310
#, fuzzy
msgid "Download Folder & Subfolders _To…"
msgstr "Elŝutu F_older(j) Al…"

#: pynicotine/gtkgui/userbrowse.py:334
#, fuzzy
msgid "File Name"
msgstr "Dosiernomo"

#: pynicotine/gtkgui/userbrowse.py:373
#, fuzzy
msgid "Up_load File(s)…"
msgstr "Alŝutu_dosiero(j)"

#: pynicotine/gtkgui/userbrowse.py:374
#, fuzzy
msgid "Upload Folder…"
msgstr "Alŝutu dosierujon al…"

#: pynicotine/gtkgui/userbrowse.py:396
#, fuzzy
msgid "Download Folder _To…"
msgstr "Elŝutu Dosierujon _Al…"

#: pynicotine/gtkgui/userbrowse.py:600
#, fuzzy
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"La listo de uzanto de komunaj dosieroj estas malplena. Aŭ la uzanto nenion "
"kundividas, aŭ ili private dividas dosierojn."

#: pynicotine/gtkgui/userbrowse.py:615
#, fuzzy
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Ne eblas peti komunajn dosierojn de uzanto. Aŭ la uzanto estas eksterrete, "
"vi ambaŭ havas fermitan aŭskultan havenon, aŭ estas provizora problemo pri "
"konektebleco."

#: pynicotine/gtkgui/userbrowse.py:953
#, fuzzy
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Elektu Celon por Elŝuti Dosierujon de Uzanto"

#: pynicotine/gtkgui/userbrowse.py:997
#, fuzzy
msgid "Upload Folder (with Subfolders) To User"
msgstr "Alŝutu Dosierujon (kun Subdosierujoj) Al Uzanto"

#: pynicotine/gtkgui/userbrowse.py:999
#, fuzzy
msgid "Upload Folder To User"
msgstr "Alŝutu dosierujon al…"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
#, fuzzy
msgid "Enter the name of the user you want to upload to:"
msgstr "Enigu la nomon de la uzanto al kiu vi volas alŝuti:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
#, fuzzy
msgid "_Upload"
msgstr "Alŝutu"

#: pynicotine/gtkgui/userbrowse.py:1139
#, fuzzy
msgid "Select Destination Folder for Files"
msgstr "Elektu Celon por Elŝuti Dosieron(j)n de Uzanto"

#: pynicotine/gtkgui/userbrowse.py:1161
#, fuzzy
msgid "Upload File(s) To User"
msgstr "Alŝutu dosiero(j)"

#: pynicotine/gtkgui/userinfo.py:376
#, fuzzy
msgid "Copy Picture"
msgstr "Bildo:"

#: pynicotine/gtkgui/userinfo.py:377
#, fuzzy
msgid "Save Picture"
msgstr "Konservu Bildon"

#: pynicotine/gtkgui/userinfo.py:468
#, fuzzy, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Malsukcesis ŝargi bildon por uzanto %(user)s: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
#, fuzzy
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Ne eblas peti informojn de uzanto. Aŭ vi ambaŭ havas fermitan aŭskultan "
"havenon, la uzanto estas eksterrete, aŭ estas provizora problemo pri "
"konektebleco."

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Remove _Buddy"
msgstr "Forigi Wi_sh"

#: pynicotine/gtkgui/userinfo.py:577
#, fuzzy
msgid "Add _Buddy"
msgstr "Aldonu amikon…"

#: pynicotine/gtkgui/userinfo.py:581
#, fuzzy
msgid "Unban User"
msgstr "Malpermesu Uzanton"

#: pynicotine/gtkgui/userinfo.py:585
#, fuzzy
msgid "Unignore User"
msgstr "Ignoru Uzanton"

#: pynicotine/gtkgui/userinfo.py:613
#, fuzzy
msgid "Yes"
msgstr "Jes"

#: pynicotine/gtkgui/userinfo.py:613
#, fuzzy
msgid "No"
msgstr "Ne"

#: pynicotine/gtkgui/userinfo.py:773
#, fuzzy
msgid "Please enter number of days."
msgstr "Bonvolu enigi nombron da tagoj!"

#: pynicotine/gtkgui/userinfo.py:787
#, fuzzy, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Donacu tagojn de viaj Soulseek-privilegioj al uzanto %(user)s "
"(%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, fuzzy, python-format
msgid "%(days)s days left"
msgstr "%(days)s tagoj restas"

#: pynicotine/gtkgui/userinfo.py:795
#, fuzzy
msgid "Gift Privileges"
msgstr "Donacaj Privilegioj"

#: pynicotine/gtkgui/userinfo.py:797
#, fuzzy
msgid "_Give Privileges"
msgstr "Donacaj Privilegioj"

#: pynicotine/gtkgui/widgets/dialogs.py:309
#, fuzzy
msgid "Close"
msgstr "Fermu langeton"

#: pynicotine/gtkgui/widgets/dialogs.py:488
#, fuzzy
msgid "_Yes"
msgstr "_Jes"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
#, fuzzy
msgid "_OK"
msgstr "bone"

#: pynicotine/gtkgui/widgets/filechooser.py:39
#, fuzzy
msgid "Select a File"
msgstr "Elektu Dosieron"

#: pynicotine/gtkgui/widgets/filechooser.py:174
#, fuzzy
msgid "Select a Folder"
msgstr "Elektu Dosierujon"

#: pynicotine/gtkgui/widgets/filechooser.py:179
#, fuzzy
msgid "_Select"
msgstr "Elekti ĉiujn"

#: pynicotine/gtkgui/widgets/filechooser.py:196
#, fuzzy
msgid "Select an Image"
msgstr "Elektu Bildon"

#: pynicotine/gtkgui/widgets/filechooser.py:203
#, fuzzy
msgid "All images"
msgstr "Ĉiuj bildoj"

#: pynicotine/gtkgui/widgets/filechooser.py:241
#, fuzzy
msgid "Save as…"
msgstr "Konservi kiel…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
#, fuzzy
msgid "(None)"
msgstr "(Neniu)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
#, fuzzy
msgid "Close Tab"
msgstr "_Fermu Tab"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
#, fuzzy
msgid "Close All Tabs?"
msgstr "Ĉu fermi ĉiujn langetojn?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
#, fuzzy
msgid "Do you really want to close all tabs?"
msgstr "Ĉu vi vere volas fermi ĉiujn langetojn?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, fuzzy, python-format
msgid "%i Unread Tab(s)"
msgstr "Nelegitaj Tabs"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
#, fuzzy
msgid "All Tabs"
msgstr "Ĉu fermi ĉiujn langetojn?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
#, fuzzy
msgid "Re_open Closed Tab"
msgstr "_Fermu Tab"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, fuzzy, python-format
msgid "%s File(s) Selected"
msgstr "%s Dosiero(j) Elektita(j)."

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
#, fuzzy
msgid "_Browse Files"
msgstr "_Fumu dosierojn"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
#, fuzzy
msgid "_Add Buddy"
msgstr "Aldonu amikon…"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
#, fuzzy
msgid "Show IP A_ddress"
msgstr "Montru IP A_adreson"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
#, fuzzy
msgid "Private Rooms"
msgstr "Privataj Ĉambroj"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, fuzzy, python-format
msgid "Remove from Private Room %s"
msgstr "Forigi el Privata Ĉambro %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, fuzzy, python-format
msgid "Add to Private Room %s"
msgstr "Aldoni al Privata Ĉambro %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, fuzzy, python-format
msgid "Remove as Operator of %s"
msgstr "Forigi kiel Operaciiston de %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, fuzzy, python-format
msgid "Add as Operator of %s"
msgstr "Aldoni kiel Operaciisto de %s"

#: pynicotine/gtkgui/widgets/textentry.py:37
#, fuzzy
msgid "Send message…"
msgstr "Sendi mesaĝon"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
#, fuzzy
msgid "Find Previous Match"
msgstr "Trovu Antaŭan Matĉon"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
#, fuzzy
msgid "Find Next Match"
msgstr "Trovu Sekvan Matĉon"

#: pynicotine/gtkgui/widgets/textview.py:443
#, fuzzy
msgid "--- old messages above ---"
msgstr "--- malnovaj mesaĝoj supre ---"

#: pynicotine/gtkgui/widgets/theme.py:243
#, fuzzy
msgid "Executable"
msgstr "Efektive: %s"

#: pynicotine/gtkgui/widgets/theme.py:244
#, fuzzy
msgid "Audio"
msgstr "Aŭdio"

#: pynicotine/gtkgui/widgets/theme.py:245
#, fuzzy
msgid "Image"
msgstr "Bildo"

#: pynicotine/gtkgui/widgets/theme.py:246
#, fuzzy
msgid "Archive"
msgstr "Arkivo"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
#, fuzzy
msgid "Miscellaneous"
msgstr "Diversaj"

#: pynicotine/gtkgui/widgets/theme.py:248
#, fuzzy
msgid "Video"
msgstr "Video"

#: pynicotine/gtkgui/widgets/theme.py:249
#, fuzzy
msgid "Document"
msgstr "Dokumento/Teksto"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr ""

#: pynicotine/gtkgui/widgets/theme.py:357
#, fuzzy, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "Eraro dum ŝarĝo de kutima ikono %(path)s: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
#, fuzzy
msgid "Hide Nicotine+"
msgstr "Kaŝi Nicotine+"

#: pynicotine/gtkgui/widgets/trayicon.py:114
#, fuzzy
msgid "Show Nicotine+"
msgstr "Montru Nicotine+"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, fuzzy, python-format
msgid "Column #%i"
msgstr "Kolumno #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
#, fuzzy
msgid "Ungrouped"
msgstr "Negrupigita"

#: pynicotine/gtkgui/widgets/treeview.py:960
#, fuzzy
msgid "Group by Folder"
msgstr "Grupo per Dosierujo"

#: pynicotine/gtkgui/widgets/treeview.py:963
#, fuzzy
msgid "Group by User"
msgstr "Grupo laŭ Uzanto"

#: pynicotine/headless/application.py:77
#, fuzzy, python-format
msgid "Do you really want to exit? %s"
msgstr "Ĉu vi vere volas eliri Nicotine+?"

#: pynicotine/headless/application.py:81
#, fuzzy, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr ""
"Uzanto %s jam ekzistas, kaj la pasvorto, kiun vi enigis, estas malvalida. "
"Bonvolu elekti alian uzantnomon se ĉi tio estas via unua fojo ensalutanta."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr ""

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
#, fuzzy
msgid "Password: "
msgstr "Pasvorto"

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
#, fuzzy
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Por krei novan Soulseek-konton, enigu vian deziratan uzantnomon kaj "
"pasvorton. Se vi jam havas konton, enigu viajn ekzistantajn ensalutajn "
"detalojn."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr ""

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr ""

#: pynicotine/logfacility.py:181
#, fuzzy, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr "Ne eblis skribi al protokoldosiero \"%(filename)s\": %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, fuzzy, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Ne eblas konservi dosieron %(path)s: %(error)s"

#: pynicotine/networkfilter.py:40
msgid "Andorra"
msgstr "Andoro"

#: pynicotine/networkfilter.py:41
msgid "United Arab Emirates"
msgstr "Unuiĝintaj Arabaj Emirlandoj"

#: pynicotine/networkfilter.py:42
msgid "Afghanistan"
msgstr "Afganio"

#: pynicotine/networkfilter.py:43
#, fuzzy
msgid "Antigua & Barbuda"
msgstr "Antigvo & Barbudo"

#: pynicotine/networkfilter.py:44
#, fuzzy
msgid "Anguilla"
msgstr "Angvilo"

#: pynicotine/networkfilter.py:45
#, fuzzy
msgid "Albania"
msgstr "Albanio"

#: pynicotine/networkfilter.py:46
msgid "Armenia"
msgstr "Armenio"

#: pynicotine/networkfilter.py:47
msgid "Angola"
msgstr "Angolo"

#: pynicotine/networkfilter.py:48
msgid "Antarctica"
msgstr "Antarktio"

#: pynicotine/networkfilter.py:49
msgid "Argentina"
msgstr "Argentino"

#: pynicotine/networkfilter.py:50
msgid "American Samoa"
msgstr "Usona Samoo"

#: pynicotine/networkfilter.py:51
msgid "Austria"
msgstr "Aŭstrio"

#: pynicotine/networkfilter.py:52
msgid "Australia"
msgstr "Aŭstralio"

#: pynicotine/networkfilter.py:53
#, fuzzy
msgid "Aruba"
msgstr "Arubo"

#: pynicotine/networkfilter.py:54
#, fuzzy
msgid "Åland Islands"
msgstr "Alandaj Insuloj"

#: pynicotine/networkfilter.py:55
msgid "Azerbaijan"
msgstr "Azerbajĝano"

#: pynicotine/networkfilter.py:56
#, fuzzy
msgid "Bosnia & Herzegovina"
msgstr "Bosnio kaj Hercegovino"

#: pynicotine/networkfilter.py:57
#, fuzzy
msgid "Barbados"
msgstr "Barbado"

#: pynicotine/networkfilter.py:58
#, fuzzy
msgid "Bangladesh"
msgstr "Bangladeŝo"

#: pynicotine/networkfilter.py:59
#, fuzzy
msgid "Belgium"
msgstr "Belgio"

#: pynicotine/networkfilter.py:60
#, fuzzy
msgid "Burkina Faso"
msgstr "Burkino"

#: pynicotine/networkfilter.py:61
#, fuzzy
msgid "Bulgaria"
msgstr "Bulgario"

#: pynicotine/networkfilter.py:62
msgid "Bahrain"
msgstr "Barejno"

#: pynicotine/networkfilter.py:63
msgid "Burundi"
msgstr "Burundo"

#: pynicotine/networkfilter.py:64
msgid "Benin"
msgstr "Benino"

#: pynicotine/networkfilter.py:65
#, fuzzy
msgid "Saint Barthelemy"
msgstr "Sankta Bartolomeo"

#: pynicotine/networkfilter.py:66
#, fuzzy
msgid "Bermuda"
msgstr "Bermuda"

#: pynicotine/networkfilter.py:67
#, fuzzy
msgid "Brunei Darussalam"
msgstr "Brunejo Darussalam"

#: pynicotine/networkfilter.py:68
#, fuzzy
msgid "Bolivia"
msgstr "Bolivio"

#: pynicotine/networkfilter.py:69
#, fuzzy
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius kaj Saba"

#: pynicotine/networkfilter.py:70
#, fuzzy
msgid "Brazil"
msgstr "Brazilo"

#: pynicotine/networkfilter.py:71
#, fuzzy
msgid "Bahamas"
msgstr "Bahamoj"

#: pynicotine/networkfilter.py:72
#, fuzzy
msgid "Bhutan"
msgstr "Butano"

#: pynicotine/networkfilter.py:73
#, fuzzy
msgid "Bouvet Island"
msgstr "Insulo Bouvet"

#: pynicotine/networkfilter.py:74
#, fuzzy
msgid "Botswana"
msgstr "Bocvano"

#: pynicotine/networkfilter.py:75
#, fuzzy
msgid "Belarus"
msgstr "Belorusio"

#: pynicotine/networkfilter.py:76
#, fuzzy
msgid "Belize"
msgstr "Belizo"

#: pynicotine/networkfilter.py:77
msgid "Canada"
msgstr "Kanado"

#: pynicotine/networkfilter.py:78
#, fuzzy
msgid "Cocos (Keeling) Islands"
msgstr "Kokosaj (Keeling) Insuloj"

#: pynicotine/networkfilter.py:79
#, fuzzy
msgid "Democratic Republic of Congo"
msgstr "Demokratia Respubliko Kongo"

#: pynicotine/networkfilter.py:80
#, fuzzy
msgid "Central African Republic"
msgstr "Centafrika Respubliko"

#: pynicotine/networkfilter.py:81
#, fuzzy
msgid "Congo"
msgstr "Kongo"

#: pynicotine/networkfilter.py:82
#, fuzzy
msgid "Switzerland"
msgstr "Svislando"

#: pynicotine/networkfilter.py:83
#, fuzzy
msgid "Ivory Coast"
msgstr "Ebura Bordo"

#: pynicotine/networkfilter.py:84
#, fuzzy
msgid "Cook Islands"
msgstr "Cook-Insuloj"

#: pynicotine/networkfilter.py:85
#, fuzzy
msgid "Chile"
msgstr "Ĉilio"

#: pynicotine/networkfilter.py:86
#, fuzzy
msgid "Cameroon"
msgstr "Kamerunio"

#: pynicotine/networkfilter.py:87
msgid "China"
msgstr "Ĉinio"

#: pynicotine/networkfilter.py:88
msgid "Colombia"
msgstr "Kolombio"

#: pynicotine/networkfilter.py:89
msgid "Costa Rica"
msgstr "Kostariko"

#: pynicotine/networkfilter.py:90
msgid "Cuba"
msgstr "Kubo"

#: pynicotine/networkfilter.py:91
#, fuzzy
msgid "Cabo Verde"
msgstr "Cabo Verde"

#: pynicotine/networkfilter.py:92
#, fuzzy
msgid "Curaçao"
msgstr "Kuraçao"

#: pynicotine/networkfilter.py:93
#, fuzzy
msgid "Christmas Island"
msgstr "Kristnaskinsulo"

#: pynicotine/networkfilter.py:94
#, fuzzy
msgid "Cyprus"
msgstr "Kipro"

#: pynicotine/networkfilter.py:95
#, fuzzy
msgid "Czechia"
msgstr "Ĉeĥio"

#: pynicotine/networkfilter.py:96
#, fuzzy
msgid "Germany"
msgstr "Germanujo"

#: pynicotine/networkfilter.py:97
#, fuzzy
msgid "Djibouti"
msgstr "Ĝibutio"

#: pynicotine/networkfilter.py:98
#, fuzzy
msgid "Denmark"
msgstr "Danio"

#: pynicotine/networkfilter.py:99
#, fuzzy
msgid "Dominica"
msgstr "Dominiko"

#: pynicotine/networkfilter.py:100
#, fuzzy
msgid "Dominican Republic"
msgstr "Dominika Respubliko"

#: pynicotine/networkfilter.py:101
#, fuzzy
msgid "Algeria"
msgstr "Alĝerio"

#: pynicotine/networkfilter.py:102
#, fuzzy
msgid "Ecuador"
msgstr "Ekvadoro"

#: pynicotine/networkfilter.py:103
#, fuzzy
msgid "Estonia"
msgstr "Estonio"

#: pynicotine/networkfilter.py:104
#, fuzzy
msgid "Egypt"
msgstr "Egiptujo"

#: pynicotine/networkfilter.py:105
#, fuzzy
msgid "Western Sahara"
msgstr "Okcidenta Saharo"

#: pynicotine/networkfilter.py:106
#, fuzzy
msgid "Eritrea"
msgstr "Eritreo"

#: pynicotine/networkfilter.py:107
#, fuzzy
msgid "Spain"
msgstr "Hispanio"

#: pynicotine/networkfilter.py:108
#, fuzzy
msgid "Ethiopia"
msgstr "Etiopio"

#: pynicotine/networkfilter.py:109
#, fuzzy
msgid "Europe"
msgstr "Eŭropo"

#: pynicotine/networkfilter.py:110
#, fuzzy
msgid "Finland"
msgstr "Finnlando"

#: pynicotine/networkfilter.py:111
#, fuzzy
msgid "Fiji"
msgstr "Fiĝioj"

#: pynicotine/networkfilter.py:112
#, fuzzy
msgid "Falkland Islands (Malvinas)"
msgstr "Falklandinsuloj (Malvinoj)"

#: pynicotine/networkfilter.py:113
#, fuzzy
msgid "Micronesia"
msgstr "Mikronezio"

#: pynicotine/networkfilter.py:114
#, fuzzy
msgid "Faroe Islands"
msgstr "Feroaj Insuloj"

#: pynicotine/networkfilter.py:115
#, fuzzy
msgid "France"
msgstr "Francio"

#: pynicotine/networkfilter.py:116
#, fuzzy
msgid "Gabon"
msgstr "Gabono"

#: pynicotine/networkfilter.py:117
#, fuzzy
msgid "Great Britain"
msgstr "Britio"

#: pynicotine/networkfilter.py:118
#, fuzzy
msgid "Grenada"
msgstr "Grenado"

#: pynicotine/networkfilter.py:119
#, fuzzy
msgid "Georgia"
msgstr "Kartvelio"

#: pynicotine/networkfilter.py:120
#, fuzzy
msgid "French Guiana"
msgstr "Franca Gujano"

#: pynicotine/networkfilter.py:121
#, fuzzy
msgid "Guernsey"
msgstr "Ĝerzjako"

#: pynicotine/networkfilter.py:122
#, fuzzy
msgid "Ghana"
msgstr "Ganao"

#: pynicotine/networkfilter.py:123
#, fuzzy
msgid "Gibraltar"
msgstr "Ĝibraltaro"

#: pynicotine/networkfilter.py:124
#, fuzzy
msgid "Greenland"
msgstr "Gronlando"

#: pynicotine/networkfilter.py:125
#, fuzzy
msgid "Gambia"
msgstr "Gambio"

#: pynicotine/networkfilter.py:126
#, fuzzy
msgid "Guinea"
msgstr "Gvineo"

#: pynicotine/networkfilter.py:127
#, fuzzy
msgid "Guadeloupe"
msgstr "Gvadelupo"

#: pynicotine/networkfilter.py:128
#, fuzzy
msgid "Equatorial Guinea"
msgstr "Ekvatora Gvineo"

#: pynicotine/networkfilter.py:129
#, fuzzy
msgid "Greece"
msgstr "Grekio"

#: pynicotine/networkfilter.py:130
#, fuzzy
msgid "South Georgia & South Sandwich Islands"
msgstr "Suda Kartvelio & Suda Sandviĉinsuloj"

#: pynicotine/networkfilter.py:131
#, fuzzy
msgid "Guatemala"
msgstr "Gvatemalo"

#: pynicotine/networkfilter.py:132
#, fuzzy
msgid "Guam"
msgstr "Gvamo"

#: pynicotine/networkfilter.py:133
#, fuzzy
msgid "Guinea-Bissau"
msgstr "Gvineo Bisaŭa"

#: pynicotine/networkfilter.py:134
#, fuzzy
msgid "Guyana"
msgstr "Gujano"

#: pynicotine/networkfilter.py:135
#, fuzzy
msgid "Hong Kong"
msgstr "Honkongo"

#: pynicotine/networkfilter.py:136
#, fuzzy
msgid "Heard & McDonald Islands"
msgstr "Insuloj Heard kaj McDonald"

#: pynicotine/networkfilter.py:137
#, fuzzy
msgid "Honduras"
msgstr "Honduro"

#: pynicotine/networkfilter.py:138
#, fuzzy
msgid "Croatia"
msgstr "Kroatio"

#: pynicotine/networkfilter.py:139
#, fuzzy
msgid "Haiti"
msgstr "Haitio"

#: pynicotine/networkfilter.py:140
#, fuzzy
msgid "Hungary"
msgstr "Hungario"

#: pynicotine/networkfilter.py:141
#, fuzzy
msgid "Indonesia"
msgstr "Indonezio"

#: pynicotine/networkfilter.py:142
#, fuzzy
msgid "Ireland"
msgstr "Irlando"

#: pynicotine/networkfilter.py:143
#, fuzzy
msgid "Israel"
msgstr "Israelo"

#: pynicotine/networkfilter.py:144
#, fuzzy
msgid "Isle of Man"
msgstr "Manksinsulo"

#: pynicotine/networkfilter.py:145
#, fuzzy
msgid "India"
msgstr "Barato"

#: pynicotine/networkfilter.py:146
#, fuzzy
msgid "British Indian Ocean Territory"
msgstr "Brita Hinda Oceana Teritorio"

#: pynicotine/networkfilter.py:147
#, fuzzy
msgid "Iraq"
msgstr "Irako"

#: pynicotine/networkfilter.py:148
#, fuzzy
msgid "Iran"
msgstr "Irano"

#: pynicotine/networkfilter.py:149
#, fuzzy
msgid "Iceland"
msgstr "Islando"

#: pynicotine/networkfilter.py:150
#, fuzzy
msgid "Italy"
msgstr "Italio"

#: pynicotine/networkfilter.py:151
#, fuzzy
msgid "Jersey"
msgstr "Ĵerzo"

#: pynicotine/networkfilter.py:152
#, fuzzy
msgid "Jamaica"
msgstr "Jamajko"

#: pynicotine/networkfilter.py:153
#, fuzzy
msgid "Jordan"
msgstr "Jordanio"

#: pynicotine/networkfilter.py:154
#, fuzzy
msgid "Japan"
msgstr "Japanio"

#: pynicotine/networkfilter.py:155
#, fuzzy
msgid "Kenya"
msgstr "Kenjo"

#: pynicotine/networkfilter.py:156
#, fuzzy
msgid "Kyrgyzstan"
msgstr "Kirgizio"

#: pynicotine/networkfilter.py:157
#, fuzzy
msgid "Cambodia"
msgstr "Kamboĝo"

#: pynicotine/networkfilter.py:158
#, fuzzy
msgid "Kiribati"
msgstr "Kiribato"

#: pynicotine/networkfilter.py:159
#, fuzzy
msgid "Comoros"
msgstr "Komoroj"

#: pynicotine/networkfilter.py:160
#, fuzzy
msgid "Saint Kitts & Nevis"
msgstr "Sankta-Kito kaj Neviso"

#: pynicotine/networkfilter.py:161
#, fuzzy
msgid "North Korea"
msgstr "Norda Koreio"

#: pynicotine/networkfilter.py:162
#, fuzzy
msgid "South Korea"
msgstr "Sudkoreio"

#: pynicotine/networkfilter.py:163
#, fuzzy
msgid "Kuwait"
msgstr "Kuvajto"

#: pynicotine/networkfilter.py:164
#, fuzzy
msgid "Cayman Islands"
msgstr "Kajmanaj Insuloj"

#: pynicotine/networkfilter.py:165
#, fuzzy
msgid "Kazakhstan"
msgstr "Kazaĥio"

#: pynicotine/networkfilter.py:166
#, fuzzy
msgid "Laos"
msgstr "Laoso"

#: pynicotine/networkfilter.py:167
#, fuzzy
msgid "Lebanon"
msgstr "Libano"

#: pynicotine/networkfilter.py:168
#, fuzzy
msgid "Saint Lucia"
msgstr "Sankta Lucio"

#: pynicotine/networkfilter.py:169
#, fuzzy
msgid "Liechtenstein"
msgstr "Liĥtenŝtejno"

#: pynicotine/networkfilter.py:170
#, fuzzy
msgid "Sri Lanka"
msgstr "Srilanko"

#: pynicotine/networkfilter.py:171
#, fuzzy
msgid "Liberia"
msgstr "Liberio"

#: pynicotine/networkfilter.py:172
#, fuzzy
msgid "Lesotho"
msgstr "Lesoto"

#: pynicotine/networkfilter.py:173
#, fuzzy
msgid "Lithuania"
msgstr "Litovio"

#: pynicotine/networkfilter.py:174
#, fuzzy
msgid "Luxembourg"
msgstr "Luksemburgio"

#: pynicotine/networkfilter.py:175
#, fuzzy
msgid "Latvia"
msgstr "Latvio"

#: pynicotine/networkfilter.py:176
#, fuzzy
msgid "Libya"
msgstr "Libio"

#: pynicotine/networkfilter.py:177
#, fuzzy
msgid "Morocco"
msgstr "Maroko"

#: pynicotine/networkfilter.py:178
#, fuzzy
msgid "Monaco"
msgstr "Monako"

#: pynicotine/networkfilter.py:179
#, fuzzy
msgid "Moldova"
msgstr "Moldavio"

#: pynicotine/networkfilter.py:180
#, fuzzy
msgid "Montenegro"
msgstr "Montenegro"

#: pynicotine/networkfilter.py:181
#, fuzzy
msgid "Saint Martin"
msgstr "Sankta Marteno"

#: pynicotine/networkfilter.py:182
#, fuzzy
msgid "Madagascar"
msgstr "Madagaskaro"

#: pynicotine/networkfilter.py:183
#, fuzzy
msgid "Marshall Islands"
msgstr "Marŝala Insularo"

#: pynicotine/networkfilter.py:184
#, fuzzy
msgid "North Macedonia"
msgstr "Norda Makedonio"

#: pynicotine/networkfilter.py:185
#, fuzzy
msgid "Mali"
msgstr "Malio"

#: pynicotine/networkfilter.py:186
#, fuzzy
msgid "Myanmar"
msgstr "Mjanmao"

#: pynicotine/networkfilter.py:187
#, fuzzy
msgid "Mongolia"
msgstr "Mongolio"

#: pynicotine/networkfilter.py:188
#, fuzzy
msgid "Macau"
msgstr "Makao"

#: pynicotine/networkfilter.py:189
#, fuzzy
msgid "Northern Mariana Islands"
msgstr "Nord-Marianoj"

#: pynicotine/networkfilter.py:190
#, fuzzy
msgid "Martinique"
msgstr "Martiniko"

#: pynicotine/networkfilter.py:191
#, fuzzy
msgid "Mauritania"
msgstr "Maŭritanio"

#: pynicotine/networkfilter.py:192
#, fuzzy
msgid "Montserrat"
msgstr "Montserato"

#: pynicotine/networkfilter.py:193
#, fuzzy
msgid "Malta"
msgstr "Malto"

#: pynicotine/networkfilter.py:194
#, fuzzy
msgid "Mauritius"
msgstr "Maŭricio"

#: pynicotine/networkfilter.py:195
#, fuzzy
msgid "Maldives"
msgstr "Maldivoj"

#: pynicotine/networkfilter.py:196
#, fuzzy
msgid "Malawi"
msgstr "Malavio"

#: pynicotine/networkfilter.py:197
#, fuzzy
msgid "Mexico"
msgstr "Meksiko"

#: pynicotine/networkfilter.py:198
#, fuzzy
msgid "Malaysia"
msgstr "Malajzio"

#: pynicotine/networkfilter.py:199
#, fuzzy
msgid "Mozambique"
msgstr "Mozambiko"

#: pynicotine/networkfilter.py:200
#, fuzzy
msgid "Namibia"
msgstr "Namibio"

#: pynicotine/networkfilter.py:201
#, fuzzy
msgid "New Caledonia"
msgstr "Nov-Kaledonio"

#: pynicotine/networkfilter.py:202
#, fuzzy
msgid "Niger"
msgstr "Niĝero"

#: pynicotine/networkfilter.py:203
#, fuzzy
msgid "Norfolk Island"
msgstr "Norfolkinsulo"

#: pynicotine/networkfilter.py:204
#, fuzzy
msgid "Nigeria"
msgstr "Niĝerio"

#: pynicotine/networkfilter.py:205
#, fuzzy
msgid "Nicaragua"
msgstr "Nikaragvo"

#: pynicotine/networkfilter.py:206
#, fuzzy
msgid "Netherlands"
msgstr "Nederlando"

#: pynicotine/networkfilter.py:207
#, fuzzy
msgid "Norway"
msgstr "Norvegio"

#: pynicotine/networkfilter.py:208
#, fuzzy
msgid "Nepal"
msgstr "Nepalo"

#: pynicotine/networkfilter.py:209
#, fuzzy
msgid "Nauru"
msgstr "Nauro"

#: pynicotine/networkfilter.py:210
#, fuzzy
msgid "Niue"
msgstr "Niue"

#: pynicotine/networkfilter.py:211
#, fuzzy
msgid "New Zealand"
msgstr "Nov-Zelando"

#: pynicotine/networkfilter.py:212
#, fuzzy
msgid "Oman"
msgstr "Omano"

#: pynicotine/networkfilter.py:213
#, fuzzy
msgid "Panama"
msgstr "Panamo"

#: pynicotine/networkfilter.py:214
#, fuzzy
msgid "Peru"
msgstr "Peruo"

#: pynicotine/networkfilter.py:215
#, fuzzy
msgid "French Polynesia"
msgstr "Franca Polinezio"

#: pynicotine/networkfilter.py:216
#, fuzzy
msgid "Papua New Guinea"
msgstr "Papua Nova Gvineo"

#: pynicotine/networkfilter.py:217
#, fuzzy
msgid "Philippines"
msgstr "Filipinoj"

#: pynicotine/networkfilter.py:218
#, fuzzy
msgid "Pakistan"
msgstr "Pakistano"

#: pynicotine/networkfilter.py:219
#, fuzzy
msgid "Poland"
msgstr "Pollando"

#: pynicotine/networkfilter.py:220
#, fuzzy
msgid "Saint Pierre & Miquelon"
msgstr "Saint Pierre & Miquelon"

#: pynicotine/networkfilter.py:221
#, fuzzy
msgid "Pitcairn"
msgstr "Pitkarno"

#: pynicotine/networkfilter.py:222
#, fuzzy
msgid "Puerto Rico"
msgstr "Porto-Riko"

#: pynicotine/networkfilter.py:223
#, fuzzy
msgid "State of Palestine"
msgstr "Ŝtato de Palestino"

#: pynicotine/networkfilter.py:224
#, fuzzy
msgid "Portugal"
msgstr "Portugalio"

#: pynicotine/networkfilter.py:225
#, fuzzy
msgid "Palau"
msgstr "Palaŭo"

#: pynicotine/networkfilter.py:226
#, fuzzy
msgid "Paraguay"
msgstr "Paragvajo"

#: pynicotine/networkfilter.py:227
#, fuzzy
msgid "Qatar"
msgstr "Kataro"

#: pynicotine/networkfilter.py:228
#, fuzzy
msgid "Réunion"
msgstr "Reunio"

#: pynicotine/networkfilter.py:229
#, fuzzy
msgid "Romania"
msgstr "Rumanio"

#: pynicotine/networkfilter.py:230
#, fuzzy
msgid "Serbia"
msgstr "Serbio"

#: pynicotine/networkfilter.py:231
#, fuzzy
msgid "Russia"
msgstr "Rusio"

#: pynicotine/networkfilter.py:232
#, fuzzy
msgid "Rwanda"
msgstr "Ruando"

#: pynicotine/networkfilter.py:233
#, fuzzy
msgid "Saudi Arabia"
msgstr "Saŭdiarabio"

#: pynicotine/networkfilter.py:234
#, fuzzy
msgid "Solomon Islands"
msgstr "Salomonoj"

#: pynicotine/networkfilter.py:235
#, fuzzy
msgid "Seychelles"
msgstr "Sejŝeloj"

#: pynicotine/networkfilter.py:236
#, fuzzy
msgid "Sudan"
msgstr "Sudano"

#: pynicotine/networkfilter.py:237
#, fuzzy
msgid "Sweden"
msgstr "Svedio"

#: pynicotine/networkfilter.py:238
#, fuzzy
msgid "Singapore"
msgstr "Singapuro"

#: pynicotine/networkfilter.py:239
#, fuzzy
msgid "Saint Helena"
msgstr "Sankta Heleno"

#: pynicotine/networkfilter.py:240
#, fuzzy
msgid "Slovenia"
msgstr "Slovenio"

#: pynicotine/networkfilter.py:241
#, fuzzy
msgid "Svalbard & Jan Mayen Islands"
msgstr "Insuloj Svalbardo kaj Jan Mayen"

#: pynicotine/networkfilter.py:242
#, fuzzy
msgid "Slovak Republic"
msgstr "Slovaka Respubliko"

#: pynicotine/networkfilter.py:243
#, fuzzy
msgid "Sierra Leone"
msgstr "Siera-Leono"

#: pynicotine/networkfilter.py:244
#, fuzzy
msgid "San Marino"
msgstr "San Marino"

#: pynicotine/networkfilter.py:245
#, fuzzy
msgid "Senegal"
msgstr "Senegalo"

#: pynicotine/networkfilter.py:246
#, fuzzy
msgid "Somalia"
msgstr "Somalio"

#: pynicotine/networkfilter.py:247
#, fuzzy
msgid "Suriname"
msgstr "Surinamo"

#: pynicotine/networkfilter.py:248
#, fuzzy
msgid "South Sudan"
msgstr "Suda Sudano"

#: pynicotine/networkfilter.py:249
#, fuzzy
msgid "Sao Tome & Principe"
msgstr "Sao Tome & Principe"

#: pynicotine/networkfilter.py:250
#, fuzzy
msgid "El Salvador"
msgstr "Salvadoro"

#: pynicotine/networkfilter.py:251
#, fuzzy
msgid "Sint Maarten"
msgstr "Sint Maarten"

#: pynicotine/networkfilter.py:252
#, fuzzy
msgid "Syria"
msgstr "Sirio"

#: pynicotine/networkfilter.py:253
#, fuzzy
msgid "Eswatini"
msgstr "Esvatini"

#: pynicotine/networkfilter.py:254
#, fuzzy
msgid "Turks & Caicos Islands"
msgstr "Turkoj kaj Kajkoj"

#: pynicotine/networkfilter.py:255
#, fuzzy
msgid "Chad"
msgstr "Ĉadio"

#: pynicotine/networkfilter.py:256
#, fuzzy
msgid "French Southern Territories"
msgstr "Francaj Sudaj Teritorioj"

#: pynicotine/networkfilter.py:257
#, fuzzy
msgid "Togo"
msgstr "Iri"

#: pynicotine/networkfilter.py:258
#, fuzzy
msgid "Thailand"
msgstr "Tajlando"

#: pynicotine/networkfilter.py:259
#, fuzzy
msgid "Tajikistan"
msgstr "Taĝikio"

#: pynicotine/networkfilter.py:260
#, fuzzy
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
#, fuzzy
msgid "Timor-Leste"
msgstr "Timor-Leste"

#: pynicotine/networkfilter.py:262
#, fuzzy
msgid "Turkmenistan"
msgstr "Turkmenio"

#: pynicotine/networkfilter.py:263
#, fuzzy
msgid "Tunisia"
msgstr "Tunizio"

#: pynicotine/networkfilter.py:264
#, fuzzy
msgid "Tonga"
msgstr "Tongo"

#: pynicotine/networkfilter.py:265
#, fuzzy
msgid "Türkiye"
msgstr "Turkio"

#: pynicotine/networkfilter.py:266
#, fuzzy
msgid "Trinidad & Tobago"
msgstr "Trinidado kaj Tobago"

#: pynicotine/networkfilter.py:267
#, fuzzy
msgid "Tuvalu"
msgstr "Tuvalo"

#: pynicotine/networkfilter.py:268
#, fuzzy
msgid "Taiwan"
msgstr "Tajvano"

#: pynicotine/networkfilter.py:269
#, fuzzy
msgid "Tanzania"
msgstr "Tanzanio"

#: pynicotine/networkfilter.py:270
#, fuzzy
msgid "Ukraine"
msgstr "Ukrainio"

#: pynicotine/networkfilter.py:271
#, fuzzy
msgid "Uganda"
msgstr "Ugando"

#: pynicotine/networkfilter.py:272
#, fuzzy
msgid "U.S. Minor Outlying Islands"
msgstr "Malgrandaj Eksteraj Insuloj de Usono"

#: pynicotine/networkfilter.py:273
#, fuzzy
msgid "United States"
msgstr "Usono"

#: pynicotine/networkfilter.py:274
#, fuzzy
msgid "Uruguay"
msgstr "Urugvajo"

#: pynicotine/networkfilter.py:275
#, fuzzy
msgid "Uzbekistan"
msgstr "Uzbekio"

#: pynicotine/networkfilter.py:276
#, fuzzy
msgid "Holy See (Vatican City State)"
msgstr "Sankta Seĝo (Vatikanurbo Ŝtato)"

#: pynicotine/networkfilter.py:277
#, fuzzy
msgid "Saint Vincent & The Grenadines"
msgstr "Sankta Vincento kaj Grenadinoj"

#: pynicotine/networkfilter.py:278
#, fuzzy
msgid "Venezuela"
msgstr "Venezuelo"

#: pynicotine/networkfilter.py:279
#, fuzzy
msgid "British Virgin Islands"
msgstr "Britaj Virgulininsuloj"

#: pynicotine/networkfilter.py:280
#, fuzzy
msgid "U.S. Virgin Islands"
msgstr "Usonaj Virgulininsuloj"

#: pynicotine/networkfilter.py:281
#, fuzzy
msgid "Viet Nam"
msgstr "Vjetnamio"

#: pynicotine/networkfilter.py:282
#, fuzzy
msgid "Vanuatu"
msgstr "Vanuatuo"

#: pynicotine/networkfilter.py:283
#, fuzzy
msgid "Wallis & Futuna"
msgstr "Valiso kaj Futuno"

#: pynicotine/networkfilter.py:284
#, fuzzy
msgid "Samoa"
msgstr "Samoo"

#: pynicotine/networkfilter.py:285
#, fuzzy
msgid "Yemen"
msgstr "Jemeno"

#: pynicotine/networkfilter.py:286
#, fuzzy
msgid "Mayotte"
msgstr "Majoto"

#: pynicotine/networkfilter.py:287
#, fuzzy
msgid "South Africa"
msgstr "Sudafriko"

#: pynicotine/networkfilter.py:288
#, fuzzy
msgid "Zambia"
msgstr "Zambio"

#: pynicotine/networkfilter.py:289
#, fuzzy
msgid "Zimbabwe"
msgstr "Zimbabvo"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, fuzzy, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "Teksto al parolado por mesaĝo malsukcesis: %s"

#: pynicotine/nowplaying.py:130
#, fuzzy
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr "Last.fm: Bonvolu provizi kaj vian Last.fm uzantnomon kaj API-ŝlosilon"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
#, fuzzy
msgid "Now Playing Error"
msgstr "Nun Ludanta Formato"

#: pynicotine/nowplaying.py:140
#, fuzzy, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm: Ne eblis konektiĝi al Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:162
#, fuzzy, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr ""
"Last.fm: Ne eblis ricevi lastatempan trakon de Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:196
#, fuzzy
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: Ne eblis trovi taŭgan MPRIS-ludilon"

#: pynicotine/nowplaying.py:201
#, fuzzy, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr "Trovis plurajn MPRIS-ludilojn: %(players)s. Uzante: %(player)s"

#: pynicotine/nowplaying.py:204
#, fuzzy, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Aŭtomate detektita MPRIS-ludilo: %s"

#: pynicotine/nowplaying.py:219
#, fuzzy, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr "MPRIS: Io misfunkciis dum pridemando de %(player)s: %(exception)s"

#: pynicotine/nowplaying.py:266
#, fuzzy
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "ListenBrainz: Bonvolu doni vian uzantnomon ListenBrainz"

#: pynicotine/nowplaying.py:275
#, fuzzy, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "ListenBrainz: Ne eblis konektiĝi al ListenBrainz: %(error)s"

#: pynicotine/nowplaying.py:283
#, fuzzy
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "ListenBrainz: Vi ŝajnas ne aŭskulti ion ajn nun"

#: pynicotine/nowplaying.py:297
#, fuzzy, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr "ListenBrainz: Ne eblis ricevi nunan trakon de ListenBrainz: %(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
#, fuzzy
msgid "Network Filters"
msgstr "Retaj Serĉoj"

#: pynicotine/plugins/core_commands/__init__.py:44
#, fuzzy
msgid "List available commands"
msgstr "Kompletaj enkonstruitaj komandoj"

#: pynicotine/plugins/core_commands/__init__.py:49
#, fuzzy
msgid "Connect to the server"
msgstr "Ne eblas konektiĝi al la servilo. Kialo: %s"

#: pynicotine/plugins/core_commands/__init__.py:53
#, fuzzy
msgid "Disconnect from the server"
msgstr "Malkonektita de servilo %(host)s:%(port)s"

#: pynicotine/plugins/core_commands/__init__.py:58
#, fuzzy
msgid "Toggle away status"
msgstr "Ŝaltas vian for-statuson"

#: pynicotine/plugins/core_commands/__init__.py:62
#, fuzzy
msgid "Manage plugins"
msgstr "Ebligu kromaĵojn"

#: pynicotine/plugins/core_commands/__init__.py:74
#, fuzzy
msgid "Clear chat window"
msgstr "Malplenigu la babilejon"

#: pynicotine/plugins/core_commands/__init__.py:80
#, fuzzy
msgid "Say something in the third-person"
msgstr "Diru ion en la tria persono"

#: pynicotine/plugins/core_commands/__init__.py:87
#, fuzzy
msgid "Announce the song currently playing"
msgstr "Anoncu la kanton nuntempe ludantan"

#: pynicotine/plugins/core_commands/__init__.py:94
#, fuzzy
msgid "Join chat room"
msgstr "Aliĝu aŭ kreu ĉambron…"

#: pynicotine/plugins/core_commands/__init__.py:102
#, fuzzy
msgid "Leave chat room"
msgstr "Forlasu la nunan ĉambron"

#: pynicotine/plugins/core_commands/__init__.py:110
#, fuzzy
msgid "Say message in specified chat room"
msgstr "Diru mesaĝon en specifita babilejo"

#: pynicotine/plugins/core_commands/__init__.py:117
#, fuzzy
msgid "Open private chat"
msgstr "Privata Babilejo"

#: pynicotine/plugins/core_commands/__init__.py:125
#, fuzzy
msgid "Close private chat"
msgstr "Fermu la nunan privatan babilejon"

#: pynicotine/plugins/core_commands/__init__.py:133
#, fuzzy
msgid "Request user's client version"
msgstr "Petu informojn de uzanto"

#: pynicotine/plugins/core_commands/__init__.py:142
#, fuzzy
msgid "Send private message to user"
msgstr "Sendu privatan mesaĝon al ĉiuj interretaj amikoj:"

#: pynicotine/plugins/core_commands/__init__.py:150
#, fuzzy
msgid "Add user to buddy list"
msgstr "Aldonu uzanton al via kamaradlisto"

#: pynicotine/plugins/core_commands/__init__.py:158
#, fuzzy
msgid "Remove buddy from buddy list"
msgstr "Forigu uzanton el via kamaradlisto"

#: pynicotine/plugins/core_commands/__init__.py:166
#, fuzzy
msgid "Browse files of user"
msgstr "Foliumi dosierojn de uzanto 'uzanto'"

#: pynicotine/plugins/core_commands/__init__.py:175
#, fuzzy
msgid "Show user profile information"
msgstr "Informoj pri Uzanto"

#: pynicotine/plugins/core_commands/__init__.py:183
#, fuzzy
msgid "Show IP address or username"
msgstr "Montru IP por uzanto"

#: pynicotine/plugins/core_commands/__init__.py:190
#, fuzzy
msgid "Block connections from user or IP address"
msgstr "Bloki konektojn de uzanto aŭ IP-adreso"

#: pynicotine/plugins/core_commands/__init__.py:197
#, fuzzy
msgid "Remove user or IP address from ban lists"
msgstr "Forigi uzanton el via malpermesa listo"

#: pynicotine/plugins/core_commands/__init__.py:204
#, fuzzy
msgid "Silence messages from user or IP address"
msgstr "Silentigi mesaĝojn de uzanto aŭ IP-adreso"

#: pynicotine/plugins/core_commands/__init__.py:212
#, fuzzy
msgid "Remove user or IP address from ignore lists"
msgstr "Forigi uzanton el via ignora listo"

#: pynicotine/plugins/core_commands/__init__.py:220
#, fuzzy
msgid "Add share"
msgstr "Aldonu Wi_sh"

#: pynicotine/plugins/core_commands/__init__.py:226
#, fuzzy
msgid "Remove share"
msgstr "Forigi Wi_sh"

#: pynicotine/plugins/core_commands/__init__.py:233
#, fuzzy
msgid "List shares"
msgstr "Reskani akciojn"

#: pynicotine/plugins/core_commands/__init__.py:239
#, fuzzy
msgid "Rescan shares"
msgstr "Reskani akciojn"

#: pynicotine/plugins/core_commands/__init__.py:246
#, fuzzy
msgid "Start global file search"
msgstr "Komencu tutmondan dosierserĉon"

#: pynicotine/plugins/core_commands/__init__.py:254
#, fuzzy
msgid "Search files in joined rooms"
msgstr "Serĉu dosierojn kaj dosierujojn (ĝusta kongruo)"

#: pynicotine/plugins/core_commands/__init__.py:262
#, fuzzy
msgid "Search files of all buddies"
msgstr "Serĉu dosierojn kaj dosierujojn (ĝusta kongruo)"

#: pynicotine/plugins/core_commands/__init__.py:270
#, fuzzy
msgid "Search a user's shared files"
msgstr "Serĉu la akciojn de uzanto por 'demando'"

#: pynicotine/plugins/core_commands/__init__.py:296
#, fuzzy, python-format
msgid "Listing %(num)i available commands:"
msgstr "Listo de %(num)i disponeblaj komandoj:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, fuzzy, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr "Listo de %(num)i disponeblaj komandoj kongruaj kun \"%(query)s\":"

#: pynicotine/plugins/core_commands/__init__.py:311
#, fuzzy, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Tajpu %(command)s por listigi similajn komandojn"

#: pynicotine/plugins/core_commands/__init__.py:314
#, fuzzy, python-format
msgid "Type %(command)s to list available commands"
msgstr "Tajpu %(command)s por listigi disponeblajn komandojn"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, fuzzy, python-format
msgid "Not joined in room %s"
msgstr "%s aliĝis al la ĉambro"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:408
#, fuzzy, python-format
msgid "Closed private chat of user %s"
msgstr "Fermu la nunan privatan babilejon"

#: pynicotine/plugins/core_commands/__init__.py:476
#, fuzzy, python-format
msgid "Banned %s"
msgstr "Malpermesita"

#: pynicotine/plugins/core_commands/__init__.py:490
#, fuzzy, python-format
msgid "Unbanned %s"
msgstr "Malpermesu Uzanton"

#: pynicotine/plugins/core_commands/__init__.py:503
#, fuzzy, python-format
msgid "Ignored %s"
msgstr "Ignoritaj Uzantoj"

#: pynicotine/plugins/core_commands/__init__.py:517
#, fuzzy, python-format
msgid "Unignored %s"
msgstr "Ignoru Uzanton"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr ""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr ""

#: pynicotine/pluginsystem.py:413
#, fuzzy
msgid "Loading plugin system"
msgstr "Ŝargita kromaĵo %s"

#: pynicotine/pluginsystem.py:516
#, fuzzy, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Ne eblas ŝargi kromprogramon %(name)s. Kroma dosierujo nomo enhavas "
"nevalidajn signojn: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, fuzzy, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "Konfliktanta %(interface)s komando en kromaĵo %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, fuzzy, python-format
msgid "Loaded plugin %s"
msgstr "Ŝargita kromaĵo %s"

#: pynicotine/pluginsystem.py:579
#, fuzzy, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Ne eblas ŝargi kromprogramon %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, fuzzy, python-format
msgid "Unloaded plugin %s"
msgstr "Malŝarĝita kromaĵo %s"

#: pynicotine/pluginsystem.py:648
#, fuzzy, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Ne eblas malŝarĝi kromaĵon %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, fuzzy, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Kromaĵo %(module)s malsukcesis kun eraro %(errortype)s: %(error)s.\n"
"Spuro: %(trace)s"

#: pynicotine/pluginsystem.py:810
#, fuzzy
msgid "No description"
msgstr "Mempriskribo"

#: pynicotine/pluginsystem.py:887
#, fuzzy, python-format
msgid "Missing %s argument"
msgstr "Mankas %s argumento"

#: pynicotine/pluginsystem.py:896
#, fuzzy, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Nevalida Soulseek URL: %s"

#: pynicotine/pluginsystem.py:901
#, fuzzy, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Uzado: %(command)s %(args)s"

#: pynicotine/pluginsystem.py:940
#, fuzzy, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Nekonata komando: %(command)s. Tajpu %(help_command)s por listigi "
"disponeblajn komandojn."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
#, fuzzy
msgid "No UPnP devices found"
msgstr "Neniuj UPnP-aparatoj trovitaj"

#: pynicotine/portmapper.py:633
#, fuzzy, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"UPnP: Malsukcesis plusendi eksteran havenon %(external_port)s: %(error)s"

#: pynicotine/portmapper.py:647
#, fuzzy, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"UPnP: Ekstera haveno %(external_port)s sukcese plusendita al loka IP-adreso "
"%(ip_address)s haveno %(local_port)s"

#: pynicotine/privatechat.py:220
#, fuzzy, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Privata Mesaĝo de %(user)s"

#: pynicotine/search.py:368
#, fuzzy, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Serĉante deziralisteron \"%s\""

#: pynicotine/search.py:434
#, fuzzy, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Dezirlista atendperiodo agordita al %s sekundoj"

#: pynicotine/search.py:760
#, fuzzy, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr "Uzanto %(user)s serĉas \"%(query)s\", donante %(num)i rezultojn"

#: pynicotine/shares.py:302
#, fuzzy
msgid "Rebuilding shares…"
msgstr "Reskanante akciojn…"

#: pynicotine/shares.py:302
#, fuzzy
msgid "Rescanning shares…"
msgstr "Reskanante akciojn…"

#: pynicotine/shares.py:324
#, fuzzy, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Reskanado kompleta: %(num)s dosierujoj trovitaj"

#: pynicotine/shares.py:334
#, fuzzy, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Grava eraro okazis dum reskanado de akcioj. Se ĉi tiu problemo daŭras, "
"forigu %(dir)s/*.db kaj provu denove. Se tio ne helpas, bonvolu registri "
"cimraporton kun ĉi tiu stakspuro inkluzivita: %(trace)s"

#: pynicotine/shares.py:582
#, fuzzy, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Eraro dum skanado de dosiero %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, fuzzy, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Eraro dum skanado de dosierujo %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, fuzzy, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "Eraro dum skanado de metadatenoj por dosiero %(path)s: %(error)s"

#: pynicotine/shares.py:1046
#, fuzzy, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Reskanado ĉesigita pro nedisponeblaj akcioj"

#: pynicotine/shares.py:1184
#, fuzzy, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "Uzanto %(user)s foliumas vian liston de komunaj dosieroj"

#: pynicotine/slskmessages.py:3120
#, fuzzy, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Ne eblas legi datumbazon de akcioj. Bonvolu reskani viajn akciojn. Eraro: %s"

#: pynicotine/slskproto.py:500
#, fuzzy, python-format
msgid "Specified network interface '%s' is not available"
msgstr "Specifita retinterfaco '%s' ne ekzistas"

#: pynicotine/slskproto.py:511
#, fuzzy, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Ne povas aŭskulti en haveno %(port)s. Certigu, ke neniu alia aplikaĵo uzas "
"ĝin, aŭ elektu alian havenon. Eraro: %(error)s"

#: pynicotine/slskproto.py:523
#, fuzzy, python-format
msgid "Listening on port: %i"
msgstr "Aŭskultu sur haveno %i"

#: pynicotine/slskproto.py:805
#, fuzzy, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "Ne povas konektiĝi al servilo %(host)s:%(port)s: %(error)s"

#: pynicotine/slskproto.py:1095
#, fuzzy, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Aŭtomate konekti al servilo dum ekfunkciigo"

#: pynicotine/slskproto.py:1170
#, fuzzy, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Konektante al %(host)s:%(port)s"

#: pynicotine/slskproto.py:1208
#, fuzzy, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Konektita al servilo %(host)s:%(port)s, ensalutu…"

#: pynicotine/slskproto.py:1493
#, fuzzy, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Malkonektita de servilo %(host)s:%(port)s"

#: pynicotine/slskproto.py:1499
#, fuzzy
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Iu ensalutinta al via Soulseek-konto aliloke"

#: pynicotine/uploads.py:382
#, fuzzy, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr "Alŝuto finita: uzanto %(user)s, IP-adreso %(ip)s, dosiero %(file)s"

#: pynicotine/uploads.py:395
#, fuzzy, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Alŝuto ĉesigita, uzanto %(user)s dosiero %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, fuzzy, python-format
msgid "Upload I/O error: %s"
msgstr "Alŝutu I/O-eraro: %s"

#: pynicotine/uploads.py:1103
#, fuzzy, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr "Alŝuto komencita: uzanto %(user)s, IP-adreso %(ip)s, dosiero %(file)s"

#: pynicotine/userbrowse.py:176
#, fuzzy, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr "Ne povas krei dosierujon '%(folder)s', raportita eraro: %(error)s"

#: pynicotine/userbrowse.py:236
#, fuzzy, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Ŝargado de akcioj de disko malsukcesis: %(error)s"

#: pynicotine/userbrowse.py:282
#, fuzzy, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr "Konservita listo de komunaj dosieroj por uzanto '%(user)s' al %(dir)s"

#: pynicotine/userbrowse.py:286
#, fuzzy, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr "Ne povas konservi akciojn, '%(user)s', raportita eraro: %(error)s"

#: pynicotine/userinfo.py:160
#, fuzzy, python-format
msgid "Picture saved to %s"
msgstr "Bildo konservita al %s"

#: pynicotine/userinfo.py:163
#, fuzzy, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Ne povas konservi %(filename)s: %(error)s"

#: pynicotine/userinfo.py:190
#, fuzzy, python-format
msgid "User %(user)s is viewing your profile"
msgstr "Uzanto %(user)s legas viajn uzantinformojn"

#: pynicotine/users.py:272
#, fuzzy, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Ne eblas konektiĝi al la servilo. Kialo: %s"

#: pynicotine/users.py:272
#, fuzzy
msgid "Cannot Connect"
msgstr "Ne povas konekti"

#: pynicotine/users.py:306
#, fuzzy, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr "Ne povas preni la IP de uzanto %s, ĉar ĉi tiu uzanto estas eksterrete"

#: pynicotine/users.py:315
#, fuzzy, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr "IP-adreso de uzanto %(user)s estas %(ip)s, haveno %(port)i%(country)s"

#: pynicotine/users.py:433
#, fuzzy
msgid "Soulseek Announcement"
msgstr "Soulseek Kliento"

#: pynicotine/users.py:449
#, fuzzy
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"Vi ne havas privilegiojn. Privilegioj ne estas bezonataj, sed permesas viajn "
"elŝutojn esti vicigitaj antaŭ ne-privilegiaj uzantoj."

#: pynicotine/users.py:455
#, fuzzy, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"Restas %(days)i tagoj, %(hours)i horoj, %(minutes)i minutoj, %(seconds)i "
"sekundoj da elŝutaj privilegioj."

#: pynicotine/users.py:473
#, fuzzy
msgid "Your password has been changed"
msgstr "Via pasvorto estis ŝanĝita. Pasvorto estas %s"

#: pynicotine/users.py:473
#, fuzzy
msgid "Password Changed"
msgstr "Pasvorto Ŝanĝo Malakceptita"

#: pynicotine/utils.py:574
#, fuzzy, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Ne eblas konservi dosieron %(path)s: %(error)s"

#: pynicotine/utils.py:621
#, fuzzy, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "Ne povas konservi %(filename)s: %(error)s"

#: pynicotine/utils.py:646
#, fuzzy, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr "Io misfunkciis dum legado de dosiero %(filename)s: %(error)s"

#: pynicotine/utils.py:651
#, fuzzy, python-format
msgid "Attempting to load backup of file %s"
msgstr "Provante ŝargi sekurkopion de dosiero %s"

#: pynicotine/utils.py:673
#, fuzzy, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Ne eblas konservi dosieron %(path)s: %(error)s"

#: pynicotine/utils.py:694
#, fuzzy, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Ne eblas konservi dosieron %(path)s: %(error)s"

#: pynicotine/utils.py:705
#, fuzzy, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Ne eblas restarigi antaŭan dosieron %(path)s: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
#, fuzzy
msgid "Add buddy…"
msgstr "Aldonu amikon…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
#, fuzzy
msgid "Toggle Text-to-Speech"
msgstr "Ŝaltu Teksto-al-Parolado"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
#, fuzzy
msgid "Chat Room Command Help"
msgstr "Helpo pri komando de babilejo"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
#, fuzzy
msgid "_Log"
msgstr "_Logu"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
#, fuzzy
msgid "Room Wall"
msgstr "Ĉambra muro"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
#, fuzzy
msgid "R_oom Wall"
msgstr "Ĉambra muro"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
#, fuzzy
msgid "Created by"
msgstr "Kreita de"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
#, fuzzy
msgid "Translated by"
msgstr "Tradukita de"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
#, fuzzy
msgid "License"
msgstr "Permesilo"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
#, fuzzy
msgid "Welcome to Nicotine+"
msgstr "Bonvenon al Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
#, fuzzy
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr "Se via dezirata uzantnomo jam estas prenita, oni petos vin ŝanĝi ĝin."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
#, fuzzy
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Se via aŭskulta haveno estas fermita, vi nur povos konektiĝi al uzantoj, "
"kies aŭskultaj havenoj estas malfermitaj."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
#, fuzzy
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"Se necese, elektu alian aŭskultan havenon sube. Ĉi tio ankaŭ povas esti "
"farita poste en la preferoj."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
#, fuzzy
msgid "Download Files to Folder"
msgstr "Elŝutu Dosieroj al Dosierujo"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
#, fuzzy
msgid "Share Folders"
msgstr "Kunhavigi Dosierujojn"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
#, fuzzy
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Uzantoj de Soulseek povos elŝuti el viaj akcioj. Kontribuu al la reto "
"Soulseek dividante vian propran kolekton kaj redividante tion, kion vi "
"elŝutis de aliaj uzantoj."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
#, fuzzy
msgid "You are ready to use Nicotine+!"
msgstr "Vi pretas uzi Nicotine+!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
#, fuzzy
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Donacado al Soulseek donas al vi privilegiojn por certa tempoperiodo. Se vi "
"havas privilegiojn, viaj elŝutoj estos vicigitaj antaŭ ne-privilegiaj "
"uzantoj."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
#, fuzzy
msgid "Previous File"
msgstr "Antaŭa dosiero"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
#, fuzzy
msgid "Next File"
msgstr "Sekva dosiero"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
#, fuzzy
msgid "Name"
msgstr "Dosiernomo"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
#, fuzzy
msgid "Last Speed"
msgstr "Lasta Rapido"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
#, fuzzy
msgid "_Export…"
msgstr "Eksporto"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
#, fuzzy
msgid "Keyboard Shortcuts"
msgstr "Klavaraj ŝparvojoj"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
#, fuzzy
msgid "General"
msgstr "Generalo"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
#, fuzzy
msgid "Connect"
msgstr "Konekti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
#, fuzzy
msgid "Disconnect"
msgstr "Malkonekti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
#, fuzzy
msgid "Rescan Shares"
msgstr "Reskani Akciojn"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
#, fuzzy
msgid "Show Log Pane"
msgstr "Montru Log-Panelon"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
#, fuzzy
msgid "Confirm Quit"
msgstr "Agordu ensalutadon"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
#, fuzzy
msgid "Quit"
msgstr "Forlasu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
#, fuzzy
msgid "Menus"
msgstr "Menuoj"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
#, fuzzy
msgid "Open Main Menu"
msgstr "Malfermu Ĉefan Menuon"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
#, fuzzy
msgid "Open Context Menu"
msgstr "Malfermu Kuntekstan Menuon"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
#, fuzzy
msgid "Tabs"
msgstr "Langetoj"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
#, fuzzy
msgid "Change Main Tab"
msgstr "Ŝanĝi Ĉefan Langeton"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
#, fuzzy
msgid "Go to Previous Secondary Tab"
msgstr "Iru al Antaŭa Malĉefa Langeto"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
#, fuzzy
msgid "Go to Next Secondary Tab"
msgstr "Iru al Sekva Malĉefa Langeto"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
#, fuzzy
msgid "Reopen Closed Secondary Tab"
msgstr "Fermu Sekundaran Langeton"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
#, fuzzy
msgid "Close Secondary Tab"
msgstr "Fermu Sekundaran Langeton"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
#, fuzzy
msgid "Lists"
msgstr "Listoj"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
#, fuzzy
msgid "Copy Selected Cell"
msgstr "Elekti ĉiujn"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
#, fuzzy
msgid "Select All"
msgstr "Elekti ĉiujn"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
#, fuzzy
msgid "Find"
msgstr "Trovu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
#, fuzzy
msgid "Remove Selected Row"
msgstr "Forigi Elektitan Vicon"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
#, fuzzy
msgid "Editing"
msgstr "Redaktado"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
#, fuzzy
msgid "Cut"
msgstr "Tranĉi"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
#, fuzzy
msgid "Paste"
msgstr "Alglui"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
#, fuzzy
msgid "Insert Emoji"
msgstr "Enigu Emoji"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
#, fuzzy
msgid "File Transfers"
msgstr "Translokigoj"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
#, fuzzy
msgid "Resume / Retry Transfer"
msgstr "Rekomenci/Reprovi Translokigon"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
#, fuzzy
msgid "Pause / Abort Transfer"
msgstr "Paŭzi / Ĉesi Translokigon"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
#, fuzzy
msgid "Download / Upload To"
msgstr "Elŝutu Dosierujon _Al…"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
#, fuzzy
msgid "Save List to Disk"
msgstr "_Konservu Liston de Akcioj al Disko"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
#, fuzzy
msgid "Refresh"
msgstr "Refreŝigi dosierojn"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
#, fuzzy
msgid "Expand / Collapse All"
msgstr "Plivastigi / Kolapsigi ĉiujn"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
#, fuzzy
msgid "Back to Parent Folder"
msgstr "Ne eblas Kunhavigi Dosierujon"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
#, fuzzy
msgid "File Search"
msgstr "Serĉu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
#, fuzzy
msgid "Result Filters"
msgstr "_Rezultaj Filtriloj"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
#, fuzzy
msgid "Current Session"
msgstr "Nuna Sesio"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
#, fuzzy
msgid "Completed Downloads"
msgstr "Finitaj Elŝutoj"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
#, fuzzy
msgid "Downloaded Size"
msgstr "Elŝutita Grandeco"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
#, fuzzy
msgid "Completed Uploads"
msgstr "Finitaj Alŝutoj"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
#, fuzzy
msgid "Uploaded Size"
msgstr "Alŝutita Grandeco"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
#, fuzzy
msgid "Total"
msgstr "Entute"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
#, fuzzy
msgid "_Reset…"
msgstr "_Rekomenci"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
#, fuzzy
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Dezirlistaĵoj estas aŭtomate serĉataj je regulaj intervaloj, por malkovri "
"nekutimajn dosierojn."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
#, fuzzy
msgid "Add Wish…"
msgstr "Aldonu Deziron…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
#, fuzzy
msgid "Clear All…"
msgstr "Forigi Ĉion…"

#: pynicotine/gtkgui/ui/downloads.ui:138
#, fuzzy
msgid "Clear All Finished/Filtered Downloads"
msgstr "Dosiero elŝutita"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
#, fuzzy
msgid "Clear Finished"
msgstr "Klara Finita"

#: pynicotine/gtkgui/ui/downloads.ui:169
#, fuzzy
msgid "Clear Specific Downloads"
msgstr "Forigi Ĉiuj Elŝutoj"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
#, fuzzy
msgid "Clear _All…"
msgstr "Forigi Ĉion…"

#: pynicotine/gtkgui/ui/interests.ui:21
#, fuzzy
msgid "Personal Interests"
msgstr "Personaj Interesoj"

#: pynicotine/gtkgui/ui/interests.ui:40
#, fuzzy
msgid "Add something you like…"
msgstr "Aldonu ion, kion vi ŝatas…"

#: pynicotine/gtkgui/ui/interests.ui:62
#, fuzzy
msgid "Personal Dislikes"
msgstr "Personaj Malŝatoj"

#: pynicotine/gtkgui/ui/interests.ui:80
#, fuzzy
msgid "Add something you dislike…"
msgstr "Aldonu ion, kion vi malŝatas…"

#: pynicotine/gtkgui/ui/interests.ui:143
#, fuzzy
msgid "Refresh Recommendations"
msgstr "Rekomendoj"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
#, fuzzy
msgid "Main Menu"
msgstr "Malfermu Ĉefan Menuon"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
#, fuzzy
msgid "Room…"
msgstr "Ĉambro…"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
#, fuzzy
msgid "Username…"
msgstr "Uzantnomo…"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
#, fuzzy
msgid "Search term…"
msgstr "Serĉa termino…"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
#, fuzzy
msgid "Clear"
msgstr "Klara"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
#, fuzzy
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Serĉaj ŝablonoj: kun vorto = termino, sen vorto = -termino, parta vorto = "
"*erm"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
#, fuzzy
msgid "Search Scope"
msgstr "Serĉu amplekson"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
#, fuzzy
msgid "_Wishlist"
msgstr "_Dezirlisto"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
#, fuzzy
msgid "Configure Searches"
msgstr "Agordi serĉojn"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
#, fuzzy
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Enigu serĉterminon por serĉi dosierojn kunhavitajn de aliaj uzantoj en la "
"Soulseek-reto"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
#, fuzzy
msgid "File Grouping Mode"
msgstr "Dosiera grupiga reĝimo"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
#, fuzzy
msgid "Configure Downloads"
msgstr "Agordi elŝutojn"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
#, fuzzy
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Dosieroj, kiujn vi elŝutas de aliaj uzantoj, estas vicigitaj ĉi tie, kaj "
"povas esti paŭzitaj kaj rekomencitaj laŭpeto"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
#, fuzzy
msgid "Configure Uploads"
msgstr "Agordi alŝutojn"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
#, fuzzy
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Provoj de uzantoj elŝuti viajn komunajn dosierojn estas vicigitaj kaj "
"administritaj ĉi tie"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
#, fuzzy
msgid "_Open List"
msgstr "_Malferma Listo"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
#, fuzzy
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Malfermas lokan liston de komunaj dosieroj, kiuj antaŭe estis konservitaj al "
"disko"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
#, fuzzy
msgid "Configure Shares"
msgstr "_Agordu Akciojn"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
#, fuzzy
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Enigu la nomon de uzanto, kies komunajn dosierojn vi ŝatus foliumi. Vi ankaŭ "
"povas konservi la liston al disko, kaj inspekti ĝin poste."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
#, fuzzy
msgid "_Personal Profile"
msgstr "Personaj Malŝatoj"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
#, fuzzy
msgid "Configure Account"
msgstr "Agordu ensalutadon"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
#, fuzzy
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Enigu la nomon de uzanto por vidi ilian uzantan priskribon, informojn kaj "
"personan bildon"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
#, fuzzy
msgid "Chat _History"
msgstr "Babilhistorio"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
#, fuzzy
msgid "Configure Chats"
msgstr "Agordi akciojn"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
#, fuzzy
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr ""
"Enigu la nomon de uzanto por komenci tekstan konversacion kun ili private"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
#, fuzzy
msgid "_Message All"
msgstr "Mesaĝoj"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
#, fuzzy
msgid "Configure Ignored Users"
msgstr "Ignoritaj Uzantoj"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
#, fuzzy
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Aldonu uzantojn kiel amikojn por dividi specifajn dosierujojn kun ili kaj "
"ricevi sciigojn kiam ili estas interretaj"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
#, fuzzy
msgid "Join or create room…"
msgstr "Aliĝu aŭ kreu ĉambron…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
#, fuzzy
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Aliĝu al ekzistanta babilejo aŭ kreu novan ĉambron por babili kun aliaj "
"uzantoj en la reto Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
#, fuzzy
msgid "Configure User Profile"
msgstr "Rigardu Uzantan Profilon"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
#, fuzzy
msgid "Connections"
msgstr "Konektoj"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
#, fuzzy
msgid "Downloading (Speed / Active Users)"
msgstr "Elŝuto (rapideco/aktivaj uzantoj)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
#, fuzzy
msgid "Uploading (Speed / Active Users)"
msgstr "Alŝuto (rapideco/aktivaj uzantoj)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
#, fuzzy
msgid "Search chat history…"
msgstr "Serĉa termino…"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
#, fuzzy
msgid "Download Speed Limits"
msgstr "Elŝutu Rapidlimojn"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
#, fuzzy
msgid "Unlimited download speed"
msgstr "Limigu alŝutan rapidon:"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
#, fuzzy
msgid "Use download speed limit (KiB/s):"
msgstr "Alternativa elŝuta rapidlimo (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
#, fuzzy
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Alternativa elŝuta rapidlimo (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
#, fuzzy
msgid "Search rooms…"
msgstr "Serĉa termino…"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
#, fuzzy
msgid "Refresh Rooms"
msgstr "Refreŝigi liston de ĉambro"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
#, fuzzy
msgid "_Show feed of public chat room messages"
msgstr "_Montri feed de publika babilejo mesaĝoj"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
#, fuzzy
msgid "_Accept private room invitations"
msgstr "_Akceptu invitojn pri privataj ĉambroj"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
#, fuzzy
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"La ĉambra muro-trajto permesas al uzantoj en ĉambro specifi unikan mesaĝon "
"por montri al aliaj. Lastatempaj mesaĝoj estas montritaj supre."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
#, fuzzy
msgid "Set wall message…"
msgstr "Agordu murmesaĝon…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
#, fuzzy
msgid "Search Result Filters"
msgstr "Serĉrezultaj Filtriloj"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
#, fuzzy
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Serĉrezultaj filtriloj estas uzataj por rafini kiuj serĉrezultoj estas "
"montrataj."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
#, fuzzy
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Ĉiu listo de serĉrezultoj havas sian propran filtrilon, kiu povas esti "
"malkaŝita ŝaltante la butonon de Rezultaj Filtriloj. Filtrilo konsistas el "
"pluraj kampoj, kiuj ĉiuj estas aplikataj kiam oni premas Enter en iu ajn el "
"ĝiaj kampoj. Filtrilo estas aplikata tuj al rezultoj jam ricevitaj, kaj "
"ankaŭ al tiuj ankoraŭ alvenontaj. Por vidi la plenajn rezultojn denove, "
"simple forigu la filtrilon de ĉiuj terminoj kaj reapliku ĝin. Kiel la nomo "
"sugestas, serĉrezulta filtrilo ne povas pligrandigi vian originalan serĉon, "
"ĝi nur povas malvastigi ĝin. Por plivastigi aŭ ŝanĝi viajn serĉajn "
"terminojn, faru novan serĉon."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
#, fuzzy
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Kiel la nomo sugestas, serĉrezulta filtrilo ne povas vastigi vian originan "
"serĉon, ĝi nur povas malvastigi ĝin. Por plivastigi aŭ ŝanĝi viajn serĉajn "
"terminojn, faru novan serĉon."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
#, fuzzy
msgid "Result Filter Usage"
msgstr "_Rezultaj Filtriloj"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
#, fuzzy
msgid "Include Text"
msgstr "Inkluzivi Tekston"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
#, fuzzy
msgid "Files, folders and usernames containing this text will be shown."
msgstr "Dosieroj kaj dosierujoj enhavantaj ĉi tiun tekston estos montritaj."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
#, fuzzy
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Majuskleco estas nesentema, sed vortordo estas grava: 'Instrumental Remix' "
"ne montros neniun 'Remix Instrumental'"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
#, fuzzy
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Uzu | (aŭ pipoj) por apartigi plurajn ekzaktajn frazojn. Ekzemplo:\n"
"    Remiksaĵo|Dub Mix|Instrumenta"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
#, fuzzy
msgid "Exclude Text"
msgstr "Ekskludi Tekston"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
#, fuzzy
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"Kiel supre, sed dosieroj kaj dosierujoj estas filtritaj se la teksto "
"kongruas."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
#, fuzzy
msgid "Filters files based upon their file extension."
msgstr "Filtrilas dosierojn surbaze de ilia dosiera etendo."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
#, fuzzy
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Multoblaj dosieraj etendoj povas esti specifitaj, kiuj siavice plilarĝigos "
"la liston de rezultoj.\n"
"    Ekzemplo: flac|wav|ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
#, fuzzy
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"Eblas ankaŭ inversigi la filtrilon, specifante dosier-etendojn, kiujn vi ne "
"volas en viaj rezultoj.\n"
"    Ekzemplo: !mp3|!jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
#, fuzzy
msgid "File Size"
msgstr "Dosiera Grandeco"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
#, fuzzy
msgid "Filters files based upon their file size."
msgstr "Filtrilas dosierojn laŭ ilia grandeco de dosiero."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
#, fuzzy
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"Defaŭlte, la unuo uzata estas bajtoj kaj dosieroj pli grandaj ol aŭ egalaj "
"al la valoro estos kongruaj."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
#, fuzzy
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Aldonu b, k, m aŭ g (alternative kib, mib aŭ gib) por specifi bajtajn, "
"kibibajtajn, mebibajtajn aŭ gibibajtajn unuojn:\n"
"    <1024k trovos dosierojn 1024 kibibajtoj (t.e. 1 mebibajto) aŭ pli "
"malgrandaj."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
#, fuzzy
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Antaŭmeti = al valoro por specifi ĝustan kongruon:\n"
"    =1024 nur kongruas dosierojn kun grandeco de 1024 bajtoj (t.e. 1 "
"kibibajto)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
#, fuzzy
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Antaŭmeti = al valoro por specifi ĝustan kongruon:\n"
"    =1024 nur kongruas dosierojn kun grandeco de 1024 bajtoj (t.e. 1 "
"kibibajto)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
#, fuzzy
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Antaŭmetu < aŭ > por trovi dosierojn pli malgrandajn/pli grandajn ol la "
"donita valoro:\n"
"    >10.5m|<1g por montri dosierojn pli grandajn ol 10.5 MiB (mebibajtoj),\n"
"    sed pli malgranda ol 1 GiB (gibibajtoj), uzu | inter kondiĉoj."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
#, fuzzy
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Por oportuno, la variantoj kb, mb, kaj gb por la pli konataj kilo-, mega-, "
"kaj gigabajtaj unuoj ankaŭ povas esti uzataj."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
#, fuzzy
msgid "Filters files based upon their bitrate."
msgstr "Filtrilas dosierojn surbaze de ilia bitrateco."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
#, fuzzy
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Valoroj devas esti enigitaj nur kiel nombraj ciferoj. La unuo ĉiam estas Kb/"
"s (Kilobitoj por sekundo)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
#, fuzzy
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Kiel Dosiera Grandeco (supre), operatoroj =, !, <, kaj > povas esti uzataj, "
"kaj pluraj kondiĉoj povas esti specifitaj per | tuboj:\n"
"    >256|<1411 por montri dosierojn kun bitrapideco de almenaŭ 256 Kb/s\n"
"    kun maksimuma bitrapideco de 1411 Kb/s."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
#, fuzzy
msgid "Filters files based upon their duration."
msgstr "Filtrilas dosierojn surbaze de ilia bitrateco."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
#, fuzzy
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"Defaŭlte, dosieroj pli longaj ol aŭ egalaj al la enigita daŭro estos "
"kongruaj, krom se operatoro (=, !, <, aŭ >) estas uzata."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
#, fuzzy
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Enigu krudan valoron en sekundoj aŭ uzu la tempoformatojn MM:SS kaj HH:MM:"
"SS:\n"
"    > 5:30 por montri dosierojn almenaŭ 5 minutojn kaj duonon.\n"
"    <5:30:00 montras dosierojn longajn malpli ol 5 horojn kaj duonon."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
#, fuzzy
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Multoblaj kondiĉoj povas esti specifitaj per | tubaj apartigiloj:\n"
"    >6:00|<12:00 por montri dosierojn inter 6 kaj 12 minutojn.\n"
"    !9:54|!8:43|!7:32 por kaŝi iujn specifajn dosierojn el la rezultoj.\n"
"    =5:34|=4:23|=3:05 por inkluzivi dosierojn kun specifaj daŭroj."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
#, fuzzy
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Uzas landkodojn difinitajn de ISO 3166-2 (vidu Vikipedion):\n"
"    'Usono' nur resendos dosierojn de uzantoj konektitaj per Usono. Simile, "
"'GB' resendas dosierojn de uzantoj kun IP-oj en Britio."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
#, fuzzy
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Pluraj landoj povas esti specifitaj per komoj aŭ spacoj."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
#, fuzzy
msgid "Free Slot"
msgstr "Senpaga Slot"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
#, fuzzy
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Montru nur tiujn rezultojn de uzantoj, kiuj havas almenaŭ unu alŝutan fendo "
"senpage. Ĉi tiu filtrilo estas aplikata tuj."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
#, fuzzy
msgid "Upload Speed Limits"
msgstr "Alŝutu Rapidlimojn"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
#, fuzzy
msgid "Unlimited upload speed"
msgstr "Limigu alŝutan rapidon:"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
#, fuzzy
msgid "Use upload speed limit (KiB/s):"
msgstr "Alternativa alŝuta rapideclimo (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
#, fuzzy
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Alternativa alŝuta rapideclimo (KiB/s):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
#, fuzzy
msgid "Private Chat Command Help"
msgstr "Privata babila komando helpo"

#: pynicotine/gtkgui/ui/search.ui:7
#, fuzzy
msgid "Include text…"
msgstr "Enmetu tekston…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
#, fuzzy
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtru en rezultoj, kies dosiervojoj enhavas la specifitan tekston. "
"Multoblaj frazoj kaj vortoj povas esti precizigitaj, ekz. ekzakta frazo|"
"muziko|termino|preciza frazo du"

#: pynicotine/gtkgui/ui/search.ui:18
#, fuzzy
msgid "Exclude text…"
msgstr "Ekskludi tekston…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
#, fuzzy
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtru el rezultoj, kies dosiervojoj enhavas la specifitan tekston. "
"Multoblaj frazoj kaj vortoj povas esti precizigitaj, ekz. ekzakta frazo|"
"muziko|termino|preciza frazo du"

#: pynicotine/gtkgui/ui/search.ui:29
#, fuzzy
msgid "File type…"
msgstr "Dosiertipo…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
#, fuzzy
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Dosiertipo, ekz. flac|wav|ape aŭ !mp3|!m4a"

#: pynicotine/gtkgui/ui/search.ui:40
#, fuzzy
msgid "File size…"
msgstr "Dosiera grandeco…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
#, fuzzy
msgid "File size, e.g. >10.5m <1g"
msgstr "Dosiera grandeco, ekz. > 10,5 m < 1 g"

#: pynicotine/gtkgui/ui/search.ui:51
#, fuzzy
msgid "Bitrate…"
msgstr "Bitrapideco…"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
#, fuzzy
msgid "Bitrate, e.g. 256 <1412"
msgstr "Bitrate, ekz. 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
#, fuzzy
msgid "Duration…"
msgstr "Daŭro…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
#, fuzzy
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Daŭro, ekz. 2:20|!3:30|=4:40"

#: pynicotine/gtkgui/ui/search.ui:73
#, fuzzy
msgid "Country code…"
msgstr "Landokodo…"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
#, fuzzy
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Landkodo, ekz. US|GB|ES aŭ !DE|!GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
#, fuzzy
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Malpermesu uzantojn aliri viajn komunajn dosierojn, laŭ uzantnomo, IP-adreso "
"aŭ lando."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
#, fuzzy
msgid "Country codes to block (comma separated):"
msgstr "Landkodoj por bloki (kome apartigitaj):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
#, fuzzy
msgid "Codes must be in ISO 3166-2 format."
msgstr "Kodoj devas esti en formato ISO 3166-2."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
#, fuzzy
msgid "Use custom geo block message:"
msgstr "Uzu kutiman geoblokan mesaĝon:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
#, fuzzy
msgid "Use custom ban message:"
msgstr "Uzu kutiman malpermesan mesaĝon:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
#, fuzzy
msgid "IP Addresses"
msgstr "Adresoj"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
#, fuzzy
msgid "Restore previously open private chats on startup"
msgstr "Restarigu antaŭe malfermitajn privatajn babilojn ĉe ekfunkciigo"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
#, fuzzy
msgid "Enable spell checker"
msgstr "Ebligu literumkontrolilon (postulas rekomencon)"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
#, fuzzy
msgid "Enable CTCP-like private message responses (client version)"
msgstr "Ebligu CTCP-similajn privatajn mesaĝajn respondojn (klienta versio)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
#, fuzzy
msgid "Number of recent private chat messages to show:"
msgstr "Nombro de lastatempaj babillinioj por montri:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
#, fuzzy
msgid "Number of recent chat room messages to show:"
msgstr "Nombro de lastatempaj babillinioj por montri:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
#, fuzzy
msgid "Chat Completion"
msgstr "Kompletigo"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
#, fuzzy
msgid "Enable tab-key completion"
msgstr "Ebligu kompletigon de tabulado"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
#, fuzzy
msgid "Enable completion drop-down list"
msgstr "Ebligu kompletigan falliston"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
#, fuzzy
msgid "Minimum characters required to display drop-down:"
msgstr "Minimumaj signoj necesaj por montri la falmenumon:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
#, fuzzy
msgid "Allowed chat completions:"
msgstr "Permesitaj Kompletigoj"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
#, fuzzy
msgid "Buddy names"
msgstr "Kamaradoj"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
#, fuzzy
msgid "Chat room usernames"
msgstr "Mesaĝo pri babilejo:"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
#, fuzzy
msgid "Room names"
msgstr "Ĉambroj"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
#, fuzzy
msgid "Commands"
msgstr "Komandoj"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
#, fuzzy
msgid "Timestamps"
msgstr "Tempomarkoj"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
#, fuzzy
msgid "Private chat format:"
msgstr "Formato de privata ĉambro:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
#, fuzzy
msgid "Default"
msgstr "Defaŭlte"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
#, fuzzy
msgid "Chat room format:"
msgstr "Formato de babilejo:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
#, fuzzy
msgid "Text-to-Speech"
msgstr "Teksto-al-Parolado"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
#, fuzzy
msgid "Enable Text-to-Speech"
msgstr "Ebligu Teksto-al-Parolado"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
#, fuzzy
msgid "Text-to-Speech command:"
msgstr "Teksto-al-Parola komando:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
#, fuzzy
msgid "Private chat message:"
msgstr "Privata babilmesaĝo:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
#, fuzzy
msgid "Chat room message:"
msgstr "Mesaĝo pri babilejo:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
#, fuzzy
msgid "Censor"
msgstr "Cenzuri"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
#, fuzzy
msgid "Enable censoring of text patterns"
msgstr "Ebligu cenzuradon de tekstaj ŝablonoj"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
#, fuzzy
msgid "Auto-Replace"
msgstr "Aŭtomate Anstataŭigi"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
#, fuzzy
msgid "Enable automatic replacement of words"
msgstr "Ebligu aŭtomatan anstataŭigon de vortoj"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
#, fuzzy
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr "Autoclear finitaj/filtritaj elŝutoj de transiga listo"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
#, fuzzy
msgid "Store completed downloads in username subfolders"
msgstr "Stoku finitajn elŝutojn en uzantnomaj subdosierujoj"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
#, fuzzy
msgid "Double-click action for downloads:"
msgstr "Duobla alklaku ago por elŝutoj:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
#, fuzzy
msgid "Allow users to send you any files:"
msgstr "Permesu al ĉi tiuj uzantoj sendi al vi dosierojn:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
#, fuzzy
msgid "Folders"
msgstr "Dosierujoj"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
#, fuzzy
msgid "Finished downloads:"
msgstr "Dosiero elŝutita"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
#, fuzzy
msgid "Incomplete downloads:"
msgstr "Finitaj Elŝutoj"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
#, fuzzy
msgid "Received files:"
msgstr "Ricevitaj dosieroj:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
#, fuzzy
msgid "Events"
msgstr "Eventoj"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
#, fuzzy
msgid "Run command after file download finishes ($ for file path):"
msgstr "Rulu komandon post finiĝo de dosiero ($ por dosiervojo):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
#, fuzzy
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"Rulu komandon post la elŝuto de dosierujo finiĝas ($ por dosierujo-vojo):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
#, fuzzy
msgid "Download Filters"
msgstr "Elŝutu Filtrilojn"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
#, fuzzy
msgid "Enable download filters"
msgstr "Ebligu elŝutajn filtrilojn"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
#, fuzzy
msgid "Add"
msgstr "Aldoni"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
#, fuzzy
msgid "Load Defaults"
msgstr "Ŝargi defaŭltojn"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
#, fuzzy
msgid "Verify Filters"
msgstr "Kontrolu Filtrilojn"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
#, fuzzy
msgid "Unverified"
msgstr "Nekontrolita"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
#, fuzzy
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Ignoru babilmesaĝojn kaj serĉrezultojn de uzantoj, surbaze de uzantnomo aŭ "
"IP-adreso."

#: pynicotine/gtkgui/ui/settings/log.ui:94
#, fuzzy
msgid "Log chatrooms by default"
msgstr "Ensalutu babilejojn defaŭlte"

#: pynicotine/gtkgui/ui/settings/log.ui:118
#, fuzzy
msgid "Log private chat by default"
msgstr "Ensalutu privatan babilejon defaŭlte"

#: pynicotine/gtkgui/ui/settings/log.ui:142
#, fuzzy
msgid "Log transfers to file"
msgstr "Ensalutu translokigojn al dosiero"

#: pynicotine/gtkgui/ui/settings/log.ui:166
#, fuzzy
msgid "Log debug messages to file"
msgstr "Ensalutu sencimigajn mesaĝojn al dosiero"

#: pynicotine/gtkgui/ui/settings/log.ui:190
#, fuzzy
msgid "Log timestamp format:"
msgstr "Log-dosierformato:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
#, fuzzy
msgid "Folder Locations"
msgstr "Dosierujoj"

#: pynicotine/gtkgui/ui/settings/log.ui:245
#, fuzzy
msgid "Chatroom logs folder:"
msgstr "Dosierujo de protokoloj de Babilejo:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
#, fuzzy
msgid "Private chat logs folder:"
msgstr "Dosierujo pri privataj protokoloj:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
#, fuzzy
msgid "Transfer logs folder:"
msgstr "Dosierujo de translokigaj protokoloj:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
#, fuzzy
msgid "Debug logs folder:"
msgstr "Sencimiga protokolo-dosierujo:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
#, fuzzy
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Ensalutu al ekzistanta Soulseek-konto aŭ kreu novan. Uzantnomoj estas "
"uskleksentemaj kaj unikaj."

#: pynicotine/gtkgui/ui/settings/network.ui:118
#, fuzzy
msgid "Public IP address:"
msgstr "Bloki IP-adreson"

#: pynicotine/gtkgui/ui/settings/network.ui:159
#, fuzzy
msgid "Listening port:"
msgstr "Aŭskultu sur haveno %i"

#: pynicotine/gtkgui/ui/settings/network.ui:185
#, fuzzy
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Aŭtomate plusendi aŭskultan havenon (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
#, fuzzy
msgid "Away Status"
msgstr "For Statuso"

#: pynicotine/gtkgui/ui/settings/network.ui:228
#, fuzzy
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Minutoj da neaktiveco antaŭ foriro (0 por malŝalti):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
#, fuzzy
msgid "Auto-reply message when away:"
msgstr "Aŭtomate responda mesaĝo kiam for:"

#: pynicotine/gtkgui/ui/settings/network.ui:299
#, fuzzy
msgid "Auto-connect to server on startup"
msgstr "Aŭtomate konekti al servilo dum ekfunkciigo"

#: pynicotine/gtkgui/ui/settings/network.ui:323
#, fuzzy
msgid "Soulseek server:"
msgstr "Soulseek-servilo:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
#, fuzzy
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Ligas ligojn al specifa retinterfaco, utila por ekz. certigante ke VPN estas "
"uzata ĉiam. Lasu malplena por uzi ajnan disponeblan interfacon. Ŝanĝu ĉi "
"tiun valoron nur se vi scias, kion vi faras."

#: pynicotine/gtkgui/ui/settings/network.ui:351
#, fuzzy
msgid "Network interface:"
msgstr "Retaj Serĉoj"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
#, fuzzy
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"Nun Ludado ebligas al vi montri tion, kion ludas via plurmedia ludilo uzante "
"la /nun komandon en babilejo."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
#, fuzzy
msgid "Other"
msgstr "Alia"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
#, fuzzy
msgid "Now Playing Format"
msgstr "Nun Ludanta Formato"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
#, fuzzy
msgid "Now Playing message format:"
msgstr "Nun Ludanta mesaĝformato:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
#, fuzzy
msgid "Test Configuration"
msgstr "Testa Agordo"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
#, fuzzy
msgid "Enable plugins"
msgstr "Ebligu kromaĵojn"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
#, fuzzy
msgid "Add Plugins"
msgstr "_Aldonu Kromaĵojn"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
#, fuzzy
msgid "_Add Plugins"
msgstr "_Aldonu Kromaĵojn"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
#, fuzzy
msgid "Settings"
msgstr "Akirante statuson"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
#, fuzzy
msgid "_Settings"
msgstr "Akirante statuson"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
#, fuzzy
msgid "Version:"
msgstr "Versio:"

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
#, fuzzy
msgid "Created by:"
msgstr "Kreita de"

#: pynicotine/gtkgui/ui/settings/search.ui:51
#, fuzzy
msgid "Enable search history"
msgstr "Ebligu serĉhistorion"

#: pynicotine/gtkgui/ui/settings/search.ui:70
#, fuzzy
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Aliaj klientoj povas proponi eblon sendi private kundividitajn dosierojn en "
"respondo al serĉpetoj. Tiaj dosieroj estas antaŭfiksitaj kun '[PRIVATA "
"DOSIERO]', kaj ne estas elŝuteblaj krom se la alŝutanto donas eksplicitan "
"permeson."

#: pynicotine/gtkgui/ui/settings/search.ui:76
#, fuzzy
msgid "Show privately shared files in search results"
msgstr "Montru private kundividitajn dosierojn en serĉrezultoj"

#: pynicotine/gtkgui/ui/settings/search.ui:106
#, fuzzy
msgid "Limit number of results per search:"
msgstr "Limigu nombron da rezultoj por serĉo:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
#, fuzzy
msgid "Result Filter Help"
msgstr "Helpo pri Filtrilo de Rezultoj"

#: pynicotine/gtkgui/ui/settings/search.ui:177
#, fuzzy
msgid "Enable search result filters by default"
msgstr "Ebligu serĉrezultajn filtrilojn defaŭlte"

#: pynicotine/gtkgui/ui/settings/search.ui:211
#, fuzzy
msgid "Include:"
msgstr "Inkluzivi:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
#, fuzzy
msgid "Exclude:"
msgstr "Ekskludi:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
#, fuzzy
msgid "File Type:"
msgstr "Dosiertipo:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
#, fuzzy
msgid "Size:"
msgstr "Grandeco:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
#, fuzzy
msgid "Bitrate:"
msgstr "Bitrapideco:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
#, fuzzy
msgid "Duration:"
msgstr "Daŭro:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
#, fuzzy
msgid "Country Code:"
msgstr "Landokodo:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
#, fuzzy
msgid "Only show results from users with an available upload slot."
msgstr "Montru nur rezultojn de uzantoj kun disponebla alŝuta fendo."

#: pynicotine/gtkgui/ui/settings/search.ui:430
#, fuzzy
msgid "Network Searches"
msgstr "Retaj Serĉoj"

#: pynicotine/gtkgui/ui/settings/search.ui:452
#, fuzzy
msgid "Respond to search requests from other users"
msgstr "Respondu al serĉpetoj de aliaj uzantoj"

#: pynicotine/gtkgui/ui/settings/search.ui:486
#, fuzzy
msgid "Searches shorter than this number of characters will be ignored:"
msgstr "Serĉoj pli mallongaj ol ĉi tiu nombro da signoj estos ignoritaj:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
#, fuzzy
msgid "Maximum search results to send per search request:"
msgstr "Maksimumaj serĉrezultoj por sendi per serĉpeto:"

#: pynicotine/gtkgui/ui/settings/search.ui:578
#, fuzzy
msgid "Clear Search History"
msgstr "Klara Serĉhistorio"

#: pynicotine/gtkgui/ui/settings/search.ui:626
#, fuzzy
msgid "Clear Filter History"
msgstr "Malplenigi Filtrilan Historion"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
#, fuzzy
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Aŭtomate resanigas la enhavon de viaj komunaj dosierujoj dum ekfunkciigo. Se "
"malebligita, viaj akcioj estas ĝisdatigitaj nur kiam vi mane iniciatas "
"resanon."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
#, fuzzy
msgid "Rescan shares on startup"
msgstr "Reskanu akciojn dum ekfunkciigo"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/shares.ui:82
#, fuzzy
msgid "Buddy shares"
msgstr "Kamaradoj"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
#, fuzzy
msgid "Trusted shares"
msgstr "Reskani akciojn"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
#, fuzzy
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr "Autoclear finitaj/nuligitaj alŝutoj de transiga listo"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
#, fuzzy
msgid "Double-click action for uploads:"
msgstr "Duobla alklaku ago por alŝutoj:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
#, fuzzy
msgid "Limit upload speed:"
msgstr "Limigu alŝutan rapidon:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
#, fuzzy
msgid "Per transfer"
msgstr "Per translokigo"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
#, fuzzy
msgid "Total transfers"
msgstr "Totalaj translokigoj"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
#, fuzzy
msgid "Upload Slots"
msgstr "Alŝutu Slots"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
#, fuzzy
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Round Robin: Dosieroj estos alŝutitaj en cikla modo al la uzantoj atendantaj "
"en atendovico.\n"
"First In, First Out: Dosieroj estos alŝutitaj en la ordo en kiu ili estis "
"vicigitaj."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
#, fuzzy
msgid "Upload queue type:"
msgstr "Alŝuta vostotipo:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
#, fuzzy
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr "Vico-alŝutoj se totala transiga rapideco atingas (KiB/s):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
#, fuzzy
msgid "Fixed number of upload slots:"
msgstr "Limigu nombron da alŝutaj fendoj al:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
#, fuzzy
msgid "Prioritize all buddies"
msgstr "Priorigu ĉiujn amikojn"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
#, fuzzy
msgid "Queue Limits"
msgstr "QueueLimoj"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
#, fuzzy
msgid "Maximum number of queued files per user:"
msgstr "Limigu nombron da rezultoj por serĉo:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr ""

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
#, fuzzy
msgid "Limits do not apply to buddies"
msgstr "Vidoviclimoj ne validas por amikoj"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
#, fuzzy
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"Kazoj de $ estos anstataŭigitaj per la ligilo. La defaŭlta retumilo de la "
"sistemo estos uzata en kazoj kie protokolo ne estis agordita."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
#, fuzzy
msgid "File manager command:"
msgstr "Komando de Dosiera Administranto ($ por dosiervojo):"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
#, fuzzy
msgid "Reset Picture"
msgstr "Restarigi Bildon"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
#, fuzzy
msgid "Self Description"
msgstr "Mempriskribo"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
#, fuzzy
msgid "Picture:"
msgstr "Bildo:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
#, fuzzy
msgid "Prefer dark mode"
msgstr "Preferu malhelan reĝimon"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
#, fuzzy
msgid "Use header bar"
msgstr "Uzu _Header Bar"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
#, fuzzy
msgid "Display tray icon"
msgstr "Montru pleto-ikonon"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
#, fuzzy
msgid "Minimize to tray on startup"
msgstr "Minimumu al pleto dum ekfunkciigo"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
#, fuzzy
msgid "Language (requires a restart):"
msgstr "Aŭskultanta portintervalo (postulas rekomencon):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
#, fuzzy
msgid "When closing window:"
msgstr "Fermante Nicotine+:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
#, fuzzy
msgid "Notifications"
msgstr "Sciigoj"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
#, fuzzy
msgid "Enable sound for notifications"
msgstr "Ebligu sonon por sciigaj ŝprucfenestroj"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
#, fuzzy
msgid "Show notification for private chats and mentions in the window title"
msgstr "Montru sciigon por privataj babilejoj kaj mencioj en la fenestrotitolo"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
#, fuzzy
msgid "Show notifications for:"
msgstr "Montru sciigajn ikonojn sur langetoj"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
#, fuzzy
msgid "Finished file downloads"
msgstr "Dosiero elŝutita"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
#, fuzzy
msgid "Finished folder downloads"
msgstr "Dosierujo elŝutita"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
#, fuzzy
msgid "Private messages"
msgstr "Privata mesaĝo de %s"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
#, fuzzy
msgid "Chat room messages"
msgstr "Mesaĝo pri babilejo:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
#, fuzzy
msgid "Chat room mentions"
msgstr "Kompletigo"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
#, fuzzy
msgid "Wishlist results found"
msgstr "Dezirlisto Rezultoj Trovita"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
#, fuzzy
msgid "Restore the previously active main tab at startup"
msgstr "Restarigu antaŭe malfermitajn privatajn babilojn ĉe ekfunkciigo"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
#, fuzzy
msgid "Close-buttons on secondary tabs"
msgstr "Fermaj butonoj sur malĉefaj langetoj"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
#, fuzzy
msgid "Regular tab label color:"
msgstr "Regula etikedkoloro de langeto:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
#, fuzzy
msgid "Changed tab label color:"
msgstr "Ŝanĝita langeta etikedokoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
#, fuzzy
msgid "Highlighted tab label color:"
msgstr "Elstarigita langeta etikedokoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr ""

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
#, fuzzy
msgid "Visible main tabs:"
msgstr "Videblaj ĉefaj langetoj:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
#, fuzzy
msgid "Tab bar positions:"
msgstr "Pozicio de langeto:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
#, fuzzy
msgid "Main tabs"
msgstr "Ĉefaj langetoj"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
#, fuzzy
msgid "Show reverse file paths (requires a restart)"
msgstr ""
"Montri inversajn dosiervojojn en serĉaj kaj translokigaj vidoj (postulas "
"rekomencon)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
#, fuzzy
msgid "Show exact file sizes (requires a restart)"
msgstr "Reta interfaco (postulas rekomencon):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
#, fuzzy
msgid "List text color:"
msgstr "Listo de tekstokoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
#, fuzzy
msgid "Enable colored usernames"
msgstr "Mesaĝo pri babilejo:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
#, fuzzy
msgid "Chat username appearance:"
msgstr "Apero de uzantnomo de babilado:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
#, fuzzy
msgid "Remote text color:"
msgstr "Malproksima tekstokoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
#, fuzzy
msgid "Local text color:"
msgstr "Koloro de loka teksto:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
#, fuzzy
msgid "Command output text color:"
msgstr "Malproksima tekstokoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
#, fuzzy
msgid "/me action text color:"
msgstr "/me ago tekstkoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
#, fuzzy
msgid "Highlighted text color:"
msgstr "Markita tekstokoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
#, fuzzy
msgid "URL link text color:"
msgstr "URL-liga tekstokoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
#, fuzzy
msgid "User Statuses"
msgstr "Usono"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
#, fuzzy
msgid "Online color:"
msgstr "Interreta tekstkoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
#, fuzzy
msgid "Away color:"
msgstr "For tekstkoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
#, fuzzy
msgid "Offline color:"
msgstr "Senreta tekstkoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
#, fuzzy
msgid "Text Entries"
msgstr "Tekstaj Eniroj"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
#, fuzzy
msgid "Text entry background color:"
msgstr "Fonkoloro de enigo de teksto:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
#, fuzzy
msgid "Text entry text color:"
msgstr "Teksta eniga tekstokoloro:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
#, fuzzy
msgid "Fonts"
msgstr "Tiparoj"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
#, fuzzy
msgid "Global font:"
msgstr "Tutmonda tiparo:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
#, fuzzy
msgid "List font:"
msgstr "Listo tiparo:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
#, fuzzy
msgid "Text view font:"
msgstr "Tiparo de tekstvido:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
#, fuzzy
msgid "Chat font:"
msgstr "Babiltiparo:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
#, fuzzy
msgid "Transfers font:"
msgstr "Transdona tiparo:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
#, fuzzy
msgid "Search font:"
msgstr "Serĉtiparo:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
#, fuzzy
msgid "Browse font:"
msgstr "Foliumi tiparon:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
#, fuzzy
msgid "Icons"
msgstr "Ikonoj"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
#, fuzzy
msgid "Icon theme folder:"
msgstr "Nekompleta dosierujo:"

#: pynicotine/gtkgui/ui/uploads.ui:86
#, fuzzy
msgid "Abort User(s)"
msgstr "Ĉesi Uzanton(j)"

#: pynicotine/gtkgui/ui/uploads.ui:116
#, fuzzy
msgid "Ban User(s)"
msgstr "Malpermesu uzanton(j)"

#: pynicotine/gtkgui/ui/uploads.ui:138
#, fuzzy
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Autoclear finitaj/nuligitaj alŝutoj de transiga listo"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
#, fuzzy
msgid "Message All"
msgstr "Mesaĝoj"

#: pynicotine/gtkgui/ui/uploads.ui:199
#, fuzzy
msgid "Clear Specific Uploads"
msgstr "Forigi Ĉiuj Alŝutoj"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
#, fuzzy
msgid "Save Shares List to Disk"
msgstr "_Konservu Liston de Akcioj al Disko"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
#, fuzzy
msgid "Refresh Files"
msgstr "Refreŝigi dosierojn"

#: pynicotine/gtkgui/ui/userinfo.ui:101
#, fuzzy
msgid "Edit Profile"
msgstr "Komunaj Dosierujoj"

#: pynicotine/gtkgui/ui/userinfo.ui:149
#, fuzzy
msgid "Shared Files"
msgstr "Komunaj Dosieroj"

#: pynicotine/gtkgui/ui/userinfo.ui:203
#, fuzzy
msgid "Upload Speed"
msgstr "Alŝuto Rapido"

#: pynicotine/gtkgui/ui/userinfo.ui:230
#, fuzzy
msgid "Free Upload Slots"
msgstr "Senpaga Alŝuto Slots"

#: pynicotine/gtkgui/ui/userinfo.ui:284
#, fuzzy
msgid "Queued Uploads"
msgstr "Envicigitaj Alŝutoj"

#: pynicotine/gtkgui/ui/userinfo.ui:354
#, fuzzy
msgid "Edit Interests"
msgstr "Interesoj"

#: pynicotine/gtkgui/ui/userinfo.ui:624
#, fuzzy
msgid "_Gift Privileges…"
msgstr "_Donacaj Privilegioj…"

#: pynicotine/gtkgui/ui/userinfo.ui:663
#, fuzzy
msgid "_Refresh Profile"
msgstr "Refreŝigi dosierojn"

#: pynicotine/plugins/core_commands/PLUGININFO:3
#, fuzzy
msgid "Nicotine+ Commands"
msgstr "Teamo Nicotine+"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Teamo Nicotine+"

#, fuzzy
#~ msgid "Listening port (requires a restart):"
#~ msgstr "Aŭskultanta portintervalo (postulas rekomencon):"

#, fuzzy
#~ msgid "Network interface (requires a restart):"
#~ msgstr "Aŭskultanta portintervalo (postulas rekomencon):"

#, fuzzy
#~ msgid "Invalid Password"
#~ msgstr "Nevalida Pasvorto"

#, fuzzy
#~ msgid "Change _Login Details"
#~ msgstr "Ŝanĝi Ensalutajn Detalojn"

#, fuzzy, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i privilegiitaj uzantoj"

#, fuzzy
#~ msgid "_Set Up…"
#~ msgstr "_Agordi…"

#, fuzzy
#~ msgid "Queued search result text color:"
#~ msgstr "Envicigita serĉrezulta tekstkoloro:"
