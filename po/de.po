# SPDX-FileCopyrightText: 2003-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2025-05-31 18:02+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/de/>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.12-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr "Soulseek-Client"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Grafischer Client für das Soulseek Peer-to-Peer-Netzwerk"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr "Soulseek;Nicotine;Austausch;Musik;P2P;Peer-to-Peer;GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
msgid "Browse the Soulseek network"
msgstr "Durchsuche das Soulseek-Netzwerk"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr ""
"Nicotine+ ist ein grafischer Client für das Soulseek Peer-to-Peer-Netzwerk."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ zielt darauf ab, eine leichte, angenehme, freie und eine Open "
"Source (FOSS) Alternative zum offiziellen Soulseek-Client zu sein und auch "
"eine umfassende Reihe von Funktionen zu bieten."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
msgid "Search Files"
msgstr "Dateien suchen"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Downloads"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
msgid "Browse Shares"
msgstr "Freigaben durchsuchen"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
msgid "Private Chat"
msgstr "Privater Chat"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Nicotine+ Team"

#: pynicotine/__init__.py:50
#, python-format
msgid "Website: %s"
msgstr "Webseite: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "diese Hilfemeldung anzeigen und beenden"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "Datei"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr "verwende eine nicht standardmäßige Konfigurationsdatei"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "Verzeichnis"

#: pynicotine/__init__.py:64
msgid "alternative directory for user data and plugins"
msgstr "alternatives Verzeichnis für Benutzerdaten und Plugins"

#: pynicotine/__init__.py:68
msgid "start the program without showing window"
msgstr "Starte das Programm, ohne das Fenster anzuzeigen"

#: pynicotine/__init__.py:71
msgid "ip"
msgstr "IP"

#: pynicotine/__init__.py:72
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "Sockets an die gegebene IP binden (nützlich für VPN)"

#: pynicotine/__init__.py:75
msgid "port"
msgstr "Port"

#: pynicotine/__init__.py:76
msgid "listen on the given port"
msgstr "Hör auf den gegebenen Port"

#: pynicotine/__init__.py:80
msgid "rescan shared files"
msgstr "erneut freigegebene Dateien scannen"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr ""
"Starte das Programm im Headless-Modus (keine grafische Benutzeroberfläche)"

#: pynicotine/__init__.py:88
msgid "display version and exit"
msgstr "Version anzeigen und beenden"

#: pynicotine/__init__.py:121
#, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Du verwendest eine nicht unterstützte Version von Python (%(old_version)s).\n"
"Du solltest Python %(min_version)s oder neuer installieren."

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Fehler beim Scannen der Freigaben. Bitte schließe andere Nicotine+-Instanzen "
"und versuche es erneut."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, python-format
msgid "%(user)s is away"
msgstr "%(user)s ist abwesend"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, python-format
msgid "%(user)s is online"
msgstr "%(user)s ist online"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, python-format
msgid "%(user)s is offline"
msgstr "%(user)s ist offline"

#: pynicotine/buddies.py:316
msgid "Buddy Status"
msgstr "Freund Status"

#: pynicotine/chatrooms.py:383
#, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Du wurdest einem privaten Raum hinzugefügt: %(room)s"

#: pynicotine/chatrooms.py:478
#, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr "Chat-Nachricht von Benutzer '%(user)s' im Raum '%(room)s': %(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr ""
"Kann Verzeichnis '%(path)s' nicht erstellen, gemeldeter Fehler: %(error)s"

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr "Fehler beim sichern der Konfiguration: %s"

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr "Konfiguration gesichert in: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Laden von %(program)s %(version)s"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Aufhören mit %(program)s %(version)s, %(status)s …"

#: pynicotine/core.py:246
msgid "terminating"
msgstr "beendend"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "Schließen der Anwendung"

#: pynicotine/core.py:259
#, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Beenden von %(program)s %(version)s!"

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr ""
"Du musst einen Benutzernamen und ein Passwort angeben, bevor du dich "
"verbindest …"

#: pynicotine/downloads.py:239
#, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""
"Fehler: Download-Filter fehlgeschlagen! Überprüfe deine Filter. Grund: %s"

#: pynicotine/downloads.py:254
#, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Fehler: %(num)d Download-Filter fehlgeschlagen! %(error)s "

#: pynicotine/downloads.py:366
#, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s heruntergeladen von %(user)s"

#: pynicotine/downloads.py:370
msgid "File Downloaded"
msgstr "Datei heruntergeladen"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Ausgeführt: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "Die Ausführung von '%(command)s' ist fehlgeschlagen: %(error)s"

#: pynicotine/downloads.py:407
#, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s heruntergeladen von %(user)s"

#: pynicotine/downloads.py:411
msgid "Folder Downloaded"
msgstr "Ordner heruntergeladen"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Ausgeführt im Ordner: %s"

#: pynicotine/downloads.py:443
#, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "Konnte '%(tempfile)s' nicht nach '%(file)s' verschieben: %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
msgid "Download Folder Error"
msgstr "Fehler beim Herunterladen des Ordners"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Herunterladen abgeschlossen: Benutzer %(user)s, Datei %(file)s"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Herunterladen abgebrochen, Benutzer %(user)s Datei %(file)s"

#: pynicotine/downloads.py:1150
#, python-format
msgid "Download I/O error: %s"
msgstr "Download I/O-Fehler: %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Kann keine exklusive Sperre für Datei erhalten - I/O-Fehler: %s"

#: pynicotine/downloads.py:1202
#, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Datei kann nicht in %(folder_path)s gespeichert werden: %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Download gestartet: Benutzer %(user)s, Datei %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, python-format
msgid "Cannot find %s, please install it."
msgstr "%s kann nicht gefunden werden, bitte installiere es."

#: pynicotine/gtkgui/__init__.py:162
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr ""
"Keine grafische Umgebung verfügbar, verwende den Headless-Modus (keine GUI)"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "Verbindung herstellen"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "_Verbindung trennen"

#: pynicotine/gtkgui/application.py:308
msgid "Soulseek _Privileges"
msgstr "Soulseek _Privilegien"

#: pynicotine/gtkgui/application.py:314
msgid "_Preferences"
msgstr "_Einstellungen"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
msgid "_Quit"
msgstr "_Beenden"

#: pynicotine/gtkgui/application.py:337
msgid "Browse _Public Shares"
msgstr "Durchsuche _Öffentliche Freigaben"

#: pynicotine/gtkgui/application.py:338
msgid "Browse _Buddy Shares"
msgstr "Durchsuche _Freund Freigaben"

#: pynicotine/gtkgui/application.py:339
msgid "Browse _Trusted Shares"
msgstr "Durchsuche _Vertrauenswürdige Freigaben"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
msgid "_Rescan Shares"
msgstr "_Freigaben aktualisieren"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
msgid "Configure _Shares"
msgstr "Freigaben konfigurieren"

#: pynicotine/gtkgui/application.py:371
msgid "_Keyboard Shortcuts"
msgstr "_Tastaturkürzel"

#: pynicotine/gtkgui/application.py:372
msgid "_Setup Assistant"
msgstr "_Einrichtungsassistent"

#: pynicotine/gtkgui/application.py:373
msgid "_Transfer Statistics"
msgstr "_Übertragungsstatistiken"

#: pynicotine/gtkgui/application.py:378
msgid "Report a _Bug"
msgstr "Einen _Bug melden"

#: pynicotine/gtkgui/application.py:379
msgid "Improve T_ranslations"
msgstr "Übersetzung _verbessern"

#: pynicotine/gtkgui/application.py:383
msgid "_About Nicotine+"
msgstr "Über _Nicotine+"

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "Datei"

#: pynicotine/gtkgui/application.py:395
msgid "_Shares"
msgstr "_Freigaben"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
msgid "_Help"
msgstr "_Hilfe"

#: pynicotine/gtkgui/application.py:410
msgid "_Browse Shares"
msgstr "_Freigaben durchsuchen"

#: pynicotine/gtkgui/application.py:465
#, python-format
msgid "Unable to show notification: %s"
msgstr "Benachrichtigung kann nicht angezeigt werden: %s"

#: pynicotine/gtkgui/application.py:526
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Du lädst immer noch Dateien hoch. Willst du wirklich beenden?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr "Warte, bis die Uploads fertig sind"

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr "Willst du wirklich beenden?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
msgid "_No"
msgstr "_Nein"

#: pynicotine/gtkgui/application.py:535
msgid "_Run in Background"
msgstr "_Im Hintergrund ausführen"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr "Nicotine+ beenden"

#: pynicotine/gtkgui/application.py:561
msgid "Shares Not Available"
msgstr "Freigaben nicht verfügbar"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Überprüfe, ob externe Festplatten eingebunden sind und die "
"Ordnerberechtigungen korrekt sind."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
msgid "_Cancel"
msgstr "_Abbrechen"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "_Wiederholen"

#: pynicotine/gtkgui/application.py:567
msgid "_Force Rescan"
msgstr "_Neues Scannen erzwingen"

#: pynicotine/gtkgui/application.py:742
msgid "Message Downloading Users"
msgstr "Nachricht an herunterladene Benutzer"

#: pynicotine/gtkgui/application.py:743
msgid "Send private message to all users who are downloading from you:"
msgstr ""
"Sende eine private Nachricht an alle Nutzer, die von dir herunterladen:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
msgid "_Send Message"
msgstr "_Nachricht senden"

#: pynicotine/gtkgui/application.py:756
msgid "Message Buddies"
msgstr "Freunde Nachricht senden"

#: pynicotine/gtkgui/application.py:757
msgid "Send private message to all online buddies:"
msgstr "Sende eine private Nachricht an alle online Freunde:"

#: pynicotine/gtkgui/application.py:786
msgid "Select a Saved Shares List File"
msgstr "Wähle eine gespeicherte Freigabeliste-Datei aus"

#: pynicotine/gtkgui/application.py:877
msgid "Critical Error"
msgstr "Kritischer Fehler"

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ ist auf einen kritischen Fehler gestoßen und muss beendet werden. "
"Bitte kopiere die folgende Nachricht und füge sie in einen Fehlerbericht ein:"

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr "_Nicotine+ beenden"

#: pynicotine/gtkgui/application.py:883
msgid "_Copy & Report Bug"
msgstr "_Kopieren & Fehler melden"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Status"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr "Land"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Benutzer"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Geschwindigkeit"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Dateien"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted"
msgstr "Vertrauenswürdig"

#: pynicotine/gtkgui/buddies.py:108
msgid "Notify"
msgstr "Benachrichtigung"

#: pynicotine/gtkgui/buddies.py:114
msgid "Prioritized"
msgstr "Priorisiert"

#: pynicotine/gtkgui/buddies.py:120
msgid "Last Seen"
msgstr "Zuletzt gesehen"

#: pynicotine/gtkgui/buddies.py:126
msgid "Note"
msgstr "Notiz"

#: pynicotine/gtkgui/buddies.py:143
msgid "Add User _Note…"
msgstr "Benutzer_Notiz hinzufügen …"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Entfernen"

#: pynicotine/gtkgui/buddies.py:364
msgid "Never seen"
msgstr "Noch nie gesehen"

#: pynicotine/gtkgui/buddies.py:529
msgid "Add User Note"
msgstr "Benutzernotiz hinzufügen"

#: pynicotine/gtkgui/buddies.py:530
#, python-format
msgid "Add a note about user %s:"
msgstr "Füge eine Notiz über den Benutzer %s hinzu:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
msgid "_Add"
msgstr "_Hinzufügen"

#: pynicotine/gtkgui/chatrooms.py:224
msgid "Create New Room?"
msgstr "Neuen Raum erstellen?"

#: pynicotine/gtkgui/chatrooms.py:225
#, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Willst du wirklich einen neuen Raum \"%s\" erstellen?"

#: pynicotine/gtkgui/chatrooms.py:226
msgid "Make room private"
msgstr "Mach den Raum privat"

#: pynicotine/gtkgui/chatrooms.py:515
msgid "Search activity log…"
msgstr "Aktivitätsprotokoll durchsuchen …"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
msgid "Search chat log…"
msgstr "Chatprotokoll durchsuchen …"

#: pynicotine/gtkgui/chatrooms.py:597
msgid "Sear_ch User's Files"
msgstr "Benutzerdateien durchsuchen"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
msgid "Find…"
msgstr "Finden …"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Kopieren"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr "Alles kopieren"

#: pynicotine/gtkgui/chatrooms.py:608
msgid "Clear Activity View"
msgstr "Aktivitätsansicht löschen"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
msgid "_Leave Room"
msgstr "_Raum verlassen"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
msgid "Copy Link"
msgstr "URL kopieren"

#: pynicotine/gtkgui/chatrooms.py:624
msgid "View Room Log"
msgstr "Raumprotokoll anzeigen"

#: pynicotine/gtkgui/chatrooms.py:627
msgid "Delete Room Log…"
msgstr "Raumprotokoll löschen …"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
msgid "Clear Message View"
msgstr "Nachrichtenansicht löschen"

#: pynicotine/gtkgui/chatrooms.py:832
#, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s hat dich im Raum %(room)s erwähnt"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "Erwähnt von %(user)s im Raum %(room)s"

#: pynicotine/gtkgui/chatrooms.py:855
#, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "Nachricht von %(user)s im Raum %(room)s"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s ist dem Raum beigetreten"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s hat den Raum verlassen"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s ist weggegangen"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s ist zurück"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
msgid "Delete Logged Messages?"
msgstr "Protokollierte Nachrichten löschen?"

#: pynicotine/gtkgui/chatrooms.py:1197
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"Möchtest du wirklich alle protokollierten Nachrichten für diesen Raum "
"dauerhaft löschen?"

#: pynicotine/gtkgui/dialogs/about.py:405
msgid "About"
msgstr "Über"

#: pynicotine/gtkgui/dialogs/about.py:415
msgid "Website"
msgstr "Webseite"

#: pynicotine/gtkgui/dialogs/about.py:457
#, python-format
msgid "Error checking latest version: %s"
msgstr "Fehler beim Überprüfen der neuesten Version: %s"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr "Neue Version verfügbar: %s"

#: pynicotine/gtkgui/dialogs/about.py:467
msgid "Up to date"
msgstr "Aktuell"

#: pynicotine/gtkgui/dialogs/about.py:496
msgid "Checking latest version…"
msgstr "Neueste Version überprüfen …"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
msgid "Setup Assistant"
msgstr "Einrichtungsassistent"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
msgid "Virtual Folder"
msgstr "Virtueller Ordner"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
msgid "Folder"
msgstr "Ordner"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
msgid "_Previous"
msgstr "_Bisherige"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
msgid "_Finish"
msgstr "_Beenden"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
msgid "_Next"
msgstr "_Nächste"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
msgid "Add a Shared Folder"
msgstr "Einen freigegebenen Ordner hinzufügen"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
msgid "Edit Shared Folder"
msgstr "Freigabe bearbeiten"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Gib einen neuen virtuellen Namen für \"%(dir)s\" ein:"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
msgid "_Edit"
msgstr "_Bearbeiten"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"Benutzer %s existiert bereits und das eingegebene Passwort ist ungültig. "
"Bitte wähle einen anderen Benutzernamen, wenn du dich zum ersten Mal "
"anmeldest."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
msgid "File Properties"
msgstr "Dateieigenschaften"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "Dateieigenschaften (%(num)i von %(total)i / %(size)s / %(length)s)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Dateieigenschaften (%(num)i von %(total)i / %(size)s)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
msgid "_Apply"
msgstr "_Anwenden"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
msgid "Add…"
msgstr "Hinzufügen …"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
msgid "Edit…"
msgstr "Bearbeiten …"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, python-format
msgid "%s Settings"
msgstr "%s Einstellungen"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
msgid "Add Item"
msgstr "Artikel hinzufügen"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
msgid "Edit Item"
msgstr "Artikel bearbeiten"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "Unbekannt"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
msgid "Check Port Status"
msgstr "Portstatus überprüfen"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, Anschluss %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
msgid "Password Change Rejected"
msgstr "Passwortänderung abgelehnt"

#: pynicotine/gtkgui/dialogs/preferences.py:218
msgid "Enter a new password for your Soulseek account:"
msgstr "Gib ein neues Passwort für dein Soulseek-Konto ein:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"Du bist derzeit vom Soulseek-Netzwerk abgemeldet. Wenn du das Passwort eines "
"bestehenden Soulseek-Kontos ändern möchtest, musst du bei diesem Konto "
"angemeldet sein."

#: pynicotine/gtkgui/dialogs/preferences.py:223
msgid "Enter password to use when logging in:"
msgstr "Gib das Passwort ein, das beim Anmelden verwendet werden soll:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
msgid "Change Password"
msgstr "Passwort ändern"

#: pynicotine/gtkgui/dialogs/preferences.py:230
msgid "_Change"
msgstr "_Ändern"

#: pynicotine/gtkgui/dialogs/preferences.py:274
msgid "No one"
msgstr "Niemand"

#: pynicotine/gtkgui/dialogs/preferences.py:275
msgid "Everyone"
msgstr "Jeder"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Freunde"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted buddies"
msgstr "Vertrauenswürdige Freunde"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
msgid "Nothing"
msgstr "Nichts"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
msgid "Open File"
msgstr "Datei öffnen"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
msgid "Open in File Manager"
msgstr "Im Dateimanager öffnen"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Suchen"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
msgid "Pause"
msgstr "Anhalten"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
msgid "Resume"
msgstr "Fortsetzen"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
msgid "Browse Folder"
msgstr "Ordner durchsuchen"

#: pynicotine/gtkgui/dialogs/preferences.py:317
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Syntax</b>: Groß- und Kleinschreibung wird nicht berücksichtigt. Wenn "
"diese Option aktiviert ist, können Python-Reguläre Ausdrücke eingesetzt "
"werden – andernfalls werden lediglich Platzhalter-Übereinstimmungen mit * "
"unterstützt."

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr "Filter"

#: pynicotine/gtkgui/dialogs/preferences.py:334
msgid "Regex"
msgstr "Regex (regulärer Ausdruck)"

#: pynicotine/gtkgui/dialogs/preferences.py:468
msgid "Add Download Filter"
msgstr "Download-Filter hinzufügen"

#: pynicotine/gtkgui/dialogs/preferences.py:469
msgid "Enter a new download filter:"
msgstr "Gib einen neuen Download-Filter ein:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
msgid "Enable regular expressions"
msgstr "Reguläre Ausdrücke aktivieren"

#: pynicotine/gtkgui/dialogs/preferences.py:498
msgid "Edit Download Filter"
msgstr "Download-Filter bearbeiten"

#: pynicotine/gtkgui/dialogs/preferences.py:499
msgid "Modify the following download filter:"
msgstr "Ändere den folgenden Download-Filter:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d Fehlgeschlagen! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
msgid "Filters Successful"
msgstr "Filter Erfolgreich"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr "Öffentlich"

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr "Erreichbar für"

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "Abbrechen"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
msgid "Retry"
msgstr "Wiederholen"

#: pynicotine/gtkgui/dialogs/preferences.py:828
msgid "Round Robin"
msgstr "Rundlaufverfahren (Round Robin)"

#: pynicotine/gtkgui/dialogs/preferences.py:829
msgid "First In, First Out"
msgstr "Zuerst rein, zuerst raus (FIFO - First In, First Out)"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
msgid "Username"
msgstr "Benutzername"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
msgid "IP Address"
msgstr "IP-Adresse"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
msgid "Ignore User"
msgstr "Benutzer ignorieren"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
msgid "Enter the name of the user you want to ignore:"
msgstr "Gib den Namen des Benutzers ein, den du ignorieren möchtest:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
msgid "Ignore IP Address"
msgstr "IP-Adresse ignorieren"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
msgid "Enter an IP address you want to ignore:"
msgstr "Gib eine IP-Adresse ein, die du ignorieren möchtest:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "* is a wildcard"
msgstr "* ist ein Platzhalter"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
msgid "Ban User"
msgstr "Benutzer blockieren"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
msgid "Enter the name of the user you want to ban:"
msgstr "Gib den Namen des Benutzers ein, den du blockieren möchtest:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
msgid "Ban IP Address"
msgstr "IP-Adresse blockieren"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "Enter an IP address you want to ban:"
msgstr "Gib eine IP-Adresse ein, die du blockieren möchtest:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr "Formatierungscodes"

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
msgid "Pattern"
msgstr "Muster"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
msgid "Replacement"
msgstr "Ersatz"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
msgid "Censor Pattern"
msgstr "Zensurmuster"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Gib ein Muster ein, das du zensieren möchtest. Füge Leerzeichen um das "
"Muster ein, wenn du nicht möchtest, dass Zeichenketten innerhalb von Wörtern "
"übereinstimmen (kann am Anfang und Ende von Zeilen fehlschlagen)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
msgid "Edit Censored Pattern"
msgstr "Zensiertes Muster bearbeiten"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
msgid "Add Replacement"
msgstr "Ersatz hinzufügen"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
msgid "Enter a text pattern and what to replace it with:"
msgstr "Gib ein Textmuster und das Ersetzungsmuster ein:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
msgid "Edit Replacement"
msgstr "Ersatz bearbeiten"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
msgid "System default"
msgstr "Systemvoreinstellung"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
msgid "Show confirmation dialog"
msgstr "Bestätigungsdialog anzeigen"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
msgid "Run in the background"
msgstr "Im Hintergrund ausführen"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
msgid "bold"
msgstr "fett"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
msgid "italic"
msgstr "kursiv"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
msgid "underline"
msgstr "unterstrichen"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
msgid "normal"
msgstr "normal"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
msgid "Separate Buddies tab"
msgstr "Separater Freunde-Tab"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
msgid "Sidebar in Chat Rooms tab"
msgstr "Seitenleiste im Chaträume-Tab"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr "Immer sichtbare Seitenleiste"

#: pynicotine/gtkgui/dialogs/preferences.py:1777
msgid "Top"
msgstr "Oben"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
msgid "Bottom"
msgstr "Unten"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
msgid "Left"
msgstr "Links"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
msgid "Right"
msgstr "Rechts"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "Online"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Abwesend"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Offline"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
msgid "Tab Changed"
msgstr "Tab geändert"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
msgid "Tab Highlight"
msgstr "Tab-Hervorhebung"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
msgid "Window"
msgstr "Fenster"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
msgid "Online (Tray)"
msgstr "Online (Fach)"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
msgid "Away (Tray)"
msgstr "Abwesend (Fach)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
msgid "Offline (Tray)"
msgstr "Offline (Fach)"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
msgid "Message (Tray)"
msgstr "Nachricht (Fach)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Protokoll"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
msgid "Command"
msgstr "Befehl"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
msgid "Add URL Handler"
msgstr "URL-Handler hinzufügen"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Gib das Protokoll und den Befehl für den URL-Handler ein:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
msgid "Edit Command"
msgstr "Befehl bearbeiten"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Gib einen neuen Befehl für das Protokoll %s ein:"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
msgid "Username;APIKEY"
msgstr "Benutzername;APIKEY"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr ""
"Musikplayer (z. B. Amarok, Audacious, Exaile); leer lassen, um die "
"automatische Erkennung zu ermöglichen:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
msgid "Username: "
msgstr "Benutzername: "

#: pynicotine/gtkgui/dialogs/preferences.py:2767
msgid "Command:"
msgstr "Befehl:"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
msgid "Title"
msgstr "Titel"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Jetzt läuft (typischerweise \"%(artist)s - %(title)s\")"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
msgid "Artist"
msgstr "Künstler"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
msgid "Duration"
msgstr "Dauer"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "Bitrate"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
msgid "Comment"
msgstr "Kommentar"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
msgid "Album"
msgstr "Album"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
msgid "Track Number"
msgstr "Titelnummer"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
msgid "Year"
msgstr "Jahr"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
msgid "Filename (URI)"
msgstr "Dateiname (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
msgid "Program"
msgstr "Programm"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
msgid "Enabled"
msgstr "Aktiviert"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
msgid "Plugin"
msgstr "Plugin"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
msgid "No Plugin Selected"
msgstr "Kein Plugin ausgewählt"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
msgid "Preferences"
msgstr "Präferenzen"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
msgid "Network"
msgstr "Netzwerk"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
msgid "User Interface"
msgstr "Benutzeroberfläche"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Freigaben"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Uploads"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Suche"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
msgid "User Profile"
msgstr "Benutzerprofil"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
msgid "Chats"
msgstr "Chats"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
msgid "Now Playing"
msgstr "Jetzt läuft"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Protokollierung"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
msgid "Banned Users"
msgstr "Gesperrte Benutzer"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
msgid "Ignored Users"
msgstr "Ignorierte Benutzer"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
msgid "URL Handlers"
msgstr "URL-Handler"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr "Plugins"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
msgid "Pick a File Name for Config Backup"
msgstr "Wähle einen Dateinamen für das Konfigurations-Backup"

#: pynicotine/gtkgui/dialogs/statistics.py:75
msgid "Transfer Statistics"
msgstr "Transferstatistik"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, python-format
msgid "Total Since %(date)s"
msgstr "Gesamt seit %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
msgid "Reset Transfer Statistics?"
msgstr "Übertragungsstatistiken zurücksetzen?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
msgid "Do you really want to reset transfer statistics?"
msgstr "Willst du wirklich die Übertragungsstatistiken zurücksetzen?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
msgid "Wishlist"
msgstr "Wunschliste"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
msgid "Wish"
msgstr "Wunsch"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
msgid "_Search for Item"
msgstr "_Suche Eintrag"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
msgid "Edit Wish"
msgstr "Wunsch bearbeiten"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, python-format
msgid "Enter new value for wish '%s':"
msgstr "Neuen Wert für den Wunsch '%s' eingeben:"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
msgid "Clear Wishlist?"
msgstr "Wunschliste löschen?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
msgid "Do you really want to clear your wishlist?"
msgstr "Möchtest du wirklich deine Wunschliste löschen?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Pfad"

#: pynicotine/gtkgui/downloads.py:47
msgid "_Resume"
msgstr "_Fortsetzen"

#: pynicotine/gtkgui/downloads.py:48
msgid "P_ause"
msgstr "P_ause"

#: pynicotine/gtkgui/downloads.py:72
msgid "Finished / Filtered"
msgstr "Fertig / Gefiltert"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Abgeschlossen"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
msgid "Paused"
msgstr "Pausiert"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
msgid "Filtered"
msgstr "Gefiltert"

#: pynicotine/gtkgui/downloads.py:77
msgid "Deleted"
msgstr "Gelöscht"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
msgid "Queued…"
msgstr "In der Warteschlange …"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
msgid "Everything…"
msgstr "Alles …"

#: pynicotine/gtkgui/downloads.py:132
#, python-format
msgid "Downloads: %(speed)s"
msgstr "Herunterladen: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
msgid "Clear Queued Downloads"
msgstr "Lösche wartende Downloads"

#: pynicotine/gtkgui/downloads.py:139
msgid "Do you really want to clear all queued downloads?"
msgstr "Willst du wirklich alle Downloads in der Warteschlange löschen?"

#: pynicotine/gtkgui/downloads.py:151
msgid "Clear All Downloads"
msgstr "Alle Downloads löschen"

#: pynicotine/gtkgui/downloads.py:152
msgid "Do you really want to clear all downloads?"
msgstr "Willst du wirklich alle Downloads löschen?"

#: pynicotine/gtkgui/downloads.py:169
#, python-format
msgid "Download %(num)i files?"
msgstr "%(num)i Dateien herunterladen?"

#: pynicotine/gtkgui/downloads.py:170
#, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"Willst du wirklich %(num)i Dateien aus %(user)s Ordner %(folder)s "
"herunterladen?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
msgid "_Download Folder"
msgstr "_Download-Ordner"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
msgid "Likes"
msgstr "Gefällt mir"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
msgid "Dislikes"
msgstr "Gefällt mir nicht"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Bewertung"

#: pynicotine/gtkgui/interests.py:111
msgid "Item"
msgstr "Artikel"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
msgid "_Recommendations for Item"
msgstr "_Empfehlungen für Artikel"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
msgid "I _Like This"
msgstr "Mir _gefällt das"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
msgid "I _Dislike This"
msgstr "Ich mag das nicht"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
msgid "Recommendations"
msgstr "Empfehlungen"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
msgid "Similar Users"
msgstr "Ähnliche Benutzer"

#: pynicotine/gtkgui/interests.py:427
#, python-format
msgid "Recommendations (%s)"
msgstr "Empfehlungen (%s)"

#: pynicotine/gtkgui/interests.py:451
#, python-format
msgid "Similar Users (%s)"
msgstr "Ähnliche Benutzer (%s)"

#: pynicotine/gtkgui/mainwindow.py:238
msgid "Search log…"
msgstr "Suchprotokoll …"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, python-format
msgid "Private Message from %(user)s"
msgstr "Private Nachricht von %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
msgid "Wishlist Results Found"
msgstr "Wunschliste Ergebnisse gefunden"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
msgid "User Profiles"
msgstr "Benutzerprofile"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
msgid "Chat Rooms"
msgstr "Chaträume"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "Interessen"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
msgid "Chat"
msgstr "Chat"

#: pynicotine/gtkgui/mainwindow.py:1111
msgid "[Debug] Connections"
msgstr "[Debug] Verbindungen"

#: pynicotine/gtkgui/mainwindow.py:1112
msgid "[Debug] Messages"
msgstr "[Debug] Meldungen"

#: pynicotine/gtkgui/mainwindow.py:1113
msgid "[Debug] Transfers"
msgstr "[Debug] Übertragungen"

#: pynicotine/gtkgui/mainwindow.py:1114
msgid "[Debug] Miscellaneous"
msgstr "[Debug] Sonstiges"

#: pynicotine/gtkgui/mainwindow.py:1119
msgid "_Find…"
msgstr "_Finden …"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
msgid "_Copy"
msgstr "_Kopieren"

#: pynicotine/gtkgui/mainwindow.py:1122
msgid "Copy _All"
msgstr "_Alles kopieren"

#: pynicotine/gtkgui/mainwindow.py:1127
msgid "View _Debug Logs"
msgstr "Debug-Protokolle anzeigen"

#: pynicotine/gtkgui/mainwindow.py:1128
msgid "View _Transfer Logs"
msgstr "Übertragungsprotokoll anzeigen"

#: pynicotine/gtkgui/mainwindow.py:1132
msgid "_Log Categories"
msgstr "_Protokollkategorien"

#: pynicotine/gtkgui/mainwindow.py:1134
msgid "Clear Log View"
msgstr "Protokollansicht löschen"

#: pynicotine/gtkgui/mainwindow.py:1199
msgid "Preparing Shares"
msgstr "Freigaben vorbereiten"

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr "Aktualisierung der Freigaben"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
msgid "Shared Folders"
msgstr "Freigegebene Ordner"

#: pynicotine/gtkgui/popovers/chathistory.py:77
msgid "Latest Message"
msgstr "Neueste Nachricht"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Raum"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Nutzer"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
msgid "Join Room"
msgstr "Raum beitreten"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
msgid "Leave Room"
msgstr "_Raum verlassen"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
msgid "Disown Private Room"
msgstr "Privaten Raum aufgeben"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
msgid "Cancel Room Membership"
msgstr "Raummitgliedschaft kündigen"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
msgid "Close All Tabs…"
msgstr "Alle Tabs schließen …"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
msgid "_Close Tab"
msgstr "Tab schließen"

#: pynicotine/gtkgui/privatechat.py:379
msgid "View Chat Log"
msgstr "Chat-Protokoll anzeigen"

#: pynicotine/gtkgui/privatechat.py:382
msgid "Delete Chat Log…"
msgstr "Chatprotokoll löschen …"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
msgid "User Actions"
msgstr "Benutzeraktionen"

#: pynicotine/gtkgui/privatechat.py:477
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Möchtest du wirklich alle protokollierten Nachrichten für diesen Benutzer "
"dauerhaft löschen?"

#: pynicotine/gtkgui/privatechat.py:528
msgid "* Messages sent while you were offline"
msgstr "* Nachrichten, die gesendet wurden, während du offline warst"

#: pynicotine/gtkgui/search.py:90
msgid "_Global"
msgstr "_Global"

#: pynicotine/gtkgui/search.py:91
msgid "_Buddies"
msgstr "_Freunde"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
msgid "_Rooms"
msgstr "_Räume"

#: pynicotine/gtkgui/search.py:93
msgid "_User"
msgstr "_Benutzer"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
msgid "In Queue"
msgstr "In der Warteschlange"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
msgid "File Type"
msgstr "Dateityp"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Dateiname"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Größe"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
msgid "Quality"
msgstr "Qualität"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
msgid "Copy _File Path"
msgstr "_Dateipfad kopieren"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "URL kopieren"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
msgid "Copy Folder U_RL"
msgstr "Ordner U_RL kopieren"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
msgid "_Download File(s)"
msgstr "Datei(en) herunterladen"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
msgid "Download File(s) _To…"
msgstr "Datei(en) herunterladen _in …"

#: pynicotine/gtkgui/search.py:615
msgid "Download _Folder(s)"
msgstr "_Ordner herunterladen"

#: pynicotine/gtkgui/search.py:616
msgid "Download F_older(s) To…"
msgstr "Datei(en) herunterladen zu …"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
msgid "View User _Profile"
msgstr "Benutzer _Profil anzeigen"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
msgid "_Browse Folder"
msgstr "_Ordner durchsuchen"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
msgid "F_ile Properties"
msgstr "_Dateieigenschaften"

#: pynicotine/gtkgui/search.py:629
msgid "Copy Search Term"
msgstr "Suchbegriff kopieren"

#: pynicotine/gtkgui/search.py:631
msgid "Clear All Results"
msgstr "Alle Ergebnisse löschen"

#: pynicotine/gtkgui/search.py:718
msgid "Clear Filters"
msgstr "Filter löschen"

#: pynicotine/gtkgui/search.py:721
msgid "Restore Filters"
msgstr "Filter wiederherstellen"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, python-format
msgid "[PRIVATE]  %s"
msgstr "[PRIVAT]  %s"

#: pynicotine/gtkgui/search.py:1273
#, python-format
msgid "_Result Filters [%d]"
msgstr "_Ergebnisfilter [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
msgid "_Result Filters"
msgstr "_Ergebnisfilter"

#: pynicotine/gtkgui/search.py:1277
#, python-format
msgid "%d active filter(s)"
msgstr "%d aktive Filter"

#: pynicotine/gtkgui/search.py:1329
msgid "Add Wi_sh"
msgstr "_Wunsch hinzufügen"

#: pynicotine/gtkgui/search.py:1332
msgid "Remove Wi_sh"
msgstr "_Wunsch entfernen"

#: pynicotine/gtkgui/search.py:1349
msgid "Select User's Results"
msgstr "Ergebnisse des Benutzers auswählen"

#: pynicotine/gtkgui/search.py:1472
#, python-format
msgid "Total: %s"
msgstr "Insgesamt %s"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
msgid "Results"
msgstr "Ergebnisse"

#: pynicotine/gtkgui/search.py:1590
msgid "Select Destination Folder for File(s)"
msgstr "Wähle Zielordner für Datei(en) aus"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
msgid "Select Destination Folder"
msgstr "Zielordner auswählen"

#: pynicotine/gtkgui/transfers.py:61
msgid "Queued"
msgstr "In Warteschlange"

#: pynicotine/gtkgui/transfers.py:62
msgid "Queued (prioritized)"
msgstr "In Warteschlange (priorisiert)"

#: pynicotine/gtkgui/transfers.py:63
msgid "Queued (privileged)"
msgstr "In Warteschlange (privilegiert)"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Status ermitteln"

#: pynicotine/gtkgui/transfers.py:65
msgid "Transferring"
msgstr "Übertragen"

#: pynicotine/gtkgui/transfers.py:66
msgid "Connection closed"
msgstr "Verbindung getrennt"

#: pynicotine/gtkgui/transfers.py:67
msgid "Connection timeout"
msgstr "Verbindungszeitüberschreitung"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Benutzer abgemeldet"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
msgid "Cancelled"
msgstr "Abgebrochen"

#: pynicotine/gtkgui/transfers.py:73
msgid "Download folder error"
msgstr "Fehler beim Herunterladen des Ordners"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Lokaler Dateifehler"

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr "Gesperrt"

#: pynicotine/gtkgui/transfers.py:76
msgid "File not shared"
msgstr "Datei nicht freigegeben"

#: pynicotine/gtkgui/transfers.py:77
msgid "Pending shutdown"
msgstr "Ausstehendes beenden"

#: pynicotine/gtkgui/transfers.py:78
msgid "File read error"
msgstr "Fehler beim Lesen der Datei"

#: pynicotine/gtkgui/transfers.py:182
msgid "Queue"
msgstr "Warteschlange"

#: pynicotine/gtkgui/transfers.py:188
msgid "Percent"
msgstr "Prozent"

#: pynicotine/gtkgui/transfers.py:208
msgid "Time Elapsed"
msgstr "Zeit vergangen"

#: pynicotine/gtkgui/transfers.py:215
msgid "Time Left"
msgstr "Zeit verbleibend"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
msgid "_Open File"
msgstr "Datei _öffnen"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
msgid "Open in File _Manager"
msgstr "Im Datei-Manager öffnen"

#: pynicotine/gtkgui/transfers.py:286
msgid "_Search"
msgstr "Suchen"

#: pynicotine/gtkgui/transfers.py:289
msgid "Clear All"
msgstr "Alles löschen"

#: pynicotine/gtkgui/transfers.py:953
msgid "Select User's Transfers"
msgstr "Benutzerübertragungen auswählen"

#: pynicotine/gtkgui/uploads.py:50
msgid "_Abort"
msgstr "_Abbrechen"

#: pynicotine/gtkgui/uploads.py:74
msgid "Finished / Cancelled / Failed"
msgstr "Fertig / Abgebrochen / Fehlgeschlagen"

#: pynicotine/gtkgui/uploads.py:75
msgid "Finished / Cancelled"
msgstr "Fertig / Abgebrochen"

#: pynicotine/gtkgui/uploads.py:79
msgid "Failed"
msgstr "Fehlgeschlagen"

#: pynicotine/gtkgui/uploads.py:80
msgid "User Logged Off"
msgstr "Benutzer abgemeldet"

#: pynicotine/gtkgui/uploads.py:142
#, python-format
msgid "Uploads: %(speed)s"
msgstr "Uploads: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr "Beenden …"

#: pynicotine/gtkgui/uploads.py:164
msgid "Clear Queued Uploads"
msgstr "Warteschlange für Uploads löschen"

#: pynicotine/gtkgui/uploads.py:165
msgid "Do you really want to clear all queued uploads?"
msgstr "Willst du wirklich alle wartenden Uploads löschen?"

#: pynicotine/gtkgui/uploads.py:177
msgid "Clear All Uploads"
msgstr "Alle Uploads löschen"

#: pynicotine/gtkgui/uploads.py:178
msgid "Do you really want to clear all uploads?"
msgstr "Willst du wirklich alle Uploads löschen?"

#: pynicotine/gtkgui/userbrowse.py:282
msgid "_Save Shares List to Disk"
msgstr "_Speichere Freigabeliste auf Festplatte"

#: pynicotine/gtkgui/userbrowse.py:292
msgid "Upload Folder & Subfolders…"
msgstr "Ordner & Unterordner hochladen …"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
msgid "Copy _Folder Path"
msgstr "_Ordnerpfad kopieren"

#: pynicotine/gtkgui/userbrowse.py:309
msgid "_Download Folder & Subfolders"
msgstr "_Ordner & Unterordner herunterladen"

#: pynicotine/gtkgui/userbrowse.py:310
msgid "Download Folder & Subfolders _To…"
msgstr "Ordner & Unterordner herunterladen _in …"

#: pynicotine/gtkgui/userbrowse.py:334
msgid "File Name"
msgstr "Dateiname"

#: pynicotine/gtkgui/userbrowse.py:373
msgid "Up_load File(s)…"
msgstr "_Datei(en) hochladen …"

#: pynicotine/gtkgui/userbrowse.py:374
msgid "Upload Folder…"
msgstr "Ordner hochladen …"

#: pynicotine/gtkgui/userbrowse.py:396
msgid "Download Folder _To…"
msgstr "Ordner herunterladen _in …"

#: pynicotine/gtkgui/userbrowse.py:600
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"Die Liste der vom Benutzer freigegebenen Dateien ist leer. Entweder hat der "
"Benutzer nichts freigegeben oder er teilt die Dateien ausschließlich privat."

#: pynicotine/gtkgui/userbrowse.py:615
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Die freigegebene Dateien können nicht vom Benutzer angefordert werden. "
"Entweder ist der Benutzer offline, die zuhörenden Ports sind auf beiden "
"Seiten geschlossen oder es liegt ein vorübergehendes Verbindungsproblem vor."

#: pynicotine/gtkgui/userbrowse.py:953
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Wähle das Zielverzeichnis für den Download mehrerer Ordner aus"

#: pynicotine/gtkgui/userbrowse.py:997
msgid "Upload Folder (with Subfolders) To User"
msgstr "Ordner (mit Unterordnern) zum Benutzer hochladen"

#: pynicotine/gtkgui/userbrowse.py:999
msgid "Upload Folder To User"
msgstr "Ordner zum Benutzer hochladen"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
msgid "Enter the name of the user you want to upload to:"
msgstr "Gib den Namen des Benutzers ein, zu dem du hochladen möchtest:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
msgid "_Upload"
msgstr "_Hochladen"

#: pynicotine/gtkgui/userbrowse.py:1139
msgid "Select Destination Folder for Files"
msgstr "Wähle Zielordner für Dateien aus"

#: pynicotine/gtkgui/userbrowse.py:1161
msgid "Upload File(s) To User"
msgstr "Datei(en) an Benutzer hochladen"

#: pynicotine/gtkgui/userinfo.py:376
msgid "Copy Picture"
msgstr "Bild kopieren"

#: pynicotine/gtkgui/userinfo.py:377
msgid "Save Picture"
msgstr "Bild speichern"

#: pynicotine/gtkgui/userinfo.py:468
#, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Fehler beim Laden des Bildes für Benutzer %(user)s: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Informationen vom Benutzer können nicht angefordert werden. Entweder sind "
"auf beiden Seiten die zuhörenden Ports geschlossen, der Benutzer ist offline "
"oder es liegt ein vorübergehendes Konnektivitätsproblem vor."

#: pynicotine/gtkgui/userinfo.py:577
msgid "Remove _Buddy"
msgstr "_Freund entfernen"

#: pynicotine/gtkgui/userinfo.py:577
msgid "Add _Buddy"
msgstr "_Freund hinzufügen"

#: pynicotine/gtkgui/userinfo.py:581
msgid "Unban User"
msgstr "Benutzer Blockierung aufheben"

#: pynicotine/gtkgui/userinfo.py:585
msgid "Unignore User"
msgstr "Benutzer nicht ignorieren"

#: pynicotine/gtkgui/userinfo.py:613
msgid "Yes"
msgstr "Ja"

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr "Nein"

#: pynicotine/gtkgui/userinfo.py:773
msgid "Please enter number of days."
msgstr "Bitte gib die Anzahl der Tage ein."

#: pynicotine/gtkgui/userinfo.py:787
#, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Verschenke Tage deiner Soulseek-Privilegien an den Benutzer %(user)s "
"(%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, python-format
msgid "%(days)s days left"
msgstr "%(days)s Tage übrig"

#: pynicotine/gtkgui/userinfo.py:795
msgid "Gift Privileges"
msgstr "Privilegien schenken"

#: pynicotine/gtkgui/userinfo.py:797
msgid "_Give Privileges"
msgstr "Privilegien _geben"

#: pynicotine/gtkgui/widgets/dialogs.py:309
msgid "Close"
msgstr "Schließen"

#: pynicotine/gtkgui/widgets/dialogs.py:488
msgid "_Yes"
msgstr "_Ja"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
msgid "_OK"
msgstr "_OK"

#: pynicotine/gtkgui/widgets/filechooser.py:39
msgid "Select a File"
msgstr "Wähle eine Datei"

#: pynicotine/gtkgui/widgets/filechooser.py:174
msgid "Select a Folder"
msgstr "Wähle einen Ordner"

#: pynicotine/gtkgui/widgets/filechooser.py:179
msgid "_Select"
msgstr "_Auswählen"

#: pynicotine/gtkgui/widgets/filechooser.py:196
msgid "Select an Image"
msgstr "Ein Bild auswählen"

#: pynicotine/gtkgui/widgets/filechooser.py:203
msgid "All images"
msgstr "Alle Bilder"

#: pynicotine/gtkgui/widgets/filechooser.py:241
msgid "Save as…"
msgstr "Speichern unter …"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
msgid "(None)"
msgstr "(Keine)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
msgid "Close Tab"
msgstr "Tab schließen"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
msgid "Close All Tabs?"
msgstr "Alle Tabs schließen?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
msgid "Do you really want to close all tabs?"
msgstr "Willst du wirklich alle Tabs schließen?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, python-format
msgid "%i Unread Tab(s)"
msgstr "%i Ungelesene Tab(s)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
msgid "All Tabs"
msgstr "Alle Tabs"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
msgid "Re_open Closed Tab"
msgstr "Geschlossenen Tab _wieder öffnen"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, python-format
msgid "%s File(s) Selected"
msgstr "%s Ausgewählte Datei(en)"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
msgid "_Browse Files"
msgstr "_Dateien durchsuchen"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
msgid "_Add Buddy"
msgstr "Freund _hinzufügen"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
msgid "Show IP A_ddress"
msgstr "IP-A_dresse anzeigen"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
msgid "Private Rooms"
msgstr "Private Räume"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, python-format
msgid "Remove from Private Room %s"
msgstr "Aus dem privaten Raum entfernen %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, python-format
msgid "Add to Private Room %s"
msgstr "Zum privaten Raum hinzufügen %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, python-format
msgid "Remove as Operator of %s"
msgstr "Als Operator von %s entfernen"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, python-format
msgid "Add as Operator of %s"
msgstr "Als Operator von %s hinzufügen"

#: pynicotine/gtkgui/widgets/textentry.py:37
msgid "Send message…"
msgstr "Nachricht senden …"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
msgid "Find Previous Match"
msgstr "Vorherige Treffer finden"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
msgid "Find Next Match"
msgstr "Nächsten Treffer finden"

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr "--- alte Nachrichten oben ---"

#: pynicotine/gtkgui/widgets/theme.py:243
msgid "Executable"
msgstr "Ausführbare Datei"

#: pynicotine/gtkgui/widgets/theme.py:244
msgid "Audio"
msgstr "Audio"

#: pynicotine/gtkgui/widgets/theme.py:245
msgid "Image"
msgstr "Bild"

#: pynicotine/gtkgui/widgets/theme.py:246
msgid "Archive"
msgstr "Archiv"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
msgid "Miscellaneous"
msgstr "Sonstiges"

#: pynicotine/gtkgui/widgets/theme.py:248
msgid "Video"
msgstr "Video"

#: pynicotine/gtkgui/widgets/theme.py:249
msgid "Document"
msgstr "Dokument"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr "Text"

#: pynicotine/gtkgui/widgets/theme.py:357
#, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "Fehler beim Laden des benutzerdefinierten Symbols %(path)s: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Hide Nicotine+"
msgstr "Nicotine+ ausblenden"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Show Nicotine+"
msgstr "Nicotine+ anzeigen"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, python-format
msgid "Column #%i"
msgstr "Spalte #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
msgid "Ungrouped"
msgstr "Nicht gruppiert"

#: pynicotine/gtkgui/widgets/treeview.py:960
msgid "Group by Folder"
msgstr "Nach Ordner gruppieren"

#: pynicotine/gtkgui/widgets/treeview.py:963
msgid "Group by User"
msgstr "Nach Benutzer gruppieren"

#: pynicotine/headless/application.py:77
#, python-format
msgid "Do you really want to exit? %s"
msgstr "Willst du wirklich beenden? %s"

#: pynicotine/headless/application.py:81
#, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr ""
"Benutzer %s existiert bereits, und das eingegebene Passwort ist ungültig."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr ""
"Gib %s ein, um dich mit einem anderen Benutzernamen oder Passwort anzumelden."

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
msgid "Password: "
msgstr "Passwort: "

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Um ein neues Soulseek-Konto zu erstellen, gib deinen gewünschten "
"Benutzernamen und dein Passwort ein. Wenn du schon ein Konto hast, benutze "
"deine vorhandenen Anmeldedaten."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr "Die folgenden Freigaben sind nicht verfügbar:"

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr "Scannen erneut versuchen? %s"

#: pynicotine/logfacility.py:181
#, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr ""
"Konnte nicht in die Protokolldatei \"%(filename)s\" schreiben: %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Kann auf Logdatei %(path)s nicht zugreifen: %(error)s"

#: pynicotine/networkfilter.py:40
msgid "Andorra"
msgstr "Andorra"

#: pynicotine/networkfilter.py:41
msgid "United Arab Emirates"
msgstr "Vereinigte Arabische Emirate"

#: pynicotine/networkfilter.py:42
msgid "Afghanistan"
msgstr "Afghanistan"

#: pynicotine/networkfilter.py:43
msgid "Antigua & Barbuda"
msgstr "Antigua und Barbuda"

#: pynicotine/networkfilter.py:44
msgid "Anguilla"
msgstr "Anguilla"

#: pynicotine/networkfilter.py:45
msgid "Albania"
msgstr "Albanien"

#: pynicotine/networkfilter.py:46
msgid "Armenia"
msgstr "Armenien"

#: pynicotine/networkfilter.py:47
msgid "Angola"
msgstr "Angola"

#: pynicotine/networkfilter.py:48
msgid "Antarctica"
msgstr "Antarktis"

#: pynicotine/networkfilter.py:49
msgid "Argentina"
msgstr "Argentinien"

#: pynicotine/networkfilter.py:50
msgid "American Samoa"
msgstr "Amerikanisch-Samoa"

#: pynicotine/networkfilter.py:51
msgid "Austria"
msgstr "Österreich"

#: pynicotine/networkfilter.py:52
msgid "Australia"
msgstr "Australien"

#: pynicotine/networkfilter.py:53
msgid "Aruba"
msgstr "Aruba"

#: pynicotine/networkfilter.py:54
msgid "Åland Islands"
msgstr "Åland-Inseln"

#: pynicotine/networkfilter.py:55
msgid "Azerbaijan"
msgstr "Aserbaidschan"

#: pynicotine/networkfilter.py:56
msgid "Bosnia & Herzegovina"
msgstr "Bosnien und Herzegowina"

#: pynicotine/networkfilter.py:57
msgid "Barbados"
msgstr "Barbados"

#: pynicotine/networkfilter.py:58
msgid "Bangladesh"
msgstr "Bangladesch"

#: pynicotine/networkfilter.py:59
msgid "Belgium"
msgstr "Belgien"

#: pynicotine/networkfilter.py:60
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: pynicotine/networkfilter.py:61
msgid "Bulgaria"
msgstr "Bulgarien"

#: pynicotine/networkfilter.py:62
msgid "Bahrain"
msgstr "Bahrain"

#: pynicotine/networkfilter.py:63
msgid "Burundi"
msgstr "Burundi"

#: pynicotine/networkfilter.py:64
msgid "Benin"
msgstr "Benin"

#: pynicotine/networkfilter.py:65
msgid "Saint Barthelemy"
msgstr "St. Barthelemy"

#: pynicotine/networkfilter.py:66
msgid "Bermuda"
msgstr "Bermuda"

#: pynicotine/networkfilter.py:67
msgid "Brunei Darussalam"
msgstr "Brunei Darussalam"

#: pynicotine/networkfilter.py:68
msgid "Bolivia"
msgstr "Bolivien"

#: pynicotine/networkfilter.py:69
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius und Saba"

#: pynicotine/networkfilter.py:70
msgid "Brazil"
msgstr "Brasilien"

#: pynicotine/networkfilter.py:71
msgid "Bahamas"
msgstr "Bahamas"

#: pynicotine/networkfilter.py:72
msgid "Bhutan"
msgstr "Bhutan"

#: pynicotine/networkfilter.py:73
msgid "Bouvet Island"
msgstr "Bouvetinsel"

#: pynicotine/networkfilter.py:74
msgid "Botswana"
msgstr "Botswana"

#: pynicotine/networkfilter.py:75
msgid "Belarus"
msgstr "Weißrussland"

#: pynicotine/networkfilter.py:76
msgid "Belize"
msgstr "Belize"

#: pynicotine/networkfilter.py:77
msgid "Canada"
msgstr "Kanada"

#: pynicotine/networkfilter.py:78
msgid "Cocos (Keeling) Islands"
msgstr "Kokos-(Keeling-)Inseln"

#: pynicotine/networkfilter.py:79
msgid "Democratic Republic of Congo"
msgstr "Demokratische Republik Kongo"

#: pynicotine/networkfilter.py:80
msgid "Central African Republic"
msgstr "Zentralafrikanische Republik"

#: pynicotine/networkfilter.py:81
msgid "Congo"
msgstr "Kongo"

#: pynicotine/networkfilter.py:82
msgid "Switzerland"
msgstr "Schweiz"

#: pynicotine/networkfilter.py:83
msgid "Ivory Coast"
msgstr "Elfenbeinküste"

#: pynicotine/networkfilter.py:84
msgid "Cook Islands"
msgstr "Cookinseln"

#: pynicotine/networkfilter.py:85
msgid "Chile"
msgstr "Chile"

#: pynicotine/networkfilter.py:86
msgid "Cameroon"
msgstr "Kamerun"

#: pynicotine/networkfilter.py:87
msgid "China"
msgstr "China"

#: pynicotine/networkfilter.py:88
msgid "Colombia"
msgstr "Kolumbien"

#: pynicotine/networkfilter.py:89
msgid "Costa Rica"
msgstr "Costa Rica"

#: pynicotine/networkfilter.py:90
msgid "Cuba"
msgstr "Kuba"

#: pynicotine/networkfilter.py:91
msgid "Cabo Verde"
msgstr "Cabo Verde"

#: pynicotine/networkfilter.py:92
msgid "Curaçao"
msgstr "Curaçao"

#: pynicotine/networkfilter.py:93
msgid "Christmas Island"
msgstr "Weihnachtsinsel"

#: pynicotine/networkfilter.py:94
msgid "Cyprus"
msgstr "Zypern"

#: pynicotine/networkfilter.py:95
msgid "Czechia"
msgstr "Tschechien"

#: pynicotine/networkfilter.py:96
msgid "Germany"
msgstr "Deutschland"

#: pynicotine/networkfilter.py:97
msgid "Djibouti"
msgstr "Dschibuti"

#: pynicotine/networkfilter.py:98
msgid "Denmark"
msgstr "Dänemark"

#: pynicotine/networkfilter.py:99
msgid "Dominica"
msgstr "Dominica"

#: pynicotine/networkfilter.py:100
msgid "Dominican Republic"
msgstr "Dominikanische Republik"

#: pynicotine/networkfilter.py:101
msgid "Algeria"
msgstr "Algerien"

#: pynicotine/networkfilter.py:102
msgid "Ecuador"
msgstr "Ecuador"

#: pynicotine/networkfilter.py:103
msgid "Estonia"
msgstr "Estland"

#: pynicotine/networkfilter.py:104
msgid "Egypt"
msgstr "Ägypten"

#: pynicotine/networkfilter.py:105
msgid "Western Sahara"
msgstr "Westsahara"

#: pynicotine/networkfilter.py:106
msgid "Eritrea"
msgstr "Eritrea"

#: pynicotine/networkfilter.py:107
msgid "Spain"
msgstr "Spanien"

#: pynicotine/networkfilter.py:108
msgid "Ethiopia"
msgstr "Äthiopien"

#: pynicotine/networkfilter.py:109
msgid "Europe"
msgstr "Europa"

#: pynicotine/networkfilter.py:110
msgid "Finland"
msgstr "Finnland"

#: pynicotine/networkfilter.py:111
msgid "Fiji"
msgstr "Fidschi"

#: pynicotine/networkfilter.py:112
msgid "Falkland Islands (Malvinas)"
msgstr "Falklandinseln (Malwinen)"

#: pynicotine/networkfilter.py:113
msgid "Micronesia"
msgstr "Mikronesien"

#: pynicotine/networkfilter.py:114
msgid "Faroe Islands"
msgstr "Färöer Inseln"

#: pynicotine/networkfilter.py:115
msgid "France"
msgstr "Frankreich"

#: pynicotine/networkfilter.py:116
msgid "Gabon"
msgstr "Gabun"

#: pynicotine/networkfilter.py:117
msgid "Great Britain"
msgstr "Großbritannien"

#: pynicotine/networkfilter.py:118
msgid "Grenada"
msgstr "Grenada"

#: pynicotine/networkfilter.py:119
msgid "Georgia"
msgstr "Georgien"

#: pynicotine/networkfilter.py:120
msgid "French Guiana"
msgstr "Französisch-Guayana"

#: pynicotine/networkfilter.py:121
msgid "Guernsey"
msgstr "Guernsey"

#: pynicotine/networkfilter.py:122
msgid "Ghana"
msgstr "Ghana"

#: pynicotine/networkfilter.py:123
msgid "Gibraltar"
msgstr "Gibraltar"

#: pynicotine/networkfilter.py:124
msgid "Greenland"
msgstr "Grönland"

#: pynicotine/networkfilter.py:125
msgid "Gambia"
msgstr "Gambia"

#: pynicotine/networkfilter.py:126
msgid "Guinea"
msgstr "Guinea"

#: pynicotine/networkfilter.py:127
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: pynicotine/networkfilter.py:128
msgid "Equatorial Guinea"
msgstr "Äquatorialguinea"

#: pynicotine/networkfilter.py:129
msgid "Greece"
msgstr "Griechenland"

#: pynicotine/networkfilter.py:130
msgid "South Georgia & South Sandwich Islands"
msgstr "Südgeorgien und die Südlichen Sandwichinseln"

#: pynicotine/networkfilter.py:131
msgid "Guatemala"
msgstr "Guatemala"

#: pynicotine/networkfilter.py:132
msgid "Guam"
msgstr "Guam"

#: pynicotine/networkfilter.py:133
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: pynicotine/networkfilter.py:134
msgid "Guyana"
msgstr "Guyana"

#: pynicotine/networkfilter.py:135
msgid "Hong Kong"
msgstr "Hongkong"

#: pynicotine/networkfilter.py:136
msgid "Heard & McDonald Islands"
msgstr "Heard- und McDonaldinseln"

#: pynicotine/networkfilter.py:137
msgid "Honduras"
msgstr "Honduras"

#: pynicotine/networkfilter.py:138
msgid "Croatia"
msgstr "Kroatien"

#: pynicotine/networkfilter.py:139
msgid "Haiti"
msgstr "Haiti"

#: pynicotine/networkfilter.py:140
msgid "Hungary"
msgstr "Ungarn"

#: pynicotine/networkfilter.py:141
msgid "Indonesia"
msgstr "Indonesien"

#: pynicotine/networkfilter.py:142
msgid "Ireland"
msgstr "Irland"

#: pynicotine/networkfilter.py:143
msgid "Israel"
msgstr "Israel"

#: pynicotine/networkfilter.py:144
msgid "Isle of Man"
msgstr "Isle of Man"

#: pynicotine/networkfilter.py:145
msgid "India"
msgstr "Indien"

#: pynicotine/networkfilter.py:146
msgid "British Indian Ocean Territory"
msgstr "Britisches Territorium im Indischen Ozean"

#: pynicotine/networkfilter.py:147
msgid "Iraq"
msgstr "Irak"

#: pynicotine/networkfilter.py:148
msgid "Iran"
msgstr "Iran"

#: pynicotine/networkfilter.py:149
msgid "Iceland"
msgstr "Island"

#: pynicotine/networkfilter.py:150
msgid "Italy"
msgstr "Italien"

#: pynicotine/networkfilter.py:151
msgid "Jersey"
msgstr "Jersey"

#: pynicotine/networkfilter.py:152
msgid "Jamaica"
msgstr "Jamaika"

#: pynicotine/networkfilter.py:153
msgid "Jordan"
msgstr "Jordanien"

#: pynicotine/networkfilter.py:154
msgid "Japan"
msgstr "Japan"

#: pynicotine/networkfilter.py:155
msgid "Kenya"
msgstr "Kenia"

#: pynicotine/networkfilter.py:156
msgid "Kyrgyzstan"
msgstr "Kirgisistan"

#: pynicotine/networkfilter.py:157
msgid "Cambodia"
msgstr "Kambodscha"

#: pynicotine/networkfilter.py:158
msgid "Kiribati"
msgstr "Kiribati"

#: pynicotine/networkfilter.py:159
msgid "Comoros"
msgstr "Komoren"

#: pynicotine/networkfilter.py:160
msgid "Saint Kitts & Nevis"
msgstr "St. Kitts und Nevis"

#: pynicotine/networkfilter.py:161
msgid "North Korea"
msgstr "Nordkorea"

#: pynicotine/networkfilter.py:162
msgid "South Korea"
msgstr "Südkorea"

#: pynicotine/networkfilter.py:163
msgid "Kuwait"
msgstr "Kuwait"

#: pynicotine/networkfilter.py:164
msgid "Cayman Islands"
msgstr "Kaimaninseln"

#: pynicotine/networkfilter.py:165
msgid "Kazakhstan"
msgstr "Kasachstan"

#: pynicotine/networkfilter.py:166
msgid "Laos"
msgstr "Laos"

#: pynicotine/networkfilter.py:167
msgid "Lebanon"
msgstr "Libanon"

#: pynicotine/networkfilter.py:168
msgid "Saint Lucia"
msgstr "St. Lucia"

#: pynicotine/networkfilter.py:169
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: pynicotine/networkfilter.py:170
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: pynicotine/networkfilter.py:171
msgid "Liberia"
msgstr "Liberia"

#: pynicotine/networkfilter.py:172
msgid "Lesotho"
msgstr "Lesotho"

#: pynicotine/networkfilter.py:173
msgid "Lithuania"
msgstr "Litauen"

#: pynicotine/networkfilter.py:174
msgid "Luxembourg"
msgstr "Luxemburg"

#: pynicotine/networkfilter.py:175
msgid "Latvia"
msgstr "Lettland"

#: pynicotine/networkfilter.py:176
msgid "Libya"
msgstr "Libyen"

#: pynicotine/networkfilter.py:177
msgid "Morocco"
msgstr "Marokko"

#: pynicotine/networkfilter.py:178
msgid "Monaco"
msgstr "Monaco"

#: pynicotine/networkfilter.py:179
msgid "Moldova"
msgstr "Moldawien"

#: pynicotine/networkfilter.py:180
msgid "Montenegro"
msgstr "Montenegro"

#: pynicotine/networkfilter.py:181
msgid "Saint Martin"
msgstr "Sankt Martin"

#: pynicotine/networkfilter.py:182
msgid "Madagascar"
msgstr "Madagaskar"

#: pynicotine/networkfilter.py:183
msgid "Marshall Islands"
msgstr "Marshallinseln"

#: pynicotine/networkfilter.py:184
msgid "North Macedonia"
msgstr "Nordmazedonien"

#: pynicotine/networkfilter.py:185
msgid "Mali"
msgstr "Mali"

#: pynicotine/networkfilter.py:186
msgid "Myanmar"
msgstr "Myanmar"

#: pynicotine/networkfilter.py:187
msgid "Mongolia"
msgstr "Mongolei"

#: pynicotine/networkfilter.py:188
msgid "Macau"
msgstr "Macau"

#: pynicotine/networkfilter.py:189
msgid "Northern Mariana Islands"
msgstr "Nördliche Marianen"

#: pynicotine/networkfilter.py:190
msgid "Martinique"
msgstr "Martinique"

#: pynicotine/networkfilter.py:191
msgid "Mauritania"
msgstr "Mauretanien"

#: pynicotine/networkfilter.py:192
msgid "Montserrat"
msgstr "Montserrat"

#: pynicotine/networkfilter.py:193
msgid "Malta"
msgstr "Malta"

#: pynicotine/networkfilter.py:194
msgid "Mauritius"
msgstr "Mauritius"

#: pynicotine/networkfilter.py:195
msgid "Maldives"
msgstr "Malediven"

#: pynicotine/networkfilter.py:196
msgid "Malawi"
msgstr "Malawi"

#: pynicotine/networkfilter.py:197
msgid "Mexico"
msgstr "Mexiko"

#: pynicotine/networkfilter.py:198
msgid "Malaysia"
msgstr "Malaysia"

#: pynicotine/networkfilter.py:199
msgid "Mozambique"
msgstr "Mosambik"

#: pynicotine/networkfilter.py:200
msgid "Namibia"
msgstr "Namibia"

#: pynicotine/networkfilter.py:201
msgid "New Caledonia"
msgstr "Neukaledonien"

#: pynicotine/networkfilter.py:202
msgid "Niger"
msgstr "Niger"

#: pynicotine/networkfilter.py:203
msgid "Norfolk Island"
msgstr "Norfolkinsel"

#: pynicotine/networkfilter.py:204
msgid "Nigeria"
msgstr "Nigeria"

#: pynicotine/networkfilter.py:205
msgid "Nicaragua"
msgstr "Nicaragua"

#: pynicotine/networkfilter.py:206
msgid "Netherlands"
msgstr "Niederlande"

#: pynicotine/networkfilter.py:207
msgid "Norway"
msgstr "Norwegen"

#: pynicotine/networkfilter.py:208
msgid "Nepal"
msgstr "Nepal"

#: pynicotine/networkfilter.py:209
msgid "Nauru"
msgstr "Nauru"

#: pynicotine/networkfilter.py:210
msgid "Niue"
msgstr "Niue"

#: pynicotine/networkfilter.py:211
msgid "New Zealand"
msgstr "Neuseeland"

#: pynicotine/networkfilter.py:212
msgid "Oman"
msgstr "Oman"

#: pynicotine/networkfilter.py:213
msgid "Panama"
msgstr "Panama"

#: pynicotine/networkfilter.py:214
msgid "Peru"
msgstr "Peru"

#: pynicotine/networkfilter.py:215
msgid "French Polynesia"
msgstr "Französisch-Polynesien"

#: pynicotine/networkfilter.py:216
msgid "Papua New Guinea"
msgstr "Papua-Neuguinea"

#: pynicotine/networkfilter.py:217
msgid "Philippines"
msgstr "Phillippinen"

#: pynicotine/networkfilter.py:218
msgid "Pakistan"
msgstr "Pakistan"

#: pynicotine/networkfilter.py:219
msgid "Poland"
msgstr "Polen"

#: pynicotine/networkfilter.py:220
msgid "Saint Pierre & Miquelon"
msgstr "Saint-Pierre und Miquelon"

#: pynicotine/networkfilter.py:221
msgid "Pitcairn"
msgstr "Pitcairn"

#: pynicotine/networkfilter.py:222
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: pynicotine/networkfilter.py:223
msgid "State of Palestine"
msgstr "Palästina"

#: pynicotine/networkfilter.py:224
msgid "Portugal"
msgstr "Portugal"

#: pynicotine/networkfilter.py:225
msgid "Palau"
msgstr "Palau"

#: pynicotine/networkfilter.py:226
msgid "Paraguay"
msgstr "Paraguay"

#: pynicotine/networkfilter.py:227
msgid "Qatar"
msgstr "Katar"

#: pynicotine/networkfilter.py:228
msgid "Réunion"
msgstr "Réunion"

#: pynicotine/networkfilter.py:229
msgid "Romania"
msgstr "Rumänien"

#: pynicotine/networkfilter.py:230
msgid "Serbia"
msgstr "Serbien"

#: pynicotine/networkfilter.py:231
msgid "Russia"
msgstr "Russland"

#: pynicotine/networkfilter.py:232
msgid "Rwanda"
msgstr "Ruanda"

#: pynicotine/networkfilter.py:233
msgid "Saudi Arabia"
msgstr "Saudi-Arabien"

#: pynicotine/networkfilter.py:234
msgid "Solomon Islands"
msgstr "Salomoninseln"

#: pynicotine/networkfilter.py:235
msgid "Seychelles"
msgstr "Seychellen"

#: pynicotine/networkfilter.py:236
msgid "Sudan"
msgstr "Sudan"

#: pynicotine/networkfilter.py:237
msgid "Sweden"
msgstr "Schweden"

#: pynicotine/networkfilter.py:238
msgid "Singapore"
msgstr "Singapur"

#: pynicotine/networkfilter.py:239
msgid "Saint Helena"
msgstr "Sankt Helena"

#: pynicotine/networkfilter.py:240
msgid "Slovenia"
msgstr "Slowenien"

#: pynicotine/networkfilter.py:241
msgid "Svalbard & Jan Mayen Islands"
msgstr "Spitzbergen & Jan Mayen Inseln"

#: pynicotine/networkfilter.py:242
msgid "Slovak Republic"
msgstr "Slowakische Republik"

#: pynicotine/networkfilter.py:243
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: pynicotine/networkfilter.py:244
msgid "San Marino"
msgstr "San Marino"

#: pynicotine/networkfilter.py:245
msgid "Senegal"
msgstr "Senegal"

#: pynicotine/networkfilter.py:246
msgid "Somalia"
msgstr "Somalia"

#: pynicotine/networkfilter.py:247
msgid "Suriname"
msgstr "Surinam"

#: pynicotine/networkfilter.py:248
msgid "South Sudan"
msgstr "Südsudan"

#: pynicotine/networkfilter.py:249
msgid "Sao Tome & Principe"
msgstr "São Tomé und Príncipe"

#: pynicotine/networkfilter.py:250
msgid "El Salvador"
msgstr "El Salvador"

#: pynicotine/networkfilter.py:251
msgid "Sint Maarten"
msgstr "Sint Maarten"

#: pynicotine/networkfilter.py:252
msgid "Syria"
msgstr "Syrien"

#: pynicotine/networkfilter.py:253
msgid "Eswatini"
msgstr "Eswatini"

#: pynicotine/networkfilter.py:254
msgid "Turks & Caicos Islands"
msgstr "Turks- und Caicosinseln"

#: pynicotine/networkfilter.py:255
msgid "Chad"
msgstr "Tschad"

#: pynicotine/networkfilter.py:256
msgid "French Southern Territories"
msgstr "Südfranzösische Territorien"

#: pynicotine/networkfilter.py:257
msgid "Togo"
msgstr "Togo"

#: pynicotine/networkfilter.py:258
msgid "Thailand"
msgstr "Thailand"

#: pynicotine/networkfilter.py:259
msgid "Tajikistan"
msgstr "Tadschikistan"

#: pynicotine/networkfilter.py:260
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
msgid "Timor-Leste"
msgstr "Timor-Leste"

#: pynicotine/networkfilter.py:262
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: pynicotine/networkfilter.py:263
msgid "Tunisia"
msgstr "Tunesien"

#: pynicotine/networkfilter.py:264
msgid "Tonga"
msgstr "Tonga"

#: pynicotine/networkfilter.py:265
msgid "Türkiye"
msgstr "Türkei"

#: pynicotine/networkfilter.py:266
msgid "Trinidad & Tobago"
msgstr "Trinidad und Tobago"

#: pynicotine/networkfilter.py:267
msgid "Tuvalu"
msgstr "Tuvalu"

#: pynicotine/networkfilter.py:268
msgid "Taiwan"
msgstr "Taiwan"

#: pynicotine/networkfilter.py:269
msgid "Tanzania"
msgstr "Tansania"

#: pynicotine/networkfilter.py:270
msgid "Ukraine"
msgstr "Ukraine"

#: pynicotine/networkfilter.py:271
msgid "Uganda"
msgstr "Uganda"

#: pynicotine/networkfilter.py:272
msgid "U.S. Minor Outlying Islands"
msgstr "US-amerikanische kleinere abgelegene Inseln"

#: pynicotine/networkfilter.py:273
msgid "United States"
msgstr "Vereinigte Staaten von Amerika"

#: pynicotine/networkfilter.py:274
msgid "Uruguay"
msgstr "Uruguay"

#: pynicotine/networkfilter.py:275
msgid "Uzbekistan"
msgstr "Usbekistan"

#: pynicotine/networkfilter.py:276
msgid "Holy See (Vatican City State)"
msgstr "Heiliger Stuhl (Vatikanstadt)"

#: pynicotine/networkfilter.py:277
msgid "Saint Vincent & The Grenadines"
msgstr "St. Vincent und die Grenadinen"

#: pynicotine/networkfilter.py:278
msgid "Venezuela"
msgstr "Venezuela"

#: pynicotine/networkfilter.py:279
msgid "British Virgin Islands"
msgstr "Britische Jungferninseln"

#: pynicotine/networkfilter.py:280
msgid "U.S. Virgin Islands"
msgstr "U.S. Jungferninseln"

#: pynicotine/networkfilter.py:281
msgid "Viet Nam"
msgstr "Vietnam"

#: pynicotine/networkfilter.py:282
msgid "Vanuatu"
msgstr "Vanuatu"

#: pynicotine/networkfilter.py:283
msgid "Wallis & Futuna"
msgstr "Wallis und Futuna"

#: pynicotine/networkfilter.py:284
msgid "Samoa"
msgstr "Samoa"

#: pynicotine/networkfilter.py:285
msgid "Yemen"
msgstr "Jemen"

#: pynicotine/networkfilter.py:286
msgid "Mayotte"
msgstr "Mayotte"

#: pynicotine/networkfilter.py:287
msgid "South Africa"
msgstr "Südafrika"

#: pynicotine/networkfilter.py:288
msgid "Zambia"
msgstr "Sambia"

#: pynicotine/networkfilter.py:289
msgid "Zimbabwe"
msgstr "Simbabwe"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, python-format
msgid "Text-to-speech for message failed: %s"
msgstr ""
"Die Text-zu-Sprache-Umwandlung für die Nachricht ist fehlgeschlagen: %s"

#: pynicotine/nowplaying.py:130
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr ""
"Last.fm: Bitte gib sowohl deinen Last.fm-Benutzernamen als auch deinen API-"
"Schlüssel an"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
msgid "Now Playing Error"
msgstr "Fehler bei der Wiedergabe von \"Jetzt läuft\""

#: pynicotine/nowplaying.py:140
#, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr ""
"Last.fm: Konnte keine Verbindung zu Audioscrobbler herstellen: %(error)s"

#: pynicotine/nowplaying.py:162
#, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr ""
"Last.fm: Konnte den letzten Track von Audioscrobbler nicht abrufen: %(error)s"

#: pynicotine/nowplaying.py:196
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: Konnte keinen passenden MPRIS-Mediaspieler finden"

#: pynicotine/nowplaying.py:201
#, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr ""
"Mehrere MPRIS-Spieler gefunden: %(players)s. Verwendet wird: %(player)s"

#: pynicotine/nowplaying.py:204
#, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Automatisch erkannter MPRIS-Mediaplayer: %s"

#: pynicotine/nowplaying.py:219
#, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr ""
"MPRIS: Beim Abfragen von %(player)s ist ein Fehler aufgetreten: %(exception)s"

#: pynicotine/nowplaying.py:266
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "Bitte gib deinen ListenBrainz Benutzernamen an"

#: pynicotine/nowplaying.py:275
#, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz: Konnte keine Verbindung zu ListenBrainz herstellen: %(error)s"

#: pynicotine/nowplaying.py:283
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "ListenBrainz: Es scheint, als würdest du gerade nichts hören"

#: pynicotine/nowplaying.py:297
#, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz: Konnte den aktuellen Track von ListenBrainz nicht abrufen: "
"%(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
msgid "Network Filters"
msgstr "Netzwerkschnittstelle"

#: pynicotine/plugins/core_commands/__init__.py:44
msgid "List available commands"
msgstr "Verfügbare Befehle auflisten"

#: pynicotine/plugins/core_commands/__init__.py:49
msgid "Connect to the server"
msgstr "Verbinde mit dem Server"

#: pynicotine/plugins/core_commands/__init__.py:53
msgid "Disconnect from the server"
msgstr "Vom Server trennen"

#: pynicotine/plugins/core_commands/__init__.py:58
msgid "Toggle away status"
msgstr "Abwesenheitsstatus umschalten"

#: pynicotine/plugins/core_commands/__init__.py:62
msgid "Manage plugins"
msgstr "Plugins verwalten"

#: pynicotine/plugins/core_commands/__init__.py:74
msgid "Clear chat window"
msgstr "Chat-Fenster leeren"

#: pynicotine/plugins/core_commands/__init__.py:80
msgid "Say something in the third-person"
msgstr "Sage etwas in der dritten Person"

#: pynicotine/plugins/core_commands/__init__.py:87
msgid "Announce the song currently playing"
msgstr "Gib den aktuell gespielten Song an"

#: pynicotine/plugins/core_commands/__init__.py:94
msgid "Join chat room"
msgstr "Chatroom beitreten"

#: pynicotine/plugins/core_commands/__init__.py:102
msgid "Leave chat room"
msgstr "Aktuellen Raum verlassen"

#: pynicotine/plugins/core_commands/__init__.py:110
msgid "Say message in specified chat room"
msgstr "Sende Nachricht im angegebenen Chatraum"

#: pynicotine/plugins/core_commands/__init__.py:117
msgid "Open private chat"
msgstr "Privatchat öffnen"

#: pynicotine/plugins/core_commands/__init__.py:125
msgid "Close private chat"
msgstr "Privaten Chat schließen"

#: pynicotine/plugins/core_commands/__init__.py:133
msgid "Request user's client version"
msgstr "Clientversion des Benutzers anfordern"

#: pynicotine/plugins/core_commands/__init__.py:142
msgid "Send private message to user"
msgstr "Sende private Nachricht an Benutzer"

#: pynicotine/plugins/core_commands/__init__.py:150
msgid "Add user to buddy list"
msgstr "Benutzer zur Freundesliste hinzufügen"

#: pynicotine/plugins/core_commands/__init__.py:158
msgid "Remove buddy from buddy list"
msgstr "Freund aus der Freundesliste entfernen"

#: pynicotine/plugins/core_commands/__init__.py:166
msgid "Browse files of user"
msgstr "Dateien des Benutzers durchsuchen"

#: pynicotine/plugins/core_commands/__init__.py:175
msgid "Show user profile information"
msgstr "Profilinformationen des Benutzers anzeigen"

#: pynicotine/plugins/core_commands/__init__.py:183
msgid "Show IP address or username"
msgstr "IP-Adresse oder Benutzername anzeigen"

#: pynicotine/plugins/core_commands/__init__.py:190
msgid "Block connections from user or IP address"
msgstr "Blockiere Verbindungen von Benutzer oder IP-Adresse"

#: pynicotine/plugins/core_commands/__init__.py:197
msgid "Remove user or IP address from ban lists"
msgstr "Benutzer oder IP-Adresse aus der Blockierungsliste entfernen"

#: pynicotine/plugins/core_commands/__init__.py:204
msgid "Silence messages from user or IP address"
msgstr "Nachrichten von Benutzer oder IP-Adresse stummschalten"

#: pynicotine/plugins/core_commands/__init__.py:212
msgid "Remove user or IP address from ignore lists"
msgstr "Benutzer oder IP-Adresse aus den Ignorierlisten entfernen"

#: pynicotine/plugins/core_commands/__init__.py:220
msgid "Add share"
msgstr "Freigabe hinzufügen"

#: pynicotine/plugins/core_commands/__init__.py:226
msgid "Remove share"
msgstr "Freigabe entfernen"

#: pynicotine/plugins/core_commands/__init__.py:233
msgid "List shares"
msgstr "Freigaben auflisten"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Freigaben aktualisieren"

#: pynicotine/plugins/core_commands/__init__.py:246
msgid "Start global file search"
msgstr "Starte globale Dateisuche"

#: pynicotine/plugins/core_commands/__init__.py:254
msgid "Search files in joined rooms"
msgstr "Suche Dateien in beigetretenen Räumen"

#: pynicotine/plugins/core_commands/__init__.py:262
msgid "Search files of all buddies"
msgstr "Dateien aller Freunde durchsuchen"

#: pynicotine/plugins/core_commands/__init__.py:270
msgid "Search a user's shared files"
msgstr "Suche in den freigegebenen Dateien eines Benutzers"

#: pynicotine/plugins/core_commands/__init__.py:296
#, python-format
msgid "Listing %(num)i available commands:"
msgstr "Auflistung %(num)i verfügbarer Befehle:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr ""
"Auflistung %(num)i verfügbarer Befehle, die mit \"%(query)s\" übereinstimmen:"

#: pynicotine/plugins/core_commands/__init__.py:311
#, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Gib %(command)s ein, um ähnliche Befehle aufzulisten"

#: pynicotine/plugins/core_commands/__init__.py:314
#, python-format
msgid "Type %(command)s to list available commands"
msgstr "Gib %(command)s ein, um verfügbare Befehle anzuzeigen"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, python-format
msgid "Not joined in room %s"
msgstr "Bisher nicht dem Raum %s beigetreten"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr "Keine Nachrichten mit Benutzer %s"

#: pynicotine/plugins/core_commands/__init__.py:408
#, python-format
msgid "Closed private chat of user %s"
msgstr "Geschlossener privater Chat von Benutzer %s"

#: pynicotine/plugins/core_commands/__init__.py:476
#, python-format
msgid "Banned %s"
msgstr "Blockiert (%s)"

#: pynicotine/plugins/core_commands/__init__.py:490
#, python-format
msgid "Unbanned %s"
msgstr "Blockierung aufheben %s"

#: pynicotine/plugins/core_commands/__init__.py:503
#, python-format
msgid "Ignored %s"
msgstr "Ignoriert %s"

#: pynicotine/plugins/core_commands/__init__.py:517
#, python-format
msgid "Unignored %s"
msgstr "%s nicht mehr ignoriert"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr "%(num_listed)s Freigaben aufgelistet (%(num_total)s konfiguriert)"

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr "Der Ordner \"%s\" ist nicht zugänglich – Freigabe nicht möglich"

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""
"%(group_name)s-Freigabe \"%(virtual_name)s\" hinzugefügt (erneuter Scan "
"erforderlich)"

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr "Keine Freigabe mit dem Namen \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr "Freigabe \"%s\" entfernt (erneuter Scan erforderlich)"

#: pynicotine/pluginsystem.py:413
msgid "Loading plugin system"
msgstr "Lade Plugin-System"

#: pynicotine/pluginsystem.py:516
#, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Plugin %(name)s konnte nicht geladen werden. Der Ordnername des Plugins "
"enthält ungültige Zeichen: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "Konfliktbefehl %(interface)s im Plugin %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, python-format
msgid "Loaded plugin %s"
msgstr "Geladenes Plugin %s"

#: pynicotine/pluginsystem.py:579
#, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Plugin %(module)s konnte nicht geladen werden\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, python-format
msgid "Unloaded plugin %s"
msgstr "Ungeladenes Plugin %s"

#: pynicotine/pluginsystem.py:648
#, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Plugin %(module)s konnte nicht entladen werden\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Plugin %(module)s ist mit dem Fehler %(errortype)s fehlgeschlagen: "
"%(error)s.\n"
"Trace: %(trace)s"

#: pynicotine/pluginsystem.py:810
msgid "No description"
msgstr "Keine Beschreibung vorhanden"

#: pynicotine/pluginsystem.py:887
#, python-format
msgid "Missing %s argument"
msgstr "Fehlendes %s-Argument"

#: pynicotine/pluginsystem.py:896
#, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Ungültiges Argument, mögliche Optionen: %s"

#: pynicotine/pluginsystem.py:901
#, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Verwendung: %(command)s %(parameters)s"

#: pynicotine/pluginsystem.py:940
#, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Unbekannter Befehl: %(command)s. Gib %(help_command)s ein, um verfügbare "
"Befehle anzuzeigen."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
msgid "No UPnP devices found"
msgstr "Keine UPnP-Geräte gefunden"

#: pynicotine/portmapper.py:633
#, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"%(protocol)s: Weiterleitung des externen Ports fehlgeschlagen "
"%(external_port)s: %(error)s"

#: pynicotine/portmapper.py:647
#, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"%(protocol)s: Externer Port %(external_port)s erfolgreich an lokale IP-"
"Adresse %(ip_address)s Port %(local_port)s weitergeleitet"

#: pynicotine/privatechat.py:220
#, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Private Nachricht von Benutzer '%(user)s': %(message)s"

#: pynicotine/search.py:368
#, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Wunschlisteintrag suchen \"%s\""

#: pynicotine/search.py:434
#, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Wartezeit für die Wunschliste wurde auf %s Sekunden eingestellt"

#: pynicotine/search.py:760
#, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr ""
"Benutzer %(user)s sucht nach \"%(query)s\", %(num)i Ergebnisse gefunden"

#: pynicotine/shares.py:302
msgid "Rebuilding shares…"
msgstr "Freigaben neu erstellen …"

#: pynicotine/shares.py:302
msgid "Rescanning shares…"
msgstr "Freigaben werden erneut gescannt …"

#: pynicotine/shares.py:324
#, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Erneuter Scan abgeschlossen: %(num)s Ordner gefunden"

#: pynicotine/shares.py:334
#, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Ein schwerwiegender Fehler ist beim erneuten Scannen der Freigaben "
"aufgetreten. Wenn das Problem weiterhin besteht, lösche %(dir)s/*.dbn und "
"versuche es erneut. Wenn dies nicht hilft, erstelle bitte einen "
"Fehlerbericht, unter Angabe des folgenden Stacktraces: %(trace)s"

#: pynicotine/shares.py:582
#, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Fehler beim Scannen der Datei %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Fehler beim Scannen des Ordners %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "Fehler beim Scannen der Metadaten für die Datei %(path)s: %(error)s"

#: pynicotine/shares.py:1046
#, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr ""
"Das erneute Scannen wurde abgebrochen, weil Freigaben nicht verfügbar sind: "
"%s"

#: pynicotine/shares.py:1184
#, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "Benutzer %(user)s durchsucht deine Liste freigegebener Dateien"

#: pynicotine/slskmessages.py:3120
#, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Die Freigabedatenbank kann nicht gelesen werden. Bitte scanne deine "
"Freigaben erneut. Fehler: %s"

#: pynicotine/slskproto.py:500
#, python-format
msgid "Specified network interface '%s' is not available"
msgstr "Das angegebene Netzwerkinterface '%s' ist nicht verfügbar"

#: pynicotine/slskproto.py:511
#, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Kann nicht auf Port %(port)s lauschen. Stelle sicher, dass keine andere "
"Anwendung diesen Port verwendet, oder wähle einen anderen Port. Fehler: "
"%(error)s"

#: pynicotine/slskproto.py:523
#, python-format
msgid "Listening on port: %i"
msgstr "Lauschen auf Port: %i"

#: pynicotine/slskproto.py:805
#, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr ""
"Kann keine Verbindung zum Server %(host)s:%(port)s: %(error)s herstellen"

#: pynicotine/slskproto.py:1095
#, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Wiederverbindung zum Server in %s Sekunden"

#: pynicotine/slskproto.py:1170
#, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Verbindung zu %(host)s:%(port)s herstellen"

#: pynicotine/slskproto.py:1208
#, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Verbunden mit Server %(host)s:%(port)s, Anmeldung läuft …"

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Vom Server getrennt %(host)s:%(port)s"

#: pynicotine/slskproto.py:1499
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Jemand hat sich an einem anderen Ort in dein Deezer-Konto eingeloggt"

#: pynicotine/uploads.py:382
#, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Upload abgeschlossen: Benutzer %(user)s, IP-Adresse %(ip)s, Datei %(file)s"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Upload abgebrochen, Benutzer %(user)s Datei %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, python-format
msgid "Upload I/O error: %s"
msgstr "Upload I/O-Fehler: %s"

#: pynicotine/uploads.py:1103
#, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr "Upload gestartet: Benutzer %(user)s, IP-Adresse %(ip)s, Datei %(file)s"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr ""
"Kann Verzeichnis '%(folder)s' nicht erstellen, gemeldeter Fehler: %(error)s"

#: pynicotine/userbrowse.py:236
#, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr ""
"Das Laden von Freigaben von der Festplatte ist fehlgeschlagen: %(error)s"

#: pynicotine/userbrowse.py:282
#, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr ""
"Gespeicherte Liste der freigegebenen Dateien für den Benutzer '%(user)s' auf "
"%(dir)s"

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr ""
"Kann Freigaben nicht speichern, '%(user)s', gemeldeter Fehler: %(error)s"

#: pynicotine/userinfo.py:160
#, python-format
msgid "Picture saved to %s"
msgstr "Bild gespeichert in %s"

#: pynicotine/userinfo.py:163
#, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Kann Bild nicht auf %(filename)s speichern: %(error)s"

#: pynicotine/userinfo.py:190
#, python-format
msgid "User %(user)s is viewing your profile"
msgstr "Benutzer %(user)s sieht sich dein Profil an"

#: pynicotine/users.py:272
#, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Verbindung zum Server nicht möglich. Grund: %s"

#: pynicotine/users.py:272
msgid "Cannot Connect"
msgstr "Verbindung konnte nicht hergestellt werden"

#: pynicotine/users.py:306
#, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr ""
"Kann die IP des Benutzers %s nicht abrufen, da dieser Benutzer offline ist"

#: pynicotine/users.py:315
#, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr "IP-Adresse des Benutzers %(user)s: %(ip)s, Port %(port)i%(country)s"

#: pynicotine/users.py:433
msgid "Soulseek Announcement"
msgstr "Soulseek-Ankündigung"

#: pynicotine/users.py:449
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"Du hast keine Soulseek Privilegien. Nur mit aktiven Privilegien, werden "
"deine Downloads vor denen von nicht-privilegierten Nutzern in die "
"Warteschlange eingereiht."

#: pynicotine/users.py:455
#, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"%(days)i Tage, %(hours)i Stunden, %(minutes)i Minuten, %(seconds)i Sekunden "
"Soulseek-Privilegien verbleibend"

#: pynicotine/users.py:473
msgid "Your password has been changed"
msgstr "Dein Passwort wurde geändert"

#: pynicotine/users.py:473
msgid "Password Changed"
msgstr "Passwort geändert"

#: pynicotine/utils.py:574
#, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Kann Dateipfad %(path)s nicht öffnen: %(error)s"

#: pynicotine/utils.py:621
#, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "URL %(url)s kann nicht geöffnet werden: %(error)s"

#: pynicotine/utils.py:646
#, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr ""
"Beim Lesen der Datei %(filename)s ist ein Fehler aufgetreten: %(error)s"

#: pynicotine/utils.py:651
#, python-format
msgid "Attempting to load backup of file %s"
msgstr "Versuch, das Backup der Datei %s zu laden"

#: pynicotine/utils.py:673
#, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Datei %(path)s konnte nicht gesichert werden: %(error)s"

#: pynicotine/utils.py:694
#, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Datei %(path)s konnte nicht gespeichert werden: %(error)s"

#: pynicotine/utils.py:705
#, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr ""
"Vorherige Datei %(path)s konnte nicht wiederhergestellt werden: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
msgid "Add buddy…"
msgstr "Freund hinzufügen …"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
msgid "Toggle Text-to-Speech"
msgstr "Text-zu-Sprache umschalten"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
msgid "Chat Room Command Help"
msgstr "Chatraum-Befehls-Hilfe"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
msgid "_Log"
msgstr "_Protokoll"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
msgid "Room Wall"
msgstr "Raum Pinnwand"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
msgid "R_oom Wall"
msgstr "R_oom Pinnwand"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
msgid "Created by"
msgstr "Erstellt von"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
msgid "Translated by"
msgstr "Übersetzt von"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
msgid "License"
msgstr "Lizenz"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
msgid "Welcome to Nicotine+"
msgstr "Willkommen bei Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Wenn dein gewünschter Benutzername bereits vergeben ist, wirst du "
"aufgefordert, ihn zu ändern."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""
"Um dich mit anderen Soulseek-Teilnehmern zu verbinden, muss auf deinem "
"Router ein Empfangsport an deinen Computer weitergeleitet werden."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Wenn dein Empfangsport geschlossen ist, kannst du dich nur mit Benutzern "
"verbinden, deren Empfangsports offen sind."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"Falls erforderlich, wähle unten einen anderen Empfangsport. Dies kann auch "
"später in den Einstellungen vorgenommen werden."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
msgid "Download Files to Folder"
msgstr "Dateien in Ordner herunterladen"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
msgid "Share Folders"
msgstr "Ordner freigeben"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Soulseek-Nutzer werden in der Lage sein, von deinen Freigaben "
"herunterzuladen. Trage zum Soulseek-Netzwerk bei, indem du deine eigenen "
"Dateien freigibst und das, was du von anderen Nutzern heruntergeladen hast, "
"weitergibst."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
msgid "You are ready to use Nicotine+!"
msgstr "Du bist bereit, Nicotine+ zu nutzen!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""
"Soulseek ist ein unverschlüsseltes Protokoll, das nicht für sichere "
"Kommunikation gedacht ist."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Mit einer Spende an Soulseek erhältst du für einen bestimmten Zeitraum "
"Privilegien. Wenn du über diese Privilegien verfügst, werden deine Downloads "
"vor nicht privilegierten Benutzern in die Warteschlange eingereiht."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
msgid "Previous File"
msgstr "Vorherige Datei"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
msgid "Next File"
msgstr "Nächste Datei"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
msgid "Name"
msgstr "Name"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
msgid "Last Speed"
msgstr "Letzte Geschwindigkeit"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
msgid "_Export…"
msgstr "_Exportieren …"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
msgid "Keyboard Shortcuts"
msgstr "Tastenkürzel"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
msgid "General"
msgstr "Allgemein"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr "Verbindung herstellen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr "Verbindung trennen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
msgid "Rescan Shares"
msgstr "Freigaben aktualisieren"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
msgid "Show Log Pane"
msgstr "Protokollfenster anzeigen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
msgid "Confirm Quit"
msgstr "Beenden bestätigen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
msgid "Quit"
msgstr "Beenden"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
msgid "Menus"
msgstr "Menüs"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
msgid "Open Main Menu"
msgstr "Hauptmenü öffnen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
msgid "Open Context Menu"
msgstr "Kontextmenü öffnen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
msgid "Tabs"
msgstr "Tabs"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
msgid "Change Main Tab"
msgstr "Hauptregisterkarte ändern"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
msgid "Go to Previous Secondary Tab"
msgstr "Wechsle zum vorherigen sekundären Tab"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
msgid "Go to Next Secondary Tab"
msgstr "Wechsle zum nächsten untergeordneten Tab"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
msgid "Reopen Closed Secondary Tab"
msgstr "Geschlossenen sekundären Tab wieder öffnen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
msgid "Close Secondary Tab"
msgstr "Sekundären Tab schließen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
msgid "Lists"
msgstr "Listen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
msgid "Copy Selected Cell"
msgstr "Ausgewählte Zelle kopieren"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
msgid "Select All"
msgstr "Alle auswählen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
msgid "Find"
msgstr "Finden"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
msgid "Remove Selected Row"
msgstr "Ausgewählte Zeile entfernen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
msgid "Editing"
msgstr "Bearbeitung"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
msgid "Cut"
msgstr "Ausschneiden"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
msgid "Paste"
msgstr "Einfügen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
msgid "Insert Emoji"
msgstr "Emoji einfügen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
msgid "File Transfers"
msgstr "Dateiübertragungen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
msgid "Resume / Retry Transfer"
msgstr "Übertragung fortsetzen / erneut versuchen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
msgid "Pause / Abort Transfer"
msgstr "Übertragung pausieren / abbrechen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
msgid "Download / Upload To"
msgstr "Herunterladen / Hochladen in"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
msgid "Save List to Disk"
msgstr "Liste auf Festplatte speichern"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
msgid "Refresh"
msgstr "Aktualisieren"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
msgid "Expand / Collapse All"
msgstr "Alle erweitern / Alle zusammenklappen"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
msgid "Back to Parent Folder"
msgstr "Zurück zum übergeordneten Ordner"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
msgid "File Search"
msgstr "Dateisuche"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
msgid "Result Filters"
msgstr "Ergebnisfilter"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
msgid "Current Session"
msgstr "Aktuelle Sitzung"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
msgid "Completed Downloads"
msgstr "Abgeschlossene Downloads"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
msgid "Downloaded Size"
msgstr "Heruntergeladene Größe"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
msgid "Completed Uploads"
msgstr "Abgeschlossene Uploads"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
msgid "Uploaded Size"
msgstr "Hochgeladene Größe"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
msgid "Total"
msgstr "Insgesamt"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
msgid "_Reset…"
msgstr "_Zurücksetzen …"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Wunschlisteneinträge werden in regelmäßigen Abständen automatisch gesucht, "
"um seltene Dateien zu entdecken."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
msgid "Add Wish…"
msgstr "Wunsch hinzufügen …"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
msgid "Clear All…"
msgstr "Alles löschen …"

#: pynicotine/gtkgui/ui/downloads.ui:138
msgid "Clear All Finished/Filtered Downloads"
msgstr "Alle abgeschlossenen/gefilterten Downloads löschen"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
msgid "Clear Finished"
msgstr "Abgeschlossene entfernen"

#: pynicotine/gtkgui/ui/downloads.ui:169
msgid "Clear Specific Downloads"
msgstr "Bestimmte Downloads löschen"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
msgid "Clear _All…"
msgstr "_Alles löschen …"

#: pynicotine/gtkgui/ui/interests.ui:21
msgid "Personal Interests"
msgstr "Persönliche Interessen"

#: pynicotine/gtkgui/ui/interests.ui:40
msgid "Add something you like…"
msgstr "Füge etwas hinzu, das dir gefällt …"

#: pynicotine/gtkgui/ui/interests.ui:62
msgid "Personal Dislikes"
msgstr "Persönliche Abneigungen"

#: pynicotine/gtkgui/ui/interests.ui:80
msgid "Add something you dislike…"
msgstr "Füge etwas hinzu, das du nicht magst …"

#: pynicotine/gtkgui/ui/interests.ui:143
msgid "Refresh Recommendations"
msgstr "Empfehlungen aktualisieren"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
msgid "Main Menu"
msgstr "Hauptmenü"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
msgid "Room…"
msgstr "Raum …"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
msgid "Username…"
msgstr "Benutzername …"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
msgid "Search term…"
msgstr "Suchbegriff …"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Entfernen"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Suchmuster: mit einem Wort = Begriff, ohne ein Wort = -Begriff, Teilwort = "
"*iff"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
msgid "Search Scope"
msgstr "Suchbereich"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
msgid "_Wishlist"
msgstr "_Wunschliste"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
msgid "Configure Searches"
msgstr "Sucheinstellungen konfigurieren"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Gib einen Suchbegriff ein, um nach Dateien zu suchen, die von anderen "
"Nutzern im Soulseek-Netzwerk freigegeben wurden"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
msgid "File Grouping Mode"
msgstr "Datei-Gruppierungsmodus"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
msgid "Configure Downloads"
msgstr "Downloads konfigurieren"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Dateien, die du von anderen Nutzern herunterlädst, werden hier in die "
"Warteschlange gestellt und können nach Bedarf pausiert und fortgesetzt werden"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
msgid "Configure Uploads"
msgstr "Uploads konfigurieren"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Die Versuche der Benutzer, deine freigegebenen Dateien herunterzuladen, "
"werden hier in die Warteschlange gestellt und verwaltet"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
msgid "_Open List"
msgstr "_Liste öffnen"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Öffnet eine lokale Liste von freigegebenen Dateien, die zuvor auf der "
"Festplatte gespeichert wurde"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
msgid "Configure Shares"
msgstr "Freigaben konfigurieren"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Gib den Namen eines Benutzers ein, dessen freigegebene Dateien du "
"durchstöbern möchtest. Du kannst die Liste auch auf der Festplatte speichern "
"und später ansehen."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
msgid "_Personal Profile"
msgstr "_Persönliches Profil"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
msgid "Configure Account"
msgstr "Konto konfigurieren"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Gib den Namen eines Benutzers ein, um dessen Benutzerbeschreibung, "
"Informationen und Profilbild anzuzeigen"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
msgid "Chat _History"
msgstr "Chatverlauf"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
msgid "Configure Chats"
msgstr "Chats konfigurieren"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr ""
"Gib den Namen eines Benutzers ein, um eine private Textunterhaltung mit ihm "
"zu beginnen"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
msgid "_Message All"
msgstr "_Nachricht an alle"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
msgid "Configure Ignored Users"
msgstr "Ignorierte Benutzer konfigurieren"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Füge Benutzer als Freunde hinzu, um mit ihnen bestimmte Ordner zu teilen und "
"Benachrichtigungen zu erhalten, wenn sie online sind"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
msgid "Join or create room…"
msgstr "Tritt einem Raum bei oder erstelle einen …"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Tritt einem bestehenden Chatraum bei oder erstelle einen neuen Raum, um mit "
"anderen Nutzern im Soulseek-Netzwerk zu chatten"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
msgid "Configure User Profile"
msgstr "Benutzerprofil konfigurieren"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
msgid "Connections"
msgstr "Verbindungen"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
msgid "Downloading (Speed / Active Users)"
msgstr "Download (Geschwindigkeit / Aktive Nutzer)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
msgid "Uploading (Speed / Active Users)"
msgstr "Hochladen (Geschwindigkeit / Aktive Nutzer)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
msgid "Search chat history…"
msgstr "Durchsuche Chatverlauf …"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
msgid "Download Speed Limits"
msgstr "Download-Geschwindigkeitsbegrenzungen"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
msgid "Unlimited download speed"
msgstr "Unbegrenzte Download-Geschwindigkeit"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
msgid "Use download speed limit (KiB/s):"
msgstr "Download-Geschwindigkeitsbegrenzungen verwenden (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Verwende alternative Download-Geschwindigkeitsbegrenzungen (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
msgid "Search rooms…"
msgstr "Räume durchsuchen …"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
msgid "Refresh Rooms"
msgstr "Räume aktualisieren"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
msgid "_Show feed of public chat room messages"
msgstr "_Nachrichtenfeed des öffentlichen Chatraums anzeigen"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
msgid "_Accept private room invitations"
msgstr "_Einladungen zu privaten Raum annehmen"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Schreibe eine einzelne Nachricht, die von anderen Chatraum-Benutzern später "
"gelesen werden kann. Neueste Nachrichten werden oben angezeigt."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
msgid "Set wall message…"
msgstr "Pinnwandnachricht festlegen …"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
msgid "Search Result Filters"
msgstr "Filter für Suchergebnisse"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Suchergebnisfilter helfen dabei, die angezeigten Suchergebnisse gezielt zu "
"verfeinern."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Jede Suchergebnisliste hat ihren eigenen Filter, der durch Umschalten des "
"Buttons \"Ergebnisfilter\" angezeigt werden kann. Ein Filter besteht aus "
"mehreren Feldern, die alle angewendet werden, wenn man in einem beliebigen "
"Feld Enter drückt. Die Filterung wird sofort auf bereits erhaltene "
"Ergebnisse angewendet und auch auf solche, die noch eintreffen werden."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Wie der Name schon sagt, kann ein Suchergebnisfilter deine ursprüngliche "
"Suche nicht erweitern, er kann sie nur einschränken. Um deine Suchbegriffe "
"zu erweitern oder zu ändern, führe eine neue Suche durch."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
msgid "Result Filter Usage"
msgstr "Ergebnis Filterverwendung"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
msgid "Include Text"
msgstr "Text einschließen"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
msgid "Files, folders and usernames containing this text will be shown."
msgstr ""
"Dateien, Ordner und Benutzernamen, die diesen Text enthalten, werden "
"angezeigt."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Die Groß-/Kleinschreibung spielt keine Rolle, aber die Wortreihenfolge ist "
"wichtig: 'Instrumental Remix' zeigt kein 'Remix Instrumental' an"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Verwende | (senkrechter Strich), um mehrere exakte Phrasen zu trennen. "
"Beispiel:\n"
"    Remix|Dub Mix|Instrumental"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
msgid "Exclude Text"
msgstr "Text ausschließen"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"Wie oben, aber Dateien, Ordner und Benutzernamen werden herausgefiltert, "
"wenn der Text übereinstimmt."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
msgid "Filters files based upon their file extension."
msgstr "Filtert Dateien auf der Grundlage ihrer Dateierweiterung."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Es können mehrere Dateierweiterungen angegeben werden, was wiederum dazu "
"führt, dass weitere Ergebnisse in der Ergebnisliste angezeigt werden. "
"Beispiel:\n"
"    flac wav ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"Es ist auch möglich, den Filter umzukehren, indem Dateiendungen, die in den "
"Ergebnissen nicht erscheinen sollen, mit einem Ausrufezeichen versehen "
"werden.\n"
"    Beispiel: !mp3 !jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
msgid "File Size"
msgstr "Dateigröße"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
msgid "Filters files based upon their file size."
msgstr "Filtert Dateien basierend auf ihrer Dateigröße."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"Standardmäßig wird die Einheit Bytes (B) verwendet, und Dateien, die größer "
"oder gleich (>=) dem angegebenen Wert sind, werden ausgewählt."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Hänge b, k, m oder g (alternativ kib, mib oder gib) an, um die Einheiten "
"Byte, Kibibyte, Mebibyte oder Gibibyte anzugeben:\n"
"    20m zeigt Dateien an, die größer als 20 MiB (Mebibyte) sind."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Setze ein \"=\" vor einen Wert, um eine exakte Übereinstimmung festzulegen:\n"
"    =1024 entspricht Dateien, die exakt 1 KiB (Kibibyte) groß sind."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Füge ein \"!\" vor einen Wert, um Dateien einer bestimmten Größe "
"auszuschließen:\n"
"    !30.5m blendet Dateien aus, die 30.5 MiB (Mebibyte) groß sind."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Füge < oder > vor einen Wert, um Dateien zu finden, die kleiner bzw. größer "
"als der angegebene Wert sind. Verwende ein Leerzeichen zwischen den "
"Bedingungen, um einen Bereich festzulegen:\n"
"    >10.5m <1g zeigt Dateien an, die größer als 10.5 MiB, aber kleiner als 1 "
"GiB sind."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Die bekannteren Varianten \"kb\", \"mb\" und \"gb\" können auch für Kilobyte-"
", Megabyte- und Gigabyte-Einheiten verwendet werden."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
msgid "Filters files based upon their bitrate."
msgstr "Filtert Dateien basierend auf ihrer Bitrate."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Werte müssen ausschließlich als numerische Ziffern eingegeben werden. Die "
"Einheit ist immer Kb/s (Kilobit pro Sekunde)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Wie bei der Dateigröße (oben) können Operatoren =, !, <, >, <= oder >= "
"verwendet werden, und es können mehrere Bedingungen kombiniert werden, "
"beispielsweise um Dateien mit einer Bitrate von mindestens 256 Kb/s und "
"höchstens 1411 Kb/s anzuzeigen:\n"
"    256 <= 1411"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
msgid "Filters files based upon their duration."
msgstr "Filtert Dateien basierend auf ihrer Dauer."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"Standardmäßig werden Dateien ausgewählt, die länger als oder gleich (>=) der "
"eingegebenen Dauer sind, sofern kein Operator (=, !, <=, < oder >) verwendet "
"wird."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Gib einen Rohwert in Sekunden ein oder verwende die Zeitformate MM:SS und "
"HH:MM:SS:\n"
"    =53 zeigt Dateien an, die ungefähr 53 Sekunden lang sind.\n"
"    >5:30 zeigt Dateien an, die länger als 5 Minuten und 30 Sekunden sind.\n"
"    <5:30:00 zeigt Dateien an, die kürzer als 5 Stunden und 30 Minuten sind."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Mehrere Bedingungen können angegeben werden:\n"
"    >6:00 <12:00 zeigt Dateien an, die zwischen 6 und 12 Minuten lang sind.\n"
"    !9:54 !8:43 !7:32 blendet bestimmte Dateien aus den Ergebnissen aus..\n"
"    =5:34 =4:23 =3:05 schließt Dateien mit bestimmten Dauern ein."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Filtert Dateien basierend auf dem geografischen Standort der Benutzer gemäß "
"den in ISO 3166-2 definierten Ländercodes:\n"
"    US zeigt nur Ergebnisse von Benutzern an, deren IP-Adressen in den "
"Vereinigten Staaten liegen.\n"
"    !GB blendet Ergebnisse aus, die von Benutzern aus Großbritannien stammen."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Mehrere Länder können mit Kommas oder Leerzeichen angegeben werden."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
msgid "Free Slot"
msgstr "Freier Slot"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Zeige nur die Ergebnisse von Benutzern an, die mindestens einen freien "
"Upload-Slot haben, also Dateien, die sofort verfügbar sind."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
msgid "Upload Speed Limits"
msgstr "Upload-Geschwindigkeitsbegrenzung"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
msgid "Unlimited upload speed"
msgstr "Unbegrenzte Upload-Geschwindigkeit"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
msgid "Use upload speed limit (KiB/s):"
msgstr "Upload-Geschwindigkeitsbegrenzung (KiB/s) verwenden:"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Verwende alternative Upload-Geschwindigkeitsbegrenzung (KiB/s):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
msgid "Private Chat Command Help"
msgstr "Befehlshilfe im privaten Chat"

#: pynicotine/gtkgui/ui/search.ui:7
msgid "Include text…"
msgstr "Text einschließen …"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtere Ergebnisse, deren Dateipfade den angegebenen Text enthalten. Mehrere "
"Phrasen und Wörter können angegeben werden, z. B. genauer "
"Ausdruck|Musik|Begriff|genauer Ausdruck zwei"

#: pynicotine/gtkgui/ui/search.ui:18
msgid "Exclude text…"
msgstr "Text ausschließen …"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Schließt Ergebnisse aus, deren Dateipfade den angegebenen Text enthalten. "
"Mehrere Phrasen und Wörter können angegeben werden, z. B. genauer "
"Ausdruck|Musik|Begriff|genauer Ausdruck zwei"

#: pynicotine/gtkgui/ui/search.ui:29
msgid "File type…"
msgstr "Dateityp …"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Dateityp, z.B. flac wav oder !mp3 !m4a"

#: pynicotine/gtkgui/ui/search.ui:40
msgid "File size…"
msgstr "Dateigröße …"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
msgid "File size, e.g. >10.5m <1g"
msgstr "Dateigröße, z. B. >10.5m <1g"

#: pynicotine/gtkgui/ui/search.ui:51
msgid "Bitrate…"
msgstr "Bitrate …"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
msgid "Bitrate, e.g. 256 <1412"
msgstr "Bitrate, z. B. 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
msgid "Duration…"
msgstr "Dauer …"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Dauer, z. B. >6:00 <12:00 !6:54"

#: pynicotine/gtkgui/ui/search.ui:73
msgid "Country code…"
msgstr "Ländercode …"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Ländercode, z. B. US ES oder !DE !GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Verbiete Benutzern den Zugriff auf deine freigegebenen Dateien, basierend "
"auf deren Benutzernamen, IP-Adresse oder Land."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Ländercodes, die blockiert werden sollen (durch Komma getrennt):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
msgid "Codes must be in ISO 3166-2 format."
msgstr "Die Ländercodes müssen im Format ISO 3166-2 angegeben werden."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
msgid "Use custom geo block message:"
msgstr "Verwende eine benutzerdefinierte Geo-Block-Nachricht:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Benutzerdefinierte Blockierungsnachricht verwenden:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
msgid "IP Addresses"
msgstr "IP-Adressen"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
msgid "Restore previously open private chats on startup"
msgstr "Beim Start zuvor geöffnete private Chats wiederherstellen"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
msgid "Enable spell checker"
msgstr "Rechtschreibprüfung aktivieren"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
msgid "Enable CTCP-like private message responses (client version)"
msgstr "Aktiviere CTCP-ähnliche private Nachrichtenantworten (Client-Version)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
msgid "Number of recent private chat messages to show:"
msgstr "Anzahl der kürzlich angezeigten privaten Chat-Nachrichten:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
msgid "Number of recent chat room messages to show:"
msgstr "Anzahl der anzuzeigenden letzten Chatraumnachrichten:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
msgid "Chat Completion"
msgstr "Chat-Vervollständigung"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
msgid "Enable tab-key completion"
msgstr "Aktiviere die Tab-Tasten-Vervollständigung"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
msgid "Enable completion drop-down list"
msgstr "Aktiviere die Vervollständigungs-Dropdown-Liste"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
msgid "Minimum characters required to display drop-down:"
msgstr ""
"Mindestanzahl an Zeichen, die erforderlich ist, um das Dropdown-Menü "
"anzuzeigen:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
msgid "Allowed chat completions:"
msgstr "Erlaubte Chat-Vervollständigungen:"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
msgid "Buddy names"
msgstr "Namen der Freunde"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
msgid "Chat room usernames"
msgstr "Benutzernamen im Chatraum"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
msgid "Room names"
msgstr "Raumnamen"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
msgid "Commands"
msgstr "Befehle"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
msgid "Timestamps"
msgstr "Zeitstempel"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
msgid "Private chat format:"
msgstr "Privates Chat-Format:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "Standard"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
msgid "Chat room format:"
msgstr "Chatraumformat:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
msgid "Text-to-Speech"
msgstr "Text-zu-Sprache"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
msgid "Enable Text-to-Speech"
msgstr "Text-zu-Sprache aktivieren"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
msgid "Text-to-Speech command:"
msgstr "Befehl für Text-zu-Sprache:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
msgid "Private chat message:"
msgstr "Private Nachricht:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
msgid "Chat room message:"
msgstr "Nachricht im Chatraum:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
msgid "Censor"
msgstr "Zensur"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
msgid "Enable censoring of text patterns"
msgstr "Aktiviere die Zensur von Textmustern"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
msgid "Auto-Replace"
msgstr "Automatisch ersetzen"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
msgid "Enable automatic replacement of words"
msgstr "Aktiviere die automatische Wortersetzung"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr ""
"Abgeschlossene/gefilterte Downloads aus der Transferliste automatisch löschen"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
msgid "Store completed downloads in username subfolders"
msgstr ""
"Abgeschlossene Downloads in Unterordnern, die nach dem Benutzernamen benannt "
"sind, speichern"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
msgid "Double-click action for downloads:"
msgstr "Doppelklick-Aktion für Downloads:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
msgid "Allow users to send you any files:"
msgstr "Erlaube Nutzern, dir beliebige Dateien zu senden:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
msgid "Folders"
msgstr "Ordner"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
msgid "Finished downloads:"
msgstr "Abgeschlossene Downloads:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
msgid "Incomplete downloads:"
msgstr "Unvollständige Downloads:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
msgid "Received files:"
msgstr "Empfangene Dateien:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "Ereignisse"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
msgid "Run command after file download finishes ($ for file path):"
msgstr ""
"Führe den Befehl nach Beendigung des Dateidownloads aus ($ steht für den "
"Dateipfad):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"Führe den Befehl aus, nachdem der Download des Ordners abgeschlossen ist ($ "
"steht für den Ordnerpfad):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
msgid "Download Filters"
msgstr "Downloadfilter"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
msgid "Enable download filters"
msgstr "Downloadfilter aktivieren"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Hinzufügen"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Standardwerte laden"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
msgid "Verify Filters"
msgstr "Überprüfe Filter"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
msgid "Unverified"
msgstr "Ungeprüft"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Ignoriere Chat-Nachrichten und Suchergebnisse von Benutzern, basierend auf "
"Benutzername oder IP-Adresse."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Chaträume standardmäßig protokollieren"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Privaten Chat standardmäßig protokollieren"

#: pynicotine/gtkgui/ui/settings/log.ui:142
msgid "Log transfers to file"
msgstr "Übertragungen in Datei protokollieren"

#: pynicotine/gtkgui/ui/settings/log.ui:166
msgid "Log debug messages to file"
msgstr "Debug-Meldungen in Datei protokollieren"

#: pynicotine/gtkgui/ui/settings/log.ui:190
msgid "Log timestamp format:"
msgstr "Protokoll-Zeitstempelformat:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
msgid "Folder Locations"
msgstr "Ordnerpfade"

#: pynicotine/gtkgui/ui/settings/log.ui:245
msgid "Chatroom logs folder:"
msgstr "Ordner für Chatroom-Protokolle:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
msgid "Private chat logs folder:"
msgstr "Ordner für private Chat-Protokolle:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
msgid "Transfer logs folder:"
msgstr "Ordner für Übertragungsprotokolle:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
msgid "Debug logs folder:"
msgstr "Ordner für Debug-Protokolle:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Melde dich mit einem bestehenden Soulseek-Konto an oder erstelle ein neues. "
"Beachte, dass Benutzernamen die Groß- und Kleinschreibung unterscheiden und "
"einzigartig sind."

#: pynicotine/gtkgui/ui/settings/network.ui:118
msgid "Public IP address:"
msgstr "Öffentliche IP-Adresse:"

#: pynicotine/gtkgui/ui/settings/network.ui:159
msgid "Listening port:"
msgstr "Lauschport:"

#: pynicotine/gtkgui/ui/settings/network.ui:185
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Automatisches Weiterleiten des Lauschports (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
msgid "Away Status"
msgstr "Abwesenheitsstatus"

#: pynicotine/gtkgui/ui/settings/network.ui:228
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr ""
"Minuten der Inaktivität, bevor der Status auf Abwesend gesetzt wird (0 zum "
"Deaktivieren):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
msgid "Auto-reply message when away:"
msgstr "Automatische Antwortnachricht bei Abwesenheit:"

#: pynicotine/gtkgui/ui/settings/network.ui:299
msgid "Auto-connect to server on startup"
msgstr "Automatische Verbindung zum Server beim Start"

#: pynicotine/gtkgui/ui/settings/network.ui:323
msgid "Soulseek server:"
msgstr "Soulseek-Server:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Bindet Verbindungen an eine bestimmte Netzwerkschnittstelle – nützlich "
"beispielsweise, um sicherzustellen, dass stets ein VPN verwendet wird. Lass "
"dieses Feld leer, um jede verfügbare Schnittstelle zu nutzen. Ändere diesen "
"Wert nur, wenn du weißt, was du tust."

#: pynicotine/gtkgui/ui/settings/network.ui:351
msgid "Network interface:"
msgstr "Netzwerkschnittstelle:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"Jetzt läuft ermöglicht es dir, im Chat anzuzeigen, was dein Mediaplayer "
"gerade abspielt – verwende dazu den /now-Befehl."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
msgid "Other"
msgstr "Andere"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
msgid "Now Playing Format"
msgstr "Jetzt läuft-Format"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
msgid "Now Playing message format:"
msgstr "Jetzt läuft-Nachrichtenformat:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
msgid "Test Configuration"
msgstr "Test-Konfiguration"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
msgid "Enable plugins"
msgstr "Plugins einschalten"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
msgid "Add Plugins"
msgstr "Plugins hinzufügen"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
msgid "_Add Plugins"
msgstr "_Plugins hinzufügen"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
msgid "Settings"
msgstr "Einstellungen"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
msgid "_Settings"
msgstr "_Einstellungen"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
msgid "Version:"
msgstr "Version:"

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
msgid "Created by:"
msgstr "Erstellt von:"

#: pynicotine/gtkgui/ui/settings/search.ui:51
msgid "Enable search history"
msgstr "Suchverlauf aktivieren"

#: pynicotine/gtkgui/ui/settings/search.ui:70
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Privat geteilte Dateien, die für alle sichtbar gemacht wurden, werden mit '"
"[PRIVAT]' gekennzeichnet und können nicht heruntergeladen werden, bis der "
"Uploader ausdrücklich seine Erlaubnis erteilt hat. Frag ihn bitte freundlich."

#: pynicotine/gtkgui/ui/settings/search.ui:76
msgid "Show privately shared files in search results"
msgstr "Privat freigegebene Dateien in den Suchergebnissen anzeigen"

#: pynicotine/gtkgui/ui/settings/search.ui:106
msgid "Limit number of results per search:"
msgstr "Begrenze die Anzahl der Ergebnisse pro Suche:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
msgid "Result Filter Help"
msgstr "Ergebnisfilter-Hilfe"

#: pynicotine/gtkgui/ui/settings/search.ui:177
msgid "Enable search result filters by default"
msgstr "Suchergebnisfilter standardmäßig aktivieren"

#: pynicotine/gtkgui/ui/settings/search.ui:211
msgid "Include:"
msgstr "Einschließen:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
msgid "Exclude:"
msgstr "Ausschließen:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
msgid "File Type:"
msgstr "Dateityp:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "Größe:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Bitrate:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
msgid "Duration:"
msgstr "Dauer:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
msgid "Country Code:"
msgstr "Ländercode:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
msgid "Only show results from users with an available upload slot."
msgstr "Zeige nur Ergebnisse von Benutzern mit einem verfügbaren Upload-Slot."

#: pynicotine/gtkgui/ui/settings/search.ui:430
msgid "Network Searches"
msgstr "Netzwerksuchen"

#: pynicotine/gtkgui/ui/settings/search.ui:452
msgid "Respond to search requests from other users"
msgstr "Auf Suchanfragen anderer Nutzer antworten"

#: pynicotine/gtkgui/ui/settings/search.ui:486
msgid "Searches shorter than this number of characters will be ignored:"
msgstr ""
"Suchanfragen, die kürzer als diese Anzahl von Zeichen sind, werden ignoriert:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
msgid "Maximum search results to send per search request:"
msgstr "Maximale Anzahl an Suchergebnissen pro Suchanfrage:"

#: pynicotine/gtkgui/ui/settings/search.ui:578
msgid "Clear Search History"
msgstr "Suchverlauf löschen"

#: pynicotine/gtkgui/ui/settings/search.ui:626
msgid "Clear Filter History"
msgstr "Filterverlauf löschen"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Beim Start wird der Inhalt deiner freigegebenen Ordner automatisch neu "
"gescannt. Wenn diese Funktion deaktiviert ist, werden deine Freigaben nur "
"aktualisiert, wenn du manuell einen erneuten Scan durchführst."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Freigaben beim Start neu scannen"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr "Für jeden sichtbar:"

#: pynicotine/gtkgui/ui/settings/shares.ui:82
msgid "Buddy shares"
msgstr "Freunde-Freigaben"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
msgid "Trusted shares"
msgstr "Vertrauenswürdige Freigaben"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr ""
"Automatisches Löschen von abgeschlossenen/abgebrochenen Uploads aus der "
"Übertragungsliste"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
msgid "Double-click action for uploads:"
msgstr "Doppelklick-Aktion für Uploads:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
msgid "Limit upload speed:"
msgstr "Upload-Geschwindigkeit begrenzen auf:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
msgid "Per transfer"
msgstr "Pro Übertragung"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
msgid "Total transfers"
msgstr "Übertragungen Insgesamt"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
msgid "Upload Slots"
msgstr "Upload-Slots"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Round Robin: Dateien werden zyklisch an die in der Warteschlange wartenden "
"Nutzer hochgeladen.\n"
"First In, First Out: Dateien werden in der Reihenfolge hochgeladen, in der "
"sie in die Warteschlange eingereiht wurden."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
msgid "Upload queue type:"
msgstr "Typ der Upload-Warteschlange:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr ""
"Upload-Slots zuweisen, bis die Gesamtgeschwindigkeit (KiB/s) erreicht ist:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
msgid "Fixed number of upload slots:"
msgstr "Feste Anzahl von Upload-Slots:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
msgid "Prioritize all buddies"
msgstr "Priorisiere alle Freunde"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
msgid "Queue Limits"
msgstr "Warteschlangenbeschränkungen"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
msgid "Maximum number of queued files per user:"
msgstr "Maximale Anzahl von Dateien in der Warteschlange pro Benutzer:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr ""
"Maximale Gesamtgröße der in der Warteschlange befindlichen Dateien pro "
"Benutzer (MiB):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
msgid "Limits do not apply to buddies"
msgstr "Beschränkungen gelten nicht für Freunde"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"Instanzen von $ werden durch die URL ersetzt. Standard-Systemanwendungen "
"kommen zum Einsatz, falls kein Protokoll konfiguriert wurde."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
msgid "File manager command:"
msgstr "Dateimanager-Befehl:"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
msgid "Reset Picture"
msgstr "Bild zurücksetzen"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
msgid "Self Description"
msgstr "Selbstbeschreibung"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""
"Fügen Angaben hinzu, die jeder sehen soll, wie beispielsweise eine kurze "
"Beschreibung, hilfreiche Tipps oder Richtlinien zum Herunterladen deiner "
"Freigaben."

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
msgid "Picture:"
msgstr "Bild:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
msgid "Prefer dark mode"
msgstr "Bevorzuge den Dunkelmodus"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
msgid "Use header bar"
msgstr "Kopfleiste verwenden"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
msgid "Display tray icon"
msgstr "Symbol im Infobereich anzeigen"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
msgid "Minimize to tray on startup"
msgstr "Beim Start in den Infobereich minimieren"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
msgid "Language (requires a restart):"
msgstr "Sprache (erfordert einen Neustart):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
msgid "When closing window:"
msgstr "Beim Schließen des Fensters:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
msgid "Notifications"
msgstr "Benachrichtigungen"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
msgid "Enable sound for notifications"
msgstr "Aktiviere Ton für Benachrichtigungen"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
msgid "Show notification for private chats and mentions in the window title"
msgstr ""
"Zeige Benachrichtigungen für private Chats und Erwähnungen im Fenstertitel an"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
msgid "Show notifications for:"
msgstr "Benachrichtigungen anzeigen für:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
msgid "Finished file downloads"
msgstr "Fertige Datei-Downloads"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
msgid "Finished folder downloads"
msgstr "Fertige Ordner-Downloads"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
msgid "Private messages"
msgstr "Private Nachrichten"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
msgid "Chat room messages"
msgstr "Nachrichten im Chatraum"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
msgid "Chat room mentions"
msgstr "Chatraum-Erwähnungen"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
msgid "Wishlist results found"
msgstr "Ergebnisse in der Wunschliste gefunden"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
msgid "Restore the previously active main tab at startup"
msgstr "Stelle beim Start den zuvor aktiven Haupt-Tab wieder her"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
msgid "Close-buttons on secondary tabs"
msgstr "Schließen-Schaltflächen auf sekundären Tabs"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
msgid "Regular tab label color:"
msgstr "Farbe der normalen Tab-Beschriftung:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
msgid "Changed tab label color:"
msgstr "Farbe der Tab-Beschriftung geändert:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
msgid "Highlighted tab label color:"
msgstr "Hervorgehobene Tab-Bezeichnungsfarbe:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr "Position Freunde-Liste:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
msgid "Visible main tabs:"
msgstr "Sichtbare Hauptregisterkarten:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
msgid "Tab bar positions:"
msgstr "Positionen der Tab-Leiste:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
msgid "Main tabs"
msgstr "Hauptregisterkarten"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
msgid "Show reverse file paths (requires a restart)"
msgstr "Umgekehrte Dateipfade anzeigen (erfordert einen Neustart)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
msgid "Show exact file sizes (requires a restart)"
msgstr "Exakte Dateigrößen anzeigen (erfordert einen Neustart)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
msgid "List text color:"
msgstr "Textfarbe der Liste:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
msgid "Enable colored usernames"
msgstr "Farbige Benutzernamen aktivieren"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
msgid "Chat username appearance:"
msgstr "Aussehen des Chat-Benutzernamens:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
msgid "Remote text color:"
msgstr "Remote-Textfarbe:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
msgid "Local text color:"
msgstr "Lokale Textfarbe:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
msgid "Command output text color:"
msgstr "Befehlsausgabe-Textfarbe:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
msgid "/me action text color:"
msgstr "/me Aktionstextfarbe:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
msgid "Highlighted text color:"
msgstr "Hervorgehobene Textfarbe:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
msgid "URL link text color:"
msgstr "URL-Link-Textfarbe:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
msgid "User Statuses"
msgstr "Benutzerstatus"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
msgid "Online color:"
msgstr "Online-Farbe:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
msgid "Away color:"
msgstr "Farbe bei Abwesenheit:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
msgid "Offline color:"
msgstr "Farbe für Offline:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
msgid "Text Entries"
msgstr "Texteinträge"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
msgid "Text entry background color:"
msgstr "Hintergrundfarbe der Texteingabe:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
msgid "Text entry text color:"
msgstr "Texteingabefarbe:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
msgid "Fonts"
msgstr "Schriftarten"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
msgid "Global font:"
msgstr "Globale Schriftart:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
msgid "List font:"
msgstr "Schriftart für Listen:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
msgid "Text view font:"
msgstr "Schriftart der Textansicht:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
msgid "Chat font:"
msgstr "Schriftart für Chats:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
msgid "Transfers font:"
msgstr "Schriftart für Übertragungen:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
msgid "Search font:"
msgstr "Schriftart für Suche:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
msgid "Browse font:"
msgstr "Schriftart für Durchsuchen:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
msgid "Icons"
msgstr "Symbole"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
msgid "Icon theme folder:"
msgstr "Icon-Themenordner:"

#: pynicotine/gtkgui/ui/uploads.ui:86
msgid "Abort User(s)"
msgstr "Benutzer trennen"

#: pynicotine/gtkgui/ui/uploads.ui:116
msgid "Ban User(s)"
msgstr "Benutzer sperren"

#: pynicotine/gtkgui/ui/uploads.ui:138
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Alle abgeschlossenen/abgebrochenen Uploads löschen"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
msgid "Message All"
msgstr "Nachricht an alle"

#: pynicotine/gtkgui/ui/uploads.ui:199
msgid "Clear Specific Uploads"
msgstr "Bestimmte Uploads löschen"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
msgid "Save Shares List to Disk"
msgstr "Freigabeliste auf Festplatte speichern"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
msgid "Refresh Files"
msgstr "Dateien aktualisieren"

#: pynicotine/gtkgui/ui/userinfo.ui:101
msgid "Edit Profile"
msgstr "Profil bearbeiten"

#: pynicotine/gtkgui/ui/userinfo.ui:149
msgid "Shared Files"
msgstr "Freigegebene Dateien"

#: pynicotine/gtkgui/ui/userinfo.ui:203
msgid "Upload Speed"
msgstr "Upload-Geschwindigkeit"

#: pynicotine/gtkgui/ui/userinfo.ui:230
msgid "Free Upload Slots"
msgstr "Freie Upload-Slots"

#: pynicotine/gtkgui/ui/userinfo.ui:284
msgid "Queued Uploads"
msgstr "Uploads in der Warteschlange"

#: pynicotine/gtkgui/ui/userinfo.ui:354
msgid "Edit Interests"
msgstr "Interessen bearbeiten"

#: pynicotine/gtkgui/ui/userinfo.ui:624
msgid "_Gift Privileges…"
msgstr "_Privilegien verschenken …"

#: pynicotine/gtkgui/ui/userinfo.ui:663
msgid "_Refresh Profile"
msgstr "Profil _Aktualisieren"

#: pynicotine/plugins/core_commands/PLUGININFO:3
msgid "Nicotine+ Commands"
msgstr "Nikotin+ Befehle"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Nicotine+ Team"

#~ msgid "Listening port (requires a restart):"
#~ msgstr "Lauschport (erfordert einen Neustart):"

#~ msgid "Network interface (requires a restart):"
#~ msgstr "Netzwerkschnittstelle (erfordert einen Neustart):"

#~ msgid "Invalid Password"
#~ msgstr "Ungültiges Passwort"

#~ msgid "Change _Login Details"
#~ msgstr "_Anmeldedaten ändern"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i privilegierte Benutzer"

#~ msgid "_Set Up…"
#~ msgstr "_Einrichten …"

#~ msgid "Queued search result text color:"
#~ msgstr "Farbe des Suchergebnistextes in der Warteschlange:"

#, python-format
#~ msgid "Failed to fetch the shared folder %(folder)s: %(error)s"
#~ msgstr "Fehler beim Abrufen des freigegebenen Ordners %(folder)s: %(error)s"

#~ msgid "_Clear"
#~ msgstr "Entfernen"

#, python-format
#~ msgid "Can't save %(filename)s: %(error)s"
#~ msgstr "Kann %(filename)s nicht speichern: %(error)s"

#~ msgid "Trusted Buddies"
#~ msgstr "Vertrauenswürdige Freunde"

#~ msgid "Quit program"
#~ msgstr "Programm beenden"

#~ msgid "Username:"
#~ msgstr "Benutzername:"

#~ msgid "Re_commendations for Item"
#~ msgstr "Empfehlungen für Artikel"

#~ msgid "_Remove Item"
#~ msgstr "_Artikel entfernen"

#~ msgid "_Remove"
#~ msgstr "_Entfernen"

#~ msgid "Send M_essage"
#~ msgstr "_Nachricht senden"

#~ msgid "Send Message"
#~ msgstr "Nachricht senden"

#~ msgid "View User Profile"
#~ msgstr "Benutzerprofil anzeigen"

#~ msgid "Start Messaging"
#~ msgstr "Nachrichten senden starten"

#~ msgid "Enter the name of the user whom you want to send a message:"
#~ msgstr ""
#~ "Gib den Namen des Benutzers ein, dem du eine Nachricht senden möchtest:"

#~ msgid "_Message"
#~ msgstr "_Nachricht"

#~ msgid "Enter the name of the user whose profile you want to see:"
#~ msgstr "Gib den Namen des Benutzers ein, dessen Profil du sehen möchtest:"

#~ msgid "_View Profile"
#~ msgstr "_Profil anzeigen"

#~ msgid "Enter the name of the user whose shares you want to see:"
#~ msgstr ""
#~ "Gib den Namen des Benutzers ein, dessen Freigaben du sehen möchtest:"

#~ msgid "_Browse"
#~ msgstr "_Blättern"

#~ msgid "Password"
#~ msgstr "Passwort"

#~ msgid "Download File"
#~ msgstr "Datei herunterladen"

#~ msgid "Refresh Similar Users"
#~ msgstr "Ähnliche Benutzer aktualisieren"

#~ msgid "Enter the username of the person whose files you want to see"
#~ msgstr ""
#~ "Gib den Benutzernamen der Person ein, deren Dateien du sehen möchtest"

#~ msgid "Enter the username of the person whose information you want to see"
#~ msgstr ""
#~ "Gib den Benutzernamen der Person ein, deren Informationen du sehen "
#~ "möchtest"

#~ msgid "Enter the username of the person you want to send a message to"
#~ msgstr ""
#~ "Gib den Benutzernamen der Person ein, der du eine Nachricht senden "
#~ "möchtest"

#~ msgid "Enter the username of the person you want to add to your buddy list"
#~ msgstr ""
#~ "Gib den Benutzernamen der Person ein, die du zu deiner Freundesliste "
#~ "hinzufügen möchtest"

#~ msgid ""
#~ "Enter the name of a room you want to join. If the room doesn't exist, it "
#~ "will be created."
#~ msgstr ""
#~ "Gib den Namen eines Raums ein, den du beitreten möchtest. Wenn der Raum "
#~ "nicht existiert, wird er erstellt."

#~ msgid "Show Log History Pane"
#~ msgstr "Protokollverlaufsfenster anzeigen"

#~ msgid "Save _Picture"
#~ msgstr "_Bild speichern"

#, python-format
#~ msgid ""
#~ "Filtered out incorrect search result %(filepath)s from user %(user)s for "
#~ "search query \"%(query)s\""
#~ msgstr ""
#~ "Falsches Suchergebnis %(filepath)s von Benutzer %(user)s für Suchanfrage "
#~ "\"%(query)s\" herausgefiltert"

#~ msgid ""
#~ "Certain clients don't send search results if special characters are "
#~ "included."
#~ msgstr ""
#~ "Bestimmte Clients senden keine Suchergebnisse, wenn Sonderzeichen "
#~ "enthalten sind."

#~ msgid "Remove special characters from search terms"
#~ msgstr "Sonderzeichen aus Suchbegriffen entfernen"

#, python-format
#~ msgid "Trouble executing '%s'"
#~ msgstr "Probleme bei der Ausführung von '%s'"

#, python-format
#~ msgid "Trouble executing on folder: %s"
#~ msgstr "Probleme beim Ausführen im Ordner: %s"

#~ msgid "Disallowed extension"
#~ msgstr "Unzulässige Erweiterung"

#~ msgid "Too many files"
#~ msgstr "Zu viele Dateien"

#~ msgid "Too many megabytes"
#~ msgstr "Zu viele Megabytes"

#~ msgid "Server does not permit performing wishlist searches at this time"
#~ msgstr "Server erlaubt derzeit keine Wunschlistensuche"

#~ msgid ""
#~ "File transfer speeds depend on users you are downloading from. Certain "
#~ "users will be faster, while others will be slow."
#~ msgstr ""
#~ "Die Geschwindigkeit der Dateiübertragung hängt von den Nutzern ab, von "
#~ "denen du die Dateien herunterladest. Bestimmte Nutzer sind schneller, "
#~ "während andere langsam sind."

#~ msgid "Started Downloads"
#~ msgstr "Gestartete Downloads"

#~ msgid "Started Uploads"
#~ msgstr "Gestartete Uploads"

#~ msgid "Replace censored letters with:"
#~ msgstr "Zensierte Zeichen ersetzen durch:"

#~ msgid "Censored Patterns"
#~ msgstr "Zensierte Muster"

#~ msgid "Replacements"
#~ msgstr "Ersetzung"

#~ msgid ""
#~ "If a user on the Soulseek network searches for a file that exists in your "
#~ "shares, search results will be sent to the user."
#~ msgstr ""
#~ "Wenn ein Benutzer im Soulseek-Netzwerk nach einer Datei sucht, die in "
#~ "deine Freigaben vorhanden ist, werden die Suchergebnisse dem Benutzer "
#~ "gesendet."

#~ msgid "Send to Player"
#~ msgstr "An Spieler senden"

#~ msgid "Send to _Player"
#~ msgstr "An _Spieler senden"

#~ msgid "_Open in File Manager"
#~ msgstr "In Datei-Manager öffnen"

#~ msgid "Media player command:"
#~ msgstr "Media-Player-Befehl:"

#, python-format
#~ msgid ""
#~ "Unable to save download to username subfolder, falling back to default "
#~ "download folder. Error: %s"
#~ msgstr ""
#~ "Der Download kann nicht im Unterordner des Benutzernamens gespeichert "
#~ "werden und fällt in den Standard-Download-Ordner zurück. Fehler: %s"

#~ msgid "Buddy-only"
#~ msgstr "Nur für Freunde"

#~ msgid "Share with buddies only"
#~ msgstr "Nur mit Freunden teilen"

#~ msgid "_Quit…"
#~ msgstr "_Beenden…"

#~ msgid "_Configure Shares"
#~ msgstr "_Freigaben konfigurieren"

#~ msgid "Remote file error"
#~ msgstr "Fehler in einer entfernten Datei"

#, python-format
#~ msgid "Cannot find %(option1)s or %(option2)s, please install either one."
#~ msgstr ""
#~ "Kann weder %(option1)s noch %(option2)s finden, bitte installiere einen "
#~ "der beiden."

#, python-format
#~ msgid "%(num)s folders found before rescan"
#~ msgstr "%(num)s Ordner vor dem erneuten Scannen gefunden"

#, python-format
#~ msgid "Failed to process the following databases: %(names)s"
#~ msgstr ""
#~ "Die folgenden Datenbanken konnten nicht verarbeitet werden: %(names)s"

#, python-format
#~ msgid "Failed to send number of shared files to the server: %s"
#~ msgstr ""
#~ "Die Anzahl der freigegebenen Dateien konnte nicht an den Server gesendet "
#~ "werden: %s"

#~ msgid "Quit / Run in Background"
#~ msgstr "Beenden / Ausführen im Hintergrund"

#~ msgid "Limit buddy-only shares to trusted buddies"
#~ msgstr "Beschränkung der Freundenfreigaben auf vertrauenswürdige Freunde"

#~ msgid "Shared"
#~ msgstr "Freigegeben"

#~ msgid "Search Files and Folders"
#~ msgstr "Suche in Dateien und Ordnern"

#~ msgid "Out of Date"
#~ msgstr "Veraltet"

#, python-format
#~ msgid "Version %(version)s is available, released on %(date)s"
#~ msgstr "Version %(version)s ist verfügbar, veröffentlicht auf %(date)s"

#, python-format
#~ msgid "You are using a development version of %s"
#~ msgstr "Du verwendest eine Entwicklungsversion von %s"

#, python-format
#~ msgid "You are using the latest version of %s"
#~ msgstr "Du verwendest die aktuellste Version von %s"

#~ msgid "Latest Version Unknown"
#~ msgstr "Neueste Version unbekannt"

#~ msgid "Prefer Dark _Mode"
#~ msgstr "Dunkle _Mode bevorzugen"

#~ msgid "Show _Log History Pane"
#~ msgstr "_Protokolfenster anzeigen"

#~ msgid "Buddy List in Separate Tab"
#~ msgstr "Freundeliste in separatem Tab"

#~ msgid "Buddy List Always Visible"
#~ msgstr "Freundeliste immer sichtbar"

#~ msgid "_View"
#~ msgstr "_Ansicht"

#~ msgid "_Open Log Folder"
#~ msgstr "_Open Protokollordner"

#~ msgid "_Browse Folder(s)"
#~ msgstr "_Ordner durchsuchen"

#~ msgid "Kibibytes (2^10 bytes) per second."
#~ msgstr "Kibibytes (2^10 Bytes) pro Sekunde."

#~ msgid "Sent to users as the reason for being geo blocked."
#~ msgstr "Wird den Nutzern als Grund für die Geoblockade mitgeteilt."

#~ msgid "Sent to users as the reason for being banned."
#~ msgstr "Wird den Benutzern als Grund für die Sperrung mitgeteilt."

#~ msgid ""
#~ "Once you interact with the application being away, status will be set to "
#~ "online."
#~ msgstr ""
#~ "Sobald Sie mit der Anwendung interagieren, wird der Status auf online "
#~ "gesetzt."

#~ msgid "Each user may queue a maximum of either:"
#~ msgstr ""
#~ "Jeder Benutzer kann maximal eines des folgendes in die Warteschlange "
#~ "stellen:"

#~ msgid "Mebibytes (2^20 bytes)."
#~ msgstr "Mebibytes (2^20 Bytes)."

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "files"
#~ msgstr "Dateien"

#~ msgid "Queue Behavior"
#~ msgstr "Warteschlangenverhalten"

#~ msgid ""
#~ "If disabled, slots will automatically be determined by available "
#~ "bandwidth limitations."
#~ msgstr ""
#~ "Wenn deaktiviert, wird die Anzahl freier Slots anhand der maximalen "
#~ "zugestandenen Bandbreite ermittelt."

#~ msgid "Note that the operating system's theme may take precedence."
#~ msgstr ""
#~ "Beachten Sie, dass das Thema des Betriebssystems Vorrang haben kann."

#~ msgid "By default the leftmost tab is activated at startup"
#~ msgstr ""
#~ "Standardmäßig wird die Registerkarte ganz links beim Start aktiviert"

#, python-format
#~ msgid "Quit %(program)s %(version)s, %(status)s!"
#~ msgstr "%(program)s Beenden %(version)s, %(status)s!"

#~ msgid "terminated"
#~ msgstr "beendet"

#~ msgid "done"
#~ msgstr "fertig"

#~ msgid "Remember choice"
#~ msgstr "Auswahl merken"

#~ msgid "--- disconnected ---"
#~ msgstr "–––Verbindung getrennt–––"

#~ msgid "--- reconnected ---"
#~ msgstr "–––Verbindung wiederhergestellt–––"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Earth"
#~ msgstr "Erde"

#~ msgid "Czech Republic"
#~ msgstr "Tschechische Republik"

#~ msgid "Turkey"
#~ msgstr "Türkei"

#~ msgid "Kosovo"
#~ msgstr "Kosovo"

#, python-format
#~ msgid "Usage: %(command)s %(args)s"
#~ msgstr "Nutzung: %(command)s %(args)s"

#~ msgid "Joined Rooms "
#~ msgstr "Besuchte Räume "

#~ msgid "Unknown Network Interface"
#~ msgstr "Unbekannte Netzwerkschnittstelle"

#~ msgid "Listening Port Unavailable"
#~ msgstr "Empfangsport nicht verfügbar"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr ""
#~ "Der Server scheint abgeschaltet zu sein oder nicht zu antworten. Versuche "
#~ "es in %i Sekunden erneut"

#~ msgid "_Auto-join Room"
#~ msgstr "_Raum automatisch beitreten"

#~ msgid ""
#~ "Nicotine+ uses peer-to-peer networking to connect to other users. In "
#~ "order to allow users to connect to you without trouble, an open listening "
#~ "port is crucial."
#~ msgstr ""
#~ "Nicotine+ verwendet Peer-to-Peer-Netzwerke, um sich mit anderen Benutzern "
#~ "zu verbinden. Damit die Benutzer ohne Probleme eine Verbindung zu Ihnen "
#~ "herstellen können, ist ein offener Empfangsport entscheidend."

#~ msgid "System Default"
#~ msgstr "Systemvorgabe"

#~ msgid ""
#~ "<b>Syntax</b>: Letters are case-insensitive. All Python regular "
#~ "expressions are supported if escaping is disabled. For simple filters, "
#~ "keeping escaping enabled is recommended."
#~ msgstr ""
#~ "<b>Syntax</b>: Bei Buchstaben wird die Groß-/Kleinschreibung nicht "
#~ "beachtet. Alle regulären Python-Ausdrücke werden unterstützt, wenn die "
#~ "Escape-Funktion deaktiviert ist. Für einfache Filter wird empfohlen, die "
#~ "Escape-Funktion aktiviert zu lassen."

#~ msgid "Escaped"
#~ msgstr "Escapet"

#~ msgid "Escape filter"
#~ msgstr "Fluchtfilter"

#~ msgid "Enter a text pattern and what to replace it with"
#~ msgstr "Gebe ein Textmuster ein und womit es ersetzt werden soll"

#, python-format
#~ msgid "%(num)s folders found before rescan, rebuilding…"
#~ msgstr "%(num)s Ordner vor erneutem Scan gefunden, Neuaufbau…"

#, python-format
#~ msgid "No listening port is available in the specified port range %s–%s"
#~ msgstr "Kein Empfangsport ist im angegebenen Port-Bereich verfügbar %s–%s"

#~ msgid ""
#~ "Choose a range to select a listening port from. The first available port "
#~ "in the range will be used."
#~ msgstr ""
#~ "Wähle einen Bereich für den Empfangsport. Der erste verfügbare Port im "
#~ "Bereich wird verwendet."

#~ msgid "First Port"
#~ msgstr "Erster Port"

#~ msgid "to"
#~ msgstr "bis"

#~ msgid "Last Port"
#~ msgstr "Letzter Port"

#, python-format
#~ msgid "Cannot find %s or newer, please install it."
#~ msgstr "Kann %s oder neuer nicht finden, bitte installiere es."

#~ msgid ""
#~ "Cannot import the Gtk module. Bad install of the python-gobject module?"
#~ msgstr ""
#~ "Das Gtk-Modul kann nicht importiert werden. Schlechte Installation des "
#~ "Python-Gobject-Moduls?"

#, python-format
#~ msgid ""
#~ "You are using an unsupported version of GTK %(major_version)s. You should "
#~ "install GTK %(complete_version)s or newer."
#~ msgstr ""
#~ "Sie verwenden eine nicht unterstützte Version von GTK %(major_version)s. "
#~ "Sie sollten GTK %(complete_version)s oder neuer installieren."

#~ msgid "User Info"
#~ msgstr "Benutzerinformationen"

#~ msgid "Zoom 1:1"
#~ msgstr "Zoom 1:1"

#~ msgid "Zoom In"
#~ msgstr "Reinzoomen"

#~ msgid "Zoom Out"
#~ msgstr "Rauszoomen"

#~ msgid "Show User I_nfo"
#~ msgstr "Benutzer_informationen anzeigen"

#~ msgid "Request User's Info"
#~ msgstr "Benutzerinformationen anfordern"

#~ msgid "Request User's Shares"
#~ msgstr "Benutzerfreigaben anfordern"

#~ msgid "Request User Info"
#~ msgstr "Benutzerinformationen anfordern"

#~ msgid "Enter the name of the user whose info you want to see:"
#~ msgstr ""
#~ "Geben Sie den Namen des Benutzers ein, dessen Informationen Sie sehen "
#~ "möchten:"

#~ msgid "Request Shares List"
#~ msgstr "Liste der Freigaben anfordern"

#, python-format
#~ msgid "Invalid Soulseek URL: %s"
#~ msgstr "Ungültige Soulseek-URL: %s"

#~ msgid ""
#~ "Users on the Soulseek network will be able to download files from folders "
#~ "you share. Sharing files is crucial for the health of the Soulseek "
#~ "network."
#~ msgstr ""
#~ "Benutzer im Soulseek-Netzwerk können Dateien aus Ordnern herunterladen, "
#~ "die Sie freigeben. Das Teilen von Dateien ist entscheidend für die "
#~ "Gesundheit des Soulseek-Netzwerks."

#~ msgid "Update I_nfo"
#~ msgstr "I_nfo aktualisieren"

#~ msgid "Chat Room Commands"
#~ msgstr "Chatraum-Befehle"

#~ msgid "/join /j 'room'"
#~ msgstr "/join /j 'Raum'"

#~ msgid "Join room 'room'"
#~ msgstr "Raum betreten"

#~ msgid "/me 'message'"
#~ msgstr "/me 'Nachricht'"

#~ msgid "Display the Now Playing script's output"
#~ msgstr "Zeige Ausgabe des Now-Playing-Skripts"

#~ msgid "/add /ad 'user'"
#~ msgstr "/add /ad 'Benutzer'"

#~ msgid "Add user 'user' to your buddy list"
#~ msgstr "Benutzer 'Benutzer' zu Ihrer Kontaktliste hinzufügen"

#~ msgid "/rem /unbuddy 'user'"
#~ msgstr "/rem /unbuddy 'Benutzer'"

#~ msgid "Remove user 'user' from your buddy list"
#~ msgstr "Benutzer 'Benutzer' aus Ihrer Freundesliste entfernen"

#~ msgid "/ban 'user'"
#~ msgstr "/ban 'Benutzer'"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "Benutzer der Sperrliste hinzufügen"

#~ msgid "/unban 'user'"
#~ msgstr "/unban 'Benutzer'"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "Benutzer aus Sperrliste entfernen"

#~ msgid "/ignore 'user'"
#~ msgstr "/ignore 'Benutzer'"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "Benutzer zur Ingnorierliste hinzufügen"

#~ msgid "/unignore 'user'"
#~ msgstr "/unignore 'Benutzer'"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "Benutzer aus Ignorierliste entfernen"

#~ msgid "/browse /b 'user'"
#~ msgstr "/browse /b 'Benutzer'"

#~ msgid "/whois /w 'user'"
#~ msgstr "/whois /w 'Benutzer'"

#~ msgid "Request info for 'user'"
#~ msgstr "Informationen für 'Benutzer' anfordern"

#~ msgid "/ip 'user'"
#~ msgstr "/ip 'Benutzer'"

#~ msgid "Show IP for user 'user'"
#~ msgstr "IP-Adresse des Benutzers anzeigen"

#~ msgid "/search /s 'query'"
#~ msgstr "/search /s 'Abfrage'"

#~ msgid "Start a new search for 'query'"
#~ msgstr "Neue Suche starten"

#~ msgid "/rsearch /rs 'query'"
#~ msgstr "/rsearch /rs 'Abfrage'"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "Suche in den beigetretenen Räumen nach 'query'"

#~ msgid "/bsearch /bs 'query'"
#~ msgstr "/bsearch /bs 'Abfrage'"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "In der Freundeliste suchen"

#~ msgid "/usearch /us 'user' 'query'"
#~ msgstr "/usearch /us 'Benutzer' 'Abfrage'"

#~ msgid "/msg 'user' 'message'"
#~ msgstr "/msg 'Benutzer' 'Nachricht'"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "Sende Nachricht an Benutzer"

#~ msgid "/pm 'user'"
#~ msgstr "/pm 'Benutzer'"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr "Öffne privaten Chat"

#~ msgid "Private Chat Commands"
#~ msgstr "Private Chat-Befehle"

#~ msgid "Add user to your ban list"
#~ msgstr "Benutzer zu Ihrer Sperrliste hinzufügen"

#~ msgid "Add user to your ignore list"
#~ msgstr "Benutzer zur Ingnorierliste hinzufügen"

#~ msgid "Browse shares of user"
#~ msgstr "Benutzerfreigaben durchsuchen"

#~ msgid ""
#~ "For order-insensitive filtering, as well as filtering several exact "
#~ "phrases, vertical bars can be used to separate phrases and words.\n"
#~ "    Example: Remix|Instrumental|Dub Mix"
#~ msgstr ""
#~ "Für eine ordnungsunempfindliche Filterung sowie zum Filtern mehrerer "
#~ "exakter Phrasen können vertikale Balken verwendet werden, um Phrasen und "
#~ "Wörter zu trennen.\n"
#~ "    Beispiel: Remix| Instrumental| Dub-Mix"

#~ msgid "To exclude non-audio files use !0 in the duration filter."
#~ msgstr "Verwende !0 im Dauerfilter, um Nicht-Audiodateien auszuschließen."

#~ msgid "Filters files based upon users' geographical location."
#~ msgstr ""
#~ "Filtert Dateien basierend auf dem geografischen Standort der Benutzer."

#~ msgid "To view the full results again, simply clear all active filters."
#~ msgstr ""
#~ "Lösche einfach alle aktiven Filter, um die vollständigen Ergebnisse "
#~ "anzuzeigen."

#~ msgid "See the preferences to set default search result filter options."
#~ msgstr ""
#~ "In den Einstellungen finden Sie Informationen zum Festlegen der "
#~ "Standardfilteroptionen für Suchergebnisse."

#~ msgid "File size"
#~ msgstr "Dateigröße"

#~ msgid "Clear Active Filters"
#~ msgstr "Aktive Filter löschen"

#~ msgid "Cycle through completions when pressing tab-key"
#~ msgstr ""
#~ "Beim Drücken der Tabulatortaste durch die Vervollständigungen blättern"

#~ msgid "Hide drop-down when only one matches"
#~ msgstr "Drop-Down-Liste nicht anzeigen, wenn nur ein Treffer vorliegt"

#~ msgid "Download folders in reverse alphanumerical order"
#~ msgstr ""
#~ "Herunterladen von Ordnern in umgekehrter alphanumerischer Reihenfolge"

#~ msgid "Incomplete file folder:"
#~ msgstr "Unvollständiger Dateiordner:"

#~ msgid "Download folder:"
#~ msgstr "Downloadordner:"

#~ msgid "Save buddies' uploads to:"
#~ msgstr "Uploads von Freunden speichern unter:"

#~ msgid "Use UPnP to forward listening port"
#~ msgstr "UPnP verwenden zum Weiterleiten des Empfangsports"

#~ msgid ""
#~ "Share folders with every Soulseek user or buddies, allowing contents to "
#~ "be downloaded directly from your device. Hidden files are never shared."
#~ msgstr ""
#~ "Teilen Sie Ordner mit jedem Soulseek-Benutzer oder -Freunden, sodass "
#~ "Inhalte direkt von Ihrem Gerät heruntergeladen werden können. Versteckte "
#~ "Dateien werden niemals freigegeben."

#~ msgid "Secondary Tabs"
#~ msgstr "Sekundäre Tabs"

#~ msgid "Chat room tab bar position:"
#~ msgstr "Position der Chatraum-Tabsleiste:"

#~ msgid "Private chat tab bar position:"
#~ msgstr "Position der privaten Chat-Tab-Leiste:"

#~ msgid "Search tab bar position:"
#~ msgstr "Position der Suchregisterkarte:"

#~ msgid "User info tab bar position:"
#~ msgstr "Position der Registerkarten für Benutzerinformationen:"

#~ msgid "User browse tab bar position:"
#~ msgstr "Position der Registerkarten für Benutzerinfos:"

#~ msgid "Tab Labels"
#~ msgstr "Tabsbeschriftung"

#~ msgid "_Refresh Info"
#~ msgstr "_Info aktualisieren"

#~ msgid "Block IP Address"
#~ msgstr "IP-Adresse blockieren"

#~ msgid "Connected"
#~ msgstr "Verbunden"

#~ msgid "Disconnected"
#~ msgstr "Nicht verbunden"

#~ msgid "Disconnected (Tray)"
#~ msgstr "Getrennt (Fach)"

#~ msgid "User(s)"
#~ msgstr "Benutzer"

#, python-format
#~ msgid "Alias \"%s\" returned nothing"
#~ msgstr "Der Alias \"%s\" ergab nichts"

#~ msgid "Alternative Speed Limits"
#~ msgstr "Alternative Geschwindigkeitsbegrenzungen"

#~ msgid "Last played"
#~ msgstr "Zuletzt gespielt"

#~ msgid "Playing now"
#~ msgstr "Jetzt spielt"

#, python-format
#~ msgid "Cannot download file to %(path)s: %(error)s"
#~ msgstr "Kann Datei nicht herunterladen %(path)s: %(error)s"

#, python-format
#~ msgid "Error code %(code)s: %(description)s"
#~ msgstr "Fehlercode %(code)s: %(description)s"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "Kein solcher Alias (%s)"

#~ msgid "Aliases:"
#~ msgstr "Aliase:"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "Alias %(alias)s entfernt: %(action)s\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "Kein solcher Alias (%(alias)s)\n"

#~ msgid "Use Alternative Transfer Speed Limits"
#~ msgstr "Alternative Geschwindigkeitsbegrenzungen verwenden"

#~ msgid "Aliases"
#~ msgstr "Aliase"

#~ msgid "/alias /al 'command' 'definition'"
#~ msgstr "/alias /al 'Befehl' 'Definition'"

#~ msgid "Add a new alias"
#~ msgstr "Einen neuen Alias hinzufügen"

#~ msgid "/unalias /un 'command'"
#~ msgstr "/unalias /un 'Befehl'"

#~ msgid "Remove an alias"
#~ msgstr "Einen Alias entfernen"

#~ msgid "Chat History"
#~ msgstr "Chat-Verlauf"

#~ msgid "Command aliases"
#~ msgstr "Befehls-Aliase"

#~ msgid "Limit download speed to (KiB/s):"
#~ msgstr "Download-Geschwindigkeit auf (KiB/s) begrenzen:"

#~ msgid "Author(s):"
#~ msgstr "Autor(en):"

#~ msgid "Limit upload speed to (KiB/s):"
#~ msgstr "Upload-Geschwindigkeit begrenzen auf (KiB/s):"

#~ msgid "Wishlist item found"
#~ msgstr "Wunschartikel gefunden"

#~ msgid "Tabs show user status icons instead of status text"
#~ msgstr "Tabs zeigen Benutzerstatussymbole anstelle von Statustext an"

#~ msgid "Show file path tooltips in file list views"
#~ msgstr "Dateipfad-Tooltips in Dateilistenansichten anzeigen"

#~ msgid "Colored and clickable usernames"
#~ msgstr "Farbige und anklickbare Benutzernamen"

#~ msgid "Notification changes the tab's text color"
#~ msgstr "Benachrichtigung verändert die Textfarbe des Tabs"

#~ msgid "Cancel"
#~ msgstr "Abbrechen"

#~ msgid "OK"
#~ msgstr "Okay"

#~ msgid "_Add to Buddy List"
#~ msgstr "_Zur Freundesliste hinzufügen"

#~ msgid "use non-default user data directory for e.g. list of downloads"
#~ msgstr ""
#~ "Verwendung eines nicht standardmäßigen Benutzerdatenverzeichnisses, z. B. "
#~ "für die Liste der Downloads"

#, python-format
#~ msgid "Unknown config section '%s'"
#~ msgstr "Unbekannter Konfigurationsabschnitt '%s'"

#, python-format
#~ msgid "Unknown config option '%(option)s' in section '%(section)s'"
#~ msgstr ""
#~ "Unbekannte Konfigurationsoption '%(option)s' in Abschnitt '%(section)s'"

#, python-format
#~ msgid "Version %s is available"
#~ msgstr "Version %s ist verfügbar"

#, python-format
#~ msgid "released on %s"
#~ msgstr "freigegeben am %s"

#~ msgid "Nicotine+ is running in the background"
#~ msgstr "Nicotine+ läuft im Hintergrund"

#, python-format
#~ msgid "Private message from %s"
#~ msgstr "Private Nachricht von %s"

#~ msgid "Aborted"
#~ msgstr "Abgebrochen"

#~ msgid "Blocked country"
#~ msgstr "Gesperrtes Land"

#~ msgid "Finished / Aborted"
#~ msgstr "Beendet / Abgebrochen"

#~ msgid "Close tab"
#~ msgstr "Tab schließen"

#, python-format
#~ msgid "You've been mentioned in the %(room)s room"
#~ msgstr "Dein Name wurde im Raum „%(room)s“ erwähnt"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "Befehl %s ist unbekannt"

#, python-format
#~ msgid "(Warning: %(realuser)s is attempting to spoof %(fakeuser)s) "
#~ msgstr "(Warnung: %(realuser)s versucht, %(fakeuser)s zu spoofen.) "

#, python-format
#~ msgid ""
#~ "The network interface you specified, '%s', does not exist. Change or "
#~ "remove the specified network interface and restart Nicotine+."
#~ msgstr ""
#~ "Die von Ihnen angegebene Netzwerkschnittstelle '%s' existiert nicht. "
#~ "Ändern oder entfernen Sie die angegebene Netzwerkschnittstelle und "
#~ "starten Sie Nicotine+ neu."

#~ msgid ""
#~ "The range you specified for client connection ports was {}-{}, but none "
#~ "of these were usable. Increase and/or "
#~ msgstr ""
#~ "Der von Ihnen angegebene Bereich für Client-Verbindungsports war {}-{}, "
#~ "aber keiner dieser Ports war verwendbar. Erhöhen Sie und/oder "

#~ msgid ""
#~ "Note that part of your range lies below 1024, this is usually not allowed "
#~ "on most operating systems with the exception of Windows."
#~ msgstr ""
#~ "Beachte dass dein Portbereich teilweise unter 1024 liegt und dies in der "
#~ "Regel in den meisten Betriebssystemen nicht erlaubt ist."

#, python-format
#~ msgid "Rescan progress: %s"
#~ msgstr "Fortschritt des Rescans: %s"

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "Betriebssystem-Fehler: %s"

#~ msgid "UPnP is not available on this network"
#~ msgstr "UPnP ist in diesem Netzwerk nicht verfügbar"

#, python-format
#~ msgid "Failed to map the external WAN port: %(error)s"
#~ msgstr "Fehler beim Zuordnen des externen WAN-Ports: %(error)s"

#~ msgid "Room wall"
#~ msgstr "Raumwand"

#~ msgid ""
#~ "The default listening port '2234' works fine in most cases. If you need "
#~ "to use a different port, you will be able to modify it in the preferences "
#~ "later."
#~ msgstr ""
#~ "Der Standard-Port '2234' funktioniert in den meisten Fällen gut. Wenn Sie "
#~ "einen anderen Port verwenden möchten, können Sie diesen später in den "
#~ "Einstellungen ändern."

#~ msgid "Show users with similar interests"
#~ msgstr "Benutzer mit ähnlichen Interessen anzeigen"

#~ msgid "Menu"
#~ msgstr "Menü"

#~ msgid "Expand / Collapse all"
#~ msgstr "Alle auf-/zuklappen"

#~ msgid "Configure shares"
#~ msgstr "Freigaben konfigurieren"

#~ msgid "Create or join room…"
#~ msgstr "Raum erstellen oder beitreten…"

#~ msgid "_Room List"
#~ msgstr "_Raumliste"

#~ msgid "Enable alternative download and upload speed limits"
#~ msgstr ""
#~ "Alternative Geschwindigkeitsbegrenzungen für Download und Upload "
#~ "aktivieren"

#~ msgid "Show log history"
#~ msgstr "Protokollverlauf anzeigen"

#~ msgid "Result grouping mode"
#~ msgstr "Ergebnisgruppierungsmodus"

#~ msgid "Free slot"
#~ msgstr "Freier Slot"

#~ msgid "Save shares list to disk"
#~ msgstr "Freigabeliste speichern"

#~ msgid "_Away"
#~ msgstr "_Weg"

#, python-format
#~ msgid "Failed to load ui file %(file)s: %(error)s"
#~ msgstr ""
#~ "Die Benutzeroberflachdatei konnte nicht geladen werden %(file)s: %(error)s"

#~ msgid ""
#~ "Attempting to reset index of shared files due to an error. Please rescan "
#~ "your shares."
#~ msgstr ""
#~ "Der Versuch, den Index der freigegebenen Dateien aufgrund eines Fehlers "
#~ "zurückzusetzen. Bitte scannen Sie Ihre Freigaben erneut."

#~ msgid ""
#~ "File index of shared files could not be accessed. This could occur due to "
#~ "several instances of Nicotine+ being active simultaneously, file "
#~ "permission issues, or another issue in Nicotine+."
#~ msgstr ""
#~ "Auf den Dateiindex der freigegebenen Dateien konnte nicht zugegriffen "
#~ "werden. Dies könnte daran liegen, dass mehrere Instanzen von Nicotine+ "
#~ "gleichzeitig aktiv sind, Probleme mit der Dateiberechtigung oder ein "
#~ "anderes Problem in Nicotine+ auftreten ist."

#~ msgid "Nicotine+ is a Soulseek client"
#~ msgstr "Nicotine+ ist ein Soulseek-Kunde"

#~ msgid "enable the tray icon"
#~ msgstr "Aktivieren Sie das Taskleistensymbol"

#~ msgid "disable the tray icon"
#~ msgstr "Deaktivieren Sie das Taskleistensymbol"

#~ msgid "Get Soulseek Privileges…"
#~ msgstr "Soulseek-Privilegien erhalten…"

#, python-format
#~ msgid ""
#~ "Public IP address is <b>%(ip)s</b> and active listening port is "
#~ "<b>%(port)s</b>"
#~ msgstr ""
#~ "Die öffentliche IP-Adresse ist <b>%(ip)s</b> und der aktive Abhörsport "
#~ "ist <b>%(port)s</b>"

#~ msgid "unknown"
#~ msgstr "unbekannt"

#~ msgid "Notification"
#~ msgstr "Benachrichtigung"

#~ msgid "Length"
#~ msgstr "Länge"

#, python-format
#~ msgid "Picture not saved, %s already exists."
#~ msgstr "Bild nicht gespeichert, %s ist bereits vorhanden."

#~ msgid "_Open"
#~ msgstr "Ö_ffnen"

#~ msgid "_Save"
#~ msgstr "_Speichern"

#, python-format
#~ msgid "Failed to load plugin '%s', could not find it."
#~ msgstr "Fehler beim Laden des Plugins '%s', konnte es nicht finden."

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "E/A-Fehler: %s"

#, python-format
#~ msgid "Failed to open file path: %s"
#~ msgstr "Der Dateipfad konnte nicht geöffnet werden: %s"

#, python-format
#~ msgid "Failed to open URL: %s"
#~ msgstr "URL konnte nicht geöffnet werden: %s"

#~ msgid "_Log Conversation"
#~ msgstr "_Konversation protokollieren"

#~ msgid "_Add…"
#~ msgstr "_Hinzufügen…"

#~ msgid "Result Filter List"
#~ msgstr "Ergebnis-Filterliste"

#~ msgid "Prepend < or > to find files less/greater than the given value."
#~ msgstr ""
#~ "Stellen Sie < oder > voran, um Dateien zu finden, die kleiner/größer als "
#~ "der angegebene Wert sind."

#~ msgid ""
#~ "VBR files display their average bitrate and are typically lower in "
#~ "bitrate than a compressed 320 kbps CBR file of the same audio quality."
#~ msgstr ""
#~ "VBR-Dateien zeigen ihre durchschnittliche Bitrate an und haben in der "
#~ "Regel eine niedrigere Bitrate als eine komprimierte 320-kbit/s-CBR-Datei "
#~ "mit derselben Audioqualität."

#~ msgid "Like Size above, =, <, and > can be used."
#~ msgstr "Wie oben bei Größe können =, < und > verwendet werden."

#~ msgid ""
#~ "Prevent write access by other programs for files being downloaded (turn "
#~ "off for NFS)"
#~ msgstr ""
#~ "Schreibzugriff durch andere Programme für heruntergeladene Dateien "
#~ "verhindern (für NFS ausschalten)"

#~ msgid "Where incomplete downloads are temporarily stored."
#~ msgstr "Ort für temporäre Download-Dateien."

#~ msgid ""
#~ "Where buddies' uploads will be stored (with a subfolder created for each "
#~ "buddy)."
#~ msgstr ""
#~ "Wo die Uploads der Freunde gespeichert werden (mit einem Unterordner, der "
#~ "für jeden Freund erstellt wird)."

#~ msgid "Toggle away status after minutes of inactivity:"
#~ msgstr "Status wegschalten nach Minuten der Inaktivität:"

#~ msgid "Protocol:"
#~ msgstr "Protokoll:"

#~ msgid "Icon theme folder (requires restart):"
#~ msgstr "Icon-Themenordner (Neustart erforderlich):"

#~ msgid "Establishing connection"
#~ msgstr "Stelle Verbindung her"

#~ msgid "Clear Groups"
#~ msgstr "Gruppen leeren"

#~ msgid "User List"
#~ msgstr "Benutzerliste"

#~ msgid "_Reset Statistics…"
#~ msgstr "Statistik zurücksetzen…"

#~ msgid "Clear _Downloads…"
#~ msgstr "_Downloads löschen…"

#~ msgid "Clear Uploa_ds…"
#~ msgstr "Uploa_ds löschen…"

#~ msgid "Block User's IP Address"
#~ msgstr "IP-Adresse des Benutzers blockieren"

#~ msgid "Ignore User's IP Address"
#~ msgstr "IP-Adresse des Benutzers ignorieren"

#~ msgid ""
#~ "Clear every download that has finished transferring or been caught by a "
#~ "filter."
#~ msgstr ""
#~ "Löschen Sie jeden Download, der die Übertragung abgeschlossen hat oder "
#~ "von einem Filter abgefangen wurde."

#~ msgid "Usernames"
#~ msgstr "Benutzernamen"

#~ msgid "Display logged chat room messages when a room is rejoined"
#~ msgstr ""
#~ "Protokollierte Nachrichten anzeigen, wenn ein Raum erneut betreten wird"

#~ msgid ""
#~ "Clear every upload that has either finished transferring, or been "
#~ "cancelled by the remote user."
#~ msgstr ""
#~ "Löschen Sie jeden Upload, der entweder fertig übertragen oder vom Remote-"
#~ "Benutzer abgebrochen wurde."

#~ msgid "Queue Position"
#~ msgstr "Position in der Warteschlange"

#, python-format
#~ msgid ""
#~ "User %(user)s is directly searching for \"%(query)s\", returning %(num)i "
#~ "results"
#~ msgstr ""
#~ "Benutzer %(user)s sucht direkt nach \"%(query)s\" und erhält %(num)i "
#~ "Ergebnisse"

#~ msgid "Edit"
#~ msgstr "Bearbeiten"

#~ msgid "Room wall (personal message set)"
#~ msgstr "Raumwand (persönliches Nachrichtenset)"

#~ msgid "Your config file is corrupt"
#~ msgstr "Ihre Konfigurationsdatei ist beschädigt"

#, python-format
#~ msgid ""
#~ "We're sorry, but it seems your configuration file is corrupt. Please "
#~ "reconfigure Nicotine+.\n"
#~ "\n"
#~ "We renamed your old configuration file to\n"
#~ "%(corrupt)s\n"
#~ "If you open this file with a text editor you might be able to rescue some "
#~ "of your settings."
#~ msgstr ""
#~ "Es tut uns leid, aber es scheint, dass Ihre Konfigurationsdatei "
#~ "beschädigt ist. Bitte konfigurieren Sie Nicotine+ neu.\n"
#~ "\n"
#~ "Wir haben Ihre alte Konfigurationsdatei umbenannt in\n"
#~ "%(corrupt)s\n"
#~ "Wenn Sie diese Datei mit einem Texteditor öffnen, können Sie vielleicht "
#~ "einige Ihrer Einstellungen retten."

#~ msgid "User Description"
#~ msgstr "Benutzer Beschreibung"

#~ msgid "User Interests"
#~ msgstr "Benutzerinteressen"

#~ msgid "User Picture"
#~ msgstr "Benutzerbild"

#~ msgid "Search Wishlist"
#~ msgstr "Wunschzettel suchen"

#, python-format
#~ msgid "Loading Nicotine+ %(nic_version)s"
#~ msgstr "Laden von Nicotine+ %(nic_version)s"

#, python-format
#~ msgid "Using Python %(py_version)s"
#~ msgstr "Python %(py_version)s in Verwendung"

#, python-format
#~ msgid "Quitting Nicotine+ %(version)s, %(status)s…"
#~ msgstr "Nicotine+ beenden %(version)s, %(status)s…"

#, python-format
#~ msgid "Quit Nicotine+ %(version)s, %(status)s!"
#~ msgstr "Nicotine+ Beenden %(version)s, %(status)s!"

#~ msgid "User:"
#~ msgstr "Benutzer:"

#, python-format
#~ msgid "All %(ext)s"
#~ msgstr "Alle %(ext)s"

#, python-format
#~ msgid "%(number)2s files "
#~ msgstr "%(number)2s Dateien "

#~ msgid "Allow regular expressions for the filter's include and exclude"
#~ msgstr ""
#~ "Reguläre Ausdrücke für das Einschließen und Ausschließen des Filters "
#~ "zulassen"

#~ msgid "Quit…"
#~ msgstr "Beenden…"

#~ msgid "Remember previous primary tab on startup"
#~ msgstr "Vorherige primäre Tab beim Starten anzeigen"

#~ msgid "Start with Search Files by default."
#~ msgstr "Starten Sie standardmäßig mit Dateien suchen."

#~ msgid "Close Nicotine+?"
#~ msgstr "Nicotine+ schließen?"

#~ msgid "Do you really want to exit Nicotine+?"
#~ msgstr "Möchten Sie Nikotin+ wirklich beenden?"

#~ msgid "Run in Background"
#~ msgstr "Im Hintergrund ausführen"

#~ msgid "_Online Notify"
#~ msgstr "_Online Benachrichtigen"

#~ msgid "_Prioritize User"
#~ msgstr "_Benutzer bevorzugen"

#~ msgid "_Trust User"
#~ msgstr "_Benutzer vertrauen"

#~ msgid "Request User's IP Address"
#~ msgstr "IP-Adresse des Benutzers anfordern"

#~ msgid "Request IP Address"
#~ msgstr "IP-Adresse anfordern"

#~ msgid "Enter the name of the user whose IP address you want to see:"
#~ msgstr ""
#~ "Geben Sie den Namen des Benutzers ein, dessen IP-Adresse Sie sehen "
#~ "möchten:"

#~ msgid "Downloaded"
#~ msgstr "Heruntergeladen"

#, python-format
#~ msgid "Failed to add download %(filename)s to shared files: %(error)s"
#~ msgstr ""
#~ "Der Download %(filename)s konnte nicht zu den freigegebenen Dateien "
#~ "hinzugefügt werden: %(error)s"

#~ msgid "Automatically share completed downloads"
#~ msgstr "Automatische Freigabe abgeschlossener Downloads"

#~ msgid ""
#~ "The equivalent of adding your download folder as a public share, however "
#~ "files downloaded to this folder will be automatically accessible to "
#~ "others (no rescan required)."
#~ msgstr ""
#~ "Das Äquivalent zum Hinzufügen Ihres Download-Ordners als öffentliche "
#~ "Freigabe, jedoch sind Dateien, die in diesen Ordner heruntergeladen "
#~ "wurden, automatisch für andere zugänglich (kein erneuter Scan "
#~ "erforderlich)."

#~ msgid "Unable to Share Folder"
#~ msgstr "Freigabe des Ordners nicht möglich"

#~ msgid "The chosen virtual name is empty"
#~ msgstr "Der gewählte virtuelle Name ist leer"

#~ msgid "The chosen virtual name already exists"
#~ msgstr "Der gewählte virtuelle Name existiert bereits"

#~ msgid "The chosen folder is already shared"
#~ msgstr "Der gewählte Ordner ist bereits freigegeben"

#~ msgid "Set Virtual Name"
#~ msgstr "Virtuellen Namen festlegen"

#, python-format
#~ msgid "Enter virtual name for '%(dir)s':"
#~ msgstr "Virtuellen Namen für '%(dir)s' eingeben:"

#~ msgid "The chosen virtual name is either empty or already exists"
#~ msgstr ""
#~ "Der gewählte virtuelle Name ist entweder leer oder existiert bereits"

#~ msgid "The chosen folder is already shared."
#~ msgstr "Der gewählte Ordner ist bereits freigegeben."

#, python-format
#~ msgid "%s Properties"
#~ msgstr "%s Eigenschaften"

#~ msgid "Finished rescanning shares"
#~ msgstr "Aktualisierung der Freigaben beendet"

#~ msgid "Plugin List"
#~ msgstr "Plugin-Liste"

#, python-format
#~ msgid "Error while scanning %(path)s: %(error)s"
#~ msgstr "Fehler beim Scannen von %(path)s: %(error)s"

#~ msgid "Show _Log Pane"
#~ msgstr "_Log-Fenster anzeigen"

#~ msgid "Addresses"
#~ msgstr "Adressen"

#~ msgid "Handler"
#~ msgstr "Programm"

#~ msgid "Could not enable plugin."
#~ msgstr "Das Plugin konnte nicht aktiviert werden."

#~ msgid "Could not disable plugin."
#~ msgstr "Das Plugin konnte nicht deaktiviert werden."

#~ msgid "Transfers"
#~ msgstr "Überträgt"

#~ msgid "Ban List"
#~ msgstr "Sperrliste"

#~ msgid "Ignore List"
#~ msgstr "Ignorier-Liste"

#~ msgid "Censor & Replace"
#~ msgstr "Zensieren & Ersetzen"

#~ msgid "Completion"
#~ msgstr "Vervollständigung"

#~ msgid "Categories"
#~ msgstr "Kategorien"

#~ msgid "Upload Folder To…"
#~ msgstr "Ordner hochladen zu…"

#~ msgid "Upload Folder Recursive To…"
#~ msgstr "Ordner rekursiv hochladen zu…"

#~ msgid "Download _Recursive"
#~ msgstr "Rekursiv herunterladen"

#~ msgid "Download R_ecursive To…"
#~ msgstr "Rekursiv herunterladen zu…"

#~ msgid "Up_load File(s)"
#~ msgstr "_Datei(en) hochladen"

#, python-format
#~ msgid ""
#~ "Error while attempting to display folder '%(folder)s', reported error: "
#~ "%(error)s"
#~ msgstr ""
#~ "Fehler beim Versuch, den Ordner '%(folder)s' anzuzeigen, gemeldeter "
#~ "Fehler: %(error)s"

#~ msgid "Select Destination for Downloading a Folder from User"
#~ msgstr "Ziel für das Herunterladen eines Ordners vom Benutzer auswählen"

#~ msgid "Select Destination for Downloading File(s) from User"
#~ msgstr "Wählen Sie das Ziel zum Herunterladen der Datei(en) vom Benutzer"

#~ msgid "Wishes"
#~ msgstr "Wünsche"

#~ msgid "privileged"
#~ msgstr "privilegiert"

#~ msgid "prioritized"
#~ msgstr "priorisiert"

#~ msgid "Configure logging"
#~ msgstr "Protokollierung konfigurieren"

#~ msgid "Blocked IP Addresses"
#~ msgstr "Geblockte IP-Adressen"

#~ msgid "Complete buddy names"
#~ msgstr "Vervollständige Freundennamen"

#~ msgid "Complete usernames in chat rooms"
#~ msgstr "Benutzernamen vervollständigen"

#~ msgid "Complete room names"
#~ msgstr "Vervollständige Raumnamen"

#~ msgid "Drop-down List"
#~ msgstr "Auswahlliste"

#~ msgid "File Manager command ($ for file path):"
#~ msgstr "Dateimanager-Befehl ($ für Dateipfad):"

#~ msgid "Media Player command ($ for file path):"
#~ msgstr "Media Player-Befehl ($ für Dateipfad):"

#~ msgid "Ignored IP Addresses"
#~ msgstr "Ignorierte IP-Adressen"

#~ msgid "Display timestamps"
#~ msgstr "Zeitstempel anzeigen"

#~ msgid "Notification Popups"
#~ msgstr "Benachrichtigungs-Popups"

#~ msgid "Show notification popup when a file has finished downloading"
#~ msgstr ""
#~ "Popup-Benachrichtigung anzeigen, wenn das Herunterladen einer Datei "
#~ "abgeschlossen ist"

#~ msgid "Show notification popup when a folder has finished downloading"
#~ msgstr ""
#~ "Popup-Benachrichtigung anzeigen, wenn ein Ordner fertig heruntergeladen "
#~ "ist"

#~ msgid "Show notification popup when you receive a private message"
#~ msgstr ""
#~ "Benachrichtigungs-Popup anzeigen, wenn Sie eine private Nachricht erhalten"

#~ msgid "Show notification popup when someone sends a message in a chat room"
#~ msgstr ""
#~ "Benachrichtigungs-Popup anzeigen, wenn jemand eine Nachricht in einem "
#~ "Chatraum sendet"

#~ msgid "Show notification popup when you are mentioned in a chat room"
#~ msgstr ""
#~ "Benachrichtigungs-Popup anzeigen, wenn Sie in einem Chat-Raum erwähnt "
#~ "werden"

#~ msgid "Tray"
#~ msgstr "Taskleiste"

#~ msgid ""
#~ "Instances of $ will be replaced by the link. The default web browser of "
#~ "the system will be used in cases where a protocol has not been configured."
#~ msgstr ""
#~ "Instanzen von $ werden durch den Link ersetzt. In Fällen, in denen kein "
#~ "Protokoll konfiguriert wurde, wird der Standard-Webbrowser des Systems "
#~ "verwendet."

#~ msgid "Handler:"
#~ msgstr "Programm:"

#~ msgid "Primary Tabs"
#~ msgstr "Primäre Tabs"

#~ msgid "Enable file path tooltips in search and transfer views"
#~ msgstr ""
#~ "Aktivieren von Dateipfad-Tooltips in Such- und Übertragungsansichten"

#~ msgid ""
#~ "Displays the complete file path of a search result or file transfer when "
#~ "you hover a folder path or file name with your cursor."
#~ msgstr ""
#~ "Zeigt den vollständigen Dateipfad eines Suchergebnisses oder einer "
#~ "Dateiübertragung an, wenn Sie mit dem Mauszeiger über einen Ordnerpfad "
#~ "oder einen Dateinamen fahren."

#~ msgid "Show privately shared files in user shares"
#~ msgstr "Privat freigegebene Dateien in Benutzerfreigaben anzeigen"

#~ msgid ""
#~ "Other clients may offer an option to send privately shared files when you "
#~ "browse their shares. Folders containing such files are prefixed with "
#~ "'[PRIVATE FOLDER]', and are not downloadable unless the uploader gives "
#~ "explicit permission."
#~ msgstr ""
#~ "Andere Clients bieten möglicherweise eine Option zum Senden privat "
#~ "freigegebener Dateien an, wenn Sie ihre Freigaben durchsuchen. Ordner, "
#~ "die solche Dateien enthalten, sind mit dem Präfix '[PRIVATE FOLDER]' "
#~ "versehen und können nicht heruntergeladen werden, es sei denn, der "
#~ "Hochladende gibt seine ausdrückliche Erlaubnis."

#~ msgid "Chat Censor & Replace"
#~ msgstr "Chat Zensieren & Ersetzen"

#~ msgid "Select Destination for Downloading Folder with Subfolders from User"
#~ msgstr ""
#~ "Wählen Sie das Ziel zum Herunterladen des Ordners mit Unterordnern vom "
#~ "Benutzer"

#~ msgid "Show _Debug Log Controls"
#~ msgstr "_Debug-Log-Steuerelemente anzeigen"

#~ msgid "Debug Logging"
#~ msgstr "Debug Protokollierung"

#~ msgid "Copy Folder URL"
#~ msgstr "URL des Ordners kopieren"

#~ msgid "Download _To…"
#~ msgstr "Herunterladen _zu…"

#~ msgid "Download"
#~ msgstr "Herunterladen"

#~ msgid "Upload"
#~ msgstr "Hochladen"

#~ msgid "Upload Folder's Contents"
#~ msgstr "Inhalt des Ordners hochladen"

#~ msgid "Virtual Name"
#~ msgstr "Virtueller Name"

#~ msgid "Edit Virtual Name"
#~ msgstr "Virtuellen Namen bearbeiten"

#~ msgid "File Lists"
#~ msgstr "Dateilisten"

#~ msgid "Copy File Path"
#~ msgstr "Dateipfad kopieren"

#~ msgid "R_emove Wish"
#~ msgstr "_Wunsch entfernen"

#~ msgid "Rename"
#~ msgstr "Umbenennen"

#, python-format
#~ msgid "It appears '%s' is not a directory, not loading plugins."
#~ msgstr ""
#~ "Es scheint, dass '%s' kein Verzeichnis ist, Plugins werden nicht geladen."

#~ msgid "Rescanning buddy shares…"
#~ msgstr "Freundenfreigaben werden aktualisiert…"

#~ msgid "Finished rescanning buddy shares"
#~ msgstr "Aktualisierung der Freundenfreigaben beendet"

#, python-format
#~ msgid "Your buddy, %s, is attempting to upload file(s) to you."
#~ msgstr "Dein Freund %s versucht dir eine Datei zu senden."

#, python-format
#~ msgid ""
#~ "%s is not allowed to send you file(s), but is attempting to, anyway. "
#~ "Warning Sent."
#~ msgstr ""
#~ "%s hat versucht, dir Dateien zu senden, ist aber nicht berechtigt. "
#~ "Warnung gesendet."

#~ msgid "Version: "
#~ msgstr "Version: "

#~ msgid "Author(s): "
#~ msgstr "Autor(en): "

#~ msgid "Client Version"
#~ msgstr "Client-Version"

#, python-format
#~ msgid "How many days of privileges should user %s be gifted?"
#~ msgstr ""
#~ "Für wie viele Tage sollen dem Benutzer %s Berechtigungen eingeräumt "
#~ "werden?"

#~ msgid ""
#~ "Buddies will have higher priority in the queue, the same as globally "
#~ "privileged users."
#~ msgstr ""
#~ "Freunde haben höhere Priorität in der Warteliste, genauso wie global "
#~ "privilegierte Benutzer."

#~ msgid "Privileged"
#~ msgstr "Privilegiert"

#~ msgid "_Privileged"
#~ msgstr "_Privilegiert"

#~ msgid "Comments"
#~ msgstr "Kommentar"

#~ msgid "Edit _Comments…"
#~ msgstr "_Kommentare bearbeiten…"

#~ msgid ""
#~ "Creates subfolders based on the user you are downloading from, and stores "
#~ "the downloaded file / folder there."
#~ msgstr ""
#~ "Erstellt Unterordner auf der Grundlage des Benutzers, von dem Sie "
#~ "herunterladen, und speichert die heruntergeladene Datei/Ordner dort."

#~ msgid "Login Details"
#~ msgstr "Login-Daten"

#~ msgid "Advanced"
#~ msgstr "Fortgeschrittene"

#~ msgid "Port mapping renewal interval in hours:"
#~ msgstr "Erneuerungsintervall für die Portzuordnung in Stunden:"

#~ msgid "Enable buddy-only shares"
#~ msgstr "Freigaben nur für Freunde aktivieren"

#~ msgid "Files will be uploaded in the order they were queued."
#~ msgstr ""
#~ "Dateien werden in der Reihenfolge hochgeladen, in der sie in die "
#~ "Warteschlange gestellt wurden."

#~ msgid "Rescanning normal shares…"
#~ msgstr "Normale Freigaben erneut scannen…"

#~ msgid "Finished rescanning public shares"
#~ msgstr "Erneutes Scannen öffentlicher Freigaben abgeschlossen"

#~ msgid "Scanning Buddy Shares"
#~ msgstr "Aktualisierung privater Freigaben begonnen"

#~ msgid "_Rescan Public Shares"
#~ msgstr "_Publike Freigaben aktualisieren"

#~ msgid "Rescan B_uddy Shares"
#~ msgstr "Freundenfreigaben aktualisieren"

#~ msgid "Rescan Public Shares"
#~ msgstr "_Publike Freigaben aktualisieren"

#~ msgid "Rescan Buddy Shares"
#~ msgstr "Freuden-Freigaben erneut scannen"

#~ msgid "Enables buddy shares that only users on your buddy list can access."
#~ msgstr ""
#~ "Aktiviert Freunden freigaben, auf die nur Benutzer in Ihrer Freundesliste "
#~ "zugreifen können."

#~ msgid "Mark each shared folder as buddy-only"
#~ msgstr "Markieren Sie jeden freigegebenen Ordner als nur für Freunde"

#~ msgid ""
#~ "Overrides the per-share option, useful if you temporarily need to prevent "
#~ "public access to shares."
#~ msgstr ""
#~ "Überschreibt die Option pro Freigabe, die nützlich ist, wenn Sie "
#~ "vorübergehend den öffentlichen Zugriff auf Freigaben verhindern müssen."

#~ msgid ""
#~ "Only users marked as trusted on your buddy list can access your buddy-"
#~ "only shares."
#~ msgstr ""
#~ "Nur Benutzer, die in Ihrer Freudenliste als vertrauenswürdig markiert "
#~ "sind, können auf Ihre Freundenfreigaben zugreifen."

#~ msgid ""
#~ "Nicotine+ allows you to share folders directly from your computer. All "
#~ "the contents of these folders (with the exception of dotfiles) can be "
#~ "downloaded by other users on the Soulseek network. Public shares are "
#~ "available for every user, while users in your buddy list can access buddy-"
#~ "only shares in addition to public shares."
#~ msgstr ""
#~ "Mit Nicotine+ können Sie Ordner direkt von Ihrem Computer aus freigeben. "
#~ "Der gesamte Inhalt dieser Ordner (mit Ausnahme von Punktdateien) können "
#~ "von anderen Benutzern im Soulseek-Netzwerk heruntergeladen werden. "
#~ "Öffentliche Freigaben sind für jeden Benutzer verfügbar, während Benutzer "
#~ "in Ihrer Freunden-Liste zusätzlich zu den öffentlichen Freigaben auch "
#~ "Zugriff auf Freigaben haben, die nur für Freunden-Mitglieder bestimmt "
#~ "sind."

#~ msgid "Filtered out excluded search result "
#~ msgstr "Ausgeschlossenes Suchergebnis herausgefiltert "

#~ msgid "Filtered out inexact or incorrect search result "
#~ msgstr "Ungenaue oder falsche Suchergebnisse werden herausgefiltert "

#, python-format
#~ msgid ""
#~ "Stored setting '%(key)s' is no longer present in the '%(name)s' plugin"
#~ msgstr ""
#~ "Die gespeicherte Einstellung '%(key)s' ist im Plugin '%(name)s' nicht "
#~ "mehr vorhanden"

#, python-format
#~ msgid "Plugin %(module)s returned something weird, '%(value)s', ignoring"
#~ msgstr ""
#~ "Plugin %(module)s gab etwas Seltsames zurück, '%(value)s', ignoriert"

#, python-format
#~ msgid "Inconsistent cache for '%(vdir)s', rebuilding '%(dir)s'"
#~ msgstr ""
#~ "Inkonsistenter Cache für '%(vdir)s', Wiederherstellung von '%(dir)s'"

#, python-format
#~ msgid "Dropping missing folder %(dir)s"
#~ msgstr "Löschen des fehlenden Ordners %(dir)s"

#, python-format
#~ msgid "All tickers / wall messages for %(room)s:"
#~ msgstr "Alle Ticker / Wandmeldungen für %(room)s:"

#~ msgid "Set your personal ticker"
#~ msgstr "Eigenen Ticker setzen"

#~ msgid "Show all the tickers"
#~ msgstr "Alle Ticker anzeigen"

#~ msgid ""
#~ "Failed to scan shares. If Nicotine+ is currently running, please close "
#~ "the program before scanning."
#~ msgstr ""
#~ "Fehler beim Scannen von Freigaben. Wenn Nicotine+ gerade läuft, schließen "
#~ "Sie bitte das Programm vor dem Scannen."

#~ msgid "Are you sure you wish to exit Nicotine+ at this time?"
#~ msgstr "Möchten Sie Nicotine+ wirklich beenden?"

#~ msgid "Receive a User's IP Address"
#~ msgstr "IP-Adresse des Benutzers abfragen"

#~ msgid "Receive a User's Info"
#~ msgstr "Benutzerinfo abfragen"

#~ msgid "The server forbid us from doing wishlist searches."
#~ msgstr "Der Server verbietet uns, Wunschlistensuchen durchzuführen."

#, python-format
#~ msgid ""
#~ "Your shares database is corrupted. Please rescan your shares and report "
#~ "any potential scanning issues to the developers. Error: %s"
#~ msgstr ""
#~ "Die Datenbank Ihrer Freigaben ist beschädigt. Bitte scannen Sie Ihre "
#~ "Freigaben erneut und melden Sie eventuelle Scan-Probleme an die "
#~ "Entwickler. Fehler: %s"

#~ msgid ""
#~ "Please keep in mind that certain usernames may be taken. If this is the "
#~ "case, you will be prompted to change your username when connecting to the "
#~ "network."
#~ msgstr ""
#~ "Bitte denken Sie daran, dass ein Benutzername bereits vergeben sein "
#~ "könnte. Wenn dies der Fall ist, werden Sie aufgefordert, Ihren "
#~ "Benutzernamen zu ändern, wenn Sie sich mit dem Netz verbinden."

#~ msgid "Enter the username of the person you to receive information about"
#~ msgstr ""
#~ "Geben Sie den Benutzernamen der Person ein, über die Sie Informationen "
#~ "erhalten möchten"

#~ msgid "Add user 'user' to your user list"
#~ msgstr "Benutzer der Freunde-Liste hinzufügen"

#~ msgid "Remove user 'user' from your user list"
#~ msgstr "Benutzer aus der Freunde-Liste entfernen"

#~ msgid "Request user info for user 'user'"
#~ msgstr "Die Info des Benutzers abrufen"

#~ msgid "Add user to your user list"
#~ msgstr "Benutzer zu Ihrer Benutzerliste hinzufügen"

#~ msgid "Remove user from your user list"
#~ msgstr "Benutzer aus der Freunde-Liste entfernen"

#~ msgid "Respond to search requests containing minimum character count:"
#~ msgstr ""
#~ "Suchanfragen mit weniger als diese Anzahl von Zeichen werden ignoriert:"

#~ msgid ""
#~ "Queued users will be uploaded one file at a time in a cyclical fashion."
#~ msgstr ""
#~ "Benutzer in der Warteschlange werden zyklisch eine Datei nach der anderen "
#~ "hochgeladen."

#~ msgid ""
#~ "Could not execute now playing code. Are you sure you picked the right "
#~ "player?"
#~ msgstr ""
#~ "Der Befehl konnte nicht ausgeführt werden. Bist du sicher, dass du den "
#~ "richtigen Spieler ausgewählt hast?"

#~ msgid "/leave /l /part /p"
#~ msgstr "/leave /l /part /p"

#~ msgid "/clear /cl"
#~ msgstr "/clear /cl"

#~ msgid "/tick /t"
#~ msgstr "/tick /t"

#~ msgid "/tickers"
#~ msgstr "/tickers"

#~ msgid "/now"
#~ msgstr "/now"

#~ msgid "/away /a"
#~ msgstr "/away /a"

#~ msgid "/quit /q /exit"
#~ msgstr "/quit /q /exit"

#~ msgid "/close /c"
#~ msgstr "/close /c"

#~ msgid "/add /ad"
#~ msgstr "/add /ad"

#~ msgid "/rem /unbuddy"
#~ msgstr "/rem /unbuddy"

#~ msgid "/ban"
#~ msgstr "/ban"

#~ msgid "/unban"
#~ msgstr "/unban"

#~ msgid "/ignore"
#~ msgstr "/ignore"

#~ msgid "/unignore"
#~ msgstr "/unignore"

#~ msgid "/browse /b"
#~ msgstr "/browse /b"

#~ msgid "/whois /w"
#~ msgstr "/whois /w"

#~ msgid "/ip"
#~ msgstr "/ip"

#~ msgid "Cannot find the pynicotine.utils module."
#~ msgstr "Kann Modul pynicotine.utils nicht finden"

#~ msgid "Errors occured while trying to change process name:"
#~ msgstr "Beim Versuch, den Prozessnamen zu ändern, sind Fehler aufgetreten:"

#, python-format
#~ msgid "Can't remove %s"
#~ msgstr "Kann %s nicht entfernen."

#, python-format
#~ msgid "Can't back config file up, error: %s"
#~ msgstr "Kann Konfigurationsdatei nicht sichern, Fehler: %s"

#, python-format
#~ msgid "Can't rename config file, error: %s"
#~ msgstr "Kann Konfigurationsdatei nicht umbenennen, Fehler: %s"

#, python-format
#~ msgid "Connection closed by peer: %s"
#~ msgstr "Verbindung von Gegenseite getrennt: %s"

#, python-format
#~ msgid ""
#~ "Server reported port 0 for the 10th time for user %(user)s, giving up"
#~ msgstr ""
#~ "Server nannte zum zehnten Mal Port 0 für Benutzer %(user)s, gebe auf."

#, python-format
#~ msgid ""
#~ "Server reported non-zero port for user %(user)s after %(tries)i retries"
#~ msgstr ""
#~ "Server nennt einen Port ungleich 0 für Benutzer %(user)s nach %(tries)i "
#~ "Wiederholungen."

#, python-format
#~ msgid "Server reported port 0 for user %(user)s, retrying"
#~ msgstr "Server nennt Port 0 für Benutzer %(user)s, erneuter Versuch"

#, python-format
#~ msgid "Can not log in, reason: %s"
#~ msgstr "Anmeldung nicht möglich, Grund: %s"

#~ msgid ""
#~ "Someone else is logging in with the same nickname, server is going to "
#~ "disconnect us"
#~ msgstr ""
#~ "Jemand anderes meldet sich mit demselben Nick an, Server trennt uns."

#, python-format
#~ msgid ""
#~ "IP %(ip)s:%(port)s is spoofing user %(user)s with a peer request, "
#~ "blocking because it does not match IP: %(real_ip)s"
#~ msgstr ""
#~ "IP %(ip)s:%(port)s spooft Benutzer %(user)s mit einem Peer-Request. "
#~ "Blockiert, da nicht die richtige IP: %(real_ip)s."

#, python-format
#~ msgid ""
#~ "Blocking %(user)s from making a UserInfo request, possible spoofing "
#~ "attempt from IP %(ip)s port %(port)s"
#~ msgstr ""
#~ "Blocke Benutzerinfo-Anfrage von %(user)s, möglicherweise ein "
#~ "Spoofingversuch von IP %(ip)s Port %(port)s."

#, python-format
#~ msgid ""
#~ "Blocking %s from making a UserInfo request, possible spoofing attempt "
#~ "from an unknown IP & port"
#~ msgstr ""
#~ "Blocke Benutzerinfo Anfrage von %s, möglicherweise ein Spoofingversuch "
#~ "von einer unbekannten IP."

#, python-format
#~ msgid "%(user)s is banned, but is making a UserInfo request"
#~ msgstr "Gesperrter Benutzer %(user)s macht eine Benutzerinfo-Anfrage"

#, python-format
#~ msgid ""
#~ "%(user)s is making a BrowseShares request, blocking possible spoofing "
#~ "attempt from IP %(ip)s port %(port)s"
#~ msgstr ""
#~ "%(user)s fordert deine Freigaben an, blockiere möglichen Spoofingversuch "
#~ "von IP %(ip)s Port %(port)s."

#, python-format
#~ msgid ""
#~ "%(user)s is making a BrowseShares request, blocking possible spoofing "
#~ "attempt from an unknown IP & port"
#~ msgstr ""
#~ "%(user)s fordert deine Freigaben an, blockiere möglichen Spoofingversuch "
#~ "von unbekannter IP."

#, python-format
#~ msgid "%(user)s is making a BrowseShares request"
#~ msgstr "%(user)s fordert deine Freigaben an."

#, python-format
#~ msgid "Unknown tunneled message: %s"
#~ msgstr "Unbekannte getunnelte Nachricht: %s"

#~ msgid "Shared files database seems to be corrupted, rescan your shares"
#~ msgstr "Deine Freigabendatenbank scheint defekt zu sein, erstelle sie neu."

#, python-format
#~ msgid "Empty message made, class %s"
#~ msgstr "Leere Nachricht erzeugt, Klasse %s"

#, python-format
#~ msgid "Can't parse incoming messages, class %s"
#~ msgstr "Kann eingehende Nachricht nicht parsen, Klasse %s"

#~ msgid "Could not bind to a local port, aborting connection"
#~ msgstr "Lokale Portreservierung fehlgeschlagen. Verbindungsabbruch."

#, python-format
#~ msgid "Can't handle connection type %s"
#~ msgstr "Kann mit Verbindungsart %s nicht umgehen."

#, python-format
#~ msgid "Error packaging message: %(type)s %(msg_obj)s, %(error)s"
#~ msgstr ""
#~ "Fehler beim Verpacken einer Nachricht: %(type)s %(msg_obj)s, %(error)s"

#, python-format
#~ msgid ""
#~ "Can't send the message over the closed connection: %(type)s %(msg_obj)s"
#~ msgstr ""
#~ "Kann die Nachricht nicht über eine geschlossene Verbindung senden: "
#~ "%(type)s %(msg_obj)s"

#, python-format
#~ msgid "Major Socket Error: Networking terminated! %s"
#~ msgstr "Major-Socket-Fehler: Netzwerkverbindung unterbrochen! %s"

#, python-format
#~ msgid "Filtering: %s"
#~ msgstr "Filtern: %s"

#, python-format
#~ msgid "Retrying failed download: user %(user)s, file %(file)s"
#~ msgstr ""
#~ "Wiederhole fehlgeschlagenen Download: Benutzer %(user)s, Datei %(file)s"

#, python-format
#~ msgid "Got transfer request %s but cannot determine requestor"
#~ msgstr "Bekam Transferanfrage %s aber kann Anforderer nicht ausmachen"

#~ msgid "[Automatic Message] "
#~ msgstr "[Automatische Nachricht] "

#~ msgid "You are not allowed to send me files."
#~ msgstr "You are not allowed to send me files, heh."

#, python-format
#~ msgid "Got unknown transfer response: %s"
#~ msgstr "Umbekannte Transfer-Antwort erhalten: %s"

#, python-format
#~ msgid "Download finished: %(file)s"
#~ msgstr "Download abgeschlossen: %(file)s"

#~ msgid "(friend)"
#~ msgstr "(Freund)"

#, python-format
#~ msgid "Upload finished: %(user)s, file %(file)s"
#~ msgstr "Upload abgeschlossen: Benutzer %(user)s, Datei %(file)s"

#~ msgid "Get user i_nfo"
#~ msgstr "Benutzerinfo abrufen"

#~ msgid "_Add user to list"
#~ msgstr "Benutzer der Liste hinzufügen"

#~ msgid "_Ban this user"
#~ msgstr "Diesen Benutzer sperren"

#~ msgid "_Ignore this user"
#~ msgstr "Diesen Benutzer ignorieren"

#~ msgid "Clear finished/aborted"
#~ msgstr "Entferne Abgeschlossene/Abgebrochene"

#~ msgid "Clear aborted"
#~ msgstr "Entferne Abgebrochene"

#~ msgid "Clear queued"
#~ msgstr "Entferne Wartende"

#~ msgid "Abor_t"
#~ msgstr "Abbrechen"

#~ msgid "Directory"
#~ msgstr "Verzeichnis"

#~ msgid "Warning"
#~ msgstr "Warnung"

#~ msgid "Search files"
#~ msgstr "Suche"

#~ msgid "User info"
#~ msgstr "Benutzerinfo"

#~ msgid "Chat rooms"
#~ msgstr "Chat-Räume"

#, python-format
#~ msgid "Hide %(tab)s"
#~ msgstr "%(tab)s verstecken"

#~ msgid "Rescanning Buddy Shares started"
#~ msgstr "Aktualisierung der privaten Freigaben begonnen"

#~ msgid "Rescanning Buddy Shares finished"
#~ msgstr "Aktualisierung der privaten Freigaben abgeschlossen"

#~ msgid "Rescanning finished"
#~ msgstr "Aktualisierung abgeschlossen"

#~ msgid "Send to tray"
#~ msgstr "Ins Tray minimieren"

#~ msgid "I like"
#~ msgstr "Ich mag"

#~ msgid "I _don't like this"
#~ msgstr "Das mag ich nicht"

#~ msgid "Ban this user"
#~ msgstr "Benutzer sperren"

#~ msgid "Ignore this user"
#~ msgstr "Benutzer ignorieren"

#~ msgid "In queue"
#~ msgstr "In Wartschlange"

#, python-format
#~ msgid "Client port is <b>%(port)s</b>"
#~ msgstr "Der Client-Port ist <b>%(port)s</b>"

#~ msgid "Your IP address has not been retrieved from the server"
#~ msgstr "Deine IP-Adresse wurde vom Server nicht ausgegeben"

#, python-format
#~ msgid "Your IP address is <b>%(ip)s</b>"
#~ msgstr "Deine IP-Adresse lautet: <b>%(ip)s</b>"

#~ msgid "Warning: Bad Username"
#~ msgstr "Achtung: schlechter Benutzername"

#~ msgid "Username 'None' is not a good one, please pick another."
#~ msgstr "Der Benutzername „None“ ist kein guter, nimm einen anderen."

#~ msgid "Warning: Invalid ports"
#~ msgstr "Achtung: ungültige Ports"

#~ msgid "Client ports are invalid."
#~ msgstr "Die Client-Ports sind ungültig."

#~ msgid "Users in list"
#~ msgstr "Benutzer in Liste"

#, python-format
#~ msgid "Security Risk: you should not share your %s directory!"
#~ msgstr "Sicherheitsrisiko: Du solltest das %s-Verzeichnis nicht freigeben!"

#~ msgid "Ignore user..."
#~ msgstr "Ignoriere Benutzer ..."

#~ msgid "IP:"
#~ msgstr "IP:"

#~ msgid "Ban user..."
#~ msgstr "Sperre Benutzer ..."

#~ msgid "Server"
#~ msgstr "Server"

#~ msgid "Geo Block"
#~ msgstr "Geo-Block"

#~ msgid "Notebook Tabs"
#~ msgstr "Tabs"

#~ msgid "URL Catching"
#~ msgstr "URL-Catching"

#~ msgid "Initializing transfer"
#~ msgstr "Initialisiere Transfer"

#~ msgid "Waiting for peer to connect"
#~ msgstr "Warte auf Gegenseite"

#~ msgid "Getting address"
#~ msgstr "Ermittle Adresse"

#~ msgid "Lookup a User's IP"
#~ msgstr "IP eines Benutzers anzeigen"

#~ msgid "Clear failed"
#~ msgstr "Entferne Fehlgeschlagene"

#~ msgid "Directories"
#~ msgstr "Verzeichnisse"

#~ msgid "Download r_ecursive to..."
#~ msgstr "Rekursiv herunterladen nach ..."

#~ msgid "Upload Directory to..."
#~ msgstr "Verzeichnis hochladen an ..."

#, python-format
#~ msgid "Speed: %s"
#~ msgstr "Geschwindigkeit: %s"

#, python-format
#~ msgid "Files: %s"
#~ msgstr "Dateien: %s"

#~ msgid "Hates"
#~ msgstr "Hasst"

#, python-format
#~ msgid "Total uploads allowed: %i"
#~ msgstr "Uploads erlaubt: %i"

#, python-format
#~ msgid "Slots free: %s"
#~ msgstr "Slots frei: %s"

#, python-format
#~ msgid "%s"
#~ msgstr "%s"

#, python-format
#~ msgid "to %(user)s"
#~ msgstr "an %(user)s"

#~ msgid "Log"
#~ msgstr "Log"

#~ msgid "Clear Queued"
#~ msgstr "Entferne Wartende"

#~ msgid "Clear all searches attempts"
#~ msgstr "Alle offenen Suchen schließen"

#~ msgid ""
#~ "Send the private message directly to the user (not supported on most "
#~ "clients)"
#~ msgstr ""
#~ "Nachrichten direkt an Gegenstelle senden (Wird von den meisten Clients "
#~ "nicht unterstützt.)"

#~ msgid "Stop new search results from being displayed"
#~ msgstr "Keine weiteren Suchergebnisse anzeigen"

#~ msgid "Total uploads allowed: unknown"
#~ msgstr "Uploads insgesamt erlaubt: unbekannt"

#~ msgid "Slots free: unknown"
#~ msgstr "Slots verfügbar: unbekannt"

#~ msgid "Queue size: unknown"
#~ msgstr "Länge der Warteschlange: unbekannt"

#~ msgid "Speed: unknown"
#~ msgstr "Geschwindigkeit: unbekannt"

#~ msgid "Files: unknown"
#~ msgstr "Dateien: unbekannt"

#~ msgid "Directories: unknown"
#~ msgstr "Verzeichnisse: unbekannt"

#~ msgid "Accepts Uploads from:"
#~ msgstr "Akzeptiert Uploads von:"

#~ msgid "Add..."
#~ msgstr "Hinzufügen ..."

#~ msgid "About chat room commands"
#~ msgstr "Über Befehle in Chat-Räumen"

#~ msgid "Leave room 'room'"
#~ msgstr "Raum verlassen"

#~ msgid "About search filters"
#~ msgstr "Über Suchfilter"

#~ msgid "_Modes"
#~ msgstr "Modi"

#~ msgid "_Uploads"
#~ msgstr "Uploads"

#~ msgid "_Private Chat"
#~ msgstr "Privater Chat"

#~ msgid "Buddy _List"
#~ msgstr "Freunde"

#~ msgid "_Chat Rooms"
#~ msgstr "Chat-Räume"

#~ msgid "_Interests"
#~ msgstr "Interessen"

#~ msgid "About _chat room commands"
#~ msgstr "Über Befehle in Chat-Räumen"

#~ msgid "About _private chat commands"
#~ msgstr "Über Befehle im privaten Chat"

#~ msgid "About _search filters"
#~ msgstr "Über Suchfilter"

#~ msgid "Toggle away after "
#~ msgstr "Zu „Abwesend“ wechseln nach "

#~ msgid "Enable Censorship"
#~ msgstr "Zensurfilter aktivieren"

#~ msgid "Username Font Style:"
#~ msgstr "Benutzernamen-Schriftart:"

#~ msgid "Display away colors"
#~ msgstr "Abwesenheitsfarben verwenden"

#~ msgid "Lock incoming files (turn off for NFS)"
#~ msgstr "Eingehende Dateien sperren (für NFS abschalten)"

#~ msgid ""
#~ "The users will be able to send you files. These files will be downloaded "
#~ "into the Buddy Uploads subdirectory in your Download directory"
#~ msgstr ""
#~ "Die Benutzer sind berechtigt, dir Dateien zu senden. Diese Dateien werden "
#~ "im entsprechenden Freunde-Upload-Ordner in deinem Downloadverzeichnis "
#~ "gespeichert."

#~ msgid "Upload transfers"
#~ msgstr "Uploads"

#~ msgid "Send to player"
#~ msgstr "An Player übergeben"

#~ msgid "Open Directory"
#~ msgstr "Verzeichnis öffnen"

#~ msgid "Enable geographical blocker"
#~ msgstr "Den Ländercode-Blocker aktivieren"

#~ msgid "Geographical paranoia (block unresolvable IPs)"
#~ msgstr "Geografischer Verfolgungswahn (Unauflösbare IPs sperren)"

#~ msgid "Always quit when main window is closed"
#~ msgstr "Beim Schließen des Hauptfensters immer beenden"

#~ msgid "<b>Trayicon</b>"
#~ msgstr "<b>Tray-Symbol</b>"

#~ msgid "Online:"
#~ msgstr "Online:"

#~ msgid "Away:"
#~ msgstr "Abwesend:"

#~ msgid "Offline:"
#~ msgstr "Offline:"

#~ msgid "Hilite:"
#~ msgstr "Markiert:"

#~ msgid "<b>Status</b>"
#~ msgstr "<b>Status</b>"

#~ msgid "Private Chat Logs directory:"
#~ msgstr "Verzeichnis für Privat-Chat-Protokolle:"

#~ msgid "Read"
#~ msgstr "Lesen"

#~ msgid "Reopen last Private Chat messages"
#~ msgstr "Letzte Privat-Chat-Mitteilungen öffnen"

#~ msgid "Label Angle:"
#~ msgstr "Ausrichtung:"

#~ msgid "last.fm"
#~ msgstr "last.fm"

#~ msgid "Player Command/Username"
#~ msgstr "Player-Kommando/Benutzer"

#~ msgid "Legend:"
#~ msgstr "Legende:"

#~ msgid "Example:"
#~ msgstr "Beispiel:"

#~ msgid "Send out a max of"
#~ msgstr "Liefere höchstens"

#~ msgid "Filter out:"
#~ msgstr "Rausfiltern:"

#~ msgid "Filter in:"
#~ msgstr "Reinfiltern:"

#~ msgid ""
#~ "If buddy shares are enabled, they will be shared. Otherwise normal shares "
#~ "will be used."
#~ msgstr ""
#~ "Wenn private Freigaben aktiviert sind, werden diese benutzt. Ansonsten "
#~ "werden normale Freigaben benutzt."

#~ msgid "Users will be sent one file and then another user will be selected"
#~ msgstr "Es wird je eine Datei pro Benutzer gewährt, dann folgt der Nächste"

#~ msgid "KBytes/sec"
#~ msgstr "KByte/s"

#~ msgid "Megabytes"
#~ msgstr "Megabyte"

#~ msgid "Enable URL catching"
#~ msgstr "URL-Catching aktivieren"

#~ msgid "Humanize slsk:// urls"
#~ msgstr "Mache slsk://-URLs lesbar"

#~ msgid "Use the first available listening port from the following range:"
#~ msgstr "Lausche am ersten freien Port in diesem Bereich:"
