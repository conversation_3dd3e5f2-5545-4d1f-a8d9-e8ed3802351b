# SPDX-FileCopyrightText: 2022-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2025-04-10 16:05+0000\n"
"Last-Translator: Pagal3 <<EMAIL>>\n"
"Language-Team: Latvian <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/lv/>\n"
"Language: lv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n % 10 == 0 || n % 100 >= 11 && n % 100 <= "
"19) ? 0 : ((n % 10 == 1 && n % 100 != 11) ? 1 : 2);\n"
"X-Generator: Weblate 5.11-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr "Soulseek Klients"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Grafiskais klients Soulseek vienādranga tīklam"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr ""
"Soulseek;Nicotine;kopīgošana;tērzēšana;ziņapmaiņa;P2P;peer-to-peer;GTK;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
msgid "Browse the Soulseek network"
msgstr "Pārlūko Soulseek tīklu"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+ ir Soulseek vienādranga tīkla grafiskais klients."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ mērķis ir kļūt par vieglu, patīkamu, bezmaksas un atvērtā pirmkoda "
"(FOSS) alternatīvu oficiālajam Soulseek klientam, vienlaikus nodrošinot arī "
"plašu funkciju klāstu."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
msgid "Search Files"
msgstr "Failu meklēšana"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Lejupielādes"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
msgid "Browse Shares"
msgstr "Koplietojumu pārlūkošana"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
msgid "Private Chat"
msgstr "Privātā tērzēšana"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "Nicotine+ komanda"

#: pynicotine/__init__.py:50
#, python-format
msgid "Website: %s"
msgstr "Vietne: %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "parādīt šo palīdzības ziņu un iziet"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "fails"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr "izmantot ne noklusējuma konfigurācijas failu"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "dir"

#: pynicotine/__init__.py:64
msgid "alternative directory for user data and plugins"
msgstr "alternatīva direktorija lietotāja datiem un spraudņiem"

#: pynicotine/__init__.py:68
msgid "start the program without showing window"
msgstr "palaist programmu bez loga parādīšanas"

#: pynicotine/__init__.py:71
msgid "ip"
msgstr "ip"

#: pynicotine/__init__.py:72
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "piesaistīt ligzdas norādītajam IP (noderīgi, lietojot VPN)"

#: pynicotine/__init__.py:75
msgid "port"
msgstr "ports"

#: pynicotine/__init__.py:76
msgid "listen on the given port"
msgstr "klausīties uz norādīto portu"

#: pynicotine/__init__.py:80
msgid "rescan shared files"
msgstr "pārskenēt koplietotos failus"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr "palaist programmu bezgalvas režīmā (bez grafiskās lietotāja saskarnes)"

#: pynicotine/__init__.py:88
msgid "display version and exit"
msgstr "parādīt versiju un iziet"

#: pynicotine/__init__.py:121
#, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Jūs izmantojat neatbalstītu Python versiju (%(old_version)s).\n"
"Jums vajadzētu instalēt Python %(min_version)s vai jaunāku versiju."

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Koplietojumu skenēšana neizdevās. Lūdzu, aizveriet citas Nicotine+ instances "
"un mēģiniet vēlreiz."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, python-format
msgid "%(user)s is away"
msgstr "%(user)s ir aizgājis"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, python-format
msgid "%(user)s is online"
msgstr "%(user)s ir tiešsaistē"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, python-format
msgid "%(user)s is offline"
msgstr "%(user)s ir bezsaistē"

#: pynicotine/buddies.py:316
msgid "Buddy Status"
msgstr "Draugu statuss"

#: pynicotine/chatrooms.py:383
#, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Jūs esat pievienots privātai istabai: %(room)s"

#: pynicotine/chatrooms.py:478
#, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr "Tērzēšanas ziņa no lietotāja '%(user)s' istabā '%(room)s': %(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr "Nevar izveidot direktoriju '%(path)s', ziņotā kļūda: %(error)s"

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr "Dublējot konfigurāciju, radās kļūda: %s"

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr "Konfigurācijas dublējums saglabāts: %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Ielādē %(program)s %(version)s"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Iziet no %(program)s %(version)s, %(status)s…"

#: pynicotine/core.py:246
msgid "terminating"
msgstr "pārtrauc"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "lietotnes aizvēršana"

#: pynicotine/core.py:259
#, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Iziet no %(program)s %(version)s!"

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr "Pirms savienojuma izveides ir jānorāda lietotājvārds un parole…"

#: pynicotine/downloads.py:239
#, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""
"Kļūda: Lejupielādes filtrs nenostrādāja! Pārbaudiet savus filtrus. Iemesls: "
"%s"

#: pynicotine/downloads.py:254
#, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Kļūda: %(num)d Lejupielādes filtri nenostrādāja! %(error)s "

#: pynicotine/downloads.py:366
#, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s lejupielādēts(i) no %(user)s"

#: pynicotine/downloads.py:370
msgid "File Downloaded"
msgstr "Fails lejupielādēts"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Izpildīts: %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "Izpildīt '%(command)s' neizdevās: %(error)s"

#: pynicotine/downloads.py:407
#, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s lejupielādēta(s) no %(user)s"

#: pynicotine/downloads.py:411
msgid "Folder Downloaded"
msgstr "Mape lejupielādēta"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Izpildīts mapē: %s"

#: pynicotine/downloads.py:443
#, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "Nevarēja pārvietot '%(tempfile)s' uz '%(file)s': %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
msgid "Download Folder Error"
msgstr "Lejupielādes mapes kļūda"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Lejupielāde pabeigta: lietotājs %(user)s, fails %(file)s"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Lejupielāde pārtraukta, lietotāja %(user)s fails %(file)s"

#: pynicotine/downloads.py:1150
#, python-format
msgid "Download I/O error: %s"
msgstr "Lejupielādes I/O kļūda: %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Nevar iegūt ekskluzīvu piekļuvi failam - I/O kļūda: %s"

#: pynicotine/downloads.py:1202
#, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Nevar saglabāt failu %(folder_path)s: %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Lejupielāde sākta: lietotājs %(user)s, fails %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, python-format
msgid "Cannot find %s, please install it."
msgstr "Nevar atrast %s, lūdzu, instalējiet to."

#: pynicotine/gtkgui/__init__.py:162
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr ""
"Nav pieejama grafiskā vide, tiek izmantots bezgalvas (bez grafiskās "
"lietotāja saskarnes) režīms"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "_Savienoties"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "_Atvienoties"

#: pynicotine/gtkgui/application.py:308
msgid "Soulseek _Privileges"
msgstr "Soulseek _privilēģijas"

#: pynicotine/gtkgui/application.py:314
msgid "_Preferences"
msgstr "_Preferences"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
msgid "_Quit"
msgstr "_Iziet"

#: pynicotine/gtkgui/application.py:337
msgid "Browse _Public Shares"
msgstr "Pārlūkot _publiskos koplietojumus"

#: pynicotine/gtkgui/application.py:338
msgid "Browse _Buddy Shares"
msgstr "Pārlūkot _draugu koplietojumus"

#: pynicotine/gtkgui/application.py:339
msgid "Browse _Trusted Shares"
msgstr "Pārlūkot _uzticamo draugu koplietojumus"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
msgid "_Rescan Shares"
msgstr "Pā_rskenēt koplietojumus"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
msgid "Configure _Shares"
msgstr "Konfigurēt _Koplietojumus"

#: pynicotine/gtkgui/application.py:371
msgid "_Keyboard Shortcuts"
msgstr "_Tastatūras īsinājumtaustiņi"

#: pynicotine/gtkgui/application.py:372
msgid "_Setup Assistant"
msgstr "Uz_stādīšanas palīgs"

#: pynicotine/gtkgui/application.py:373
msgid "_Transfer Statistics"
msgstr "Pārsū_tīšanas statistika"

#: pynicotine/gtkgui/application.py:378
msgid "Report a _Bug"
msgstr "Ziņot par _kļūdu"

#: pynicotine/gtkgui/application.py:379
msgid "Improve T_ranslations"
msgstr "Uzlabot t_ulkojumus"

#: pynicotine/gtkgui/application.py:383
msgid "_About Nicotine+"
msgstr "P_ar Nicotine+"

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "_Fails"

#: pynicotine/gtkgui/application.py:395
msgid "_Shares"
msgstr "_Koplietojumi"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
msgid "_Help"
msgstr "_Palīdzība"

#: pynicotine/gtkgui/application.py:410
msgid "_Browse Shares"
msgstr "_Pārlūkot koplietojumus"

#: pynicotine/gtkgui/application.py:465
#, python-format
msgid "Unable to show notification: %s"
msgstr "Nevar parādīt paziņojumu: %s"

#: pynicotine/gtkgui/application.py:526
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Jūs joprojām augšupielādējat failus. Vai tiešām vēlaties iziet?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr "Gaidīt, līdz augšupielādes tiks pabeigtas"

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr "Vai tiešām vēlaties iziet?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
msgid "_No"
msgstr "_Nē"

#: pynicotine/gtkgui/application.py:535
msgid "_Run in Background"
msgstr "_Palaist fonā"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr "Iziet no Nicotine+"

#: pynicotine/gtkgui/application.py:561
msgid "Shares Not Available"
msgstr "Koplietojumi nav pieejami"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Pārbaudiet, vai ārējie diski ir pievienoti un vai mapju atļaujas ir pareizas."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
msgid "_Cancel"
msgstr "At_celt"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "_Mēģināt vēlreiz"

#: pynicotine/gtkgui/application.py:567
msgid "_Force Rescan"
msgstr "_Piespiedu pārskenēšana"

#: pynicotine/gtkgui/application.py:742
msgid "Message Downloading Users"
msgstr "Ziņa lejupielādētājiem"

#: pynicotine/gtkgui/application.py:743
msgid "Send private message to all users who are downloading from you:"
msgstr "Nosūtīt privātu ziņu visiem lietotājiem, kuri lejupielādē no jums:"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
msgid "_Send Message"
msgstr "_Sūtīt ziņu"

#: pynicotine/gtkgui/application.py:756
msgid "Message Buddies"
msgstr "Sūtīt ziņu draugiem"

#: pynicotine/gtkgui/application.py:757
msgid "Send private message to all online buddies:"
msgstr "Nosūtīt privātu ziņu visiem draugiem, kas ir tiešsaistē:"

#: pynicotine/gtkgui/application.py:786
msgid "Select a Saved Shares List File"
msgstr "Atlasiet saglabāto koplietojumu saraksta failu"

#: pynicotine/gtkgui/application.py:877
msgid "Critical Error"
msgstr "Kritiska kļūda"

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ saskārās ar kritisku kļūdu un ir jāiziet. Lūdzu, nokopējiet "
"sekojošo ziņu un iekļaujiet to kļūdu ziņojumā:"

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr "_Iziet no Nicotine+"

#: pynicotine/gtkgui/application.py:883
msgid "_Copy & Report Bug"
msgstr "_Kopēt un ziņot par kļūdu"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "Statuss"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr "Valsts"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Lietotājs"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Ātrums"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Faili"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted"
msgstr "Uzticams"

#: pynicotine/gtkgui/buddies.py:108
msgid "Notify"
msgstr "Paziņot"

#: pynicotine/gtkgui/buddies.py:114
msgid "Prioritized"
msgstr "Prioritizēts"

#: pynicotine/gtkgui/buddies.py:120
msgid "Last Seen"
msgstr "Pēdējo reizi redzēts"

#: pynicotine/gtkgui/buddies.py:126
msgid "Note"
msgstr "Piezīme"

#: pynicotine/gtkgui/buddies.py:143
msgid "Add User _Note…"
msgstr "Pievie_not lietotājam piezīmi…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Noņemt"

#: pynicotine/gtkgui/buddies.py:364
msgid "Never seen"
msgstr "Nekad nav redzēts"

#: pynicotine/gtkgui/buddies.py:529
msgid "Add User Note"
msgstr "Pievienot lietotājam piezīmi"

#: pynicotine/gtkgui/buddies.py:530
#, python-format
msgid "Add a note about user %s:"
msgstr "Pievienojiet piezīmi par lietotāju %s:"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
msgid "_Add"
msgstr "_Pievienot"

#: pynicotine/gtkgui/chatrooms.py:224
msgid "Create New Room?"
msgstr "Vai izveidot jaunu istabu?"

#: pynicotine/gtkgui/chatrooms.py:225
#, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Vai tiešām vēlaties izveidot jaunu istabu \"%s\"?"

#: pynicotine/gtkgui/chatrooms.py:226
msgid "Make room private"
msgstr "Padarīt istabu privātu"

#: pynicotine/gtkgui/chatrooms.py:515
msgid "Search activity log…"
msgstr "Aktivitātes žurnāla meklēšana…"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
msgid "Search chat log…"
msgstr "Meklēšana tērzēšanas žurnālā…"

#: pynicotine/gtkgui/chatrooms.py:597
msgid "Sear_ch User's Files"
msgstr "_Meklēt lietotāja failos"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
msgid "Find…"
msgstr "Atrast…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Kopēt"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr "Kopēt visu"

#: pynicotine/gtkgui/chatrooms.py:608
msgid "Clear Activity View"
msgstr "Notīrīt darbību skatu"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
msgid "_Leave Room"
msgstr "_Iziet no istabas"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
msgid "Copy Link"
msgstr "Kopēt saiti"

#: pynicotine/gtkgui/chatrooms.py:624
msgid "View Room Log"
msgstr "Skatīt istabas žurnālu"

#: pynicotine/gtkgui/chatrooms.py:627
msgid "Delete Room Log…"
msgstr "Dzēst istabas žurnālu…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
msgid "Clear Message View"
msgstr "Notīrīt ziņas skatu"

#: pynicotine/gtkgui/chatrooms.py:832
#, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s pieminēja Jūs istabā %(room)s"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "%(user)s Jūs pieminēja istabā %(room)s"

#: pynicotine/gtkgui/chatrooms.py:855
#, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "Ziņa no %(user)s istabā %(room)s"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s pievienojās istabai"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s izgāja no istabas"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s ir aizgājis"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s ir atgriezies"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
msgid "Delete Logged Messages?"
msgstr "Vai dzēst žurnālā reģistrētās ziņas?"

#: pynicotine/gtkgui/chatrooms.py:1197
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"Vai tiešām vēlaties neatgriezeniski dzēst visas žurnālā reģistrētās ziņas "
"šai istabai?"

#: pynicotine/gtkgui/dialogs/about.py:405
msgid "About"
msgstr "Par"

#: pynicotine/gtkgui/dialogs/about.py:415
msgid "Website"
msgstr "Vietne"

#: pynicotine/gtkgui/dialogs/about.py:457
#, python-format
msgid "Error checking latest version: %s"
msgstr "Radās kļūda, pārbaudot jaunāko versiju: %s"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr "Pieejama jauna versija: %s"

#: pynicotine/gtkgui/dialogs/about.py:467
msgid "Up to date"
msgstr "Atjaunināta"

#: pynicotine/gtkgui/dialogs/about.py:496
msgid "Checking latest version…"
msgstr "Pārbauda jaunāko versiju…"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
msgid "Setup Assistant"
msgstr "Uzstādīšanas palīgs"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
msgid "Virtual Folder"
msgstr "Virtuālā mape"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
msgid "Folder"
msgstr "Mape"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
msgid "_Previous"
msgstr "_Iepriekšējais"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
msgid "_Finish"
msgstr "_Pabeigt"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
msgid "_Next"
msgstr "_Nākamais"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
msgid "Add a Shared Folder"
msgstr "Pievienojiet mapi koplietošanai"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
msgid "Edit Shared Folder"
msgstr "Rediģēt koplietoto mapi"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Ievadiet jaunu virtuālo nosaukumu mapei '%(dir)s':"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
msgid "_Edit"
msgstr "_Rediģēt"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"Lietotājs %s jau pastāv, un ievadītā parole nav derīga. Lūdzu, izvēlieties "
"citu lietotājvārdu, ja pieslēdzaties pirmo reizi."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
msgid "File Properties"
msgstr "Faila rekvizīti"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr "Faila rekvizīti (%(num)i no %(total)i  /  %(size)s  /  %(length)s)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Faila rekvizīti (%(num)i no %(total)i  /  %(size)s)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
msgid "_Apply"
msgstr "_Pielietot"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
msgid "Add…"
msgstr "Pievienot…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
msgid "Edit…"
msgstr "Rediģēt…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, python-format
msgid "%s Settings"
msgstr "%s iestatījumi"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
msgid "Add Item"
msgstr "Pievienot vienumu"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
msgid "Edit Item"
msgstr "Rediģēt vienumu"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "Nezināms"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
msgid "Check Port Status"
msgstr "Pārbaudīt porta statusu"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, ports %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
msgid "Password Change Rejected"
msgstr "Paroles maiņa noraidīta"

#: pynicotine/gtkgui/dialogs/preferences.py:218
msgid "Enter a new password for your Soulseek account:"
msgstr "Ievadiet jaunu paroli savam Soulseek kontam:"

#: pynicotine/gtkgui/dialogs/preferences.py:220
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"Jūs pašlaik esat izrakstījies no Soulseek tīkla. Ja vēlaties mainīt jau "
"esoša Soulseek konta paroli, jums tajā kontā ir jāpieslēdzas."

#: pynicotine/gtkgui/dialogs/preferences.py:223
msgid "Enter password to use when logging in:"
msgstr "Ievadiet paroli, ko izmantosiet, lai pieslēgtos:"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
msgid "Change Password"
msgstr "Mainīt paroli"

#: pynicotine/gtkgui/dialogs/preferences.py:230
msgid "_Change"
msgstr "_Izmainīt"

#: pynicotine/gtkgui/dialogs/preferences.py:274
msgid "No one"
msgstr "Neviens"

#: pynicotine/gtkgui/dialogs/preferences.py:275
msgid "Everyone"
msgstr "Visi"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Draugi"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted buddies"
msgstr "Uzticamie draugi"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
msgid "Nothing"
msgstr "Nekas"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
msgid "Open File"
msgstr "Atvērt failu"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
msgid "Open in File Manager"
msgstr "Atvērt failu pārvaldniekā"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Meklēšana"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
msgid "Pause"
msgstr "Pauzēt"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
msgid "Resume"
msgstr "Atsākt"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
msgid "Browse Folder"
msgstr "Pārlūkot mapi"

#: pynicotine/gtkgui/dialogs/preferences.py:317
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Sintakse</b>: Nav reģistrjutīga. Ja iespējots, var izmantot Python "
"regulārās izteiksmes, pretējā gadījumā tiek atbalstītas tikai aizstājējzīmju "
"* sakritības."

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr "Filtrs"

#: pynicotine/gtkgui/dialogs/preferences.py:334
msgid "Regex"
msgstr "Regex"

#: pynicotine/gtkgui/dialogs/preferences.py:468
msgid "Add Download Filter"
msgstr "Pievienot lejupielādes filtru"

#: pynicotine/gtkgui/dialogs/preferences.py:469
msgid "Enter a new download filter:"
msgstr "Ievadiet jaunu lejupielādes filtru:"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
msgid "Enable regular expressions"
msgstr "Iespējot regulārās izteiksmes"

#: pynicotine/gtkgui/dialogs/preferences.py:498
msgid "Edit Download Filter"
msgstr "Rediģēt lejupielādes filtru"

#: pynicotine/gtkgui/dialogs/preferences.py:499
msgid "Modify the following download filter:"
msgstr "Mainiet šo lejupielādes filtru:"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d nenostrādāja! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
msgid "Filters Successful"
msgstr "Filtri ir veiksmīgi"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr "Publisks"

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr "Pieejamība"

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "Pārtraukt"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
msgid "Retry"
msgstr "Mēģināt vēlreiz"

#: pynicotine/gtkgui/dialogs/preferences.py:828
msgid "Round Robin"
msgstr "Round Robin"

#: pynicotine/gtkgui/dialogs/preferences.py:829
msgid "First In, First Out"
msgstr "Pirmais iekšā, pirmais ārā"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
msgid "Username"
msgstr "Lietotājvārds"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
msgid "IP Address"
msgstr "IP adrese"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
msgid "Ignore User"
msgstr "Ignorēt lietotāju"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
msgid "Enter the name of the user you want to ignore:"
msgstr "Ievadiet tā lietotāja vārdu, kuru vēlaties ignorēt:"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
msgid "Ignore IP Address"
msgstr "Ignorēt IP adresi"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
msgid "Enter an IP address you want to ignore:"
msgstr "Ievadiet IP adresi, kuru vēlaties ignorēt:"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "* is a wildcard"
msgstr "* ir aizstājējzīme"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
msgid "Ban User"
msgstr "Aizliegt lietotāju"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
msgid "Enter the name of the user you want to ban:"
msgstr "Ievadiet tā lietotāja vārdu, kuru vēlaties aizliegt:"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
msgid "Ban IP Address"
msgstr "Aizliegt IP adresi"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "Enter an IP address you want to ban:"
msgstr "Ievadiet IP adresi, kuru vēlaties bloķēt:"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr "Formatēt kodus"

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
msgid "Pattern"
msgstr "Modelis"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
msgid "Replacement"
msgstr "Aizvietotājs"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
msgid "Censor Pattern"
msgstr "Cenzūras šablons"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Ievadiet šablonu, pēc kura vēlaties cenzēt. Pievienojiet atstarpi šablona "
"sākumā un beigās, ja nevēlaties šablonam sakrist ar virknēm iekš vārdiem "
"(var nenostrādāt rindu sākumā un beigās)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
msgid "Edit Censored Pattern"
msgstr "Rediģēt cenzūras šablonu"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
msgid "Add Replacement"
msgstr "Pievienot aizvietotāju"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
msgid "Enter a text pattern and what to replace it with:"
msgstr "Ievadiet teksta modeli un to, ar ko to aizstāt:"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
msgid "Edit Replacement"
msgstr "Rediģēt aizvietotāju"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
msgid "System default"
msgstr "Sistēmas noklusējums"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
msgid "Show confirmation dialog"
msgstr "Rādīt apstiprinājuma dialoglodziņu"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
msgid "Run in the background"
msgstr "Palaist fonā"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
msgid "bold"
msgstr "treknraksts"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
msgid "italic"
msgstr "slīpraksts"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
msgid "underline"
msgstr "pasvītrots"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
msgid "normal"
msgstr "normāls"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
msgid "Separate Buddies tab"
msgstr "Atsevišķa draugu cilne"

#: pynicotine/gtkgui/dialogs/preferences.py:1771
msgid "Sidebar in Chat Rooms tab"
msgstr "Sānjoslā 'Tērzēšanas istabas' cilnē"

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr "Vienmēr redzama sānjosla"

#: pynicotine/gtkgui/dialogs/preferences.py:1777
msgid "Top"
msgstr "Augšā"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
msgid "Bottom"
msgstr "Apakšā"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
msgid "Left"
msgstr "Pa kreisi"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
msgid "Right"
msgstr "Pa labi"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "Tiešsaistē"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Aizgājis"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Bezsaistē"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
msgid "Tab Changed"
msgstr "Cilne izmainīta"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
msgid "Tab Highlight"
msgstr "Cilnes izcelšana"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
msgid "Window"
msgstr "Logs"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
msgid "Online (Tray)"
msgstr "Tiešsaistē (ikonjosla)"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
msgid "Away (Tray)"
msgstr "Aizgājis (ikonjosla)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
msgid "Offline (Tray)"
msgstr "Bezsaistē (ikonjosla)"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
msgid "Message (Tray)"
msgstr "Ziņa (ikonjosla)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Protokols"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
msgid "Command"
msgstr "Komanda"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
msgid "Add URL Handler"
msgstr "Pievienot URL apstrādātāju"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
msgid "Enter the protocol and the command for the URL handler:"
msgstr "Ievadiet protokolu un komandu URL apstrādātājam:"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
msgid "Edit Command"
msgstr "Rediģēt komandu"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Ievadiet jaunu komandu protokolam %s:"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
msgid "Username;APIKEY"
msgstr "Lietotājvārds;APIKEY"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr ""
"Mūzikas atskaņotājs (piem., amarok, audacious, exaile); atstāt tukšu, lai "
"automātiski noteiktu:"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
msgid "Username: "
msgstr "Lietotājvārds: "

#: pynicotine/gtkgui/dialogs/preferences.py:2767
msgid "Command:"
msgstr "Komanda:"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
msgid "Title"
msgstr "Nosaukums"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Tagad atskaņo (parasti \"%(artist)s - %(title)s\")"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
msgid "Artist"
msgstr "Mākslinieks"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
msgid "Duration"
msgstr "Ilgums"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "Bitu pārraides ātrums"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
msgid "Comment"
msgstr "Komentārs"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
msgid "Album"
msgstr "Albums"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
msgid "Track Number"
msgstr "Dziesmas numurs"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
msgid "Year"
msgstr "Gads"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
msgid "Filename (URI)"
msgstr "Faila nosaukums (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
msgid "Program"
msgstr "Programma"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
msgid "Enabled"
msgstr "Iespējots"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
msgid "Plugin"
msgstr "Spraudnis"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
msgid "No Plugin Selected"
msgstr "Nav atlasīts neviens spraudnis"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
msgid "Preferences"
msgstr "Preferences"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
msgid "Network"
msgstr "Tīkls"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
msgid "User Interface"
msgstr "Lietotāja saskarne"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Koplietojumi"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Augšupielādes"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Meklējumi"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
msgid "User Profile"
msgstr "Lietotāja profils"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
msgid "Chats"
msgstr "Tērzēšana"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
msgid "Now Playing"
msgstr "Tagad atskaņo"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Žurnalēšana"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
msgid "Banned Users"
msgstr "Aizliegtie lietotāji"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
msgid "Ignored Users"
msgstr "Ignorētie lietotāji"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
msgid "URL Handlers"
msgstr "URL apstrādātāji"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr "Spraudņi"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
msgid "Pick a File Name for Config Backup"
msgstr "Izvēlieties konfigurācijas dublējuma faila nosaukumu"

#: pynicotine/gtkgui/dialogs/statistics.py:75
msgid "Transfer Statistics"
msgstr "Pārsūtīšanas statistika"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, python-format
msgid "Total Since %(date)s"
msgstr "Kopā kopš %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
msgid "Reset Transfer Statistics?"
msgstr "Vai atiestatīt pārsūtīšanas statistiku?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
msgid "Do you really want to reset transfer statistics?"
msgstr "Vai tiešām vēlaties atiestatīt pārsūtīšanas statistiku?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
msgid "Wishlist"
msgstr "Vēlmju saraksts"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
msgid "Wish"
msgstr "Vēlme"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
msgid "_Search for Item"
msgstr "_Meklēt vienumu"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
msgid "Edit Wish"
msgstr "Rediģēt vēlmi"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, python-format
msgid "Enter new value for wish '%s':"
msgstr "Ievadiet jaunu vērtību vēlmei '%s':"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
msgid "Clear Wishlist?"
msgstr "Vai notīrīt vēlmju sarakstu?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
msgid "Do you really want to clear your wishlist?"
msgstr "Vai tiešām vēlaties notīrīt savu vēlmju sarakstu?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Ceļš"

#: pynicotine/gtkgui/downloads.py:47
msgid "_Resume"
msgstr "Tu_rpināt"

#: pynicotine/gtkgui/downloads.py:48
msgid "P_ause"
msgstr "P_auze"

#: pynicotine/gtkgui/downloads.py:72
msgid "Finished / Filtered"
msgstr "Pabeigts / Filtrēts"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Pabeigts"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
msgid "Paused"
msgstr "Pauzēts"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
msgid "Filtered"
msgstr "Filtrēts"

#: pynicotine/gtkgui/downloads.py:77
msgid "Deleted"
msgstr "Izdzēsts"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
msgid "Queued…"
msgstr "Rindā…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
msgid "Everything…"
msgstr "Viss…"

#: pynicotine/gtkgui/downloads.py:132
#, python-format
msgid "Downloads: %(speed)s"
msgstr "Lejupielādes: %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
msgid "Clear Queued Downloads"
msgstr "Notīrīt rindā esošās lejupielādes"

#: pynicotine/gtkgui/downloads.py:139
msgid "Do you really want to clear all queued downloads?"
msgstr "Vai tiešām vēlaties notīrīt visas rindā esošās lejupielādes?"

#: pynicotine/gtkgui/downloads.py:151
msgid "Clear All Downloads"
msgstr "Notīrīt visas lejupielādes"

#: pynicotine/gtkgui/downloads.py:152
msgid "Do you really want to clear all downloads?"
msgstr "Vai tiešām vēlaties notīrīt visas lejupielādes?"

#: pynicotine/gtkgui/downloads.py:169
#, python-format
msgid "Download %(num)i files?"
msgstr "Vai lejupielādēt %(num)i failus?"

#: pynicotine/gtkgui/downloads.py:170
#, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"Vai tiešām vēlaties lejupielādēt %(num)i failus no %(user)s mapes %(folder)s?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
msgid "_Download Folder"
msgstr "_Lejupielādēt mapi"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
msgid "Likes"
msgstr "Patīk"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
msgid "Dislikes"
msgstr "Nepatīk"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Vērtējums"

#: pynicotine/gtkgui/interests.py:111
msgid "Item"
msgstr "Vienums"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
msgid "_Recommendations for Item"
msgstr "_Ieteikumi vienumam"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
msgid "I _Like This"
msgstr "Man šis _patīk"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
msgid "I _Dislike This"
msgstr "Man šis _nepatīk"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
msgid "Recommendations"
msgstr "Ieteikumi"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
msgid "Similar Users"
msgstr "Līdzīgi lietotāji"

#: pynicotine/gtkgui/interests.py:427
#, python-format
msgid "Recommendations (%s)"
msgstr "Ieteikumi (%s)"

#: pynicotine/gtkgui/interests.py:451
#, python-format
msgid "Similar Users (%s)"
msgstr "Līdzīgi lietotāji (%s)"

#: pynicotine/gtkgui/mainwindow.py:238
msgid "Search log…"
msgstr "Meklēšana žurnālā…"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, python-format
msgid "Private Message from %(user)s"
msgstr "Privāta ziņa no %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
msgid "Wishlist Results Found"
msgstr "Atrastie rezultāti no vēlmju saraksta"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
msgid "User Profiles"
msgstr "Lietotāju profili"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
msgid "Chat Rooms"
msgstr "Tērzēšanas istabas"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "Intereses"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
msgid "Chat"
msgstr "Tērzēšana"

#: pynicotine/gtkgui/mainwindow.py:1111
msgid "[Debug] Connections"
msgstr "[Debug] Savienojumi"

#: pynicotine/gtkgui/mainwindow.py:1112
msgid "[Debug] Messages"
msgstr "[Debug] Ziņas"

#: pynicotine/gtkgui/mainwindow.py:1113
msgid "[Debug] Transfers"
msgstr "[Debug] Pārsūtījumi"

#: pynicotine/gtkgui/mainwindow.py:1114
msgid "[Debug] Miscellaneous"
msgstr "[Debug] Dažādi"

#: pynicotine/gtkgui/mainwindow.py:1119
msgid "_Find…"
msgstr "_Atrast…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
msgid "_Copy"
msgstr "_Kopēt"

#: pynicotine/gtkgui/mainwindow.py:1122
msgid "Copy _All"
msgstr "Kopēt _visu"

#: pynicotine/gtkgui/mainwindow.py:1127
msgid "View _Debug Logs"
msgstr "Skatīt _atkļūdošanas žurnālus"

#: pynicotine/gtkgui/mainwindow.py:1128
msgid "View _Transfer Logs"
msgstr "Skatīt _pārsūtījumu žurnālus"

#: pynicotine/gtkgui/mainwindow.py:1132
msgid "_Log Categories"
msgstr "Žurnā_la kategorijas"

#: pynicotine/gtkgui/mainwindow.py:1134
msgid "Clear Log View"
msgstr "Notīrīt žurnāla skatu"

#: pynicotine/gtkgui/mainwindow.py:1199
msgid "Preparing Shares"
msgstr "Sagatavo koplietojumus"

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr "Skenē koplietojumus"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
msgid "Shared Folders"
msgstr "Koplietotās mapes"

#: pynicotine/gtkgui/popovers/chathistory.py:77
msgid "Latest Message"
msgstr "Jaunākā ziņa"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Istaba"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Lietotāji"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
msgid "Join Room"
msgstr "Ienākt istabā"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
msgid "Leave Room"
msgstr "Iziet no istabas"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
msgid "Disown Private Room"
msgstr "Atteikties no privātās istabas"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
msgid "Cancel Room Membership"
msgstr "Anulēt istabas dalību"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
msgid "Close All Tabs…"
msgstr "Aizvērt visas cilnes…"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
msgid "_Close Tab"
msgstr "Aizvērt _cilni"

#: pynicotine/gtkgui/privatechat.py:379
msgid "View Chat Log"
msgstr "Skatīt tērzēšanas žurnālu"

#: pynicotine/gtkgui/privatechat.py:382
msgid "Delete Chat Log…"
msgstr "Dzēst tērzēšanas žurnālu…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
msgid "User Actions"
msgstr "Lietotāja darbības"

#: pynicotine/gtkgui/privatechat.py:477
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Vai tiešām vēlaties neatgriezeniski dzēst visas žurnālā reģistrētās ziņas "
"šim lietotājam?"

#: pynicotine/gtkgui/privatechat.py:528
msgid "* Messages sent while you were offline"
msgstr "* Ziņas nosūtītas, kamēr bijāt bezsaistē"

#: pynicotine/gtkgui/search.py:90
msgid "_Global"
msgstr "_Globāli"

#: pynicotine/gtkgui/search.py:91
msgid "_Buddies"
msgstr "_Draugi"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
msgid "_Rooms"
msgstr "_Istabas"

#: pynicotine/gtkgui/search.py:93
msgid "_User"
msgstr "_Lietotājs"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
msgid "In Queue"
msgstr "Rindā"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
msgid "File Type"
msgstr "Faila tips"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Faila nosaukums"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Izmērs"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
msgid "Quality"
msgstr "Kvalitāte"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
msgid "Copy _File Path"
msgstr "Kopēt _faila ceļu"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "Kopēt _URL"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
msgid "Copy Folder U_RL"
msgstr "Kopēt mapes U_RL"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
msgid "_Download File(s)"
msgstr "_Lejupielādēt failu(s)"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
msgid "Download File(s) _To…"
msgstr "Lejupielādēt failu(s) _uz…"

#: pynicotine/gtkgui/search.py:615
msgid "Download _Folder(s)"
msgstr "Lejupielādēt _mapi(es)"

#: pynicotine/gtkgui/search.py:616
msgid "Download F_older(s) To…"
msgstr "Lejupielādēt m_api(es) uz…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
msgid "View User _Profile"
msgstr "Skatīt lietotāja _profilu"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
msgid "_Browse Folder"
msgstr "_Pārlūkot mapi"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
msgid "F_ile Properties"
msgstr "Fa_ila rekvizīti"

#: pynicotine/gtkgui/search.py:629
msgid "Copy Search Term"
msgstr "Kopēt meklēšanas vienumu"

#: pynicotine/gtkgui/search.py:631
msgid "Clear All Results"
msgstr "Notīrīt visus rezultātus"

#: pynicotine/gtkgui/search.py:718
msgid "Clear Filters"
msgstr "Notīrīt filtrus"

#: pynicotine/gtkgui/search.py:721
msgid "Restore Filters"
msgstr "Atjaunot filtrus"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, python-format
msgid "[PRIVATE]  %s"
msgstr "[PRIVĀTS] %s"

#: pynicotine/gtkgui/search.py:1273
#, python-format
msgid "_Result Filters [%d]"
msgstr "_Rezultātu filtri [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
msgid "_Result Filters"
msgstr "_Rezultātu filtri"

#: pynicotine/gtkgui/search.py:1277
#, python-format
msgid "%d active filter(s)"
msgstr "%d aktīvais(ie) filtrs(i)"

#: pynicotine/gtkgui/search.py:1329
msgid "Add Wi_sh"
msgstr "Pievienot vēl_mi"

#: pynicotine/gtkgui/search.py:1332
msgid "Remove Wi_sh"
msgstr "Noņemt vēl_mi"

#: pynicotine/gtkgui/search.py:1349
msgid "Select User's Results"
msgstr "Atlasīt lietotāja rezultātus"

#: pynicotine/gtkgui/search.py:1472
#, python-format
msgid "Total: %s"
msgstr "Kopā: %s"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
msgid "Results"
msgstr "Rezultāti"

#: pynicotine/gtkgui/search.py:1590
msgid "Select Destination Folder for File(s)"
msgstr "Atlasiet faila(u) galamērķa mapi"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
msgid "Select Destination Folder"
msgstr "Atlasiet galamērķa mapi"

#: pynicotine/gtkgui/transfers.py:61
msgid "Queued"
msgstr "Rindā"

#: pynicotine/gtkgui/transfers.py:62
msgid "Queued (prioritized)"
msgstr "Rindā (prioritizēta)"

#: pynicotine/gtkgui/transfers.py:63
msgid "Queued (privileged)"
msgstr "Rindā (priviliģēta)"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Statusa iegūšana"

#: pynicotine/gtkgui/transfers.py:65
msgid "Transferring"
msgstr "Pārsūta"

#: pynicotine/gtkgui/transfers.py:66
msgid "Connection closed"
msgstr "Savienojums aizvērts"

#: pynicotine/gtkgui/transfers.py:67
msgid "Connection timeout"
msgstr "Savienojuma taimauts"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Lietotājs izrakstījies"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
msgid "Cancelled"
msgstr "Atcelts"

#: pynicotine/gtkgui/transfers.py:73
msgid "Download folder error"
msgstr "Lejupielādes mapes kļūda"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Lokālā faila kļūda"

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr "Aizliegts"

#: pynicotine/gtkgui/transfers.py:76
msgid "File not shared"
msgstr "Fails nav kopīgots"

#: pynicotine/gtkgui/transfers.py:77
msgid "Pending shutdown"
msgstr "Gaida izslēgšanu"

#: pynicotine/gtkgui/transfers.py:78
msgid "File read error"
msgstr "Failu nolasīšanas kļūda"

#: pynicotine/gtkgui/transfers.py:182
msgid "Queue"
msgstr "Rinda"

#: pynicotine/gtkgui/transfers.py:188
msgid "Percent"
msgstr "Procenti"

#: pynicotine/gtkgui/transfers.py:208
msgid "Time Elapsed"
msgstr "Pagājušais laiks"

#: pynicotine/gtkgui/transfers.py:215
msgid "Time Left"
msgstr "Atlikušais laiks"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
msgid "_Open File"
msgstr "_Atvērt failu"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
msgid "Open in File _Manager"
msgstr "Atvērt failu _pārvaldniekā"

#: pynicotine/gtkgui/transfers.py:286
msgid "_Search"
msgstr "_Meklēt"

#: pynicotine/gtkgui/transfers.py:289
msgid "Clear All"
msgstr "Notīrīt visu"

#: pynicotine/gtkgui/transfers.py:953
msgid "Select User's Transfers"
msgstr "Atlasīt lietotāja pārsūtījumus"

#: pynicotine/gtkgui/uploads.py:50
msgid "_Abort"
msgstr "_Pārtraukt"

#: pynicotine/gtkgui/uploads.py:74
msgid "Finished / Cancelled / Failed"
msgstr "Pabeigts / Atcelts / Neizdevies"

#: pynicotine/gtkgui/uploads.py:75
msgid "Finished / Cancelled"
msgstr "Pabeigts / Atcelts"

#: pynicotine/gtkgui/uploads.py:79
msgid "Failed"
msgstr "Neizdevies"

#: pynicotine/gtkgui/uploads.py:80
msgid "User Logged Off"
msgstr "Lietotājs izrakstījies"

#: pynicotine/gtkgui/uploads.py:142
#, python-format
msgid "Uploads: %(speed)s"
msgstr "Augšupielādes: %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr "Iziet..."

#: pynicotine/gtkgui/uploads.py:164
msgid "Clear Queued Uploads"
msgstr "Notīrīt rindā esošās augšupielādes"

#: pynicotine/gtkgui/uploads.py:165
msgid "Do you really want to clear all queued uploads?"
msgstr "Vai tiešām vēlaties notīrīt visas rindā esošās augšupielādes?"

#: pynicotine/gtkgui/uploads.py:177
msgid "Clear All Uploads"
msgstr "Notīrīt visas augšupielādes"

#: pynicotine/gtkgui/uploads.py:178
msgid "Do you really want to clear all uploads?"
msgstr "Vai tiešām vēlaties notīrīt visas augšupielādes?"

#: pynicotine/gtkgui/userbrowse.py:282
msgid "_Save Shares List to Disk"
msgstr "_Saglabāt koplietojumu sarakstu diskā"

#: pynicotine/gtkgui/userbrowse.py:292
msgid "Upload Folder & Subfolders…"
msgstr "Augšupielādēt mapi un apakšmapes…"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
msgid "Copy _Folder Path"
msgstr "Kopēt _mapes ceļu"

#: pynicotine/gtkgui/userbrowse.py:309
msgid "_Download Folder & Subfolders"
msgstr "_Lejupielādēt mapi un apakšmapes"

#: pynicotine/gtkgui/userbrowse.py:310
msgid "Download Folder & Subfolders _To…"
msgstr "Lejupielādēt mapi un apakšmapes _uz…"

#: pynicotine/gtkgui/userbrowse.py:334
msgid "File Name"
msgstr "Faila nosaukums"

#: pynicotine/gtkgui/userbrowse.py:373
msgid "Up_load File(s)…"
msgstr "Augšupie_lādēt failu(s)…"

#: pynicotine/gtkgui/userbrowse.py:374
msgid "Upload Folder…"
msgstr "Augšupielādēt mapi…"

#: pynicotine/gtkgui/userbrowse.py:396
msgid "Download Folder _To…"
msgstr "Lejupielādēt mapi _uz…"

#: pynicotine/gtkgui/userbrowse.py:600
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"Lietotāja koplietoto failu saraksts ir tukšs. Vai nu lietotājs neko "
"nekopīgo, vai arī kopīgo failus privāti."

#: pynicotine/gtkgui/userbrowse.py:615
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Nevar pieprasīt koplietojamos failus no lietotāja. Vai nu lietotājs ir "
"bezsaistē, vai klausīšanās porti ir aizvērti abās pusēs, vai arī ir "
"īslaicīgs savienojamības traucējums."

#: pynicotine/gtkgui/userbrowse.py:953
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Atlasiet vairāku mapju lejupielādes galamērķi"

#: pynicotine/gtkgui/userbrowse.py:997
msgid "Upload Folder (with Subfolders) To User"
msgstr "Augšupielādēt mapi (ar apakšmapēm) lietotājam"

#: pynicotine/gtkgui/userbrowse.py:999
msgid "Upload Folder To User"
msgstr "Augšupielādēt mapi lietotājam"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
msgid "Enter the name of the user you want to upload to:"
msgstr "Ievadiet tā lietotāja vārdu, kuram vēlaties augšupielādēt:"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
msgid "_Upload"
msgstr "_Augšupielāde"

#: pynicotine/gtkgui/userbrowse.py:1139
msgid "Select Destination Folder for Files"
msgstr "Atlasiet failu galamērķa mapi"

#: pynicotine/gtkgui/userbrowse.py:1161
msgid "Upload File(s) To User"
msgstr "Augšupielādējiet failu(s) lietotājam"

#: pynicotine/gtkgui/userinfo.py:376
msgid "Copy Picture"
msgstr "Kopēt attēlu"

#: pynicotine/gtkgui/userinfo.py:377
msgid "Save Picture"
msgstr "Saglabāt attēlu"

#: pynicotine/gtkgui/userinfo.py:468
#, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Neizdevās ielādēt attēlu lietotājam %(user)s: %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Nevar pieprasīt informāciju no lietotāja. Vai nu jums abiem ir aizvērts "
"klausīšanās ports, vai lietotājs ir bezsaistē, vai arī ir īslaicīgs "
"savienojamības traucējums."

#: pynicotine/gtkgui/userinfo.py:577
msgid "Remove _Buddy"
msgstr "Noņemt _draugu"

#: pynicotine/gtkgui/userinfo.py:577
msgid "Add _Buddy"
msgstr "Pievienot _draugu"

#: pynicotine/gtkgui/userinfo.py:581
msgid "Unban User"
msgstr "Atbloķēt lietotāju"

#: pynicotine/gtkgui/userinfo.py:585
msgid "Unignore User"
msgstr "Beigt lietotāja ignorēšanu"

#: pynicotine/gtkgui/userinfo.py:613
msgid "Yes"
msgstr "Jā"

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr "Nē"

#: pynicotine/gtkgui/userinfo.py:773
msgid "Please enter number of days."
msgstr "Lūdzu, ievadiet dienu skaitu."

#: pynicotine/gtkgui/userinfo.py:787
#, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Dāviniet dienas no savām Soulseek privilēģijām lietotājam %(user)s "
"(%(days_left)s):"

#: pynicotine/gtkgui/userinfo.py:788
#, python-format
msgid "%(days)s days left"
msgstr "Atlikušas %(days)s dienas"

#: pynicotine/gtkgui/userinfo.py:795
msgid "Gift Privileges"
msgstr "Dāvināt privilēģijas"

#: pynicotine/gtkgui/userinfo.py:797
msgid "_Give Privileges"
msgstr "_Dot privilēģijas"

#: pynicotine/gtkgui/widgets/dialogs.py:309
msgid "Close"
msgstr "Aizvērt"

#: pynicotine/gtkgui/widgets/dialogs.py:488
msgid "_Yes"
msgstr "_Jā"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
msgid "_OK"
msgstr "_OK"

#: pynicotine/gtkgui/widgets/filechooser.py:39
msgid "Select a File"
msgstr "Atlasiet failu"

#: pynicotine/gtkgui/widgets/filechooser.py:174
msgid "Select a Folder"
msgstr "Atlasiet mapi"

#: pynicotine/gtkgui/widgets/filechooser.py:179
msgid "_Select"
msgstr "Atla_sīt"

#: pynicotine/gtkgui/widgets/filechooser.py:196
msgid "Select an Image"
msgstr "Atlasiet attēlu"

#: pynicotine/gtkgui/widgets/filechooser.py:203
msgid "All images"
msgstr "Visi attēli"

#: pynicotine/gtkgui/widgets/filechooser.py:241
msgid "Save as…"
msgstr "Saglabāt kā…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
msgid "(None)"
msgstr "(Neviens)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
msgid "Close Tab"
msgstr "Aizvērt cilni"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
msgid "Close All Tabs?"
msgstr "Vai aizvērt visas cilnes?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
msgid "Do you really want to close all tabs?"
msgstr "Vai tiešām vēlaties aizvērt visas cilnes?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, python-format
msgid "%i Unread Tab(s)"
msgstr "%i nelasīta(s) cilne(s)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
msgid "All Tabs"
msgstr "Visas cilnes"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
msgid "Re_open Closed Tab"
msgstr "_Atkārtoti atvērt aizvērto cilni"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, python-format
msgid "%s File(s) Selected"
msgstr "Atlasīts(i) %s fails(i)"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
msgid "_Browse Files"
msgstr "_Pārlūkot failus"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
msgid "_Add Buddy"
msgstr "_Pievienot draugu"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
msgid "Show IP A_ddress"
msgstr "Rādīt IP a_dresi"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
msgid "Private Rooms"
msgstr "Privātās istabas"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, python-format
msgid "Remove from Private Room %s"
msgstr "Noņemt no privātās istabas %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, python-format
msgid "Add to Private Room %s"
msgstr "Pievienot privātajai istabai %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, python-format
msgid "Remove as Operator of %s"
msgstr "Noņemt kā %s operatoru"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, python-format
msgid "Add as Operator of %s"
msgstr "Pievienot kā %s operatoru"

#: pynicotine/gtkgui/widgets/textentry.py:37
msgid "Send message…"
msgstr "Sūtīt ziņu…"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
msgid "Find Previous Match"
msgstr "Atrast iepriekšējo atbilstību"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
msgid "Find Next Match"
msgstr "Atrast nākamo atbilstību"

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr "--- iepriekšējās ziņas augstāk ---"

#: pynicotine/gtkgui/widgets/theme.py:243
msgid "Executable"
msgstr "Izpildāms"

#: pynicotine/gtkgui/widgets/theme.py:244
msgid "Audio"
msgstr "Audio"

#: pynicotine/gtkgui/widgets/theme.py:245
msgid "Image"
msgstr "Attēls"

#: pynicotine/gtkgui/widgets/theme.py:246
msgid "Archive"
msgstr "Arhīvs"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
msgid "Miscellaneous"
msgstr "Dažādi"

#: pynicotine/gtkgui/widgets/theme.py:248
msgid "Video"
msgstr "Video"

#: pynicotine/gtkgui/widgets/theme.py:249
msgid "Document"
msgstr "Dokuments"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr "Teksts"

#: pynicotine/gtkgui/widgets/theme.py:357
#, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr "Radās kļūda, ielādējot pielāgoto ikonu %(path)s: %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Hide Nicotine+"
msgstr "Paslēpt Nicotine+"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Show Nicotine+"
msgstr "Rādīt Nicotine+"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, python-format
msgid "Column #%i"
msgstr "Kolonna #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
msgid "Ungrouped"
msgstr "Negrupēti"

#: pynicotine/gtkgui/widgets/treeview.py:960
msgid "Group by Folder"
msgstr "Grupēt pēc mapes"

#: pynicotine/gtkgui/widgets/treeview.py:963
msgid "Group by User"
msgstr "Grupēt pēc lietotāja"

#: pynicotine/headless/application.py:77
#, python-format
msgid "Do you really want to exit? %s"
msgstr "Vai tiešām vēlaties iziet? %s"

#: pynicotine/headless/application.py:81
#, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr "Lietotājs %s jau pastāv, un ievadītā parole nav derīga."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr "Ierakstiet %s, lai pieslēgtos ar citu lietotājvārdu vai paroli."

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
msgid "Password: "
msgstr "Parole: "

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Lai izveidotu jaunu Soulseek kontu, ievadiet vēlamo lietotājvārdu un paroli. "
"Ja jums jau ir konts, ievadiet savus esošos pieslēgšanās datus."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr "Tālāk norādītie koplietojumi nav pieejami:"

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr "Mēģināt atkārtotu pārskenēšanu? %s"

#: pynicotine/logfacility.py:181
#, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr "Nevarēja ierakstīt žurnāla failā \"%(filename)s\": %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Nevar piekļūt žurnāla failam %(path)s: %(error)s"

#: pynicotine/networkfilter.py:40
msgid "Andorra"
msgstr "Andorra"

#: pynicotine/networkfilter.py:41
msgid "United Arab Emirates"
msgstr "Apvienotie Arābu Emirāti"

#: pynicotine/networkfilter.py:42
msgid "Afghanistan"
msgstr "Afganistāna"

#: pynicotine/networkfilter.py:43
msgid "Antigua & Barbuda"
msgstr "Antigva un Barbuda"

#: pynicotine/networkfilter.py:44
msgid "Anguilla"
msgstr "Angilja"

#: pynicotine/networkfilter.py:45
msgid "Albania"
msgstr "Albānija"

#: pynicotine/networkfilter.py:46
msgid "Armenia"
msgstr "Armēnija"

#: pynicotine/networkfilter.py:47
msgid "Angola"
msgstr "Angola"

#: pynicotine/networkfilter.py:48
msgid "Antarctica"
msgstr "Antarktīda"

#: pynicotine/networkfilter.py:49
msgid "Argentina"
msgstr "Argentīna"

#: pynicotine/networkfilter.py:50
msgid "American Samoa"
msgstr "Amerikas Samoa"

#: pynicotine/networkfilter.py:51
msgid "Austria"
msgstr "Austrija"

#: pynicotine/networkfilter.py:52
msgid "Australia"
msgstr "Austrālija"

#: pynicotine/networkfilter.py:53
msgid "Aruba"
msgstr "Aruba"

#: pynicotine/networkfilter.py:54
msgid "Åland Islands"
msgstr "Ālandu salas"

#: pynicotine/networkfilter.py:55
msgid "Azerbaijan"
msgstr "Azerbaidžāna"

#: pynicotine/networkfilter.py:56
msgid "Bosnia & Herzegovina"
msgstr "Bosnija un Hercegovina"

#: pynicotine/networkfilter.py:57
msgid "Barbados"
msgstr "Barbadosa"

#: pynicotine/networkfilter.py:58
msgid "Bangladesh"
msgstr "Bangladeša"

#: pynicotine/networkfilter.py:59
msgid "Belgium"
msgstr "Beļģija"

#: pynicotine/networkfilter.py:60
msgid "Burkina Faso"
msgstr "Burkinafaso"

#: pynicotine/networkfilter.py:61
msgid "Bulgaria"
msgstr "Bulgārija"

#: pynicotine/networkfilter.py:62
msgid "Bahrain"
msgstr "Bahreina"

#: pynicotine/networkfilter.py:63
msgid "Burundi"
msgstr "Burundi"

#: pynicotine/networkfilter.py:64
msgid "Benin"
msgstr "Benina"

#: pynicotine/networkfilter.py:65
msgid "Saint Barthelemy"
msgstr "Svētā Bartelemija"

#: pynicotine/networkfilter.py:66
msgid "Bermuda"
msgstr "Bermudu salas"

#: pynicotine/networkfilter.py:67
msgid "Brunei Darussalam"
msgstr "Bruneja Darusalama"

#: pynicotine/networkfilter.py:68
msgid "Bolivia"
msgstr "Bolīvija"

#: pynicotine/networkfilter.py:69
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sintēstatiusa un Saba"

#: pynicotine/networkfilter.py:70
msgid "Brazil"
msgstr "Brazīlija"

#: pynicotine/networkfilter.py:71
msgid "Bahamas"
msgstr "Bahamu Salas"

#: pynicotine/networkfilter.py:72
msgid "Bhutan"
msgstr "Butāna"

#: pynicotine/networkfilter.py:73
msgid "Bouvet Island"
msgstr "Buvē sala"

#: pynicotine/networkfilter.py:74
msgid "Botswana"
msgstr "Botsvāna"

#: pynicotine/networkfilter.py:75
msgid "Belarus"
msgstr "Baltkrievija"

#: pynicotine/networkfilter.py:76
msgid "Belize"
msgstr "Beliza"

#: pynicotine/networkfilter.py:77
msgid "Canada"
msgstr "Kanāda"

#: pynicotine/networkfilter.py:78
msgid "Cocos (Keeling) Islands"
msgstr "Kokosu (Kīlinga) Salas"

#: pynicotine/networkfilter.py:79
msgid "Democratic Republic of Congo"
msgstr "Kongo Demokrātiskā Republika"

#: pynicotine/networkfilter.py:80
msgid "Central African Republic"
msgstr "Centrālāfrikas Republika"

#: pynicotine/networkfilter.py:81
msgid "Congo"
msgstr "Kongo"

#: pynicotine/networkfilter.py:82
msgid "Switzerland"
msgstr "Šveice"

#: pynicotine/networkfilter.py:83
msgid "Ivory Coast"
msgstr "Kotdivuāra"

#: pynicotine/networkfilter.py:84
msgid "Cook Islands"
msgstr "Kuka Salas"

#: pynicotine/networkfilter.py:85
msgid "Chile"
msgstr "Čīle"

#: pynicotine/networkfilter.py:86
msgid "Cameroon"
msgstr "Kamerūna"

#: pynicotine/networkfilter.py:87
msgid "China"
msgstr "Ķīna"

#: pynicotine/networkfilter.py:88
msgid "Colombia"
msgstr "Kolumbija"

#: pynicotine/networkfilter.py:89
msgid "Costa Rica"
msgstr "Kostarika"

#: pynicotine/networkfilter.py:90
msgid "Cuba"
msgstr "Kuba"

#: pynicotine/networkfilter.py:91
msgid "Cabo Verde"
msgstr "Kaboverde"

#: pynicotine/networkfilter.py:92
msgid "Curaçao"
msgstr "Kirasao"

#: pynicotine/networkfilter.py:93
msgid "Christmas Island"
msgstr "Ziemassvētku Sala"

#: pynicotine/networkfilter.py:94
msgid "Cyprus"
msgstr "Kipra"

#: pynicotine/networkfilter.py:95
msgid "Czechia"
msgstr "Čehija"

#: pynicotine/networkfilter.py:96
msgid "Germany"
msgstr "Vācija"

#: pynicotine/networkfilter.py:97
msgid "Djibouti"
msgstr "Džibutija"

#: pynicotine/networkfilter.py:98
msgid "Denmark"
msgstr "Dānija"

#: pynicotine/networkfilter.py:99
msgid "Dominica"
msgstr "Dominika"

#: pynicotine/networkfilter.py:100
msgid "Dominican Republic"
msgstr "Dominikānas Republika"

#: pynicotine/networkfilter.py:101
msgid "Algeria"
msgstr "Alžīrija"

#: pynicotine/networkfilter.py:102
msgid "Ecuador"
msgstr "Ekvadora"

#: pynicotine/networkfilter.py:103
msgid "Estonia"
msgstr "Igaunija"

#: pynicotine/networkfilter.py:104
msgid "Egypt"
msgstr "Ēģipte"

#: pynicotine/networkfilter.py:105
msgid "Western Sahara"
msgstr "Rietumsahāra"

#: pynicotine/networkfilter.py:106
msgid "Eritrea"
msgstr "Eritreja"

#: pynicotine/networkfilter.py:107
msgid "Spain"
msgstr "Spānija"

#: pynicotine/networkfilter.py:108
msgid "Ethiopia"
msgstr "Etiopija"

#: pynicotine/networkfilter.py:109
msgid "Europe"
msgstr "Eiropa"

#: pynicotine/networkfilter.py:110
msgid "Finland"
msgstr "Somija"

#: pynicotine/networkfilter.py:111
msgid "Fiji"
msgstr "Fidži"

#: pynicotine/networkfilter.py:112
msgid "Falkland Islands (Malvinas)"
msgstr "Folklenda Salas (Malvinu salas)"

#: pynicotine/networkfilter.py:113
msgid "Micronesia"
msgstr "Mikronēzija"

#: pynicotine/networkfilter.py:114
msgid "Faroe Islands"
msgstr "Fēru Salas"

#: pynicotine/networkfilter.py:115
msgid "France"
msgstr "Francija"

#: pynicotine/networkfilter.py:116
msgid "Gabon"
msgstr "Gabona"

#: pynicotine/networkfilter.py:117
msgid "Great Britain"
msgstr "Lielbritānija"

#: pynicotine/networkfilter.py:118
msgid "Grenada"
msgstr "Grenāda"

#: pynicotine/networkfilter.py:119
msgid "Georgia"
msgstr "Gruzija"

#: pynicotine/networkfilter.py:120
msgid "French Guiana"
msgstr "Franču Gviāna"

#: pynicotine/networkfilter.py:121
msgid "Guernsey"
msgstr "Gērnsija"

#: pynicotine/networkfilter.py:122
msgid "Ghana"
msgstr "Gana"

#: pynicotine/networkfilter.py:123
msgid "Gibraltar"
msgstr "Gibraltārs"

#: pynicotine/networkfilter.py:124
msgid "Greenland"
msgstr "Grenlande"

#: pynicotine/networkfilter.py:125
msgid "Gambia"
msgstr "Gambija"

#: pynicotine/networkfilter.py:126
msgid "Guinea"
msgstr "Gvineja"

#: pynicotine/networkfilter.py:127
msgid "Guadeloupe"
msgstr "Gvadelupa"

#: pynicotine/networkfilter.py:128
msgid "Equatorial Guinea"
msgstr "Ekvatoriālā Gvineja"

#: pynicotine/networkfilter.py:129
msgid "Greece"
msgstr "Grieķija"

#: pynicotine/networkfilter.py:130
msgid "South Georgia & South Sandwich Islands"
msgstr "Dienviddžordžija un Dienvidsendviču Salas"

#: pynicotine/networkfilter.py:131
msgid "Guatemala"
msgstr "Gvatemala"

#: pynicotine/networkfilter.py:132
msgid "Guam"
msgstr "Guama"

#: pynicotine/networkfilter.py:133
msgid "Guinea-Bissau"
msgstr "Gvineja-Bisava"

#: pynicotine/networkfilter.py:134
msgid "Guyana"
msgstr "Gajāna"

#: pynicotine/networkfilter.py:135
msgid "Hong Kong"
msgstr "Honkonga"

#: pynicotine/networkfilter.py:136
msgid "Heard & McDonald Islands"
msgstr "Hērda un Makdonalda Salas"

#: pynicotine/networkfilter.py:137
msgid "Honduras"
msgstr "Hondurasa"

#: pynicotine/networkfilter.py:138
msgid "Croatia"
msgstr "Horvātija"

#: pynicotine/networkfilter.py:139
msgid "Haiti"
msgstr "Haiti"

#: pynicotine/networkfilter.py:140
msgid "Hungary"
msgstr "Ungārija"

#: pynicotine/networkfilter.py:141
msgid "Indonesia"
msgstr "Indonēzija"

#: pynicotine/networkfilter.py:142
msgid "Ireland"
msgstr "Īrija"

#: pynicotine/networkfilter.py:143
msgid "Israel"
msgstr "Izraēla"

#: pynicotine/networkfilter.py:144
msgid "Isle of Man"
msgstr "Menas Sala"

#: pynicotine/networkfilter.py:145
msgid "India"
msgstr "Indija"

#: pynicotine/networkfilter.py:146
msgid "British Indian Ocean Territory"
msgstr "Britu Indijas Okeāna Teritorija"

#: pynicotine/networkfilter.py:147
msgid "Iraq"
msgstr "Irāka"

#: pynicotine/networkfilter.py:148
msgid "Iran"
msgstr "Irāna"

#: pynicotine/networkfilter.py:149
msgid "Iceland"
msgstr "Islande"

#: pynicotine/networkfilter.py:150
msgid "Italy"
msgstr "Itālija"

#: pynicotine/networkfilter.py:151
msgid "Jersey"
msgstr "Džērsija"

#: pynicotine/networkfilter.py:152
msgid "Jamaica"
msgstr "Jamaika"

#: pynicotine/networkfilter.py:153
msgid "Jordan"
msgstr "Jordānija"

#: pynicotine/networkfilter.py:154
msgid "Japan"
msgstr "Japāna"

#: pynicotine/networkfilter.py:155
msgid "Kenya"
msgstr "Kenija"

#: pynicotine/networkfilter.py:156
msgid "Kyrgyzstan"
msgstr "Kirgizstāna"

#: pynicotine/networkfilter.py:157
msgid "Cambodia"
msgstr "Kambodža"

#: pynicotine/networkfilter.py:158
msgid "Kiribati"
msgstr "Kiribati"

#: pynicotine/networkfilter.py:159
msgid "Comoros"
msgstr "Komoras"

#: pynicotine/networkfilter.py:160
msgid "Saint Kitts & Nevis"
msgstr "Sentkitsa un Nevisa"

#: pynicotine/networkfilter.py:161
msgid "North Korea"
msgstr "Ziemeļkoreja"

#: pynicotine/networkfilter.py:162
msgid "South Korea"
msgstr "Dienvidkoreja"

#: pynicotine/networkfilter.py:163
msgid "Kuwait"
msgstr "Kuveita"

#: pynicotine/networkfilter.py:164
msgid "Cayman Islands"
msgstr "Kaimanu Salas"

#: pynicotine/networkfilter.py:165
msgid "Kazakhstan"
msgstr "Kazahstāna"

#: pynicotine/networkfilter.py:166
msgid "Laos"
msgstr "Laosa"

#: pynicotine/networkfilter.py:167
msgid "Lebanon"
msgstr "Libāna"

#: pynicotine/networkfilter.py:168
msgid "Saint Lucia"
msgstr "Sentlūsija"

#: pynicotine/networkfilter.py:169
msgid "Liechtenstein"
msgstr "Lihtenšteina"

#: pynicotine/networkfilter.py:170
msgid "Sri Lanka"
msgstr "Šrilanka"

#: pynicotine/networkfilter.py:171
msgid "Liberia"
msgstr "Libērija"

#: pynicotine/networkfilter.py:172
msgid "Lesotho"
msgstr "Lesoto"

#: pynicotine/networkfilter.py:173
msgid "Lithuania"
msgstr "Lietuva"

#: pynicotine/networkfilter.py:174
msgid "Luxembourg"
msgstr "Luksemburga"

#: pynicotine/networkfilter.py:175
msgid "Latvia"
msgstr "Latvija"

#: pynicotine/networkfilter.py:176
msgid "Libya"
msgstr "Lībija"

#: pynicotine/networkfilter.py:177
msgid "Morocco"
msgstr "Maroka"

#: pynicotine/networkfilter.py:178
msgid "Monaco"
msgstr "Monako"

#: pynicotine/networkfilter.py:179
msgid "Moldova"
msgstr "Moldova"

#: pynicotine/networkfilter.py:180
msgid "Montenegro"
msgstr "Melnkalne"

#: pynicotine/networkfilter.py:181
msgid "Saint Martin"
msgstr "Senmartēna"

#: pynicotine/networkfilter.py:182
msgid "Madagascar"
msgstr "Madagaskara"

#: pynicotine/networkfilter.py:183
msgid "Marshall Islands"
msgstr "Māršala Salas"

#: pynicotine/networkfilter.py:184
msgid "North Macedonia"
msgstr "Ziemeļmaķedonija"

#: pynicotine/networkfilter.py:185
msgid "Mali"
msgstr "Mali"

#: pynicotine/networkfilter.py:186
msgid "Myanmar"
msgstr "Mjanma"

#: pynicotine/networkfilter.py:187
msgid "Mongolia"
msgstr "Mongolija"

#: pynicotine/networkfilter.py:188
msgid "Macau"
msgstr "Makao"

#: pynicotine/networkfilter.py:189
msgid "Northern Mariana Islands"
msgstr "Ziemeļu Marianas Salas"

#: pynicotine/networkfilter.py:190
msgid "Martinique"
msgstr "Martinika"

#: pynicotine/networkfilter.py:191
msgid "Mauritania"
msgstr "Mauritānija"

#: pynicotine/networkfilter.py:192
msgid "Montserrat"
msgstr "Montserrata"

#: pynicotine/networkfilter.py:193
msgid "Malta"
msgstr "Malta"

#: pynicotine/networkfilter.py:194
msgid "Mauritius"
msgstr "Maurīcija"

#: pynicotine/networkfilter.py:195
msgid "Maldives"
msgstr "Maldīvija"

#: pynicotine/networkfilter.py:196
msgid "Malawi"
msgstr "Malāvija"

#: pynicotine/networkfilter.py:197
msgid "Mexico"
msgstr "Meksika"

#: pynicotine/networkfilter.py:198
msgid "Malaysia"
msgstr "Malaizija"

#: pynicotine/networkfilter.py:199
msgid "Mozambique"
msgstr "Mozambika"

#: pynicotine/networkfilter.py:200
msgid "Namibia"
msgstr "Namībija"

#: pynicotine/networkfilter.py:201
msgid "New Caledonia"
msgstr "Jaunkaledonija"

#: pynicotine/networkfilter.py:202
msgid "Niger"
msgstr "Nigēra"

#: pynicotine/networkfilter.py:203
msgid "Norfolk Island"
msgstr "Norfolkas Sala"

#: pynicotine/networkfilter.py:204
msgid "Nigeria"
msgstr "Nigērija"

#: pynicotine/networkfilter.py:205
msgid "Nicaragua"
msgstr "Nikaragva"

#: pynicotine/networkfilter.py:206
msgid "Netherlands"
msgstr "Nīderlande"

#: pynicotine/networkfilter.py:207
msgid "Norway"
msgstr "Norvēģija"

#: pynicotine/networkfilter.py:208
msgid "Nepal"
msgstr "Nepāla"

#: pynicotine/networkfilter.py:209
msgid "Nauru"
msgstr "Nauru"

#: pynicotine/networkfilter.py:210
msgid "Niue"
msgstr "Niue"

#: pynicotine/networkfilter.py:211
msgid "New Zealand"
msgstr "Jaunzēlande"

#: pynicotine/networkfilter.py:212
msgid "Oman"
msgstr "Omāna"

#: pynicotine/networkfilter.py:213
msgid "Panama"
msgstr "Panama"

#: pynicotine/networkfilter.py:214
msgid "Peru"
msgstr "Peru"

#: pynicotine/networkfilter.py:215
msgid "French Polynesia"
msgstr "Franču Polinēzija"

#: pynicotine/networkfilter.py:216
msgid "Papua New Guinea"
msgstr "Papua Jaungvineja"

#: pynicotine/networkfilter.py:217
msgid "Philippines"
msgstr "Filipīnas"

#: pynicotine/networkfilter.py:218
msgid "Pakistan"
msgstr "Pakistāna"

#: pynicotine/networkfilter.py:219
msgid "Poland"
msgstr "Polija"

#: pynicotine/networkfilter.py:220
msgid "Saint Pierre & Miquelon"
msgstr "Senpjēra un Mikelona"

#: pynicotine/networkfilter.py:221
msgid "Pitcairn"
msgstr "Pitkērna"

#: pynicotine/networkfilter.py:222
msgid "Puerto Rico"
msgstr "Puertoriko"

#: pynicotine/networkfilter.py:223
msgid "State of Palestine"
msgstr "Palestīnas Valsts"

#: pynicotine/networkfilter.py:224
msgid "Portugal"
msgstr "Portugāle"

#: pynicotine/networkfilter.py:225
msgid "Palau"
msgstr "Palau"

#: pynicotine/networkfilter.py:226
msgid "Paraguay"
msgstr "Paragvaja"

#: pynicotine/networkfilter.py:227
msgid "Qatar"
msgstr "Katara"

#: pynicotine/networkfilter.py:228
msgid "Réunion"
msgstr "Reinjona"

#: pynicotine/networkfilter.py:229
msgid "Romania"
msgstr "Rumānija"

#: pynicotine/networkfilter.py:230
msgid "Serbia"
msgstr "Serbija"

#: pynicotine/networkfilter.py:231
msgid "Russia"
msgstr "Krievija"

#: pynicotine/networkfilter.py:232
msgid "Rwanda"
msgstr "Ruanda"

#: pynicotine/networkfilter.py:233
msgid "Saudi Arabia"
msgstr "Saūda Arābija"

#: pynicotine/networkfilter.py:234
msgid "Solomon Islands"
msgstr "Zālamana Salas"

#: pynicotine/networkfilter.py:235
msgid "Seychelles"
msgstr "Seišelas"

#: pynicotine/networkfilter.py:236
msgid "Sudan"
msgstr "Sudāna"

#: pynicotine/networkfilter.py:237
msgid "Sweden"
msgstr "Zviedrija"

#: pynicotine/networkfilter.py:238
msgid "Singapore"
msgstr "Singapūra"

#: pynicotine/networkfilter.py:239
msgid "Saint Helena"
msgstr "Svētās Helēnas Sala"

#: pynicotine/networkfilter.py:240
msgid "Slovenia"
msgstr "Slovēnija"

#: pynicotine/networkfilter.py:241
msgid "Svalbard & Jan Mayen Islands"
msgstr "Svalbāra un Jana Majena Salas"

#: pynicotine/networkfilter.py:242
msgid "Slovak Republic"
msgstr "Slovākijas Republika"

#: pynicotine/networkfilter.py:243
msgid "Sierra Leone"
msgstr "Sjerraleone"

#: pynicotine/networkfilter.py:244
msgid "San Marino"
msgstr "Sanmarīno"

#: pynicotine/networkfilter.py:245
msgid "Senegal"
msgstr "Senegāla"

#: pynicotine/networkfilter.py:246
msgid "Somalia"
msgstr "Somālija"

#: pynicotine/networkfilter.py:247
msgid "Suriname"
msgstr "Surinama"

#: pynicotine/networkfilter.py:248
msgid "South Sudan"
msgstr "Dienvidsudāna"

#: pynicotine/networkfilter.py:249
msgid "Sao Tome & Principe"
msgstr "Santome un Prinsipi"

#: pynicotine/networkfilter.py:250
msgid "El Salvador"
msgstr "Salvadora"

#: pynicotine/networkfilter.py:251
msgid "Sint Maarten"
msgstr "Sintmartena"

#: pynicotine/networkfilter.py:252
msgid "Syria"
msgstr "Sīrija"

#: pynicotine/networkfilter.py:253
msgid "Eswatini"
msgstr "Esvatīni"

#: pynicotine/networkfilter.py:254
msgid "Turks & Caicos Islands"
msgstr "Tērksas un Kaikosas Salas"

#: pynicotine/networkfilter.py:255
msgid "Chad"
msgstr "Čada"

#: pynicotine/networkfilter.py:256
msgid "French Southern Territories"
msgstr "Francijas Dienvidjūru Zemes"

#: pynicotine/networkfilter.py:257
msgid "Togo"
msgstr "Togo"

#: pynicotine/networkfilter.py:258
msgid "Thailand"
msgstr "Taizeme"

#: pynicotine/networkfilter.py:259
msgid "Tajikistan"
msgstr "Tadžikistāna"

#: pynicotine/networkfilter.py:260
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
msgid "Timor-Leste"
msgstr "Austrumtimora"

#: pynicotine/networkfilter.py:262
msgid "Turkmenistan"
msgstr "Turkmenistāna"

#: pynicotine/networkfilter.py:263
msgid "Tunisia"
msgstr "Tunisija"

#: pynicotine/networkfilter.py:264
msgid "Tonga"
msgstr "Tonga"

#: pynicotine/networkfilter.py:265
msgid "Türkiye"
msgstr "Turcija"

#: pynicotine/networkfilter.py:266
msgid "Trinidad & Tobago"
msgstr "Trinidāda un Tobāgo"

#: pynicotine/networkfilter.py:267
msgid "Tuvalu"
msgstr "Tuvalu"

#: pynicotine/networkfilter.py:268
msgid "Taiwan"
msgstr "Taivāna"

#: pynicotine/networkfilter.py:269
msgid "Tanzania"
msgstr "Tanzānija"

#: pynicotine/networkfilter.py:270
msgid "Ukraine"
msgstr "Ukraina"

#: pynicotine/networkfilter.py:271
msgid "Uganda"
msgstr "Uganda"

#: pynicotine/networkfilter.py:272
msgid "U.S. Minor Outlying Islands"
msgstr "ASV Mazās Aizjūras Salas"

#: pynicotine/networkfilter.py:273
msgid "United States"
msgstr "Amerikas Savienotās Valstis"

#: pynicotine/networkfilter.py:274
msgid "Uruguay"
msgstr "Urugvaja"

#: pynicotine/networkfilter.py:275
msgid "Uzbekistan"
msgstr "Uzbekistāna"

#: pynicotine/networkfilter.py:276
msgid "Holy See (Vatican City State)"
msgstr "Svētais Krēsls (Vatikāna Pilsētvalsts)"

#: pynicotine/networkfilter.py:277
msgid "Saint Vincent & The Grenadines"
msgstr "Sentvinsenta un Grenadīnas"

#: pynicotine/networkfilter.py:278
msgid "Venezuela"
msgstr "Venecuēla"

#: pynicotine/networkfilter.py:279
msgid "British Virgin Islands"
msgstr "Britu Virdžīnu Salas"

#: pynicotine/networkfilter.py:280
msgid "U.S. Virgin Islands"
msgstr "ASV Virdžīnu Salas"

#: pynicotine/networkfilter.py:281
msgid "Viet Nam"
msgstr "Vjetnama"

#: pynicotine/networkfilter.py:282
msgid "Vanuatu"
msgstr "Vanuatu"

#: pynicotine/networkfilter.py:283
msgid "Wallis & Futuna"
msgstr "Volisa un Futuna"

#: pynicotine/networkfilter.py:284
msgid "Samoa"
msgstr "Samoa"

#: pynicotine/networkfilter.py:285
msgid "Yemen"
msgstr "Jemena"

#: pynicotine/networkfilter.py:286
msgid "Mayotte"
msgstr "Majota"

#: pynicotine/networkfilter.py:287
msgid "South Africa"
msgstr "Dienvidāfrika"

#: pynicotine/networkfilter.py:288
msgid "Zambia"
msgstr "Zambija"

#: pynicotine/networkfilter.py:289
msgid "Zimbabwe"
msgstr "Zimbabve"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "Ziņas teksta pārvēršana runā neizdevās: %s"

#: pynicotine/nowplaying.py:130
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr "Last.fm: Lūdzu, norādiet savu Last.fm lietotājvārdu un API atslēgu"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
msgid "Now Playing Error"
msgstr "\"Tagad atskaņo\" kļūda"

#: pynicotine/nowplaying.py:140
#, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm: Nevarēja izveidot savienojumu ar Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:162
#, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr "Last.fm: Nevarēja iegūt nesenāko dziesmu no Audioscrobbler: %(error)s"

#: pynicotine/nowplaying.py:196
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS: Nevarēja atrast piemērotu MPRIS atskaņotāju"

#: pynicotine/nowplaying.py:201
#, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr "Atrasti vairāki MPRIS atskaņotāji: %(players)s. Izmanto: %(player)s"

#: pynicotine/nowplaying.py:204
#, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Automātiski atrasts MPRIS atskaņotājs: %s"

#: pynicotine/nowplaying.py:219
#, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr "MPRIS: Radās problēma vaicājumā %(player)s: %(exception)s"

#: pynicotine/nowplaying.py:266
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "ListenBrainz: Lūdzu, norādiet savu ListenBrainz lietotājvārdu"

#: pynicotine/nowplaying.py:275
#, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "ListenBrainz: Nevarēja izveidot savienojumu ar ListenBrainz: %(error)s"

#: pynicotine/nowplaying.py:283
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr "ListenBrainz: Neizskatās, ka Jūs šobrīd kaut ko klausāties"

#: pynicotine/nowplaying.py:297
#, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz: Nevarēja iegūt pašreizējo celiņu no ListenBrainz: %(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
msgid "Network Filters"
msgstr "Tīkla filtri"

#: pynicotine/plugins/core_commands/__init__.py:44
msgid "List available commands"
msgstr "Pieejamo komandu saraksts"

#: pynicotine/plugins/core_commands/__init__.py:49
msgid "Connect to the server"
msgstr "Savienoties ar serveri"

#: pynicotine/plugins/core_commands/__init__.py:53
msgid "Disconnect from the server"
msgstr "Atvienoties no servera"

#: pynicotine/plugins/core_commands/__init__.py:58
msgid "Toggle away status"
msgstr "Pārslēgt aizgājis statusu"

#: pynicotine/plugins/core_commands/__init__.py:62
msgid "Manage plugins"
msgstr "Spraudņu pārvaldība"

#: pynicotine/plugins/core_commands/__init__.py:74
msgid "Clear chat window"
msgstr "Notīrīt tērzēšanas logu"

#: pynicotine/plugins/core_commands/__init__.py:80
msgid "Say something in the third-person"
msgstr "Sacīt kaut ko trešajā personā"

#: pynicotine/plugins/core_commands/__init__.py:87
msgid "Announce the song currently playing"
msgstr "Paziņot pašlaik atskaņoto dziesmu"

#: pynicotine/plugins/core_commands/__init__.py:94
msgid "Join chat room"
msgstr "Pievienoties tērzēšanas istabai"

#: pynicotine/plugins/core_commands/__init__.py:102
msgid "Leave chat room"
msgstr "Iziet no tērzēšanas istabas"

#: pynicotine/plugins/core_commands/__init__.py:110
msgid "Say message in specified chat room"
msgstr "Sacīt ziņu norādītajā tērzēšanas istabā"

#: pynicotine/plugins/core_commands/__init__.py:117
msgid "Open private chat"
msgstr "Atvērt privāto tērzēšanu"

#: pynicotine/plugins/core_commands/__init__.py:125
msgid "Close private chat"
msgstr "Aizvērt privāto tērzēšanu"

#: pynicotine/plugins/core_commands/__init__.py:133
msgid "Request user's client version"
msgstr "Pieprasīt lietotāja klienta versiju"

#: pynicotine/plugins/core_commands/__init__.py:142
msgid "Send private message to user"
msgstr "Sūtīt privātu ziņu lietotājam"

#: pynicotine/plugins/core_commands/__init__.py:150
msgid "Add user to buddy list"
msgstr "Pievienot lietotāju draugu sarakstam"

#: pynicotine/plugins/core_commands/__init__.py:158
msgid "Remove buddy from buddy list"
msgstr "Noņemt draugu no draugu saraksta"

#: pynicotine/plugins/core_commands/__init__.py:166
msgid "Browse files of user"
msgstr "Pārlūkot lietotāja failus"

#: pynicotine/plugins/core_commands/__init__.py:175
msgid "Show user profile information"
msgstr "Rādīt lietotāja profila informāciju"

#: pynicotine/plugins/core_commands/__init__.py:183
msgid "Show IP address or username"
msgstr "Rādīt IP adresi vai lietotājvārdu"

#: pynicotine/plugins/core_commands/__init__.py:190
msgid "Block connections from user or IP address"
msgstr "Bloķēt savienojumus no lietotāja vai IP adreses"

#: pynicotine/plugins/core_commands/__init__.py:197
msgid "Remove user or IP address from ban lists"
msgstr "Noņemt lietotāju vai IP adresi no aizlieguma sarakstiem"

#: pynicotine/plugins/core_commands/__init__.py:204
msgid "Silence messages from user or IP address"
msgstr "Apklusināt ziņas no lietotāja vai IP adreses"

#: pynicotine/plugins/core_commands/__init__.py:212
msgid "Remove user or IP address from ignore lists"
msgstr "Noņemt lietotāju vai IP adresi no ignorēšanas sarakstiem"

#: pynicotine/plugins/core_commands/__init__.py:220
msgid "Add share"
msgstr "Pievienot koplietojumu"

#: pynicotine/plugins/core_commands/__init__.py:226
msgid "Remove share"
msgstr "Noņemt koplietojumu"

#: pynicotine/plugins/core_commands/__init__.py:233
msgid "List shares"
msgstr "Uzskaitīt koplietojumus"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Pārskenēt koplietojumus"

#: pynicotine/plugins/core_commands/__init__.py:246
msgid "Start global file search"
msgstr "Sākt globālu failu meklēšanu"

#: pynicotine/plugins/core_commands/__init__.py:254
msgid "Search files in joined rooms"
msgstr "Meklēt failus pievienotajās istabās"

#: pynicotine/plugins/core_commands/__init__.py:262
msgid "Search files of all buddies"
msgstr "Meklēt visu draugu failos"

#: pynicotine/plugins/core_commands/__init__.py:270
msgid "Search a user's shared files"
msgstr "Meklēt lietotāja koplietojamos failos"

#: pynicotine/plugins/core_commands/__init__.py:296
#, python-format
msgid "Listing %(num)i available commands:"
msgstr "Uzskaita %(num)i pieejamās komandas:"

#: pynicotine/plugins/core_commands/__init__.py:298
#, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr "Uzskaita %(num)i pieejamās komandas, kas atbilst \"%(query)s\":"

#: pynicotine/plugins/core_commands/__init__.py:311
#, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Ievadiet %(command)s, lai uzskaitītu līdzīgas komandas"

#: pynicotine/plugins/core_commands/__init__.py:314
#, python-format
msgid "Type %(command)s to list available commands"
msgstr "Ievadiet %(command)s, lai uzskaitītu pieejamās komandas"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, python-format
msgid "Not joined in room %s"
msgstr "Nav pievienojies istabā %s"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr "Nav ziņapmaiņas ar lietotāju %s"

#: pynicotine/plugins/core_commands/__init__.py:408
#, python-format
msgid "Closed private chat of user %s"
msgstr "Aizvērta privātā tērzēšana ar lietotāju %s"

#: pynicotine/plugins/core_commands/__init__.py:476
#, python-format
msgid "Banned %s"
msgstr "Aizliegts %s"

#: pynicotine/plugins/core_commands/__init__.py:490
#, python-format
msgid "Unbanned %s"
msgstr "Atcelts aizliegums %s"

#: pynicotine/plugins/core_commands/__init__.py:503
#, python-format
msgid "Ignored %s"
msgstr "Ignorēts %s"

#: pynicotine/plugins/core_commands/__init__.py:517
#, python-format
msgid "Unignored %s"
msgstr "Neignorēts %s"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr "Uzskaitīti %(num_listed)s koplietojumi (%(num_total)s konfigurēti)"

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr "Nevar kopīgot nepieejamu mapi \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr ""
"Pievienots %(group_name)s koplietojums \"%(virtual_name)s\" (nepieciešama "
"pārskenēšana)"

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr "Nav koplietojuma ar nosaukumu \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr "Noņemts koplietojums \"%s\" (nepieciešama pārskenēšana)"

#: pynicotine/pluginsystem.py:413
msgid "Loading plugin system"
msgstr "Ielādē spraudņu sistēmu"

#: pynicotine/pluginsystem.py:516
#, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Nevar ielādēt spraudni %(name)s. Spraudņa mapes nosaukumā ir nederīgas "
"rakstzīmes: %(characters)s"

#: pynicotine/pluginsystem.py:548
#, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr "Konfliktējoša %(interface)s komanda spraudnī %(name)s: %(command)s"

#: pynicotine/pluginsystem.py:575
#, python-format
msgid "Loaded plugin %s"
msgstr "Ielādēts spraudnis %s"

#: pynicotine/pluginsystem.py:579
#, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Nevar ielādēt spraudni %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, python-format
msgid "Unloaded plugin %s"
msgstr "Izlādēts spraudnis %s"

#: pynicotine/pluginsystem.py:648
#, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Nevar izlādēt spraudni %(module)s\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Spraudnis %(module)s nenostrādāja ar kļūdu %(errortype)s: %(error)s.\n"
"Trasējums: %(trace)s"

#: pynicotine/pluginsystem.py:810
msgid "No description"
msgstr "Nav apraksta"

#: pynicotine/pluginsystem.py:887
#, python-format
msgid "Missing %s argument"
msgstr "Trūkst %s argumenta"

#: pynicotine/pluginsystem.py:896
#, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Nederīgs arguments, iespējamās izvēles: %s"

#: pynicotine/pluginsystem.py:901
#, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Lietojums: %(command)s %(parameters)s"

#: pynicotine/pluginsystem.py:940
#, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Nezināma komanda: %(command)s. Ievadiet %(help_command)s, lai atvērtu "
"pieejamo komandu sarakstu."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
msgid "No UPnP devices found"
msgstr "Neviena UPnP ierīce netika atrasta"

#: pynicotine/portmapper.py:633
#, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"%(protocol)s: Neizdevās pārsūtīt ārējo portu %(external_port)s: %(error)s"

#: pynicotine/portmapper.py:647
#, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"%(protocol)s: Ārējais ports %(external_port)s veiksmīgi pārsūtīts uz lokālo "
"IP adresi %(ip_address)s ar portu %(local_port)s"

#: pynicotine/privatechat.py:220
#, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Privāta ziņa no lietotāja '%(user)s': %(message)s"

#: pynicotine/search.py:368
#, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Meklē vēlmju saraksta vienumu \"%s\""

#: pynicotine/search.py:434
#, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "Vēlmju saraksta gaidīšanas periods ir iestatīts uz %s sekundēm"

#: pynicotine/search.py:760
#, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr "Lietotājs %(user)s meklē \"%(query)s\", atrada %(num)i rezultātus"

#: pynicotine/shares.py:302
msgid "Rebuilding shares…"
msgstr "Atjauno koplietotojumus…"

#: pynicotine/shares.py:302
msgid "Rescanning shares…"
msgstr "Pārskenē koplietojamos failus…"

#: pynicotine/shares.py:324
#, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Pārskenēšana pabeigta: atrastas %(num)s mapes"

#: pynicotine/shares.py:334
#, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Radusies nopietna kļūda, pārskenējot koplietojumus. Ja šī problēma "
"saglabājas, izdzēsiet %(dir)s/*.dbn un mēģiniet vēlreiz. Ja tas nepalīdz, "
"lūdzu, iesniedziet ziņojumu par kļūdu, iekļaujot šo steka trasējumu: "
"%(trace)s"

#: pynicotine/shares.py:582
#, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Kļūda skenējot failu %(path)s: %(error)s"

#: pynicotine/shares.py:590
#, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Kļūda skenējot mapi %(path)s: %(error)s"

#: pynicotine/shares.py:627
#, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr "Kļūda, skenējot metadatus failam %(path)s: %(error)s"

#: pynicotine/shares.py:1046
#, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Pārskenēšana pārtraukta, jo koplietojumi nav pieejami: %s"

#: pynicotine/shares.py:1184
#, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "Lietotājs %(user)s pārlūko jūsu kopīgoto failu sarakstu"

#: pynicotine/slskmessages.py:3120
#, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Nevar nolasīt koplietošanas datu bāzi. Lūdzu, pārskenējiet savus "
"koplietojumus. Kļūda: %s"

#: pynicotine/slskproto.py:500
#, python-format
msgid "Specified network interface '%s' is not available"
msgstr "Norādītā tīkla saskarne '%s' nav pieejama"

#: pynicotine/slskproto.py:511
#, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Nevar klausīties portā %(port)s. Pārliecinieties, ka to neizmanto neviena "
"cita lietotne, vai izvēlieties citu portu. Kļūda: %(error)s"

#: pynicotine/slskproto.py:523
#, python-format
msgid "Listening on port: %i"
msgstr "Notiek klausīšanās portā: %i"

#: pynicotine/slskproto.py:805
#, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "Nevar izveidot savienojumu ar serveri %(host)s:%(port)s: %(error)s"

#: pynicotine/slskproto.py:1095
#, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Atkārtota savienojuma izveide ar serveri pēc %s sekundēm"

#: pynicotine/slskproto.py:1170
#, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "Savienojas ar %(host)s:%(port)s"

#: pynicotine/slskproto.py:1208
#, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr ""
"Izveidots savienojums ar serveri %(host)s:%(port)s, notiek pieslēgšanās…"

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Atvienots no servera %(host)s:%(port)s"

#: pynicotine/slskproto.py:1499
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Kāds citur ir pieslēdzies jūsu Soulseek kontā"

#: pynicotine/uploads.py:382
#, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Augšupielāde pabeigta: lietotājs %(user)s, IP adrese %(ip)s, fails %(file)s"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Augšupielāde pārtraukta, lietotāja %(user)s fails %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, python-format
msgid "Upload I/O error: %s"
msgstr "Augšupielādes I/O kļūda: %s"

#: pynicotine/uploads.py:1103
#, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Sākta augšupielāde: lietotājs %(user)s, IP adrese %(ip)s, fails %(file)s"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr "Nevar izveidot direktoriju “%(folder)s”, ziņotā kļūda: %(error)s"

#: pynicotine/userbrowse.py:236
#, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Neizdevās ielādēt koplietošanas failus no diska: %(error)s"

#: pynicotine/userbrowse.py:282
#, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr "Saglabāts lietotāja '%(user)s' koplietoto failu saraksts: %(dir)s"

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr "Nevar saglabāt koplietojumus, “%(user)s”, ziņotā kļūda: %(error)s"

#: pynicotine/userinfo.py:160
#, python-format
msgid "Picture saved to %s"
msgstr "Attēls saglabāts uz %s"

#: pynicotine/userinfo.py:163
#, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Nevar saglabāt attēlu uz %(filename)s: %(error)s"

#: pynicotine/userinfo.py:190
#, python-format
msgid "User %(user)s is viewing your profile"
msgstr "Lietotājs %(user)s apskata jūsu profilu"

#: pynicotine/users.py:272
#, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Nevar izveidot savienojumu ar serveri. Iemesls: %s"

#: pynicotine/users.py:272
msgid "Cannot Connect"
msgstr "Nevar izveidot savienojumu"

#: pynicotine/users.py:306
#, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr "Nevar izgūt lietotāja %s IP, jo šis lietotājs ir bezsaistē"

#: pynicotine/users.py:315
#, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr "Lietotāja %(user)s IP adrese: %(ip)s, ports %(port)i%(country)s"

#: pynicotine/users.py:433
msgid "Soulseek Announcement"
msgstr "Soulseek paziņojums"

#: pynicotine/users.py:449
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"Jums nav Soulseek privilēģiju. Kamēr privilēģijas ir aktīvas, jūsu "
"lejupielādes tiks rindotas pirms nepriviliģēto lietotāju lejupielādēm."

#: pynicotine/users.py:455
#, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"Atlikušas %(days)i dienas, %(hours)i stundas, %(minutes)i minūtes, "
"%(seconds)i sekundes Soulseek privilēģijām"

#: pynicotine/users.py:473
msgid "Your password has been changed"
msgstr "Jūsu parole ir nomainīta"

#: pynicotine/users.py:473
msgid "Password Changed"
msgstr "Parole nomainīta"

#: pynicotine/utils.py:574
#, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Nevar atvērt faila ceļu %(path)s: %(error)s"

#: pynicotine/utils.py:621
#, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "Nevar atvērt URL %(url)s: %(error)s"

#: pynicotine/utils.py:646
#, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr "Radās problēma, lasot failu %(filename)s: %(error)s"

#: pynicotine/utils.py:651
#, python-format
msgid "Attempting to load backup of file %s"
msgstr "Mēģinājums ielādēt dublējumu failam %s"

#: pynicotine/utils.py:673
#, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Nevar dublēt failu %(path)s: %(error)s"

#: pynicotine/utils.py:694
#, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Nevar saglabāt failu %(path)s: %(error)s"

#: pynicotine/utils.py:705
#, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Nevar atjaunot iepriekšējo failu %(path)s: %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
msgid "Add buddy…"
msgstr "Pievienot draugu…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
msgid "Toggle Text-to-Speech"
msgstr "Pārslēgt teksta-runu"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
msgid "Chat Room Command Help"
msgstr "Tērzēšanas istabas komandu palīdzība"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
msgid "_Log"
msgstr "_Žurnāls"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
msgid "Room Wall"
msgstr "Istabas siena"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
msgid "R_oom Wall"
msgstr "_Istabas siena"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
msgid "Created by"
msgstr "Izstrādātāji"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
msgid "Translated by"
msgstr "Tulkotāji"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
msgid "License"
msgstr "License"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
msgid "Welcome to Nicotine+"
msgstr "Laipni lūgti Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Ja vēlamais lietotājvārds jau ir aizņemts, jums tiks piedāvāts to mainīt."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""
"Lai savienotos ar citiem Soulseek lietotājiem, klausīšanās ports jūsu "
"maršrutētājā ir jānovirza uz jūsu datoru."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Ja klausīšanās ports ir aizvērts, varēsiet izveidot savienojumu tikai ar "
"tiem lietotājiem, kuru klausīšanās porti ir atvērti."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"Ja nepieciešams, tālāk izvēlieties citu klausīšanās portu. To var izdarīt "
"arī vēlāk preferencēs."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
msgid "Download Files to Folder"
msgstr "Failu lejupielāde mapē"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
msgid "Share Folders"
msgstr "Koplietojamās mapes"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Soulseek lietotāji varēs lejupielādēt no jūsu koplietojumiem. Veiciniet "
"Soulseek tīkla attīstību, daloties ar saviem failiem, kā arī ar to, ko esat "
"lejupielādējuši no citiem lietotājiem."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
msgid "You are ready to use Nicotine+!"
msgstr "Jūs esat gatavs lietot Nicotine+!"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr "Soulseek ir nešifrēts protokols, kas nav paredzēts drošai saziņai."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Ziedojot Soulseek jums tiek piešķirtas privilēģijas uz noteiktu laika "
"periodu. Ja jums ir privilēģijas, jūsu lejupielādes būs rindā pirms "
"lietotājiem, kuriem nav privilēģiju."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
msgid "Previous File"
msgstr "Iepriekšējais fails"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
msgid "Next File"
msgstr "Nākamais fails"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
msgid "Name"
msgstr "Nosaukums"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
msgid "Last Speed"
msgstr "Pēdējais ātrums"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
msgid "_Export…"
msgstr "_Eksportēt…"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
msgid "Keyboard Shortcuts"
msgstr "Tastatūras īsinājumtaustiņi"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
msgid "General"
msgstr "Vispārīgi"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr "Savienot"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr "Atvienot"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
msgid "Rescan Shares"
msgstr "Pārskenēt koplietojumus"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
msgid "Show Log Pane"
msgstr "Rādīt žurnāla paneli"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
msgid "Confirm Quit"
msgstr "Apstiprināt iziešanu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
msgid "Quit"
msgstr "Iziet"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
msgid "Menus"
msgstr "Izvēlnes"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
msgid "Open Main Menu"
msgstr "Atvērt galveno izvēlni"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
msgid "Open Context Menu"
msgstr "Atvērt kontekstizvēlni"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
msgid "Tabs"
msgstr "Cilnes"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
msgid "Change Main Tab"
msgstr "Mainīt galveno cilni"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
msgid "Go to Previous Secondary Tab"
msgstr "Doties uz iepriekšējo sekundāro cilni"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
msgid "Go to Next Secondary Tab"
msgstr "Doties uz nākamo sekundāro cilni"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
msgid "Reopen Closed Secondary Tab"
msgstr "Atkārtoti atvērt aizvērto sekundāro cilni"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
msgid "Close Secondary Tab"
msgstr "Aizvērt sekundāro cilni"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
msgid "Lists"
msgstr "Saraksti"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
msgid "Copy Selected Cell"
msgstr "Kopēt atlasīto šūnu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
msgid "Select All"
msgstr "Atlasīt visu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
msgid "Find"
msgstr "Atrast"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
msgid "Remove Selected Row"
msgstr "Noņemt atlasīto rindu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
msgid "Editing"
msgstr "Rediģēšana"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
msgid "Cut"
msgstr "Izgriezt"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
msgid "Paste"
msgstr "Ielīmēt"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
msgid "Insert Emoji"
msgstr "Ievietot emocijzīmes"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
msgid "File Transfers"
msgstr "Failu pārsūtījumi"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
msgid "Resume / Retry Transfer"
msgstr "Atsākt / Atkārtot pārsūtīšanu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
msgid "Pause / Abort Transfer"
msgstr "Pauzēt / Pārtraukt pārsūtīšanu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
msgid "Download / Upload To"
msgstr "Lejupielādēt / Augšupielādēt uz"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
msgid "Save List to Disk"
msgstr "Saglabāt sarakstu diskā"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
msgid "Refresh"
msgstr "Atsvaidzināt"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
msgid "Expand / Collapse All"
msgstr "Izvērst / Sakļaut visu"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
msgid "Back to Parent Folder"
msgstr "Atpakaļ uz vecākmapi"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
msgid "File Search"
msgstr "Failu meklēšana"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
msgid "Result Filters"
msgstr "Rezultātu filtri"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
msgid "Current Session"
msgstr "Pašreizējā sesija"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
msgid "Completed Downloads"
msgstr "Pabeigtās lejupielādes"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
msgid "Downloaded Size"
msgstr "Lejupielādētais apjoms"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
msgid "Completed Uploads"
msgstr "Pabeigtās augšupielādes"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
msgid "Uploaded Size"
msgstr "Augšupielādētais apjoms"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
msgid "Total"
msgstr "Kopā"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
msgid "_Reset…"
msgstr "_Atiestatīt…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Vēlmju saraksta vienumi tiek automātiski meklēti regulāros intervālos, lai "
"atrastu reti sastopamus failus."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
msgid "Add Wish…"
msgstr "Pievienot vēlmi…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
msgid "Clear All…"
msgstr "Notīrīt visu…"

#: pynicotine/gtkgui/ui/downloads.ui:138
msgid "Clear All Finished/Filtered Downloads"
msgstr "Notīrīt visas pabeigtās/filtrētās lejupielādes"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
msgid "Clear Finished"
msgstr "Notīrīt pabeigtos"

#: pynicotine/gtkgui/ui/downloads.ui:169
msgid "Clear Specific Downloads"
msgstr "Notīrīt konkrētas lejupielādes"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
msgid "Clear _All…"
msgstr "Notīrīt _visu…"

#: pynicotine/gtkgui/ui/interests.ui:21
msgid "Personal Interests"
msgstr "Personīgās intereses"

#: pynicotine/gtkgui/ui/interests.ui:40
msgid "Add something you like…"
msgstr "Pievienojiet kaut ko, kas jums patīk…"

#: pynicotine/gtkgui/ui/interests.ui:62
msgid "Personal Dislikes"
msgstr "Personīgi nepatīk"

#: pynicotine/gtkgui/ui/interests.ui:80
msgid "Add something you dislike…"
msgstr "Pievienojiet kaut ko, kas jums nepatīk…"

#: pynicotine/gtkgui/ui/interests.ui:143
msgid "Refresh Recommendations"
msgstr "Atsvaidzināt ieteikumus"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
msgid "Main Menu"
msgstr "Galvenā izvēlne"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
msgid "Room…"
msgstr "Istaba…"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
msgid "Username…"
msgstr "Lietotājvārds…"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
msgid "Search term…"
msgstr "Meklēšanas vienums…"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Notīrīt"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Meklēšanas modeļi: ar vārdu = vienums, bez vārda = -vienums, daļējs vārds = "
"*nums"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
msgid "Search Scope"
msgstr "Meklēšanas tvērums"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
msgid "_Wishlist"
msgstr "_Vēlmju saraksts"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
msgid "Configure Searches"
msgstr "Konfigurēt meklēšanas"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Ievadiet meklēšanas terminu, lai meklētu failus, kurus koplieto citi "
"lietotāji Soulseek tīklā"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
msgid "File Grouping Mode"
msgstr "Failu grupēšanas režīms"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
msgid "Configure Downloads"
msgstr "Konfigurēt lejupielādes"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Faili, kurus lejupielādējat no citiem lietotājiem, tiek ierindoti šeit, un "
"tos var pauzēt un atsākt pēc pieprasījuma"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
msgid "Configure Uploads"
msgstr "Konfigurēt augšupielādes"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Lietotāju mēģinājumi lejupielādēt jūsu koplietotos failus tiek ierindoti un "
"pārvaldīti šeit"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
msgid "_Open List"
msgstr "_Atvērt sarakstu"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Atver lokālo koplietoto failu sarakstu, kas iepriekš tika saglabāti diskā"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
msgid "Configure Shares"
msgstr "Konfigurēt koplietojumus"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Ievadiet tā lietotāja vārdu, kura koplietotos failus vēlaties pārlūkot. "
"Varat arī saglabāt sarakstu diskā un pārskatīt to vēlāk."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
msgid "_Personal Profile"
msgstr "_Personīgais profils"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
msgid "Configure Account"
msgstr "Konfigurēt kontu"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Ievadiet lietotāja vārdu, lai skatītu lietotāja aprakstu, informāciju un "
"personīgo attēlu"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
msgid "Chat _History"
msgstr "Tērzēšanas _vēsture"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
msgid "Configure Chats"
msgstr "Konfigurēt tērzēšanas"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr "Ievadiet lietotāja vārdu, lai sāktu ar viņu privātu saraksti"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
msgid "_Message All"
msgstr "Sūtīt ziņu visie_m"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
msgid "Configure Ignored Users"
msgstr "Konfigurēt ignorētos lietotājus"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Pievienojiet lietotājus kā draugus, lai koplietotu ar viņiem noteiktas mapes "
"un saņemtu paziņojumus, kad viņi ir tiešsaistē"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
msgid "Join or create room…"
msgstr "Pievienoties vai izveidot istabu…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Pievienojieties esošai tērzēšanas istabai vai izveidojiet jaunu istabu, lai "
"tērzētu ar citiem lietotājiem Soulseek tīklā"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
msgid "Configure User Profile"
msgstr "Konfigurēt lietotāja profilu"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
msgid "Connections"
msgstr "Savienojumi"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
msgid "Downloading (Speed / Active Users)"
msgstr "Lejupielāde (ātrums / aktīvie lietotāji)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
msgid "Uploading (Speed / Active Users)"
msgstr "Augšupielāde (ātrums / aktīvie lietotāji)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
msgid "Search chat history…"
msgstr "Meklēšana tērzēšanas vēsturē…"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
msgid "Download Speed Limits"
msgstr "Lejupielādes ātruma ierobežojumi"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
msgid "Unlimited download speed"
msgstr "Neierobežots lejupielādes ātrums"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
msgid "Use download speed limit (KiB/s):"
msgstr "Izmantot lejupielādes ātruma ierobežojumu (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Izmantot alternatīvu lejupielādes ātruma ierobežojumu (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
msgid "Search rooms…"
msgstr "Meklēšana istabās…"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
msgid "Refresh Rooms"
msgstr "Atsvaidzināt istabas"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
msgid "_Show feed of public chat room messages"
msgstr "_Rādīt publiskās tērzēšanas istabas ziņu plūsmu"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
msgid "_Accept private room invitations"
msgstr "_Pieņemt privāto istabu ielūgumus"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Uzrakstiet vienu ziņojumu, ko pārējie istabas lietotāji varēs izlasīt vēlāk. "
"Jaunākās ziņas tiek rādītas augšdaļā."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
msgid "Set wall message…"
msgstr "Uzstādīt sienas ziņu…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
msgid "Search Result Filters"
msgstr "Meklēšanas rezultātu filtri"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Meklēšanas rezultātu filtri tiek izmantoti, lai precizētu, kuri meklēšanas "
"rezultāti tiek rādīti."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Katram meklēšanas rezultātu sarakstam ir savs filtrs, ko var atklāt, "
"pārslēdzot pogu 'Rezultātu filtri'. Filtrs sastāv no vairākiem laukiem, kuri "
"visi tiek pielietoti, nospiežot Enter jebkurā no laukiem. Filtrēšana tiek "
"nekavējoties pielietota jau saņemtajiem rezultātiem, kā arī tiem, kas vēl "
"tiks saņemti."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Kā norāda nosaukums, meklēšanas rezultātu filtrs nevar paplašināt jūsu "
"sākotnējo meklēšanu, tas var tikai to sašaurināt. Lai paplašinātu vai "
"mainītu meklēšanas nosacījumus, veiciet jaunu meklēšanu."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
msgid "Result Filter Usage"
msgstr "Rezultātu filtra lietojums"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
msgid "Include Text"
msgstr "Iekļaut tekstu"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
msgid "Files, folders and usernames containing this text will be shown."
msgstr "Tiks parādīti faili, mapes un lietotājvārdi, kas satur šo tekstu."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"Lielo/mazo burtu lietojums ir nebūtisks, taču vārdu secība ir svarīga: "
"'Instrumental Remix” nerādīs nevienu 'Remix Instrumental'"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Izmantojiet | (vertikāla līnija), lai atdalītu vairākas precīzas frāzes. "
"Piemērs:\n"
"    Remix|Dub Mix|Instrumental"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
msgid "Exclude Text"
msgstr "Neiekļaut tekstu"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"Tāpat kā iepriekš, bet faili, mapes un lietotājvārdi tiek filtrēti, ja "
"teksts sakrīt."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
msgid "Filters files based upon their file extension."
msgstr "Filtrē failus, pamatojoties uz to paplašinājumu."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Var norādīt vairākus failu paplašinājumus, kas atklās vairāk rezultātu "
"sarakstā. Piemērs:\n"
"    flac wav ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"Ir iespējams arī invertēt filtru, norādot failu paplašinājumus, kurus "
"nevēlaties redzēt rezultātos, ar izsaukuma zīmi! Piemērs:\n"
"    !mp3 !jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
msgid "File Size"
msgstr "Faila izmērs"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
msgid "Filters files based upon their file size."
msgstr "Filtrē failus, pamatojoties uz to izmēru."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"Pēc noklusējuma izmantotā vienība ir baiti (B), un tiks saskaņoti faili, kas "
"ir lielāki vai vienādi ar (>=) šo vērtību."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Pievienojiet b, k, m vai g (alternatīvi kib, mib vai gib), lai norādītu "
"baita, kibibaita, mebibaita vai gibibaita vienības:\n"
"    20m, lai parādītu failus, kas lielāki par 20 MiB (mebibaitiem)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Pievienojiet sākumā = vērtībai, lai norādītu precīzu atbilstību:\n"
"    =1024 atbilst failiem, kas ir tieši 1 KiB (kibibaits)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Pievienojiet sākumā ! vērtībai, lai neiekļautu konkrēta izmēra failus:\n"
"    !30.5m, lai paslēptu failus, kuru izmērs ir 30.5 MiB (megabaiti)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Pievienojiet sākumā < vai >, lai atrastu failus, kas ir mazāki/lielāki par "
"norādīto vērtību. Lai iekļautu diapazonu, starp katru nosacījumu izmantojiet "
"atstarpi:\n"
"    >10.5m <1g, lai parādītu failus, kas lielāki par 10.5 MiB, bet mazāki "
"par 1 GiB."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Labāk zināmos variantus kb, mb un gb var izmantot arī kilobaita, megabaita "
"un gigabaita vienībām."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
msgid "Filters files based upon their bitrate."
msgstr "Filtrē failus, pamatojoties uz to bitu pārraides ātrumu."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Vērtības jāievada tikai kā ciparu zīmes. Vienība vienmēr ir Kb/s (kilobiti "
"sekundē)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Līdzīgi kā iepriekš Failu Lielumiem var izmantot operatorus =, !, <, >, <= "
"vai >=, un var norādīt vairākus nosacījumus, piemēram, lai parādītu failus, "
"kuru bitu pārraides ātrums ir vismaz 256 Kb/s, bet maksimālais bitrate ir "
"1411 Kb/s:\n"
"    256 <=1411"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
msgid "Filters files based upon their duration."
msgstr "Filtrē failus, pamatojoties uz to ilgumu."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"Pēc noklusējuma tiks atlasīti faili, kas ir garāki vai vienādi (>=) ar "
"ievadīto ilgumu, ja vien nav izmantots kāds operators (=, !, <=, < vai >)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Ievadiet neapstrādātu vērtību sekundēs vai izmantojiet laika formātus MM:SS "
"un HH:MM:SS:\n"
"    =53 parāda failus, kuru ilgums ir aptuveni 53 sekundes.\n"
"    >5:30, lai parādītu failus, kas ir garāki par piecarpus minūtēm.\n"
"    <5:30:00 parāda failus, kas ir īsāki par piecarpus stundām."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Var norādīt vairākus nosacījumus:\n"
"    >6:00 <12:00, lai parādītu failus, kuru garums ir no 6 līdz 12 minūtēm.\n"
"    !9:54 !8:43 !7:32, lai no rezultātiem paslēptu dažus konkrētus failus.\n"
"    =5:34 =4:23 =3:05, lai iekļautu failus ar konkrētu ilgumu."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Filtrē failus, pamatojoties uz lietotāju ģeogrāfisko atrašanās vietu saskaņā "
"ar ISO 3166-2 definētajiem valstu kodiem:\n"
"    ASV tiks rādīti tikai to lietotāju rezultāti, kuru IP adreses ir "
"Amerikas Savienotajās Valstīs.\n"
"    !GB slēps rezultātus, kas nāk no lietotājiem Lielbritānijā."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
msgid "Multiple countries can be specified with commas or spaces."
msgstr "Vairākas valstis var norādīt ar komatiem vai atstarpēm."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
msgid "Free Slot"
msgstr "Brīvs slots"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"Rādīt tikai to lietotāju rezultātus, kuriem ir vismaz viens brīvs "
"augšupielādes slots, t. i., failus, kas ir pieejami nekavējoties."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
msgid "Upload Speed Limits"
msgstr "Augšupielādes ātruma ierobežojumi"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
msgid "Unlimited upload speed"
msgstr "Neierobežots augšupielādes ātrums"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
msgid "Use upload speed limit (KiB/s):"
msgstr "Izmantot augšupielādes ātruma ierobežojumu (KiB/s):"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Izmantot alternatīvu augšupielādes ātruma ierobežojumu (KiB/s):"

#: pynicotine/gtkgui/ui/privatechat.ui:63
msgid "Private Chat Command Help"
msgstr "Privātās tērzēšanas komandu palīdzība"

#: pynicotine/gtkgui/ui/search.ui:7
msgid "Include text…"
msgstr "Iekļaut tekstu…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Iekļaut rezultātus, kuru failu ceļos ir norādīts specifisks teksts. Var "
"norādīt vairākas frāzes un vārdus, piem., noteikta frāze|mūzika|termins|"
"noteikta frāze divi"

#: pynicotine/gtkgui/ui/search.ui:18
msgid "Exclude text…"
msgstr "Neiekļaut tekstu…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Neiekļaut rezultātus, kuru failu ceļos ir norādīts specifisks teksts. Var "
"norādīt vairākas frāzes un vārdus, piem., noteikta frāze|mūzika|termins|"
"noteikta frāze divi"

#: pynicotine/gtkgui/ui/search.ui:29
msgid "File type…"
msgstr "Faila tips…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Faila tips, piem., flac wav vai !mp3 !m4a"

#: pynicotine/gtkgui/ui/search.ui:40
msgid "File size…"
msgstr "Faila izmērs…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
msgid "File size, e.g. >10.5m <1g"
msgstr "Faila lielums, piem. >10.5m <1g"

#: pynicotine/gtkgui/ui/search.ui:51
msgid "Bitrate…"
msgstr "Bitu pārraides ātrums…"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
msgid "Bitrate, e.g. 256 <1412"
msgstr "Bitu pārraides ātrums, piem. 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
msgid "Duration…"
msgstr "Ilgums…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Ilgums, piem., >6:00 <12:00 !6:54"

#: pynicotine/gtkgui/ui/search.ui:73
msgid "Country code…"
msgstr "Valsts kods…"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Valsts kods, piem., US ES vai !DE !GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Aizliegt lietotājiem piekļūt jūsu koplietotajiem failiem, pamatojoties uz "
"lietotājvārdu, IP adresi vai valsti."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Bloķējamo valstu kodi (atdalāmi ar komatu):"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
msgid "Codes must be in ISO 3166-2 format."
msgstr "Kodiem jābūt ISO 3166-2 formātā."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
msgid "Use custom geo block message:"
msgstr "Izmantot pielāgotu ģeogrāfiskās bloķēšanas ziņu:"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Izmantot pielāgotu aizlieguma ziņu:"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
msgid "IP Addresses"
msgstr "IP adreses"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
msgid "Restore previously open private chats on startup"
msgstr ""
"Atjaunot iepriekš atvērtās privātās tērzēšanas sarakstes startēšanas laikā"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
msgid "Enable spell checker"
msgstr "Iespējot pareizrakstības pārbaudi"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
msgid "Enable CTCP-like private message responses (client version)"
msgstr "Iespējot CTCP-līdzīgas privāto ziņu atbildes (klienta versija)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
msgid "Number of recent private chat messages to show:"
msgstr "Rādāmo pēdējo privāto tērzēšanas ziņu skaits:"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
msgid "Number of recent chat room messages to show:"
msgstr "Rādāmo pēdējo tērzēšanas istabu ziņu skaits:"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
msgid "Chat Completion"
msgstr "Tērzēšanas pabeigšana"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
msgid "Enable tab-key completion"
msgstr "Iespējot tabulēšanas taustiņa aizpildīšanu"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
msgid "Enable completion drop-down list"
msgstr "Iespējot aizpildīšanas nolaižamo sarakstu"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
msgid "Minimum characters required to display drop-down:"
msgstr ""
"Minimālais rakstzīmju skaits, kas nepieciešams, lai parādītu nolaižamo "
"izvēlni:"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
msgid "Allowed chat completions:"
msgstr "Atļautās tērzēšanas aizpildīšanas:"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
msgid "Buddy names"
msgstr "Draugu vārdi"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
msgid "Chat room usernames"
msgstr "Tērzēšanas istabu lietotājvārdi"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
msgid "Room names"
msgstr "Istabu nosaukumi"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
msgid "Commands"
msgstr "Komandas"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
msgid "Timestamps"
msgstr "Laika zīmogi"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
msgid "Private chat format:"
msgstr "Privātās tērzēšanas formāts:"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "Noklusējums"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
msgid "Chat room format:"
msgstr "Tērzēšanas istabas formāts:"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
msgid "Text-to-Speech"
msgstr "No teksta uz runu"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
msgid "Enable Text-to-Speech"
msgstr "Iespējot teksta-runu"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
msgid "Text-to-Speech command:"
msgstr "Teksta pārvēršanas runā komanda:"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
msgid "Private chat message:"
msgstr "Privātās tērzēšanas ziņa:"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
msgid "Chat room message:"
msgstr "Tērzēšanas istabas ziņa:"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
msgid "Censor"
msgstr "Cenzēt"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
msgid "Enable censoring of text patterns"
msgstr "Iespējot teksta modeļu cenzēšanu"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
msgid "Auto-Replace"
msgstr "Automātiska aizstāšana"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
msgid "Enable automatic replacement of words"
msgstr "Iespējot automātisku vārdu aizstāšanu"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr ""
"Automātiski notīrīt pabeigtās/filtrētās lejupielādes no pārsūtīšanas saraksta"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
msgid "Store completed downloads in username subfolders"
msgstr "Saglabāt pabeigtās lejupielādes lietotājvārdu apakšmapēs"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
msgid "Double-click action for downloads:"
msgstr "Dubultklikšķa darbība priekš lejupielādēm:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
msgid "Allow users to send you any files:"
msgstr "Atļaut lietotājiem sūtīt jums jebkādus failus:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
msgid "Folders"
msgstr "Mapes"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
msgid "Finished downloads:"
msgstr "Pabeigtas lejupielādes:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
msgid "Incomplete downloads:"
msgstr "Nepilnīgas lejupielādes:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
msgid "Received files:"
msgstr "Saņemtie faili:"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "Notikumi"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
msgid "Run command after file download finishes ($ for file path):"
msgstr "Palaist komandu pēc faila lejupielādes pabeigšanas ($ faila ceļam):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
msgid "Run command after folder download finishes ($ for folder path):"
msgstr "Palaist komandu pēc mapes lejupielādes pabeigšanas ($ mapes ceļam):"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
msgid "Download Filters"
msgstr "Lejupielādes filtri"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
msgid "Enable download filters"
msgstr "Iespējot lejupielādes filtrus"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Pievienot"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Ielādēt noklusējumus"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
msgid "Verify Filters"
msgstr "Pārbaudīt filtrus"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
msgid "Unverified"
msgstr "Nepārbaudīts"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Ignorēt lietotāju tērzēšanas ziņas un meklēšanas rezultātus, pamatojoties uz "
"lietotājvārdu vai IP adresi."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Reģistrēt žurnālā tērzētavas pēc noklusējuma"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Reģistrēt žurnālā privāto tērzēšanas sarunas pēc noklusējuma"

#: pynicotine/gtkgui/ui/settings/log.ui:142
msgid "Log transfers to file"
msgstr "Reģistrēt žurnālā pārsūtījumus uz failu"

#: pynicotine/gtkgui/ui/settings/log.ui:166
msgid "Log debug messages to file"
msgstr "Reģistrēt žurnālā atkļūdošanas ziņojumus uz failu"

#: pynicotine/gtkgui/ui/settings/log.ui:190
msgid "Log timestamp format:"
msgstr "Žurnāla laika zīmoga formāts:"

#: pynicotine/gtkgui/ui/settings/log.ui:228
msgid "Folder Locations"
msgstr "Mapju atrašanās vietas"

#: pynicotine/gtkgui/ui/settings/log.ui:245
msgid "Chatroom logs folder:"
msgstr "Tērzēšanas istabas žurnālu mape:"

#: pynicotine/gtkgui/ui/settings/log.ui:261
msgid "Private chat logs folder:"
msgstr "Privātās tērzēšanas žurnālu mape:"

#: pynicotine/gtkgui/ui/settings/log.ui:277
msgid "Transfer logs folder:"
msgstr "Pārsūtījumu žurnālu mape:"

#: pynicotine/gtkgui/ui/settings/log.ui:293
msgid "Debug logs folder:"
msgstr "Atkļūdošanas žurnālu mape:"

#: pynicotine/gtkgui/ui/settings/network.ui:39
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Pieteicieties esošā Soulseek kontā vai izveidojiet jaunu kontu. "
"Lietotājvārdi ir reģistrjutīgi un unikāli."

#: pynicotine/gtkgui/ui/settings/network.ui:118
msgid "Public IP address:"
msgstr "Publiskā IP adrese:"

#: pynicotine/gtkgui/ui/settings/network.ui:159
msgid "Listening port:"
msgstr "Klausīšanās ports:"

#: pynicotine/gtkgui/ui/settings/network.ui:185
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Automātiski pārsūtīt klausīšanās portu (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
msgid "Away Status"
msgstr "Prombūtnes statuss"

#: pynicotine/gtkgui/ui/settings/network.ui:228
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Neaktivitātes minūtes pirms aiziešanas (0, lai atspējotu):"

#: pynicotine/gtkgui/ui/settings/network.ui:253
msgid "Auto-reply message when away:"
msgstr "Automātiskās atbildes ziņa, kad esat \"aizgājis\":"

#: pynicotine/gtkgui/ui/settings/network.ui:299
msgid "Auto-connect to server on startup"
msgstr "Automātiska savienošanās ar serveri lietotnes startēšanas laikā"

#: pynicotine/gtkgui/ui/settings/network.ui:323
msgid "Soulseek server:"
msgstr "Soulseek serveris:"

#: pynicotine/gtkgui/ui/settings/network.ui:347
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Savienojumus sasaista ar konkrētu tīkla saskarni, kas noder gadījumos, kad "
"jānodrošina, ka VPN tiek izmantots visu laiku. Atstājiet tukšu, lai "
"izmantotu jebkuru pieejamo saskarni. Mainiet šo vērtību tikai tad, ja zināt, "
"ko darāt."

#: pynicotine/gtkgui/ui/settings/network.ui:351
msgid "Network interface:"
msgstr "Tīkla saskarne:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"'Tagad atskaņo' ļauj parādīt, kas tiek atskaņots jūsu multivides "
"atskaņotājā, tērzēšanā izmantojot komandu '/now'."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
msgid "Other"
msgstr "Cits"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
msgid "Now Playing Format"
msgstr "'Tagad atskaņo' formāts"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
msgid "Now Playing message format:"
msgstr "'Tagad atskaņo' ziņas formāts:"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
msgid "Test Configuration"
msgstr "Testēt konfigurāciju"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
msgid "Enable plugins"
msgstr "Iespējot spraudņus"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
msgid "Add Plugins"
msgstr "Pievienot spraudņus"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
msgid "_Add Plugins"
msgstr "_Pievienot spraudņus"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
msgid "Settings"
msgstr "Iestatījumi"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
msgid "_Settings"
msgstr "_Iestatījumi"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
msgid "Version:"
msgstr "Versija:"

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
msgid "Created by:"
msgstr "Izveidoja:"

#: pynicotine/gtkgui/ui/settings/search.ui:51
msgid "Enable search history"
msgstr "Iespējot meklēšanas vēsturi"

#: pynicotine/gtkgui/ui/settings/search.ui:70
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Privāti kopīgotiem failiem, kas ir padarīti redzami visiem, tiks pievienots "
"prefikss '[PRIVATE]', un tos nevarēs lejupielādēt, kamēr augšupielādētājs "
"nebūs devis skaidru atļauju. Laipni palūdziet to!"

#: pynicotine/gtkgui/ui/settings/search.ui:76
msgid "Show privately shared files in search results"
msgstr "Rādīt privāti koplietotos failus meklēšanas rezultātos"

#: pynicotine/gtkgui/ui/settings/search.ui:106
msgid "Limit number of results per search:"
msgstr "Ierobežot rezultātu skaitu vienai meklēšanai:"

#: pynicotine/gtkgui/ui/settings/search.ui:149
msgid "Result Filter Help"
msgstr "Rezultātu filtrēšanas palīdzība"

#: pynicotine/gtkgui/ui/settings/search.ui:177
msgid "Enable search result filters by default"
msgstr "Iespējot meklēšanas rezultātu filtrus pēc noklusējuma"

#: pynicotine/gtkgui/ui/settings/search.ui:211
msgid "Include:"
msgstr "Iekļaut:"

#: pynicotine/gtkgui/ui/settings/search.ui:236
msgid "Exclude:"
msgstr "Neiekļaut:"

#: pynicotine/gtkgui/ui/settings/search.ui:261
msgid "File Type:"
msgstr "Faila tips:"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "Izmērs:"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Bitu pārraides ātrums:"

#: pynicotine/gtkgui/ui/settings/search.ui:342
msgid "Duration:"
msgstr "Ilgums:"

#: pynicotine/gtkgui/ui/settings/search.ui:369
msgid "Country Code:"
msgstr "Valsts kods:"

#: pynicotine/gtkgui/ui/settings/search.ui:407
msgid "Only show results from users with an available upload slot."
msgstr ""
"Rādīt tikai to lietotāju rezultātus, kuriem ir pieejams augšupielādes slots."

#: pynicotine/gtkgui/ui/settings/search.ui:430
msgid "Network Searches"
msgstr "Tīkla meklēšana"

#: pynicotine/gtkgui/ui/settings/search.ui:452
msgid "Respond to search requests from other users"
msgstr "Atbildēt uz citu lietotāju meklēšanas pieprasījumiem"

#: pynicotine/gtkgui/ui/settings/search.ui:486
msgid "Searches shorter than this number of characters will be ignored:"
msgstr "Meklējumi, kas ir īsāki par šo rakstzīmju skaitu, tiks ignorēti:"

#: pynicotine/gtkgui/ui/settings/search.ui:511
msgid "Maximum search results to send per search request:"
msgstr ""
"Maksimālais meklēšanas rezultātu skaits, ko nosūtīt katram meklēšanas "
"pieprasījumam:"

#: pynicotine/gtkgui/ui/settings/search.ui:578
msgid "Clear Search History"
msgstr "Notīrīt meklēšanas vēsturi"

#: pynicotine/gtkgui/ui/settings/search.ui:626
msgid "Clear Filter History"
msgstr "Notīrīt filtru vēsturi"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Automātiska kopīgoto mapju satura pārskenēšana palaišanas laikā. Ja šī "
"opcija ir atspējota, koplietojamās mapes tiek atjauninātas tikai tad, kad "
"manuāli iniciējat pārskenēšanu."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Pārskenēt koplietojumus startēšanas laikā"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr "Redzams visiem:"

#: pynicotine/gtkgui/ui/settings/shares.ui:82
msgid "Buddy shares"
msgstr "Draugu koplietojumi"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
msgid "Trusted shares"
msgstr "Uzticamo Draugu koplietojumi"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr ""
"Automātiski notīrīt pabeigtās/atceltās augšupielādes no pārsūtīšanas saraksta"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
msgid "Double-click action for uploads:"
msgstr "Dubultklikšķa darbība augšupielādei:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
msgid "Limit upload speed:"
msgstr "Ierobežot augšupielādes ātrumu:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
msgid "Per transfer"
msgstr "Uz pārsūtījumu"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
msgid "Total transfers"
msgstr "Uz visiem pārsūtījumiem"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
msgid "Upload Slots"
msgstr "Augšupielādes sloti"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Round Robin: Faili cikliski tiks augšupielādēti rindā gaidošajiem "
"lietotājiem.\n"
"Pirmais iekšā, pirmais ārā: Faili tiks augšupielādēti tādā secībā, kādā tie "
"tika ievietoti rindā."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
msgid "Upload queue type:"
msgstr "Augšupielādes rindas tips:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr "Piešķirt augšupielādes slotus, līdz kopējais ātrums sasniedz (KiB/s):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
msgid "Fixed number of upload slots:"
msgstr "Fiksēts augšupielādes slotu skaits:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
msgid "Prioritize all buddies"
msgstr "Prioritizēt visus draugus"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
msgid "Queue Limits"
msgstr "Rindu ierobežojumi"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
msgid "Maximum number of queued files per user:"
msgstr "Maksimālais rindā gaidāmo failu skaits vienam lietotājam:"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr ""
"Maksimālais rindā gaidāmo failu kopējais izmērs vienam lietotājam (MiB):"

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
msgid "Limits do not apply to buddies"
msgstr "Ierobežojumi neattiecas uz draugiem"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"$ gadījumi tiek aizstāti ar URL. Noklusējuma sistēmas lietotnes tiek "
"izmantotas gadījumos, kad protokols nav konfigurēts."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
msgid "File manager command:"
msgstr "Failu pārvaldnieka komanda:"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
msgid "Reset Picture"
msgstr "Atiestatīt attēlu"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
msgid "Self Description"
msgstr "Pašapraksts"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""
"Pievienojiet informāciju, ko vēlaties, lai ikviens redzētu, piemēram, īsu "
"aprakstu, noderīgus padomus vai norādījumus par jūsu koplietojumu "
"lejupielādi."

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
msgid "Picture:"
msgstr "Attēls:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
msgid "Prefer dark mode"
msgstr "Priekšroka tumšajam režīmam"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
msgid "Use header bar"
msgstr "Izmantot galvenes joslu"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
msgid "Display tray icon"
msgstr "Rādīt ikonjoslas ikonu"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
msgid "Minimize to tray on startup"
msgstr "Startēšanas laikā samazināt ikonjoslā"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
msgid "Language (requires a restart):"
msgstr "Valoda (nepieciešams restartēt):"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
msgid "When closing window:"
msgstr "Aizverot logu:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
msgid "Notifications"
msgstr "Paziņojumi"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
msgid "Enable sound for notifications"
msgstr "Iespējot skaņu paziņojumiem"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
msgid "Show notification for private chats and mentions in the window title"
msgstr "Privāto tērzēšanu un pieminējumu paziņojumu rādīšana loga nosaukumā"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
msgid "Show notifications for:"
msgstr "Rādīt paziņojumus par:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
msgid "Finished file downloads"
msgstr "Pabeigta faila lejupielāde"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
msgid "Finished folder downloads"
msgstr "Pabeigta mapes lejupielāde"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
msgid "Private messages"
msgstr "Privātās ziņas"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
msgid "Chat room messages"
msgstr "Tērzēšanas istabas ziņas"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
msgid "Chat room mentions"
msgstr "Tērzēšanas istabas pieminējumi"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
msgid "Wishlist results found"
msgstr "Atrastie vēlmju saraksta rezultāti"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
msgid "Restore the previously active main tab at startup"
msgstr "Startēšanas laikā atjaunot iepriekš aktīvo galveno cilni"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
msgid "Close-buttons on secondary tabs"
msgstr "Aizvēršanas pogas sekundārajās cilnēs"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
msgid "Regular tab label color:"
msgstr "Parasta cilnes etiķetes krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
msgid "Changed tab label color:"
msgstr "Mainīta cilnes etiķetes krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
msgid "Highlighted tab label color:"
msgstr "Izceltās cilnes etiķetes krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr "Draugu saraksta pozīcija:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
msgid "Visible main tabs:"
msgstr "Redzamās galvenās cilnes:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
msgid "Tab bar positions:"
msgstr "Cilņu joslas pozīcijas:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
msgid "Main tabs"
msgstr "Galvenās cilnes"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
msgid "Show reverse file paths (requires a restart)"
msgstr "Rādīt apgrieztus failu ceļus (nepieciešama restartēšana)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
msgid "Show exact file sizes (requires a restart)"
msgstr "Rādīt precīzus failu izmērus (nepieciešama restartēšana)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
msgid "List text color:"
msgstr "Saraksta teksta krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
msgid "Enable colored usernames"
msgstr "Iespējot krāsainus lietotājvārdus"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
msgid "Chat username appearance:"
msgstr "Tērzēšanas lietotājvārda izskats:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
msgid "Remote text color:"
msgstr "Attālā teksta krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
msgid "Local text color:"
msgstr "Lokālā teksta krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
msgid "Command output text color:"
msgstr "Komandas izvades teksta krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
msgid "/me action text color:"
msgstr "/me darbības teksta krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
msgid "Highlighted text color:"
msgstr "Izceltā teksta krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
msgid "URL link text color:"
msgstr "URL saites teksta krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
msgid "User Statuses"
msgstr "Lietotāju statusi"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
msgid "Online color:"
msgstr "\"Tiešsaistē\" krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
msgid "Away color:"
msgstr "\"Aizgājis\" krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
msgid "Offline color:"
msgstr "\"Bezsaistē\" krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
msgid "Text Entries"
msgstr "Teksta ieraksti"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
msgid "Text entry background color:"
msgstr "Teksta ieraksta fona krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
msgid "Text entry text color:"
msgstr "Teksta ieraksta teksta krāsa:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
msgid "Fonts"
msgstr "Fonti"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
msgid "Global font:"
msgstr "Globālais fonts:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
msgid "List font:"
msgstr "Saraksts fonts:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
msgid "Text view font:"
msgstr "Teksta skata fonts:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
msgid "Chat font:"
msgstr "Tērzēšana fonts:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
msgid "Transfers font:"
msgstr "Pārsūtījumi fonts:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
msgid "Search font:"
msgstr "Meklēšana fonts:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
msgid "Browse font:"
msgstr "Pārlūkot fonts:"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
msgid "Icons"
msgstr "Ikonas"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
msgid "Icon theme folder:"
msgstr "Ikonu motīvu mape:"

#: pynicotine/gtkgui/ui/uploads.ui:86
msgid "Abort User(s)"
msgstr "Pārtraukt lietotāju(s)"

#: pynicotine/gtkgui/ui/uploads.ui:116
msgid "Ban User(s)"
msgstr "Aizliegt lietotāju(us)"

#: pynicotine/gtkgui/ui/uploads.ui:138
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Notīrīt visas pabeigtās/atceltās augšupielādes"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
msgid "Message All"
msgstr "Sūtīt ziņu visiem"

#: pynicotine/gtkgui/ui/uploads.ui:199
msgid "Clear Specific Uploads"
msgstr "Notīrīt konkrētas augšupielādes"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
msgid "Save Shares List to Disk"
msgstr "Saglabāt koplietojumu sarakstu diskā"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
msgid "Refresh Files"
msgstr "Atsvaidzināt failus"

#: pynicotine/gtkgui/ui/userinfo.ui:101
msgid "Edit Profile"
msgstr "Rediģēt profilu"

#: pynicotine/gtkgui/ui/userinfo.ui:149
msgid "Shared Files"
msgstr "Koplietotie faili"

#: pynicotine/gtkgui/ui/userinfo.ui:203
msgid "Upload Speed"
msgstr "Augšupielādes ātrums"

#: pynicotine/gtkgui/ui/userinfo.ui:230
msgid "Free Upload Slots"
msgstr "Brīvi augšupielādes sloti"

#: pynicotine/gtkgui/ui/userinfo.ui:284
msgid "Queued Uploads"
msgstr "Augšupielādes rindā"

#: pynicotine/gtkgui/ui/userinfo.ui:354
msgid "Edit Interests"
msgstr "Rediģēt intereses"

#: pynicotine/gtkgui/ui/userinfo.ui:624
msgid "_Gift Privileges…"
msgstr "_Dāvināt privilēģijas…"

#: pynicotine/gtkgui/ui/userinfo.ui:663
msgid "_Refresh Profile"
msgstr "Atsvaidzināt p_rofilu"

#: pynicotine/plugins/core_commands/PLUGININFO:3
msgid "Nicotine+ Commands"
msgstr "Nicotine+ komandas"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "Nicotine+ Komanda"

#~ msgid "Listening port (requires a restart):"
#~ msgstr "Klausīšanās ports (nepieciešams restartēt):"

#~ msgid "Network interface (requires a restart):"
#~ msgstr "Tīkla saskarne (nepieciešams restartēt):"

#~ msgid "Invalid Password"
#~ msgstr "Nepareiza Parole"

#~ msgid "Change _Login Details"
#~ msgstr "Mainīt _Pierakstīšanās Informāciju"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i priviliģēti lietotāji"

#~ msgid "_Set Up…"
#~ msgstr "_Uzstādīt…"

#~ msgid "Queued search result text color:"
#~ msgstr "Rindā ievietotā meklēšanas rezultāta teksta krāsa:"

#, python-format
#~ msgid "Failed to fetch the shared folder %(folder)s: %(error)s"
#~ msgstr "Neizdevās izgūt koplietoto mapi %(folder)s: %(error)s"

#~ msgid "_Clear"
#~ msgstr "_Notīrīt"

#, python-format
#~ msgid "Can't save %(filename)s: %(error)s"
#~ msgstr "Nevar saglabāt %(filename)s: %(error)s"

#~ msgid "Trusted Buddies"
#~ msgstr "Uzticamie Draugi"

#~ msgid "Quit program"
#~ msgstr "Iziet no programmas"

#~ msgid "Username:"
#~ msgstr "Lietotājvārds:"

#~ msgid "Re_commendations for Item"
#~ msgstr "Ietei_kumi vienumam"

#~ msgid "_Remove Item"
#~ msgstr "_Noņemt Vienumu"

#~ msgid "_Remove"
#~ msgstr "_Noņemt"

#~ msgid "Send M_essage"
#~ msgstr "Sūtīt Z_iņu"

#~ msgid "Send Message"
#~ msgstr "Sūtīt Ziņu"

#~ msgid "View User Profile"
#~ msgstr "Skatīt Lietotāja Profilu"

#~ msgid "Start Messaging"
#~ msgstr "Sākt Ziņapmaiņu"

#~ msgid "Enter the name of the user whom you want to send a message:"
#~ msgstr "Ievadiet lietotāja vārdu, kuram vēlaties nosūtīt ziņu:"

#~ msgid "_Message"
#~ msgstr "_Ziņa"

#~ msgid "Enter the name of the user whose profile you want to see:"
#~ msgstr "Ievadiet lietotāja vārdu, kura profilu vēlaties apskatīt:"

#~ msgid "_View Profile"
#~ msgstr "_Skatīt Profilu"

#~ msgid "Enter the name of the user whose shares you want to see:"
#~ msgstr "Ievadiet lietotāja vārdu, kura koplietojumus vēlaties redzēt:"

#~ msgid "_Browse"
#~ msgstr "_Pārlūkot"

#~ msgid "Password"
#~ msgstr "Parole"

#~ msgid "Download File"
#~ msgstr "Lejupielādēt Failu"

#~ msgid "Refresh Similar Users"
#~ msgstr "Atsvaidzināt Līdzīgus Lietotājus"

#~ msgid "Enter the username of the person whose files you want to see"
#~ msgstr "Ievadiet tās personas lietotājvārdu, kuras failus vēlaties skatīt"

#~ msgid "Enter the username of the person whose information you want to see"
#~ msgstr "Ievadiet personas lietotājvārdu, kuras informāciju vēlaties redzēt"

#~ msgid "Enter the username of the person you want to send a message to"
#~ msgstr "Ievadiet personas lietotājvārdu, kurai vēlaties nosūtīt ziņu"

#~ msgid "Enter the username of the person you want to add to your buddy list"
#~ msgstr ""
#~ "Ievadiet personas lietotājvārdu, kuru vēlaties pievienot savam draugu "
#~ "sarakstam"

#~ msgid ""
#~ "Enter the name of a room you want to join. If the room doesn't exist, it "
#~ "will be created."
#~ msgstr ""
#~ "Ievadiet istabas nosaukumu, kurai vēlaties pievienoties. Ja istaba "
#~ "neeksistē, tā tiks izveidota."

#~ msgid "Show Log History Pane"
#~ msgstr "Rādīt Žurnāla Vēstures Paneli"

#~ msgid "Save _Picture"
#~ msgstr "Saglabāt _attēlu"

#, python-format
#~ msgid ""
#~ "Filtered out incorrect search result %(filepath)s from user %(user)s for "
#~ "search query \"%(query)s\""
#~ msgstr ""
#~ "Izfiltrēts nepareizs meklēšanas rezultāts %(filepath)s no lietotāja "
#~ "%(user)s meklēšanas vaicājumam \"%(query)s\""

#~ msgid ""
#~ "Certain clients don't send search results if special characters are "
#~ "included."
#~ msgstr ""
#~ "Daži Soulseek klienti nesūta meklēšanas rezultātus, ja ir iekļautas "
#~ "speciālās rakstzīmes."

#~ msgid "Remove special characters from search terms"
#~ msgstr "Noņemt speciālās rakstzīmes no meklēšanas vienumiem"

#, python-format
#~ msgid "Trouble executing '%s'"
#~ msgstr "Problēmas, izpildot “%s”"

#, python-format
#~ msgid "Trouble executing on folder: %s"
#~ msgstr "Problēma, izpildot mapē: %s"

#~ msgid "Disallowed extension"
#~ msgstr "Neatļauts paplašinājums"

#~ msgid "Too many files"
#~ msgstr "Pārāk daudz failu"

#~ msgid "Too many megabytes"
#~ msgstr "Pārāk daudz megabaitu"

#~ msgid "Server does not permit performing wishlist searches at this time"
#~ msgstr "Serveris šobrīd neļauj veikt vēlmju saraksta meklēšanu"

#~ msgid ""
#~ "File transfer speeds depend on users you are downloading from. Certain "
#~ "users will be faster, while others will be slow."
#~ msgstr ""
#~ "Failu pārsūtīšanas ātrums ir atkarīgs no lietotājiem, no kuriem "
#~ "lejupielādējat. Daži lietotāji būs ātrāki, kamēr citi lēnāki."

#~ msgid "Started Downloads"
#~ msgstr "Sāktās Lejupielādes"

#~ msgid "Started Uploads"
#~ msgstr "Sāktās Augšupielādes"

#~ msgid "Replace censored letters with:"
#~ msgstr "Aizstāt cenzētos burtus ar:"

#~ msgid "Censored Patterns"
#~ msgstr "Cenzētie Modeļi"

#~ msgid "Replacements"
#~ msgstr "Aizvietotāji"

#~ msgid ""
#~ "If a user on the Soulseek network searches for a file that exists in your "
#~ "shares, search results will be sent to the user."
#~ msgstr ""
#~ "Ja lietotājs Soulseek tīklā meklēs failu, kas ir jūsu koplietojumā, "
#~ "meklēšanas rezultāti tiks nosūtīti lietotājam."

#~ msgid "Send to Player"
#~ msgstr "Sūtīt atskaņotājam"

#~ msgid "Send to _Player"
#~ msgstr "Nosūtīt uz _atskaņotāju"

#~ msgid "_Open in File Manager"
#~ msgstr "_Atvērt Failu Pārvaldniekā"

#~ msgid "Media player command:"
#~ msgstr "Multivides atskaņotāja komanda:"

#, python-format
#~ msgid ""
#~ "Unable to save download to username subfolder, falling back to default "
#~ "download folder. Error: %s"
#~ msgstr ""
#~ "Nevar saglabāt lejupielādi lietotājvārda apakšmapē, atgriežoties "
#~ "noklusējuma lejupielādes mapē. Kļūda: %s"

#~ msgid "Buddy-only"
#~ msgstr "Tikai draugiem"

#~ msgid "Share with buddies only"
#~ msgstr "Kopīgot tikai ar draugiem"

#~ msgid "_Quit…"
#~ msgstr "_Iziet…"

#~ msgid "_Configure Shares"
#~ msgstr "_Konfigurēt Koplietojumus"

#~ msgid "Remote file error"
#~ msgstr "Attālā faila kļūda"

#, python-format
#~ msgid "Cannot find %(option1)s or %(option2)s, please install either one."
#~ msgstr ""
#~ "Nevar atrast %(option1)s vai %(option2)s, lūdzu, instalējiet vismaz vienu."

#, python-format
#~ msgid "%(num)s folders found before rescan"
#~ msgstr "%(num)s mapes atrastas pirms pārskenēšanas"

#, python-format
#~ msgid "Failed to process the following databases: %(names)s"
#~ msgstr "Neizdevās apstrādāt šīs datu bāzes: %(names)s"

#, python-format
#~ msgid "Failed to send number of shared files to the server: %s"
#~ msgstr "Neizdevās uz serveri nosūtīt koplietoto failu skaitu: %s"

#~ msgid "Quit / Run in Background"
#~ msgstr "Iziet / Palaist fonā"

#~ msgid "Limit buddy-only shares to trusted buddies"
#~ msgstr "Ierobežot tikai draugiem kopīgošanu līdz uzticamajiem draugiem"

#~ msgid "Shared"
#~ msgstr "Koplietots"

#~ msgid "Search Files and Folders"
#~ msgstr "Meklēt Failus un Mapes"

#~ msgid "Out of Date"
#~ msgstr "Novecojis"

#, python-format
#~ msgid "Version %(version)s is available, released on %(date)s"
#~ msgstr "Ir pieejama versija %(version)s, izlaista %(date)s"

#, python-format
#~ msgid "You are using a development version of %s"
#~ msgstr "Jūs izmantojat izstrādes versiju %s"

#, python-format
#~ msgid "You are using the latest version of %s"
#~ msgstr "Jūs izmantojat jaunāko versiju %s"

#~ msgid "Latest Version Unknown"
#~ msgstr "Jaunākā Versija Nezināma"

#~ msgid "Prefer Dark _Mode"
#~ msgstr "Dot Priekšroku Tumšajam Režīma_m"

#~ msgid "Show _Log History Pane"
#~ msgstr "Rādīt Žurnā_la Vēstures Paneli"

#~ msgid "Buddy List in Separate Tab"
#~ msgstr "Draugu Saraksts Atsevišķā Cilnē"

#~ msgid "Buddy List Always Visible"
#~ msgstr "Draugu Saraksts Vienmēr Redzams"

#~ msgid "_View"
#~ msgstr "Ska_ts"

#~ msgid "_Open Log Folder"
#~ msgstr "_Atvērt Žurnāla Mapi"

#~ msgid "_Browse Folder(s)"
#~ msgstr "_Pārlūkot Mapi(-es)"

#~ msgid "Kibibytes (2^10 bytes) per second."
#~ msgstr "Kibibaiti (2^10 baiti) sekundē."

#~ msgid "Sent to users as the reason for being geo blocked."
#~ msgstr "Nosūtīt lietotājiem kā ģeogrāfiskās bloķēšanas iemeslu."

#~ msgid "Sent to users as the reason for being banned."
#~ msgstr "Nosūtīt lietotājiem kā aizlieguma iemeslu."

#~ msgid ""
#~ "Once you interact with the application being away, status will be set to "
#~ "online."
#~ msgstr ""
#~ "Kad mijiedarbojaties ar lietojumprogrammu, kamēr esat aizgājis, statuss "
#~ "tiks iestatīts uz tiešsaistē."

#~ msgid "Each user may queue a maximum of either:"
#~ msgstr "Katrs lietotājs rindā var iekļaut ne vairāk kā:"

#~ msgid "Mebibytes (2^20 bytes)."
#~ msgstr "Mebibaiti (2^20 baiti)."

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "files"
#~ msgstr "faili"

#~ msgid "Queue Behavior"
#~ msgstr "Rindas Uzvedība"

#~ msgid ""
#~ "If disabled, slots will automatically be determined by available "
#~ "bandwidth limitations."
#~ msgstr ""
#~ "Ja atspējots, sloti tiks automātiski noteikti pēc pieejamiem joslas "
#~ "platuma ierobežojumiem."

#~ msgid "Note that the operating system's theme may take precedence."
#~ msgstr "Jāņem vērā, ka operētājsistēmas motīvam var būt prioritāte."

#~ msgid "By default the leftmost tab is activated at startup"
#~ msgstr ""
#~ "Pēc noklusējuma galējā kreisā cilne tiek aktivizēta startēšanas laikā"

#, python-format
#~ msgid "Quit %(program)s %(version)s, %(status)s!"
#~ msgstr "Iziet no %(program)s %(version)s, %(status)s!"

#~ msgid "terminated"
#~ msgstr "pārtraukta"

#~ msgid "done"
#~ msgstr "gatavs"

#~ msgid "Remember choice"
#~ msgstr "Atcerēties izvēli"

#~ msgid "Kosovo"
#~ msgstr "Kosova"

#~ msgid "Unknown Network Interface"
#~ msgstr "Nezināms Tīkla Interfeiss"

#~ msgid "Listening Port Unavailable"
#~ msgstr "Klausīšanās Ports Nav Pieejams"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr ""
#~ "Šķiet, ka serveris nedarbojas vai nereaģē. Atkārtots mēģinājums pēc %i "
#~ "sekundēm"

#~ msgid ""
#~ "Nicotine+ uses peer-to-peer networking to connect to other users. In "
#~ "order to allow users to connect to you without trouble, an open listening "
#~ "port is crucial."
#~ msgstr ""
#~ "Nicotine+ izmanto peer-to-peer tīklus, lai izveidotu savienojumu ar "
#~ "citiem lietotājiem. Lai lietotāji varētu bez problēmām izveidot "
#~ "savienojumu ar Jums, ir nepieciešams atvērts klausīšanās ports."

#~ msgid "--- disconnected ---"
#~ msgstr "--- atvienots ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- atjaunoti savienots ---"

#~ msgid "ID"
#~ msgstr "ID"

#~ msgid "Earth"
#~ msgstr "Zeme"

#~ msgid "Czech Republic"
#~ msgstr "Čehijas Republika"

#~ msgid "Turkey"
#~ msgstr "Turcija"

#~ msgid "Joined Rooms "
#~ msgstr "Pievienotās Istabas "

#~ msgid "_Auto-join Room"
#~ msgstr "_Automātiski pievienoties istabai"

#~ msgid ""
#~ "<b>Syntax</b>: Letters are case-insensitive. All Python regular "
#~ "expressions are supported if escaping is disabled. For simple filters, "
#~ "keeping escaping enabled is recommended."
#~ msgstr ""
#~ "<b>Sintakse</b>: Burti nav reģistrjutīgi. Visas Python regulārās "
#~ "izteiksmes tiek atbalstītas, ja izvadīšana ir atspējota. Vienkāršiem "
#~ "filtriem ieteicams saglabāt ieslēgtu izvadīšanu."

#~ msgid "Escaped"
#~ msgstr "Izbēdzis"

#~ msgid "Escape filter"
#~ msgstr "Iziet no filtra"

#~ msgid "Enter a text pattern and what to replace it with"
#~ msgstr "Ievadiet teksta modeli un to, ar ko to aizstāt"

#, python-format
#~ msgid "No listening port is available in the specified port range %s–%s"
#~ msgstr ""
#~ "Norādītajā portu diapazonā %s–%s nav pieejams neviens klausīšanās ports"

#~ msgid ""
#~ "Choose a range to select a listening port from. The first available port "
#~ "in the range will be used."
#~ msgstr ""
#~ "Izvēlieties diapazonu, no kura izvēlēties klausīšanās portu. Tiks "
#~ "izmantots pirmais pieejamais ports šajā diapazonā."

#~ msgid "First Port"
#~ msgstr "Pirmais Ports"

#~ msgid "to"
#~ msgstr "līdz"

#~ msgid "Last Port"
#~ msgstr "Pēdējais Ports"

#, python-format
#~ msgid "Cannot find %s or newer, please install it."
#~ msgstr "Nevar atrast %s vai jaunāku, lūdzu, instalējiet to."

#~ msgid ""
#~ "Cannot import the Gtk module. Bad install of the python-gobject module?"
#~ msgstr ""
#~ "Nevar importēt Gtk moduli. Slikta python-gobject moduļa instalēšana?"

#, python-format
#~ msgid ""
#~ "You are using an unsupported version of GTK %(major_version)s. You should "
#~ "install GTK %(complete_version)s or newer."
#~ msgstr ""
#~ "Jūs izmantojat neatbalstītu GTK %(major_version)s versiju. Jums vajadzētu "
#~ "instalēt GTK %(complete_version)s vai jaunāku versiju."

#~ msgid "User Info"
#~ msgstr "Lietotāja Informācija"

#~ msgid "Zoom 1:1"
#~ msgstr "Tālummaiņa 1:1"

#~ msgid "Zoom In"
#~ msgstr "Pietuvināt"

#~ msgid "Zoom Out"
#~ msgstr "Attālināt"

#~ msgid "Show User I_nfo"
#~ msgstr "Rādīt Lietotāja I_nformāciju"

#~ msgid "Request User's Info"
#~ msgstr "Pieprasīt Lietotāja Informāciju"

#~ msgid "Request User's Shares"
#~ msgstr "Pieprasīt Lietotāja Koplietojumus"

#~ msgid "Request User Info"
#~ msgstr "Pieprasīt Lietotāja Informāciju"

#~ msgid "Enter the name of the user whose info you want to see:"
#~ msgstr "Ievadiet tā lietotāja vārdu, kura informāciju vēlaties redzēt:"

#~ msgid "Request Shares List"
#~ msgstr "Pieprasīt Koplietojumu Sarakstu"

#, python-format
#~ msgid "Invalid Soulseek URL: %s"
#~ msgstr "Nederīgs Soulseek URL: %s"

#~ msgid ""
#~ "Users on the Soulseek network will be able to download files from folders "
#~ "you share. Sharing files is crucial for the health of the Soulseek "
#~ "network."
#~ msgstr ""
#~ "Soulseek tīkla lietotāji varēs lejupielādēt failus no Jūsu koplietotajām "
#~ "mapēm. Failu koplietošana ir būtiska Soulseek tīkla veselībai un darbībai."

#~ msgid "Update I_nfo"
#~ msgstr "Atjaunināt I_nformāciju"

#~ msgid "Chat Room Commands"
#~ msgstr "Tērzēšanas Istabas Komandas"

#~ msgid "/join /j 'room'"
#~ msgstr "/join /j 'istaba'"

#~ msgid "Join room 'room'"
#~ msgstr "Pievienoties istabai \"istaba\""

#~ msgid "/me 'message'"
#~ msgstr "/me 'ziņa'"

#~ msgid "Display the Now Playing script's output"
#~ msgstr "Parādīt Skripta Tagad Atskaņo Izvadi"

#~ msgid "/add /ad 'user'"
#~ msgstr "/add /ad 'lietotājs'"

#~ msgid "Add user 'user' to your buddy list"
#~ msgstr "Pievieno lietotāju \"lietotājs\" savam draugu sarakstam"

#~ msgid "/rem /unbuddy 'user'"
#~ msgstr "/rem /unbuddy 'lietotājs'"

#~ msgid "Remove user 'user' from your buddy list"
#~ msgstr "Noņem lietotāju \"lietotājs\" no sava draugu saraksta"

#~ msgid "/ban 'user'"
#~ msgstr "/ban 'lietotājs'"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "Pievieno lietotāju \"lietotājs\" savam aizlieguma sarakstam"

#~ msgid "/unban 'user'"
#~ msgstr "/unban 'lietotājs'"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "Noņem lietotāju \"lietotājs\" no aizlieguma saraksta"

#~ msgid "/ignore 'user'"
#~ msgstr "/ignore 'lietotājs'"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "Pievieno lietotāju “lietotājs” savam ignorēšanas sarakstam"

#~ msgid "/unignore 'user'"
#~ msgstr "/unignore 'lietotājs'"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "Noņem lietotāju \"lietotājs\" no sava ignorēšanas saraksta"

#~ msgid "/browse /b 'user'"
#~ msgstr "/browse /b 'lietotājs'"

#~ msgid "/whois /w 'user'"
#~ msgstr "/whois /w 'lietotājs'"

#~ msgid "Request info for 'user'"
#~ msgstr "Pieprasīt lietotāja 'lietotājs' informāciju"

#~ msgid "/ip 'user'"
#~ msgstr "/ip 'lietotājs'"

#~ msgid "Show IP for user 'user'"
#~ msgstr "Rādīt Lietotāja 'lietotājs' IP Adresi"

#~ msgid "/search /s 'query'"
#~ msgstr "/search /s 'vaicājums'"

#~ msgid "Start a new search for 'query'"
#~ msgstr "Sākt jaunu meklēšanu 'vaicājums'"

#~ msgid "/rsearch /rs 'query'"
#~ msgstr "/rsearch /rs 'vaicājums'"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "Meklēt pievienotajās telpās 'vaicājums'"

#~ msgid "/bsearch /bs 'query'"
#~ msgstr "/bsearch /bs 'vaicājums'"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "Meklēt draugu sarakstā 'vaicājums'"

#~ msgid "/usearch /us 'user' 'query'"
#~ msgstr "/usearch /us 'lietotājs' 'vaicājums'"

#~ msgid "/msg 'user' 'message'"
#~ msgstr "/msg 'lietotājs' 'ziņa'"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "Sūtīt ziņu 'ziņa' lietotājam 'lietotājs'"

#~ msgid "/pm 'user'"
#~ msgstr "/pm 'lietotājs'"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr "Atvērt privāto tērzēšanas logu lietotājam 'lietotājs'"

#~ msgid "Private Chat Commands"
#~ msgstr "Privātās Tērzēšanas Komandas"

#~ msgid "Add user to your ban list"
#~ msgstr "Pievienot lietotāju savam aizliegumu sarakstam"

#~ msgid "Add user to your ignore list"
#~ msgstr "Pievienot lietotāju savam ignorēšanas sarakstam"

#~ msgid "Browse shares of user"
#~ msgstr "Pārlūkot lietotāja koplietojumus"

#~ msgid ""
#~ "For order-insensitive filtering, as well as filtering several exact "
#~ "phrases, vertical bars can be used to separate phrases and words.\n"
#~ "    Example: Remix|Instrumental|Dub Mix"
#~ msgstr ""
#~ "Lai filtrētu lielo/mazo burtu lietojumu, kā arī vairākas precīzas frāzes, "
#~ "var izmantot vertikālas joslas frāžu un vārdu atdalīšanai.\n"
#~ "    Piemērs: Remix|Instrumental|Dub Mix"

#~ msgid "To exclude non-audio files use !0 in the duration filter."
#~ msgstr ""
#~ "Lai neiekļautu failus, kas nav audio faili, ilguma filtrā izmantojiet !0."

#~ msgid "Filters files based upon users' geographical location."
#~ msgstr ""
#~ "Filtrē failus, pamatojoties uz lietotāju ģeogrāfisko atrašanās vietu."

#~ msgid "To view the full results again, simply clear all active filters."
#~ msgstr ""
#~ "Lai vēlreiz skatītu visus rezultātus, notīriet visus aktīvos filtrus."

#~ msgid "See the preferences to set default search result filter options."
#~ msgstr ""
#~ "Skatiet preferences, lai iestatītu noklusējuma meklēšanas rezultātu "
#~ "filtra opcijas."

#~ msgid "File size"
#~ msgstr "Faila izmērs"

#~ msgid "Clear Active Filters"
#~ msgstr "Notīrīt Aktīvos Filtrus"

#~ msgid "Cycle through completions when pressing tab-key"
#~ msgstr ""
#~ "Nospiežot tabulēšanas taustiņu, cikliski pārvietojaties cauri ieteikumiem"

#~ msgid "Hide drop-down when only one matches"
#~ msgstr "Paslēpt nolaižamo izvēlni, ja ir tikai viena atbilstība"

#~ msgid "Download folders in reverse alphanumerical order"
#~ msgstr "Lejupielādēt mapes apgrieztā burtu un ciparu secībā"

#~ msgid "Incomplete file folder:"
#~ msgstr "Nepabeigtu failu mape:"

#~ msgid "Download folder:"
#~ msgstr "Lejupielādes mape:"

#~ msgid "Save buddies' uploads to:"
#~ msgstr "Saglabāt draugu augšupielādes šeit:"

#~ msgid "Use UPnP to forward listening port"
#~ msgstr "Izmantot UPnP, lai pārsūtītu klausīšanās portu"

#~ msgid ""
#~ "Share folders with every Soulseek user or buddies, allowing contents to "
#~ "be downloaded directly from your device. Hidden files are never shared."
#~ msgstr ""
#~ "Kopīgojiet mapes ar katru Soulseek lietotāju vai draugiem, ļaujot saturu "
#~ "lejupielādēt tieši no Jūsu ierīces. Slēptie faili nekad netiek koplietoti."

#~ msgid "Secondary Tabs"
#~ msgstr "Sekundārās Cilnes"

#~ msgid "Chat room tab bar position:"
#~ msgstr "Tērzēšanas istabas cilnes joslas pozīcija:"

#~ msgid "Private chat tab bar position:"
#~ msgstr "Privātās tērzēšanas cilnes joslas pozīcija:"

#~ msgid "Search tab bar position:"
#~ msgstr "Meklēšanas cilnes joslas pozīcija:"

#~ msgid "User info tab bar position:"
#~ msgstr "Lietotāja informācijas cilnes joslas pozīcija:"

#~ msgid "User browse tab bar position:"
#~ msgstr "Lietotāja pārlūkošanas cilnes joslas pozīcija:"

#~ msgid "Tab Labels"
#~ msgstr "Cilnes Etiķetes"

#~ msgid "_Refresh Info"
#~ msgstr "_Atsvaidzināt Informāciju"

#~ msgid "Block IP Address"
#~ msgstr "Bloķēt IP adresi"

#~ msgid "Connected"
#~ msgstr "Savienots"

#~ msgid "Disconnected"
#~ msgstr "Atvienots"

#~ msgid "Disconnected (Tray)"
#~ msgstr "Atvienots (Ikonjosla)"

#~ msgid "User(s)"
#~ msgstr "Lietotājs(-i)"

#, python-format
#~ msgid "Alias \"%s\" returned nothing"
#~ msgstr "Alias \"%s\" neko neatgrieza"

#~ msgid "Alternative Speed Limits"
#~ msgstr "Alternatīvie ātruma ierobežojumi"

#~ msgid "Last played"
#~ msgstr "Pēdējo reizi atskaņots"

#~ msgid "Playing now"
#~ msgstr "Tagad atskaņo"

#, python-format
#~ msgid "Error code %(code)s: %(description)s"
#~ msgstr "Kļūdas kods %(code)s: %(description)s"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "Nav šāda aliasa (%s)"

#~ msgid "Aliases:"
#~ msgstr "Aliasi:"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "Noņemts alias %(alias)s: %(action)s\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "Nav šāda aliasa (%(alias)s)\n"

#~ msgid "Aliases"
#~ msgstr "Aliasi"

#~ msgid "/alias /al 'command' 'definition'"
#~ msgstr "/alias /al 'komanda' 'definīcija'"

#~ msgid "Add a new alias"
#~ msgstr "Pievienot jaunu aliasu"

#~ msgid "/unalias /un 'command'"
#~ msgstr "/unalias /un 'komanda'"

#~ msgid "Remove an alias"
#~ msgstr "Noņemt aliasu"

#~ msgid "Chat History"
#~ msgstr "Tērzēšanas vēsture"

#~ msgid "Command aliases"
#~ msgstr "Komandu aizstājvārdi"

#~ msgid "Limit download speed to (KiB/s):"
#~ msgstr "Ierobežot lejupielādes ātrumu līdz (KiB/s):"

#~ msgid "Author(s):"
#~ msgstr "Autors(-i):"

#~ msgid "Limit upload speed to (KiB/s):"
#~ msgstr "Ierobežot augšupielādes ātrumu līdz (KiB/s):"

#~ msgid "Tabs show user status icons instead of status text"
#~ msgstr "Cilnēs statusa teksta vietā tiek rādītas lietotāja statusa ikonas"

#~ msgid "Show file path tooltips in file list views"
#~ msgstr "Rādīt faila ceļa padomus failu saraksta skatos"

#~ msgid "Colored and clickable usernames"
#~ msgstr "Krāsaini un klikšķināmi lietotājvārdi"

#~ msgid "Notification changes the tab's text color"
#~ msgstr "Paziņojums maina cilnes teksta krāsu"

#~ msgid "Cancel"
#~ msgstr "Atcelt"

#~ msgid "OK"
#~ msgstr "LABI"

#~ msgid "_Add to Buddy List"
#~ msgstr "_Pievienot draugu sarakstam"

#~ msgid "use non-default user data directory for e.g. list of downloads"
#~ msgstr ""
#~ "izmantot ne-noklusējuma lietotāja datu direktoriju, piemēram, lejupielāžu "
#~ "sarakstu"

#, python-format
#~ msgid "Unknown config section '%s'"
#~ msgstr "Nezināma konfigurācijas sadaļa “%s”"

#, python-format
#~ msgid "Unknown config option '%(option)s' in section '%(section)s'"
#~ msgstr "Nezināma konfigurācijas opcija “%(option)s” sadaļā “%(section)s”"

#, python-format
#~ msgid "Version %s is available"
#~ msgstr "Ir pieejama versija %s"

#, python-format
#~ msgid "released on %s"
#~ msgstr "izlaists %s"

#~ msgid "Nicotine+ is running in the background"
#~ msgstr "Nicotine+ darbojas fonā"

#, python-format
#~ msgid "Private message from %s"
#~ msgstr "Privāta ziņa no %s"

#~ msgid "Aborted"
#~ msgstr "Pārtraukts"

#~ msgid "Blocked country"
#~ msgstr "Bloķēta valsts"

#~ msgid "Finished / Aborted"
#~ msgstr "Pabeigts / Pārtraukts"

#~ msgid "Close tab"
#~ msgstr "Aizvērt cilni"

#, python-format
#~ msgid "You've been mentioned in the %(room)s room"
#~ msgstr "Jūs pieminēja %(room)s istabā"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "Komanda %s nav atpazīta"

#, python-format
#~ msgid "(Warning: %(realuser)s is attempting to spoof %(fakeuser)s) "
#~ msgstr "(Brīdinājums: %(realuser)s mēģina atdarināt %(fakeuser)s) "

#, python-format
#~ msgid ""
#~ "The network interface you specified, '%s', does not exist. Change or "
#~ "remove the specified network interface and restart Nicotine+."
#~ msgstr ""
#~ "Jūsu norādītā tīkla saskarne “%s” neeksistē. Mainiet vai noņemiet "
#~ "norādīto tīkla interfeisu un restartējiet Nicotine+."

#~ msgid ""
#~ "The range you specified for client connection ports was {}-{}, but none "
#~ "of these were usable. Increase and/or "
#~ msgstr ""
#~ "Diapazons, ko norādījāt klienta savienojuma portiem, bija {}-{}, taču "
#~ "neviens no tiem nebija izmantojams. Palieliniet un/vai "

#~ msgid ""
#~ "Note that part of your range lies below 1024, this is usually not allowed "
#~ "on most operating systems with the exception of Windows."
#~ msgstr ""
#~ "Ņemiet vērā, ka daļa Jūsu diapazona ir mazāka par 1024; izņemot Windows, "
#~ "tas parasti nav atļauts lielākajā daļā operētājsistēmu."

#, python-format
#~ msgid "Rescan progress: %s"
#~ msgstr "Pārskenēšanas progress: %s"

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "OS kļūda: %s"

#~ msgid "UPnP is not available on this network"
#~ msgstr "UPnP šajā tīklā nav pieejams"

#, python-format
#~ msgid "Failed to map the external WAN port: %(error)s"
#~ msgstr "Neizdevās kartēt ārējo WAN portu: %(error)s"

#~ msgid "Room wall"
#~ msgstr "Istabas siena"

#~ msgid ""
#~ "The default listening port '2234' works fine in most cases. If you need "
#~ "to use a different port, you will be able to modify it in the preferences "
#~ "later."
#~ msgstr ""
#~ "Vairumā gadījumu ar noklusējuma klausīšanās portu '2234' nav problēmu. Ja "
#~ "Jums nepieciešams izmantot citu portu, vēlāk to varēsiet mainīt "
#~ "preferencēs."

#~ msgid "Show users with similar interests"
#~ msgstr "Rādīt lietotājus ar līdzīgām interesēm"

#~ msgid "Menu"
#~ msgstr "Izvēlne"

#~ msgid "Expand / Collapse all"
#~ msgstr "Izvērst / Sakļaut visu"

#~ msgid "Configure shares"
#~ msgstr "Konfigurēt koplietojumus"

#~ msgid "Create or join room…"
#~ msgstr "Izveidot vai pievienoties istabai…"

#~ msgid "_Room List"
#~ msgstr "_Istabu saraksts"

#~ msgid "Enable alternative download and upload speed limits"
#~ msgstr ""
#~ "Iespējot alternatīvos lejupielādes un augšupielādes ātruma ierobežojumus"

#~ msgid "Show log history"
#~ msgstr "Rādīt žurnāla vēsturi"

#~ msgid "Result grouping mode"
#~ msgstr "Rezultātu grupēšanas režīms"

#~ msgid "Free slot"
#~ msgstr "Brīvs slots"

#~ msgid "Save shares list to disk"
#~ msgstr "Saglabāt koplietojumu sarakstu diskā"

#~ msgid "_Away"
#~ msgstr "_Aizgājis"

#, python-format
#~ msgid "Failed to load ui file %(file)s: %(error)s"
#~ msgstr "Neizdevās ielādēt lietotāja interfeisa failu %(file)s: %(error)s"

#~ msgid ""
#~ "Attempting to reset index of shared files due to an error. Please rescan "
#~ "your shares."
#~ msgstr ""
#~ "Kļūdas dēļ tiek mēģināts atiestatīt koplietoto failu indeksu. Lūdzu, "
#~ "pārskenējiet savus koplietojumus."

#~ msgid ""
#~ "File index of shared files could not be accessed. This could occur due to "
#~ "several instances of Nicotine+ being active simultaneously, file "
#~ "permission issues, or another issue in Nicotine+."
#~ msgstr ""
#~ "Nevarēja piekļūt koplietoto failu indeksam. Tas var notikt, kad vairāki "
#~ "Nicotine+ vienlaikus ir aktīvi, faila atļauju problēmu vai citu Nicotine+ "
#~ "problēmu dēļ."

#~ msgid "Nicotine+ is a Soulseek client"
#~ msgstr "Nicotine+ ir Soulseek klients"

#~ msgid "enable the tray icon"
#~ msgstr "iespējot ikonjoslas ikonu"

#~ msgid "disable the tray icon"
#~ msgstr "atspējojiet ikonjoslas ikonu"

#~ msgid "Get Soulseek Privileges…"
#~ msgstr "Iegūt Soulseek privilēģijas…"

#, python-format
#~ msgid ""
#~ "Public IP address is <b>%(ip)s</b> and active listening port is "
#~ "<b>%(port)s</b>"
#~ msgstr ""
#~ "Publiskā IP adrese ir <b>%(ip)s</b> un aktīvais klausīšanās ports ir "
#~ "<b>%(port)s</b>"

#~ msgid "unknown"
#~ msgstr "nezināms"

#~ msgid "Notification"
#~ msgstr "Paziņojums"

#~ msgid "Length"
#~ msgstr "Garums"

#, python-format
#~ msgid "Picture not saved, %s already exists."
#~ msgstr "Attēls nav saglabāts, %s jau pastāv."

#~ msgid "_Open"
#~ msgstr "_Atvērt"

#~ msgid "_Save"
#~ msgstr "_Saglabāt"

#, python-format
#~ msgid "Failed to load plugin '%s', could not find it."
#~ msgstr "Neizdevās ielādēt spraudni “%s”, nevarēja to atrast."

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "I/O kļūda: %s"

#, python-format
#~ msgid "Failed to open file path: %s"
#~ msgstr "Neizdevās atvērt faila ceļu: %s"

#, python-format
#~ msgid "Failed to open URL: %s"
#~ msgstr "Neizdevās atvērt URL: %s"

#~ msgid "_Log Conversation"
#~ msgstr "_Reģistrēt sarunu"

#~ msgid "Result Filter List"
#~ msgstr "Rezultātu filtru saraksts"

#~ msgid "Prepend < or > to find files less/greater than the given value."
#~ msgstr ""
#~ "Pievienojiet sākumā < vai >, lai atrastu failus, kas ir mazāki/lielāki "
#~ "par norādīto vērtību."

#~ msgid ""
#~ "VBR files display their average bitrate and are typically lower in "
#~ "bitrate than a compressed 320 kbps CBR file of the same audio quality."
#~ msgstr ""
#~ "VBR faili parāda vidējo bitu pārraides ātrumu, un parasti tie ir mazāki "
#~ "nekā saspiestam 320 kbps CBR failam ar tādu pašu audio kvalitāti."

#~ msgid "Like Size above, =, <, and > can be used."
#~ msgstr "Tāpat kā iepriekš norādīto izmēru, var izmantot =, < un >."

#~ msgid ""
#~ "Prevent write access by other programs for files being downloaded (turn "
#~ "off for NFS)"
#~ msgstr ""
#~ "Neļaut citām programmām piekļūt lejupielādējamo failu rakstīšanai "
#~ "(izslēgt NFS gadījumā)"

#~ msgid "Where incomplete downloads are temporarily stored."
#~ msgstr "Kur uz laiku tiek uzglabātas nepabeigtās lejupielādes."

#~ msgid ""
#~ "Where buddies' uploads will be stored (with a subfolder created for each "
#~ "buddy)."
#~ msgstr ""
#~ "Kur tiks glabātas draugu augšupielādes (katram draugam tiek izveidota "
#~ "apakšmape)."

#~ msgid "Toggle away status after minutes of inactivity:"
#~ msgstr "Pārslēgt \"aizgājis\" statusu pēc neaktivitātes minūtēm:"

#~ msgid "Protocol:"
#~ msgstr "Protokols:"

#~ msgid "Icon theme folder (requires restart):"
#~ msgstr "Ikonu motīvu mape (nepieciešams restartēt):"
