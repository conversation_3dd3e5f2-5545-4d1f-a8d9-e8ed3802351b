# SPDX-FileCopyrightText: 2003-2025 <PERSON><PERSON>+ Translators
# SPDX-License-Identifier: GPL-3.0-or-later
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-08 11:16+0200\n"
"PO-Revision-Date: 2024-11-17 12:58+0000\n"
"Last-Translator: Mat <<EMAIL>>\n"
"Language-Team: French <https://hosted.weblate.org/projects/nicotine-plus/"
"nicotine-plus/fr/>\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: Weblate 5.9-dev\n"

#: data/org.nicotine_plus.Nicotine.desktop.in:5
msgid "Soulseek Client"
msgstr "Client Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:6 pynicotine/__init__.py:49
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:112
msgid "Graphical client for the Soulseek peer-to-peer network"
msgstr "Client graphique pour le réseau pair-à-pair Soulseek"

#: data/org.nicotine_plus.Nicotine.desktop.in:11
msgid "Soulseek;Nicotine;sharing;chat;messaging;P2P;peer-to-peer;GTK;"
msgstr ""
"Soulseek ;Nicotine ;partage ;tchat ;messagerie ;P2P ;pair-à-pair ;GTK ;"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:9
msgid "Browse the Soulseek network"
msgstr "Naviguez sur le réseau Soulseek"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:11
msgid "Nicotine+ is a graphical client for the Soulseek peer-to-peer network."
msgstr "Nicotine+ est un client graphique pour le réseau pair-à-pair Soulseek."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:15
msgid ""
"Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS) "
"alternative to the official Soulseek client, while also providing a "
"comprehensive set of features."
msgstr ""
"Nicotine+ vise à être une alternative légère, agréable, libre et ouverte "
"(FOSS) au client officiel de Soulseek, tout en fournissant un ensemble "
"complet de fonctionnalités."

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:27
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:43
#: pynicotine/gtkgui/mainwindow.py:753
#: pynicotine/plugins/core_commands/__init__.py:29
#: pynicotine/gtkgui/ui/mainwindow.ui:221
#: pynicotine/gtkgui/ui/settings/userinterface.ui:620
#: pynicotine/gtkgui/ui/settings/userinterface.ui:806
#: pynicotine/gtkgui/ui/userbrowse.ui:144
msgid "Search Files"
msgstr "Rechercher des fichiers"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:31
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:47
#: pynicotine/gtkgui/dialogs/preferences.py:3033
#: pynicotine/gtkgui/mainwindow.py:754 pynicotine/gtkgui/mainwindow.py:1106
#: pynicotine/gtkgui/widgets/trayicon.py:89
#: pynicotine/gtkgui/ui/mainwindow.ui:460
#: pynicotine/gtkgui/ui/settings/downloads.ui:71
#: pynicotine/gtkgui/ui/settings/userinterface.ui:632
msgid "Downloads"
msgstr "Téléchargements"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:35
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:51
#: pynicotine/gtkgui/mainwindow.py:756
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:267
#: pynicotine/gtkgui/ui/mainwindow.ui:867
#: pynicotine/gtkgui/ui/settings/userinterface.ui:656
#: pynicotine/gtkgui/ui/settings/userinterface.ui:828
msgid "Browse Shares"
msgstr "Parcourir les partages"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:39
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:55
#: pynicotine/gtkgui/mainwindow.py:758 pynicotine/gtkgui/widgets/trayicon.py:94
#: pynicotine/plugins/core_commands/__init__.py:27
#: pynicotine/gtkgui/ui/mainwindow.ui:1194
#: pynicotine/gtkgui/ui/settings/userinterface.ui:680
#: pynicotine/gtkgui/ui/settings/userinterface.ui:872
msgid "Private Chat"
msgstr "Dialogues privés"

#: data/org.nicotine_plus.Nicotine.appdata.xml.in:77
#: data/org.nicotine_plus.Nicotine.appdata.xml.in:79
msgid "Nicotine+ Team"
msgstr "L'équipe Nicotine+"

#: pynicotine/__init__.py:50
#, python-format
msgid "Website: %s"
msgstr "Site internet : %s"

#: pynicotine/__init__.py:56
msgid "show this help message and exit"
msgstr "afficher ce message d'aide et quitter"

#: pynicotine/__init__.py:59
msgid "file"
msgstr "fichier"

#: pynicotine/__init__.py:60
msgid "use non-default configuration file"
msgstr "utiliser un fichier de configuration spécifique"

#: pynicotine/__init__.py:63
msgid "dir"
msgstr "rép"

#: pynicotine/__init__.py:64
msgid "alternative directory for user data and plugins"
msgstr "répertoire spécifique pour les données et extensions"

#: pynicotine/__init__.py:68
msgid "start the program without showing window"
msgstr "démarrer le programme sans afficher de fenêtre"

#: pynicotine/__init__.py:71
msgid "ip"
msgstr "ip"

#: pynicotine/__init__.py:72
msgid "bind sockets to the given IP (useful for VPN)"
msgstr "lier les sockets à l'IP spécifiée (utile pour les VPN)"

#: pynicotine/__init__.py:75
msgid "port"
msgstr "port"

#: pynicotine/__init__.py:76
msgid "listen on the given port"
msgstr "écouter sur le port spécifié"

#: pynicotine/__init__.py:80
msgid "rescan shared files"
msgstr "rescanner les fichiers partagés"

#: pynicotine/__init__.py:84
msgid "start the program in headless mode (no GUI)"
msgstr "démarrer le programme en mode sans interface (« headless »)"

#: pynicotine/__init__.py:88
msgid "display version and exit"
msgstr "afficher la version et quitter"

#: pynicotine/__init__.py:121
#, python-format
msgid ""
"You are using an unsupported version of Python (%(old_version)s).\n"
"You should install Python %(min_version)s or newer."
msgstr ""
"Vous utilisez une version de Python non prise en charge (%(old_version)s).\n"
"Vous devriez installer Python %(min_version)s ou supérieure."

#: pynicotine/__init__.py:192
msgid ""
"Failed to scan shares. Please close other Nicotine+ instances and try again."
msgstr ""
"Les partages n'ont pas pu être lus. Fermez les autres instances de Nicotine+ "
"et réessayez."

#: pynicotine/buddies.py:307 pynicotine/plugins/core_commands/__init__.py:337
#, python-format
msgid "%(user)s is away"
msgstr "L'utilisateur %(user)s est absent"

#: pynicotine/buddies.py:310 pynicotine/plugins/core_commands/__init__.py:335
#, python-format
msgid "%(user)s is online"
msgstr "L'utilisateur %(user)s est en ligne"

#: pynicotine/buddies.py:313 pynicotine/plugins/core_commands/__init__.py:329
#, python-format
msgid "%(user)s is offline"
msgstr "L'utilisateur %(user)s est déconnecté"

#: pynicotine/buddies.py:316
msgid "Buddy Status"
msgstr "Status de l'ami"

#: pynicotine/chatrooms.py:383
#, python-format
msgid "You have been added to a private room: %(room)s"
msgstr "Vous avez été invité dans le salon privé : %(room)s"

#: pynicotine/chatrooms.py:478
#, python-format
msgid "Chat message from user '%(user)s' in room '%(room)s': %(message)s"
msgstr ""
"Message de discussion de l'utilisateur '%(user)s' dans la salle '%(room)s' : "
"%(message)s"

#: pynicotine/config.py:126 pynicotine/config.py:145
#, python-format
msgid "Can't create directory '%(path)s', reported error: %(error)s"
msgstr ""
"Impossible de créer le répertoire « %(path)s », l'erreur est  : %(error)s"

#: pynicotine/config.py:816
#, python-format
msgid "Error backing up config: %s"
msgstr "Erreur à la sauvegarde de la configuration : %s"

#: pynicotine/config.py:819
#, python-format
msgid "Config backed up to: %s"
msgstr "Configuration sauvegardée dans %s"

#: pynicotine/core.py:101 pynicotine/core.py:106
#: pynicotine/gtkgui/__init__.py:114
#, python-format
msgid "Loading %(program)s %(version)s"
msgstr "Chargement de %(program)s %(version)s"

#: pynicotine/core.py:243
#, python-format
msgid "Quitting %(program)s %(version)s, %(status)s…"
msgstr "Arrêt de %(program)s %(version)s, %(status)s…"

#: pynicotine/core.py:246
msgid "terminating"
msgstr "terminer"

#: pynicotine/core.py:246
msgid "application closing"
msgstr "fermeture du logiciel"

#: pynicotine/core.py:259
#, python-format
msgid "Quit %(program)s %(version)s!"
msgstr "Fermeture de %(program)s %(version)s !"

#: pynicotine/core.py:267
msgid "You need to specify a username and password before connecting…"
msgstr ""
"Vous devez spécifier un nom d'utilisateur et un mot de passe avant de vous "
"connecter…"

#: pynicotine/downloads.py:239
#, python-format
msgid "Error: Download Filter failed! Verify your filters. Reason: %s"
msgstr ""
"Erreur  : Échec des filtres de téléchargement  ! Vérifier vos filtres. "
"Raison  : %s"

#: pynicotine/downloads.py:254
#, python-format
msgid "Error: %(num)d Download filters failed! %(error)s "
msgstr "Erreur  : %(num)d filtres de téléchargement ont échoués  ! %(error)s "

#: pynicotine/downloads.py:366
#, python-format
msgid "%(file)s downloaded from %(user)s"
msgstr "%(file)s téléchargé de %(user)s"

#: pynicotine/downloads.py:370
msgid "File Downloaded"
msgstr "Fichier téléchargé"

#: pynicotine/downloads.py:378
#, python-format
msgid "Executed: %s"
msgstr "Exécuté  : %s"

#: pynicotine/downloads.py:381 pynicotine/downloads.py:422
#: pynicotine/nowplaying.py:312
#, python-format
msgid "Executing '%(command)s' failed: %(error)s"
msgstr "L'exécution de '%(command)s' a échoué : %(error)s"

#: pynicotine/downloads.py:407
#, python-format
msgid "%(folder)s downloaded from %(user)s"
msgstr "%(folder)s téléchargé s de %(user)s"

#: pynicotine/downloads.py:411
msgid "Folder Downloaded"
msgstr "Dossier téléchargé"

#: pynicotine/downloads.py:419
#, python-format
msgid "Executed on folder: %s"
msgstr "Exécuté sur le dossier  : %s"

#: pynicotine/downloads.py:443
#, python-format
msgid "Couldn't move '%(tempfile)s' to '%(file)s': %(error)s"
msgstr "Impossible de déplacer '%(tempfile)s' vers '%(file)s'  : %(error)s"

#: pynicotine/downloads.py:451 pynicotine/downloads.py:1208
msgid "Download Folder Error"
msgstr "Erreur du téléchargement d'un répertoire"

#: pynicotine/downloads.py:489
#, python-format
msgid "Download finished: user %(user)s, file %(file)s"
msgstr "Téléchargement terminé  : utilisateur %(user)s, fichier %(file)s"

#: pynicotine/downloads.py:499
#, python-format
msgid "Download aborted, user %(user)s file %(file)s"
msgstr "Téléchargement annulé, utilisateur %(user)s, fichier %(file)s"

#: pynicotine/downloads.py:1150
#, python-format
msgid "Download I/O error: %s"
msgstr "Erreur d'E/S lors du téléchargement  : %s"

#: pynicotine/downloads.py:1189
#, python-format
msgid "Can't get an exclusive lock on file - I/O error: %s"
msgstr "Ne peut obtenir un verrou exclusif sur le fichier - Erreur E/S  : %s"

#: pynicotine/downloads.py:1202
#, python-format
msgid "Cannot save file in %(folder_path)s: %(error)s"
msgstr "Impossible d'enregistrer le fichier sur %(folder_path)s : %(error)s"

#: pynicotine/downloads.py:1221
#, python-format
msgid "Download started: user %(user)s, file %(file)s"
msgstr "Téléchargement commencé  : utilisateur %(user)s, fichier %(file)s"

#: pynicotine/gtkgui/__init__.py:88 pynicotine/gtkgui/__init__.py:97
#, python-format
msgid "Cannot find %s, please install it."
msgstr "Impossible de trouver %s, veuillez l'installer."

#: pynicotine/gtkgui/__init__.py:162
msgid "No graphical environment available, using headless (no GUI) mode"
msgstr ""
"Aucun environnement graphique disponible, utilise le mode sans interface "
"graphique"

#: pynicotine/gtkgui/application.py:306
#: pynicotine/gtkgui/widgets/trayicon.py:101
msgid "_Connect"
msgstr "Se _connecter"

#: pynicotine/gtkgui/application.py:307
#: pynicotine/gtkgui/widgets/trayicon.py:102
msgid "_Disconnect"
msgstr "Se _déconnecter"

#: pynicotine/gtkgui/application.py:308
msgid "Soulseek _Privileges"
msgstr "Privilèges _Soulseek"

#: pynicotine/gtkgui/application.py:314
msgid "_Preferences"
msgstr "_Préférences"

#: pynicotine/gtkgui/application.py:320 pynicotine/gtkgui/application.py:534
#: pynicotine/gtkgui/widgets/trayicon.py:107
msgid "_Quit"
msgstr "_Quitter"

#: pynicotine/gtkgui/application.py:337
msgid "Browse _Public Shares"
msgstr "_Parcourir les partages publics"

#: pynicotine/gtkgui/application.py:338
msgid "Browse _Buddy Shares"
msgstr "Par_courir les partages des amis"

#: pynicotine/gtkgui/application.py:339
msgid "Browse _Trusted Shares"
msgstr "Par_courir les partages pour amis de confiance"

#: pynicotine/gtkgui/application.py:348 pynicotine/gtkgui/application.py:409
msgid "_Rescan Shares"
msgstr "_Réexaminer mes partages"

#: pynicotine/gtkgui/application.py:349 pynicotine/gtkgui/application.py:411
msgid "Configure _Shares"
msgstr "Configurer les partages"

#: pynicotine/gtkgui/application.py:371
msgid "_Keyboard Shortcuts"
msgstr "_Raccourcis clavier"

#: pynicotine/gtkgui/application.py:372
msgid "_Setup Assistant"
msgstr "_Assistant de configuration"

#: pynicotine/gtkgui/application.py:373
msgid "_Transfer Statistics"
msgstr "Statistiques de transferts"

#: pynicotine/gtkgui/application.py:378
msgid "Report a _Bug"
msgstr "Signaler un _bogue"

#: pynicotine/gtkgui/application.py:379
msgid "Improve T_ranslations"
msgstr "Améliorer les t_raductions"

#: pynicotine/gtkgui/application.py:383
msgid "_About Nicotine+"
msgstr "À propos de _Nicotine+"

#: pynicotine/gtkgui/application.py:394
msgid "_File"
msgstr "_Fichier"

#: pynicotine/gtkgui/application.py:395
msgid "_Shares"
msgstr "_Partages"

#: pynicotine/gtkgui/application.py:396 pynicotine/gtkgui/application.py:413
msgid "_Help"
msgstr "A_ide"

#: pynicotine/gtkgui/application.py:410
msgid "_Browse Shares"
msgstr "_Parcourir les partages"

#: pynicotine/gtkgui/application.py:465
#, python-format
msgid "Unable to show notification: %s"
msgstr "Impossible d'afficher la notification : %s"

#: pynicotine/gtkgui/application.py:526
msgid "You are still uploading files. Do you really want to exit?"
msgstr "Vous téléchargez toujours des fichiers. Voulez vous vraiment quitter ?"

#: pynicotine/gtkgui/application.py:527
msgid "Wait for uploads to finish"
msgstr "Attendre la fin des transferts"

#: pynicotine/gtkgui/application.py:529
msgid "Do you really want to exit?"
msgstr "Voulez-vous vraiment quitter ?"

#: pynicotine/gtkgui/application.py:533
#: pynicotine/gtkgui/widgets/dialogs.py:487
msgid "_No"
msgstr "_Non"

#: pynicotine/gtkgui/application.py:535
msgid "_Run in Background"
msgstr "Exécuter en a_rrière-plan"

#: pynicotine/gtkgui/application.py:540
#: pynicotine/gtkgui/dialogs/preferences.py:1749
#: pynicotine/plugins/core_commands/__init__.py:68
msgid "Quit Nicotine+"
msgstr "Quitter Nicotine+"

#: pynicotine/gtkgui/application.py:561
msgid "Shares Not Available"
msgstr "Partages non disponibles"

#: pynicotine/gtkgui/application.py:562 pynicotine/headless/application.py:124
msgid ""
"Verify that external disks are mounted and folder permissions are correct."
msgstr ""
"Vérifiez que les disques externes sont montés et que les permissions sur les "
"dossiers sont correctes."

#: pynicotine/gtkgui/application.py:565
#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
#: pynicotine/gtkgui/dialogs/pluginsettings.py:42
#: pynicotine/gtkgui/downloads.py:173 pynicotine/gtkgui/widgets/dialogs.py:148
#: pynicotine/gtkgui/widgets/dialogs.py:526
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:5
msgid "_Cancel"
msgstr "A_nnuler"

#: pynicotine/gtkgui/application.py:566 pynicotine/gtkgui/uploads.py:49
msgid "_Retry"
msgstr "_Réessayer"

#: pynicotine/gtkgui/application.py:567
msgid "_Force Rescan"
msgstr "_Forcer la réanalyse"

#: pynicotine/gtkgui/application.py:742
msgid "Message Downloading Users"
msgstr "Envoyer un message aux utilisateurs qui téléchargent"

#: pynicotine/gtkgui/application.py:743
msgid "Send private message to all users who are downloading from you:"
msgstr ""
"Envoyer un message privé à tous les utilisateurs qui téléchargent "
"actuellement à partir de vous :"

#: pynicotine/gtkgui/application.py:744 pynicotine/gtkgui/application.py:758
#: pynicotine/gtkgui/widgets/popupmenu.py:438
#: pynicotine/gtkgui/ui/userinfo.ui:463
msgid "_Send Message"
msgstr "_Envoyer un message"

#: pynicotine/gtkgui/application.py:756
msgid "Message Buddies"
msgstr "Message pour les amis"

#: pynicotine/gtkgui/application.py:757
msgid "Send private message to all online buddies:"
msgstr "Envoyer un message privé à tous les amis en ligne :"

#: pynicotine/gtkgui/application.py:786
msgid "Select a Saved Shares List File"
msgstr "Sélectionner un fichier de liste des partages sauvegardées"

#: pynicotine/gtkgui/application.py:877
msgid "Critical Error"
msgstr "Erreur critique"

#: pynicotine/gtkgui/application.py:878
msgid ""
"Nicotine+ has encountered a critical error and needs to exit. Please copy "
"the following message and include it in a bug report:"
msgstr ""
"Nicotine+ a rencontré une erreur critique et doit se terminer. Veuillez "
"copier le message suivant et l'inclure dans un rapport de bogue :"

#: pynicotine/gtkgui/application.py:882
msgid "_Quit Nicotine+"
msgstr "_Quitter Nicotine+"

#: pynicotine/gtkgui/application.py:883
msgid "_Copy & Report Bug"
msgstr "_Copier et rapporter le bogue"

#: pynicotine/gtkgui/buddies.py:71 pynicotine/gtkgui/chatrooms.py:539
#: pynicotine/gtkgui/interests.py:127
#: pynicotine/gtkgui/popovers/chathistory.py:65
#: pynicotine/gtkgui/transfers.py:176
msgid "Status"
msgstr "État"

#: pynicotine/gtkgui/buddies.py:77 pynicotine/gtkgui/chatrooms.py:545
#: pynicotine/gtkgui/interests.py:133 pynicotine/gtkgui/search.py:519
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:387
msgid "Country"
msgstr "Pays"

#: pynicotine/gtkgui/buddies.py:83 pynicotine/gtkgui/chatrooms.py:551
#: pynicotine/gtkgui/dialogs/preferences.py:997
#: pynicotine/gtkgui/dialogs/preferences.py:1169
#: pynicotine/gtkgui/interests.py:139
#: pynicotine/gtkgui/popovers/chathistory.py:71 pynicotine/gtkgui/search.py:513
#: pynicotine/gtkgui/transfers.py:147
msgid "User"
msgstr "Utilisateur"

#: pynicotine/gtkgui/buddies.py:90 pynicotine/gtkgui/chatrooms.py:562
#: pynicotine/gtkgui/interests.py:146 pynicotine/gtkgui/search.py:525
#: pynicotine/gtkgui/transfers.py:201
msgid "Speed"
msgstr "Vitesse"

#: pynicotine/gtkgui/buddies.py:96 pynicotine/gtkgui/chatrooms.py:570
#: pynicotine/gtkgui/interests.py:153 pynicotine/gtkgui/ui/mainwindow.ui:338
#: pynicotine/gtkgui/ui/mainwindow.ui:577
msgid "Files"
msgstr "Fichiers"

#: pynicotine/gtkgui/buddies.py:102
#: pynicotine/gtkgui/dialogs/preferences.py:665
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted"
msgstr "Utilisateur de confiance"

#: pynicotine/gtkgui/buddies.py:108
msgid "Notify"
msgstr "Notifier"

#: pynicotine/gtkgui/buddies.py:114
msgid "Prioritized"
msgstr "En priorité"

#: pynicotine/gtkgui/buddies.py:120
msgid "Last Seen"
msgstr "Vu pour la dernière fois"

#: pynicotine/gtkgui/buddies.py:126
msgid "Note"
msgstr "Note"

#: pynicotine/gtkgui/buddies.py:143
msgid "Add User _Note…"
msgstr "Ajouter des _Notes d’utilisateur…"

#: pynicotine/gtkgui/buddies.py:145
#: pynicotine/gtkgui/dialogs/pluginsettings.py:224
#: pynicotine/gtkgui/dialogs/preferences.py:292
#: pynicotine/gtkgui/dialogs/preferences.py:816
#: pynicotine/gtkgui/dialogs/wishlist.py:81 pynicotine/gtkgui/interests.py:188
#: pynicotine/gtkgui/interests.py:197 pynicotine/gtkgui/transfers.py:282
#: pynicotine/gtkgui/userinfo.py:379
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:513
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:529
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:90
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:106
#: pynicotine/gtkgui/ui/downloads.ui:116
#: pynicotine/gtkgui/ui/settings/ban.ui:194
#: pynicotine/gtkgui/ui/settings/ban.ui:210
#: pynicotine/gtkgui/ui/settings/ban.ui:316
#: pynicotine/gtkgui/ui/settings/ban.ui:332
#: pynicotine/gtkgui/ui/settings/chats.ui:765
#: pynicotine/gtkgui/ui/settings/chats.ui:781
#: pynicotine/gtkgui/ui/settings/chats.ui:949
#: pynicotine/gtkgui/ui/settings/chats.ui:965
#: pynicotine/gtkgui/ui/settings/downloads.ui:510
#: pynicotine/gtkgui/ui/settings/downloads.ui:526
#: pynicotine/gtkgui/ui/settings/ignore.ui:128
#: pynicotine/gtkgui/ui/settings/ignore.ui:144
#: pynicotine/gtkgui/ui/settings/ignore.ui:249
#: pynicotine/gtkgui/ui/settings/ignore.ui:265
#: pynicotine/gtkgui/ui/settings/shares.ui:189
#: pynicotine/gtkgui/ui/settings/shares.ui:205
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:138
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:154
msgid "Remove"
msgstr "Enlever"

#: pynicotine/gtkgui/buddies.py:364
msgid "Never seen"
msgstr "Jamais vu"

#: pynicotine/gtkgui/buddies.py:529
msgid "Add User Note"
msgstr "Ajouter une note utilisateur"

#: pynicotine/gtkgui/buddies.py:530
#, python-format
msgid "Add a note about user %s:"
msgstr "Ajouter des commentaires concernant l'utilisateur %s :"

#: pynicotine/gtkgui/buddies.py:531
#: pynicotine/gtkgui/dialogs/pluginsettings.py:385
#: pynicotine/gtkgui/dialogs/preferences.py:470
#: pynicotine/gtkgui/dialogs/preferences.py:1059
#: pynicotine/gtkgui/dialogs/preferences.py:1100
#: pynicotine/gtkgui/dialogs/preferences.py:1250
#: pynicotine/gtkgui/dialogs/preferences.py:1291
#: pynicotine/gtkgui/dialogs/preferences.py:1527
#: pynicotine/gtkgui/dialogs/preferences.py:1590
#: pynicotine/gtkgui/dialogs/preferences.py:2572
msgid "_Add"
msgstr "_Ajouter"

#: pynicotine/gtkgui/chatrooms.py:224
msgid "Create New Room?"
msgstr "Créer un nouveau Salon  ?"

#: pynicotine/gtkgui/chatrooms.py:225
#, python-format
msgid "Do you really want to create a new room \"%s\"?"
msgstr "Voulez-vous vraiment créer le nouveau salon \"%s\" ?"

#: pynicotine/gtkgui/chatrooms.py:226
msgid "Make room private"
msgstr "Rendre le Salon privée"

#: pynicotine/gtkgui/chatrooms.py:515
msgid "Search activity log…"
msgstr "recherche de journalisation…"

#: pynicotine/gtkgui/chatrooms.py:522 pynicotine/gtkgui/privatechat.py:342
msgid "Search chat log…"
msgstr "Recherche dans les salons…"

#: pynicotine/gtkgui/chatrooms.py:597
msgid "Sear_ch User's Files"
msgstr "Recher_cher dans les fichiers de l'utilisateur"

#: pynicotine/gtkgui/chatrooms.py:603 pynicotine/gtkgui/chatrooms.py:615
#: pynicotine/gtkgui/privatechat.py:370
msgid "Find…"
msgstr "Trouver…"

#: pynicotine/gtkgui/chatrooms.py:605 pynicotine/gtkgui/chatrooms.py:617
#: pynicotine/gtkgui/chatrooms.py:806 pynicotine/gtkgui/chatrooms.py:809
#: pynicotine/gtkgui/privatechat.py:372 pynicotine/gtkgui/privatechat.py:440
#: pynicotine/gtkgui/search.py:622 pynicotine/gtkgui/transfers.py:288
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:190
msgid "Copy"
msgstr "Copier"

#: pynicotine/gtkgui/chatrooms.py:606 pynicotine/gtkgui/chatrooms.py:619
#: pynicotine/gtkgui/privatechat.py:374
msgid "Copy All"
msgstr "Copier tout"

#: pynicotine/gtkgui/chatrooms.py:608
msgid "Clear Activity View"
msgstr "Effacer la vue des activités"

#: pynicotine/gtkgui/chatrooms.py:610 pynicotine/gtkgui/chatrooms.py:630
#: pynicotine/gtkgui/chatrooms.py:635
msgid "_Leave Room"
msgstr "_Quitter le salon"

#: pynicotine/gtkgui/chatrooms.py:618 pynicotine/gtkgui/chatrooms.py:810
#: pynicotine/gtkgui/privatechat.py:373 pynicotine/gtkgui/privatechat.py:441
msgid "Copy Link"
msgstr "Copier l'URL"

#: pynicotine/gtkgui/chatrooms.py:624
msgid "View Room Log"
msgstr "Afficher les logs du salon"

#: pynicotine/gtkgui/chatrooms.py:627
msgid "Delete Room Log…"
msgstr "Supprimer le log du salon…"

#: pynicotine/gtkgui/chatrooms.py:629 pynicotine/gtkgui/privatechat.py:384
msgid "Clear Message View"
msgstr "Effacer les messages du salon"

#: pynicotine/gtkgui/chatrooms.py:832
#, python-format
msgid "%(user)s mentioned you in room %(room)s"
msgstr "%(user)s vous a mentionné dans le salon %(room)s"

#: pynicotine/gtkgui/chatrooms.py:837 pynicotine/gtkgui/mainwindow.py:425
#, python-format
msgid "Mentioned by %(user)s in Room %(room)s"
msgstr "Mentionné par %(user)s dans le salon %(room)s"

#: pynicotine/gtkgui/chatrooms.py:855
#, python-format
msgid "Message by %(user)s in Room %(room)s"
msgstr "Message de %(user)s dans le salon %(room)s"

#: pynicotine/gtkgui/chatrooms.py:913 pynicotine/gtkgui/chatrooms.py:1148
#, python-format
msgid "%s joined the room"
msgstr "%s a rejoint le salon"

#: pynicotine/gtkgui/chatrooms.py:937
#, python-format
msgid "%s left the room"
msgstr "%s a quitté le salon"

#: pynicotine/gtkgui/chatrooms.py:1095
#, python-format
msgid "%s has gone away"
msgstr "%s s'absente"

#: pynicotine/gtkgui/chatrooms.py:1098
#, python-format
msgid "%s has returned"
msgstr "%s est de retour"

#: pynicotine/gtkgui/chatrooms.py:1196 pynicotine/gtkgui/privatechat.py:476
msgid "Delete Logged Messages?"
msgstr "Supprimer les messages enregistrés ?"

#: pynicotine/gtkgui/chatrooms.py:1197
msgid ""
"Do you really want to permanently delete all logged messages for this room?"
msgstr ""
"Voulez-vous vraiment supprimer définitivement tous les messages enregistrés "
"de ce salon ?"

#: pynicotine/gtkgui/dialogs/about.py:405
msgid "About"
msgstr "À propos"

#: pynicotine/gtkgui/dialogs/about.py:415
msgid "Website"
msgstr "Site Web"

#: pynicotine/gtkgui/dialogs/about.py:457
#, python-format
msgid "Error checking latest version: %s"
msgstr "Erreur lors de la vérification de la dernière version : %s"

#: pynicotine/gtkgui/dialogs/about.py:462
#, python-format
msgid "New release available: %s"
msgstr "Nouvelle version disponible : %s"

#: pynicotine/gtkgui/dialogs/about.py:467
msgid "Up to date"
msgstr "À jour"

#: pynicotine/gtkgui/dialogs/about.py:496
msgid "Checking latest version…"
msgstr "Vérification d'une nouvelle version…"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:75
msgid "Setup Assistant"
msgstr "Assistant de configuration"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:100
#: pynicotine/gtkgui/dialogs/preferences.py:613
msgid "Virtual Folder"
msgstr "Répertoire virtuel"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:107
#: pynicotine/gtkgui/dialogs/preferences.py:620 pynicotine/gtkgui/search.py:539
#: pynicotine/gtkgui/uploads.py:48 pynicotine/gtkgui/userbrowse.py:266
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:121
msgid "Folder"
msgstr "Répertoire"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:133
msgid "_Previous"
msgstr "_Précédent"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
msgid "_Finish"
msgstr "_Terminer"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:134
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:136
msgid "_Next"
msgstr "_Suivant"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:180
#: pynicotine/gtkgui/dialogs/preferences.py:711
msgid "Add a Shared Folder"
msgstr "Ajouter un répertoire partagé"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:213
#: pynicotine/gtkgui/dialogs/preferences.py:753
msgid "Edit Shared Folder"
msgstr "Modifier les répertoires partagés"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:214
#: pynicotine/gtkgui/dialogs/preferences.py:754
#, python-format
msgid "Enter new virtual name for '%(dir)s':"
msgstr "Choisissez un nouveau nom virtuel pour '%(dir)s'  :"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:216
#: pynicotine/gtkgui/dialogs/pluginsettings.py:413
#: pynicotine/gtkgui/dialogs/preferences.py:500
#: pynicotine/gtkgui/dialogs/preferences.py:760
#: pynicotine/gtkgui/dialogs/preferences.py:1557
#: pynicotine/gtkgui/dialogs/preferences.py:1622
#: pynicotine/gtkgui/dialogs/preferences.py:2601
#: pynicotine/gtkgui/dialogs/wishlist.py:140
msgid "_Edit"
msgstr "_Modifier"

#: pynicotine/gtkgui/dialogs/fastconfigure.py:310
#, python-format
msgid ""
"User %s already exists, and the password you entered is invalid. Please "
"choose another username if this is your first time logging in."
msgstr ""
"L'utilisateur %s existe déjà, et le mot de passe que vous avez entré n'est "
"pas valide. Choisissez un autre nom d'utilisateur si c'est votre premier "
"essai."

#: pynicotine/gtkgui/dialogs/fileproperties.py:65
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:259
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:279
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:334
msgid "File Properties"
msgstr "Propriétés du fichier"

#: pynicotine/gtkgui/dialogs/fileproperties.py:76
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s  /  %(length)s)"
msgstr ""
"Propriété du fichier (%(num)i sur %(total)i  /  %(size)s  /  %(length)s)"

#: pynicotine/gtkgui/dialogs/fileproperties.py:82
#, python-format
msgid "File Properties (%(num)i of %(total)i  /  %(size)s)"
msgstr "Propriété du fichier (%(num)i sur %(total)i  /  %(size)s)"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:45
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:17
msgid "_Apply"
msgstr "_Appliquer"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:222
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:451
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:467
#: pynicotine/gtkgui/ui/settings/ban.ui:163
#: pynicotine/gtkgui/ui/settings/ban.ui:179
#: pynicotine/gtkgui/ui/settings/ban.ui:285
#: pynicotine/gtkgui/ui/settings/ban.ui:301
#: pynicotine/gtkgui/ui/settings/chats.ui:703
#: pynicotine/gtkgui/ui/settings/chats.ui:719
#: pynicotine/gtkgui/ui/settings/chats.ui:887
#: pynicotine/gtkgui/ui/settings/chats.ui:903
#: pynicotine/gtkgui/ui/settings/downloads.ui:464
#: pynicotine/gtkgui/ui/settings/ignore.ui:97
#: pynicotine/gtkgui/ui/settings/ignore.ui:113
#: pynicotine/gtkgui/ui/settings/ignore.ui:218
#: pynicotine/gtkgui/ui/settings/ignore.ui:234
#: pynicotine/gtkgui/ui/settings/shares.ui:127
#: pynicotine/gtkgui/ui/settings/shares.ui:143
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:76
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:92
#: pynicotine/gtkgui/ui/userinfo.ui:370
msgid "Add…"
msgstr "Ajouter…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:223
#: pynicotine/gtkgui/dialogs/wishlist.py:79 pynicotine/gtkgui/search.py:628
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:482
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:498
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:60
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:76
#: pynicotine/gtkgui/ui/settings/chats.ui:734
#: pynicotine/gtkgui/ui/settings/chats.ui:750
#: pynicotine/gtkgui/ui/settings/chats.ui:918
#: pynicotine/gtkgui/ui/settings/chats.ui:934
#: pynicotine/gtkgui/ui/settings/downloads.ui:479
#: pynicotine/gtkgui/ui/settings/downloads.ui:495
#: pynicotine/gtkgui/ui/settings/shares.ui:158
#: pynicotine/gtkgui/ui/settings/shares.ui:174
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:107
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:123
msgid "Edit…"
msgstr "Editer…"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:366
#, python-format
msgid "%s Settings"
msgstr "Paramètres de %s"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:383
msgid "Add Item"
msgstr "Ajouter un élément"

#: pynicotine/gtkgui/dialogs/pluginsettings.py:411
msgid "Edit Item"
msgstr "Modifier un élément"

#: pynicotine/gtkgui/dialogs/preferences.py:124
#: pynicotine/gtkgui/userinfo.py:631 pynicotine/gtkgui/userinfo.py:649
#: pynicotine/gtkgui/userinfo.py:783 pynicotine/gtkgui/widgets/treeview.py:687
#: pynicotine/users.py:310 pynicotine/gtkgui/ui/settings/network.ui:132
#: pynicotine/gtkgui/ui/userinfo.ui:160 pynicotine/gtkgui/ui/userinfo.ui:187
#: pynicotine/gtkgui/ui/userinfo.ui:214 pynicotine/gtkgui/ui/userinfo.ui:241
#: pynicotine/gtkgui/ui/userinfo.ui:268 pynicotine/gtkgui/ui/userinfo.ui:295
msgid "Unknown"
msgstr "Inconnu"

#: pynicotine/gtkgui/dialogs/preferences.py:128
#: pynicotine/gtkgui/ui/settings/network.ui:142
msgid "Check Port Status"
msgstr "Vérifier l'état du port"

#: pynicotine/gtkgui/dialogs/preferences.py:130
#, python-format
msgid "<b>%(ip)s</b>, port %(port)s"
msgstr "<b>%(ip)s</b>, port %(port)s"

#: pynicotine/gtkgui/dialogs/preferences.py:199
msgid "Password Change Rejected"
msgstr "Le changement de mot de passe est rejeté"

#: pynicotine/gtkgui/dialogs/preferences.py:218
msgid "Enter a new password for your Soulseek account:"
msgstr "Entrez un nouveau mot de passe pour votre compte Soulseek  :"

#: pynicotine/gtkgui/dialogs/preferences.py:220
msgid ""
"You are currently logged out of the Soulseek network. If you want to change "
"the password of an existing Soulseek account, you need to be logged into "
"that account."
msgstr ""
"Vous êtes actuellement déconnecté du réseau Soulseek. Si vous tentez de "
"modifier le mot de passe d'un compte Soulseek existant, vous devez être "
"connecté au compte en question."

#: pynicotine/gtkgui/dialogs/preferences.py:223
msgid "Enter password to use when logging in:"
msgstr "Entrez le mot de passe à utiliser lors de la connexion  :"

#: pynicotine/gtkgui/dialogs/preferences.py:227
#: pynicotine/gtkgui/ui/settings/network.ui:80
#: pynicotine/gtkgui/ui/settings/network.ui:96
msgid "Change Password"
msgstr "Modifier le mot de passe"

#: pynicotine/gtkgui/dialogs/preferences.py:230
msgid "_Change"
msgstr "_Changer"

#: pynicotine/gtkgui/dialogs/preferences.py:274
msgid "No one"
msgstr "Personne"

#: pynicotine/gtkgui/dialogs/preferences.py:275
msgid "Everyone"
msgstr "Tout le monde"

#: pynicotine/gtkgui/dialogs/preferences.py:276
#: pynicotine/gtkgui/dialogs/preferences.py:585
#: pynicotine/gtkgui/dialogs/preferences.py:661
#: pynicotine/gtkgui/mainwindow.py:759 pynicotine/gtkgui/search.py:276
#: pynicotine/gtkgui/ui/buddies.ui:18 pynicotine/gtkgui/ui/mainwindow.ui:1363
#: pynicotine/gtkgui/ui/settings/userinterface.ui:692
msgid "Buddies"
msgstr "Amis"

#: pynicotine/gtkgui/dialogs/preferences.py:277
#: pynicotine/gtkgui/dialogs/preferences.py:586
#: pynicotine/gtkgui/dialogs/preferences.py:720
#: pynicotine/gtkgui/dialogs/preferences.py:756
msgid "Trusted buddies"
msgstr "Amis de confiance"

#: pynicotine/gtkgui/dialogs/preferences.py:282
#: pynicotine/gtkgui/dialogs/preferences.py:806
msgid "Nothing"
msgstr "Rien"

#: pynicotine/gtkgui/dialogs/preferences.py:286
#: pynicotine/gtkgui/dialogs/preferences.py:810
msgid "Open File"
msgstr "Ouvrir le fichier"

#: pynicotine/gtkgui/dialogs/preferences.py:287
#: pynicotine/gtkgui/dialogs/preferences.py:811
#: pynicotine/gtkgui/widgets/filechooser.py:293
msgid "Open in File Manager"
msgstr "Ouvrir dans le gestionnaire de fichiers"

#: pynicotine/gtkgui/dialogs/preferences.py:290
#: pynicotine/gtkgui/dialogs/preferences.py:814
#: pynicotine/gtkgui/mainwindow.py:1108
msgid "Search"
msgstr "Recherche"

#: pynicotine/gtkgui/dialogs/preferences.py:291
#: pynicotine/gtkgui/ui/downloads.ui:86
msgid "Pause"
msgstr "Pause"

#: pynicotine/gtkgui/dialogs/preferences.py:293
#: pynicotine/gtkgui/ui/downloads.ui:56
msgid "Resume"
msgstr "Reprendre"

#: pynicotine/gtkgui/dialogs/preferences.py:294
#: pynicotine/gtkgui/dialogs/preferences.py:818
msgid "Browse Folder"
msgstr "Parcourir le dossier"

#: pynicotine/gtkgui/dialogs/preferences.py:317
msgid ""
"<b>Syntax</b>: Case-insensitive. If enabled, Python regular expressions can "
"be used, otherwise only wildcard * matches are supported."
msgstr ""
"<b>Syntaxe</b> : Insensible à la casse. Si activé, les expressions "
"régulières de Python peuvent être utilisées, sinon seuls les correspondances "
"génériques * sont pris en charge."

#: pynicotine/gtkgui/dialogs/preferences.py:327
msgid "Filter"
msgstr "Filtrer"

#: pynicotine/gtkgui/dialogs/preferences.py:334
msgid "Regex"
msgstr "Regex (Expression régulière)"

#: pynicotine/gtkgui/dialogs/preferences.py:468
msgid "Add Download Filter"
msgstr "Ajouter un filtre de téléchargement"

#: pynicotine/gtkgui/dialogs/preferences.py:469
msgid "Enter a new download filter:"
msgstr "Entrer un nouveau filtre de téléchargement  :"

#: pynicotine/gtkgui/dialogs/preferences.py:473
#: pynicotine/gtkgui/dialogs/preferences.py:505
msgid "Enable regular expressions"
msgstr "Activer les expressions régulières"

#: pynicotine/gtkgui/dialogs/preferences.py:498
msgid "Edit Download Filter"
msgstr "Éditer un filtre de téléchargement"

#: pynicotine/gtkgui/dialogs/preferences.py:499
msgid "Modify the following download filter:"
msgstr "Modifiez le filtre de téléchargement suivant  :"

#: pynicotine/gtkgui/dialogs/preferences.py:571
#, python-format
msgid "%(num)d Failed! %(error)s "
msgstr "%(num)d a échoué  ! %(error)s "

#: pynicotine/gtkgui/dialogs/preferences.py:578
msgid "Filters Successful"
msgstr "Filtres réussis"

#: pynicotine/gtkgui/dialogs/preferences.py:584
#: pynicotine/gtkgui/dialogs/preferences.py:657
#: pynicotine/gtkgui/dialogs/preferences.py:693
msgid "Public"
msgstr "Public"

#: pynicotine/gtkgui/dialogs/preferences.py:626
msgid "Accessible To"
msgstr "Accessible à"

#: pynicotine/gtkgui/dialogs/preferences.py:815
#: pynicotine/gtkgui/ui/uploads.ui:56
msgid "Abort"
msgstr "Abandonner"

#: pynicotine/gtkgui/dialogs/preferences.py:817
#: pynicotine/gtkgui/ui/userbrowse.ui:5 pynicotine/gtkgui/ui/userinfo.ui:5
msgid "Retry"
msgstr "Réessayer"

#: pynicotine/gtkgui/dialogs/preferences.py:828
msgid "Round Robin"
msgstr "Aléatoire"

#: pynicotine/gtkgui/dialogs/preferences.py:829
msgid "First In, First Out"
msgstr "Premier arrivé, premier servi"

#: pynicotine/gtkgui/dialogs/preferences.py:978
#: pynicotine/gtkgui/dialogs/preferences.py:1150
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:239
msgid "Username"
msgstr "Utilisateur"

#: pynicotine/gtkgui/dialogs/preferences.py:991
#: pynicotine/gtkgui/dialogs/preferences.py:1163 pynicotine/users.py:320
msgid "IP Address"
msgstr "Adresse IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1057
#: pynicotine/gtkgui/userinfo.py:585 pynicotine/gtkgui/widgets/popupmenu.py:449
#: pynicotine/gtkgui/widgets/popupmenu.py:495
msgid "Ignore User"
msgstr "Ignorer l'utilisateur"

#: pynicotine/gtkgui/dialogs/preferences.py:1058
msgid "Enter the name of the user you want to ignore:"
msgstr "Saisir le nom d'un utilisateur que vous souhaitez ignorer  :"

#: pynicotine/gtkgui/dialogs/preferences.py:1098
#: pynicotine/gtkgui/widgets/popupmenu.py:452
#: pynicotine/gtkgui/widgets/popupmenu.py:497
msgid "Ignore IP Address"
msgstr "Ignorer l'adresse IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
msgid "Enter an IP address you want to ignore:"
msgstr "Saisir une adresse IP que vous souhaitez ignorer  :"

#: pynicotine/gtkgui/dialogs/preferences.py:1099
#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "* is a wildcard"
msgstr "* implique tout le monde"

#: pynicotine/gtkgui/dialogs/preferences.py:1248
#: pynicotine/gtkgui/userinfo.py:581 pynicotine/gtkgui/widgets/popupmenu.py:448
#: pynicotine/gtkgui/widgets/popupmenu.py:494
msgid "Ban User"
msgstr "Bannir l'utilisateur"

#: pynicotine/gtkgui/dialogs/preferences.py:1249
msgid "Enter the name of the user you want to ban:"
msgstr "Saisir le nom d'un utilisateur que vous souhaitez bannir  :"

#: pynicotine/gtkgui/dialogs/preferences.py:1289
#: pynicotine/gtkgui/widgets/popupmenu.py:451
#: pynicotine/gtkgui/widgets/popupmenu.py:496
msgid "Ban IP Address"
msgstr "Bannir l'adresse IP"

#: pynicotine/gtkgui/dialogs/preferences.py:1290
msgid "Enter an IP address you want to ban:"
msgstr "Saisir une adresse IP que vous souhaitez bannir :"

#: pynicotine/gtkgui/dialogs/preferences.py:1348
#: pynicotine/gtkgui/dialogs/preferences.py:2205
msgid "Format codes"
msgstr "Formatage de variables"

#: pynicotine/gtkgui/dialogs/preferences.py:1370
#: pynicotine/gtkgui/dialogs/preferences.py:1384
msgid "Pattern"
msgstr "Motif"

#: pynicotine/gtkgui/dialogs/preferences.py:1391
msgid "Replacement"
msgstr "Remplacement"

#: pynicotine/gtkgui/dialogs/preferences.py:1524
msgid "Censor Pattern"
msgstr "Motif de censure"

#: pynicotine/gtkgui/dialogs/preferences.py:1525
#: pynicotine/gtkgui/dialogs/preferences.py:1555
msgid ""
"Enter a pattern you want to censor. Add spaces around the pattern if you "
"don't want to match strings inside words (may fail at the beginning and end "
"of lines)."
msgstr ""
"Saisissez le motif que vous souhaitez censurer. Ajoutez des espaces autour "
"du motif si vous ne souhaitez pas faire correspondre les chaînes de "
"caractères à l'intérieur des mots (cela peut échouer au début et à la fin "
"des lignes)."

#: pynicotine/gtkgui/dialogs/preferences.py:1554
msgid "Edit Censored Pattern"
msgstr "Modifier le motif de censure"

#: pynicotine/gtkgui/dialogs/preferences.py:1588
msgid "Add Replacement"
msgstr "Ajouter un remplacement"

#: pynicotine/gtkgui/dialogs/preferences.py:1589
#: pynicotine/gtkgui/dialogs/preferences.py:1621
msgid "Enter a text pattern and what to replace it with:"
msgstr "Entrez le modèle de texte et son remplacement :"

#: pynicotine/gtkgui/dialogs/preferences.py:1620
msgid "Edit Replacement"
msgstr "Modifier le remplacement"

#: pynicotine/gtkgui/dialogs/preferences.py:1736
msgid "System default"
msgstr "Par défaut"

#: pynicotine/gtkgui/dialogs/preferences.py:1750
msgid "Show confirmation dialog"
msgstr "Afficher la boîte de dialogue de confirmation"

#: pynicotine/gtkgui/dialogs/preferences.py:1751
msgid "Run in the background"
msgstr "Exécuter en tâche de fond"

#: pynicotine/gtkgui/dialogs/preferences.py:1759
msgid "bold"
msgstr "gras"

#: pynicotine/gtkgui/dialogs/preferences.py:1760
msgid "italic"
msgstr "italique"

#: pynicotine/gtkgui/dialogs/preferences.py:1761
msgid "underline"
msgstr "souligné"

#: pynicotine/gtkgui/dialogs/preferences.py:1762
msgid "normal"
msgstr "normal"

#: pynicotine/gtkgui/dialogs/preferences.py:1770
msgid "Separate Buddies tab"
msgstr "Séparer l'onglet \"Amis\""

#: pynicotine/gtkgui/dialogs/preferences.py:1771
msgid "Sidebar in Chat Rooms tab"
msgstr "Barre latérale dans l'onglet \"Salon de Discussion\""

#: pynicotine/gtkgui/dialogs/preferences.py:1772
msgid "Always visible sidebar"
msgstr "Barre latérale toujours visible"

#: pynicotine/gtkgui/dialogs/preferences.py:1777
msgid "Top"
msgstr "Haut"

#: pynicotine/gtkgui/dialogs/preferences.py:1778
msgid "Bottom"
msgstr "Bas"

#: pynicotine/gtkgui/dialogs/preferences.py:1779
msgid "Left"
msgstr "Gauche"

#: pynicotine/gtkgui/dialogs/preferences.py:1780
msgid "Right"
msgstr "Droite"

#: pynicotine/gtkgui/dialogs/preferences.py:1913
#: pynicotine/gtkgui/mainwindow.py:990
#: pynicotine/gtkgui/widgets/iconnotebook.py:613
#: pynicotine/gtkgui/widgets/theme.py:253
msgid "Online"
msgstr "Présent"

#: pynicotine/gtkgui/dialogs/preferences.py:1914
#: pynicotine/gtkgui/mainwindow.py:987
#: pynicotine/gtkgui/widgets/iconnotebook.py:610
#: pynicotine/gtkgui/widgets/theme.py:254
#: pynicotine/gtkgui/widgets/trayicon.py:100
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:33
msgid "Away"
msgstr "Absent"

#: pynicotine/gtkgui/dialogs/preferences.py:1915
#: pynicotine/gtkgui/mainwindow.py:994
#: pynicotine/gtkgui/widgets/iconnotebook.py:616
#: pynicotine/gtkgui/widgets/theme.py:255
#: pynicotine/gtkgui/ui/mainwindow.ui:1843
msgid "Offline"
msgstr "Hors ligne"

#: pynicotine/gtkgui/dialogs/preferences.py:1917
msgid "Tab Changed"
msgstr "Onglet modifié"

#: pynicotine/gtkgui/dialogs/preferences.py:1918
msgid "Tab Highlight"
msgstr "Mise en évidence de l'onglet"

#: pynicotine/gtkgui/dialogs/preferences.py:1919
msgid "Window"
msgstr "Fenêtre"

#: pynicotine/gtkgui/dialogs/preferences.py:1923
msgid "Online (Tray)"
msgstr "Connecté (zone de notification)"

#: pynicotine/gtkgui/dialogs/preferences.py:1924
msgid "Away (Tray)"
msgstr "Absent (zone de notification)"

#: pynicotine/gtkgui/dialogs/preferences.py:1925
msgid "Offline (Tray)"
msgstr "Hors ligne (zone de notification)"

#: pynicotine/gtkgui/dialogs/preferences.py:1926
msgid "Message (Tray)"
msgstr "Message (zone de notification)"

#: pynicotine/gtkgui/dialogs/preferences.py:2495
msgid "Protocol"
msgstr "Protocole"

#: pynicotine/gtkgui/dialogs/preferences.py:2503
msgid "Command"
msgstr "Commande"

#: pynicotine/gtkgui/dialogs/preferences.py:2570
msgid "Add URL Handler"
msgstr "Ajouter un gestionnaire d'URL"

#: pynicotine/gtkgui/dialogs/preferences.py:2571
msgid "Enter the protocol and the command for the URL handler:"
msgstr ""
"Entrez le protocole et la commande pour le gestionnaire d'URL, "
"respectivement :"

#: pynicotine/gtkgui/dialogs/preferences.py:2599
msgid "Edit Command"
msgstr "Éditer les commandes"

#: pynicotine/gtkgui/dialogs/preferences.py:2600
#, python-format
msgid "Enter a new command for protocol %s:"
msgstr "Choisissez une nouvelle commande pour le protocole %s  :"

#: pynicotine/gtkgui/dialogs/preferences.py:2755
msgid "Username;APIKEY"
msgstr "Nom d'utilisateur;APIKEY"

#: pynicotine/gtkgui/dialogs/preferences.py:2759
msgid ""
"Music player (e.g. amarok, audacious, exaile); leave empty to autodetect:"
msgstr ""
"Lecteur de musique (par exemple amarok, audacious, exaile) ou laisser-le "
"vide pour une sélection automatique  :"

#: pynicotine/gtkgui/dialogs/preferences.py:2763
#: pynicotine/headless/application.py:104
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:233
#: pynicotine/gtkgui/ui/settings/network.ui:54
msgid "Username: "
msgstr "Nom d’utilisateur : "

#: pynicotine/gtkgui/dialogs/preferences.py:2767
msgid "Command:"
msgstr "Commande :"

#: pynicotine/gtkgui/dialogs/preferences.py:2775
#: pynicotine/gtkgui/dialogs/preferences.py:2778
msgid "Title"
msgstr "Titre"

#: pynicotine/gtkgui/dialogs/preferences.py:2777
#, python-format
msgid "Now Playing (typically \"%(artist)s - %(title)s\")"
msgstr "Joué actuellement (typiquement \"%(artist)s - %(title)s\")"

#: pynicotine/gtkgui/dialogs/preferences.py:2778
#: pynicotine/gtkgui/dialogs/preferences.py:2786
msgid "Artist"
msgstr "Artiste"

#: pynicotine/gtkgui/dialogs/preferences.py:2780
#: pynicotine/gtkgui/search.py:576 pynicotine/gtkgui/userbrowse.py:354
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:179
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:323
msgid "Duration"
msgstr "Durée"

#: pynicotine/gtkgui/dialogs/preferences.py:2782
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:274
msgid "Bitrate"
msgstr "bitrate"

#: pynicotine/gtkgui/dialogs/preferences.py:2784
msgid "Comment"
msgstr "Commentaires"

#: pynicotine/gtkgui/dialogs/preferences.py:2788
msgid "Album"
msgstr "Album"

#: pynicotine/gtkgui/dialogs/preferences.py:2790
msgid "Track Number"
msgstr "Numéro de piste"

#: pynicotine/gtkgui/dialogs/preferences.py:2792
msgid "Year"
msgstr "Année"

#: pynicotine/gtkgui/dialogs/preferences.py:2794
msgid "Filename (URI)"
msgstr "Nom de fichier (URI)"

#: pynicotine/gtkgui/dialogs/preferences.py:2796
msgid "Program"
msgstr "Programme"

#: pynicotine/gtkgui/dialogs/preferences.py:2859
msgid "Enabled"
msgstr "Activé"

#: pynicotine/gtkgui/dialogs/preferences.py:2866
msgid "Plugin"
msgstr "Extension"

#: pynicotine/gtkgui/dialogs/preferences.py:2919
msgid "No Plugin Selected"
msgstr "Aucune extension sélectionnée"

#: pynicotine/gtkgui/dialogs/preferences.py:3016
#: pynicotine/gtkgui/widgets/trayicon.py:106
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:61
msgid "Preferences"
msgstr "Préférences"

#: pynicotine/gtkgui/dialogs/preferences.py:3030
#: pynicotine/gtkgui/ui/settings/network.ui:27
msgid "Network"
msgstr "Réseau"

#: pynicotine/gtkgui/dialogs/preferences.py:3031
#: pynicotine/gtkgui/ui/settings/userinterface.ui:31
msgid "User Interface"
msgstr "Interface utilisateur"

#: pynicotine/gtkgui/dialogs/preferences.py:3032
#: pynicotine/plugins/core_commands/__init__.py:30
#: pynicotine/gtkgui/ui/settings/shares.ui:11
msgid "Shares"
msgstr "Partages"

#: pynicotine/gtkgui/dialogs/preferences.py:3034
#: pynicotine/gtkgui/mainwindow.py:755 pynicotine/gtkgui/mainwindow.py:1107
#: pynicotine/gtkgui/widgets/trayicon.py:90
#: pynicotine/gtkgui/ui/mainwindow.ui:699
#: pynicotine/gtkgui/ui/settings/uploads.ui:47
#: pynicotine/gtkgui/ui/settings/userinterface.ui:644
msgid "Uploads"
msgstr "Envois"

#: pynicotine/gtkgui/dialogs/preferences.py:3035
#: pynicotine/gtkgui/widgets/trayicon.py:96
#: pynicotine/gtkgui/ui/settings/search.ui:33
msgid "Searches"
msgstr "Recherches"

#: pynicotine/gtkgui/dialogs/preferences.py:3036
msgid "User Profile"
msgstr "Profil de l’utilisateur"

#: pynicotine/gtkgui/dialogs/preferences.py:3037
#: pynicotine/gtkgui/ui/settings/chats.ui:32
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1033
msgid "Chats"
msgstr "Discussions"

#: pynicotine/gtkgui/dialogs/preferences.py:3038
#: pynicotine/gtkgui/ui/settings/nowplaying.ui:16
msgid "Now Playing"
msgstr "En cours de lecture"

#: pynicotine/gtkgui/dialogs/preferences.py:3039
#: pynicotine/gtkgui/ui/settings/log.ui:76
msgid "Logging"
msgstr "Enregistrement"

#: pynicotine/gtkgui/dialogs/preferences.py:3040
#: pynicotine/gtkgui/ui/settings/ban.ui:16
msgid "Banned Users"
msgstr "Utilisateurs bannis"

#: pynicotine/gtkgui/dialogs/preferences.py:3041
#: pynicotine/gtkgui/ui/settings/ignore.ui:16
msgid "Ignored Users"
msgstr "Utilisateurs ignorés"

#: pynicotine/gtkgui/dialogs/preferences.py:3042
#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:11
msgid "URL Handlers"
msgstr "Gestionnaires d'URL"

#: pynicotine/gtkgui/dialogs/preferences.py:3043
#: pynicotine/gtkgui/ui/settings/plugin.ui:29
msgid "Plugins"
msgstr "Extensions"

#: pynicotine/gtkgui/dialogs/preferences.py:3433
msgid "Pick a File Name for Config Backup"
msgstr "Sélectionnez un nom de fichier pour la sauvegarde de la configuration"

#: pynicotine/gtkgui/dialogs/statistics.py:75
msgid "Transfer Statistics"
msgstr "Statistiques de transfert"

#: pynicotine/gtkgui/dialogs/statistics.py:97
#, python-format
msgid "Total Since %(date)s"
msgstr "Total depuis %(date)s"

#: pynicotine/gtkgui/dialogs/statistics.py:123
msgid "Reset Transfer Statistics?"
msgstr "Réinitialiser les statistiques de transfert ?"

#: pynicotine/gtkgui/dialogs/statistics.py:124
msgid "Do you really want to reset transfer statistics?"
msgstr "Souhaitez-vous vraiment réinitialiser les statistiques de transfert ?"

#: pynicotine/gtkgui/dialogs/wishlist.py:46
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:341
msgid "Wishlist"
msgstr "Liste de souhaits"

#: pynicotine/gtkgui/dialogs/wishlist.py:59 pynicotine/gtkgui/search.py:356
msgid "Wish"
msgstr "Souhait"

#: pynicotine/gtkgui/dialogs/wishlist.py:78 pynicotine/gtkgui/interests.py:186
#: pynicotine/gtkgui/interests.py:195 pynicotine/gtkgui/interests.py:207
#: pynicotine/gtkgui/userinfo.py:363
msgid "_Search for Item"
msgstr "_Recherche de l'article"

#: pynicotine/gtkgui/dialogs/wishlist.py:137
msgid "Edit Wish"
msgstr "Modifier le souhait"

#: pynicotine/gtkgui/dialogs/wishlist.py:138
#, python-format
msgid "Enter new value for wish '%s':"
msgstr "Choisissez une nouvelle valeur pour le souhait '%s'  :"

#: pynicotine/gtkgui/dialogs/wishlist.py:173
msgid "Clear Wishlist?"
msgstr "Effacer la liste de souhaits ?"

#: pynicotine/gtkgui/dialogs/wishlist.py:174
msgid "Do you really want to clear your wishlist?"
msgstr "Voulez-vous vraiment effacer votre liste de souhaits ?"

#: pynicotine/gtkgui/downloads.py:46
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:150
msgid "Path"
msgstr "Chemin"

#: pynicotine/gtkgui/downloads.py:47
msgid "_Resume"
msgstr "_Reprendre"

#: pynicotine/gtkgui/downloads.py:48
msgid "P_ause"
msgstr "P_ause"

#: pynicotine/gtkgui/downloads.py:72
msgid "Finished / Filtered"
msgstr "Terminé / Filtré"

#: pynicotine/gtkgui/downloads.py:74 pynicotine/gtkgui/transfers.py:71
#: pynicotine/gtkgui/uploads.py:77
msgid "Finished"
msgstr "Terminé"

#: pynicotine/gtkgui/downloads.py:75 pynicotine/gtkgui/transfers.py:69
msgid "Paused"
msgstr "En pause"

#: pynicotine/gtkgui/downloads.py:76 pynicotine/gtkgui/transfers.py:72
msgid "Filtered"
msgstr "Filtré"

#: pynicotine/gtkgui/downloads.py:77
msgid "Deleted"
msgstr "Supprimé"

#: pynicotine/gtkgui/downloads.py:78 pynicotine/gtkgui/uploads.py:81
msgid "Queued…"
msgstr "En attente…"

#: pynicotine/gtkgui/downloads.py:80 pynicotine/gtkgui/uploads.py:83
msgid "Everything…"
msgstr "Tout…"

#: pynicotine/gtkgui/downloads.py:132
#, python-format
msgid "Downloads: %(speed)s"
msgstr "Téléchargements : %(speed)s"

#: pynicotine/gtkgui/downloads.py:138
msgid "Clear Queued Downloads"
msgstr "Effacer les téléchargements en attente"

#: pynicotine/gtkgui/downloads.py:139
msgid "Do you really want to clear all queued downloads?"
msgstr ""
"Voulez-vous vraiment effacer tous les téléchargements en file d'attente ?"

#: pynicotine/gtkgui/downloads.py:151
msgid "Clear All Downloads"
msgstr "Effacer tous les téléchargements"

#: pynicotine/gtkgui/downloads.py:152
msgid "Do you really want to clear all downloads?"
msgstr "Voulez-vous vraiment effacer tous les téléchargements ?"

#: pynicotine/gtkgui/downloads.py:169
#, python-format
msgid "Download %(num)i files?"
msgstr "Télécharger %(num)i fichiers ?"

#: pynicotine/gtkgui/downloads.py:170
#, python-format
msgid ""
"Do you really want to download %(num)i files from %(user)s's folder "
"%(folder)s?"
msgstr ""
"Voulez-vous vraiment télécharger %(num)i fichiers du dossier %(folder)s de "
"l'utilisateur %(user)s ?"

#: pynicotine/gtkgui/downloads.py:174 pynicotine/gtkgui/userbrowse.py:395
msgid "_Download Folder"
msgstr "_Télécharger le répertoire"

#: pynicotine/gtkgui/interests.py:79 pynicotine/gtkgui/userinfo.py:328
msgid "Likes"
msgstr "Il aime"

#: pynicotine/gtkgui/interests.py:91 pynicotine/gtkgui/userinfo.py:341
msgid "Dislikes"
msgstr "N'aime pas"

#: pynicotine/gtkgui/interests.py:104
msgid "Rating"
msgstr "Classement"

#: pynicotine/gtkgui/interests.py:111
msgid "Item"
msgstr "Élément"

#: pynicotine/gtkgui/interests.py:185 pynicotine/gtkgui/interests.py:194
#: pynicotine/gtkgui/interests.py:206 pynicotine/gtkgui/userinfo.py:362
msgid "_Recommendations for Item"
msgstr "_Recommandations pour l'article"

#: pynicotine/gtkgui/interests.py:203 pynicotine/gtkgui/interests.py:551
#: pynicotine/gtkgui/userinfo.py:359
msgid "I _Like This"
msgstr "J'_aime ceci"

#: pynicotine/gtkgui/interests.py:204 pynicotine/gtkgui/interests.py:554
#: pynicotine/gtkgui/userinfo.py:360
msgid "I _Dislike This"
msgstr "Je n'aime _pas ceci"

#: pynicotine/gtkgui/interests.py:286 pynicotine/gtkgui/interests.py:429
#: pynicotine/gtkgui/ui/interests.ui:131
msgid "Recommendations"
msgstr "Recommandations"

#: pynicotine/gtkgui/interests.py:287 pynicotine/gtkgui/interests.py:453
#: pynicotine/gtkgui/ui/interests.ui:191
msgid "Similar Users"
msgstr "Utilisateurs similaires"

#: pynicotine/gtkgui/interests.py:427
#, python-format
msgid "Recommendations (%s)"
msgstr "Recommandations (%s)"

#: pynicotine/gtkgui/interests.py:451
#, python-format
msgid "Similar Users (%s)"
msgstr "Utilisateurs similaires (%s)"

#: pynicotine/gtkgui/mainwindow.py:238
msgid "Search log…"
msgstr "Recherche dans les journalisations…"

#: pynicotine/gtkgui/mainwindow.py:419 pynicotine/gtkgui/privatechat.py:499
#, python-format
msgid "Private Message from %(user)s"
msgstr "Message privé de %(user)s"

#: pynicotine/gtkgui/mainwindow.py:429 pynicotine/gtkgui/search.py:926
msgid "Wishlist Results Found"
msgstr "Résultats trouvés depuis la liste de souhaits"

#: pynicotine/gtkgui/mainwindow.py:757 pynicotine/gtkgui/ui/mainwindow.ui:1034
#: pynicotine/gtkgui/ui/settings/userinterface.ui:668
#: pynicotine/gtkgui/ui/settings/userinterface.ui:850
msgid "User Profiles"
msgstr "Profils d'utilisateurs"

#: pynicotine/gtkgui/mainwindow.py:760 pynicotine/gtkgui/widgets/trayicon.py:95
#: pynicotine/plugins/core_commands/__init__.py:26
#: pynicotine/gtkgui/ui/mainwindow.ui:1528
#: pynicotine/gtkgui/ui/settings/userinterface.ui:705
#: pynicotine/gtkgui/ui/settings/userinterface.ui:894
msgid "Chat Rooms"
msgstr "Salons de discussions"

#: pynicotine/gtkgui/mainwindow.py:761 pynicotine/gtkgui/ui/mainwindow.ui:1584
#: pynicotine/gtkgui/ui/settings/userinterface.ui:717
#: pynicotine/gtkgui/ui/userinfo.ui:340
msgid "Interests"
msgstr "Centres d'intérêts"

#: pynicotine/gtkgui/mainwindow.py:1109
#: pynicotine/plugins/core_commands/__init__.py:25
msgid "Chat"
msgstr "Discussion"

#: pynicotine/gtkgui/mainwindow.py:1111
msgid "[Debug] Connections"
msgstr "[Débogage] Connexions"

#: pynicotine/gtkgui/mainwindow.py:1112
msgid "[Debug] Messages"
msgstr "[Débogage] Messages"

#: pynicotine/gtkgui/mainwindow.py:1113
msgid "[Debug] Transfers"
msgstr "[Débogage] Transferts"

#: pynicotine/gtkgui/mainwindow.py:1114
msgid "[Debug] Miscellaneous"
msgstr "[Débogage] Divers"

#: pynicotine/gtkgui/mainwindow.py:1119
msgid "_Find…"
msgstr "_Trouver…"

#: pynicotine/gtkgui/mainwindow.py:1121 pynicotine/gtkgui/mainwindow.py:1152
msgid "_Copy"
msgstr "_Copier"

#: pynicotine/gtkgui/mainwindow.py:1122
msgid "Copy _All"
msgstr "Copier _tout"

#: pynicotine/gtkgui/mainwindow.py:1127
msgid "View _Debug Logs"
msgstr "Afficher les journaux de _déboguage"

#: pynicotine/gtkgui/mainwindow.py:1128
msgid "View _Transfer Logs"
msgstr "Afficher les journaux de _transfert"

#: pynicotine/gtkgui/mainwindow.py:1132
msgid "_Log Categories"
msgstr "Catégories des _journaux"

#: pynicotine/gtkgui/mainwindow.py:1134
msgid "Clear Log View"
msgstr "Effacer la vue des journaux"

#: pynicotine/gtkgui/mainwindow.py:1199
msgid "Preparing Shares"
msgstr "Préparation des partages"

#: pynicotine/gtkgui/mainwindow.py:1210
msgid "Scanning Shares"
msgstr "Examen des partages"

#: pynicotine/gtkgui/mainwindow.py:1215 pynicotine/gtkgui/ui/userinfo.ui:176
msgid "Shared Folders"
msgstr "Répertoires partagés"

#: pynicotine/gtkgui/popovers/chathistory.py:77
msgid "Latest Message"
msgstr "Dernier message"

#: pynicotine/gtkgui/popovers/roomlist.py:66
msgid "Room"
msgstr "Salon"

#: pynicotine/gtkgui/popovers/roomlist.py:74
#: pynicotine/plugins/core_commands/__init__.py:31
#: pynicotine/gtkgui/ui/chatrooms.ui:164 pynicotine/gtkgui/ui/mainwindow.ui:298
#: pynicotine/gtkgui/ui/mainwindow.ui:537
#: pynicotine/gtkgui/ui/settings/ban.ui:124
#: pynicotine/gtkgui/ui/settings/ignore.ui:59
msgid "Users"
msgstr "Utilisateurs"

#: pynicotine/gtkgui/popovers/roomlist.py:90
#: pynicotine/gtkgui/popovers/roomlist.py:275
msgid "Join Room"
msgstr "Rejoindre un salon"

#: pynicotine/gtkgui/popovers/roomlist.py:91
#: pynicotine/gtkgui/popovers/roomlist.py:276
msgid "Leave Room"
msgstr "Quitter un salon"

#: pynicotine/gtkgui/popovers/roomlist.py:93
#: pynicotine/gtkgui/popovers/roomlist.py:278
msgid "Disown Private Room"
msgstr "Rendre public le salon privé"

#: pynicotine/gtkgui/popovers/roomlist.py:94
#: pynicotine/gtkgui/popovers/roomlist.py:279
msgid "Cancel Room Membership"
msgstr "Annuler l'adhésion au salon"

#: pynicotine/gtkgui/privatechat.py:364 pynicotine/gtkgui/search.py:632
#: pynicotine/gtkgui/userbrowse.py:283 pynicotine/gtkgui/userinfo.py:353
#: pynicotine/gtkgui/widgets/iconnotebook.py:738
msgid "Close All Tabs…"
msgstr "Fermer tous les onglets …"

#: pynicotine/gtkgui/privatechat.py:365 pynicotine/gtkgui/search.py:633
#: pynicotine/gtkgui/userbrowse.py:284 pynicotine/gtkgui/userinfo.py:354
msgid "_Close Tab"
msgstr "_Fermer l'onglet"

#: pynicotine/gtkgui/privatechat.py:379
msgid "View Chat Log"
msgstr "Afficher le journal des discussions"

#: pynicotine/gtkgui/privatechat.py:382
msgid "Delete Chat Log…"
msgstr "Supprimer le journal de discussion…"

#: pynicotine/gtkgui/privatechat.py:386 pynicotine/gtkgui/search.py:623
#: pynicotine/gtkgui/transfers.py:290 pynicotine/gtkgui/userbrowse.py:305
#: pynicotine/gtkgui/userbrowse.py:317 pynicotine/gtkgui/userbrowse.py:388
#: pynicotine/gtkgui/userbrowse.py:403
msgid "User Actions"
msgstr "Actions de l'utilisateur"

#: pynicotine/gtkgui/privatechat.py:477
msgid ""
"Do you really want to permanently delete all logged messages for this user?"
msgstr ""
"Êtes-vous sûr de vouloir effacer définitivement tous les messages "
"enregistrés de cet utilisateur ?"

#: pynicotine/gtkgui/privatechat.py:528
msgid "* Messages sent while you were offline"
msgstr "* Les message(s) envoyé(s) quand vous êtes hors-ligne"

#: pynicotine/gtkgui/search.py:90
msgid "_Global"
msgstr "_Global"

#: pynicotine/gtkgui/search.py:91
msgid "_Buddies"
msgstr "_Amis"

#: pynicotine/gtkgui/search.py:92 pynicotine/gtkgui/ui/mainwindow.ui:1446
msgid "_Rooms"
msgstr "_Salons"

#: pynicotine/gtkgui/search.py:93
msgid "_User"
msgstr "_Utilisateur"

#: pynicotine/gtkgui/search.py:532
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:270
msgid "In Queue"
msgstr "En liste d'attente"

#: pynicotine/gtkgui/search.py:547 pynicotine/gtkgui/transfers.py:161
#: pynicotine/gtkgui/userbrowse.py:328
#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:139
msgid "File Type"
msgstr "Type de fichier"

#: pynicotine/gtkgui/search.py:554 pynicotine/gtkgui/transfers.py:168
msgid "Filename"
msgstr "Nom de fichier"

#: pynicotine/gtkgui/search.py:562 pynicotine/gtkgui/transfers.py:194
#: pynicotine/gtkgui/userbrowse.py:342
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:92
msgid "Size"
msgstr "Taille"

#: pynicotine/gtkgui/search.py:569 pynicotine/gtkgui/userbrowse.py:348
#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:210
msgid "Quality"
msgstr "Qualité"

#: pynicotine/gtkgui/search.py:603 pynicotine/gtkgui/transfers.py:264
#: pynicotine/gtkgui/userbrowse.py:385 pynicotine/gtkgui/userbrowse.py:400
msgid "Copy _File Path"
msgstr "Copier le chemin du _fichier"

#: pynicotine/gtkgui/search.py:604 pynicotine/gtkgui/transfers.py:265
#: pynicotine/gtkgui/userbrowse.py:386 pynicotine/gtkgui/userbrowse.py:401
msgid "Copy _URL"
msgstr "Copier l'_URL"

#: pynicotine/gtkgui/search.py:605 pynicotine/gtkgui/transfers.py:266
#: pynicotine/gtkgui/userbrowse.py:303 pynicotine/gtkgui/userbrowse.py:315
msgid "Copy Folder U_RL"
msgstr "Copier l'U_RL du répertoire"

#: pynicotine/gtkgui/search.py:612 pynicotine/gtkgui/userbrowse.py:392
msgid "_Download File(s)"
msgstr "_Télécharger le(s) fichier(s)"

#: pynicotine/gtkgui/search.py:613 pynicotine/gtkgui/userbrowse.py:393
msgid "Download File(s) _To…"
msgstr "Télécharger le(s) fichier(s) _vers…"

#: pynicotine/gtkgui/search.py:615
msgid "Download _Folder(s)"
msgstr "Télécharger le(s) _répertoire(s)"

#: pynicotine/gtkgui/search.py:616
msgid "Download F_older(s) To…"
msgstr "Télécharger le(s) répertoire(s) v_ers…"

#: pynicotine/gtkgui/search.py:618 pynicotine/gtkgui/transfers.py:284
#: pynicotine/gtkgui/widgets/popupmenu.py:435
msgid "View User _Profile"
msgstr "Voir Profil _Utilisateur"

#: pynicotine/gtkgui/search.py:619 pynicotine/gtkgui/transfers.py:285
msgid "_Browse Folder"
msgstr "_Parcourir le dossier"

#: pynicotine/gtkgui/search.py:620 pynicotine/gtkgui/transfers.py:278
#: pynicotine/gtkgui/userbrowse.py:300 pynicotine/gtkgui/userbrowse.py:312
#: pynicotine/gtkgui/userbrowse.py:383 pynicotine/gtkgui/userbrowse.py:398
msgid "F_ile Properties"
msgstr "Propriétés de F_ile"

#: pynicotine/gtkgui/search.py:629
msgid "Copy Search Term"
msgstr "Copier le terme de recherche"

#: pynicotine/gtkgui/search.py:631
msgid "Clear All Results"
msgstr "Effacer tous les résultats"

#: pynicotine/gtkgui/search.py:718
msgid "Clear Filters"
msgstr "Effacer les filtres"

#: pynicotine/gtkgui/search.py:721
msgid "Restore Filters"
msgstr "Rétablir les filtres"

#: pynicotine/gtkgui/search.py:839 pynicotine/gtkgui/userbrowse.py:514
#, python-format
msgid "[PRIVATE]  %s"
msgstr "[PRIVÉ] %s"

#: pynicotine/gtkgui/search.py:1273
#, python-format
msgid "_Result Filters [%d]"
msgstr "Filtres de résultat [%d]"

#: pynicotine/gtkgui/search.py:1275 pynicotine/gtkgui/ui/search.ui:181
msgid "_Result Filters"
msgstr "Filtres de _Résultat"

#: pynicotine/gtkgui/search.py:1277
#, python-format
msgid "%d active filter(s)"
msgstr "%d filtre(s) actif(s)"

#: pynicotine/gtkgui/search.py:1329
msgid "Add Wi_sh"
msgstr "Ajouter _Souhait"

#: pynicotine/gtkgui/search.py:1332
msgid "Remove Wi_sh"
msgstr "Supprimer _souhait"

#: pynicotine/gtkgui/search.py:1349
msgid "Select User's Results"
msgstr "Sélectionner les résultats de l'utilisateur"

#: pynicotine/gtkgui/search.py:1472
#, python-format
msgid "Total: %s"
msgstr "Total : %s"

#: pynicotine/gtkgui/search.py:1475 pynicotine/gtkgui/ui/search.ui:105
msgid "Results"
msgstr "Résultats"

#: pynicotine/gtkgui/search.py:1590
msgid "Select Destination Folder for File(s)"
msgstr "Sélectionnez la dossier de destination pour le(s) fichier(s)"

#: pynicotine/gtkgui/search.py:1633 pynicotine/gtkgui/userbrowse.py:955
msgid "Select Destination Folder"
msgstr "Sélectionnez le dossier de destination"

#: pynicotine/gtkgui/transfers.py:61
msgid "Queued"
msgstr "En attente"

#: pynicotine/gtkgui/transfers.py:62
msgid "Queued (prioritized)"
msgstr "En attente (priorisé)"

#: pynicotine/gtkgui/transfers.py:63
msgid "Queued (privileged)"
msgstr "En attente (privilégié)"

#: pynicotine/gtkgui/transfers.py:64
msgid "Getting status"
msgstr "Recherche le statut"

#: pynicotine/gtkgui/transfers.py:65
msgid "Transferring"
msgstr "Transfert en cours"

#: pynicotine/gtkgui/transfers.py:66
msgid "Connection closed"
msgstr "Connexion fermée"

#: pynicotine/gtkgui/transfers.py:67
msgid "Connection timeout"
msgstr "Délai de connexion dépassé"

#: pynicotine/gtkgui/transfers.py:68 pynicotine/gtkgui/transfers.py:673
msgid "User logged off"
msgstr "Utilisateur déconnecté"

#: pynicotine/gtkgui/transfers.py:70 pynicotine/gtkgui/uploads.py:78
msgid "Cancelled"
msgstr "Annulé"

#: pynicotine/gtkgui/transfers.py:73
msgid "Download folder error"
msgstr "Erreur du téléchargement d'un répertoire"

#: pynicotine/gtkgui/transfers.py:74
msgid "Local file error"
msgstr "Erreur du fichier local"

#: pynicotine/gtkgui/transfers.py:75
msgid "Banned"
msgstr "Bannis"

#: pynicotine/gtkgui/transfers.py:76
msgid "File not shared"
msgstr "Fichier non partagé"

#: pynicotine/gtkgui/transfers.py:77
msgid "Pending shutdown"
msgstr "Arrêt en cours"

#: pynicotine/gtkgui/transfers.py:78
msgid "File read error"
msgstr "Erreur de lecture de fichier"

#: pynicotine/gtkgui/transfers.py:182
msgid "Queue"
msgstr "File d'attente"

#: pynicotine/gtkgui/transfers.py:188
msgid "Percent"
msgstr "Pourcentage"

#: pynicotine/gtkgui/transfers.py:208
msgid "Time Elapsed"
msgstr "Temps écoulé"

#: pynicotine/gtkgui/transfers.py:215
msgid "Time Left"
msgstr "Temps restant"

#: pynicotine/gtkgui/transfers.py:274 pynicotine/gtkgui/userbrowse.py:379
msgid "_Open File"
msgstr "_Ouvrir le fichier"

#: pynicotine/gtkgui/transfers.py:275 pynicotine/gtkgui/userbrowse.py:297
#: pynicotine/gtkgui/userbrowse.py:380
msgid "Open in File _Manager"
msgstr "Ouvrir dans le _gestionnaire de fichiers"

#: pynicotine/gtkgui/transfers.py:286
msgid "_Search"
msgstr "_Rechercher"

#: pynicotine/gtkgui/transfers.py:289
msgid "Clear All"
msgstr "Tout effacer"

#: pynicotine/gtkgui/transfers.py:953
msgid "Select User's Transfers"
msgstr "Sélectionner les transferts de cet utilisateur"

#: pynicotine/gtkgui/uploads.py:50
msgid "_Abort"
msgstr "_Interrompre"

#: pynicotine/gtkgui/uploads.py:74
msgid "Finished / Cancelled / Failed"
msgstr "Terminés / annulés / échoués"

#: pynicotine/gtkgui/uploads.py:75
msgid "Finished / Cancelled"
msgstr "Terminés / Annulés"

#: pynicotine/gtkgui/uploads.py:79
msgid "Failed"
msgstr "Échoué"

#: pynicotine/gtkgui/uploads.py:80
msgid "User Logged Off"
msgstr "Utilisateur déconnecté"

#: pynicotine/gtkgui/uploads.py:142
#, python-format
msgid "Uploads: %(speed)s"
msgstr "Envois : %(speed)s"

#: pynicotine/gtkgui/uploads.py:155
msgid "Quitting..."
msgstr "Arrêt en cours..."

#: pynicotine/gtkgui/uploads.py:164
msgid "Clear Queued Uploads"
msgstr "Effacer les envois en attente"

#: pynicotine/gtkgui/uploads.py:165
msgid "Do you really want to clear all queued uploads?"
msgstr "Êtes-vous sûr de vouloir effacer tous les envois en attente ?"

#: pynicotine/gtkgui/uploads.py:177
msgid "Clear All Uploads"
msgstr "Effacer tous les envois"

#: pynicotine/gtkgui/uploads.py:178
msgid "Do you really want to clear all uploads?"
msgstr "Êtes-vous sûr de vouloir effacer tous les envois ?"

#: pynicotine/gtkgui/userbrowse.py:282
msgid "_Save Shares List to Disk"
msgstr "_Sauvegarder la liste des partages sur le disque"

#: pynicotine/gtkgui/userbrowse.py:292
msgid "Upload Folder & Subfolders…"
msgstr "Envoyer le dossier et ses sous-dossiers…"

#: pynicotine/gtkgui/userbrowse.py:302 pynicotine/gtkgui/userbrowse.py:314
msgid "Copy _Folder Path"
msgstr "Copier le chemin du _répertoire"

#: pynicotine/gtkgui/userbrowse.py:309
msgid "_Download Folder & Subfolders"
msgstr "_Télécharger le dossier et ses sous-dossiers"

#: pynicotine/gtkgui/userbrowse.py:310
msgid "Download Folder & Subfolders _To…"
msgstr "Télécharger le dossier et ses sous-dossiers _Dans…"

#: pynicotine/gtkgui/userbrowse.py:334
msgid "File Name"
msgstr "Nom du fichier"

#: pynicotine/gtkgui/userbrowse.py:373
msgid "Up_load File(s)…"
msgstr "_Envoyer le(s) fichier(s)…"

#: pynicotine/gtkgui/userbrowse.py:374
msgid "Upload Folder…"
msgstr "Envoyer le dossier…"

#: pynicotine/gtkgui/userbrowse.py:396
msgid "Download Folder _To…"
msgstr "Télécharger le répertoire _vers…"

#: pynicotine/gtkgui/userbrowse.py:600
msgid ""
"User's list of shared files is empty. Either the user is not sharing "
"anything, or they are sharing files privately."
msgstr ""
"La liste des fichiers partagés par l'utilisateur est vide. L'utilisateur ne "
"partage aucun contenu, ou alors ses partages sont privés."

#: pynicotine/gtkgui/userbrowse.py:615
msgid ""
"Unable to request shared files from user. Either the user is offline, the "
"listening ports are closed on both sides, or there is a temporary "
"connectivity issue."
msgstr ""
"Impossible d'obtenir la liste des fichiers partagés par l'utilisateur. Soit "
"l'utilisateur n'est pas connecté, soit les ports écoutés sont fermés des "
"deux côtés ou alors un problème réseau est survenu."

#: pynicotine/gtkgui/userbrowse.py:953
msgid "Select Destination for Downloading Multiple Folders"
msgstr "Sélectionnez la destination pour télécharger plusieurs dossiers"

#: pynicotine/gtkgui/userbrowse.py:997
msgid "Upload Folder (with Subfolders) To User"
msgstr "Envoyer le dossier (avec les sous-dossiers) vers l'utilisateur"

#: pynicotine/gtkgui/userbrowse.py:999
msgid "Upload Folder To User"
msgstr "Envoyer le répertoire à l'utilisateur"

#: pynicotine/gtkgui/userbrowse.py:1004 pynicotine/gtkgui/userbrowse.py:1162
msgid "Enter the name of the user you want to upload to:"
msgstr ""
"Saisir le nom de l'utilisateur à qui vous souhaitez envoyer des fichiers :"

#: pynicotine/gtkgui/userbrowse.py:1005 pynicotine/gtkgui/userbrowse.py:1163
msgid "_Upload"
msgstr "_Envois"

#: pynicotine/gtkgui/userbrowse.py:1139
msgid "Select Destination Folder for Files"
msgstr "Sélectionnez le dossier de destination pour le(s) fichier(s)"

#: pynicotine/gtkgui/userbrowse.py:1161
msgid "Upload File(s) To User"
msgstr "Envoyer le(s) fichier(s) à l'utilisateur"

#: pynicotine/gtkgui/userinfo.py:376
msgid "Copy Picture"
msgstr "Copier l'image"

#: pynicotine/gtkgui/userinfo.py:377
msgid "Save Picture"
msgstr "Enregistrer l'image"

#: pynicotine/gtkgui/userinfo.py:468
#, python-format
msgid "Failed to load picture for user %(user)s: %(error)s"
msgstr "Échec du chargement de l'image de l'utilisateur %(user)s : %(error)s"

#: pynicotine/gtkgui/userinfo.py:505
msgid ""
"Unable to request information from user. Either you both have a closed "
"listening port, the user is offline, or there's a temporary connectivity "
"issue."
msgstr ""
"Échec d'obtention d'informations sur l'utilisateur. Soit vous avez l'un et "
"l'autre un port d'écoute fermé, soit l'utilisateur est hors ligne, soit il y "
"a un problème de connexion temporaire."

#: pynicotine/gtkgui/userinfo.py:577
msgid "Remove _Buddy"
msgstr "Supprimer _l'ami"

#: pynicotine/gtkgui/userinfo.py:577
msgid "Add _Buddy"
msgstr "Ajouter un ami"

#: pynicotine/gtkgui/userinfo.py:581
msgid "Unban User"
msgstr "Débannir l'utilisateur"

#: pynicotine/gtkgui/userinfo.py:585
msgid "Unignore User"
msgstr "Ne plus ignorer l'utilisateur"

#: pynicotine/gtkgui/userinfo.py:613
msgid "Yes"
msgstr "Oui"

#: pynicotine/gtkgui/userinfo.py:613
msgid "No"
msgstr "Non"

#: pynicotine/gtkgui/userinfo.py:773
msgid "Please enter number of days."
msgstr "Veuillez saisir le nombre de jours."

#: pynicotine/gtkgui/userinfo.py:787
#, python-format
msgid "Gift days of your Soulseek privileges to user %(user)s (%(days_left)s):"
msgstr ""
"Offrez des jours de vos privilèges Soulseek à l'utilisateur %(user)s "
"(%(days_left)s) :"

#: pynicotine/gtkgui/userinfo.py:788
#, python-format
msgid "%(days)s days left"
msgstr "%(days)s jours restants"

#: pynicotine/gtkgui/userinfo.py:795
msgid "Gift Privileges"
msgstr "Donner des privilèges"

#: pynicotine/gtkgui/userinfo.py:797
msgid "_Give Privileges"
msgstr "_Donner des privilèges"

#: pynicotine/gtkgui/widgets/dialogs.py:309
msgid "Close"
msgstr "Fermer"

#: pynicotine/gtkgui/widgets/dialogs.py:488
msgid "_Yes"
msgstr "_Oui"

#: pynicotine/gtkgui/widgets/dialogs.py:522
#: pynicotine/gtkgui/ui/dialogs/preferences.ui:23
msgid "_OK"
msgstr "_OK"

#: pynicotine/gtkgui/widgets/filechooser.py:39
msgid "Select a File"
msgstr "Sélectionnez un fichier"

#: pynicotine/gtkgui/widgets/filechooser.py:174
msgid "Select a Folder"
msgstr "Sélectionnez un répertoire"

#: pynicotine/gtkgui/widgets/filechooser.py:179
msgid "_Select"
msgstr "_Sélectionner"

#: pynicotine/gtkgui/widgets/filechooser.py:196
msgid "Select an Image"
msgstr "Sélectionnez une image"

#: pynicotine/gtkgui/widgets/filechooser.py:203
msgid "All images"
msgstr "Toutes les images"

#: pynicotine/gtkgui/widgets/filechooser.py:241
msgid "Save as…"
msgstr "Enregistrer sous…"

#: pynicotine/gtkgui/widgets/filechooser.py:289
#: pynicotine/gtkgui/widgets/filechooser.py:407
msgid "(None)"
msgstr "(aucun)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:119
msgid "Close Tab"
msgstr "Fermer l'onglet"

#: pynicotine/gtkgui/widgets/iconnotebook.py:455
msgid "Close All Tabs?"
msgstr "Fermer tous les onglets ?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:456
msgid "Do you really want to close all tabs?"
msgstr "Voulez-vous vraiment fermer tous les onglets ?"

#: pynicotine/gtkgui/widgets/iconnotebook.py:480
#, python-format
msgid "%i Unread Tab(s)"
msgstr "%i Onglet(s) non lu(s)"

#: pynicotine/gtkgui/widgets/iconnotebook.py:483
msgid "All Tabs"
msgstr "Tous les onglets"

#: pynicotine/gtkgui/widgets/iconnotebook.py:737
#: pynicotine/gtkgui/widgets/iconnotebook.py:742
msgid "Re_open Closed Tab"
msgstr "_Rouvrir l'onglet fermé"

#: pynicotine/gtkgui/widgets/popupmenu.py:404
#, python-format
msgid "%s File(s) Selected"
msgstr "%s fichier(s) sélectionné(s)"

#: pynicotine/gtkgui/widgets/popupmenu.py:441
#: pynicotine/gtkgui/ui/userinfo.ui:497
msgid "_Browse Files"
msgstr "_Parcourir les fichiers"

#: pynicotine/gtkgui/widgets/popupmenu.py:444
#: pynicotine/gtkgui/widgets/popupmenu.py:488
msgid "_Add Buddy"
msgstr "Ajouter un ami"

#: pynicotine/gtkgui/widgets/popupmenu.py:453
msgid "Show IP A_ddress"
msgstr "Afficher l'a_dresse IP"

#: pynicotine/gtkgui/widgets/popupmenu.py:455
#: pynicotine/gtkgui/widgets/popupmenu.py:506
msgid "Private Rooms"
msgstr "Salons privés"

#: pynicotine/gtkgui/widgets/popupmenu.py:529
#, python-format
msgid "Remove from Private Room %s"
msgstr "Se retirer du salon privé %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:532
#, python-format
msgid "Add to Private Room %s"
msgstr "Ajouter au salon privé %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:539
#, python-format
msgid "Remove as Operator of %s"
msgstr "Supprimer comme opérateur de %s"

#: pynicotine/gtkgui/widgets/popupmenu.py:543
#, python-format
msgid "Add as Operator of %s"
msgstr "Ajouter comme opérateur de %s"

#: pynicotine/gtkgui/widgets/textentry.py:37
msgid "Send message…"
msgstr "Envoyer un message…"

#: pynicotine/gtkgui/widgets/textentry.py:520
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:232
msgid "Find Previous Match"
msgstr "Recherche l'occurrence précédente"

#: pynicotine/gtkgui/widgets/textentry.py:521
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:225
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:293
msgid "Find Next Match"
msgstr "Recherche l'occurrence suivante"

#: pynicotine/gtkgui/widgets/textview.py:443
msgid "--- old messages above ---"
msgstr "--- anciens messages au-dessus ---"

#: pynicotine/gtkgui/widgets/theme.py:243
msgid "Executable"
msgstr "Fichier exécutable"

#: pynicotine/gtkgui/widgets/theme.py:244
msgid "Audio"
msgstr "Audio"

#: pynicotine/gtkgui/widgets/theme.py:245
msgid "Image"
msgstr "Image"

#: pynicotine/gtkgui/widgets/theme.py:246
msgid "Archive"
msgstr "Archive"

#: pynicotine/gtkgui/widgets/theme.py:247 pynicotine/pluginsystem.py:811
msgid "Miscellaneous"
msgstr "Divers"

#: pynicotine/gtkgui/widgets/theme.py:248
msgid "Video"
msgstr "Vidéo"

#: pynicotine/gtkgui/widgets/theme.py:249
msgid "Document"
msgstr "Document"

#: pynicotine/gtkgui/widgets/theme.py:250
msgid "Text"
msgstr "Texte"

#: pynicotine/gtkgui/widgets/theme.py:357
#, python-format
msgid "Error loading custom icon %(path)s: %(error)s"
msgstr ""
"Erreur lors du chargement de l'icône personnalisée %(path)s : %(error)s"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Hide Nicotine+"
msgstr "Cacher Nicotine+"

#: pynicotine/gtkgui/widgets/trayicon.py:114
msgid "Show Nicotine+"
msgstr "Afficher Nicotine+"

#: pynicotine/gtkgui/widgets/treeview.py:778
#, python-format
msgid "Column #%i"
msgstr "Colonne #%i"

#: pynicotine/gtkgui/widgets/treeview.py:957
msgid "Ungrouped"
msgstr "Non groupé"

#: pynicotine/gtkgui/widgets/treeview.py:960
msgid "Group by Folder"
msgstr "Grouper par répertoire"

#: pynicotine/gtkgui/widgets/treeview.py:963
msgid "Group by User"
msgstr "Grouper par utilisateur"

#: pynicotine/headless/application.py:77
#, python-format
msgid "Do you really want to exit? %s"
msgstr "Voulez-vous vraiment quitter ? %s"

#: pynicotine/headless/application.py:81
#, python-format
msgid "User %s already exists, and the password you entered is invalid."
msgstr ""
"L'utilisateur %s existe déjà, et le mot de passe que vous avez entré n'est "
"pas valide."

#: pynicotine/headless/application.py:83
#, python-format
msgid "Type %s to log in with another username or password."
msgstr ""
"Tapez %s pour vous connecter avec un autre nom d'utilisateur ou mot de passe."

#: pynicotine/headless/application.py:99
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:268
msgid "Password: "
msgstr "Mot de passe : "

#: pynicotine/headless/application.py:102
#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:185
msgid ""
"To create a new Soulseek account, fill in your desired username and "
"password. If you already have an account, fill in your existing login "
"details."
msgstr ""
"Pour créer un nouveau compte Soulseek, saisissez votre nom d'utilisateur et "
"votre mot de passe. Si vous avez déjà un compte, remplissez vos données de "
"connexion existantes."

#: pynicotine/headless/application.py:119
msgid "The following shares are unavailable:"
msgstr "Les partages suivants ne sont pas disponibles :"

#: pynicotine/headless/application.py:125
#, python-format
msgid "Retry rescan? %s"
msgstr "réexamen ? %s"

#: pynicotine/logfacility.py:181
#, python-format
msgid "Couldn't write to log file \"%(filename)s\": %(error)s"
msgstr ""
"Impossible d'écrire dans le fichier journal \"%(filename)s\" : %(error)s"

#: pynicotine/logfacility.py:267 pynicotine/logfacility.py:292
#, python-format
msgid "Cannot access log file %(path)s: %(error)s"
msgstr "Impossible d'accéder au fichier de journal %(path)s : %(error)s"

#: pynicotine/networkfilter.py:40
msgid "Andorra"
msgstr "Andorre"

#: pynicotine/networkfilter.py:41
msgid "United Arab Emirates"
msgstr "Émirats arabes unis"

#: pynicotine/networkfilter.py:42
msgid "Afghanistan"
msgstr "Afghanistan"

#: pynicotine/networkfilter.py:43
msgid "Antigua & Barbuda"
msgstr "Antigua-et-Barbuda"

#: pynicotine/networkfilter.py:44
msgid "Anguilla"
msgstr "Anguilla"

#: pynicotine/networkfilter.py:45
msgid "Albania"
msgstr "Albanie"

#: pynicotine/networkfilter.py:46
msgid "Armenia"
msgstr "Arménie"

#: pynicotine/networkfilter.py:47
msgid "Angola"
msgstr "Angola"

#: pynicotine/networkfilter.py:48
msgid "Antarctica"
msgstr "Antarctique"

#: pynicotine/networkfilter.py:49
msgid "Argentina"
msgstr "Argentine"

#: pynicotine/networkfilter.py:50
msgid "American Samoa"
msgstr "Samoa américaines"

#: pynicotine/networkfilter.py:51
msgid "Austria"
msgstr "Autriche"

#: pynicotine/networkfilter.py:52
msgid "Australia"
msgstr "Australie"

#: pynicotine/networkfilter.py:53
msgid "Aruba"
msgstr "Aruba"

#: pynicotine/networkfilter.py:54
msgid "Åland Islands"
msgstr "Åland"

#: pynicotine/networkfilter.py:55
msgid "Azerbaijan"
msgstr "Azerbaïdjan"

#: pynicotine/networkfilter.py:56
msgid "Bosnia & Herzegovina"
msgstr "Bosnie-Herzégovine"

#: pynicotine/networkfilter.py:57
msgid "Barbados"
msgstr "Barbade"

#: pynicotine/networkfilter.py:58
msgid "Bangladesh"
msgstr "Bangladesh"

#: pynicotine/networkfilter.py:59
msgid "Belgium"
msgstr "Belgique"

#: pynicotine/networkfilter.py:60
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: pynicotine/networkfilter.py:61
msgid "Bulgaria"
msgstr "Bulgarie"

#: pynicotine/networkfilter.py:62
msgid "Bahrain"
msgstr "Bahreïn"

#: pynicotine/networkfilter.py:63
msgid "Burundi"
msgstr "Burundi"

#: pynicotine/networkfilter.py:64
msgid "Benin"
msgstr "Bénin"

#: pynicotine/networkfilter.py:65
msgid "Saint Barthelemy"
msgstr "Saint-Barthélemy"

#: pynicotine/networkfilter.py:66
msgid "Bermuda"
msgstr "Bermudes"

#: pynicotine/networkfilter.py:67
msgid "Brunei Darussalam"
msgstr "Brunei"

#: pynicotine/networkfilter.py:68
msgid "Bolivia"
msgstr "Bolivie"

#: pynicotine/networkfilter.py:69
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Saint-Eustache et Saba"

#: pynicotine/networkfilter.py:70
msgid "Brazil"
msgstr "Brésil"

#: pynicotine/networkfilter.py:71
msgid "Bahamas"
msgstr "Bahamas"

#: pynicotine/networkfilter.py:72
msgid "Bhutan"
msgstr "Bhoutan"

#: pynicotine/networkfilter.py:73
msgid "Bouvet Island"
msgstr "Île Bouvet"

#: pynicotine/networkfilter.py:74
msgid "Botswana"
msgstr "Botswana"

#: pynicotine/networkfilter.py:75
msgid "Belarus"
msgstr "Biélorussie"

#: pynicotine/networkfilter.py:76
msgid "Belize"
msgstr "Belize"

#: pynicotine/networkfilter.py:77
msgid "Canada"
msgstr "Canada"

#: pynicotine/networkfilter.py:78
msgid "Cocos (Keeling) Islands"
msgstr "Îles Cocos"

#: pynicotine/networkfilter.py:79
msgid "Democratic Republic of Congo"
msgstr "République démocratique du Congo"

#: pynicotine/networkfilter.py:80
msgid "Central African Republic"
msgstr "République centrafricaine"

#: pynicotine/networkfilter.py:81
msgid "Congo"
msgstr "Congo"

#: pynicotine/networkfilter.py:82
msgid "Switzerland"
msgstr "Suisse"

#: pynicotine/networkfilter.py:83
msgid "Ivory Coast"
msgstr "Côte d'Ivoire"

#: pynicotine/networkfilter.py:84
msgid "Cook Islands"
msgstr "Îles Cook"

#: pynicotine/networkfilter.py:85
msgid "Chile"
msgstr "Chili"

#: pynicotine/networkfilter.py:86
msgid "Cameroon"
msgstr "Cameroun"

#: pynicotine/networkfilter.py:87
msgid "China"
msgstr "Chine"

#: pynicotine/networkfilter.py:88
msgid "Colombia"
msgstr "Colombie"

#: pynicotine/networkfilter.py:89
msgid "Costa Rica"
msgstr "Costa Rica"

#: pynicotine/networkfilter.py:90
msgid "Cuba"
msgstr "Cuba"

#: pynicotine/networkfilter.py:91
msgid "Cabo Verde"
msgstr "Cap-Vert"

#: pynicotine/networkfilter.py:92
msgid "Curaçao"
msgstr "Curaçao"

#: pynicotine/networkfilter.py:93
msgid "Christmas Island"
msgstr "Île Christmas"

#: pynicotine/networkfilter.py:94
msgid "Cyprus"
msgstr "Chypre"

#: pynicotine/networkfilter.py:95
msgid "Czechia"
msgstr "Tchéquie"

#: pynicotine/networkfilter.py:96
msgid "Germany"
msgstr "Allemagne"

#: pynicotine/networkfilter.py:97
msgid "Djibouti"
msgstr "Djibouti"

#: pynicotine/networkfilter.py:98
msgid "Denmark"
msgstr "Danemark"

#: pynicotine/networkfilter.py:99
msgid "Dominica"
msgstr "Dominique"

#: pynicotine/networkfilter.py:100
msgid "Dominican Republic"
msgstr "République dominicaine"

#: pynicotine/networkfilter.py:101
msgid "Algeria"
msgstr "Algérie"

#: pynicotine/networkfilter.py:102
msgid "Ecuador"
msgstr "Équateur"

#: pynicotine/networkfilter.py:103
msgid "Estonia"
msgstr "Estonie"

#: pynicotine/networkfilter.py:104
msgid "Egypt"
msgstr "Égypte"

#: pynicotine/networkfilter.py:105
msgid "Western Sahara"
msgstr "Sahara occidental"

#: pynicotine/networkfilter.py:106
msgid "Eritrea"
msgstr "Érythrée"

#: pynicotine/networkfilter.py:107
msgid "Spain"
msgstr "Espagne"

#: pynicotine/networkfilter.py:108
msgid "Ethiopia"
msgstr "Éthiopie"

#: pynicotine/networkfilter.py:109
msgid "Europe"
msgstr "Europe"

#: pynicotine/networkfilter.py:110
msgid "Finland"
msgstr "Finlande"

#: pynicotine/networkfilter.py:111
msgid "Fiji"
msgstr "Fidji"

#: pynicotine/networkfilter.py:112
msgid "Falkland Islands (Malvinas)"
msgstr "Îles Malouines"

#: pynicotine/networkfilter.py:113
msgid "Micronesia"
msgstr "États fédérés de Micronésie"

#: pynicotine/networkfilter.py:114
msgid "Faroe Islands"
msgstr "Îles Féroé"

#: pynicotine/networkfilter.py:115
msgid "France"
msgstr "France"

#: pynicotine/networkfilter.py:116
msgid "Gabon"
msgstr "Gabon"

#: pynicotine/networkfilter.py:117
msgid "Great Britain"
msgstr "Grande-Bretagne"

#: pynicotine/networkfilter.py:118
msgid "Grenada"
msgstr "Grenade"

#: pynicotine/networkfilter.py:119
msgid "Georgia"
msgstr "Géorgie"

#: pynicotine/networkfilter.py:120
msgid "French Guiana"
msgstr "Guyane"

#: pynicotine/networkfilter.py:121
msgid "Guernsey"
msgstr "Guernsey"

#: pynicotine/networkfilter.py:122
msgid "Ghana"
msgstr "Ghana"

#: pynicotine/networkfilter.py:123
msgid "Gibraltar"
msgstr "Gibraltar"

#: pynicotine/networkfilter.py:124
msgid "Greenland"
msgstr "Groenland"

#: pynicotine/networkfilter.py:125
msgid "Gambia"
msgstr "Gambie"

#: pynicotine/networkfilter.py:126
msgid "Guinea"
msgstr "Guinée"

#: pynicotine/networkfilter.py:127
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: pynicotine/networkfilter.py:128
msgid "Equatorial Guinea"
msgstr "Guinée équatoriale"

#: pynicotine/networkfilter.py:129
msgid "Greece"
msgstr "Grèce"

#: pynicotine/networkfilter.py:130
msgid "South Georgia & South Sandwich Islands"
msgstr "Géorgie du Sud-et-les îles Sandwich du Sud"

#: pynicotine/networkfilter.py:131
msgid "Guatemala"
msgstr "Guatemala"

#: pynicotine/networkfilter.py:132
msgid "Guam"
msgstr "Guam"

#: pynicotine/networkfilter.py:133
msgid "Guinea-Bissau"
msgstr "Guinée-Bissau"

#: pynicotine/networkfilter.py:134
msgid "Guyana"
msgstr "Guyana"

#: pynicotine/networkfilter.py:135
msgid "Hong Kong"
msgstr "Hong Kong"

#: pynicotine/networkfilter.py:136
msgid "Heard & McDonald Islands"
msgstr "Îles Heard-et-MacDonald"

#: pynicotine/networkfilter.py:137
msgid "Honduras"
msgstr "Honduras"

#: pynicotine/networkfilter.py:138
msgid "Croatia"
msgstr "Croatie"

#: pynicotine/networkfilter.py:139
msgid "Haiti"
msgstr "Haïti"

#: pynicotine/networkfilter.py:140
msgid "Hungary"
msgstr "Hongrie"

#: pynicotine/networkfilter.py:141
msgid "Indonesia"
msgstr "Indonésie"

#: pynicotine/networkfilter.py:142
msgid "Ireland"
msgstr "Irlande"

#: pynicotine/networkfilter.py:143
msgid "Israel"
msgstr "Israël"

#: pynicotine/networkfilter.py:144
msgid "Isle of Man"
msgstr "Île de Man"

#: pynicotine/networkfilter.py:145
msgid "India"
msgstr "Inde"

#: pynicotine/networkfilter.py:146
msgid "British Indian Ocean Territory"
msgstr "Territoire britannique de l'océan Indien"

#: pynicotine/networkfilter.py:147
msgid "Iraq"
msgstr "Irak"

#: pynicotine/networkfilter.py:148
msgid "Iran"
msgstr "Iran"

#: pynicotine/networkfilter.py:149
msgid "Iceland"
msgstr "Islande"

#: pynicotine/networkfilter.py:150
msgid "Italy"
msgstr "Italie"

#: pynicotine/networkfilter.py:151
msgid "Jersey"
msgstr "Jersey"

#: pynicotine/networkfilter.py:152
msgid "Jamaica"
msgstr "Jamaïque"

#: pynicotine/networkfilter.py:153
msgid "Jordan"
msgstr "Jordanie"

#: pynicotine/networkfilter.py:154
msgid "Japan"
msgstr "Japon"

#: pynicotine/networkfilter.py:155
msgid "Kenya"
msgstr "Kenya"

#: pynicotine/networkfilter.py:156
msgid "Kyrgyzstan"
msgstr "Kirghizistan"

#: pynicotine/networkfilter.py:157
msgid "Cambodia"
msgstr "Cambodge"

#: pynicotine/networkfilter.py:158
msgid "Kiribati"
msgstr "Kiribati"

#: pynicotine/networkfilter.py:159
msgid "Comoros"
msgstr "Comores"

#: pynicotine/networkfilter.py:160
msgid "Saint Kitts & Nevis"
msgstr "Saint-Christophe-et-Niévès"

#: pynicotine/networkfilter.py:161
msgid "North Korea"
msgstr "Corée du Nord"

#: pynicotine/networkfilter.py:162
msgid "South Korea"
msgstr "Corée du Sud"

#: pynicotine/networkfilter.py:163
msgid "Kuwait"
msgstr "Koweït"

#: pynicotine/networkfilter.py:164
msgid "Cayman Islands"
msgstr "Îles Caïmans"

#: pynicotine/networkfilter.py:165
msgid "Kazakhstan"
msgstr "Kazakhstan"

#: pynicotine/networkfilter.py:166
msgid "Laos"
msgstr "Laos"

#: pynicotine/networkfilter.py:167
msgid "Lebanon"
msgstr "Liban"

#: pynicotine/networkfilter.py:168
msgid "Saint Lucia"
msgstr "Sainte-Lucie"

#: pynicotine/networkfilter.py:169
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: pynicotine/networkfilter.py:170
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: pynicotine/networkfilter.py:171
msgid "Liberia"
msgstr "Liberia"

#: pynicotine/networkfilter.py:172
msgid "Lesotho"
msgstr "Lesotho"

#: pynicotine/networkfilter.py:173
msgid "Lithuania"
msgstr "Lituanie"

#: pynicotine/networkfilter.py:174
msgid "Luxembourg"
msgstr "Luxembourg"

#: pynicotine/networkfilter.py:175
msgid "Latvia"
msgstr "Lettonie"

#: pynicotine/networkfilter.py:176
msgid "Libya"
msgstr "Libye"

#: pynicotine/networkfilter.py:177
msgid "Morocco"
msgstr "Maroc"

#: pynicotine/networkfilter.py:178
msgid "Monaco"
msgstr "Monaco"

#: pynicotine/networkfilter.py:179
msgid "Moldova"
msgstr "Moldavie"

#: pynicotine/networkfilter.py:180
msgid "Montenegro"
msgstr "Montenegro"

#: pynicotine/networkfilter.py:181
msgid "Saint Martin"
msgstr "Saint-Martin (partie française)"

#: pynicotine/networkfilter.py:182
msgid "Madagascar"
msgstr "Madagascar"

#: pynicotine/networkfilter.py:183
msgid "Marshall Islands"
msgstr "Îles Marshall"

#: pynicotine/networkfilter.py:184
msgid "North Macedonia"
msgstr "Macédoine du Nord"

#: pynicotine/networkfilter.py:185
msgid "Mali"
msgstr "Mali"

#: pynicotine/networkfilter.py:186
msgid "Myanmar"
msgstr "Birmanie"

#: pynicotine/networkfilter.py:187
msgid "Mongolia"
msgstr "Mongolie"

#: pynicotine/networkfilter.py:188
msgid "Macau"
msgstr "Macao"

#: pynicotine/networkfilter.py:189
msgid "Northern Mariana Islands"
msgstr "Îles Mariannes du Nord"

#: pynicotine/networkfilter.py:190
msgid "Martinique"
msgstr "Martinique"

#: pynicotine/networkfilter.py:191
msgid "Mauritania"
msgstr "Mauritanie"

#: pynicotine/networkfilter.py:192
msgid "Montserrat"
msgstr "Montserrat"

#: pynicotine/networkfilter.py:193
msgid "Malta"
msgstr "Malte"

#: pynicotine/networkfilter.py:194
msgid "Mauritius"
msgstr "Île Maurice"

#: pynicotine/networkfilter.py:195
msgid "Maldives"
msgstr "Maldives"

#: pynicotine/networkfilter.py:196
msgid "Malawi"
msgstr "Malawi"

#: pynicotine/networkfilter.py:197
msgid "Mexico"
msgstr "Mexique"

#: pynicotine/networkfilter.py:198
msgid "Malaysia"
msgstr "Malaisie"

#: pynicotine/networkfilter.py:199
msgid "Mozambique"
msgstr "Mozambique"

#: pynicotine/networkfilter.py:200
msgid "Namibia"
msgstr "Namibie"

#: pynicotine/networkfilter.py:201
msgid "New Caledonia"
msgstr "Nouvelle-Calédonie"

#: pynicotine/networkfilter.py:202
msgid "Niger"
msgstr "Niger"

#: pynicotine/networkfilter.py:203
msgid "Norfolk Island"
msgstr "Île Norfolk"

#: pynicotine/networkfilter.py:204
msgid "Nigeria"
msgstr "Nigeria"

#: pynicotine/networkfilter.py:205
msgid "Nicaragua"
msgstr "Nicaragua"

#: pynicotine/networkfilter.py:206
msgid "Netherlands"
msgstr "Pays-Bas"

#: pynicotine/networkfilter.py:207
msgid "Norway"
msgstr "Norvège"

#: pynicotine/networkfilter.py:208
msgid "Nepal"
msgstr "Népal"

#: pynicotine/networkfilter.py:209
msgid "Nauru"
msgstr "Nauru"

#: pynicotine/networkfilter.py:210
msgid "Niue"
msgstr "Niue"

#: pynicotine/networkfilter.py:211
msgid "New Zealand"
msgstr "Nouvelle-Zélande"

#: pynicotine/networkfilter.py:212
msgid "Oman"
msgstr "Oman"

#: pynicotine/networkfilter.py:213
msgid "Panama"
msgstr "Panama"

#: pynicotine/networkfilter.py:214
msgid "Peru"
msgstr "Pérou"

#: pynicotine/networkfilter.py:215
msgid "French Polynesia"
msgstr "Polynésie française"

#: pynicotine/networkfilter.py:216
msgid "Papua New Guinea"
msgstr "Papouasie-Nouvelle-Guinée"

#: pynicotine/networkfilter.py:217
msgid "Philippines"
msgstr "Philippines"

#: pynicotine/networkfilter.py:218
msgid "Pakistan"
msgstr "Pakistan"

#: pynicotine/networkfilter.py:219
msgid "Poland"
msgstr "Pologne"

#: pynicotine/networkfilter.py:220
msgid "Saint Pierre & Miquelon"
msgstr "Saint-Pierre-et-Miquelon"

#: pynicotine/networkfilter.py:221
msgid "Pitcairn"
msgstr "Îles Pitcairn"

#: pynicotine/networkfilter.py:222
msgid "Puerto Rico"
msgstr "Porto Rico"

#: pynicotine/networkfilter.py:223
msgid "State of Palestine"
msgstr "Territoires palestiniens"

#: pynicotine/networkfilter.py:224
msgid "Portugal"
msgstr "Portugal"

#: pynicotine/networkfilter.py:225
msgid "Palau"
msgstr "Palaos"

#: pynicotine/networkfilter.py:226
msgid "Paraguay"
msgstr "Paraguay"

#: pynicotine/networkfilter.py:227
msgid "Qatar"
msgstr "Qatar"

#: pynicotine/networkfilter.py:228
msgid "Réunion"
msgstr "La Réunion"

#: pynicotine/networkfilter.py:229
msgid "Romania"
msgstr "Roumanie"

#: pynicotine/networkfilter.py:230
msgid "Serbia"
msgstr "Serbie"

#: pynicotine/networkfilter.py:231
msgid "Russia"
msgstr "Russie"

#: pynicotine/networkfilter.py:232
msgid "Rwanda"
msgstr "Rwanda"

#: pynicotine/networkfilter.py:233
msgid "Saudi Arabia"
msgstr "Arabie saoudite"

#: pynicotine/networkfilter.py:234
msgid "Solomon Islands"
msgstr "Îles Salomon"

#: pynicotine/networkfilter.py:235
msgid "Seychelles"
msgstr "Seychelles"

#: pynicotine/networkfilter.py:236
msgid "Sudan"
msgstr "Soudan"

#: pynicotine/networkfilter.py:237
msgid "Sweden"
msgstr "Suède"

#: pynicotine/networkfilter.py:238
msgid "Singapore"
msgstr "Singapour"

#: pynicotine/networkfilter.py:239
msgid "Saint Helena"
msgstr "Sainte-Hélène"

#: pynicotine/networkfilter.py:240
msgid "Slovenia"
msgstr "Slovénie"

#: pynicotine/networkfilter.py:241
msgid "Svalbard & Jan Mayen Islands"
msgstr "Svalbard et Jan Mayen"

#: pynicotine/networkfilter.py:242
msgid "Slovak Republic"
msgstr "Slovaquie"

#: pynicotine/networkfilter.py:243
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: pynicotine/networkfilter.py:244
msgid "San Marino"
msgstr "Saint-Marin"

#: pynicotine/networkfilter.py:245
msgid "Senegal"
msgstr "Sénégal"

#: pynicotine/networkfilter.py:246
msgid "Somalia"
msgstr "Somalie"

#: pynicotine/networkfilter.py:247
msgid "Suriname"
msgstr "Suriname"

#: pynicotine/networkfilter.py:248
msgid "South Sudan"
msgstr "Soudan du Sud"

#: pynicotine/networkfilter.py:249
msgid "Sao Tome & Principe"
msgstr "Sao Tomé-et-Principe"

#: pynicotine/networkfilter.py:250
msgid "El Salvador"
msgstr "Salvador"

#: pynicotine/networkfilter.py:251
msgid "Sint Maarten"
msgstr "Saint-Martin (partie néerlandaise)"

#: pynicotine/networkfilter.py:252
msgid "Syria"
msgstr "Syrie"

#: pynicotine/networkfilter.py:253
msgid "Eswatini"
msgstr "Eswatini"

#: pynicotine/networkfilter.py:254
msgid "Turks & Caicos Islands"
msgstr "Îles Turques-et-Caïques"

#: pynicotine/networkfilter.py:255
msgid "Chad"
msgstr "Tchad"

#: pynicotine/networkfilter.py:256
msgid "French Southern Territories"
msgstr "Terres australes et antarctiques françaises"

#: pynicotine/networkfilter.py:257
msgid "Togo"
msgstr "Togo"

#: pynicotine/networkfilter.py:258
msgid "Thailand"
msgstr "Thaïlande"

#: pynicotine/networkfilter.py:259
msgid "Tajikistan"
msgstr "Tadjikistan"

#: pynicotine/networkfilter.py:260
msgid "Tokelau"
msgstr "Tokelau"

#: pynicotine/networkfilter.py:261
msgid "Timor-Leste"
msgstr "Timor oriental"

#: pynicotine/networkfilter.py:262
msgid "Turkmenistan"
msgstr "Turkménistan"

#: pynicotine/networkfilter.py:263
msgid "Tunisia"
msgstr "Tunisie"

#: pynicotine/networkfilter.py:264
msgid "Tonga"
msgstr "Tonga"

#: pynicotine/networkfilter.py:265
msgid "Türkiye"
msgstr "Turquie"

#: pynicotine/networkfilter.py:266
msgid "Trinidad & Tobago"
msgstr "Trinité-et-Tobago"

#: pynicotine/networkfilter.py:267
msgid "Tuvalu"
msgstr "Tuvalu"

#: pynicotine/networkfilter.py:268
msgid "Taiwan"
msgstr "Taïwan"

#: pynicotine/networkfilter.py:269
msgid "Tanzania"
msgstr "Tanzanie"

#: pynicotine/networkfilter.py:270
msgid "Ukraine"
msgstr "Ukraine"

#: pynicotine/networkfilter.py:271
msgid "Uganda"
msgstr "Ouganda"

#: pynicotine/networkfilter.py:272
msgid "U.S. Minor Outlying Islands"
msgstr "Îles mineures éloignées des États-Unis"

#: pynicotine/networkfilter.py:273
msgid "United States"
msgstr "États-Unis"

#: pynicotine/networkfilter.py:274
msgid "Uruguay"
msgstr "Uruguay"

#: pynicotine/networkfilter.py:275
msgid "Uzbekistan"
msgstr "Ouzbékistan"

#: pynicotine/networkfilter.py:276
msgid "Holy See (Vatican City State)"
msgstr "Vatican"

#: pynicotine/networkfilter.py:277
msgid "Saint Vincent & The Grenadines"
msgstr "Saint-Vincent-et-les-Grenadines"

#: pynicotine/networkfilter.py:278
msgid "Venezuela"
msgstr "Venezuela"

#: pynicotine/networkfilter.py:279
msgid "British Virgin Islands"
msgstr "Îles Vierges britanniques"

#: pynicotine/networkfilter.py:280
msgid "U.S. Virgin Islands"
msgstr "Îles Vierges des États-Unis"

#: pynicotine/networkfilter.py:281
msgid "Viet Nam"
msgstr "Viêt Nam"

#: pynicotine/networkfilter.py:282
msgid "Vanuatu"
msgstr "Vanuatu"

#: pynicotine/networkfilter.py:283
msgid "Wallis & Futuna"
msgstr "Wallis-et-Futuna"

#: pynicotine/networkfilter.py:284
msgid "Samoa"
msgstr "Samoa"

#: pynicotine/networkfilter.py:285
msgid "Yemen"
msgstr "Yémen"

#: pynicotine/networkfilter.py:286
msgid "Mayotte"
msgstr "Mayotte"

#: pynicotine/networkfilter.py:287
msgid "South Africa"
msgstr "Afrique du Sud"

#: pynicotine/networkfilter.py:288
msgid "Zambia"
msgstr "Zambie"

#: pynicotine/networkfilter.py:289
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: pynicotine/notifications.py:74 pynicotine/notifications.py:93
#, python-format
msgid "Text-to-speech for message failed: %s"
msgstr "La synthèse vocale du message a échoué : %s"

#: pynicotine/nowplaying.py:130
msgid "Last.fm: Please provide both your Last.fm username and API key"
msgstr ""
"Last.fm : Veuillez fournir votre nom d'utilisateur Last.fm et votre clé API"

#: pynicotine/nowplaying.py:130 pynicotine/nowplaying.py:141
#: pynicotine/nowplaying.py:163 pynicotine/nowplaying.py:196
#: pynicotine/nowplaying.py:220 pynicotine/nowplaying.py:266
#: pynicotine/nowplaying.py:276 pynicotine/nowplaying.py:284
#: pynicotine/nowplaying.py:298 pynicotine/nowplaying.py:313
msgid "Now Playing Error"
msgstr "Erreur du script Now Playing"

#: pynicotine/nowplaying.py:140
#, python-format
msgid "Last.fm: Could not connect to Audioscrobbler: %(error)s"
msgstr "Last.fm : Impossible de se connecter à Audioscrobbler : %(error)s"

#: pynicotine/nowplaying.py:162
#, python-format
msgid "Last.fm: Could not get recent track from Audioscrobbler: %(error)s"
msgstr ""
"Last.fm : Impossible d'obtenir une piste récente d'Audioscrobbler : %(error)s"

#: pynicotine/nowplaying.py:196
msgid "MPRIS: Could not find a suitable MPRIS player"
msgstr "MPRIS : Impossible de trouver un lecteur MPRIS approprié"

#: pynicotine/nowplaying.py:201
#, python-format
msgid "Found multiple MPRIS players: %(players)s. Using: %(player)s"
msgstr ""
"Plusieurs lecteurs MPRIS trouvés  : %(players)s. Utilisation de  : %(player)s"

#: pynicotine/nowplaying.py:204
#, python-format
msgid "Auto-detected MPRIS player: %s"
msgstr "Lecteur MPRIS auto-détecté : %s"

#: pynicotine/nowplaying.py:219
#, python-format
msgid "MPRIS: Something went wrong while querying %(player)s: %(exception)s"
msgstr ""
"MPRIS : Un problème est survenu lors de l'interrogation de %(player)s : "
"%(exception)s"

#: pynicotine/nowplaying.py:266
msgid "ListenBrainz: Please provide your ListenBrainz username"
msgstr "ListenBrainz : Veuillez fournir votre nom d'utilisateur ListenBrainz"

#: pynicotine/nowplaying.py:275
#, python-format
msgid "ListenBrainz: Could not connect to ListenBrainz: %(error)s"
msgstr "ListenBrainz : Impossible de se connecter à ListenBrainz : %(error)s"

#: pynicotine/nowplaying.py:283
msgid "ListenBrainz: You don't seem to be listening to anything right now"
msgstr ""
"ListenBrainz : Vous ne semblez pas écouter quoi que ce soit en ce moment"

#: pynicotine/nowplaying.py:297
#, python-format
msgid "ListenBrainz: Could not get current track from ListenBrainz: %(error)s"
msgstr ""
"ListenBrainz : Impossible d'obtenir la piste actuelle de ListenBrainz : "
"%(error)s"

#: pynicotine/plugins/core_commands/__init__.py:28
msgid "Network Filters"
msgstr "Filtres réseau"

#: pynicotine/plugins/core_commands/__init__.py:44
msgid "List available commands"
msgstr "Lister les commandes disponibles"

#: pynicotine/plugins/core_commands/__init__.py:49
msgid "Connect to the server"
msgstr "Connexion au serveur"

#: pynicotine/plugins/core_commands/__init__.py:53
msgid "Disconnect from the server"
msgstr "Déconnexion du serveur"

#: pynicotine/plugins/core_commands/__init__.py:58
msgid "Toggle away status"
msgstr "Activer/désactiver le statut absent"

#: pynicotine/plugins/core_commands/__init__.py:62
msgid "Manage plugins"
msgstr "Gérer les extensions"

#: pynicotine/plugins/core_commands/__init__.py:74
msgid "Clear chat window"
msgstr "Effacer la fenêtre de discussion"

#: pynicotine/plugins/core_commands/__init__.py:80
msgid "Say something in the third-person"
msgstr "Parler à la troisième-personne"

#: pynicotine/plugins/core_commands/__init__.py:87
msgid "Announce the song currently playing"
msgstr "Annoncez la chanson en cours de lecture"

#: pynicotine/plugins/core_commands/__init__.py:94
msgid "Join chat room"
msgstr "Rejoindre ou créer une salle"

#: pynicotine/plugins/core_commands/__init__.py:102
msgid "Leave chat room"
msgstr "Quitter le salon de discussion"

#: pynicotine/plugins/core_commands/__init__.py:110
msgid "Say message in specified chat room"
msgstr "Dire un message dans le salon de discussion spécifié"

#: pynicotine/plugins/core_commands/__init__.py:117
msgid "Open private chat"
msgstr "Ouvrir un salon de discussion privé"

#: pynicotine/plugins/core_commands/__init__.py:125
msgid "Close private chat"
msgstr "Fermer le salon de discussion privé"

#: pynicotine/plugins/core_commands/__init__.py:133
msgid "Request user's client version"
msgstr "Demander la version client de l'utilisateur"

#: pynicotine/plugins/core_commands/__init__.py:142
msgid "Send private message to user"
msgstr "Envoyer un message privé à l'utilisateur"

#: pynicotine/plugins/core_commands/__init__.py:150
msgid "Add user to buddy list"
msgstr "Ajouter l'utilisateur à la liste d'amis"

#: pynicotine/plugins/core_commands/__init__.py:158
msgid "Remove buddy from buddy list"
msgstr "Retirer l’utilisateur de la liste d’amis"

#: pynicotine/plugins/core_commands/__init__.py:166
msgid "Browse files of user"
msgstr "Voir les fichiers de l'utilisateur"

#: pynicotine/plugins/core_commands/__init__.py:175
msgid "Show user profile information"
msgstr "Afficher les informations du profil de l'utilisateur"

#: pynicotine/plugins/core_commands/__init__.py:183
msgid "Show IP address or username"
msgstr "Afficher l'adresse IP ou le nom d'utilisateur"

#: pynicotine/plugins/core_commands/__init__.py:190
msgid "Block connections from user or IP address"
msgstr "Bloquer les connexions de l'utilisateur ou de l'adresse IP"

#: pynicotine/plugins/core_commands/__init__.py:197
msgid "Remove user or IP address from ban lists"
msgstr "Supprimer l'utilisateur ou une adresse IP des listes de bannissement"

#: pynicotine/plugins/core_commands/__init__.py:204
msgid "Silence messages from user or IP address"
msgstr "Désactiver les messages de l’utilisateur ou de l’adresse IP"

#: pynicotine/plugins/core_commands/__init__.py:212
msgid "Remove user or IP address from ignore lists"
msgstr ""
"Supprimer l'utilisateur ou l'adresse IP des listes d'utilisateurs ignorés"

#: pynicotine/plugins/core_commands/__init__.py:220
msgid "Add share"
msgstr "Ajouter un partage"

#: pynicotine/plugins/core_commands/__init__.py:226
msgid "Remove share"
msgstr "Supprimer le partage"

#: pynicotine/plugins/core_commands/__init__.py:233
msgid "List shares"
msgstr "Lister les partages"

#: pynicotine/plugins/core_commands/__init__.py:239
msgid "Rescan shares"
msgstr "Réexaminer mes partages"

#: pynicotine/plugins/core_commands/__init__.py:246
msgid "Start global file search"
msgstr "Lancer une recherche globale de fichiers"

#: pynicotine/plugins/core_commands/__init__.py:254
msgid "Search files in joined rooms"
msgstr "Rechercher des fichiers dans les salons joints"

#: pynicotine/plugins/core_commands/__init__.py:262
msgid "Search files of all buddies"
msgstr "Rechercher des fichiers dans tous les amis"

#: pynicotine/plugins/core_commands/__init__.py:270
msgid "Search a user's shared files"
msgstr "Rechercher les fichiers partagés d'un utilisateur"

#: pynicotine/plugins/core_commands/__init__.py:296
#, python-format
msgid "Listing %(num)i available commands:"
msgstr "Liste des commandes disponibles %(num)i :"

#: pynicotine/plugins/core_commands/__init__.py:298
#, python-format
msgid "Listing %(num)i available commands matching \"%(query)s\":"
msgstr ""
"Liste des commandes disponibles %(num)i correspondant à \"%(query)s\" :"

#: pynicotine/plugins/core_commands/__init__.py:311
#, python-format
msgid "Type %(command)s to list similar commands"
msgstr "Tapez %(command)s pour lister les commandes similaires"

#: pynicotine/plugins/core_commands/__init__.py:314
#, python-format
msgid "Type %(command)s to list available commands"
msgstr "Tapez %(command)s pour lister les commandes disponibles"

#: pynicotine/plugins/core_commands/__init__.py:376
#: pynicotine/plugins/core_commands/__init__.py:387
#, python-format
msgid "Not joined in room %s"
msgstr "Salon %s non rejoint"

#: pynicotine/plugins/core_commands/__init__.py:404
#, python-format
msgid "Not messaging with user %s"
msgstr "Pas de messages avec l'utilisateur %s"

#: pynicotine/plugins/core_commands/__init__.py:408
#, python-format
msgid "Closed private chat of user %s"
msgstr "Fermer la discussion privé de l'utilisateur %s"

#: pynicotine/plugins/core_commands/__init__.py:476
#, python-format
msgid "Banned %s"
msgstr "%s banni"

#: pynicotine/plugins/core_commands/__init__.py:490
#, python-format
msgid "Unbanned %s"
msgstr "%s débanni"

#: pynicotine/plugins/core_commands/__init__.py:503
#, python-format
msgid "Ignored %s"
msgstr "%s ignoré"

#: pynicotine/plugins/core_commands/__init__.py:517
#, python-format
msgid "Unignored %s"
msgstr "%s désignoré"

#: pynicotine/plugins/core_commands/__init__.py:553
#, python-format
msgid "%(num_listed)s shares listed (%(num_total)s configured)"
msgstr "%(num_listed)s partages listés (%(num_total)s configurés)"

#: pynicotine/plugins/core_commands/__init__.py:565
#, python-format
msgid "Cannot share inaccessible folder \"%s\""
msgstr "Impossible de partager le dossier inaccessible : \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:568
#, python-format
msgid "Added %(group_name)s share \"%(virtual_name)s\" (rescan required)"
msgstr "Partage %(group_name)s ajouté \"%(virtual_name)s\" (réexamen requis)"

#: pynicotine/plugins/core_commands/__init__.py:579
#, python-format
msgid "No share with name \"%s\""
msgstr "Aucun partage avec le nom \"%s\""

#: pynicotine/plugins/core_commands/__init__.py:582
#, python-format
msgid "Removed share \"%s\" (rescan required)"
msgstr "Le partage \"%s\" a été retiré (Réexamen requis)"

#: pynicotine/pluginsystem.py:413
msgid "Loading plugin system"
msgstr "Chargement du greffon système"

#: pynicotine/pluginsystem.py:516
#, python-format
msgid ""
"Unable to load plugin %(name)s. Plugin folder name contains invalid "
"characters: %(characters)s"
msgstr ""
"Impossible de charger le plugin %(name)s. Le nom du dossier de plugin "
"contient des caractères non valides : %(characters)s"

#: pynicotine/pluginsystem.py:548
#, python-format
msgid "Conflicting %(interface)s command in plugin %(name)s: %(command)s"
msgstr ""
"Conflit avec la commande %(interface)s dans le plugin %(name)s : %(command)s"

#: pynicotine/pluginsystem.py:575
#, python-format
msgid "Loaded plugin %s"
msgstr "Plugin chargé %s"

#: pynicotine/pluginsystem.py:579
#, python-format
msgid ""
"Unable to load plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Impossible de charger le plugin %(module)s.\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:644
#, python-format
msgid "Unloaded plugin %s"
msgstr "Plugin désactivé %s"

#: pynicotine/pluginsystem.py:648
#, python-format
msgid ""
"Unable to unload plugin %(module)s\n"
"%(exc_trace)s"
msgstr ""
"Impossible de désactiver le plugin %(module)s.\n"
"%(exc_trace)s"

#: pynicotine/pluginsystem.py:752
#, python-format
msgid ""
"Plugin %(module)s failed with error %(errortype)s: %(error)s.\n"
"Trace: %(trace)s"
msgstr ""
"Le plugin %(module)s a échoué avec l'erreur %(errortype)s : %(error)s.\n"
"Trace : %(trace)s"

#: pynicotine/pluginsystem.py:810
msgid "No description"
msgstr "Aucune description"

#: pynicotine/pluginsystem.py:887
#, python-format
msgid "Missing %s argument"
msgstr "Argument %s manquant"

#: pynicotine/pluginsystem.py:896
#, python-format
msgid "Invalid argument, possible choices: %s"
msgstr "Argument invalide, choix possibles : %s"

#: pynicotine/pluginsystem.py:901
#, python-format
msgid "Usage: %(command)s %(parameters)s"
msgstr "Utilisation : %(command)s %(parameters)s"

#: pynicotine/pluginsystem.py:940
#, python-format
msgid ""
"Unknown command: %(command)s. Type %(help_command)s to list available "
"commands."
msgstr ""
"Commande inconnue : %(command)s. Tapez %(help_command)s pour lister les "
"commandes disponibles."

#: pynicotine/portmapper.py:516 pynicotine/portmapper.py:639
msgid "No UPnP devices found"
msgstr "Aucun appareil UPnP trouvé"

#: pynicotine/portmapper.py:633
#, python-format
msgid ""
"%(protocol)s: Failed to forward external port %(external_port)s: %(error)s"
msgstr ""
"%(protocol)s : Échec de la redirection du port externe %(external_port)s : "
"%(error)s"

#: pynicotine/portmapper.py:647
#, python-format
msgid ""
"%(protocol)s: External port %(external_port)s successfully forwarded to "
"local IP address %(ip_address)s port %(local_port)s"
msgstr ""
"%(protocol)s: Le port externe %(external_port)s a été transféré avec succès "
"vers l'adresse IP locale %(ip_address)s port %(local_port)s"

#: pynicotine/privatechat.py:220
#, python-format
msgid "Private message from user '%(user)s': %(message)s"
msgstr "Message privé de '%(user)s' : %(message)s"

#: pynicotine/search.py:368
#, python-format
msgid "Searching for wishlist item \"%s\""
msgstr "Recherche de l'élément \"%s\" dans les listes de souhaits"

#: pynicotine/search.py:434
#, python-format
msgid "Wishlist wait period set to %s seconds"
msgstr "La période d'attente de la liste de souhaits est fixée à %s secondes"

#: pynicotine/search.py:760
#, python-format
msgid "User %(user)s is searching for \"%(query)s\", found %(num)i results"
msgstr ""
"L'utilisateur %(user)s effectue une recherche sur « %(query)s », %(num)i "
"résultats renvoyés"

#: pynicotine/shares.py:302
msgid "Rebuilding shares…"
msgstr "Réexamen des partages…"

#: pynicotine/shares.py:302
msgid "Rescanning shares…"
msgstr "Réanalyse des partages…"

#: pynicotine/shares.py:324
#, python-format
msgid "Rescan complete: %(num)s folders found"
msgstr "Réanalyse terminée : %(num)s dossiers trouvés"

#: pynicotine/shares.py:334
#, python-format
msgid ""
"Serious error occurred while rescanning shares. If this problem persists, "
"delete %(dir)s/*.dbn and try again. If that doesn't help, please file a bug "
"report with this stack trace included: %(trace)s"
msgstr ""
"Une erreur grave est survenue lors du réexamen des partages. Si le problème "
"persiste, supprimez %(dir)s/*.db et réessayez. Si cela ne résout pas le "
"problème, veuillez remplir un rapport de bogue en incluant cette trace "
"d'appels : %(trace)s"

#: pynicotine/shares.py:582
#, python-format
msgid "Error while scanning file %(path)s: %(error)s"
msgstr "Erreur lors de l'examen du fichier %(path)s : %(error)s"

#: pynicotine/shares.py:590
#, python-format
msgid "Error while scanning folder %(path)s: %(error)s"
msgstr "Erreur lors de l'examen du répertoire %(path)s : %(error)s"

#: pynicotine/shares.py:627
#, python-format
msgid "Error while scanning metadata for file %(path)s: %(error)s"
msgstr ""
"Erreur lors de l'examen des métadonnées du fichier %(path)s : %(error)s"

#: pynicotine/shares.py:1046
#, python-format
msgid "Rescan aborted due to unavailable shares: %s"
msgstr "Nouvelle analyse interrompue en raison de partages indisponibles : %s"

#: pynicotine/shares.py:1184
#, python-format
msgid "User %(user)s is browsing your list of shared files"
msgstr "L'utilisateur %(user)s parcourt votre liste de fichiers partagés"

#: pynicotine/slskmessages.py:3120
#, python-format
msgid "Unable to read shares database. Please rescan your shares. Error: %s"
msgstr ""
"Impossible de lire la base de données des partages. Veuillez réanalyser vos "
"partages. Erreur : %s"

#: pynicotine/slskproto.py:500
#, python-format
msgid "Specified network interface '%s' is not available"
msgstr "L'interface réseau spécifiée '%s' n'est pas disponible"

#: pynicotine/slskproto.py:511
#, python-format
msgid ""
"Cannot listen on port %(port)s. Ensure no other application uses it, or "
"choose a different port. Error: %(error)s"
msgstr ""
"Impossible d'écouter sur le port %(port)s. Assurez-vous qu'aucune autre "
"application ne l'utilise pas ou choisissez un port différent. Erreur : "
"%(error)s"

#: pynicotine/slskproto.py:523
#, python-format
msgid "Listening on port: %i"
msgstr "Écoute du port : %i"

#: pynicotine/slskproto.py:805
#, python-format
msgid "Cannot connect to server %(host)s:%(port)s: %(error)s"
msgstr "Impossible de se connecter au serveur %(host)s :%(port)s : %(error)s"

#: pynicotine/slskproto.py:1095
#, python-format
msgid "Reconnecting to server in %s seconds"
msgstr "Reconnexion automatique au serveur dans %s secondes"

#: pynicotine/slskproto.py:1170
#, python-format
msgid "Connecting to %(host)s:%(port)s"
msgstr "En cours de connexion à %(host)s :%(port)s"

#: pynicotine/slskproto.py:1208
#, python-format
msgid "Connected to server %(host)s:%(port)s, logging in…"
msgstr "Connecté au serveur %(host)s :%(port)s, ouverture de session en cours…"

#: pynicotine/slskproto.py:1493
#, python-format
msgid "Disconnected from server %(host)s:%(port)s"
msgstr "Déconnecté du serveur %(host)s :%(port)s"

#: pynicotine/slskproto.py:1499
msgid "Someone logged in to your Soulseek account elsewhere"
msgstr "Quelqu'un s'est connecté à votre compte Soulseek ailleurs"

#: pynicotine/uploads.py:382
#, python-format
msgid "Upload finished: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Envoi terminé : utilisateur %(user)s, adresse IP %(ip)s, fichier %(file)s"

#: pynicotine/uploads.py:395
#, python-format
msgid "Upload aborted, user %(user)s file %(file)s"
msgstr "Envoi annulé, utilisateur %(user)s, fichier %(file)s"

#: pynicotine/uploads.py:1063 pynicotine/uploads.py:1091
#, python-format
msgid "Upload I/O error: %s"
msgstr "Erreur d'E/S lors de l'envoi  : %s"

#: pynicotine/uploads.py:1103
#, python-format
msgid "Upload started: user %(user)s, IP address %(ip)s, file %(file)s"
msgstr ""
"Envoi démarré : utilisateur %(user)s, adresse IP %(ip)s, fichier %(file)s"

#: pynicotine/userbrowse.py:176
#, python-format
msgid "Can't create directory '%(folder)s', reported error: %(error)s"
msgstr ""
"Impossible de créer le répertoire '%(folder)s', l'erreur est  : %(error)s"

#: pynicotine/userbrowse.py:236
#, python-format
msgid "Loading Shares from disk failed: %(error)s"
msgstr "Le chargement des partages à partir du disque a échoué  : %(error)s"

#: pynicotine/userbrowse.py:282
#, python-format
msgid "Saved list of shared files for user '%(user)s' to %(dir)s"
msgstr ""
"Liste des fichiers partagés par l'utilisateur %(user)s sauvegardée dans "
"%(dir)s"

#: pynicotine/userbrowse.py:286
#, python-format
msgid "Can't save shares, '%(user)s', reported error: %(error)s"
msgstr ""
"Ne peut sauvegarder les partages, '%(user)s', erreur rapportée  : %(error)s"

#: pynicotine/userinfo.py:160
#, python-format
msgid "Picture saved to %s"
msgstr "Image sauvegardée dans %s"

#: pynicotine/userinfo.py:163
#, python-format
msgid "Cannot save picture to %(filename)s: %(error)s"
msgstr "Impossible de sauvegarder l'image %(filename)s : %(error)s"

#: pynicotine/userinfo.py:190
#, python-format
msgid "User %(user)s is viewing your profile"
msgstr "L’utilisateur %(user)s consulte votre profil"

#: pynicotine/users.py:272
#, python-format
msgid "Unable to connect to the server. Reason: %s"
msgstr "Impossible de se connecter au serveur. Raison : %s"

#: pynicotine/users.py:272
msgid "Cannot Connect"
msgstr "Impossible d'établir une connexion"

#: pynicotine/users.py:306
#, python-format
msgid "Cannot retrieve the IP of user %s, since this user is offline"
msgstr ""
"L'adresse IP de l'utilisateur %s est inconnue, car l'utilisateur est hors "
"ligne"

#: pynicotine/users.py:315
#, python-format
msgid "IP address of user %(user)s: %(ip)s, port %(port)i%(country)s"
msgstr ""
"L'adresse IP de l'utilisateur %(user)s est %(ip)s, son port est "
"%(port)i%(country)s"

#: pynicotine/users.py:433
msgid "Soulseek Announcement"
msgstr "Annonce de Soulseek"

#: pynicotine/users.py:449
msgid ""
"You have no Soulseek privileges. While privileges are active, your downloads "
"will be queued ahead of those of non-privileged users."
msgstr ""
"Vous n'avez pas de privilèges Soulseek. Lorsque les privilèges sont activés, "
"vos téléchargements seront mis en file d'attente avant ceux des utilisateurs "
"non privilégiés."

#: pynicotine/users.py:455
#, python-format
msgid ""
"%(days)i days, %(hours)i hours, %(minutes)i minutes, %(seconds)i seconds of "
"Soulseek privileges left"
msgstr ""
"%(days)i jours, %(hours)i heures, %(minutes)i minutes, %(seconds)i secondes "
"restantes de privilèges Soulseek"

#: pynicotine/users.py:473
msgid "Your password has been changed"
msgstr "Votre mot de passe a été modifié"

#: pynicotine/users.py:473
msgid "Password Changed"
msgstr "Mot de passe modifié"

#: pynicotine/utils.py:574
#, python-format
msgid "Cannot open file path %(path)s: %(error)s"
msgstr "Impossible d'ouvrir le chemin du fichier %(path)s : %(error)s"

#: pynicotine/utils.py:621
#, python-format
msgid "Cannot open URL %(url)s: %(error)s"
msgstr "Impossible d'ouvrir l'URL %(url)s : %(error)s"

#: pynicotine/utils.py:646
#, python-format
msgid "Something went wrong while reading file %(filename)s: %(error)s"
msgstr ""
"Un problème est survenu lors de la lecture du fichier %(filename)s : "
"%(error)s"

#: pynicotine/utils.py:651
#, python-format
msgid "Attempting to load backup of file %s"
msgstr "Tentative de chargement d'un fichier de sauvegarde %s"

#: pynicotine/utils.py:673
#, python-format
msgid "Unable to back up file %(path)s: %(error)s"
msgstr "Impossible de sauvegarder le fichier %(path)s : %(error)s"

#: pynicotine/utils.py:694
#, python-format
msgid "Unable to save file %(path)s: %(error)s"
msgstr "Impossible d'enregistrer le fichier %(path)s : %(error)s"

#: pynicotine/utils.py:705
#, python-format
msgid "Unable to restore previous file %(path)s: %(error)s"
msgstr "Impossible de restaurer le fichier précédent %(path)s : %(error)s"

#: pynicotine/gtkgui/ui/buddies.ui:31 pynicotine/gtkgui/ui/mainwindow.ui:1254
msgid "Add buddy…"
msgstr "Ajouter un ami…"

#: pynicotine/gtkgui/ui/chatrooms.ui:85 pynicotine/gtkgui/ui/privatechat.ui:48
msgid "Toggle Text-to-Speech"
msgstr "Activer/désactiver la synthèse vocale"

#: pynicotine/gtkgui/ui/chatrooms.ui:100
msgid "Chat Room Command Help"
msgstr "Aide sur les commandes des salons de discussion"

#: pynicotine/gtkgui/ui/chatrooms.ui:115 pynicotine/gtkgui/ui/privatechat.ui:78
msgid "_Log"
msgstr "_Journal"

#: pynicotine/gtkgui/ui/chatrooms.ui:187
msgid "Room Wall"
msgstr "Mur du salon"

#: pynicotine/gtkgui/ui/chatrooms.ui:202
msgid "R_oom Wall"
msgstr "Mur du sal_on"

#: pynicotine/gtkgui/ui/dialogs/about.ui:124
msgid "Created by"
msgstr "Créé par"

#: pynicotine/gtkgui/ui/dialogs/about.ui:163
msgid "Translated by"
msgstr "Traduit par"

#: pynicotine/gtkgui/ui/dialogs/about.ui:202
msgid "License"
msgstr "Licence"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:98
msgid "Welcome to Nicotine+"
msgstr "Bienvenue dans Nicotine+"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:195
msgid ""
"If your desired username is already taken, you will be prompted to change it."
msgstr ""
"Si votre nom d'utilisateur souhaité est déjà utilisé, vous serez invité à le "
"modifier."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:322
msgid ""
"To connect with other Soulseek peers, a listening port on your router has to "
"be forwarded to your computer."
msgstr ""
"Pour se connecter avec d'autres utilisateurs de Soulseek, un port d'écoute "
"sur votre routeur doit être envoyé à votre ordinateur."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:332
msgid ""
"If your listening port is closed, you will only be able to connect to users "
"whose listening ports are open."
msgstr ""
"Si votre port d'écoute est fermé, vous ne pourrez vous connecter qu'aux "
"utilisateurs dont le port d'écoute est ouvert."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:342
msgid ""
"If necessary, choose a different listening port below. This can also be done "
"later in the preferences."
msgstr ""
"Si nécessaire, choisissez un autre port d'écoute ci-dessous. Cela peut "
"également être fait plus tard dans les préférences."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:383
msgid "Download Files to Folder"
msgstr "Télécharger des fichiers dans un dossier"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:404
msgid "Share Folders"
msgstr "Répertoires partagés"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:416
#: pynicotine/gtkgui/ui/settings/shares.ui:23
msgid ""
"Soulseek users will be able to download from your shares. Contribute to the "
"Soulseek network by sharing your own files and by resharing what you "
"downloaded from other users."
msgstr ""
"Les utilisateurs de Soulseek pourront télécharger à partir de vos partages. "
"Contribuez au réseau Soulseek en partageant votre propre collection et en "
"partageant à nouveau ce que vous avez téléchargé d'autres utilisateurs."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:581
msgid "You are ready to use Nicotine+!"
msgstr "Vous êtes prêt à utiliser Nicotine+ !"

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:599
msgid ""
"Soulseek is an unencrypted protocol not intended for secure communication."
msgstr ""
"Soulseek est un protocole non encrypté et n'est en aucun prévu à une "
"communication sécurisée."

#: pynicotine/gtkgui/ui/dialogs/fastconfigure.ui:609
msgid ""
"Donating to Soulseek grants you privileges for a certain time period. If you "
"have privileges, your downloads will be queued ahead of non-privileged users."
msgstr ""
"Faire un don à Soulseek vous donne des privilèges pour une certaine période. "
"Si vous avez des privilèges, vos téléchargements seront mis en file "
"d'attente avant ceux des utilisateurs non privilégiés."

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:20
msgid "Previous File"
msgstr "Fichier précédent"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:34
msgid "Next File"
msgstr "Fichier suivant"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:63
msgid "Name"
msgstr "Nom"

#: pynicotine/gtkgui/ui/dialogs/fileproperties.ui:299
msgid "Last Speed"
msgstr "Dernière vitesse"

#: pynicotine/gtkgui/ui/dialogs/preferences.ui:11
msgid "_Export…"
msgstr "_Exporter…"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:6
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:54
msgid "Keyboard Shortcuts"
msgstr "Raccourcis clavier"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:14
msgid "General"
msgstr "Général"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:19
msgid "Connect"
msgstr "Se connecter"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:26
msgid "Disconnect"
msgstr "Se déconnecter"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:40
msgid "Rescan Shares"
msgstr "Réanalyser les partages"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:47
#: pynicotine/gtkgui/ui/mainwindow.ui:1861
msgid "Show Log Pane"
msgstr "Afficher le volet du journal"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:68
msgid "Confirm Quit"
msgstr "Confirmez Quitter"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:75
msgid "Quit"
msgstr "Quitter"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:83
msgid "Menus"
msgstr "Menus"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:88
msgid "Open Main Menu"
msgstr "Ouvrir le menu principal"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:95
msgid "Open Context Menu"
msgstr "Ouvrir le menu contextuel"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:103
#: pynicotine/gtkgui/ui/settings/userinterface.ui:380
msgid "Tabs"
msgstr "Onglets"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:108
msgid "Change Main Tab"
msgstr "Changer l'onglet principal"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:115
msgid "Go to Previous Secondary Tab"
msgstr "Aller à l'onglet secondaire précédent"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:122
msgid "Go to Next Secondary Tab"
msgstr "Aller à l'onglet secondaire suivant"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:129
msgid "Reopen Closed Secondary Tab"
msgstr "Rouvrir l'onglet secondaire fermé"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:136
msgid "Close Secondary Tab"
msgstr "Fermer l'onglet secondaire"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:144
#: pynicotine/gtkgui/ui/settings/userinterface.ui:924
msgid "Lists"
msgstr "Listes"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:149
msgid "Copy Selected Cell"
msgstr "Copier la cellule sélectionnée"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:156
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:211
msgid "Select All"
msgstr "Sélectionner tout"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:163
#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:218
msgid "Find"
msgstr "Trouver"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:170
msgid "Remove Selected Row"
msgstr "Supprimer la ligne sélectionnée"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:178
msgid "Editing"
msgstr "Modification de"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:183
msgid "Cut"
msgstr "Coupez"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:197
msgid "Paste"
msgstr "Coller"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:204
msgid "Insert Emoji"
msgstr "Insérer un Emoji"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:240
msgid "File Transfers"
msgstr "Transferts des fichiers"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:245
msgid "Resume / Retry Transfer"
msgstr "Reprendre / Retenter le transfert"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:252
msgid "Pause / Abort Transfer"
msgstr "Pause / Abandon du transfert"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:272
msgid "Download / Upload To"
msgstr "Télécharger ou envoyer à"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:286
msgid "Save List to Disk"
msgstr "Sauvegarder la liste sur le disque"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:300
msgid "Refresh"
msgstr "Actualiser"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:307
#: pynicotine/gtkgui/ui/mainwindow.ui:371
#: pynicotine/gtkgui/ui/mainwindow.ui:610 pynicotine/gtkgui/ui/search.ui:199
#: pynicotine/gtkgui/ui/userbrowse.ui:103
msgid "Expand / Collapse All"
msgstr "Tout minimiser ou maximiser"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:314
msgid "Back to Parent Folder"
msgstr "Retour au répertoire parent"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:322
msgid "File Search"
msgstr "Recherche de fichier"

#: pynicotine/gtkgui/ui/dialogs/shortcuts.ui:327
msgid "Result Filters"
msgstr "Filtres de Résultat"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:21
msgid "Current Session"
msgstr "Session en cours"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:38
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:156
msgid "Completed Downloads"
msgstr "Téléchargements terminés"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:64
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:182
msgid "Downloaded Size"
msgstr "Quantité de données téléchargées"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:91
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:209
msgid "Completed Uploads"
msgstr "Envois terminés"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:117
#: pynicotine/gtkgui/ui/dialogs/statistics.ui:235
msgid "Uploaded Size"
msgstr "Quantité de données envoyées"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:138
msgid "Total"
msgstr "Total"

#: pynicotine/gtkgui/ui/dialogs/statistics.ui:278
msgid "_Reset…"
msgstr "_Rétablir…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:16
msgid ""
"Wishlist items are auto-searched at regular intervals, for discovering "
"uncommon files."
msgstr ""
"Les éléments de la liste de souhaits font l'objet d'une recherche "
"automatique à intervalles réguliers, afin de découvrir des fichiers peu "
"courants."

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:26
msgid "Add Wish…"
msgstr "Ajouter un souhait…"

#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:125
#: pynicotine/gtkgui/ui/dialogs/wishlist.ui:141
msgid "Clear All…"
msgstr "Tout effacer…"

#: pynicotine/gtkgui/ui/downloads.ui:138
msgid "Clear All Finished/Filtered Downloads"
msgstr "Effacer tous les téléchargements terminés / filtrés"

#: pynicotine/gtkgui/ui/downloads.ui:154 pynicotine/gtkgui/ui/uploads.ui:154
msgid "Clear Finished"
msgstr "Effacer terminé"

#: pynicotine/gtkgui/ui/downloads.ui:169
msgid "Clear Specific Downloads"
msgstr "Effacer les téléchargements sélectionnés"

#: pynicotine/gtkgui/ui/downloads.ui:178 pynicotine/gtkgui/ui/uploads.ui:208
msgid "Clear _All…"
msgstr "Tout _effacer…"

#: pynicotine/gtkgui/ui/interests.ui:21
msgid "Personal Interests"
msgstr "Intérêts personnels"

#: pynicotine/gtkgui/ui/interests.ui:40
msgid "Add something you like…"
msgstr "Ajoutez ce que vous aimez…"

#: pynicotine/gtkgui/ui/interests.ui:62
msgid "Personal Dislikes"
msgstr "Désintérêts personnels"

#: pynicotine/gtkgui/ui/interests.ui:80
msgid "Add something you dislike…"
msgstr "Ajoutez ce que vous n'aimez pas…"

#: pynicotine/gtkgui/ui/interests.ui:143
msgid "Refresh Recommendations"
msgstr "Rafraichir la liste des recommandations"

#: pynicotine/gtkgui/ui/mainwindow.ui:26
msgid "Main Menu"
msgstr "Menu principal"

#: pynicotine/gtkgui/ui/mainwindow.ui:43
msgid "Room…"
msgstr "Salon…"

#: pynicotine/gtkgui/ui/mainwindow.ui:51 pynicotine/gtkgui/ui/mainwindow.ui:732
#: pynicotine/gtkgui/ui/mainwindow.ui:900
#: pynicotine/gtkgui/ui/mainwindow.ui:1095
msgid "Username…"
msgstr "Utilisateur…"

#: pynicotine/gtkgui/ui/mainwindow.ui:58
msgid "Search term…"
msgstr "Terme de recherche…"

#: pynicotine/gtkgui/ui/mainwindow.ui:60
#: pynicotine/gtkgui/ui/settings/userinterface.ui:5
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1613
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1661
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1709
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1757
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1805
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1853
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1901
msgid "Clear"
msgstr "Effacer"

#: pynicotine/gtkgui/ui/mainwindow.ui:61
msgid ""
"Search patterns: with a word = term, without a word = -term, partial word = "
"*erm"
msgstr ""
"Modèles de recherche : avec un mot = terme, sans mot = -terme, mot partiel = "
"*erme"

#: pynicotine/gtkgui/ui/mainwindow.ui:97
msgid "Search Scope"
msgstr "Champ de recherche"

#: pynicotine/gtkgui/ui/mainwindow.ui:143
msgid "_Wishlist"
msgstr "_Liste de souhaits"

#: pynicotine/gtkgui/ui/mainwindow.ui:165
msgid "Configure Searches"
msgstr "Configurer les recherches"

#: pynicotine/gtkgui/ui/mainwindow.ui:232
msgid ""
"Enter a search term to search for files shared by other users on the "
"Soulseek network"
msgstr ""
"Entrez un terme de recherche pour rechercher des fichiers partagés par "
"d'autres utilisateurs sur le réseau Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:386
#: pynicotine/gtkgui/ui/mainwindow.ui:625 pynicotine/gtkgui/ui/search.ui:214
msgid "File Grouping Mode"
msgstr "Mode de groupement des fichiers"

#: pynicotine/gtkgui/ui/mainwindow.ui:404
msgid "Configure Downloads"
msgstr "Configurer les téléchargements"

#: pynicotine/gtkgui/ui/mainwindow.ui:471
msgid ""
"Files you download from other users are queued here, and can be paused and "
"resumed on demand"
msgstr ""
"Les fichiers que vous téléchargez auprès d'autres utilisateurs sont mis en "
"file d'attente ici, et peuvent être mis en pause et repris à la demande"

#: pynicotine/gtkgui/ui/mainwindow.ui:643
msgid "Configure Uploads"
msgstr "Configurer les envois"

#: pynicotine/gtkgui/ui/mainwindow.ui:710
msgid ""
"Users' attempts to download your shared files are queued and managed here"
msgstr ""
"Les tentatives des utilisateurs pour télécharger vos fichiers partagés sont "
"mises en file d'attente et gérées ici"

#: pynicotine/gtkgui/ui/mainwindow.ui:788
msgid "_Open List"
msgstr "_Ouvrir une liste de partage"

#: pynicotine/gtkgui/ui/mainwindow.ui:790
msgid "Opens a local list of shared files that was previously saved to disk"
msgstr ""
"Ouvre une liste locale de fichiers partagés qui a été précédemment "
"enregistrée sur le disque"

#: pynicotine/gtkgui/ui/mainwindow.ui:811
msgid "Configure Shares"
msgstr "Configurer les partages"

#: pynicotine/gtkgui/ui/mainwindow.ui:878
msgid ""
"Enter the name of a user, whose shared files you'd like to browse. You can "
"also save the list to disk, and inspect it later on."
msgstr ""
"Saisissez le nom d'un utilisateur dont vous souhaitez parcourir les fichiers "
"partagés. Vous pouvez également sauvegarder la liste pour la parcourir plus "
"tard."

#: pynicotine/gtkgui/ui/mainwindow.ui:956
msgid "_Personal Profile"
msgstr "_Profil personnel"

#: pynicotine/gtkgui/ui/mainwindow.ui:978
msgid "Configure Account"
msgstr "Configurer le compte"

#: pynicotine/gtkgui/ui/mainwindow.ui:1045
msgid ""
"Enter the name of a user to view their user description, information and "
"personal picture"
msgstr ""
"Saisissez le nom d'un utilisateur pour afficher sa description, ses "
"informations et sa photo personnelle"

#: pynicotine/gtkgui/ui/mainwindow.ui:1112
msgid "Chat _History"
msgstr "_Historique des discussions"

#: pynicotine/gtkgui/ui/mainwindow.ui:1138
#: pynicotine/gtkgui/ui/mainwindow.ui:1472
msgid "Configure Chats"
msgstr "Configurer les salons"

#: pynicotine/gtkgui/ui/mainwindow.ui:1205
msgid ""
"Enter the name of a user to start a text conversation with them in private"
msgstr ""
"Saisissez le nom d'un utilisateur pour entamer une conversation textuelle "
"avec lui en privé"

#: pynicotine/gtkgui/ui/mainwindow.ui:1285
msgid "_Message All"
msgstr "Messages pour tous"

#: pynicotine/gtkgui/ui/mainwindow.ui:1307
msgid "Configure Ignored Users"
msgstr "Configurer les utilisateurs ignorés"

#: pynicotine/gtkgui/ui/mainwindow.ui:1374
msgid ""
"Add users as buddies to share specific folders with them and receive "
"notifications when they are online"
msgstr ""
"Ajoutez des utilisateurs à votre liste d'amis pour partager des dossiers "
"spécifiques avec eux et recevoir des notifications lorsqu'ils sont en ligne"

#: pynicotine/gtkgui/ui/mainwindow.ui:1429
msgid "Join or create room…"
msgstr "Rejoindre ou créer une salle…"

#: pynicotine/gtkgui/ui/mainwindow.ui:1539
msgid ""
"Join an existing chat room, or create a new room to chat with other users on "
"the Soulseek network"
msgstr ""
"Rejoignez un salon de discussion existant ou créez un nouveau salon pour "
"discuter avec d'autres utilisateurs du réseau Soulseek"

#: pynicotine/gtkgui/ui/mainwindow.ui:1600
msgid "Configure User Profile"
msgstr "Configurer le Profil utilisateur"

#: pynicotine/gtkgui/ui/mainwindow.ui:1730
#: pynicotine/gtkgui/ui/settings/network.ui:281
msgid "Connections"
msgstr "Connexions"

#: pynicotine/gtkgui/ui/mainwindow.ui:1762
msgid "Downloading (Speed / Active Users)"
msgstr "Téléchargement (vitesse / utilisateurs actifs)"

#: pynicotine/gtkgui/ui/mainwindow.ui:1793
msgid "Uploading (Speed / Active Users)"
msgstr "Envoi (vitesse / utilisateurs actifs)"

#: pynicotine/gtkgui/ui/popovers/chathistory.ui:21
msgid "Search chat history…"
msgstr "Recherche dans l’historique des salons…"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:176
msgid "Download Speed Limits"
msgstr "Limites de vitesse de téléchargement"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/downloads.ui:190
msgid "Unlimited download speed"
msgstr "Vitesse de téléchargement illimitée"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/downloads.ui:202
msgid "Use download speed limit (KiB/s):"
msgstr "Limite de vitesse de téléchargement (Kio/s) :"

#: pynicotine/gtkgui/ui/popovers/downloadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/downloads.ui:224
msgid "Use alternative download speed limit (KiB/s):"
msgstr "Limite alternative de vitesse de téléchargement (Kio/s) :"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:24
msgid "Search rooms…"
msgstr "Rechercher des salons…"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:32
msgid "Refresh Rooms"
msgstr "Rafraîchir la liste des salons"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:77
msgid "_Show feed of public chat room messages"
msgstr "_Afficher le flux des messages des salons de discussion publics"

#: pynicotine/gtkgui/ui/popovers/roomlist.ui:104
#: pynicotine/gtkgui/ui/settings/chats.ui:50
msgid "_Accept private room invitations"
msgstr "_Accepter les invitations à des salons privés"

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:19
msgid ""
"Write a single message that other room users can read later. Recent messages "
"are shown at the top."
msgstr ""
"Écrivez un seul message que les autres utilisateurs peuvent lire plus tard. "
"Les messages récents sont affichés en haut."

#: pynicotine/gtkgui/ui/popovers/roomwall.ui:50
msgid "Set wall message…"
msgstr "Définir un message sur le mur…"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:19
#: pynicotine/gtkgui/ui/settings/search.ui:138
msgid "Search Result Filters"
msgstr "Filtres de résultats de recherche"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:30
msgid ""
"Search result filters are used to refine which search results are displayed."
msgstr ""
"Les filtres de résultats de recherche sont utilisés pour affiner les "
"résultats de recherche affichés."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:39
msgid ""
"Each list of search results has its own filter which can be revealed by "
"toggling the Result Filters button. A filter is made up of multiple fields, "
"all of which are applied when pressing Enter in any one of its fields. "
"Filtering is applied immediately to results already received, and also to "
"those yet to arrive."
msgstr ""
"Chaque liste de résultats de recherche possède son propre filtre qui peut "
"être affiché en cliquant sur le bouton « Filtres de résultats ». Un filtre "
"est composé de plusieurs champs, chacun s'appliquant lorsque la touche "
"Entrée est Appuyé. Le filtrage s'applique alors aussitôt aux résultats déjà "
"reçus ainsi qu'à ceux à venir."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:48
msgid ""
"As the name suggests, a search result filter cannot expand your original "
"search, it can only narrow it down. To broaden or change your search terms, "
"perform a new search."
msgstr ""
"Comme son nom l'indique, un filtre de résultats de recherche ne peut pas "
"élargir votre recherche d'origine, il ne peut que la réduire. Pour élargir "
"ou modifier vos termes de recherche, effectuez une nouvelle recherche."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:57
msgid "Result Filter Usage"
msgstr "Utilisation des filtres de résultat"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:69
msgid "Include Text"
msgstr "Inclure le texte"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:85
msgid "Files, folders and usernames containing this text will be shown."
msgstr ""
"Les fichiers, dossiers et utilisateurs contenant ce texte seront affichés."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:95
msgid ""
"Case is insensitive, but word order is important: 'Instrumental Remix' will "
"not show any 'Remix Instrumental'"
msgstr ""
"La recherche n'est pas sensible à la casse mais l'ordre des mots est "
"important : « Instrumental Remix » n'affichera pas les résultats avec « "
"Remix Instrumental »"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:105
msgid ""
"Use | (or pipes) to seperate several exact phrases. Example:\n"
"    Remix|Dub Mix|Instrumental"
msgstr ""
"Utiliser | (ou tuyaux) pour séparer plusieurs phrases exactes. Exemple :\n"
"    Remix|Dub Mix|Instrumental"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:118
msgid "Exclude Text"
msgstr "Exclure le texte"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:129
msgid ""
"As above, but files, folders and usernames are filtered out if the text "
"matches."
msgstr ""
"Comme ci-dessus, mais les fichiers, dossiers et utilisateurs ne seront pas "
"affichés si le texte correspond."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:150
msgid "Filters files based upon their file extension."
msgstr "Filtre les fichiers en fonction de leur extension."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:160
msgid ""
"Multiple file extensions can be specified, which in turn will reveal more "
"from the list of results. Example:\n"
"    flac wav ape"
msgstr ""
"Plusieurs extensions de fichiers peuvent être spécifiées, ce qui élargira la "
"liste des résultats. Exemple :\n"
"\tflac wav ape"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:171
msgid ""
"It is also possible to invert the filter, specifying file extensions you "
"don't want in your results with an exclamation mark! Example:\n"
"    !mp3 !jpg"
msgstr ""
"Il est également possible d'inverser le filtre, en spécifiant les extensions "
"de fichiers que vous ne souhaitez pas voir apparaître dans vos résultats "
"avec un point d'exclamation ! Exemple :\n"
"    !mp3 !jpg"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:182
msgid "File Size"
msgstr "Taille du fichier"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:198
msgid "Filters files based upon their file size."
msgstr "Filtre les fichiers en fonction de leur taille."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:208
msgid ""
"By default, the unit used is bytes (B) and files greater than or equal to "
"(>=) the value will be matched."
msgstr ""
"Par défaut, l'unité utilisée est l'octet (B) et les fichiers supérieurs ou "
"égaux (>=) à cette valeur seront pris en compte."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:218
msgid ""
"Append b, k, m, or g (alternatively kib, mib, or gib) to specify byte, "
"kibibyte, mebibyte, or gibibyte units:\n"
"    20m to show files larger than 20 MiB (mebibytes)."
msgstr ""
"Ajoutez b, k, m ou g (alternativement kib, mib ou gib) pour spécifier les "
"unités d'octet, de kibibyte, de mebibyte ou de gibibyte :\n"
"  20m trouvera les fichiers supérieurs à 20Mio (20 mebi-octets)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:229
msgid ""
"Prepend = to a value to specify an exact match:\n"
"    =1024 matches files that are exactly 1 KiB (kibibyte)."
msgstr ""
"Ajoutez = à une valeur pour spécifier une correspondance exacte :\n"
"    =1024 correspond à des fichiers d'exactement 1 KiB (kibioctet)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:240
msgid ""
"Prepend ! to a value to exclude files of a specific size:\n"
"    !30.5m to hide files that are 30.5 MiB (mebibytes)."
msgstr ""
"Ajoutez ! à une valeur pour spécifier une correspondance exacte :\n"
"  !30.5m ne correspond qu'aux fichiers d'une taille de 30.5 Mio (mébi-octet)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:251
msgid ""
"Prepend < or > to find files smaller/larger than the given value. Use a "
"space between each condition to include a range:\n"
"    >10.5m <1g to show files larger than 10.5 MiB, but smaller than 1 GiB."
msgstr ""
"Ajoutez < ou > pour rechercher des fichiers plus petits/plus grands que la "
"valeur donnée. Ajoutez un espace entre chaque conditions pour inclure une "
"rangée de valeurs. :\n"
"    >10,5m <1g pour afficher les fichiers de plus de 10,5 Mio, mais "
"inférieur à 1 Gio."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:262
msgid ""
"The better-known variants kb, mb, and gb can also be used for kilobyte, "
"megabyte, and gigabyte units."
msgstr ""
"Par commodité, les populaires variantes kb, mb et gb respectivement pour "
"kilo-octet, méga-octet et giga-octet peuvent également être utilisées."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:290
msgid "Filters files based upon their bitrate."
msgstr "Filtre les fichiers en fonction de leur taux d'encodage."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:300
msgid ""
"Values must be entered as numeric digits only. The unit is always Kb/s "
"(Kilobits per second)."
msgstr ""
"Les valeurs ne doivent contenir que des chiffres. L'unité est toujours Kb/s "
"(Kilobits par seconde)."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:310
msgid ""
"Like File Size (above), operators =, !, <, >, <= or >= can be used, and "
"multiple conditions can be specified, for example to show files with a "
"bitrate of at least 256 Kb/s with a maximum bitrate of 1411 Kb/s:\n"
"    256 <=1411"
msgstr ""
"Comme la taille du fichier (ci-dessus), les opérateurs =, !, <, >, <= ou >= "
"peuvent être utilisés, et plusieurs conditions peuvent être spécifiées avec "
"le séparateur | par exemple pour afficher les fichiers avec un débit d'au "
"moins 256 Kb/s et avec un débit maximum de 1411 Kb/s :\n"
"    256 <=1411"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:339
msgid "Filters files based upon their duration."
msgstr "Filtre les fichiers suivant leur durée."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:349
msgid ""
"By default, files longer than or equal to (>=) the entered duration will be "
"matched, unless an operator (=, !, <=, < or >) is used."
msgstr ""
"Par défaut, les fichiers dont la durée est supérieure ou égale (>=) à la "
"durée saisie seront pris en compte, sauf si un opérateur (=, !, <=, < ou >) "
"est utilisé."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:359
msgid ""
"Enter a raw value in seconds or use the MM:SS and HH:MM:SS time formats:\n"
"    =53 shows files that are around 53 seconds long.\n"
"    >5:30 to show files more than 5 and a half minutes long.\n"
"    <5:30:00 shows files less than 5 and a half hours long."
msgstr ""
"Saisissez une valeur brute en secondes ou utilisez les formats d'heure MM:SS "
"et HH:MM:SS :\n"
"    =53 pour afficher les fichiers d'une durée d'environ 53 secondes\n"
"    >5:30 pour afficher les fichiers de plus de 5 minutes et demie.\n"
"    <5:30:00 affiche les fichiers de moins de 5 heures et demie."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:372
msgid ""
"Multiple conditions can be specified:\n"
"    >6:00 <12:00 to show files between 6 and 12 minutes long.\n"
"    !9:54 !8:43 !7:32 to hide some specific files from the results.\n"
"    =5:34 =4:23 =3:05 to include files with specific durations."
msgstr ""
"Plusieurs conditions peuvent être spécifiées :\n"
"    >6:00 <12:00 pour afficher les fichiers d'une durée comprise entre 6 et "
"12 minutes.\n"
"    !9:54 !8:43 !7:32 pour masquer certains fichiers spécifiques des "
"résultats.\n"
"    =5:34 =4:23 =3:05 pour inclure des fichiers d'une durée spécifique."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:398
msgid ""
"Filters files based upon users' geographical location according to country "
"codes defined by ISO 3166-2:\n"
"    US will only show results from users with IP addresses in the United "
"States.\n"
"    !GB will hide results that come from users in Great Britain."
msgstr ""
"Utilise les codes pays définis par la norme ISO 3166-2 :\n"
"  US renvoie uniquement les fichiers des utilisateurs connectés aux États-"
"Unis.\n"
"  !GB masque les fichiers des utilisateurs situés au Royaume-Uni."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:410
msgid "Multiple countries can be specified with commas or spaces."
msgstr ""
"Plusieurs pays peuvent être spécifiés à l'aide de virgules ou d'espaces."

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:420
#: pynicotine/gtkgui/ui/search.ui:333
#: pynicotine/gtkgui/ui/settings/search.ui:397
msgid "Free Slot"
msgstr "Place disponible"

#: pynicotine/gtkgui/ui/popovers/searchfilterhelp.ui:431
msgid ""
"Show only those results from users which have at least one upload slot free, "
"i.e. files that are available immediately."
msgstr ""
"N'afficher que les résultats des utilisateurs qui ont au moins un créneau de "
"téléchargement libre, c'est-à-dire des fichiers disponibles immédiatement."

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:30
#: pynicotine/gtkgui/ui/settings/uploads.ui:106
msgid "Upload Speed Limits"
msgstr "Limites de vitesse d'envoi"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:43
#: pynicotine/gtkgui/ui/settings/uploads.ui:158
msgid "Unlimited upload speed"
msgstr "Vitesse de téléchargement illimitée"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:56
#: pynicotine/gtkgui/ui/settings/uploads.ui:170
msgid "Use upload speed limit (KiB/s):"
msgstr "Limiter la vitesse d'envoi (KiB/s) :"

#: pynicotine/gtkgui/ui/popovers/uploadspeeds.ui:80
#: pynicotine/gtkgui/ui/settings/uploads.ui:193
msgid "Use alternative upload speed limit (KiB/s):"
msgstr "Limite de vitesse d'envoi alternative (KiB/s) :"

#: pynicotine/gtkgui/ui/privatechat.ui:63
msgid "Private Chat Command Help"
msgstr "Aide sur les commandes de salon privé"

#: pynicotine/gtkgui/ui/search.ui:7
msgid "Include text…"
msgstr "Inclure le texte…"

#: pynicotine/gtkgui/ui/search.ui:9 pynicotine/gtkgui/ui/settings/search.ui:221
msgid ""
"Filter in results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Filtrer parmi les résultats ceux dont les chemins de fichiers contiennent le "
"texte spécifié. Plusieurs expressions et mots peuvent être spécifiés, par "
"exemple « expression exacte|musique|terme|expression exacte deux »"

#: pynicotine/gtkgui/ui/search.ui:18
msgid "Exclude text…"
msgstr "Exclure le texte…"

#: pynicotine/gtkgui/ui/search.ui:20
#: pynicotine/gtkgui/ui/settings/search.ui:246
msgid ""
"Filter out results whose file paths contain the specified text. Multiple "
"phrases and words can be specified, e.g. exact phrase|music|term|exact "
"phrase two"
msgstr ""
"Exclure parmi les résultats ceux dont les chemins de fichiers contiennent le "
"texte spécifié. Plusieurs expressions et mots peuvent être spécifiés, par "
"exemple « expression exacte|musique|terme|expression exacte deux »"

#: pynicotine/gtkgui/ui/search.ui:29
msgid "File type…"
msgstr "Type de fichier…"

#: pynicotine/gtkgui/ui/search.ui:31
#: pynicotine/gtkgui/ui/settings/search.ui:273
msgid "File type, e.g. flac wav or !mp3 !m4a"
msgstr "Type de fichier, par exemple « flac|wav|ape » ou «!mp3|!m4a »"

#: pynicotine/gtkgui/ui/search.ui:40
msgid "File size…"
msgstr "Taille du fichier…"

#: pynicotine/gtkgui/ui/search.ui:42
#: pynicotine/gtkgui/ui/settings/search.ui:300
msgid "File size, e.g. >10.5m <1g"
msgstr "Taille du fichier, par exemple >10.5m <1g"

#: pynicotine/gtkgui/ui/search.ui:51
msgid "Bitrate…"
msgstr "Taux d'encodage…"

#: pynicotine/gtkgui/ui/search.ui:53
#: pynicotine/gtkgui/ui/settings/search.ui:327
msgid "Bitrate, e.g. 256 <1412"
msgstr "Débit, par exemple 256 <1412"

#: pynicotine/gtkgui/ui/search.ui:62
msgid "Duration…"
msgstr "Durée…"

#: pynicotine/gtkgui/ui/search.ui:64
#: pynicotine/gtkgui/ui/settings/search.ui:354
msgid "Duration, e.g. >6:00 <12:00 !6:54"
msgstr "Durée, par exemple >6:00 <12:00 !6:54"

#: pynicotine/gtkgui/ui/search.ui:73
msgid "Country code…"
msgstr "Code pays…"

#: pynicotine/gtkgui/ui/search.ui:75
#: pynicotine/gtkgui/ui/settings/search.ui:381
msgid "Country code, e.g. US ES or !DE !GB"
msgstr "Code pays, par exemple US ES ou !DE !GB"

#: pynicotine/gtkgui/ui/settings/ban.ui:28
msgid ""
"Prohibit users from accessing your shared files, based on username, IP "
"address or country."
msgstr ""
"Interdire aux utilisateurs d'accéder à vos fichiers partagés, en fonction du "
"nom d'utilisateur, de l'adresse IP ou du pays."

#: pynicotine/gtkgui/ui/settings/ban.ui:43
msgid "Country codes to block (comma separated):"
msgstr "Codes de pays à bloquer (séparés par des virgules)  :"

#: pynicotine/gtkgui/ui/settings/ban.ui:51
msgid "Codes must be in ISO 3166-2 format."
msgstr "Les codes doivent être au format ISO 3166-2."

#: pynicotine/gtkgui/ui/settings/ban.ui:66
msgid "Use custom geo block message:"
msgstr "Utiliser un message de blocage géographique personnalisé  :"

#: pynicotine/gtkgui/ui/settings/ban.ui:87
msgid "Use custom ban message:"
msgstr "Utiliser un message d'interdiction personnalisé  :"

#: pynicotine/gtkgui/ui/settings/ban.ui:245
#: pynicotine/gtkgui/ui/settings/ignore.ui:179
msgid "IP Addresses"
msgstr "Adresses IP"

#: pynicotine/gtkgui/ui/settings/chats.ui:75
msgid "Restore previously open private chats on startup"
msgstr ""
"Restauration au démarrage des discussions privées précédemment ouvertes"

#: pynicotine/gtkgui/ui/settings/chats.ui:99
msgid "Enable spell checker"
msgstr "Activer le correcteur"

#: pynicotine/gtkgui/ui/settings/chats.ui:123
msgid "Enable CTCP-like private message responses (client version)"
msgstr "Activer les réponses aux messages privés de type CTCP (version client)"

#: pynicotine/gtkgui/ui/settings/chats.ui:147
msgid "Number of recent private chat messages to show:"
msgstr "Le nombre de discussions privées récentes à afficher :"

#: pynicotine/gtkgui/ui/settings/chats.ui:172
msgid "Number of recent chat room messages to show:"
msgstr "Nombre de salons de discussion récents à afficher :"

#: pynicotine/gtkgui/ui/settings/chats.ui:201
msgid "Chat Completion"
msgstr "Complétion des discussions"

#: pynicotine/gtkgui/ui/settings/chats.ui:219
msgid "Enable tab-key completion"
msgstr "Activer la touche TAB pour l'auto-complétion"

#: pynicotine/gtkgui/ui/settings/chats.ui:247
msgid "Enable completion drop-down list"
msgstr "Activer la liste déroulante"

#: pynicotine/gtkgui/ui/settings/chats.ui:276
msgid "Minimum characters required to display drop-down:"
msgstr ""
"Nombre de caractères minimum requis pour afficher la liste déroulante :"

#: pynicotine/gtkgui/ui/settings/chats.ui:315
msgid "Allowed chat completions:"
msgstr "Complétions autorisées des discussions :"

#: pynicotine/gtkgui/ui/settings/chats.ui:342
msgid "Buddy names"
msgstr "Nom des amis"

#: pynicotine/gtkgui/ui/settings/chats.ui:354
msgid "Chat room usernames"
msgstr "Nom des utilisateurs des salons"

#: pynicotine/gtkgui/ui/settings/chats.ui:366
msgid "Room names"
msgstr "Nom des salons"

#: pynicotine/gtkgui/ui/settings/chats.ui:378
msgid "Commands"
msgstr "Commandes"

#: pynicotine/gtkgui/ui/settings/chats.ui:404
msgid "Timestamps"
msgstr "Horodatages"

#: pynicotine/gtkgui/ui/settings/chats.ui:421
msgid "Private chat format:"
msgstr "Format de chat privé :"

#: pynicotine/gtkgui/ui/settings/chats.ui:432
#: pynicotine/gtkgui/ui/settings/chats.ui:459
#: pynicotine/gtkgui/ui/settings/chats.ui:567
#: pynicotine/gtkgui/ui/settings/chats.ui:594
#: pynicotine/gtkgui/ui/settings/downloads.ui:15
#: pynicotine/gtkgui/ui/settings/downloads.ui:30
#: pynicotine/gtkgui/ui/settings/downloads.ui:45
#: pynicotine/gtkgui/ui/settings/log.ui:5
#: pynicotine/gtkgui/ui/settings/log.ui:20
#: pynicotine/gtkgui/ui/settings/log.ui:35
#: pynicotine/gtkgui/ui/settings/log.ui:50
#: pynicotine/gtkgui/ui/settings/log.ui:201
#: pynicotine/gtkgui/ui/settings/network.ui:334
#: pynicotine/gtkgui/ui/settings/userinterface.ui:470
#: pynicotine/gtkgui/ui/settings/userinterface.ui:510
#: pynicotine/gtkgui/ui/settings/userinterface.ui:550
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1014
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1116
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1156
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1196
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1236
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1276
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1316
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1376
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1416
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1456
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1516
#: pynicotine/gtkgui/ui/settings/userinterface.ui:1556
msgid "Default"
msgstr "Par défaut"

#: pynicotine/gtkgui/ui/settings/chats.ui:448
msgid "Chat room format:"
msgstr "Format des salons de discussion :"

#: pynicotine/gtkgui/ui/settings/chats.ui:485
msgid "Text-to-Speech"
msgstr "Synthèse vocale"

#: pynicotine/gtkgui/ui/settings/chats.ui:506
msgid "Enable Text-to-Speech"
msgstr "Activer la synthèse vocale"

#: pynicotine/gtkgui/ui/settings/chats.ui:540
msgid "Text-to-Speech command:"
msgstr "Commande de synthèse vocale :"

#: pynicotine/gtkgui/ui/settings/chats.ui:556
msgid "Private chat message:"
msgstr "Message des dialogues privés :"

#: pynicotine/gtkgui/ui/settings/chats.ui:583
msgid "Chat room message:"
msgstr "Message des salons de discussion :"

#: pynicotine/gtkgui/ui/settings/chats.ui:638
msgid "Censor"
msgstr "Censurer"

#: pynicotine/gtkgui/ui/settings/chats.ui:656
msgid "Enable censoring of text patterns"
msgstr "Activer la censure des modèles de texte"

#: pynicotine/gtkgui/ui/settings/chats.ui:821
msgid "Auto-Replace"
msgstr "Remplacement automatique"

#: pynicotine/gtkgui/ui/settings/chats.ui:839
msgid "Enable automatic replacement of words"
msgstr "Activer le remplacement automatique des mots"

#: pynicotine/gtkgui/ui/settings/downloads.ui:89
msgid "Autoclear finished/filtered downloads from transfer list"
msgstr ""
"Effacement automatique des téléchargements terminés/filtrés de la liste des "
"transferts"

#: pynicotine/gtkgui/ui/settings/downloads.ui:113
msgid "Store completed downloads in username subfolders"
msgstr ""
"Stocker les téléchargements terminés dans les sous-dossiers du nom "
"d'utilisateur"

#: pynicotine/gtkgui/ui/settings/downloads.ui:136
msgid "Double-click action for downloads:"
msgstr "Action de double-clic pour les téléchargements :"

#: pynicotine/gtkgui/ui/settings/downloads.ui:152
msgid "Allow users to send you any files:"
msgstr "Autoriser les utilisateurs à vous envoyer des fichiers :"

#: pynicotine/gtkgui/ui/settings/downloads.ui:248
#: pynicotine/gtkgui/ui/userbrowse.ui:45
msgid "Folders"
msgstr "Répertoires"

#: pynicotine/gtkgui/ui/settings/downloads.ui:265
msgid "Finished downloads:"
msgstr "Téléchargements terminés :"

#: pynicotine/gtkgui/ui/settings/downloads.ui:281
msgid "Incomplete downloads:"
msgstr "Téléchargements incomplets :"

#: pynicotine/gtkgui/ui/settings/downloads.ui:297
msgid "Received files:"
msgstr "Fichiers reçus :"

#: pynicotine/gtkgui/ui/settings/downloads.ui:316
msgid "Events"
msgstr "Événements"

#: pynicotine/gtkgui/ui/settings/downloads.ui:333
msgid "Run command after file download finishes ($ for file path):"
msgstr ""
"Exécuter la commande après la fin du téléchargement du fichier ($ pour le "
"chemin du fichier) :"

#: pynicotine/gtkgui/ui/settings/downloads.ui:357
msgid "Run command after folder download finishes ($ for folder path):"
msgstr ""
"Exécuter la commande une fois le téléchargement du dossier terminé ($ pour "
"le chemin du dossier) :"

#: pynicotine/gtkgui/ui/settings/downloads.ui:384
msgid "Download Filters"
msgstr "Filtres de téléchargement"

#: pynicotine/gtkgui/ui/settings/downloads.ui:402
msgid "Enable download filters"
msgstr "Activer les filtres de téléchargement"

#: pynicotine/gtkgui/ui/settings/downloads.ui:448
msgid "Add"
msgstr "Ajouter"

#: pynicotine/gtkgui/ui/settings/downloads.ui:546
#: pynicotine/gtkgui/ui/settings/downloads.ui:562
msgid "Load Defaults"
msgstr "Par défaut"

#: pynicotine/gtkgui/ui/settings/downloads.ui:605
msgid "Verify Filters"
msgstr "Vérifier les filtres"

#: pynicotine/gtkgui/ui/settings/downloads.ui:617
msgid "Unverified"
msgstr "Non vérifié"

#: pynicotine/gtkgui/ui/settings/ignore.ui:28
msgid ""
"Ignore chat messages and search results from users, based on username or IP "
"address."
msgstr ""
"Ignorer les discussions et les résultats de recherche des utilisateurs, en "
"fonction du nom d'utilisateur ou de l'adresse IP."

#: pynicotine/gtkgui/ui/settings/log.ui:94
msgid "Log chatrooms by default"
msgstr "Enregistrer les salons de discussion par défaut"

#: pynicotine/gtkgui/ui/settings/log.ui:118
msgid "Log private chat by default"
msgstr "Enregistrer les dialogues privés par défaut"

#: pynicotine/gtkgui/ui/settings/log.ui:142
msgid "Log transfers to file"
msgstr "Enregistrer les transferts dans un fichier"

#: pynicotine/gtkgui/ui/settings/log.ui:166
msgid "Log debug messages to file"
msgstr "Enregistrer les messages de débogage dans un fichier"

#: pynicotine/gtkgui/ui/settings/log.ui:190
msgid "Log timestamp format:"
msgstr "Format de l'horodatage du journal :"

#: pynicotine/gtkgui/ui/settings/log.ui:228
msgid "Folder Locations"
msgstr "Emplacement des dossiers"

#: pynicotine/gtkgui/ui/settings/log.ui:245
msgid "Chatroom logs folder:"
msgstr "Dossier des journaux du salon de discussion :"

#: pynicotine/gtkgui/ui/settings/log.ui:261
msgid "Private chat logs folder:"
msgstr "Dossier des journaux de discussion privées  :"

#: pynicotine/gtkgui/ui/settings/log.ui:277
msgid "Transfer logs folder:"
msgstr "Dossier des journaux de transfert :"

#: pynicotine/gtkgui/ui/settings/log.ui:293
msgid "Debug logs folder:"
msgstr "Dossier des journaux de débogage :"

#: pynicotine/gtkgui/ui/settings/network.ui:39
msgid ""
"Log in to an existing Soulseek account or create a new one. Usernames are "
"case-sensitive and unique."
msgstr ""
"Connectez-vous à un compte Soulseek existant ou créez-en un nouveau. Les "
"noms d'utilisateur sont sensibles à la casse et uniques."

#: pynicotine/gtkgui/ui/settings/network.ui:118
msgid "Public IP address:"
msgstr "Adresse IP publique :"

#: pynicotine/gtkgui/ui/settings/network.ui:159
msgid "Listening port:"
msgstr "Port d'écoute :"

#: pynicotine/gtkgui/ui/settings/network.ui:185
msgid "Automatically forward listening port (UPnP/NAT-PMP)"
msgstr "Transférer automatiquement le port d'écoute (UPnP/NAT-PMP)"

#: pynicotine/gtkgui/ui/settings/network.ui:211
msgid "Away Status"
msgstr "Statut d'absent"

#: pynicotine/gtkgui/ui/settings/network.ui:228
msgid "Minutes of inactivity before going away (0 to disable):"
msgstr "Minutes d'inactivité avant d'être absent (0 pour désactiver) :"

#: pynicotine/gtkgui/ui/settings/network.ui:253
msgid "Auto-reply message when away:"
msgstr "Message de réponse automatique en cas d'absence :"

#: pynicotine/gtkgui/ui/settings/network.ui:299
msgid "Auto-connect to server on startup"
msgstr "Connexion automatique au serveur au démarrage"

#: pynicotine/gtkgui/ui/settings/network.ui:323
msgid "Soulseek server:"
msgstr "Serveur Soulseek :"

#: pynicotine/gtkgui/ui/settings/network.ui:347
msgid ""
"Binds connections to a specific network interface, useful for e.g. ensuring "
"a VPN is used at all times. Leave empty to use any available interface. Only "
"change this value if you know what you are doing."
msgstr ""
"Lie les connexions à une interface réseau spécifique, ce qui est utile, par "
"exemple, pour s'assurer qu'un VPN est utilisé à tout moment. Laissez vide "
"pour utiliser toute interface disponible. Ne modifiez cette valeur que si "
"vous savez ce que vous faites."

#: pynicotine/gtkgui/ui/settings/network.ui:351
msgid "Network interface:"
msgstr "Interface réseau :"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:28
msgid ""
"Now Playing allows you to display what your media player is playing by using "
"the /now command in chat."
msgstr ""
"Now Playing vous permet d'afficher ce que votre lecteur mutimédia est en "
"train de jouer grâce à la commande /now dans une discussion."

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:61
msgid "Other"
msgstr "Autres"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:99
msgid "Now Playing Format"
msgstr "Format Now Playing"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:124
msgid "Now Playing message format:"
msgstr "Format du message Now Playing :"

#: pynicotine/gtkgui/ui/settings/nowplaying.ui:155
msgid "Test Configuration"
msgstr "Tester la configuration"

#: pynicotine/gtkgui/ui/settings/plugin.ui:48
msgid "Enable plugins"
msgstr "Activer les extensions"

#: pynicotine/gtkgui/ui/settings/plugin.ui:95
msgid "Add Plugins"
msgstr "Ajouter des extensions"

#: pynicotine/gtkgui/ui/settings/plugin.ui:111
msgid "_Add Plugins"
msgstr "_Ajouter des extensions"

#: pynicotine/gtkgui/ui/settings/plugin.ui:132
msgid "Settings"
msgstr "Préférences"

#: pynicotine/gtkgui/ui/settings/plugin.ui:148
msgid "_Settings"
msgstr "_Préférences"

#: pynicotine/gtkgui/ui/settings/plugin.ui:216
msgid "Version:"
msgstr "Version :"

#: pynicotine/gtkgui/ui/settings/plugin.ui:241
msgid "Created by:"
msgstr "Créé par :"

#: pynicotine/gtkgui/ui/settings/search.ui:51
msgid "Enable search history"
msgstr "Activer l'historique des recherches"

#: pynicotine/gtkgui/ui/settings/search.ui:70
msgid ""
"Privately shared files that have been made visible to everyone will be "
"prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives "
"explicit permission. Ask them kindly."
msgstr ""
"Les fichiers partagés en privé qui ont été rendus visibles à tous seront "
"préfixés avec '[PRIVATE]', et ne peuvent être téléchargés que si les "
"utilisateurs donnent une autorisation explicite. Demandez-leur gentiment."

#: pynicotine/gtkgui/ui/settings/search.ui:76
msgid "Show privately shared files in search results"
msgstr ""
"Afficher les fichiers partagés en privé dans les résultats de recherche"

#: pynicotine/gtkgui/ui/settings/search.ui:106
msgid "Limit number of results per search:"
msgstr "Limiter le nombre de résultats par recherche :"

#: pynicotine/gtkgui/ui/settings/search.ui:149
msgid "Result Filter Help"
msgstr "Aide sur le filtre de résultats"

#: pynicotine/gtkgui/ui/settings/search.ui:177
msgid "Enable search result filters by default"
msgstr "Activer les filtres de résultats de recherche par défaut"

#: pynicotine/gtkgui/ui/settings/search.ui:211
msgid "Include:"
msgstr "Inclure :"

#: pynicotine/gtkgui/ui/settings/search.ui:236
msgid "Exclude:"
msgstr "Exclure :"

#: pynicotine/gtkgui/ui/settings/search.ui:261
msgid "File Type:"
msgstr "Type de fichier :"

#: pynicotine/gtkgui/ui/settings/search.ui:288
msgid "Size:"
msgstr "Taille  :"

#: pynicotine/gtkgui/ui/settings/search.ui:315
msgid "Bitrate:"
msgstr "Taux d'encodage  :"

#: pynicotine/gtkgui/ui/settings/search.ui:342
msgid "Duration:"
msgstr "Durée :"

#: pynicotine/gtkgui/ui/settings/search.ui:369
msgid "Country Code:"
msgstr "Code pays :"

#: pynicotine/gtkgui/ui/settings/search.ui:407
msgid "Only show results from users with an available upload slot."
msgstr ""
"Afficher uniquement les résultats des utilisateurs ayant un créneau de "
"téléchargement disponible."

#: pynicotine/gtkgui/ui/settings/search.ui:430
msgid "Network Searches"
msgstr "Recherches sur le réseau"

#: pynicotine/gtkgui/ui/settings/search.ui:452
msgid "Respond to search requests from other users"
msgstr "Répondre aux demandes de recherche d'autres utilisateurs"

#: pynicotine/gtkgui/ui/settings/search.ui:486
msgid "Searches shorter than this number of characters will be ignored:"
msgstr ""
"Les recherches plus courtes que ce nombre de caractères seront ignorées :"

#: pynicotine/gtkgui/ui/settings/search.ui:511
msgid "Maximum search results to send per search request:"
msgstr ""
"Nombre maximum de résultats de recherche à envoyer par demande de recherche :"

#: pynicotine/gtkgui/ui/settings/search.ui:578
msgid "Clear Search History"
msgstr "Effacer l'historique des recherches"

#: pynicotine/gtkgui/ui/settings/search.ui:626
msgid "Clear Filter History"
msgstr "Effacer l'historique des filtres"

#: pynicotine/gtkgui/ui/settings/shares.ui:33
msgid ""
"Automatically rescans the contents of your shared folders on startup. If "
"disabled, your shares are only updated when you manually initiate a rescan."
msgstr ""
"Réanalyse automatiquement le contenu de vos dossiers partagés au démarrage. "
"Si cette option est désactivée, vos partages ne sont mis à jour que lorsque "
"vous lancez manuellement une nouvelle analyse."

#: pynicotine/gtkgui/ui/settings/shares.ui:39
msgid "Rescan shares on startup"
msgstr "Réexaminer mes partages au démarrage"

#: pynicotine/gtkgui/ui/settings/shares.ui:68
msgid "Visible to everyone:"
msgstr "Visible à tous :"

#: pynicotine/gtkgui/ui/settings/shares.ui:82
msgid "Buddy shares"
msgstr "Partage des amis"

#: pynicotine/gtkgui/ui/settings/shares.ui:89
msgid "Trusted shares"
msgstr "Partages de confiance"

#: pynicotine/gtkgui/ui/settings/uploads.ui:65
msgid "Autoclear finished/cancelled uploads from transfer list"
msgstr ""
"Effacement automatique des téléchargements terminés/annulés de la liste des "
"transferts"

#: pynicotine/gtkgui/ui/settings/uploads.ui:88
msgid "Double-click action for uploads:"
msgstr "Action de double-clic pour les envois :"

#: pynicotine/gtkgui/ui/settings/uploads.ui:122
msgid "Limit upload speed:"
msgstr "Limitez la vitesse d'envoi :"

#: pynicotine/gtkgui/ui/settings/uploads.ui:137
msgid "Per transfer"
msgstr "Par transfert"

#: pynicotine/gtkgui/ui/settings/uploads.ui:145
msgid "Total transfers"
msgstr "Total des transferts"

#: pynicotine/gtkgui/ui/settings/uploads.ui:217
#: pynicotine/gtkgui/ui/userinfo.ui:257
msgid "Upload Slots"
msgstr "Créneau d'envoi"

#: pynicotine/gtkgui/ui/settings/uploads.ui:231
msgid ""
"Round Robin: Files will be uploaded in cyclical fashion to the users waiting "
"in queue.\n"
"First In, First Out: Files will be uploaded in the order they were queued."
msgstr ""
"Round Robin : Les fichiers seront téléchargés de façon cyclique vers les "
"utilisateurs qui font la queue.\n"
"Premier entré, premier sorti : Les fichiers seront téléchargés dans l'ordre "
"dans lequel ils ont été mis en file d'attente."

#: pynicotine/gtkgui/ui/settings/uploads.ui:236
msgid "Upload queue type:"
msgstr "Type de file d'attente pour l'envoi :"

#: pynicotine/gtkgui/ui/settings/uploads.ui:253
msgid "Allocate upload slots until total speed reaches (KiB/s):"
msgstr ""
"Mettre en file d'attente les téléchargements si la vitesse de transfert "
"totale atteint (KiB/s) :"

#: pynicotine/gtkgui/ui/settings/uploads.ui:276
msgid "Fixed number of upload slots:"
msgstr "Limiter le nombre d'envoi à :"

#: pynicotine/gtkgui/ui/settings/uploads.ui:294
msgid "Prioritize all buddies"
msgstr "Donner la priorité à tous les amis"

#: pynicotine/gtkgui/ui/settings/uploads.ui:308
msgid "Queue Limits"
msgstr "Limites de file d'attente"

#: pynicotine/gtkgui/ui/settings/uploads.ui:325
msgid "Maximum number of queued files per user:"
msgstr "Nombre maximal de fichiers envoyés par utilisateur :"

#: pynicotine/gtkgui/ui/settings/uploads.ui:350
msgid "Maximum total size of queued files per user (MiB):"
msgstr "Taille maximale des fichiers en queue par utilisateur (MiB) :"

#: pynicotine/gtkgui/ui/settings/uploads.ui:371
msgid "Limits do not apply to buddies"
msgstr "Les limites ne s'appliquent pas aux amis"

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:24
msgid ""
"Instances of $ are replaced by the URL. Default system applications are used "
"in cases where a protocol has not been configured."
msgstr ""
"Les occurrences de $ seront remplacées par son adresse. L'application par "
"défaut du système sera utilisée si aucun protocole n'a pas été configuré."

#: pynicotine/gtkgui/ui/settings/urlhandlers.ui:38
msgid "File manager command:"
msgstr "Commande du gestionnaire de fichiers :"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:5
#: pynicotine/gtkgui/ui/settings/userinfo.ui:21
msgid "Reset Picture"
msgstr "Réinitialiser l’image"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:40
msgid "Self Description"
msgstr "Présentation"

#: pynicotine/gtkgui/ui/settings/userinfo.ui:53
msgid ""
"Add things you want everyone to see, such as a short description, helpful "
"tips, or guidelines for downloading your shares."
msgstr ""
"Ajoutez des choses que vous voulez que tout le monde voie, comme une "
"description courte, des conseils utiles ou des lignes directrices pour "
"télécharger vos fichiers."

#: pynicotine/gtkgui/ui/settings/userinfo.ui:89
msgid "Picture:"
msgstr "Image :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:49
msgid "Prefer dark mode"
msgstr "Privilégier le mode sombre"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:73
msgid "Use header bar"
msgstr "Utiliser _Header Bar"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:101
msgid "Display tray icon"
msgstr "Activer l'icône dans la zone de notification"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:131
msgid "Minimize to tray on startup"
msgstr "Minimiser dans la zone de notification au démarrage"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:159
msgid "Language (requires a restart):"
msgstr "Langue (nécessite un redémarrage) :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:175
msgid "When closing window:"
msgstr "Lors de la fermeture de Nicotine+ :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:194
msgid "Notifications"
msgstr "Notifications"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:212
msgid "Enable sound for notifications"
msgstr "Activer le son des notifications"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:236
msgid "Show notification for private chats and mentions in the window title"
msgstr ""
"Afficher les notifications pour les discussions privées et les mentions dans "
"le titre de la fenêtre"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:268
msgid "Show notifications for:"
msgstr "Afficher les notification pour :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:295
msgid "Finished file downloads"
msgstr "Fichiers téléchargés"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:307
msgid "Finished folder downloads"
msgstr "Dossiers téléchargés"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:319
msgid "Private messages"
msgstr "Messages privés"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:331
msgid "Chat room messages"
msgstr "Messages des salons"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:343
msgid "Chat room mentions"
msgstr "Mentions dans les salons"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:355
msgid "Wishlist results found"
msgstr "Résultats trouvés depuis la liste de souhaits"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:398
msgid "Restore the previously active main tab at startup"
msgstr "Restaurer le dernier onglet ouvert au démarrage"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:422
msgid "Close-buttons on secondary tabs"
msgstr "Boutons de fermeture sur les onglets secondaires"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:446
msgid "Regular tab label color:"
msgstr "Couleur de l'étiquette de l'onglet normal :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:486
msgid "Changed tab label color:"
msgstr "Changement de la couleur de l'étiquette de l'onglet :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:526
msgid "Highlighted tab label color:"
msgstr "Couleur de l'étiquette de l'onglet en surbrillance :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:567
msgid "Buddy list position:"
msgstr "Position de la liste d'amis :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:593
msgid "Visible main tabs:"
msgstr "Onglets principaux visibles :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:749
msgid "Tab bar positions:"
msgstr "Positions de la barre d'onglets :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:784
msgid "Main tabs"
msgstr "Onglets principaux"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:942
msgid "Show reverse file paths (requires a restart)"
msgstr "Afficher les chemins de fichiers inversés (nécessite un redémarrage)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:966
msgid "Show exact file sizes (requires a restart)"
msgstr "Afficher la taille exacte des fichiers (nécessite un redémarrage)"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:990
msgid "List text color:"
msgstr "Couleur du texte de la liste :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1051
msgid "Enable colored usernames"
msgstr "Activer les noms d'utilisateurs colorés"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1075
msgid "Chat username appearance:"
msgstr "Apparence du nom d’utilisateur dans les discussions :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1092
msgid "Remote text color:"
msgstr "Couleur du texte distant :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1132
msgid "Local text color:"
msgstr "Couleur du texte local :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1172
msgid "Command output text color:"
msgstr "Couleur du texte de sortie de la commande :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1212
msgid "/me action text color:"
msgstr "Couleur du texte d’action /me :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1252
msgid "Highlighted text color:"
msgstr "Couleur du texte en surbrillance :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1292
msgid "URL link text color:"
msgstr "Couleur du lien URL :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1335
msgid "User Statuses"
msgstr "Statuts des utilisateurs"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1352
msgid "Online color:"
msgstr "Couleur en ligne :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1392
msgid "Away color:"
msgstr "Couleur du texte d'absence :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1432
msgid "Offline color:"
msgstr "Couleur hors ligne :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1475
msgid "Text Entries"
msgstr "Saisies de texte"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1492
msgid "Text entry background color:"
msgstr "Couleur de fond de la saisie de texte :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1532
msgid "Text entry text color:"
msgstr "Couleur du texte de la saisie :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1575
msgid "Fonts"
msgstr "Polices"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1592
msgid "Global font:"
msgstr "Police par défaut :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1640
msgid "List font:"
msgstr "Police des listes :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1688
msgid "Text view font:"
msgstr "Police d’affichage du texte :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1736
msgid "Chat font:"
msgstr "Police des discussions :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1784
msgid "Transfers font:"
msgstr "Police des transferts :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1832
msgid "Search font:"
msgstr "Police des recherches :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1880
msgid "Browse font:"
msgstr "Police de navigation :"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1932
msgid "Icons"
msgstr "Icônes"

#: pynicotine/gtkgui/ui/settings/userinterface.ui:1949
msgid "Icon theme folder:"
msgstr "Dossier des icônes du thème :"

#: pynicotine/gtkgui/ui/uploads.ui:86
msgid "Abort User(s)"
msgstr "Abandonner l’utilisateur (s)"

#: pynicotine/gtkgui/ui/uploads.ui:116
msgid "Ban User(s)"
msgstr "Bannir ce(s) utilisateur(s)"

#: pynicotine/gtkgui/ui/uploads.ui:138
msgid "Clear All Finished/Cancelled Uploads"
msgstr "Effacer tous les envois terminés / annulés"

#: pynicotine/gtkgui/ui/uploads.ui:169 pynicotine/gtkgui/ui/uploads.ui:184
msgid "Message All"
msgstr "Envoyer un message à tous"

#: pynicotine/gtkgui/ui/uploads.ui:199
msgid "Clear Specific Uploads"
msgstr "Effacer des envois"

#: pynicotine/gtkgui/ui/userbrowse.ui:161
msgid "Save Shares List to Disk"
msgstr "Sauvegarder la liste des partages sur le disque"

#: pynicotine/gtkgui/ui/userbrowse.ui:178
msgid "Refresh Files"
msgstr "Actualiser les fichiers"

#: pynicotine/gtkgui/ui/userinfo.ui:101
msgid "Edit Profile"
msgstr "Modifier le profil"

#: pynicotine/gtkgui/ui/userinfo.ui:149
msgid "Shared Files"
msgstr "Fichiers partagés"

#: pynicotine/gtkgui/ui/userinfo.ui:203
msgid "Upload Speed"
msgstr "Vitesse de téléchargement"

#: pynicotine/gtkgui/ui/userinfo.ui:230
msgid "Free Upload Slots"
msgstr "Créneau de téléchargement libre"

#: pynicotine/gtkgui/ui/userinfo.ui:284
msgid "Queued Uploads"
msgstr "Téléchargements en file d'attente"

#: pynicotine/gtkgui/ui/userinfo.ui:354
msgid "Edit Interests"
msgstr "Modifier les centres d'intérêt"

#: pynicotine/gtkgui/ui/userinfo.ui:624
msgid "_Gift Privileges…"
msgstr "_Donner des privilèges…"

#: pynicotine/gtkgui/ui/userinfo.ui:663
msgid "_Refresh Profile"
msgstr "_Actualiser le profil"

#: pynicotine/plugins/core_commands/PLUGININFO:3
msgid "Nicotine+ Commands"
msgstr "Commandes Nicotine+"

#, fuzzy
#~ msgid "Nicotine+"
#~ msgstr "L'équipe Nicotine+"

#~ msgid "Listening port (requires a restart):"
#~ msgstr "Port d'écoute ( redémarrage requis) :"

#~ msgid "Network interface (requires a restart):"
#~ msgstr "Interface réseau (redémarrage requis) :"

#~ msgid "Invalid Password"
#~ msgstr "Mot de passe non valide"

#~ msgid "Change _Login Details"
#~ msgstr "Modifier _les informations de connexion"

#, python-format
#~ msgid "%i privileged users"
#~ msgstr "%i utilisateurs privilégiés"

#~ msgid "_Set Up…"
#~ msgstr "_Configurer…"

#~ msgid "Queued search result text color:"
#~ msgstr "Couleur du texte des recherches en attente :"

#, python-format
#~ msgid "Failed to fetch the shared folder %(folder)s: %(error)s"
#~ msgstr "Impossible de récupérer le dossier partagé %(folder)s : %(error)s"

#~ msgid "_Clear"
#~ msgstr "Effa_cer"

#, python-format
#~ msgid "Can't save %(filename)s: %(error)s"
#~ msgstr "Impossible de sauvegarder %(filename)s : %(error)s"

#~ msgid "Trusted Buddies"
#~ msgstr "Amis de confiance"

#~ msgid "Quit program"
#~ msgstr "Quitter le programme"

#~ msgid "Username:"
#~ msgstr "Nom d’utilisateur :"

#~ msgid "Re_commendations for Item"
#~ msgstr "Re_commandations pour l'article"

#~ msgid "_Remove Item"
#~ msgstr "_Retirer l'élément"

#~ msgid "_Remove"
#~ msgstr "Enleve_r"

#~ msgid "Send M_essage"
#~ msgstr "Envoyer M_essage"

#~ msgid "Send Message"
#~ msgstr "Envoyer un message"

#~ msgid "View User Profile"
#~ msgstr "Voir le profil de l'utilisateur"

#~ msgid "Start Messaging"
#~ msgstr "Démarrer la messagerie"

#~ msgid "Enter the name of the user whom you want to send a message:"
#~ msgstr ""
#~ "Saisissez le nom de l'utilisateur à qui vous voulez envoyer un message :"

#~ msgid "_Message"
#~ msgstr "_Message"

#~ msgid "Enter the name of the user whose profile you want to see:"
#~ msgstr ""
#~ "Saisissez le nom de l'utilisateur dont vous souhaitez voir le profil :"

#~ msgid "_View Profile"
#~ msgstr "_Voir le profil"

#~ msgid "Enter the name of the user whose shares you want to see:"
#~ msgstr ""
#~ "Saisissez le nom d'un utilisateur dont vous souhaitez recevoir la liste "
#~ "du partage :"

#~ msgid "_Browse"
#~ msgstr "_Parcourir"

#~ msgid "Password"
#~ msgstr "Mot de passe"

#~ msgid "Download File"
#~ msgstr "Télécharger le fichier"

#~ msgid "Refresh Similar Users"
#~ msgstr "Rafraichir les utilisateurs similaires"

#~ msgid "Enter the username of the person whose files you want to see"
#~ msgstr ""
#~ "Saisissez le nom d'utilisateur de la personne dont vous souhaitez voir "
#~ "les fichiers"

#~ msgid "Enter the username of the person whose information you want to see"
#~ msgstr ""
#~ "Entrez le nom d'utilisateur de la personne dont vous voulez voir les "
#~ "informations"

#~ msgid "Enter the username of the person you want to send a message to"
#~ msgstr ""
#~ "Saisissez le nom d'utilisateur de la personne à qui vous souhaitez "
#~ "envoyer un message"

#~ msgid "Enter the username of the person you want to add to your buddy list"
#~ msgstr ""
#~ "Entrez le nom d’utilisateur que vous souhaitez ajouter à votre liste "
#~ "d’amis"

#~ msgid ""
#~ "Enter the name of a room you want to join. If the room doesn't exist, it "
#~ "will be created."
#~ msgstr ""
#~ "Saisissez le nom du salon que vous souhaitez rejoindre. Si le salon "
#~ "n'existe pas, celui-ci sera créé."

#~ msgid "Show Log History Pane"
#~ msgstr "Afficher le volet de l'historique du journal"

#~ msgid "Save _Picture"
#~ msgstr "Sauvegarder l'_image"

#, python-format
#~ msgid ""
#~ "Filtered out incorrect search result %(filepath)s from user %(user)s for "
#~ "search query \"%(query)s\""
#~ msgstr ""
#~ "Filtrage des résultats de recherche incorrects %(filepath)s de "
#~ "l'utilisateur %(user)s pour la requête de recherche \"%(query)s\""

#~ msgid ""
#~ "Certain clients don't send search results if special characters are "
#~ "included."
#~ msgstr ""
#~ "Certains clients n'envoient pas les résultats de recherche si ces "
#~ "derniers comportent des caractères spéciaux."

#~ msgid "Remove special characters from search terms"
#~ msgstr "Supprimer les caractères spéciaux des termes de recherche"

#, python-format
#~ msgid "Trouble executing '%s'"
#~ msgstr "Problème lors de l'exécution de '%s'"

#, python-format
#~ msgid "Trouble executing on folder: %s"
#~ msgstr "Problème lors de l'exécution sur le dossier  : %s"

#~ msgid "Disallowed extension"
#~ msgstr "Extension non autorisée"

#~ msgid "Too many files"
#~ msgstr "Trop de fichiers"

#~ msgid "Too many megabytes"
#~ msgstr "Trop de mégaoctets"

#~ msgid "Server does not permit performing wishlist searches at this time"
#~ msgstr ""
#~ "Le serveur ne permet pas d'effectuer des recherches de listes de souhaits "
#~ "pour le moment"

#~ msgid ""
#~ "File transfer speeds depend on users you are downloading from. Certain "
#~ "users will be faster, while others will be slow."
#~ msgstr ""
#~ "La vitesse de transfert des fichiers dépend des utilisateurs à partir "
#~ "desquels vous effectuez le téléchargement. Certains utilisateurs seront "
#~ "plus rapides, tandis que d'autres seront lents."

#~ msgid "Started Downloads"
#~ msgstr "Téléchargements démarrés"

#~ msgid "Started Uploads"
#~ msgstr "Envois démarrés"

#~ msgid "Replace censored letters with:"
#~ msgstr "Remplace les lettres censurées par  :"

#~ msgid "Censored Patterns"
#~ msgstr "Motifs censurés"

#~ msgid "Replacements"
#~ msgstr "Remplacements"

#~ msgid ""
#~ "If a user on the Soulseek network searches for a file that exists in your "
#~ "shares, search results will be sent to the user."
#~ msgstr ""
#~ "Si un utilisateur du réseau Soulseek recherche un fichier qui existe dans "
#~ "vos partages, les résultats de la recherche seront envoyés à "
#~ "l'utilisateur."

#~ msgid "Send to Player"
#~ msgstr "Envoyer au lecteur"

#~ msgid "Send to _Player"
#~ msgstr "Envoyer au _lecteur"

#~ msgid "_Open in File Manager"
#~ msgstr "_Ouvrir dans le gestionnaire de fichiers"

#~ msgid "Media player command:"
#~ msgstr "Commande du lecteur multimédia :"

#, python-format
#~ msgid ""
#~ "Unable to save download to username subfolder, falling back to default "
#~ "download folder. Error: %s"
#~ msgstr ""
#~ "Impossible d'enregistrer le téléchargement dans le sous-dossier du nom "
#~ "d'utilisateur, retour au dossier de téléchargement par défaut. Erreur : %s"

#~ msgid "Buddy-only"
#~ msgstr "Pour amis uniquement"

#~ msgid "Share with buddies only"
#~ msgstr "Partager avec des copains seulement"

#~ msgid "_Quit…"
#~ msgstr "_Quitter…"

#~ msgid "_Configure Shares"
#~ msgstr "_Configurer les partages"

#~ msgid "Remote file error"
#~ msgstr "Erreur de fichier distant"

#, python-format
#~ msgid "Cannot find %(option1)s or %(option2)s, please install either one."
#~ msgstr ""
#~ "Impossible de trouver %(option1)s ou %(option2)s, veuillez installer l'un "
#~ "ou l'autre."

#, python-format
#~ msgid "Failed to process the following databases: %(names)s"
#~ msgstr "Échec du traitement des bases de données suivantes : %(names)s"

#, python-format
#~ msgid "Failed to send number of shared files to the server: %s"
#~ msgstr "Échec d'envoi du nombre de fichiers partagés au serveur : %s"

#~ msgid "Quit / Run in Background"
#~ msgstr "Quitter/exécuter en arrière-plan"

#~ msgid "Limit buddy-only shares to trusted buddies"
#~ msgstr "Limiter les partages aux amis de confiance"

#~ msgid "Shared"
#~ msgstr "Partagé"

#~ msgid "Search Files and Folders"
#~ msgstr "Rechercher dans les fichiers et dossiers"

#~ msgid "Out of Date"
#~ msgstr "Non à jour"

#, python-format
#~ msgid "Version %(version)s is available, released on %(date)s"
#~ msgstr "La version %(version)s est disponible, publiée le %(date)s"

#, python-format
#~ msgid "You are using a development version of %s"
#~ msgstr "Vous utilisez une version de développement de %s"

#, python-format
#~ msgid "You are using the latest version of %s"
#~ msgstr "Vous utilisez la dernière version de %s"

#~ msgid "Latest Version Unknown"
#~ msgstr "Dernière version inconnue"

#~ msgid "Prefer Dark _Mode"
#~ msgstr "Privilégier le mode _sombre"

#~ msgid "Show _Log History Pane"
#~ msgstr "Afficher l'historique des _journaux"

#~ msgid "Buddy List in Separate Tab"
#~ msgstr "Liste d'amis dans un onglet séparé"

#~ msgid "Buddy List Always Visible"
#~ msgstr "Liste d'amis toujours visible"

#~ msgid "_View"
#~ msgstr "_Affichage"

#~ msgid "_Open Log Folder"
#~ msgstr "_Ouvrir le dossier des journaux"

#~ msgid "_Browse Folder(s)"
#~ msgstr "_Parcourir le(s) répertoire(s)"

#~ msgid "Kibibytes (2^10 bytes) per second."
#~ msgstr "Kibioctets (2^10 octets) par seconde."

#~ msgid "Sent to users as the reason for being geo blocked."
#~ msgstr "Envoyé aux utilisateurs la raison du géoblocage."

#~ msgid "Sent to users as the reason for being banned."
#~ msgstr ""
#~ "Envoyer aux utilisateurs la raison pour laquelle ils ont été bannis."

#~ msgid ""
#~ "Once you interact with the application being away, status will be set to "
#~ "online."
#~ msgstr ""
#~ "Dès que vous interagissez avec l'application en étant absent, votre "
#~ "statut sera à \"en ligne\"."

#~ msgid "Each user may queue a maximum of either:"
#~ msgstr "Chaque utilisateur peut mettre en file d'attente un maximum de :"

#~ msgid "Mebibytes (2^20 bytes)."
#~ msgstr "Mebibytes (2^20 octets)."

#~ msgid "MiB"
#~ msgstr "MiB"

#~ msgid "files"
#~ msgstr "fichiers"

#~ msgid "Queue Behavior"
#~ msgstr "Comportement de la file d'attente"

#~ msgid ""
#~ "If disabled, slots will automatically be determined by available "
#~ "bandwidth limitations."
#~ msgstr ""
#~ "S'il est désactivé, les créneaux seront automatiquement déterminés par "
#~ "les limites de la bande passante disponible."

#~ msgid "Note that the operating system's theme may take precedence."
#~ msgstr ""
#~ "Notez que le thème du système d'exploitation peut avoir la priorité."

#~ msgid "By default the leftmost tab is activated at startup"
#~ msgstr "Par défaut, l'onglet le plus à gauche est ouvert au démarrage"

#, python-format
#~ msgid "Quit %(program)s %(version)s, %(status)s!"
#~ msgstr "Arrêter %(program)s %(version)s, %(status)s !"

#~ msgid "terminated"
#~ msgstr "terminé"

#~ msgid "done"
#~ msgstr "fait"

#~ msgid "Remember choice"
#~ msgstr "Mémoriser ce choix"

#~ msgid "Kosovo"
#~ msgstr "Kosovo"

#~ msgid "Unknown Network Interface"
#~ msgstr "Interface réseau inconnue"

#~ msgid "Listening Port Unavailable"
#~ msgstr "Port non disponible"

#, python-format
#~ msgid ""
#~ "The server seems to be down or not responding, retrying in %i seconds"
#~ msgstr "Le serveur ne répond pas, nouvelle tentative dans %i secondes"

#~ msgid ""
#~ "Nicotine+ uses peer-to-peer networking to connect to other users. In "
#~ "order to allow users to connect to you without trouble, an open listening "
#~ "port is crucial."
#~ msgstr ""
#~ "Nicotine+ utilise un réseau pair-à-pair pour se connecter aux autres "
#~ "utilisateurs. Afin de permettre aux utilisateurs de se connecter à vous "
#~ "sans problème, un port d'écoute ouvert est crucial."

#~ msgid "--- disconnected ---"
#~ msgstr "--- déconnecté ---"

#~ msgid "--- reconnected ---"
#~ msgstr "--- reconnecté ---"

#~ msgid "ID"
#~ msgstr "Identifiant"

#~ msgid "Earth"
#~ msgstr "Terre"

#~ msgid "Czech Republic"
#~ msgstr "République tchèque"

#~ msgid "Turkey"
#~ msgstr "Turquie"

#~ msgid "Joined Rooms "
#~ msgstr "Salons joints "

#~ msgid "_Auto-join Room"
#~ msgstr "Rejoindre automatiquement le salon"

#~ msgid "Escaped"
#~ msgstr "Sélection des filtres (echap.)"

#~ msgid "Escape filter"
#~ msgstr "Filtre d’échappement"

#~ msgid "Enter a text pattern and what to replace it with"
#~ msgstr "Entrez le modèle du texte et son remplacement"

#, python-format
#~ msgid "%(num)s folders found before rescan, rebuilding…"
#~ msgstr "%(num)s répertoires trouvés avant réexamen, reconstruction…"

#, python-format
#~ msgid "No listening port is available in the specified port range %s–%s"
#~ msgstr "Aucun port n'est disponible dans la plage de ports spécifiée %s–%s"

#~ msgid ""
#~ "Choose a range to select a listening port from. The first available port "
#~ "in the range will be used."
#~ msgstr ""
#~ "Choisissez une plage à partir de laquelle sélectionner un port d'écoute. "
#~ "Le premier port disponible de la plage sera utilisé."

#~ msgid "First Port"
#~ msgstr "Premier port"

#~ msgid "to"
#~ msgstr "à"

#~ msgid "Last Port"
#~ msgstr "Dernier port"

#, python-format
#~ msgid "Cannot find %s or newer, please install it."
#~ msgstr "Impossible de trouver %s ou plus récent, veuillez l'installer."

#~ msgid ""
#~ "Cannot import the Gtk module. Bad install of the python-gobject module?"
#~ msgstr ""
#~ "Impossible d'importer le module Gtk. Mauvaise installation du module "
#~ "python-gobject ?"

#, python-format
#~ msgid ""
#~ "You are using an unsupported version of GTK %(major_version)s. You should "
#~ "install GTK %(complete_version)s or newer."
#~ msgstr ""
#~ "Vous utilisez une version non supportée de GTK %(major_version)s. Vous "
#~ "devriez installer GTK %(complete_version)s ou plus récent."

#~ msgid "User Info"
#~ msgstr "Informations sur l’utilisateur"

#~ msgid "Zoom 1:1"
#~ msgstr "Zoom 1:1"

#~ msgid "Zoom In"
#~ msgstr "Zoom avant"

#~ msgid "Zoom Out"
#~ msgstr "Zoom arrière"

#~ msgid "Show User I_nfo"
#~ msgstr "Afficher les i_nformation utilisateur"

#~ msgid "Request User's Info"
#~ msgstr "Demande d'informations sur l'utilisateur"

#~ msgid "Request User's Shares"
#~ msgstr "Demander les partages de l'utilisateur"

#~ msgid "Request User Info"
#~ msgstr "Demander des informations sur l'utilisateur"

#~ msgid "Enter the name of the user whose info you want to see:"
#~ msgstr ""
#~ "Saisissez le nom de l'utilisateur dont vous souhaitez voir les "
#~ "informations :"

#~ msgid "Request Shares List"
#~ msgstr "Demander la liste du partage"

#, python-format
#~ msgid "Invalid Soulseek URL: %s"
#~ msgstr "URL Soulseek non valide : %s"

#~ msgid ""
#~ "Users on the Soulseek network will be able to download files from folders "
#~ "you share. Sharing files is crucial for the health of the Soulseek "
#~ "network."
#~ msgstr ""
#~ "Les utilisateurs du réseau Soulseek pourront télécharger des fichiers à "
#~ "partir des dossiers que vous partagez. Le partage des fichiers est "
#~ "crucial pour la santé du réseau Soulseek."

#~ msgid "Update I_nfo"
#~ msgstr "Mise à jour de l'I_nfo"

#~ msgid "Chat Room Commands"
#~ msgstr "Commandes des salons de discussion"

#~ msgid "/join /j 'room'"
#~ msgstr "/join /j 'salon'"

#~ msgid "Join room 'room'"
#~ msgstr "Rejoindre le salon 'salon'"

#~ msgid "/me 'message'"
#~ msgstr "/me 'message'"

#~ msgid "Display the Now Playing script's output"
#~ msgstr "Afficher la sortie du script du Now Playing"

#~ msgid "/add /ad 'user'"
#~ msgstr "/add /ad 'utilisateur'"

#~ msgid "Add user 'user' to your buddy list"
#~ msgstr "Ajoutez l'utilisateur 'user' à votre liste d'amis"

#~ msgid "/rem /unbuddy 'user'"
#~ msgstr "/rem /unbuddy 'utilisateur'"

#~ msgid "Remove user 'user' from your buddy list"
#~ msgstr "Supprimez l'utilisateur 'user' de votre liste d'amis"

#~ msgid "/ban 'user'"
#~ msgstr "/ban 'utilisateur'"

#~ msgid "Add user 'user' to your ban list"
#~ msgstr "Ajoute l'utilisateur 'utilisateur' à votre liste de bannissement"

#~ msgid "/unban 'user'"
#~ msgstr "/unban 'utilisateur'"

#~ msgid "Remove user 'user' from your ban list"
#~ msgstr "Retire l'utilisateur 'utilisateur' de votre liste de bannissement"

#~ msgid "/ignore 'user'"
#~ msgstr "/ignore 'utilisateur'"

#~ msgid "Add user 'user' to your ignore list"
#~ msgstr "Ajoute l'utilisateur 'utilisateur' à votre liste d'ignorance"

#~ msgid "/unignore 'user'"
#~ msgstr "/unignore 'utilisateur'"

#~ msgid "Remove user 'user' from your ignore list"
#~ msgstr "Retire l'utilisateur 'utilisateur' de votre liste d'ignorance"

#~ msgid "/browse /b 'user'"
#~ msgstr "/browse /b 'utilisateur'"

#~ msgid "/whois /w 'user'"
#~ msgstr "/whois /w 'utilisateur'"

#~ msgid "Request info for 'user'"
#~ msgstr "Demande d'informations pour \"user\""

#~ msgid "/ip 'user'"
#~ msgstr "/ip 'utilisateur'"

#~ msgid "Show IP for user 'user'"
#~ msgstr "Montre l'IP de l'utilisateur 'utilisateur'"

#~ msgid "/search /s 'query'"
#~ msgstr "/search /s 'requête'"

#~ msgid "Start a new search for 'query'"
#~ msgstr "Démarrer une nouvelle recherche pour 'requête'"

#~ msgid "/rsearch /rs 'query'"
#~ msgstr "/rsearch /rs 'requête'"

#~ msgid "Search the joined rooms for 'query'"
#~ msgstr "Rechercher 'requête' dans les salons rejoints"

#~ msgid "/bsearch /bs 'query'"
#~ msgstr "/bsearch /bs 'requête'"

#~ msgid "Search the buddy list for 'query'"
#~ msgstr "Recherche 'requête' dans sa liste d'amis"

#~ msgid "/usearch /us 'user' 'query'"
#~ msgstr "/usearch /us 'utilisateur' 'requête'"

#~ msgid "/msg 'user' 'message'"
#~ msgstr "/msg 'utilisateur' 'message'"

#~ msgid "Send message 'message' to user 'user'"
#~ msgstr "Envoie le message 'message' à l'utilisateur 'utilisateur'"

#~ msgid "/pm 'user'"
#~ msgstr "/pm 'utilisateur'"

#~ msgid "Open private chat window for user 'user'"
#~ msgstr ""
#~ "Ouvre une fenêtre de dialogue privé avec l'utilisateur 'utilisateur'"

#~ msgid "Private Chat Commands"
#~ msgstr "Commandes des salons privés"

#~ msgid "Add user to your ban list"
#~ msgstr "Ajouter l'utilisateur à votre liste de bannissement"

#~ msgid "Add user to your ignore list"
#~ msgstr "Ajouter l'utilisateur à votre liste d'utilisateurs ignorés"

#~ msgid "Browse shares of user"
#~ msgstr "Parcourir les partages de l'utilisateur"

#~ msgid ""
#~ "For order-insensitive filtering, as well as filtering several exact "
#~ "phrases, vertical bars can be used to separate phrases and words.\n"
#~ "    Example: Remix|Instrumental|Dub Mix"
#~ msgstr ""
#~ "Pour le filtrage insensible à l'ordre, ainsi que pour le filtrage de "
#~ "plusieurs phrases exactes, des barres verticales peuvent être utilisées "
#~ "pour séparer les phrases et les mots.\n"
#~ "  Exemple : Remix|Instrumental|Dub Mix"

#~ msgid "To exclude non-audio files use !0 in the duration filter."
#~ msgstr ""
#~ "Pour exclure les fichiers non audio, utilisez !0 dans le filtre de durée."

#~ msgid "Filters files based upon users' geographical location."
#~ msgstr ""
#~ "Filtre les fichiers suivant la position géographique des utilisateurs."

#~ msgid "To view the full results again, simply clear all active filters."
#~ msgstr ""
#~ "Pour afficher à nouveau les résultats complets, effacez simplement tous "
#~ "les filtres actifs."

#~ msgid "See the preferences to set default search result filter options."
#~ msgstr ""
#~ "Consultez les préférences pour définir les options de filtrage des "
#~ "résultats de recherche."

#~ msgid "File size"
#~ msgstr "Taille du fichier"

#~ msgid "Clear Active Filters"
#~ msgstr "Effacer les filtres actifs"

#~ msgid "Cycle through completions when pressing tab-key"
#~ msgstr ""
#~ "Faire défiler les complétions en appuyant sur la touche de tabulation"

#~ msgid "Hide drop-down when only one matches"
#~ msgstr "Cacher la liste déroulante lors d'un seul choix"

#~ msgid "Download folders in reverse alphanumerical order"
#~ msgstr "Télécharger les répertoires en ordre décroissant"

#~ msgid "Incomplete file folder:"
#~ msgstr "Dossier incomplet :"

#~ msgid "Download folder:"
#~ msgstr "Dossier de téléchargement :"

#~ msgid "Save buddies' uploads to:"
#~ msgstr "Enregistrez les téléchargements de vos amis vers :"

#~ msgid "Use UPnP to forward listening port"
#~ msgstr "Utilisez UPnP pour rediriger le port d'écoute"

#~ msgid ""
#~ "Share folders with every Soulseek user or buddies, allowing contents to "
#~ "be downloaded directly from your device. Hidden files are never shared."
#~ msgstr ""
#~ "Partagez vos dossiers avec tous les utilisateurs de Soulseek ou avec vos "
#~ "amis, ce qui permet de télécharger les contenus directement depuis votre "
#~ "appareil. Les fichiers cachés ne sont jamais partagés."

#~ msgid "Secondary Tabs"
#~ msgstr "Onglets secondaires"

#~ msgid "Chat room tab bar position:"
#~ msgstr "Position de la barre d'onglets du salon de discussion  :"

#~ msgid "Private chat tab bar position:"
#~ msgstr "Position de la barre d'onglet de discussion privé :"

#~ msgid "Search tab bar position:"
#~ msgstr "Position de la barre d'onglets de recherche :"

#~ msgid "User info tab bar position:"
#~ msgstr "Position de la barre d'onglet des informations utilisateur :"

#~ msgid "User browse tab bar position:"
#~ msgstr "Position de la barre d'onglets du navigateur de l'utilisateur :"

#~ msgid "Tab Labels"
#~ msgstr "Étiquettes d'onglets"

#~ msgid "_Refresh Info"
#~ msgstr "_Rafraîchir les informations"

#~ msgid "Block IP Address"
#~ msgstr "Bloquer l'adresse IP"

#~ msgid "Connected"
#~ msgstr "Connecté"

#~ msgid "Disconnected"
#~ msgstr "Déconnecté"

#~ msgid "Disconnected (Tray)"
#~ msgstr "Déconnecté (zone de notification)"

#~ msgid "User(s)"
#~ msgstr "Utilisateur(s)"

#, python-format
#~ msgid "Alias \"%s\" returned nothing"
#~ msgstr "L'alias \"%s\" n'as rien retourné"

#~ msgid "Alternative Speed Limits"
#~ msgstr "Limites de vitesse alternatives"

#~ msgid "Last played"
#~ msgstr "Joué pour la dernière fois"

#~ msgid "Playing now"
#~ msgstr "En cours de lecture"

#, python-format
#~ msgid "Cannot download file to %(path)s: %(error)s"
#~ msgstr "Impossible de télécharger le fichier %(path)s : %(error)s"

#, python-format
#~ msgid "Error code %(code)s: %(description)s"
#~ msgstr "Code d'erreur %(code)s : %(description)s"

#, python-format
#~ msgid "No such alias (%s)"
#~ msgstr "Ne trouve pas l'alias (%s)"

#~ msgid "Aliases:"
#~ msgstr "Alias :"

#, python-format
#~ msgid "Removed alias %(alias)s: %(action)s\n"
#~ msgstr "Alias %(alias)s enlevé : %(action)s\n"

#, python-format
#~ msgid "No such alias (%(alias)s)\n"
#~ msgstr "Ne trouve pas l'alias (%(alias)s)\n"

#~ msgid "Use Alternative Transfer Speed Limits"
#~ msgstr "Utiliser les limites de vitesse alternatives"

#~ msgid "Aliases"
#~ msgstr "Alias"

#~ msgid "/alias /al 'command' 'definition'"
#~ msgstr "/alias /al 'commande' 'définition'"

#~ msgid "Add a new alias"
#~ msgstr "Ajouter un nouvel alias"

#~ msgid "/unalias /un 'command'"
#~ msgstr "/unalias /un 'commande'"

#~ msgid "Remove an alias"
#~ msgstr "Enlever un alias"

#~ msgid "Chat History"
#~ msgstr "Historique de discussions"

#~ msgid "Command aliases"
#~ msgstr "Alias des commandes"

#~ msgid "Limit download speed to (KiB/s):"
#~ msgstr "Limiter la vitesse de téléchargement à (Kio/s) :"

#~ msgid "Author(s):"
#~ msgstr "Auteur(s) :"

#~ msgid "Limit upload speed to (KiB/s):"
#~ msgstr "Limitez la vitesse d'envoi à (KiB/s) :"

#~ msgid "Wishlist item found"
#~ msgstr "Élément de la liste de souhaits trouvé"

#~ msgid "Tabs show user status icons instead of status text"
#~ msgstr ""
#~ "Les onglets affichent les icônes d'état de l'utilisateur au lieu du texte"

#~ msgid "Show file path tooltips in file list views"
#~ msgstr ""
#~ "Afficher les chemins des fichiers en info-bulles dans la liste des "
#~ "fichiers"

#~ msgid "Colored and clickable usernames"
#~ msgstr "Noms d'utilisateurs colorés et cliquables"

#~ msgid "Notification changes the tab's text color"
#~ msgstr "La notification change la couleur du texte des onglets"

#~ msgid "Cancel"
#~ msgstr "Annuler"

#~ msgid "OK"
#~ msgstr "Valider"

#~ msgid "_Add to Buddy List"
#~ msgstr "_Ajouter à la liste d'amis"

#~ msgid "use non-default user data directory for e.g. list of downloads"
#~ msgstr ""
#~ "utiliser un répertoire de données utilisateur spécifique pour, par "
#~ "exemple, la liste des téléchargements"

#, python-format
#~ msgid "Unknown config section '%s'"
#~ msgstr "Section de configuration inconnue « %s »"

#, python-format
#~ msgid "Unknown config option '%(option)s' in section '%(section)s'"
#~ msgstr ""
#~ "Option de configuration inconnue « %(option)s » dans la section « "
#~ "%(section)s »"

#, python-format
#~ msgid "Version %s is available"
#~ msgstr "Version %s disponible"

#, python-format
#~ msgid "released on %s"
#~ msgstr "publiée le %s"

#~ msgid "Nicotine+ is running in the background"
#~ msgstr "Nicotine+ tourne en tâche de fond"

#, python-format
#~ msgid "Private message from %s"
#~ msgstr "Message privé de %s"

#~ msgid "Aborted"
#~ msgstr "Annulé"

#~ msgid "Blocked country"
#~ msgstr "Pays bloqué"

#~ msgid "Finished / Aborted"
#~ msgstr "Terminés/annulés"

#~ msgid "Close tab"
#~ msgstr "Fermer l'onglet"

#, python-format
#~ msgid "You've been mentioned in the %(room)s room"
#~ msgstr "Vous avez été mentionné dans le salon %(room)s"

#, python-format
#~ msgid "Command %s is not recognized"
#~ msgstr "La commande %s n'est pas reconnue"

#, python-format
#~ msgid "(Warning: %(realuser)s is attempting to spoof %(fakeuser)s) "
#~ msgstr ""
#~ "(Avertissement : %(realuser)s tente d'usurper l'identité de %(fakeuser)s) "

#, python-format
#~ msgid ""
#~ "The network interface you specified, '%s', does not exist. Change or "
#~ "remove the specified network interface and restart Nicotine+."
#~ msgstr ""
#~ "L'interface réseau que vous avez spécifiée, '%s', n'existe pas. Modifier "
#~ "ou supprimer l'interface réseau spécifiée et redémarrer Nicotine+."

#~ msgid ""
#~ "The range you specified for client connection ports was {}-{}, but none "
#~ "of these were usable. Increase and/or "
#~ msgstr ""
#~ "L'intervalle de ports pour les connexions client que vous avez spécifié "
#~ "était {}-{}, mais aucun d'entre eux n'est disponible. "

#~ msgid ""
#~ "Note that part of your range lies below 1024, this is usually not allowed "
#~ "on most operating systems with the exception of Windows."
#~ msgstr ""
#~ "Notez qu'une partie de votre plage se situe en dessous de 1024. Cela "
#~ "n'est généralement pas autorisé sauf sous Windows."

#, python-format
#~ msgid "Rescan progress: %s"
#~ msgstr "Progression de la réanalyse : %s"

#, python-format
#~ msgid "OS error: %s"
#~ msgstr "Erreur du système d'exploitation  : %s"

#~ msgid "UPnP is not available on this network"
#~ msgstr "UPnP n'est pas disponible sur ce réseau"

#, python-format
#~ msgid "Failed to map the external WAN port: %(error)s"
#~ msgstr "Échec de redirection du port WAN externe  : %(error)s"

#~ msgid "Room wall"
#~ msgstr "Mur du salon"

#~ msgid ""
#~ "The default listening port '2234' works fine in most cases. If you need "
#~ "to use a different port, you will be able to modify it in the preferences "
#~ "later."
#~ msgstr ""
#~ "Le port d'écoute par défaut '2234' fonctionne bien dans la plupart des "
#~ "cas. Si vous avez besoin d'utiliser un autre port, vous pourrez le "
#~ "modifier ultérieurement dans les préférences."

#~ msgid "Show users with similar interests"
#~ msgstr "Afficher les utilisateurs avec les mêmes intérêts"

#~ msgid "Menu"
#~ msgstr "Menu"

#~ msgid "Expand / Collapse all"
#~ msgstr "Minimiser / Maximiser"

#~ msgid "Configure shares"
#~ msgstr "Configuration des partages"

#~ msgid "Create or join room…"
#~ msgstr "Créer ou rejoindre un salon…"

#~ msgid "_Room List"
#~ msgstr "Liste des _salons"

#~ msgid "Enable alternative download and upload speed limits"
#~ msgstr "Activer les limites alternatives de téléchargement et d'envoi"

#~ msgid "Show log history"
#~ msgstr "Afficher l'histoire du journal"

#~ msgid "Result grouping mode"
#~ msgstr "Mode de groupement des résultats"

#~ msgid "Free slot"
#~ msgstr "Emplacement libre"

#~ msgid "Save shares list to disk"
#~ msgstr "Enregistrer la liste des partages sur le disque"

#~ msgid "_Away"
#~ msgstr "_Absent"

#, python-format
#~ msgid "Failed to load ui file %(file)s: %(error)s"
#~ msgstr "Échec du chargement du fichier d'interface %(file)s : %(error)s"

#~ msgid ""
#~ "Attempting to reset index of shared files due to an error. Please rescan "
#~ "your shares."
#~ msgstr ""
#~ "Tentative de réinitialisation de l'index des fichiers partagés en raison "
#~ "d'une erreur. Veuillez rescanner vos partages."

#~ msgid ""
#~ "File index of shared files could not be accessed. This could occur due to "
#~ "several instances of Nicotine+ being active simultaneously, file "
#~ "permission issues, or another issue in Nicotine+."
#~ msgstr ""
#~ "L'index des fichiers partagés n'a pas pu être consulté. Cela peut être dû "
#~ "au fait que plusieurs instances de Nicotine+ sont actives simultanément, "
#~ "à des problèmes de permission de fichiers ou à un autre problème dans "
#~ "Nicotine+."

#~ msgid "Nicotine+ is a Soulseek client"
#~ msgstr "Nicotine+ est un client Soulseek"

#~ msgid "enable the tray icon"
#~ msgstr "activer l'icône dans la zone de notification"

#~ msgid "disable the tray icon"
#~ msgstr "Désactiver l'icône dans la zone de notification"

#~ msgid "Get Soulseek Privileges…"
#~ msgstr "Obtenez les privilèges de Soulseek…"

#, python-format
#~ msgid ""
#~ "Public IP address is <b>%(ip)s</b> and active listening port is "
#~ "<b>%(port)s</b>"
#~ msgstr ""
#~ "L'adresse IP publique est <b>%(ip)s</b> et le port d'écoute actif est "
#~ "<b>%(port)s</b>"

#~ msgid "unknown"
#~ msgstr "inconnu"

#~ msgid "Notification"
#~ msgstr "Notification"

#~ msgid "Length"
#~ msgstr "Durée"

#, python-format
#~ msgid "Picture not saved, %s already exists."
#~ msgstr "Image non sauvegardée, %s existe déjà."

#~ msgid "_Open"
#~ msgstr "_Ouvrir"

#~ msgid "_Save"
#~ msgstr "_Enregistrer"

#, python-format
#~ msgid "Failed to load plugin '%s', could not find it."
#~ msgstr ""
#~ "Échec de chargement de l'extension « %s », impossible de la localiser."

#, python-format
#~ msgid "I/O error: %s"
#~ msgstr "Erreur d'E/S  : %s"

#, python-format
#~ msgid "Failed to open file path: %s"
#~ msgstr "Échec d'ouverture du fichier : %s"

#, python-format
#~ msgid "Failed to open URL: %s"
#~ msgstr "Échec d'ouverture de l'URL : %s"

#~ msgid "_Log Conversation"
#~ msgstr "_journalisation des discussions"

#~ msgid "Result Filter List"
#~ msgstr "Liste des filtres de résultats"

#~ msgid "Prepend < or > to find files less/greater than the given value."
#~ msgstr ""
#~ "Ajoutez < ou > pour rechercher des fichiers inférieurs/supérieurs à la "
#~ "valeur donnée."

#~ msgid ""
#~ "VBR files display their average bitrate and are typically lower in "
#~ "bitrate than a compressed 320 kbps CBR file of the same audio quality."
#~ msgstr ""
#~ "Les fichiers VBR affichent leur taux d'encodage moyen et sont "
#~ "généralement inférieurs en taux d'encodage d'un fichier CBR compressé de "
#~ "320 kbps de même qualité audio."

#~ msgid "Like Size above, =, <, and > can be used."
#~ msgstr "Comme la taille ci-dessus, =, < et > peuvent être utilisés."

#~ msgid ""
#~ "Prevent write access by other programs for files being downloaded (turn "
#~ "off for NFS)"
#~ msgstr ""
#~ "Empêcher l'accès en écriture par d'autres programmes pour les fichiers en "
#~ "cours de téléchargement (désactiver pour NFS)"

#~ msgid "Where incomplete downloads are temporarily stored."
#~ msgstr "Où les téléchargements incomplets sont temporairement stockés."

#~ msgid ""
#~ "Where buddies' uploads will be stored (with a subfolder created for each "
#~ "buddy)."
#~ msgstr ""
#~ "Où les téléchargements des amis seront stockés (avec un sous-dossier créé "
#~ "pour chaque ami)."

#~ msgid "Toggle away status after minutes of inactivity:"
#~ msgstr "Basculer l'état d'inactivité après quelques minutes d'inactivité :"

#~ msgid "Protocol:"
#~ msgstr "Protocole :"

#~ msgid "Icon theme folder (requires restart):"
#~ msgstr "Répertoire du thème d'icônes (nécessite un redémarrage)  :"

#~ msgid "Establishing connection"
#~ msgstr "Établissement de la connexion"

#~ msgid "Clear Groups"
#~ msgstr "Effacer les groupes"

#~ msgid "User List"
#~ msgstr "Liste des utilisateurs"

#~ msgid "_Reset Statistics…"
#~ msgstr "_Réinitialiser les statstiques…"

#~ msgid "Clear _Downloads…"
#~ msgstr "Effacer tous les téléchargements…"

#~ msgid "Clear Uploa_ds…"
#~ msgstr "Effacer tous les envois…"

#~ msgid "Block User's IP Address"
#~ msgstr "Bloquer l'adresse IP de l'utilisateur"

#~ msgid "Ignore User's IP Address"
#~ msgstr "Ignorer l'adresse IP de l'utilisateur"

#~ msgid "Usernames"
#~ msgstr "Utilisateurs"

#~ msgid "Display logged chat room messages when a room is rejoined"
#~ msgstr ""
#~ "Afficher les messages enregistrés des salons des discussions lorsqu'on y "
#~ "accède"

#~ msgid "Queue Position"
#~ msgstr "Position dans la file d'attente"

#, python-format
#~ msgid ""
#~ "User %(user)s is directly searching for \"%(query)s\", returning %(num)i "
#~ "results"
#~ msgstr ""
#~ "L'utilisateur %(user)s effectue une recherche directe sur « %(query)s », "
#~ "%(num)i résultats renvoyés"

#~ msgid "Room wall (personal message set)"
#~ msgstr "Liste des salons (message personnel défini)"

#~ msgid "Your config file is corrupt"
#~ msgstr "Votre fichier de configuration est corrompu"

#, python-format
#~ msgid ""
#~ "We're sorry, but it seems your configuration file is corrupt. Please "
#~ "reconfigure Nicotine+.\n"
#~ "\n"
#~ "We renamed your old configuration file to\n"
#~ "%(corrupt)s\n"
#~ "If you open this file with a text editor you might be able to rescue some "
#~ "of your settings."
#~ msgstr ""
#~ "Nous sommes désolés mais il semble que votre fichier de configuration "
#~ "soit corrompu. Veuillez reconfigurer Nicotine+.\n"
#~ "\n"
#~ "Nous avons renommé votre ancien fichier de configuration en\n"
#~ "%(corrupt)s\n"
#~ "Si vous ouvrez ce fichier avec un éditeur de texte, vous pourriez "
#~ "éventuellement récupérer quelques-uns de vos paramètres."

#~ msgid "User Description"
#~ msgstr "Description de l'utilisateur"

#~ msgid "User Interests"
#~ msgstr "Intérêts de l'utilisateur"

#~ msgid "User Picture"
#~ msgstr "Image de l'utilisateur"

#~ msgid "Search Wishlist"
#~ msgstr "Recherche des listes de souhaits"

#, python-format
#~ msgid "Quitting Nicotine+ %(version)s, %(status)s…"
#~ msgstr "Arrêter Nicotine+ %(version)s, %(status)s…"

#, python-format
#~ msgid "Quit Nicotine+ %(version)s, %(status)s!"
#~ msgstr "Arrêter Nicotine+ %(version)s, %(status)s !"

#~ msgid "User:"
#~ msgstr "Utilisateur  :"

#, python-format
#~ msgid "All %(ext)s"
#~ msgstr "Tous %(ext)s"

#, python-format
#~ msgid "%(number)2s files "
#~ msgstr "%(number)2s fichiers "

#~ msgid "Allow regular expressions for the filter's include and exclude"
#~ msgstr ""
#~ "Autoriser les expressions régulières pour l'inclusion et l'exclusion du "
#~ "filtre"

#~ msgid "Close Nicotine+?"
#~ msgstr "Fermer Nicotine+  ?"

#~ msgid "Do you really want to exit Nicotine+?"
#~ msgstr "Voulez-vous vraiment quitter Nicotine+ ?"

#~ msgid "Run in Background"
#~ msgstr "Exécuter en arrière-plan"

#~ msgid "_Online Notify"
#~ msgstr "N_otifier de la présence en ligne"

#~ msgid "_Prioritize User"
#~ msgstr "_Prioriser l'utilisateur"

#~ msgid "_Trust User"
#~ msgstr "Faire confiance à l'u_tilisateur"

#~ msgid "Request User's IP Address"
#~ msgstr "Demander l'adresse IP de l'utilisateur"

#~ msgid "Request IP Address"
#~ msgstr "Demander l'adresse IP"

#~ msgid "Enter the name of the user whose IP address you want to see:"
#~ msgstr ""
#~ "Saisissez le nom d'un utilisateur dont vous souhaitez recevoir l'adresse "
#~ "IP :"

#~ msgid "Downloaded"
#~ msgstr "Téléchargé"

#, python-format
#~ msgid "Failed to add download %(filename)s to shared files: %(error)s"
#~ msgstr ""
#~ "Impossible d'ajouter le téléchargement %(filename)s aux fichiers "
#~ "partagés : %(error)s"

#~ msgid "Automatically share completed downloads"
#~ msgstr "Partager automatiquement les téléchargements terminés"

#~ msgid ""
#~ "The equivalent of adding your download folder as a public share, however "
#~ "files downloaded to this folder will be automatically accessible to "
#~ "others (no rescan required)."
#~ msgstr ""
#~ "L'équivalent de l'ajout de votre dossier de téléchargement en tant que "
#~ "partage public, cependant les fichiers téléchargés dans ce dossier seront "
#~ "automatiquement accessibles aux autres (pas d'analyse nécessaire)."

#~ msgid "Unable to Share Folder"
#~ msgstr "Impossible de partager le répertoire"

#~ msgid "The chosen virtual name is empty"
#~ msgstr "Le nom virtuel choisi est vide"

#~ msgid "The chosen virtual name already exists"
#~ msgstr "Le nom virtuel choisi existe déjà"

#~ msgid "The chosen folder is already shared"
#~ msgstr "Le répertoire sélectionné est déjà partagé"

#~ msgid "Set Virtual Name"
#~ msgstr "Définir un nom virtuel"

#, python-format
#~ msgid "Enter virtual name for '%(dir)s':"
#~ msgstr "Choisissez un nom virtuel pour '%(dir)s'  :"

#~ msgid "The chosen virtual name is either empty or already exists"
#~ msgstr "Le nom virtuel choisi est vide ou bien existe déjà"

#~ msgid "The chosen folder is already shared."
#~ msgstr "Le répertoire sélectionné est déjà partagé."

#, python-format
#~ msgid "%s Properties"
#~ msgstr "Propriétés %s"

#~ msgid "Finished rescanning shares"
#~ msgstr "Analyse des partages terminée"

#~ msgid "Plugin List"
#~ msgstr "Liste des extensions"

#, python-format
#~ msgid "Error while scanning %(path)s: %(error)s"
#~ msgstr "Erreur lors de l'examen de %(path)s : %(error)s"

#~ msgid "Show _Log Pane"
#~ msgstr "Afficher le panneau des _journaux"

#~ msgid "Addresses"
#~ msgstr "Adresses"

#~ msgid "Handler"
#~ msgstr "Logiciel"

#~ msgid "Could not enable plugin."
#~ msgstr "Ne peut activer l'extension."

#~ msgid "Could not disable plugin."
#~ msgstr "Ne peut désactiver l'extension."

#~ msgid "Transfers"
#~ msgstr "Transferts"

#~ msgid "Ban List"
#~ msgstr "Liste des bannis"

#~ msgid "Ignore List"
#~ msgstr "Liste des utilisateurs ignorés"

#~ msgid "Censor & Replace"
#~ msgstr "Censure et remplacement"

#~ msgid "Completion"
#~ msgstr "Complétion"

#~ msgid "Categories"
#~ msgstr "Catégories"

#~ msgid "Upload Folder To…"
#~ msgstr "Envoyer le répertoire vers…"

#~ msgid "Upload Folder Recursive To…"
#~ msgstr "Envoyer récursivement le répertoire vers…"

#~ msgid "Download _Recursive"
#~ msgstr "Téléchargement _récursif"

#~ msgid "Download R_ecursive To…"
#~ msgstr "Téléchargement ré_cursivement vers…"

#~ msgid "Up_load File(s)"
#~ msgstr "_Envoyer des fichiers"

#, python-format
#~ msgid ""
#~ "Error while attempting to display folder '%(folder)s', reported error: "
#~ "%(error)s"
#~ msgstr ""
#~ "Erreur à l'affichage du répertoire %(folder)s, erreur remontée : %(error)s"

#~ msgid "Wishes"
#~ msgstr "Souhaits"

#~ msgid "privileged"
#~ msgstr "privilégié"

#~ msgid "prioritized"
#~ msgstr "priorisé"

#~ msgid "Blocked IP Addresses"
#~ msgstr "Adresses IP bloquées"

#~ msgid "Complete buddy names"
#~ msgstr "Compléter les noms d'amis"

#~ msgid "Complete usernames in chat rooms"
#~ msgstr "Compléter les noms d'utilisateurs dans les salons de discussion"

#~ msgid "Complete room names"
#~ msgstr "Compléter les noms de salons"

#~ msgid "Drop-down List"
#~ msgstr "Liste déroulante"

#~ msgid "File Manager command ($ for file path):"
#~ msgstr ""
#~ "Commande du gestionnaire de fichiers ($ pour le chemin du fichier) :"

#~ msgid "Media Player command ($ for file path):"
#~ msgstr "Commande du lecteur multimédia ($ pour le chemin du fichier) :"

#~ msgid "Ignored IP Addresses"
#~ msgstr "Adresse IP ignorées"

#~ msgid "Display timestamps"
#~ msgstr "Afficher l'horodatage"

#~ msgid "Notification Popups"
#~ msgstr "Notification popups"

#~ msgid "Show notification popup when a file has finished downloading"
#~ msgstr ""
#~ "Afficher une fenêtre de notification lorsque le téléchargement d'un "
#~ "fichier est terminé"

#~ msgid "Show notification popup when a folder has finished downloading"
#~ msgstr ""
#~ "Afficher une notification lorsque le téléchargement d'un dossier est "
#~ "terminé"

#~ msgid "Show notification popup when you receive a private message"
#~ msgstr ""
#~ "Afficher une fenêtre de notification lorsque vous recevez un message privé"

#~ msgid "Show notification popup when someone sends a message in a chat room"
#~ msgstr ""
#~ "Afficher une fenêtre de notification lorsque quelqu'un envoie un message "
#~ "dans un salon de discussion"

#~ msgid "Show notification popup when you are mentioned in a chat room"
#~ msgstr ""
#~ "Afficher une fenêtre de notification lorsque votre nom est mentionné dans "
#~ "un salon de discussion"

#~ msgid "Tray"
#~ msgstr "Zone de notification"

#~ msgid ""
#~ "Instances of $ will be replaced by the link. The default web browser of "
#~ "the system will be used in cases where a protocol has not been configured."
#~ msgstr ""
#~ "Les occurrences de $ seront remplacées par le lien. Le navigateur web par "
#~ "défaut du système sera utilisé dans les cas où un protocole n'a pas été "
#~ "configuré."

#~ msgid "Handler:"
#~ msgstr "Gestionnaire :"

#~ msgid "Primary Tabs"
#~ msgstr "Onglets primaires"

#~ msgid "Enable file path tooltips in search and transfer views"
#~ msgstr ""
#~ "Activer les info-bulles de chemin de fichier dans les vues de recherche "
#~ "et de transfert"

#~ msgid ""
#~ "Displays the complete file path of a search result or file transfer when "
#~ "you hover a folder path or file name with your cursor."
#~ msgstr ""
#~ "Affiche le chemin d'accès complet d'un résultat de recherche ou d'un "
#~ "transfert de fichier lorsque vous survolez un chemin de dossier ou un nom "
#~ "de fichier avec votre curseur."

#~ msgid "Show privately shared files in user shares"
#~ msgstr ""
#~ "Afficher les fichiers partagés en privé dans les partages des utilisateurs"

#~ msgid ""
#~ "Other clients may offer an option to send privately shared files when you "
#~ "browse their shares. Folders containing such files are prefixed with "
#~ "'[PRIVATE FOLDER]', and are not downloadable unless the uploader gives "
#~ "explicit permission."
#~ msgstr ""
#~ "D'autres clients peuvent proposer une option permettant d'envoyer des "
#~ "fichiers partagés de manière privée lorsque vous parcourez leurs "
#~ "partages. Les dossiers contenant de tels fichiers sont précédés du "
#~ "préfixe \"[DOSSIER PRIVÉ]\" et ne peuvent être téléchargés qu'avec "
#~ "l'autorisation explicite de l'expéditeur."

#~ msgid "Show _Debug Log Controls"
#~ msgstr "Afficher les contrôles du journal de débogage"

#~ msgid "Debug Logging"
#~ msgstr "Sortie de débogage"

#~ msgid "Virtual Name"
#~ msgstr "Nom virtuel"

#~ msgid "Edit Virtual Name"
#~ msgstr "Éditer un nom virtuel"

#~ msgid "Copy Folder URL"
#~ msgstr "Copier l'URL du répertoire"

#~ msgid "Download _To…"
#~ msgstr "Télécharger _vers…"

#~ msgid "Download"
#~ msgstr "Télécharger"

#~ msgid "Upload"
#~ msgstr "Envoi"

#~ msgid "Upload Folder's Contents"
#~ msgstr "Envoyer le contenu du répertoire"

#~ msgid "Rename"
#~ msgstr "Renommer"

#~ msgid "File Lists"
#~ msgstr "Listes de fichiers"

#~ msgid "Copy File Path"
#~ msgstr "Copier le chemin du fichier"

#~ msgid "R_emove Wish"
#~ msgstr "S_upprimer le souhait"

#, python-format
#~ msgid "It appears '%s' is not a directory, not loading plugins."
#~ msgstr ""
#~ "« %s » ne semble pas être un répertoire, les extensions ne seront pas "
#~ "chargées."

#, python-format
#~ msgid "Your buddy, %s, is attempting to upload file(s) to you."
#~ msgstr "Votre ami %s tente de vous envoyer un(/des) fichier(s)."

#, python-format
#~ msgid ""
#~ "%s is not allowed to send you file(s), but is attempting to, anyway. "
#~ "Warning Sent."
#~ msgstr ""
#~ "%s n'est pas autorisé à vous envoyer de(s) fichier(s), mais tente de le "
#~ "faire quand même. Un avertissement lui a été envoyé."

#~ msgid "Client Version"
#~ msgstr "Version du client"

#, python-format
#~ msgid "How many days of privileges should user %s be gifted?"
#~ msgstr ""
#~ "Combien de jours de privilèges doit-on accorder à l'utilisateur %s ?"

#~ msgid ""
#~ "Buddies will have higher priority in the queue, the same as globally "
#~ "privileged users."
#~ msgstr ""
#~ "Les amis auront une priorité plus élevée dans la file d'attente, de la "
#~ "même manière que les utilisateurs privilégiés."

#~ msgid "Privileged"
#~ msgstr "Privilégié"

#~ msgid "_Privileged"
#~ msgstr "_Privilégié"

#~ msgid "Comments"
#~ msgstr "Commentaires"

#~ msgid "Edit _Comments…"
#~ msgstr "Éditer les commentaires…"

#~ msgid ""
#~ "Creates subfolders based on the user you are downloading from, and stores "
#~ "the downloaded file / folder there."
#~ msgstr ""
#~ "Créer des sous-dossiers en fonction de l'utilisateur à partir duquel vous "
#~ "effectuez le téléchargement, et y stocker le fichier/dossier téléchargé."

#~ msgid "Login Details"
#~ msgstr "Données d'identification"

#~ msgid "Advanced"
#~ msgstr "Avancé"

#~ msgid "Port mapping renewal interval in hours:"
#~ msgstr "Intervalle de renouvellement de redirection de port en heures :"

#~ msgid "Enable buddy-only shares"
#~ msgstr "Activer les partages réservés aux amis"

#~ msgid "Files will be uploaded in the order they were queued."
#~ msgstr ""
#~ "Les fichiers seront envoyés dans l'ordre où ils ont été mis en file "
#~ "d'attente."

#~ msgid "Rescanning normal shares…"
#~ msgstr "Réexamen des partages normaux…"

#~ msgid "Finished rescanning public shares"
#~ msgstr "Réexamen des partages publics terminé"

#~ msgid "Scanning Buddy Shares"
#~ msgstr "Examen des partages pour amis"

#~ msgid "_Rescan Public Shares"
#~ msgstr "_Reéxaminer les partages publics"

#~ msgid "Rescan B_uddy Shares"
#~ msgstr "Réexaminer les partages pour _amis"

#~ msgid "Rescan Public Shares"
#~ msgstr "Réexaminer les partages publics"

#~ msgid "Rescan Buddy Shares"
#~ msgstr "Réexaminer les partages pour amis"

#~ msgid "Mark each shared folder as buddy-only"
#~ msgstr "Marquer chaque dossier partagé comme étant réservé aux amis"

#~ msgid ""
#~ "Overrides the per-share option, useful if you temporarily need to prevent "
#~ "public access to shares."
#~ msgstr ""
#~ "Remplace l'option par partage, utile si vous devez temporairement "
#~ "empêcher l'accès public aux actions."

#~ msgid ""
#~ "Only users marked as trusted on your buddy list can access your buddy-"
#~ "only shares."
#~ msgstr ""
#~ "Seuls les utilisateurs marqués comme étant de confiance dans votre liste "
#~ "d'amis peuvent accéder à vos partages réservés aux amis."

#~ msgid ""
#~ "Nicotine+ allows you to share folders directly from your computer. All "
#~ "the contents of these folders (with the exception of dotfiles) can be "
#~ "downloaded by other users on the Soulseek network. Public shares are "
#~ "available for every user, while users in your buddy list can access buddy-"
#~ "only shares in addition to public shares."
#~ msgstr ""
#~ "Nicotine+ vous permet de partager des répertoires directement depuis "
#~ "votre ordinateur. Tout le contenu de ces répertoires (à l'exception de "
#~ "ceux dont le nom commence par un point) peut être téléchargé par d'autres "
#~ "utilisateurs sur le réseau Soulseek. Les partages publics sont "
#~ "disponibles pour tous les utilisateurs, tandis que seuls les utilisateurs "
#~ "de votre liste d'amis peuvent accéder aux partages pour amis, en plus des "
#~ "partages publics."

#~ msgid "Filtered out excluded search result "
#~ msgstr "Résultat de recherche exclu et filtré "

#~ msgid "Filtered out inexact or incorrect search result "
#~ msgstr "Filtrage des résultats de recherche inexacts ou incorrects "

#, python-format
#~ msgid ""
#~ "Stored setting '%(key)s' is no longer present in the '%(name)s' plugin"
#~ msgstr ""
#~ "Le paramètre stocké '%(key)s] n'est plus présent dans le plugin '%(name)s]"

#, python-format
#~ msgid "Plugin %(module)s returned something weird, '%(value)s', ignoring"
#~ msgstr ""
#~ "L'extension %(module)s a remonté quelque chose d'étrange, « %(value)s », "
#~ "ignoré"

#, python-format
#~ msgid "Inconsistent cache for '%(vdir)s', rebuilding '%(dir)s'"
#~ msgstr ""
#~ "Cache inconsistant pour le partage '%(vdir)s', reconstruction de '%(dir)s'"

#, python-format
#~ msgid "Dropping missing folder %(dir)s"
#~ msgstr "Répertoire manquant %(dir)s ignoré"

#, python-format
#~ msgid "All tickers / wall messages for %(room)s:"
#~ msgstr "Tous les messages défilants ou ceux du mur pour %(room)s :"

#~ msgid "Set your personal ticker"
#~ msgstr "Choisir votre message défilant personnel"

#~ msgid "Show all the tickers"
#~ msgstr "Montrer tous les messages défilants"

#~ msgid ""
#~ "Failed to scan shares. If Nicotine+ is currently running, please close "
#~ "the program before scanning."
#~ msgstr ""
#~ "Échec de l'examen des partages. Si Nicotine+ est en cours d'exécution, "
#~ "veuillez fermer le programme avant de lancer un examen."

#~ msgid "Are you sure you wish to exit Nicotine+ at this time?"
#~ msgstr "Etes vous sûr de vouloir fermer Nicotine+  ?"

#~ msgid "Receive a User's IP Address"
#~ msgstr "Recevoir l'adresse IP d'un utilisateur"

#~ msgid "Receive a User's Info"
#~ msgstr "Recevoir les informations d'un utilisateur"

#~ msgid "The server forbid us from doing wishlist searches."
#~ msgstr "Le serveur interdit les recherches sur les listes de souhaits."

#, python-format
#~ msgid ""
#~ "Your shares database is corrupted. Please rescan your shares and report "
#~ "any potential scanning issues to the developers. Error: %s"
#~ msgstr ""
#~ "La base de données de vos partages est corrompue. Veuillez analyser à "
#~ "nouveau vos partages et signaler tout problème d'analyse potentiel aux "
#~ "développeurs. Erreur : %s"

#~ msgid ""
#~ "Please keep in mind that certain usernames may be taken. If this is the "
#~ "case, you will be prompted to change your username when connecting to the "
#~ "network."
#~ msgstr ""
#~ "Veuillez garder à l'esprit que certains noms d'utilisateur peuvent être "
#~ "pris. Si tel est le cas, vous serez invité à changer votre nom "
#~ "d'utilisateur lors de la connexion au réseau."

#~ msgid "Enter the username of the person you to receive information about"
#~ msgstr ""
#~ "Saisissez le nom d'utilisateur de la personne dont vous souhaitez des "
#~ "informations"

#~ msgid "Add user 'user' to your user list"
#~ msgstr "Ajoute l'utilisateur 'utilisateur' à votre liste d'utilisateurs"

#~ msgid "Remove user 'user' from your user list"
#~ msgstr "Retire l'utilisateur 'utilisateur' de votre liste d'utilisateurs"

#~ msgid "Request user info for user 'user'"
#~ msgstr "Demande les informations de l'utilisateur 'utilisateur'"

#~ msgid "Add user to your user list"
#~ msgstr "Ajouter l'utilisateur à votre liste d'utilisateurs"

#~ msgid "Remove user from your user list"
#~ msgstr "Retirer l'utilisateur de votre liste d'utilisateurs"

#~ msgid "Respond to search requests containing minimum character count:"
#~ msgstr ""
#~ "Répondre aux demandes de recherche contenant un nombre minimum de "
#~ "caractères :"

#~ msgid ""
#~ "Queued users will be uploaded one file at a time in a cyclical fashion."
#~ msgstr "Les fichiers en attente seront envoyés un à un de manière cyclique."

#~ msgid "/close /c"
#~ msgstr "/close /c"

#~ msgid "/add /ad"
#~ msgstr "/add /ad"

#~ msgid "/rem /unbuddy"
#~ msgstr "/rem /unbuddy"

#~ msgid "/ban"
#~ msgstr "/ban"

#~ msgid "/unban"
#~ msgstr "/unban"

#~ msgid "/ignore"
#~ msgstr "/ignore"

#~ msgid "/unignore"
#~ msgstr "/unignore"

#~ msgid "/browse /b"
#~ msgstr "/browse /b"

#~ msgid "/whois /w"
#~ msgstr "/whois /w"

#~ msgid "/ip"
#~ msgstr "/ip"

#~ msgid "Find..."
#~ msgstr "Rechercher..."

#~ msgid "Queued..."
#~ msgstr "Mis en file d'attente..."

#~ msgid "Clear All..."
#~ msgstr "Tout effacer..."

#~ msgid "Edit _Comments..."
#~ msgstr "Éditer les _commentaires..."

#~ msgid "Room..."
#~ msgstr "Salon..."

#~ msgid "Username..."
#~ msgstr "Nom d'utilisateur..."

#~ msgid "Include text..."
#~ msgstr "Include le texte..."

#~ msgid "Exclude text..."
#~ msgstr "Exclure le texte..."

#~ msgid "File type..."
#~ msgstr "Type de fichier..."

#~ msgid "Bitrate..."
#~ msgstr "Débit binaire..."

#~ msgid "Add..."
#~ msgstr "Ajouter..."

#~ msgid "Edit..."
#~ msgstr "Éditer..."

#~ msgid "_Modes"
#~ msgstr "_Onglets"

#~ msgid "_Chat Rooms"
#~ msgstr "_Discussions"

#~ msgid "_Private Chat"
#~ msgstr "Dialogues _privés"

#~ msgid "_Downloads"
#~ msgstr "_Téléchargements"

#~ msgid "_Search Files"
#~ msgstr "_Recherche de fichiers"

#~ msgid "User I_nfo"
#~ msgstr "Informations _utilisateur"

#~ msgid "_Interests"
#~ msgstr "_Centres d'intérêts"

#~ msgid "Buddy _List"
#~ msgstr "_Amis"

#~ msgid "last.fm"
#~ msgstr "last.fm"

#~ msgid "MPRIS (v2)"
#~ msgstr "MPRIS (v2)"

#~ msgid "Enable geographical blocker"
#~ msgstr "Activer le bloqueur géographique"

#~ msgid "Your IP address has not been retrieved from the server"
#~ msgstr "Votre adresse IP n'a pu être récupéré du serveur"

#~ msgid "Enable URL catching"
#~ msgstr "Activer la capture d'URL"

#, python-format
#~ msgid "Hide %(tab)s"
#~ msgstr "Cacher %(tab)s"

#, python-format
#~ msgid "Your IP address is <b>%(ip)s</b>"
#~ msgstr "Votre adresse IP est <b>%(ip)s</b>"

#~ msgid "Geo Block"
#~ msgstr "Bloqueur géographique"

#~ msgid "Censor List"
#~ msgstr "Liste des censures"

#~ msgid "Auto-Replace List"
#~ msgstr "Substitution de texte"

#~ msgid "URL Catching"
#~ msgstr "Capture d'URL"

#~ msgid "Away Mode"
#~ msgstr "Mode absent"
