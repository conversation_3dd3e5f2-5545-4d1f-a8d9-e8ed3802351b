<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2009-2025 <PERSON><PERSON>+ Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkBox" id="container">
    <property name="hexpand">True</property>
    <property name="orientation">vertical</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox" id="side_toolbar">
        <property name="margin-bottom">6</property>
        <property name="margin-end">6</property>
        <property name="margin-start">6</property>
        <property name="margin-top">6</property>
        <property name="spacing">6</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="label" translatable="yes">Buddies</property>
            <property name="margin-end">12</property>
            <property name="margin-start">6</property>
            <property name="visible">True</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkEntry">
            <property name="height-request">0</property>
            <property name="hexpand">True</property>
            <property name="input-hints">no-emoji</property>
            <property name="placeholder-text" translatable="yes">Add buddy…</property>
            <property name="primary-icon-name">system-users-symbolic</property>
            <property name="visible">True</property>
            <property name="width-chars">15</property>
            <signal name="activate" handler="on_add_buddy"/>
            <signal name="icon-press" handler="on_add_buddy"/>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkScrolledWindow" id="list_container">
            <property name="hexpand">True</property>
            <property name="visible">True</property>
            <style>
              <class name="colored-icon"/>
              <class name="user-status"/>
            </style>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
