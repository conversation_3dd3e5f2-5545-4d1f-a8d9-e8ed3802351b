<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkSizeGroup">
    <property name="mode">vertical</property>
    <widgets>
      <widget name="_recommendations_label_container"/>
      <widget name="similar_users_label"/>
    </widgets>
  </object>
  <object class="GtkEntry" id="add_like_entry">
    <property name="hexpand">True</property>
    <property name="max-width-chars">26</property>
    <property name="placeholder-text" translatable="yes">Add something you like…</property>
    <property name="primary-icon-name">emblem-default-symbolic</property>
    <property name="visible">True</property>
    <property name="width-chars">15</property>
    <signal name="activate" handler="on_add_thing_i_like"/>
    <signal name="icon-press" handler="on_add_thing_i_like"/>
  </object>
  <object class="GtkEntry" id="add_dislike_entry">
    <property name="hexpand">True</property>
    <property name="placeholder-text" translatable="yes">Add something you dislike…</property>
    <property name="primary-icon-name">user-trash-symbolic</property>
    <property name="visible">True</property>
    <property name="width-chars">15</property>
    <signal name="activate" handler="on_add_thing_i_dislike"/>
    <signal name="icon-press" handler="on_add_thing_i_dislike"/>
  </object>
  <object class="GtkBox" id="container">
    <property name="spacing">1</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="hexpand">False</property>
        <property name="orientation">vertical</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="label" translatable="yes">Personal Interests</property>
            <property name="margin-bottom">12</property>
            <property name="margin-end">12</property>
            <property name="margin-start">12</property>
            <property name="margin-top">12</property>
            <property name="visible">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="add_like_container">
            <property name="margin-bottom">12</property>
            <property name="margin-end">6</property>
            <property name="margin-start">6</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="vexpand">True</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkScrolledWindow" id="likes_list_container">
                <property name="hexpand">True</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkLabel">
            <property name="label" translatable="yes">Personal Dislikes</property>
            <property name="margin-bottom">12</property>
            <property name="margin-end">12</property>
            <property name="margin-start">12</property>
            <property name="margin-top">12</property>
            <property name="visible">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="add_dislike_container">
            <property name="margin-bottom">12</property>
            <property name="margin-end">6</property>
            <property name="margin-start">6</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="vexpand">True</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkScrolledWindow" id="dislikes_list_container">
                <property name="hexpand">True</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <style>
          <class name="border-end"/>
        </style>
      </object>
    </child>
    <child>
      <object class="GtkFlowBox">
        <property name="column-spacing">1</property>
        <property name="max-children-per-line">2</property>
        <property name="row-spacing">1</property>
        <property name="selection-mode">none</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkFlowBoxChild">
            <property name="can-focus">False</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="orientation">vertical</property>
                <property name="visible">True</property>
                <property name="width-request">360</property>
                <child>
                  <object class="GtkBox" id="_recommendations_label_container">
                    <property name="margin-bottom">6</property>
                    <property name="margin-end">6</property>
                    <property name="margin-start">6</property>
                    <property name="margin-top">6</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel" id="recommendations_label">
                        <property name="label" translatable="yes">Recommendations</property>
                        <property name="margin-start">6</property>
                        <property name="visible">True</property>
                        <property name="xalign">0</property>
                        <style>
                          <class name="heading"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton" id="recommendations_button">
                        <property name="sensitive">False</property>
                        <property name="tooltip-text" translatable="yes">Refresh Recommendations</property>
                        <property name="valign">center</property>
                        <property name="visible">True</property>
                        <signal name="clicked" handler="on_refresh_recommendations"/>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">view-refresh-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="circular"/>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="vexpand">True</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkScrolledWindow" id="recommendations_list_container">
                        <property name="hexpand">True</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="border-end"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkFlowBoxChild">
            <property name="can-focus">False</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="orientation">vertical</property>
                <property name="visible">True</property>
                <property name="width-request">360</property>
                <child>
                  <object class="GtkLabel" id="similar_users_label">
                    <property name="label" translatable="yes">Similar Users</property>
                    <property name="margin-end">12</property>
                    <property name="margin-start">12</property>
                    <property name="visible">True</property>
                    <property name="xalign">0</property>
                    <style>
                      <class name="heading"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="vexpand">True</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkScrolledWindow" id="similar_users_list_container">
                        <property name="hexpand">True</property>
                        <property name="visible">True</property>
                        <style>
                          <class name="colored-icon"/>
                          <class name="user-status"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
