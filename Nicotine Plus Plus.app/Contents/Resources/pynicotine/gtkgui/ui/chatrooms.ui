<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkBox" id="container">
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="spacing">1</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkPaned" id="chat_paned">
            <property name="hexpand">True</property>
            <property name="orientation">vertical</property>
            <property name="visible">True</property>
            <property name="width-request">380</property>
            <child>
              <object class="GtkBox" id="activity_container">
                <property name="orientation">vertical</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkSearchBar" id="activity_search_bar">
                    <property name="show-close-button">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="vexpand">True</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkScrolledWindow" id="activity_view_container">
                        <property name="height-request">48</property>
                        <property name="hexpand">True</property>
                        <property name="visible">True</property>
                        <style>
                          <class name="chat-view"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkBox" id="chat_container">
                <property name="orientation">vertical</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkSearchBar" id="chat_search_bar">
                    <property name="show-close-button">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="vexpand">True</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkScrolledWindow" id="chat_view_container">
                        <property name="hexpand">True</property>
                        <property name="visible">True</property>
                        <style>
                          <class name="chat-view"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkBox" id="chat_entry_row">
                    <property name="margin-bottom">8</property>
                    <property name="margin-end">8</property>
                    <property name="margin-start">8</property>
                    <property name="margin-top">8</property>
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox" id="chat_entry_container">
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkMenuButton" id="help_button">
                        <property name="tooltip-text" translatable="yes">Chat Room Command Help</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">dialog-question-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkToggleButton" id="user_list_button">
                        <property name="tooltip-text" translatable="yes">Show Room Users</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">sidebar-show-right-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkCheckButton" id="log_toggle">
                        <property name="label" translatable="yes">_Log</property>
                        <property name="use-underline">True</property>
                        <property name="visible">True</property>
                        <signal name="toggled" handler="on_log_toggled"/>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="border-end"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="users_container">
            <property name="hexpand">False</property>
            <property name="orientation">vertical</property>
            <property name="visible">False</property>
            <property name="width-request">180</property>
            <child>
              <object class="GtkBox">
                <property name="margin-bottom">6</property>
                <property name="margin-end">6</property>
                <property name="margin-start">12</property>
                <property name="margin-top">6</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="hexpand">True</property>
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="ellipsize">end</property>
                        <property name="label" bind-source="_users_button" bind-property="tooltip-text" bind-flags="bidirectional|sync-create"/>
                        <property name="mnemonic-widget">_users_button</property>
                        <property name="visible">True</property>
                        <property name="width-chars">4</property>
                        <style>
                          <class name="heading"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton" id="_users_button">
                        <property name="action-name">app.configure-ignored-users</property>
                        <property name="tooltip-text" translatable="yes">Users</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel" id="users_label">
                            <property name="label">0</property>
                            <property name="mnemonic-widget">_users_button</property>
                            <property name="visible">True</property>
                            <style>
                              <class name="bold"/>
                              <class name="dim-label"/>
                            </style>
                          </object>
                        </child>
                        <style>
                          <class name="circular"/>
                          <class name="count"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkMenuButton" id="room_wall_button">
                    <property name="tooltip-text" translatable="yes">Room Wall</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">view-list-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkLabel" id="room_wall_label">
                            <property name="ellipsize">end</property>
                            <property name="label" translatable="yes">R_oom Wall</property>
                            <property name="mnemonic-widget">room_wall_button</property>
                            <property name="use-underline">True</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <style>
                      <class name="flat"/>
                    </style>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="vexpand">True</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkScrolledWindow" id="users_list_container">
                    <property name="hexpand">True</property>
                    <property name="visible">True</property>
                    <style>
                      <class name="colored-icon"/>
                      <class name="user-status"/>
                    </style>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkRevealer">
                <property name="reveal-child">False</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkEntry">
                    <property name="max-width-chars">50</property>
                    <property name="visible">True</property>
                    <property name="width-chars">0</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
