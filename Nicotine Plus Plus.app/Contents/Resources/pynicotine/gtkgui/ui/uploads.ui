<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkBox" id="container">
    <property name="hexpand">True</property>
    <property name="orientation">vertical</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkScrolledWindow" id="tree_container">
            <property name="hexpand">True</property>
            <property name="visible">True</property>
            <style>
              <class name="transfers-view"/>
            </style>
          </object>
        </child>
        <style>
          <class name="border-bottom"/>
        </style>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="margin-bottom">6</property>
        <property name="margin-end">6</property>
        <property name="margin-start">6</property>
        <property name="margin-top">6</property>
        <property name="spacing">6</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkBox">
            <property name="spacing">6</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkButton" id="_abort_button">
                <property name="tooltip-text" bind-source="_abort_label" bind-property="label" bind-flags="bidirectional|sync-create"/>
                <property name="visible">True</property>
                <signal name="clicked" handler="on_abort_transfer"/>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">system-shutdown-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel" id="_abort_label">
                        <property name="ellipsize">end</property>
                        <property name="label" translatable="yes">Abort</property>
                        <property name="mnemonic-widget">_abort_button</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="flat"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkButton" id="_abort_users_button">
                <property name="tooltip-text" bind-source="_abort_users_label" bind-property="label" bind-flags="bidirectional|sync-create"/>
                <property name="visible">True</property>
                <signal name="clicked" handler="on_abort_users"/>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">system-shutdown-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel" id="_abort_users_label">
                        <property name="ellipsize">end</property>
                        <property name="label" translatable="yes">Abort User(s)</property>
                        <property name="mnemonic-widget">_abort_users_button</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="flat"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="halign">end</property>
            <property name="hexpand">True</property>
            <property name="spacing">6</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkButton" id="_message_all_button">
                <property name="action-name">app.message-downloading-users</property>
                <property name="tooltip-text" translatable="yes">Message All</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">mail-send-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="ellipsize">end</property>
                        <property name="label" translatable="yes">Message All</property>
                        <property name="mnemonic-widget">_message_all_button</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="flat"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkButton" id="_clear_finished_button">
                <property name="tooltip-text" translatable="yes">Clear All Finished/Cancelled Uploads</property>
                <property name="visible">True</property>
                <signal name="clicked" handler="on_clear_finished_cancelled"/>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">edit-clear-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="ellipsize">end</property>
                        <property name="label" translatable="yes">Clear Finished</property>
                        <property name="mnemonic-widget">_clear_finished_button</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="flat"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkMenuButton" id="clear_all_button">
                <property name="direction">up</property>
                <property name="tooltip-text" translatable="yes">Clear Specific Uploads</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel" id="clear_all_label">
                        <property name="ellipsize">end</property>
                        <property name="label" translatable="yes">Clear _All…</property>
                        <property name="mnemonic-widget">clear_all_button</property>
                        <property name="use-underline">True</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">pan-up-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="flat"/>
                </style>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
