<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2021-2025 <PERSON><PERSON>+ Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkShortcutsWindow" id="dialog">
    <property name="modal">True</property>
    <property name="title" translatable="yes">Keyboard Shortcuts</property>
    <child>
      <object class="GtkShortcutsSection">
        <property name="max-height">13</property>
        <property name="section-name">shortcuts</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkShortcutsGroup">
            <property name="title" translatable="yes">General</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">app.connect</property>
                <property name="title" translatable="yes">Connect</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">app.disconnect</property>
                <property name="title" translatable="yes">Disconnect</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">app.away-accel</property>
                <property name="title" translatable="yes">Away</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">app.rescan-shares</property>
                <property name="title" translatable="yes">Rescan Shares</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">win.show-log-pane</property>
                <property name="title" translatable="yes">Show Log Pane</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">app.keyboard-shortcuts</property>
                <property name="title" translatable="yes">Keyboard Shortcuts</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">app.preferences</property>
                <property name="title" translatable="yes">Preferences</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">app.confirm-quit</property>
                <property name="title" translatable="yes">Confirm Quit</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">app.force-quit</property>
                <property name="title" translatable="yes">Quit</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkShortcutsGroup">
            <property name="title" translatable="yes">Menus</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">win.main-menu</property>
                <property name="title" translatable="yes">Open Main Menu</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">win.context-menu</property>
                <property name="title" translatable="yes">Open Context Menu</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkShortcutsGroup">
            <property name="title" translatable="yes">Tabs</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="accelerator">&lt;Alt&gt;1...9</property>
                <property name="title" translatable="yes">Change Main Tab</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">win.cycle-tabs-reverse</property>
                <property name="title" translatable="yes">Go to Previous Secondary Tab</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">win.cycle-tabs</property>
                <property name="title" translatable="yes">Go to Next Secondary Tab</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">win.reopen-closed-tab</property>
                <property name="title" translatable="yes">Reopen Closed Secondary Tab</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">win.close-tab</property>
                <property name="title" translatable="yes">Close Secondary Tab</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkShortcutsGroup">
            <property name="title" translatable="yes">Lists</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.copy-clipboard</property>
                <property name="title" translatable="yes">Copy Selected Cell</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.select-all</property>
                <property name="title" translatable="yes">Select All</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.find</property>
                <property name="title" translatable="yes">Find</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.remove</property>
                <property name="title" translatable="yes">Remove Selected Row</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkShortcutsGroup">
            <property name="title" translatable="yes">Editing</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.cut-clipboard</property>
                <property name="title" translatable="yes">Cut</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.copy-clipboard</property>
                <property name="title" translatable="yes">Copy</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.paste-clipboard</property>
                <property name="title" translatable="yes">Paste</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut" id="emoji_shortcut">
                <property name="action-name">accel.insert-emoji</property>
                <property name="title" translatable="yes">Insert Emoji</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.select-all</property>
                <property name="title" translatable="yes">Select All</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.find</property>
                <property name="title" translatable="yes">Find</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.find-next-match</property>
                <property name="title" translatable="yes">Find Next Match</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.find-previous-match</property>
                <property name="title" translatable="yes">Find Previous Match</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkShortcutsGroup">
            <property name="title" translatable="yes">File Transfers</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.retry-transfer</property>
                <property name="title" translatable="yes">Resume / Retry Transfer</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.abort-transfer</property>
                <property name="title" translatable="yes">Pause / Abort Transfer</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.file-properties</property>
                <property name="title" translatable="yes">File Properties</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkShortcutsGroup">
            <property name="title" translatable="yes">Browse Shares</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.download-to</property>
                <property name="title" translatable="yes">Download / Upload To</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.file-properties</property>
                <property name="title" translatable="yes">File Properties</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.save</property>
                <property name="title" translatable="yes">Save List to Disk</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.find-next-match</property>
                <property name="title" translatable="yes">Find Next Match</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.refresh</property>
                <property name="title" translatable="yes">Refresh</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.toggle-row-expand</property>
                <property name="title" translatable="yes">Expand / Collapse All</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.back</property>
                <property name="title" translatable="yes">Back to Parent Folder</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkShortcutsGroup">
            <property name="title" translatable="yes">File Search</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.find</property>
                <property name="title" translatable="yes">Result Filters</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">accel.file-properties</property>
                <property name="title" translatable="yes">File Properties</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkShortcutsShortcut">
                <property name="action-name">app.wishlist</property>
                <property name="title" translatable="yes">Wishlist</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
