<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2016-2025 <PERSON><PERSON>+ Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkButton" id="cancel_button">
    <property name="label" translatable="yes">_Cancel</property>
    <property name="use-underline">True</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_cancel"/>
  </object>
  <object class="GtkButton" id="export_button">
    <property name="label" translatable="yes">_Export…</property>
    <property name="use-underline">True</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_back_up_config"/>
  </object>
  <object class="GtkButton" id="apply_button">
    <property name="label" translatable="yes">_Apply</property>
    <property name="use-underline">True</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_apply"/>
  </object>
  <object class="GtkButton" id="ok_button">
    <property name="label" translatable="yes">_OK</property>
    <property name="use-underline">True</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_ok"/>
    <style>
      <class name="suggested-action"/>
    </style>
  </object>
  <object class="GtkBox" id="container">
    <property name="height-request">300</property>
    <property name="vexpand">True</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkScrolledWindow">
        <property name="hscrollbar-policy">never</property>
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <property name="width-request">250</property>
        <child>
          <object class="GtkListBox" id="preferences_list">
            <property name="selection-mode">browse</property>
            <property name="visible">True</property>
            <signal name="row-selected" handler="on_switch_page"/>
            <style>
              <class name="navigation-sidebar"/>
              <class name="view"/>
            </style>
          </object>
        </child>
        <style>
          <class name="border-end"/>
        </style>
      </object>
    </child>
    <child>
      <object class="GtkScrolledWindow" id="content">
        <property name="hexpand">True</property>
        <property name="hscrollbar-policy">never</property>
        <property name="visible">True</property>
        <property name="width-request">360</property>
        <child>
          <object class="GtkViewport" id="viewport">
            <property name="visible">True</property>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
