<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkAdjustment" id="_speed_adjustment">
    <property name="page-increment">50</property>
    <property name="step-increment">10</property>
    <property name="upper">1000000</property>
  </object>
  <object class="GtkAdjustment" id="_alt_speed_adjustment">
    <property name="page-increment">50</property>
    <property name="step-increment">10</property>
    <property name="upper">1000000</property>
  </object>
  <object class="GtkAdjustment" id="_max_queued_files_adjustment">
    <property name="page-increment">50</property>
    <property name="step-increment">10</property>
    <property name="upper">1000000</property>
  </object>
  <object class="GtkAdjustment" id="_max_queued_size_adjustment">
    <property name="page-increment">100</property>
    <property name="step-increment">25</property>
    <property name="upper">1000000</property>
  </object>
  <object class="GtkAdjustment" id="_upload_bandwidth_adjustment">
    <property name="page-increment">50</property>
    <property name="step-increment">10</property>
    <property name="upper">1000000</property>
  </object>
  <object class="GtkAdjustment" id="_upload_slots_adjustment">
    <property name="lower">1</property>
    <property name="page-increment">10</property>
    <property name="step-increment">1</property>
    <property name="upper">1000000</property>
  </object>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="spacing">30</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Uploads</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Automatically remove finished and cancelled uploads from list</property>
                <property name="mnemonic-widget">autoclear_uploads_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="autoclear_uploads_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="upload_double_click_label">
                <property name="label" translatable="yes">Double-click action for uploads:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="label" translatable="yes">Upload Speed Limits</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Limit upload speed:</property>
                <property name="mnemonic-widget">_limit_per_transfers_radio</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkRadioButton" id="_limit_per_transfers_radio">
                    <property name="active">True</property>
                    <property name="label" translatable="yes">Per transfer</property>
                    <property name="use-underline">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkRadioButton" id="limit_total_transfers_radio">
                    <property name="group">_limit_per_transfers_radio</property>
                    <property name="label" translatable="yes">Total transfers</property>
                    <property name="use-underline">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkRadioButton" id="use_unlimited_speed_radio">
            <property name="active">True</property>
            <property name="hexpand">True</property>
            <property name="label" translatable="yes">Unlimited upload speed</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkRadioButton" id="use_speed_limit_radio">
                <property name="group">use_unlimited_speed_radio</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Use upload speed limit (KiB/s):</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton" id="speed_spinner">
                <property name="adjustment">_speed_adjustment</property>
                <property name="max-width-chars">6</property>
                <property name="numeric">True</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkRadioButton" id="use_alt_speed_limit_radio">
                <property name="group">use_unlimited_speed_radio</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Use alternative upload speed limit (KiB/s):</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton" id="alt_speed_spinner">
                <property name="adjustment">_alt_speed_adjustment</property>
                <property name="numeric">True</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Upload Slots</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="tooltip-text" translatable="yes">Round Robin: Files will be uploaded in cyclical fashion to the users waiting in queue.
First In, First Out: Files will be uploaded in the order they were queued.</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="upload_queue_type_label">
                <property name="label" translatable="yes">Upload queue type:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkRadioButton" id="use_upload_slots_bandwidth_radio">
                <property name="active">True</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Allocate upload slots until total speed reaches (KiB/s):</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton" id="upload_bandwidth_spinner">
                <property name="adjustment">_upload_bandwidth_adjustment</property>
                <property name="numeric">True</property>
                <property name="sensitive" bind-source="use_upload_slots_fixed_radio" bind-property="active" bind-flags="bidirectional|invert-boolean|sync-create"/>
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkRadioButton" id="use_upload_slots_fixed_radio">
                <property name="group">use_upload_slots_bandwidth_radio</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Fixed number of upload slots:</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton" id="upload_slots_spinner">
                <property name="adjustment">_upload_slots_adjustment</property>
                <property name="numeric">True</property>
                <property name="sensitive" bind-source="use_upload_slots_fixed_radio" bind-property="active" bind-flags="bidirectional|sync-create"/>
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkCheckButton" id="prioritize_buddies_toggle">
            <property name="halign">end</property>
            <property name="label" translatable="yes">Prioritize all buddies</property>
            <property name="visible">True</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Queue Limits</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Maximum number of queued files per user:</property>
                <property name="mnemonic-widget">max_queued_files_spinner</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton" id="max_queued_files_spinner">
                <property name="adjustment">_max_queued_files_adjustment</property>
                <property name="numeric">True</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Maximum total size of queued files per user (MiB):</property>
                <property name="mnemonic-widget">max_queued_size_spinner</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton" id="max_queued_size_spinner">
                <property name="adjustment">_max_queued_size_adjustment</property>
                <property name="numeric">True</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkCheckButton" id="no_buddy_limits_toggle">
            <property name="halign">end</property>
            <property name="label" translatable="yes">Limits do not apply to buddies</property>
            <property name="visible">True</property>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
