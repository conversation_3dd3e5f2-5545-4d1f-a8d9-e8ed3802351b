<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkAdjustment" id="_speed_adjustment">
    <property name="page-increment">50</property>
    <property name="step-increment">10</property>
    <property name="upper">1000000</property>
  </object>
  <object class="GtkAdjustment" id="_alt_speed_adjustment">
    <property name="page-increment">50</property>
    <property name="step-increment">10</property>
    <property name="upper">1000000</property>
  </object>
  <object class="GtkButton" id="download_folder_default_button">
    <property name="tooltip-text" translatable="yes">Default</property>
    <property name="valign">center</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_default_download_folder"/>
    <child>
      <object class="GtkImage">
        <property name="icon-name">edit-undo-symbolic</property>
        <property name="visible">True</property>
      </object>
    </child>
    <style>
      <class name="image-button"/>
    </style>
  </object>
  <object class="GtkButton" id="incomplete_folder_default_button">
    <property name="tooltip-text" translatable="yes">Default</property>
    <property name="valign">center</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_default_incomplete_folder"/>
    <child>
      <object class="GtkImage">
        <property name="icon-name">edit-undo-symbolic</property>
        <property name="visible">True</property>
      </object>
    </child>
    <style>
      <class name="image-button"/>
    </style>
  </object>
  <object class="GtkButton" id="received_folder_default_button">
    <property name="tooltip-text" translatable="yes">Default</property>
    <property name="valign">center</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_default_received_folder"/>
    <child>
      <object class="GtkImage">
        <property name="icon-name">edit-undo-symbolic</property>
        <property name="visible">True</property>
      </object>
    </child>
    <style>
      <class name="image-button"/>
    </style>
  </object>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="spacing">30</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Downloads</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Automatically remove finished and filtered downloads from list</property>
                <property name="mnemonic-widget">autoclear_downloads_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="autoclear_downloads_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Store completed downloads in username subfolders</property>
                <property name="mnemonic-widget">enable_username_subfolders_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="enable_username_subfolders_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="download_double_click_label">
                <property name="label" translatable="yes">Double-click action for downloads:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkCheckButton" id="accept_sent_files_toggle">
                <property name="label" translatable="yes">Allow users to send you any files:</property>
                <property name="use-underline">True</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkBox" id="sent_files_permission_container">
                <property name="homogeneous">True</property>
                <property name="sensitive" bind-source="accept_sent_files_toggle" bind-property="active" bind-flags="bidirectional|sync-create"/>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Download Speed Limits</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkRadioButton" id="use_unlimited_speed_radio">
            <property name="active">True</property>
            <property name="hexpand">True</property>
            <property name="label" translatable="yes">Unlimited download speed</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkRadioButton" id="use_speed_limit_radio">
                <property name="group">use_unlimited_speed_radio</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Use download speed limit (KiB/s):</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton" id="speed_spinner">
                <property name="adjustment">_speed_adjustment</property>
                <property name="numeric">True</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkRadioButton" id="use_alt_speed_limit_radio">
                <property name="group">use_unlimited_speed_radio</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Use alternative download speed limit (KiB/s):</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton" id="alt_speed_spinner">
                <property name="adjustment">_alt_speed_adjustment</property>
                <property name="numeric">True</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Folders</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="download_folder_label">
                <property name="label" translatable="yes">Finished downloads:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="incomplete_folder_label">
                <property name="label" translatable="yes">Incomplete downloads:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="received_folder_label">
                <property name="label" translatable="yes">Received files:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Events</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="orientation">vertical</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Run command after file download finishes ($ for file path):</property>
                <property name="mnemonic-widget">file_finished_command_entry</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkEntry" id="file_finished_command_entry">
                <property name="visible">True</property>
                <property name="width-chars">16</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="margin-top">6</property>
            <property name="orientation">vertical</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Run command after folder download finishes ($ for folder path):</property>
                <property name="mnemonic-widget">folder_finished_command_entry</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkEntry" id="folder_finished_command_entry">
                <property name="visible">True</property>
                <property name="width-chars">16</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Download Filters</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Enable download filters</property>
                <property name="mnemonic-widget">enable_filters_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="enable_filters_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkFrame">
            <property name="margin-top">6</property>
            <property name="vexpand">True</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="orientation">vertical</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkScrolledWindow" id="filter_list_container">
                    <property name="height-request">200</property>
                    <property name="hexpand">True</property>
                    <property name="vexpand">True</property>
                    <property name="visible">True</property>
                    <style>
                      <class name="border-bottom"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="margin-bottom">6</property>
                    <property name="margin-end">6</property>
                    <property name="margin-start">6</property>
                    <property name="margin-top">6</property>
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkButton" id="_add_filter_button">
                        <property name="tooltip-text" translatable="yes">Add</property>
                        <property name="visible">True</property>
                        <signal name="clicked" handler="on_add_filter"/>
                        <child>
                          <object class="GtkBox">
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">list-add-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkLabel">
                                <property name="ellipsize">end</property>
                                <property name="label" translatable="yes">Add…</property>
                                <property name="mnemonic-widget">_add_filter_button</property>
                                <property name="use-underline">True</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                        <style>
                          <class name="flat"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton" id="_edit_filter_button">
                        <property name="tooltip-text" translatable="yes">Edit…</property>
                        <property name="visible">True</property>
                        <signal name="clicked" handler="on_edit_filter"/>
                        <child>
                          <object class="GtkBox">
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">document-edit-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkLabel">
                                <property name="ellipsize">end</property>
                                <property name="label" translatable="yes">Edit…</property>
                                <property name="mnemonic-widget">_edit_filter_button</property>
                                <property name="use-underline">True</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                        <style>
                          <class name="flat"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton" id="_remove_filter_button">
                        <property name="tooltip-text" translatable="yes">Remove</property>
                        <property name="visible">True</property>
                        <signal name="clicked" handler="on_remove_filter"/>
                        <child>
                          <object class="GtkBox">
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">list-remove-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkLabel">
                                <property name="ellipsize">end</property>
                                <property name="label" translatable="yes">Remove</property>
                                <property name="mnemonic-widget">_remove_filter_button</property>
                                <property name="use-underline">True</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                        <style>
                          <class name="flat"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="halign">end</property>
                        <property name="hexpand">True</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkButton" id="_default_filters_button">
                            <property name="tooltip-text" translatable="yes">Load Defaults</property>
                            <property name="visible">True</property>
                            <signal name="clicked" handler="on_default_filters"/>
                            <child>
                              <object class="GtkBox">
                                <property name="spacing">6</property>
                                <property name="visible">True</property>
                                <child>
                                  <object class="GtkImage">
                                    <property name="icon-name">edit-undo-symbolic</property>
                                    <property name="visible">True</property>
                                  </object>
                                </child>
                                <child>
                                  <object class="GtkLabel">
                                    <property name="ellipsize">end</property>
                                    <property name="label" translatable="yes">Load Defaults</property>
                                    <property name="mnemonic-widget">_default_filters_button</property>
                                    <property name="use-underline">True</property>
                                    <property name="visible">True</property>
                                  </object>
                                </child>
                              </object>
                            </child>
                            <style>
                              <class name="flat"/>
                            </style>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="margin-top">6</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkButton" id="_verify_filters_button">
                <property name="valign">center</property>
                <property name="visible">True</property>
                <signal name="clicked" handler="on_verify_filter"/>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">object-select-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="label" translatable="yes">Verify Filters</property>
                        <property name="mnemonic-widget">_verify_filters_button</property>
                        <property name="use-underline">True</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkLabel" id="filter_status_label">
                <property name="label" translatable="yes">Unverified</property>
                <property name="hexpand">True</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="xalign">0</property>
                <style>
                  <class name="bold"/>
                </style>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
