<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkButton" id="retry_button">
    <property name="label" translatable="yes">Retry</property>
    <property name="valign">center</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_search_again"/>
  </object>
  <object class="GtkEntry" id="filter_include_entry">
    <property name="hexpand">True</property>
    <property name="max-width-chars">0</property>
    <property name="placeholder-text" translatable="yes">Include text…</property>
    <property name="primary-icon-name">object-select-symbolic</property>
    <property name="tooltip-text" translatable="yes">Add a filter to only show results whose file paths have the given text. Use a '|' to specify multiple words or phrases, for example: long trail|till we meet again</property>
    <property name="visible">True</property>
    <property name="width-chars">14</property>
    <signal name="activate" handler="on_refilter"/>
    <signal name="icon-press" handler="on_filter_entry_icon_press"/>
  </object>
  <object class="GtkEntry" id="filter_exclude_entry">
    <property name="hexpand">True</property>
    <property name="max-width-chars">0</property>
    <property name="placeholder-text" translatable="yes">Exclude text…</property>
    <property name="primary-icon-name">action-unavailable-symbolic</property>
    <property name="tooltip-text" translatable="yes">Add a filter to hide results whose file paths have the given text. Use a '|' to specify multiple words or phrases, for example: long trail|till we meet again</property>
    <property name="visible">True</property>
    <property name="width-chars">14</property>
    <signal name="activate" handler="on_refilter"/>
    <signal name="icon-press" handler="on_filter_entry_icon_press"/>
  </object>
  <object class="GtkEntry" id="filter_file_type_entry">
    <property name="hexpand">True</property>
    <property name="input-hints">no-emoji</property>
    <property name="max-width-chars">0</property>
    <property name="placeholder-text" translatable="yes">File type…</property>
    <property name="primary-icon-name">folder-documents-symbolic</property>
    <property name="tooltip-text" translatable="yes">File type, e.g. flac wav or !mp3 !m4a</property>
    <property name="visible">True</property>
    <property name="width-chars">12</property>
    <signal name="activate" handler="on_refilter"/>
    <signal name="icon-press" handler="on_filter_entry_icon_press"/>
  </object>
  <object class="GtkEntry" id="filter_file_size_entry">
    <property name="hexpand">True</property>
    <property name="input-hints">no-emoji</property>
    <property name="max-width-chars">0</property>
    <property name="placeholder-text" translatable="yes">File size…</property>
    <property name="primary-icon-name">drive-harddisk-symbolic</property>
    <property name="tooltip-text" translatable="yes">File size, e.g. &gt;10.5m &lt;1g</property>
    <property name="visible">True</property>
    <property name="width-chars">10</property>
    <signal name="activate" handler="on_refilter"/>
    <signal name="icon-press" handler="on_filter_entry_icon_press"/>
  </object>
  <object class="GtkEntry" id="filter_bitrate_entry">
    <property name="hexpand">True</property>
    <property name="input-hints">no-emoji</property>
    <property name="max-width-chars">0</property>
    <property name="placeholder-text" translatable="yes">Bitrate…</property>
    <property name="primary-icon-name">audio-volume-high-symbolic</property>
    <property name="tooltip-text" translatable="yes">Bitrate, e.g. 256 &lt;1412</property>
    <property name="visible">True</property>
    <property name="width-chars">10</property>
    <signal name="activate" handler="on_refilter"/>
    <signal name="icon-press" handler="on_filter_entry_icon_press"/>
  </object>
  <object class="GtkEntry" id="filter_length_entry">
    <property name="hexpand">True</property>
    <property name="input-hints">no-emoji</property>
    <property name="max-width-chars">0</property>
    <property name="placeholder-text" translatable="yes">Duration…</property>
    <property name="primary-icon-name">media-playback-start-symbolic</property>
    <property name="tooltip-text" translatable="yes">Duration, e.g. &gt;6:00 &lt;12:00 !6:54</property>
    <property name="visible">True</property>
    <property name="width-chars">10</property>
    <signal name="activate" handler="on_refilter"/>
    <signal name="icon-press" handler="on_filter_entry_icon_press"/>
  </object>
  <object class="GtkEntry" id="filter_country_entry">
    <property name="hexpand">True</property>
    <property name="input-hints">no-emoji</property>
    <property name="max-width-chars">0</property>
    <property name="placeholder-text" translatable="yes">Country code…</property>
    <property name="primary-icon-name">mark-location-symbolic</property>
    <property name="tooltip-text" translatable="yes">Country code, e.g. US ES or !DE !GB</property>
    <property name="visible">True</property>
    <property name="width-chars">8</property>
    <signal name="activate" handler="on_refilter"/>
    <signal name="icon-press" handler="on_filter_entry_icon_press"/>
  </object>
  <object class="GtkBox" id="container">
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkBox" id="info_bar_container">
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="margin-bottom">6</property>
            <property name="margin-end">6</property>
            <property name="margin-start">6</property>
            <property name="margin-top">6</property>
            <property name="spacing">6</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="hexpand">True</property>
                <property name="margin-end">6</property>
                <property name="margin-start">6</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="label" translatable="yes">Results</property>
                    <property name="mnemonic-widget">results_button</property>
                    <property name="visible">True</property>
                    <style>
                      <class name="heading"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="results_button">
                    <property name="visible">True</property>
                    <signal name="clicked" handler="on_counter_button"/>
                    <child>
                      <object class="GtkLabel" id="results_label">
                        <property name="label">0</property>
                        <property name="mnemonic-widget">results_button</property>
                        <property name="visible">True</property>
                        <style>
                          <class name="bold"/>
                          <class name="dim-label"/>
                        </style>
                      </object>
                    </child>
                    <style>
                      <class name="circular"/>
                      <class name="count"/>
                    </style>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkButton" id="add_wish_button">
                <property name="visible">True</property>
                <signal name="clicked" handler="on_add_wish"/>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage" id="add_wish_icon">
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel" id="add_wish_label">
                        <property name="ellipsize">end</property>
                        <property name="mnemonic-widget">add_wish_button</property>
                        <property name="use-underline">True</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="flat"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkToggleButton" id="filters_button">
                <property name="visible">True</property>
                <signal name="toggled" handler="on_toggle_filters"/>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">edit-find-replace-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel" id="filters_label">
                        <property name="ellipsize">end</property>
                        <property name="label" translatable="yes">_Result Filters</property>
                        <property name="mnemonic-widget">filters_button</property>
                        <property name="use-underline">True</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="flat"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="visible">True</property>
                <child>
                  <object class="GtkToggleButton" id="expand_button">
                    <property name="tooltip-text" translatable="yes">Expand All</property>
                    <signal name="toggled" handler="on_toggle_expand_all"/>
                    <child>
                      <object class="GtkImage" id="expand_icon">
                        <property name="icon-name">view-fullscreen-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <style>
                      <class name="image-button"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkMenuButton" id="grouping_button">
                    <property name="tooltip-text" translatable="yes">File Grouping Mode</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">view-list-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <style>
                      <class name="image-button"/>
                    </style>
                  </object>
                </child>
                <style>
                  <class name="linked"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkRevealer" id="filters_container">
            <property name="transition-type">slide-down</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="margin-bottom">6</property>
                <property name="margin-end">6</property>
                <property name="margin-start">12</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkFlowBox">
                    <property name="column-spacing">12</property>
                    <property name="max-children-per-line">7</property>
                    <property name="min-children-per-line">2</property>
                    <property name="row-spacing">6</property>
                    <property name="selection-mode">none</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox" id="filter_include_container">
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox" id="filter_exclude_container">
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox" id="filter_file_type_container">
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox" id="filter_file_size_container">
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox" id="filter_bitrate_container">
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox" id="filter_length_container">
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="spacing">12</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkBox" id="filter_country_container">
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkBox">
                                <property name="visible">True</property>
                                <child>
                                  <object class="GtkToggleButton" id="filter_free_slot_button">
                                    <property name="tooltip-text" translatable="yes">Free Slot</property>
                                    <property name="visible">True</property>
                                    <signal name="toggled" handler="on_refilter"/>
                                    <child>
                                      <object class="GtkImage">
                                        <property name="icon-name">document-open-recent-symbolic</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                    <style>
                                      <class name="image-button"/>
                                    </style>
                                  </object>
                                </child>
                                <child>
                                  <object class="GtkToggleButton" id="filter_public_files_button">
                                    <property name="tooltip-text" translatable="yes">Public Files</property>
                                    <property name="visible">True</property>
                                    <signal name="toggled" handler="on_refilter"/>
                                    <child>
                                      <object class="GtkImage">
                                        <property name="icon-name">changes-allow-symbolic</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                    <style>
                                      <class name="image-button"/>
                                    </style>
                                  </object>
                                </child>
                                <style>
                                  <class name="linked"/>
                                </style>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkSeparator">
                    <property name="margin-end">6</property>
                    <property name="margin-start">12</property>
                    <property name="orientation">vertical</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="clear_undo_filters_button">
                    <property name="valign">start</property>
                    <property name="visible">True</property>
                    <signal name="clicked" handler="on_clear_undo_filters"/>
                    <child>
                      <object class="GtkImage" id="clear_undo_filters_icon">
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <style>
                      <class name="circular"/>
                      <class name="flat"/>
                      <class name="image-button"/>
                    </style>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="vexpand">True</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkScrolledWindow" id="tree_container">
                <property name="hexpand">True</property>
                <property name="visible">True</property>
                <style>
                  <class name="search-view"/>
                </style>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
