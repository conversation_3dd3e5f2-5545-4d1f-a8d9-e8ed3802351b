# SPDX-FileCopyrightText: 2020-2025 <PERSON><PERSON>+ Contributors
# SPDX-License-Identifier: GPL-3.0-or-later

Version = "2020-11-08r00"
Authors = ["<PERSON><PERSON>+"]
Name = "Now Playing Search"
Description = "Searches for a song your media player is currently playing. This plugin uses the media player specified in the settings of the 'Now Playing'-feature.\n\nThe following keywords can be used when searching:\n- $t : Title\n- $n : Now Playing (typically 'Artist' - 'Title')\n- $l : Duration\n- $r : Bitrate\n- $c : Comment\n- $a : Artist\n- $b : Album\n- $k : Track Number\n- $y : Year\n- $f : Filename (URI)\n- $p : Program"
