# Nicotine Plus Plus - Enhanced Features

## Version 3.4.0-plus

### New Features Added:

#### 🚀 Bulk Search Functionality
- **Multi-Query Search**: Enter multiple search queries separated by commas
- **Individual Tabs**: Each query opens in its own search tab for organized results
- **Smart Processing**: Automatically trims whitespace and filters empty queries
- **Mode Preservation**: Maintains current search mode (Global/Buddies/Rooms/User)
- **User-Friendly Interface**: Clear instructions and intuitive dialog design

#### 📋 How to Use Bulk Search:
1. Navigate to the Search tab
2. Click the bulk search button (📋 icon) in the search toolbar
3. Enter comma-separated queries like: `<PERSON>, <PERSON>, <PERSON> Beatles, <PERSON>`
4. Click "Search" to create individual tabs for each query
5. <PERSON>rowse results in organized, separate tabs

#### ✨ Benefits:
- **Time-Saving**: Search for multiple items at once
- **Organized**: Each search gets its own tab for easy management
- **Flexible**: Works with all search modes and settings
- **Efficient**: No need to type each query individually

### Technical Implementation:
- Added BulkSearchDialog class with multi-line text input
- Integrated seamlessly with existing search system
- Maintains all current search functionality and settings
- Follows Nicotine+ coding standards and UI patterns

### Compatibility:
- Fully compatible with original Nicotine+ features
- Works on macOS, Linux, and Windows
- Requires GTK 3.0+ and Python 3.8+

---

**Nicotine Plus Plus** - Taking your Soulseek experience to the next level! 🎵
