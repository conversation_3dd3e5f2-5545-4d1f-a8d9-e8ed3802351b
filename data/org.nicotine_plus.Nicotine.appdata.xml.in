<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2020-2025 <PERSON><PERSON>+ Contributors
  SPDX-License-Identifier: CC0-1.0
-->
<component type="desktop-application">
  <id>org.nicotine_plus.<PERSON>tine</id>
  <launchable type="desktop-id">org.nicotine_plus.Nicotine.desktop</launchable>
  <metadata_license>CC0-1.0</metadata_license>
  <project_license>GPL-3.0-or-later</project_license>
  <name translate="no"><PERSON><PERSON>+</name>
  <summary>Browse the Soulseek network</summary>
  <description>
    <p>
      <PERSON><PERSON>+ is a graphical client for the Soulseek peer-to-peer
      network.
    </p>
    <p>
      <PERSON><PERSON>+ aims to be a lightweight, pleasant, free and open
      source (FOSS) alternative to the official Soulseek client, while
      also providing a comprehensive set of features.
    </p>
  </description>
  <branding>
    <color type="primary" scheme_preference="light">#D4E0EE</color>
    <color type="primary" scheme_preference="dark">#203651</color>
  </branding>
  <screenshots>
    <screenshot environment="gnome" type="default">
      <caption>Search Files</caption>
      <image>https://raw.githubusercontent.com/nicotine-plus/nicotine-plus/HEAD/data/screenshots/screenshot1.png</image>
    </screenshot>
    <screenshot environment="gnome">
      <caption>Downloads</caption>
      <image>https://raw.githubusercontent.com/nicotine-plus/nicotine-plus/HEAD/data/screenshots/screenshot2.png</image>
    </screenshot>
    <screenshot environment="gnome">
      <caption>Browse Shares</caption>
      <image>https://raw.githubusercontent.com/nicotine-plus/nicotine-plus/HEAD/data/screenshots/screenshot3.png</image>
    </screenshot>
    <screenshot environment="gnome">
      <caption>Private Chat</caption>
      <image>https://raw.githubusercontent.com/nicotine-plus/nicotine-plus/HEAD/data/screenshots/screenshot4.png</image>
    </screenshot>
    <screenshot environment="gnome:dark">
      <caption>Search Files</caption>
      <image>https://raw.githubusercontent.com/nicotine-plus/nicotine-plus/HEAD/data/screenshots/screenshot5.png</image>
    </screenshot>
    <screenshot environment="gnome:dark">
      <caption>Downloads</caption>
      <image>https://raw.githubusercontent.com/nicotine-plus/nicotine-plus/HEAD/data/screenshots/screenshot6.png</image>
    </screenshot>
    <screenshot environment="gnome:dark">
      <caption>Browse Shares</caption>
      <image>https://raw.githubusercontent.com/nicotine-plus/nicotine-plus/HEAD/data/screenshots/screenshot7.png</image>
    </screenshot>
    <screenshot environment="gnome:dark">
      <caption>Private Chat</caption>
      <image>https://raw.githubusercontent.com/nicotine-plus/nicotine-plus/HEAD/data/screenshots/screenshot8.png</image>
    </screenshot>
  </screenshots>
  <requires>
    <display_length compare="ge">600</display_length>
    <internet>always</internet>
  </requires>
  <supports>
    <control>keyboard</control>
    <control>pointing</control>
    <control>touch</control>
  </supports>
  <kudos>
    <kudo>HiDpiIcon</kudo>
    <kudo>ModernToolkit</kudo>
    <kudo>Notifications</kudo>
  </kudos>
  <url type="homepage">https://nicotine-plus.org/</url>
  <url type="bugtracker">https://github.com/nicotine-plus/nicotine-plus/issues</url>
  <url type="translate">https://nicotine-plus.org/doc/TRANSLATIONS</url>
  <url type="vcs-browser">https://github.com/nicotine-plus/nicotine-plus</url>
  <developer_name>Nicotine+ Team</developer_name>  <!-- deprecated in AppStream 1.0 (2023) -->
  <developer id="org.nicotine-plus">
    <name>Nicotine+ Team</name>
  </developer>
  <content_rating type="oars-1.1">
    <content_attribute id="social-chat">intense</content_attribute>
    <content_attribute id="social-info">mild</content_attribute>
  </content_rating>
  <releases>
    <release version="3.4.0.dev1" date="2025-03-14" type="development"/>
    <release version="3.3.10" date="2025-03-10">
      <url>https://nicotine-plus.org/NEWS</url>
    </release>
    <release version="3.3.9" date="2025-03-09"/>
    <release version="3.3.8" date="2025-02-24"/>
    <release version="3.3.7" date="2024-12-15"/>
    <release version="3.3.6" date="2024-10-15"/>
    <release version="3.3.5" date="2024-09-22"/>
    <release version="3.3.4" date="2024-05-06"/>
    <release version="3.3.3" date="2024-05-05"/>
    <release version="3.3.2" date="2024-02-25"/>
    <release version="3.3.1" date="2024-02-24"/>
    <release version="3.3.0" date="2024-02-01"/>
    <release version="3.2.9" date="2023-03-05"/>
    <release version="3.2.8" date="2023-01-06"/>
    <release version="3.2.7" date="2022-12-01"/>
    <release version="3.2.6" date="2022-10-21"/>
    <release version="3.2.5" date="2022-08-31"/>
    <release version="3.2.4" date="2022-08-07"/>
    <release version="3.2.3" date="2022-08-05"/>
    <release version="3.2.2" date="2022-03-19"/>
    <release version="3.2.1" date="2022-02-10"/>
    <release version="3.2.0" date="2021-12-18"/>
    <release version="3.1.1" date="2021-08-02"/>
    <release version="3.1.0" date="2021-07-23"/>
    <release version="3.0.6" date="2021-05-01"/>
    <release version="3.0.5" date="2021-04-24"/>
    <release version="3.0.4" date="2021-04-07"/>
    <release version="3.0.3" date="2021-04-01"/>
    <release version="3.0.2" date="2021-03-01"/>
    <release version="3.0.1" date="2021-02-26"/>
    <release version="3.0.0" date="2021-02-12"/>
    <release version="2.2.2" date="2020-12-15"/>
    <release version="2.2.1" date="2020-12-14"/>
    <release version="2.2.0" date="2020-12-04"/>
    <release version="2.1.2" date="2020-10-12"/>
    <release version="2.1.1" date="2020-09-26"/>
    <release version="2.1.0" date="2020-09-12"/>
    <release version="2.0.1" date="2020-07-16"/>
    <release version="2.0.0" date="2020-07-14"/>
  </releases>
</component>
