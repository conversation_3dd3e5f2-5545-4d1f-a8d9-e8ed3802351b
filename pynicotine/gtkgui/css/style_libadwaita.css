/*
 * SPDX-FileCopyrightText: 2023-2025 <PERSON><PERSON>+ Contributors
 * SPDX-License-Identifier: GPL-3.0-or-later
 */

/* Tweaks (libadwaita) */

window:not(.filechooser):not(.preferences-border):not(.titlebar-border) headerbar.titlebar,
window:not(.menubar-border) menubar {
    /* Make title/header bars flat to match other libadwaita apps */
    background: none;
    box-shadow: none;
    color: inherit;
}

treeview > header > button > box {
    /* Use relative font size for column headers */
    font-size: 0.92em;
}

dropdown popover.menu scrolledwindow > listview > row {
    /* Reduce menu item height in dropdowns to match other menus */
    min-height: 32px;
    padding-bottom: 0;
    padding-top: 0;
}

