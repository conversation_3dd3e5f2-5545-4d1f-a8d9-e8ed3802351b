/*
 * SPDX-FileCopyrightText: 2023-2025 <PERSON><PERSON>+ Contributors
 * SPDX-License-Identifier: GPL-3.0-or-later
 */

/* Tweaks (GTK 4+) */

* {
    /* Unset line height due to it resulting in blurry text */
    line-height: unset;
}

treeview.normal-icons {
    /* Country flag icon size in treeviews */
    -gtk-icon-size: 21px;
}

window.dialog:not(.message) .dialog-action-area {
    /* Add missing spacing to dialog action buttons */
    border-spacing: 6px;
}

.image-text-button box {
    /* Remove unwanted spacing from buttons */
    border-spacing: 0;
}

popover.custom > contents {
    /* Remove unwanted spacing from popovers */
    padding: 0;
}

popover.custom button,
popover.custom entry,
popover.custom switch {
    /* Remove unwanted spacing from popovers */
    margin: 0;
}

popover.entry-completion > contents {
    /* Add margin to entry completion list */
    padding: 8px;
}

popover.entry-completion treeview:not(active):not(:selected) {
    /* Remove unwanted background from entry completion list */
    background: none;
}

.fontchooser listview label {
    /* Use same height for all labels to work around broken scrolling
    https://gitlab.gnome.org/GNOME/gtk/-/issues/4751 */
    min-height: 2.5em;
}

.fontchooser listview row {
    margin: 0;
    padding: 0;
}

dropdown.entry > button > box {
    /* Hide label in combobox dropdown button */
    border-spacing: 0;
    font-size: 0;
}

tabs > arrow {
    /* Workaround for criticals when right-clicking notebook tabs */
    margin-top: -5px;
    margin-bottom: -5px;
}

