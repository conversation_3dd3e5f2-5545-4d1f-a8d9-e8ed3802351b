<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkHeaderBar" id="header_bar">
    <property name="height-request">42</property>
    <property name="visible">True</property>
    <child type="title">
      <object class="GtkBox" id="header_title">
        <property name="height-request">0</property>
        <property name="hexpand">False</property>
        <property name="visible">True</property>
      </object>
    </child>
  </object>
  <object class="GtkBox" id="header_end">
    <property name="spacing">6</property>
    <property name="valign">center</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox" id="header_end_container">
        <property name="visible">True</property>
      </object>
    </child>
    <child>
      <object class="GtkMenuButton" id="header_menu">
        <property name="tooltip-text" translatable="yes">Main Menu</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkImage">
            <property name="icon-name">open-menu-symbolic</property>
            <property name="visible">True</property>
          </object>
        </child>
        <style>
          <class name="image-button"/>
        </style>
      </object>
    </child>
  </object>
  <object class="GtkEntry" id="room_search_entry">
    <property name="hexpand">False</property>
    <property name="input-hints">no-emoji</property>
    <property name="max-width-chars">20</property>
    <property name="placeholder-text" translatable="yes">Room…</property>
    <property name="visible">True</property>
    <property name="width-chars">5</property>
    <signal name="activate" handler="on_search"/>
  </object>
  <object class="GtkEntry" id="user_search_entry">
    <property name="hexpand">False</property>
    <property name="input-hints">no-emoji</property>
    <property name="max-width-chars">20</property>
    <property name="placeholder-text" translatable="yes">Username…</property>
    <property name="visible">True</property>
    <property name="width-chars">5</property>
    <signal name="activate" handler="on_search"/>
  </object>
  <object class="GtkEntry" id="search_entry">
    <property name="max-width-chars">72</property>
    <property name="placeholder-text" translatable="yes">Search term…</property>
    <property name="primary-icon-name">system-search-symbolic</property>
    <property name="secondary-icon-tooltip-text" translatable="yes">Clear</property>
    <property name="tooltip-text" translatable="yes">Search patterns: with a word = term, without a word = -term, partial word = *erm, exact phrase = "search term"</property>
    <property name="visible">True</property>
    <property name="width-chars">5</property>
    <signal name="activate" handler="on_search"/>
    <signal name="changed" handler="on_search_entry_changed"/>
    <signal name="icon-press" handler="on_search_entry_icon_press"/>
  </object>
  <object class="GtkBox" id="search_page">
    <property name="visible">False</property>
    <property name="width-request">580</property>
    <child>
      <object class="GtkBox">
        <property name="hexpand">True</property>
        <property name="orientation">vertical</property>
        <property name="visible">False</property>
        <child>
          <object class="GtkBox" id="search_toolbar">
            <property name="visible">False</property>
            <child>
              <object class="GtkBox">
                <property name="hexpand">True</property>
                <property name="margin-bottom">6</property>
                <property name="margin-end">6</property>
                <property name="margin-start">6</property>
                <property name="margin-top">6</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox" id="search_title">
                    <property name="hexpand">True</property>
                    <property name="spacing">4</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkMenuButton" id="search_mode_button">
                        <property name="tooltip-text" translatable="yes">Search Scope</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel" id="search_mode_label">
                                <property name="mnemonic-widget">search_mode_button</property>
                                <property name="use-underline">True</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">pan-down-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkBox" id="search_end">
                    <property name="spacing">6</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkButton" id="_wishlist_button">
                        <property name="action-name">app.wishlist</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">document-edit-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkLabel">
                                <property name="label" translatable="yes">_Wishlist</property>
                                <property name="mnemonic-widget">_wishlist_button</property>
                                <property name="use-underline">True</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                        <style>
                          <class name="image-text-button"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton" id="bulk_search_button">
                        <property name="tooltip-text" translatable="yes">Bulk Search</property>
                        <property name="visible">True</property>
                        <signal name="clicked" handler="on_bulk_search"/>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">view-list-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkSeparator">
                        <property name="orientation">vertical</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton">
                        <property name="action-name">app.configure-searches</property>
                        <property name="tooltip-text" translatable="yes">Configure Searches</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">emblem-system-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="border-bottom"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="visible" bind-source="search_content" bind-property="visible" bind-flags="bidirectional|invert-boolean|sync-create"/>
            <child>
              <object class="GtkBox">
                <property name="halign">center</property>
                <property name="hexpand">True</property>
                <property name="margin-bottom">30</property>
                <property name="margin-end">24</property>
                <property name="margin-start">24</property>
                <property name="margin-top">30</property>
                <property name="orientation">vertical</property>
                <property name="spacing">30</property>
                <property name="valign">center</property>
                <property name="vexpand">True</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">system-search-symbolic</property>
                    <property name="pixel-size">128</property>
                    <property name="visible">True</property>
                    <style>
                      <class name="dim-label"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Search Files</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                        <style>
                          <class name="title-1"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Enter a search term to search for files shared by other users on the Soulseek network</property>
                        <property name="max-width-chars">55</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="search_content">
            <property name="visible">False</property>
          </object>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkBox" id="downloads_page">
    <property name="visible">False</property>
    <property name="width-request">580</property>
    <child>
      <object class="GtkBox">
        <property name="hexpand">True</property>
        <property name="orientation">vertical</property>
        <property name="visible">False</property>
        <child>
          <object class="GtkBox" id="downloads_toolbar">
            <property name="visible">False</property>
            <child>
              <object class="GtkBox">
                <property name="hexpand">True</property>
                <property name="margin-bottom">6</property>
                <property name="margin-end">6</property>
                <property name="margin-start">6</property>
                <property name="margin-top">6</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox" id="downloads_title">
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <property name="spacing">6</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="margin-end">6</property>
                        <property name="margin-start">6</property>
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="label" bind-source="download_users_button" bind-property="tooltip-text" bind-flags="bidirectional|sync-create"/>
                            <property name="mnemonic-widget">download_users_button</property>
                            <property name="visible">True</property>
                            <style>
                              <class name="heading"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkButton" id="download_users_button">
                            <property name="action-name">app.transfer-statistics</property>
                            <property name="tooltip-text" translatable="yes">Users</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel" id="download_users_label">
                                <property name="label">0</property>
                                <property name="mnemonic-widget">download_users_button</property>
                                <property name="visible">True</property>
                                <style>
                                  <class name="bold"/>
                                  <class name="dim-label"/>
                                </style>
                              </object>
                            </child>
                            <style>
                              <class name="circular"/>
                              <class name="count"/>
                            </style>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="margin-end">18</property>
                        <property name="margin-start">6</property>
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="label" bind-source="_download_files_button" bind-property="tooltip-text" bind-flags="bidirectional|sync-create"/>
                            <property name="mnemonic-widget">_download_files_button</property>
                            <property name="visible">True</property>
                            <style>
                              <class name="heading"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkButton" id="_download_files_button">
                            <property name="action-name">app.transfer-statistics</property>
                            <property name="tooltip-text" translatable="yes">Files</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel" id="download_files_label">
                                <property name="label">0</property>
                                <property name="mnemonic-widget">_download_files_button</property>
                                <property name="visible">True</property>
                                <style>
                                  <class name="bold"/>
                                  <class name="dim-label"/>
                                </style>
                              </object>
                            </child>
                            <style>
                              <class name="circular"/>
                              <class name="count"/>
                            </style>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkBox" id="downloads_end">
                    <property name="spacing">6</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkToggleButton" id="downloads_expand_button">
                            <property name="tooltip-text" translatable="yes">Expand All</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage" id="downloads_expand_icon">
                                <property name="icon-name">view-fullscreen-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <style>
                              <class name="image-button"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkMenuButton" id="downloads_grouping_button">
                            <property name="tooltip-text" translatable="yes">File Grouping Mode</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">view-list-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                        <style>
                          <class name="linked"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton">
                        <property name="action-name">app.configure-downloads</property>
                        <property name="tooltip-text" translatable="yes">Configure Downloads</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">emblem-system-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="border-bottom"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="visible" bind-source="downloads_content" bind-property="visible" bind-flags="bidirectional|invert-boolean|sync-create"/>
            <child>
              <object class="GtkBox">
                <property name="halign">center</property>
                <property name="hexpand">True</property>
                <property name="margin-bottom">30</property>
                <property name="margin-end">24</property>
                <property name="margin-start">24</property>
                <property name="margin-top">30</property>
                <property name="orientation">vertical</property>
                <property name="spacing">30</property>
                <property name="valign">center</property>
                <property name="vexpand">True</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">folder-download-symbolic</property>
                    <property name="pixel-size">128</property>
                    <property name="visible">True</property>
                    <style>
                      <class name="dim-label"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Downloads</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                        <style>
                          <class name="title-1"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Files you download from other users are queued here, and can be paused and resumed on demand</property>
                        <property name="max-width-chars">55</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="downloads_content">
            <property name="visible">False</property>
          </object>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkBox" id="uploads_page">
    <property name="visible">False</property>
    <property name="width-request">580</property>
    <child>
      <object class="GtkBox">
        <property name="hexpand">True</property>
        <property name="orientation">vertical</property>
        <property name="visible">False</property>
        <child>
          <object class="GtkBox" id="uploads_toolbar">
            <property name="visible">False</property>
            <child>
              <object class="GtkBox">
                <property name="hexpand">True</property>
                <property name="margin-bottom">6</property>
                <property name="margin-end">6</property>
                <property name="margin-start">6</property>
                <property name="margin-top">6</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox" id="uploads_title">
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <property name="spacing">6</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="margin-end">6</property>
                        <property name="margin-start">6</property>
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="label" bind-source="upload_users_button" bind-property="tooltip-text" bind-flags="bidirectional|sync-create"/>
                            <property name="mnemonic-widget">upload_users_button</property>
                            <property name="visible">True</property>
                            <style>
                              <class name="heading"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkButton" id="upload_users_button">
                            <property name="action-name">app.transfer-statistics</property>
                            <property name="tooltip-text" translatable="yes">Users</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel" id="upload_users_label">
                                <property name="label">0</property>
                                <property name="mnemonic-widget">upload_users_button</property>
                                <property name="visible">True</property>
                                <style>
                                  <class name="bold"/>
                                  <class name="dim-label"/>
                                </style>
                              </object>
                            </child>
                            <style>
                              <class name="circular"/>
                              <class name="count"/>
                            </style>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="margin-end">18</property>
                        <property name="margin-start">6</property>
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="label" bind-source="_upload_files_button" bind-property="tooltip-text" bind-flags="bidirectional|sync-create"/>
                            <property name="mnemonic-widget">_upload_files_button</property>
                            <property name="visible">True</property>
                            <style>
                              <class name="heading"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkButton" id="_upload_files_button">
                            <property name="action-name">app.transfer-statistics</property>
                            <property name="tooltip-text" translatable="yes">Files</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel" id="upload_files_label">
                                <property name="label">0</property>
                                <property name="mnemonic-widget">_upload_files_button</property>
                                <property name="visible">True</property>
                                <style>
                                  <class name="bold"/>
                                  <class name="dim-label"/>
                                </style>
                              </object>
                            </child>
                            <style>
                              <class name="circular"/>
                              <class name="count"/>
                            </style>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkBox" id="uploads_end">
                    <property name="spacing">6</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkToggleButton" id="uploads_expand_button">
                            <property name="tooltip-text" translatable="yes">Expand All</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage" id="uploads_expand_icon">
                                <property name="icon-name">view-fullscreen-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <style>
                              <class name="image-button"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkMenuButton" id="uploads_grouping_button">
                            <property name="tooltip-text" translatable="yes">File Grouping Mode</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">view-list-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                        <style>
                          <class name="linked"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton">
                        <property name="action-name">app.configure-uploads</property>
                        <property name="tooltip-text" translatable="yes">Configure Uploads</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">emblem-system-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="border-bottom"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="visible" bind-source="uploads_content" bind-property="visible" bind-flags="bidirectional|invert-boolean|sync-create"/>
            <child>
              <object class="GtkBox">
                <property name="halign">center</property>
                <property name="hexpand">True</property>
                <property name="margin-bottom">30</property>
                <property name="margin-end">24</property>
                <property name="margin-start">24</property>
                <property name="margin-top">30</property>
                <property name="orientation">vertical</property>
                <property name="spacing">30</property>
                <property name="valign">center</property>
                <property name="vexpand">True</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">emblem-shared-symbolic</property>
                    <property name="pixel-size">128</property>
                    <property name="visible">True</property>
                    <style>
                      <class name="dim-label"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Uploads</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                        <style>
                          <class name="title-1"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Users' attempts to download your shared files are queued and managed here</property>
                        <property name="max-width-chars">55</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="uploads_content">
            <property name="visible">False</property>
          </object>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkEntry" id="userbrowse_entry">
    <property name="input-hints">no-emoji</property>
    <property name="max-width-chars">45</property>
    <property name="placeholder-text" translatable="yes">Username…</property>
    <property name="primary-icon-name">avatar-default-symbolic</property>
    <property name="visible">True</property>
    <property name="width-chars">5</property>
    <signal name="activate" handler="on_get_shares"/>
    <signal name="icon-press" handler="on_get_shares"/>
  </object>
  <object class="GtkBox" id="userbrowse_page">
    <property name="visible">False</property>
    <property name="width-request">580</property>
    <child>
      <object class="GtkBox">
        <property name="hexpand">True</property>
        <property name="orientation">vertical</property>
        <property name="visible">False</property>
        <child>
          <object class="GtkBox" id="userbrowse_toolbar">
            <property name="visible">False</property>
            <child>
              <object class="GtkBox">
                <property name="hexpand">True</property>
                <property name="margin-bottom">6</property>
                <property name="margin-end">6</property>
                <property name="margin-start">6</property>
                <property name="margin-top">6</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox" id="userbrowse_title">
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkBox" id="userbrowse_end">
                    <property name="spacing">6</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkButton" id="_load_from_disk_button">
                        <property name="action-name">app.load-shares-from-disk</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">document-open-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkLabel">
                                <property name="label" translatable="yes">_Open List</property>
                                <property name="mnemonic-widget">_load_from_disk_button</property>
                                <property name="tooltip-text" translatable="yes">Opens a local list of shared files that was previously saved to disk</property>
                                <property name="use-underline">True</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                        <style>
                          <class name="image-text-button"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkSeparator">
                        <property name="orientation">vertical</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton">
                        <property name="action-name">app.configure-shares</property>
                        <property name="tooltip-text" translatable="yes">Configure Shares</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">emblem-system-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="border-bottom"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="visible" bind-source="userbrowse_content" bind-property="visible" bind-flags="bidirectional|invert-boolean|sync-create"/>
            <child>
              <object class="GtkBox">
                <property name="halign">center</property>
                <property name="hexpand">True</property>
                <property name="margin-bottom">30</property>
                <property name="margin-end">24</property>
                <property name="margin-start">24</property>
                <property name="margin-top">30</property>
                <property name="orientation">vertical</property>
                <property name="spacing">30</property>
                <property name="valign">center</property>
                <property name="vexpand">True</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">folder-symbolic</property>
                    <property name="pixel-size">128</property>
                    <property name="visible">True</property>
                    <style>
                      <class name="dim-label"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Browse Shares</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                        <style>
                          <class name="title-1"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Enter the name of a user, whose shared files you'd like to browse. You can also save the list to disk, and inspect it later on.</property>
                        <property name="max-width-chars">55</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="userbrowse_content">
            <property name="visible">False</property>
          </object>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkEntry" id="userinfo_entry">
    <property name="input-hints">no-emoji</property>
    <property name="max-width-chars">45</property>
    <property name="placeholder-text" translatable="yes">Username…</property>
    <property name="primary-icon-name">avatar-default-symbolic</property>
    <property name="visible">True</property>
    <property name="width-chars">5</property>
    <signal name="activate" handler="on_show_user_profile"/>
    <signal name="icon-press" handler="on_show_user_profile"/>
  </object>
  <object class="GtkBox" id="userinfo_page">
    <property name="visible">False</property>
    <property name="width-request">580</property>
    <child>
      <object class="GtkBox">
        <property name="hexpand">True</property>
        <property name="orientation">vertical</property>
        <property name="visible">False</property>
        <child>
          <object class="GtkBox" id="userinfo_toolbar">
            <property name="visible">False</property>
            <child>
              <object class="GtkBox">
                <property name="hexpand">True</property>
                <property name="margin-bottom">6</property>
                <property name="margin-end">6</property>
                <property name="margin-start">6</property>
                <property name="margin-top">6</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox" id="userinfo_title">
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkBox" id="userinfo_end">
                    <property name="spacing">6</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkButton" id="_update_profile_button">
                        <property name="action-name">app.personal-profile</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">user-home-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkLabel">
                                <property name="label" translatable="yes">_Personal Profile</property>
                                <property name="mnemonic-widget">_update_profile_button</property>
                                <property name="use-underline">True</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                        <style>
                          <class name="image-text-button"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkSeparator">
                        <property name="orientation">vertical</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton">
                        <property name="action-name">app.configure-account</property>
                        <property name="tooltip-text" translatable="yes">Configure Account</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">emblem-system-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="border-bottom"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="visible" bind-source="userinfo_content" bind-property="visible" bind-flags="bidirectional|invert-boolean|sync-create"/>
            <child>
              <object class="GtkBox">
                <property name="halign">center</property>
                <property name="hexpand">True</property>
                <property name="margin-bottom">30</property>
                <property name="margin-end">24</property>
                <property name="margin-start">24</property>
                <property name="margin-top">30</property>
                <property name="orientation">vertical</property>
                <property name="spacing">30</property>
                <property name="valign">center</property>
                <property name="vexpand">True</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">avatar-default-symbolic</property>
                    <property name="pixel-size">128</property>
                    <property name="visible">True</property>
                    <style>
                      <class name="dim-label"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">User Profiles</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                        <style>
                          <class name="title-1"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Enter the name of a user to view their user description, information and personal picture</property>
                        <property name="max-width-chars">55</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="userinfo_content">
            <property name="visible">False</property>
          </object>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkEntry" id="private_entry">
    <property name="input-hints">no-emoji</property>
    <property name="max-width-chars">40</property>
    <property name="placeholder-text" translatable="yes">Username…</property>
    <property name="primary-icon-name">avatar-default-symbolic</property>
    <property name="visible">True</property>
    <property name="width-chars">5</property>
    <signal name="activate" handler="on_get_private_chat"/>
    <signal name="icon-press" handler="on_get_private_chat"/>
  </object>
  <object class="GtkBox" id="private_page">
    <property name="visible">False</property>
    <property name="width-request">580</property>
    <child>
      <object class="GtkBox">
        <property name="hexpand">True</property>
        <property name="orientation">vertical</property>
        <property name="visible">False</property>
        <child>
          <object class="GtkBox" id="private_toolbar">
            <property name="visible">False</property>
            <child>
              <object class="GtkBox">
                <property name="hexpand">True</property>
                <property name="margin-bottom">6</property>
                <property name="margin-end">6</property>
                <property name="margin-start">6</property>
                <property name="margin-top">6</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox" id="private_title">
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <property name="spacing">4</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox" id="private_entry_container">
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton" id="_private_history_button">
                        <property name="action-name">app.chat-history</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">mail-unread-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkLabel">
                                <property name="label" translatable="yes">Chat _History</property>
                                <property name="mnemonic-widget">_private_history_button</property>
                                <property name="use-underline">True</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                        <style>
                          <class name="image-text-button"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkBox" id="private_end">
                    <property name="spacing">6</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkButton">
                        <property name="action-name">app.configure-chats</property>
                        <property name="tooltip-text" translatable="yes">Configure Chats</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">emblem-system-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="border-bottom"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="visible" bind-source="private_content" bind-property="visible" bind-flags="bidirectional|invert-boolean|sync-create"/>
            <child>
              <object class="GtkBox">
                <property name="halign">center</property>
                <property name="hexpand">True</property>
                <property name="margin-bottom">30</property>
                <property name="margin-end">24</property>
                <property name="margin-start">24</property>
                <property name="margin-top">30</property>
                <property name="orientation">vertical</property>
                <property name="spacing">30</property>
                <property name="valign">center</property>
                <property name="vexpand">True</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">mail-unread-symbolic</property>
                    <property name="pixel-size">128</property>
                    <property name="visible">True</property>
                    <style>
                      <class name="dim-label"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Private Chat</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                        <style>
                          <class name="title-1"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Enter the name of a user to start a text conversation with them in private</property>
                        <property name="max-width-chars">55</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="private_content">
            <property name="visible">False</property>
          </object>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkBox" id="userlist_page">
    <property name="visible">False</property>
    <property name="width-request">580</property>
    <child>
      <object class="GtkBox">
        <property name="hexpand">True</property>
        <property name="orientation">vertical</property>
        <property name="visible">False</property>
        <child>
          <object class="GtkBox" id="userlist_toolbar">
            <property name="visible">False</property>
            <child>
              <object class="GtkBox">
                <property name="hexpand">True</property>
                <property name="margin-bottom">6</property>
                <property name="margin-end">6</property>
                <property name="margin-start">6</property>
                <property name="margin-top">6</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox" id="userlist_title">
                    <property name="halign">start</property>
                    <property name="hexpand">True</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkEntry" id="add_buddy_entry">
                        <property name="input-hints">no-emoji</property>
                        <property name="max-width-chars">45</property>
                        <property name="placeholder-text" translatable="yes">Add buddy…</property>
                        <property name="primary-icon-name">system-users-symbolic</property>
                        <property name="visible">True</property>
                        <property name="width-chars">5</property>
                        <signal name="activate" handler="on_add_buddy"/>
                        <signal name="icon-press" handler="on_add_buddy"/>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkBox" id="userlist_end">
                    <property name="spacing">6</property>
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkButton" id="_message_buddies_button">
                        <property name="action-name">app.message-buddies</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">mail-send-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkLabel">
                                <property name="label" translatable="yes">_Message All</property>
                                <property name="mnemonic-widget">_message_buddies_button</property>
                                <property name="use-underline">True</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                        <style>
                          <class name="image-text-button"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkSeparator">
                        <property name="orientation">vertical</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton">
                        <property name="action-name">app.configure-ignored-users</property>
                        <property name="tooltip-text" translatable="yes">Configure Ignored Users</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">emblem-system-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="border-bottom"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="visible" bind-source="userlist_content" bind-property="visible" bind-flags="bidirectional|invert-boolean|sync-create"/>
            <child>
              <object class="GtkBox">
                <property name="halign">center</property>
                <property name="hexpand">True</property>
                <property name="margin-bottom">30</property>
                <property name="margin-end">24</property>
                <property name="margin-start">24</property>
                <property name="margin-top">30</property>
                <property name="orientation">vertical</property>
                <property name="spacing">30</property>
                <property name="valign">center</property>
                <property name="vexpand">True</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">system-users-symbolic</property>
                    <property name="pixel-size">128</property>
                    <property name="visible">True</property>
                    <style>
                      <class name="dim-label"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Buddies</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                        <style>
                          <class name="title-1"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="justify">center</property>
                        <property name="label" translatable="yes">Add users as buddies to share specific folders with them and receive notifications when they are online</property>
                        <property name="max-width-chars">55</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="userlist_content">
            <property name="visible">False</property>
          </object>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkEntry" id="chatrooms_entry">
    <property name="input-hints">no-emoji</property>
    <property name="max-width-chars">40</property>
    <property name="placeholder-text" translatable="yes">Join or create room…</property>
    <property name="primary-icon-name">user-available-symbolic</property>
    <property name="visible">True</property>
    <property name="width-chars">5</property>
    <signal name="activate" handler="on_create_room"/>
    <signal name="icon-press" handler="on_create_room"/>
  </object>
  <object class="GtkBox" id="chatrooms_page">
    <property name="visible">False</property>
    <property name="width-request">580</property>
    <child>
      <object class="GtkPaned" id="chatrooms_paned">
        <property name="hexpand">True</property>
        <property name="visible">False</property>
        <child>
          <object class="GtkBox" id="chatrooms_container">
            <property name="orientation">vertical</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox" id="chatrooms_toolbar">
                <property name="visible">False</property>
                <child>
                  <object class="GtkBox">
                    <property name="hexpand">True</property>
                    <property name="margin-bottom">6</property>
                    <property name="margin-end">6</property>
                    <property name="margin-start">6</property>
                    <property name="margin-top">6</property>
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox" id="chatrooms_title">
                        <property name="halign">start</property>
                        <property name="hexpand">True</property>
                        <property name="spacing">4</property>
                        <property name="valign">center</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox" id="chatrooms_entry_container">
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkButton" id="_room_list_button">
                            <property name="action-name">app.room-list</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkBox">
                                <property name="spacing">6</property>
                                <property name="visible">True</property>
                                <child>
                                  <object class="GtkImage">
                                    <property name="icon-name">view-list-symbolic</property>
                                    <property name="visible">True</property>
                                  </object>
                                </child>
                                <child>
                                  <object class="GtkLabel">
                                    <property name="label" translatable="yes">All _Rooms</property>
                                    <property name="mnemonic-widget">_room_list_button</property>
                                    <property name="use-underline">True</property>
                                    <property name="visible">True</property>
                                  </object>
                                </child>
                              </object>
                            </child>
                            <style>
                              <class name="image-text-button"/>
                            </style>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox" id="chatrooms_end">
                        <property name="spacing">6</property>
                        <property name="valign">center</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkButton">
                            <property name="action-name">app.configure-chats</property>
                            <property name="tooltip-text" translatable="yes">Configure Chats</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkImage">
                                <property name="icon-name">emblem-system-symbolic</property>
                                <property name="visible">True</property>
                              </object>
                            </child>
                            <style>
                              <class name="image-button"/>
                            </style>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="border-bottom"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="visible" bind-source="chatrooms_content" bind-property="visible" bind-flags="bidirectional|invert-boolean|sync-create"/>
                <child>
                  <object class="GtkBox">
                    <property name="halign">center</property>
                    <property name="hexpand">True</property>
                    <property name="margin-bottom">30</property>
                    <property name="margin-end">24</property>
                    <property name="margin-start">24</property>
                    <property name="margin-top">30</property>
                    <property name="orientation">vertical</property>
                    <property name="spacing">30</property>
                    <property name="valign">center</property>
                    <property name="vexpand">True</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">user-available-symbolic</property>
                        <property name="pixel-size">128</property>
                        <property name="visible">True</property>
                        <style>
                          <class name="dim-label"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="orientation">vertical</property>
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="justify">center</property>
                            <property name="label" translatable="yes">Chat Rooms</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <style>
                              <class name="title-1"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkLabel">
                            <property name="justify">center</property>
                            <property name="label" translatable="yes">Join an existing chat room, or create a new room to chat with other users on the Soulseek network</property>
                            <property name="max-width-chars">55</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkBox" id="chatrooms_content">
                <property name="visible">False</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="chatrooms_buddy_list_container">
            <property name="visible">False</property>
          </object>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkBox" id="interests_page">
    <property name="visible">False</property>
    <property name="width-request">580</property>
    <child>
      <object class="GtkBox" id="interests_container">
        <property name="orientation">vertical</property>
        <property name="visible">False</property>
        <child>
          <object class="GtkBox" id="interests_toolbar">
            <property name="visible">False</property>
            <child>
              <object class="GtkBox">
                <property name="visible">False</property>
                <child>
                  <object class="GtkBox" id="interests_title">
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="label" translatable="yes">Interests</property>
                        <property name="visible">True</property>
                        <style>
                          <class name="title"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkBox" id="interests_end">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkButton">
                        <property name="action-name">app.configure-user-profile</property>
                        <property name="tooltip-text" translatable="yes">Configure User Profile</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">emblem-system-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <style>
                          <class name="image-button"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="visible">True</property>
    <property name="width-request">580</property>
    <child>
      <object class="GtkPaned" id="horizontal_paned">
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkPaned" id="vertical_paned">
            <property name="orientation">vertical</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox" id="content">
                <property name="visible">False</property>
              </object>
            </child>
            <child>
              <object class="GtkBox" id="log_container">
                <property name="orientation">vertical</property>
                <property name="visible" bind-source="_log_pane_button" bind-property="active" bind-flags="bidirectional|sync-create"/>
                <child>
                  <object class="GtkSearchBar" id="log_search_bar">
                    <property name="show-close-button">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="vexpand">True</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkScrolledWindow" id="log_view_container">
                        <property name="hexpand">True</property>
                        <property name="min-content-height">90</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="buddy_list_container">
            <property name="visible">False</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="spacing">18</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkBox">
            <property name="hexpand">True</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="status_label">
                <property name="ellipsize">end</property>
                <property name="margin-start">12</property>
                <property name="selectable">True</property>
                <property name="single-line-mode">True</property>
                <property name="visible">True</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="scan_progress_container">
            <property name="spacing">8</property>
            <property name="visible">False</property>
            <child>
              <object class="GtkLabel" id="scan_progress_label">
                <property name="ellipsize">end</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <attributes>
                  <attribute name="font-features" value="tnum=1"/>
                </attributes>
                <style>
                  <class name="caption"/>
                  <class name="dim-label"/>
                  <class name="normal"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkSpinner" id="scan_progress_spinner">
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="margin-bottom">2</property>
            <property name="margin-end">2</property>
            <property name="margin-start">2</property>
            <property name="margin-top">2</property>
            <property name="spacing">6</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkButton" id="_connections_button">
                <property name="action-name">app.transfer-statistics</property>
                <property name="tooltip-text" translatable="yes">Connections</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">network-wireless-symbolic</property>
                        <property name="tooltip-text" bind-source="_connections_button" bind-property="tooltip-text" bind-flags="bidirectional|sync-create"/>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel" id="connections_label">
                        <property name="label">0</property>
                        <property name="mnemonic-widget">_connections_button</property>
                        <property name="visible">True</property>
                        <style>
                          <class name="normal"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="flat"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkMenuButton" id="download_status_button">
                <property name="tooltip-text" translatable="yes">Downloading (Speed / Active Users)</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">go-down-symbolic</property>
                        <property name="tooltip-text" bind-source="download_status_button" bind-property="tooltip-text" bind-flags="bidirectional|sync-create"/>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel" id="download_status_label">
                        <property name="mnemonic-widget">download_status_button</property>
                        <property name="visible">True</property>
                        <style>
                          <class name="normal"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="flat"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkMenuButton" id="upload_status_button">
                <property name="tooltip-text" translatable="yes">Uploading (Speed / Active Users)</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">go-up-symbolic</property>
                        <property name="tooltip-text" bind-source="upload_status_button" bind-property="tooltip-text" bind-flags="bidirectional|sync-create"/>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel" id="upload_status_label">
                        <property name="mnemonic-widget">upload_status_button</property>
                        <property name="visible">True</property>
                        <style>
                          <class name="normal"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="flat"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkToggleButton" id="user_status_button">
                <property name="action-name">win.toggle-status</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage" id="user_status_icon">
                        <property name="icon-name">nplus-status-offline</property>
                        <property name="tooltip-text" bind-source="user_status_button" bind-property="tooltip-text" bind-flags="bidirectional|sync-create"/>
                        <property name="visible" bind-source="user_status_button" bind-property="visible" bind-flags="bidirectional|sync-create"/>
                        <style>
                          <class name="colored-icon"/>
                          <class name="user-status"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel" id="user_status_label">
                        <property name="label" translatable="yes">Offline</property>
                        <property name="mnemonic-widget">user_status_button</property>
                        <property name="visible">True</property>
                        <style>
                          <class name="normal"/>
                        </style>
                      </object>
                    </child>
                  </object>
                </child>
                <style>
                  <class name="flat"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkToggleButton" id="_log_pane_button">
                <property name="action-name">win.show-log-pane</property>
                <property name="tooltip-text" translatable="yes">Show Log Pane</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">view-more-symbolic</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <style>
                  <class name="circular"/>
                  <class name="flat"/>
                  <class name="image-button"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <style>
          <class name="border-top"/>
        </style>
      </object>
    </child>
    <child>
      <object class="GtkButton" id="hide_window_button">
        <property name="visible">False</property>
      </object>
    </child>
  </object>
</interface>
