<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkBox" id="container">
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="hexpand">True</property>
        <property name="orientation">vertical</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkSearchBar" id="search_bar">
            <property name="show-close-button">True</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="vexpand">True</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkScrolledWindow" id="chat_view_container">
                <property name="hexpand">True</property>
                <property name="visible">True</property>
                <style>
                  <class name="chat-view"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="margin-bottom">8</property>
            <property name="margin-end">8</property>
            <property name="margin-start">8</property>
            <property name="margin-top">8</property>
            <property name="spacing">6</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox" id="chat_entry_container">
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkMenuButton" id="help_button">
                <property name="tooltip-text" translatable="yes">Private Chat Command Help</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">dialog-question-symbolic</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <style>
                  <class name="image-button"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkCheckButton" id="log_toggle">
                <property name="label" translatable="yes">_Log</property>
                <property name="use-underline">True</property>
                <property name="visible">True</property>
                <signal name="toggled" handler="on_log_toggled"/>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
