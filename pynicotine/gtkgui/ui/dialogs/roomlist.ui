<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2021-2025 <PERSON><PERSON>+ Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkBox" id="container">
    <property name="visible">True</property>
    <property name="width-request">360</property>
    <child>
      <object class="GtkBox">
        <property name="margin-bottom">18</property>
        <property name="margin-end">18</property>
        <property name="margin-start">18</property>
        <property name="margin-top">18</property>
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkSearchEntry" id="search_entry">
                <property name="hexpand">True</property>
                <property name="placeholder-text" translatable="yes">Search rooms…</property>
                <property name="visible">True</property>
                <property name="width-chars">15</property>
                <signal name="activate" handler="on_row_activated"/>
              </object>
            </child>
            <child>
              <object class="GtkButton" id="refresh_button">
                <property name="tooltip-text" translatable="yes">Refresh Rooms</property>
                <property name="visible">True</property>
                <signal name="clicked" handler="on_refresh"/>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">view-refresh-symbolic</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <style>
                  <class name="circular"/>
                  <class name="image-button"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkFrame">
            <property name="vexpand">True</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="halign">center</property>
                        <property name="hexpand">True</property>
                        <property name="margin-bottom">30</property>
                        <property name="margin-end">24</property>
                        <property name="margin-start">24</property>
                        <property name="margin-top">30</property>
                        <property name="orientation">vertical</property>
                        <property name="spacing">30</property>
                        <property name="valign">center</property>
                        <property name="vexpand">True</property>
                        <property name="visible" bind-source="list_container" bind-property="visible" bind-flags="bidirectional|invert-boolean|sync-create"/>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">user-available-symbolic</property>
                            <property name="pixel-size">128</property>
                            <property name="visible">True</property>
                            <style>
                              <class name="dim-label"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkBox">
                            <property name="orientation">vertical</property>
                            <property name="spacing">12</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel">
                                <property name="justify">center</property>
                                <property name="label" translatable="yes">No Rooms</property>
                                <property name="visible">True</property>
                                <property name="wrap">True</property>
                                <style>
                                  <class name="title-1"/>
                                </style>
                              </object>
                            </child>
                            <child>
                              <object class="GtkLabel">
                                <property name="justify">center</property>
                                <property name="label" translatable="yes">All available rooms will appear here</property>
                                <property name="max-width-chars">55</property>
                                <property name="visible">True</property>
                                <property name="wrap">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                    <style>
                      <class name="view"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkScrolledWindow" id="list_container">
                    <property name="hexpand">True</property>
                    <property name="visible">False</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="margin-start">3</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">_Show feed of public chat room messages</property>
                <property name="mnemonic-widget">public_feed_toggle</property>
                <property name="use-underline">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="public_feed_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
                <signal name="notify::active" handler="on_toggle_public_feed"/>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="margin-start">3</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">_Accept private room invitations</property>
                <property name="mnemonic-widget">private_room_toggle</property>
                <property name="use-underline">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="private_room_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
