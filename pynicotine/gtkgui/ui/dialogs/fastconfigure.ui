<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2009-2025 <PERSON><PERSON>+ Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkButton" id="previous_button">
    <property name="visible">True</property>
    <signal name="clicked" handler="on_previous"/>
    <child>
      <object class="GtkBox">
        <property name="margin-end">3</property>
        <property name="spacing">6</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkImage">
            <property name="icon-name">go-previous-symbolic</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkLabel" id="previous_label">
            <property name="mnemonic-widget">previous_button</property>
            <property name="use-underline">True</property>
            <property name="visible">True</property>
          </object>
        </child>
      </object>
    </child>
    <style>
      <class name="image-text-button"/>
    </style>
  </object>
  <object class="GtkButton" id="next_button">
    <property name="visible">True</property>
    <signal name="clicked" handler="on_next"/>
    <child>
      <object class="GtkBox">
        <property name="margin-start">3</property>
        <property name="spacing">4</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel" id="next_label">
            <property name="mnemonic-widget">next_button</property>
            <property name="use-underline">True</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkImage">
            <property name="icon-name">go-next-symbolic</property>
            <property name="visible">True</property>
          </object>
        </child>
      </object>
    </child>
    <style>
      <class name="image-text-button"/>
      <class name="suggested-action"/>
    </style>
  </object>
  <object class="GtkAdjustment" id="_listen_port_adjustment">
    <property name="lower">1024</property>
    <property name="page-increment">10</property>
    <property name="step-increment">1</property>
    <property name="upper">65535</property>
  </object>
  <object class="GtkStack" id="stack">
    <property name="transition-type">slide-left-right</property>
    <property name="visible">True</property>
    <property name="width-request">360</property>
    <signal name="notify::visible-child" handler="on_page_change"/>
    <child>
      <object class="GtkBox" id="welcome_page">
        <property name="halign">center</property>
        <property name="margin-bottom">30</property>
        <property name="margin-end">24</property>
        <property name="margin-start">24</property>
        <property name="margin-top">6</property>
        <property name="orientation">vertical</property>
        <property name="spacing">24</property>
        <property name="valign">center</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkImage" id="main_icon">
            <property name="can-focus">True</property>
            <property name="halign">center</property>
            <property name="pixel-size">128</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="orientation">vertical</property>
            <property name="spacing">18</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="halign">center</property>
                <property name="justify">center</property>
                <property name="label" translatable="yes">Welcome to Nicotine+</property>
                <property name="max-width-chars">30</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <style>
                  <class name="title-1"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="halign">center</property>
                <property name="justify">center</property>
                <property name="label" translatable="yes">Graphical client for the Soulseek peer-to-peer network</property>
                <property name="max-width-chars">60</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkButton" id="set_up_button">
            <property name="halign">center</property>
            <property name="height-request">42</property>
            <property name="margin-top">6</property>
            <property name="visible">True</property>
            <signal name="clicked" handler="on_next"/>
            <child>
              <object class="GtkBox">
                <property name="margin-end">18</property>
                <property name="margin-start">21</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="label" translatable="yes">_Next</property>
                    <property name="mnemonic-widget">set_up_button</property>
                    <property name="use-underline">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">go-next-symbolic</property>
                    <property name="visible">True</property>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="circular"/>
              <class name="image-text-button"/>
              <class name="suggested-action"/>
            </style>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox" id="account_page">
        <property name="halign">center</property>
        <property name="margin-bottom">18</property>
        <property name="margin-end">30</property>
        <property name="margin-start">30</property>
        <property name="margin-top">6</property>
        <property name="orientation">vertical</property>
        <property name="spacing">30</property>
        <property name="valign">center</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkImage">
            <property name="halign">center</property>
            <property name="icon-name">avatar-default-symbolic</property>
            <property name="pixel-size">64</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="orientation">vertical</property>
            <property name="spacing">24</property>
            <property name="visible" bind-source="invalid_password_label" bind-property="visible" bind-flags="bidirectional|invert-boolean|sync-create"/>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">To create a new Soulseek account, fill in your desired username and password. If you already have an account, fill in your existing login details.</property>
                <property name="max-width-chars">60</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">If your desired username is already taken, you will be prompted to change it.</property>
                <property name="max-width-chars">60</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkLabel" id="invalid_password_label">
            <property name="max-width-chars">60</property>
            <property name="selectable">True</property>
            <property name="visible">False</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
          </object>
        </child>
        <child>
          <object class="GtkFlowBox">
            <property name="column-spacing">18</property>
            <property name="max-children-per-line">2</property>
            <property name="row-spacing">18</property>
            <property name="selection-mode">none</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkFlowBoxChild">
                <property name="can-focus">False</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="hexpand">True</property>
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="label" translatable="yes">Username: </property>
                        <property name="mnemonic-widget">username_entry</property>
                        <property name="visible">True</property>
                        <property name="xalign">0</property>
                        <style>
                          <class name="heading"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkEntry" id="username_entry">
                        <property name="input-hints">no-emoji</property>
                        <property name="truncate-multiline">True</property>
                        <property name="visible">True</property>
                        <property name="width-chars">20</property>
                        <signal name="activate" handler="on_user_entry_activate"/>
                        <signal name="changed" handler="on_entry_changed"/>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkFlowBoxChild">
                <property name="can-focus">False</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="hexpand">True</property>
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="label" translatable="yes">Password: </property>
                        <property name="mnemonic-widget">password_entry</property>
                        <property name="visible">True</property>
                        <property name="xalign">0</property>
                        <style>
                          <class name="heading"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkEntry" id="password_entry">
                        <property name="truncate-multiline">True</property>
                        <property name="visibility">False</property>
                        <property name="visible">True</property>
                        <property name="width-chars">20</property>
                        <signal name="activate" handler="on_user_entry_activate"/>
                        <signal name="changed" handler="on_entry_changed"/>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox" id="port_page">
        <property name="halign">center</property>
        <property name="margin-bottom">24</property>
        <property name="margin-end">30</property>
        <property name="margin-start">30</property>
        <property name="margin-top">6</property>
        <property name="orientation">vertical</property>
        <property name="spacing">30</property>
        <property name="valign">center</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkImage">
            <property name="halign">center</property>
            <property name="icon-name">network-wireless-symbolic</property>
            <property name="pixel-size">64</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="halign">center</property>
            <property name="orientation">vertical</property>
            <property name="spacing">24</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">To connect with other Soulseek peers, a listening port on your router has to be forwarded to your computer.</property>
                <property name="max-width-chars">60</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">If your listening port is closed, you will only be able to connect to users whose listening ports are open.</property>
                <property name="max-width-chars">60</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">If necessary, choose a different listening port below. This can also be done later in the preferences.</property>
                <property name="max-width-chars">60</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkSpinButton" id="listen_port_entry">
            <property name="adjustment">_listen_port_adjustment</property>
            <property name="halign">center</property>
            <property name="numeric">True</property>
            <property name="valign">center</property>
            <property name="visible">True</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox" id="share_page">
        <property name="margin-bottom">24</property>
        <property name="margin-end">30</property>
        <property name="margin-start">30</property>
        <property name="margin-top">18</property>
        <property name="orientation">vertical</property>
        <property name="spacing">30</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkBox" id="download_folder_container">
            <property name="halign">center</property>
            <property name="orientation">vertical</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <property name="width-request">300</property>
            <child>
              <object class="GtkLabel">
                <property name="halign">center</property>
                <property name="justify">center</property>
                <property name="label" translatable="yes">Download Files to Folder</property>
                <property name="max-width-chars">60</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <style>
                  <class name="heading"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="orientation">vertical</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="halign">center</property>
                <property name="justify">center</property>
                <property name="label" translatable="yes">Share Folders</property>
                <property name="max-width-chars">60</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <style>
                  <class name="heading"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Soulseek users will be able to download from your shares. Contribute to the Soulseek network by sharing your own files and by resharing what you downloaded from other users.</property>
                <property name="max-width-chars">60</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkFrame">
                <property name="vexpand">True</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkScrolledWindow" id="shares_list_container">
                        <property name="vexpand">True</property>
                        <property name="visible">True</property>
                        <style>
                          <class name="border-bottom"/>
                        </style>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="margin-bottom">6</property>
                        <property name="margin-end">6</property>
                        <property name="margin-start">6</property>
                        <property name="margin-top">6</property>
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkButton" id="_add_shared_folder_button">
                            <property name="tooltip-text" translatable="yes">Add…</property>
                            <property name="visible">True</property>
                            <signal name="clicked" handler="on_add_shared_folder"/>
                            <child>
                              <object class="GtkBox">
                                <property name="spacing">6</property>
                                <property name="visible">True</property>
                                <child>
                                  <object class="GtkImage">
                                    <property name="icon-name">list-add-symbolic</property>
                                    <property name="visible">True</property>
                                  </object>
                                </child>
                                <child>
                                  <object class="GtkLabel">
                                    <property name="ellipsize">end</property>
                                    <property name="label" translatable="yes">Add…</property>
                                    <property name="mnemonic-widget">_add_shared_folder_button</property>
                                    <property name="use-underline">True</property>
                                    <property name="visible">True</property>
                                  </object>
                                </child>
                              </object>
                            </child>
                            <style>
                              <class name="flat"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkButton" id="_edit_shared_folder_button">
                            <property name="tooltip-text" translatable="yes">Edit…</property>
                            <property name="visible">True</property>
                            <signal name="clicked" handler="on_edit_shared_folder"/>
                            <child>
                              <object class="GtkBox">
                                <property name="spacing">6</property>
                                <property name="visible">True</property>
                                <child>
                                  <object class="GtkImage">
                                    <property name="icon-name">document-edit-symbolic</property>
                                    <property name="visible">True</property>
                                  </object>
                                </child>
                                <child>
                                  <object class="GtkLabel">
                                    <property name="ellipsize">end</property>
                                    <property name="label" translatable="yes">Edit…</property>
                                    <property name="mnemonic-widget">_edit_shared_folder_button</property>
                                    <property name="use-underline">True</property>
                                    <property name="visible">True</property>
                                  </object>
                                </child>
                              </object>
                            </child>
                            <style>
                              <class name="flat"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkButton" id="_remove_shared_folder_button">
                            <property name="tooltip-text" translatable="yes">Remove</property>
                            <property name="visible">True</property>
                            <signal name="clicked" handler="on_remove_shared_folder"/>
                            <child>
                              <object class="GtkBox">
                                <property name="spacing">6</property>
                                <property name="visible">True</property>
                                <child>
                                  <object class="GtkImage">
                                    <property name="icon-name">list-remove-symbolic</property>
                                    <property name="visible">True</property>
                                  </object>
                                </child>
                                <child>
                                  <object class="GtkLabel">
                                    <property name="ellipsize">end</property>
                                    <property name="label" translatable="yes">Remove</property>
                                    <property name="mnemonic-widget">_remove_shared_folder_button</property>
                                    <property name="use-underline">True</property>
                                    <property name="visible">True</property>
                                  </object>
                                </child>
                              </object>
                            </child>
                            <style>
                              <class name="flat"/>
                            </style>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox" id="summary_page">
        <property name="halign">center</property>
        <property name="margin-bottom">24</property>
        <property name="margin-end">30</property>
        <property name="margin-start">30</property>
        <property name="margin-top">6</property>
        <property name="orientation">vertical</property>
        <property name="spacing">30</property>
        <property name="valign">center</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkImage">
            <property name="halign">center</property>
            <property name="icon-name">emblem-default-symbolic</property>
            <property name="pixel-size">64</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="halign">center</property>
            <property name="orientation">vertical</property>
            <property name="spacing">24</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="halign">center</property>
                <property name="justify">center</property>
                <property name="label" translatable="yes">You are ready to use Nicotine+!</property>
                <property name="max-width-chars">30</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <style>
                  <class name="title-1"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="halign">center</property>
                <property name="orientation">vertical</property>
                <property name="spacing">18</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="label" translatable="yes">Soulseek is an unencrypted protocol not intended for secure communication.</property>
                    <property name="max-width-chars">60</property>
                    <property name="selectable">True</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="xalign">0</property>
                  </object>
                </child>
                <child>
                  <object class="GtkLabel">
                    <property name="label" translatable="yes">Donating to Soulseek grants you privileges for a certain time period. If you have privileges, your downloads will be queued ahead of non-privileged users.</property>
                    <property name="max-width-chars">60</property>
                    <property name="selectable">True</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="xalign">0</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
