<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkButton" id="reset_picture_button">
    <property name="tooltip-text" translatable="yes">Reset Picture</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_reset_picture"/>
    <child>
      <object class="GtkBox">
        <property name="spacing">6</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkImage">
            <property name="icon-name">edit-clear-symbolic</property>
            <property name="visible">True</property>
          </object>
        </child>
        <child>
          <object class="GtkLabel">
            <property name="ellipsize">end</property>
            <property name="label" translatable="yes">Reset Picture</property>
            <property name="mnemonic-widget">reset_picture_button</property>
            <property name="use-underline">True</property>
            <property name="visible">True</property>
          </object>
        </child>
      </object>
    </child>
    <style>
      <class name="flat"/>
    </style>
  </object>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="spacing">12</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkLabel">
        <property name="halign">start</property>
        <property name="label" translatable="yes">Self Description</property>
        <property name="mnemonic-widget">description_view_container</property>
        <property name="selectable">True</property>
        <property name="visible">True</property>
        <property name="wrap">True</property>
        <property name="xalign">0</property>
        <style>
          <class name="heading"/>
        </style>
      </object>
    </child>
    <child>
      <object class="GtkLabel">
        <property name="label" translatable="yes">Add things you want everyone to see, such as a short description, helpful tips, or guidelines for downloading your shares.</property>
        <property name="selectable">True</property>
        <property name="visible">True</property>
        <property name="wrap">True</property>
        <property name="xalign">0</property>
      </object>
    </child>
    <child>
      <object class="GtkFrame">
        <property name="margin-top">6</property>
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkBox">
            <property name="orientation">vertical</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkScrolledWindow" id="description_view_container">
                <property name="hexpand">True</property>
                <property name="vexpand">True</property>
                <property name="visible">True</property>
                <style>
                  <class name="border-bottom"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="margin-bottom">6</property>
                <property name="margin-end">6</property>
                <property name="margin-start">6</property>
                <property name="margin-top">6</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel" id="select_picture_label">
                    <property name="label" translatable="yes">Picture:</property>
                    <property name="margin-start">6</property>
                    <property name="visible">True</property>
                    <property name="xalign">0</property>
                    <style>
                      <class name="bold"/>
                    </style>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
