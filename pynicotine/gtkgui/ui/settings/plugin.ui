<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2009-2025 <PERSON><PERSON>+ Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="spacing">12</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkFlowBox">
        <property name="column-spacing">18</property>
        <property name="homogeneous">True</property>
        <property name="max-children-per-line">2</property>
        <property name="row-spacing">24</property>
        <property name="selection-mode">none</property>
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkFlowBoxChild">
            <property name="can-focus">False</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="orientation">vertical</property>
                <property name="spacing">12</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="halign">start</property>
                    <property name="label" translatable="yes">Plugins</property>
                    <property name="mnemonic-widget">plugin_list_container</property>
                    <property name="selectable">True</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="xalign">0</property>
                    <style>
                      <class name="heading"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="height-request">24</property>
                        <property name="hexpand">True</property>
                        <property name="label" translatable="yes">Enable plugins</property>
                        <property name="mnemonic-widget">enable_plugins_toggle</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                        <property name="wrap-mode">word-char</property>
                        <property name="xalign">0</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkSwitch" id="enable_plugins_toggle">
                        <property name="valign">center</property>
                        <property name="visible">True</property>
                        <signal name="notify::active" handler="on_toggle_enable_plugins"/>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkFrame">
                    <property name="margin-top">6</property>
                    <property name="vexpand">True</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="orientation">vertical</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkScrolledWindow" id="plugin_list_container">
                            <property name="hexpand">True</property>
                            <property name="sensitive" bind-source="enable_plugins_toggle" bind-property="active" bind-flags="bidirectional|sync-create"/>
                            <property name="vexpand">True</property>
                            <property name="visible">True</property>
                            <style>
                              <class name="border-bottom"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkBox">
                            <property name="margin-bottom">6</property>
                            <property name="margin-end">6</property>
                            <property name="margin-start">6</property>
                            <property name="margin-top">6</property>
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkButton" id="add_plugins_button">
                                <property name="tooltip-text" translatable="yes">Add Plugins</property>
                                <property name="visible">True</property>
                                <signal name="clicked" handler="on_add_plugins"/>
                                <child>
                                  <object class="GtkBox">
                                    <property name="spacing">6</property>
                                    <property name="visible">True</property>
                                    <child>
                                      <object class="GtkImage">
                                        <property name="icon-name">list-add-symbolic</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                    <child>
                                      <object class="GtkLabel">
                                        <property name="ellipsize">end</property>
                                        <property name="label" translatable="yes">_Add Plugins</property>
                                        <property name="mnemonic-widget">add_plugins_button</property>
                                        <property name="use-underline">True</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                  </object>
                                </child>
                                <style>
                                  <class name="flat"/>
                                </style>
                              </object>
                            </child>
                            <child>
                              <object class="GtkBox">
                                <property name="halign">end</property>
                                <property name="hexpand">True</property>
                                <property name="visible">True</property>
                                <child>
                                  <object class="GtkButton" id="plugin_settings_button">
                                    <property name="sensitive">False</property>
                                    <property name="tooltip-text" translatable="yes">Settings</property>
                                    <property name="visible">True</property>
                                    <signal name="clicked" handler="on_show_plugin_settings"/>
                                    <child>
                                      <object class="GtkBox">
                                        <property name="spacing">6</property>
                                        <property name="visible">True</property>
                                        <child>
                                          <object class="GtkImage">
                                            <property name="icon-name">emblem-system-symbolic</property>
                                            <property name="visible">True</property>
                                          </object>
                                        </child>
                                        <child>
                                          <object class="GtkLabel">
                                            <property name="ellipsize">end</property>
                                            <property name="label" translatable="yes">_Settings</property>
                                            <property name="mnemonic-widget">plugin_settings_button</property>
                                            <property name="use-underline">True</property>
                                            <property name="visible">True</property>
                                          </object>
                                        </child>
                                      </object>
                                    </child>
                                    <style>
                                      <class name="flat"/>
                                    </style>
                                  </object>
                                </child>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkFlowBoxChild">
            <property name="can-focus">False</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="orientation">vertical</property>
                <property name="spacing">12</property>
                <property name="visible">True</property>
                <property name="width-request">260</property>
                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel" id="plugin_name_label">
                            <property name="halign">start</property>
                            <property name="selectable">True</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="xalign">0</property>
                            <style>
                              <class name="heading"/>
                            </style>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="orientation">vertical</property>
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel">
                                <property name="label" translatable="yes">Version:</property>
                                <property name="mnemonic-widget">plugin_version_label</property>
                                <property name="visible">True</property>
                                <property name="width-chars">15</property>
                                <property name="xalign">0</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkLabel" id="plugin_version_label">
                                <property name="selectable">True</property>
                                <property name="visible">True</property>
                                <property name="wrap">True</property>
                                <property name="xalign">0</property>
                                <style>
                                  <class name="bold"/>
                                </style>
                              </object>
                            </child>
                          </object>
                        </child>
                        <child>
                          <object class="GtkBox">
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel">
                                <property name="label" translatable="yes">Created by:</property>
                                <property name="mnemonic-widget">plugin_authors_label</property>
                                <property name="visible">True</property>
                                <property name="width-chars">15</property>
                                <property name="xalign">0</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkLabel" id="plugin_authors_label">
                                <property name="selectable">True</property>
                                <property name="visible">True</property>
                                <property name="wrap">True</property>
                                <property name="xalign">0</property>
                                <style>
                                  <class name="bold"/>
                                </style>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkFrame">
                    <property name="margin-top">6</property>
                    <property name="vexpand">True</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkScrolledWindow" id="plugin_description_view_container">
                            <property name="hexpand">True</property>
                            <property name="vexpand">True</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
