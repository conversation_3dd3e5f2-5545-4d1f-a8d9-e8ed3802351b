<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkAdjustment" id="_listen_port_adjustment">
    <property name="lower">1024</property>
    <property name="page-increment">10</property>
    <property name="step-increment">1</property>
    <property name="upper">65535</property>
  </object>
  <object class="GtkAdjustment" id="_auto_away_adjustment">
    <property name="page-increment">10</property>
    <property name="step-increment">5</property>
    <property name="upper">10000</property>
  </object>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="spacing">36</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Network</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkLabel">
            <property name="label" translatable="yes">Log in to an existing Soulseek account or create a new one. Usernames are case-sensitive and unique.</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="margin-top">6</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Username: </property>
                <property name="mnemonic-widget">username_entry</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkEntry" id="username_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="max-width-chars">10</property>
                    <property name="truncate-multiline">True</property>
                    <property name="visible">True</property>
                    <property name="width-chars">10</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="_change_password_button">
                    <property name="halign">end</property>
                    <property name="tooltip-text" translatable="yes">Change Password</property>
                    <property name="visible">True</property>
                    <signal name="clicked" handler="on_change_password"/>
                    <child>
                      <object class="GtkBox">
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">dialog-password-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkLabel">
                            <property name="ellipsize">end</property>
                            <property name="label" translatable="yes">Change Password</property>
                            <property name="mnemonic-widget">_change_password_button</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Listening port (TCP):</property>
                <property name="mnemonic-widget">listen_port_spinner</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton" id="listen_port_spinner">
                <property name="adjustment">_listen_port_adjustment</property>
                <property name="numeric">True</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Automatically forward listening port (UPnP/NAT-PMP)</property>
                <property name="mnemonic-widget">upnp_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="upnp_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Public IP address:</property>
                <property name="mnemonic-widget">current_port_label</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel" id="current_port_label">
                    <property name="label" translatable="yes">Unknown</property>
                    <property name="selectable">True</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="wrap-mode">word</property>
                    <property name="xalign">0</property>
                  </object>
                </child>
                <child>
                  <object class="GtkMenuButton" id="check_port_status_button">
                    <property name="tooltip-text" translatable="yes">Port Check</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">network-wired-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkLabel" id="check_port_status_label">
                            <property name="ellipsize">end</property>
                            <property name="label" translatable="yes">Port Check</property>
                            <property name="mnemonic-widget">check_port_status_button</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Away Status</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Minutes of inactivity before going away (0 to disable):</property>
                <property name="mnemonic-widget">auto_away_spinner</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton" id="auto_away_spinner">
                <property name="adjustment">_auto_away_adjustment</property>
                <property name="numeric">True</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Auto-reply message when away:</property>
                <property name="mnemonic-widget">auto_reply_message_entry</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkEntry" id="auto_reply_message_entry">
                <property name="show-emoji-icon">True</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <property name="width-chars">8</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Connections</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Connect to server on startup</property>
                <property name="mnemonic-widget">auto_connect_startup_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="auto_connect_startup_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="margin-top">6</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Soulseek server:</property>
                <property name="mnemonic-widget">soulseek_server_entry</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkEntry" id="soulseek_server_entry">
                <property name="input-hints">no-emoji</property>
                <property name="secondary-icon-name">edit-undo-symbolic</property>
                <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <property name="width-chars">8</property>
                <signal name="icon-press" handler="on_default_server"/>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="tooltip-text" translatable="yes">Makes sure a VPN is used by binding connections to a specific network interface. Usually left empty to use all the interfaces available.</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="network_interface_label">
                <property name="label" translatable="yes">Network interface:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
