<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkButton" id="icon_theme_clear_button">
    <property name="tooltip-text" translatable="yes">Clear</property>
    <property name="valign">center</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_clear_icon_theme"/>
    <child>
      <object class="GtkImage">
        <property name="icon-name">edit-clear-symbolic</property>
        <property name="visible">True</property>
      </object>
    </child>
    <style>
      <class name="image-button"/>
    </style>
  </object>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="spacing">30</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">User Interface</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Prefer dark mode</property>
                <property name="mnemonic-widget">dark_mode_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="dark_mode_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Use header bar</property>
                <property name="mnemonic-widget">header_bar_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="header_bar_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox" id="tray_options_container">
            <property name="orientation">vertical</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="height-request">24</property>
                    <property name="hexpand">True</property>
                    <property name="label" translatable="yes">Display tray icon</property>
                    <property name="mnemonic-widget">tray_icon_toggle</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="wrap-mode">word-char</property>
                    <property name="xalign">0</property>
                  </object>
                </child>
                <child>
                  <object class="GtkSwitch" id="tray_icon_toggle">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkRevealer">
                <property name="reveal-child" bind-source="tray_icon_toggle" bind-property="active" bind-flags="bidirectional|sync-create"/>
                <property name="transition-type">slide-down</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="margin-top">12</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="height-request">24</property>
                        <property name="hexpand">True</property>
                        <property name="label" translatable="yes">Minimize to tray on startup</property>
                        <property name="mnemonic-widget">minimize_tray_startup_toggle</property>
                        <property name="visible">True</property>
                        <property name="wrap">True</property>
                        <property name="wrap-mode">word-char</property>
                        <property name="xalign">0</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkSwitch" id="minimize_tray_startup_toggle">
                        <property name="valign">center</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="margin-top">6</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="language_label">
                <property name="label" translatable="yes">Language (requires a restart):</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="close_action_label">
                <property name="label" translatable="yes">When closing window:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Notifications</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Enable sound for notifications</property>
                <property name="mnemonic-widget">notification_sounds_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="notification_sounds_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Show notification for private chats and mentions in the window title</property>
                <property name="mnemonic-widget">notification_window_title_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="notification_window_title_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkFrame">
            <property name="margin-top">12</property>
            <property name="vexpand">True</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="margin-bottom">12</property>
                <property name="margin-end">12</property>
                <property name="margin-start">12</property>
                <property name="margin-top">12</property>
                <property name="orientation">vertical</property>
                <property name="spacing">18</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="label" translatable="yes">Show notifications for:</property>
                    <property name="mnemonic-widget">notification_file_download_toggle</property>
                    <property name="opacity">0.6</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="wrap-mode">word-char</property>
                    <property name="xalign">0</property>
                    <style>
                      <class name="heading"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkFlowBox">
                    <property name="column-spacing">18</property>
                    <property name="homogeneous">True</property>
                    <property name="max-children-per-line">2</property>
                    <property name="min-children-per-line">1</property>
                    <property name="row-spacing">12</property>
                    <property name="selection-mode">none</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="notification_file_download_toggle">
                            <property name="label" translatable="yes">Finished file downloads</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="notification_folder_download_toggle">
                            <property name="label" translatable="yes">Finished folder downloads</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="notification_queued_upload_toggle">
                            <property name="label" translatable="yes">Queued uploads</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="notification_wish_toggle">
                            <property name="label" translatable="yes">Wishlist results found</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="notification_private_message_toggle">
                            <property name="label" translatable="yes">Private messages</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="notification_chatroom_toggle">
                            <property name="label" translatable="yes">Chat room messages</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="notification_chatroom_mention_toggle">
                            <property name="label" translatable="yes">Chat room mentions</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="view"/>
            </style>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Tabs</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Restore the previously active main tab at startup</property>
                <property name="mnemonic-widget">tab_restore_startup_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="tab_restore_startup_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Close-buttons on secondary tabs</property>
                <property name="mnemonic-widget">tab_close_buttons_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="tab_close_buttons_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="margin-top">12</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Regular tab label color:</property>
                <property name="mnemonic-widget">color_tab_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_tab_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_tab_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Changed tab label color:</property>
                <property name="mnemonic-widget">color_tab_changed_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_tab_changed_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_tab_changed_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Highlighted tab label color:</property>
                <property name="mnemonic-widget">color_tab_highlighted_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_tab_highlighted_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_tab_highlighted_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="margin-top">12</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="buddy_list_position_label">
                <property name="label" translatable="yes">Buddy list position:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkFrame">
            <property name="margin-top">12</property>
            <property name="vexpand">True</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="margin-bottom">12</property>
                <property name="margin-end">12</property>
                <property name="margin-start">12</property>
                <property name="margin-top">12</property>
                <property name="orientation">vertical</property>
                <property name="spacing">18</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="halign">start</property>
                    <property name="label" translatable="yes">Visible main tabs:</property>
                    <property name="opacity">0.6</property>
                    <property name="selectable">True</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="wrap-mode">word-char</property>
                    <property name="xalign">0</property>
                    <style>
                      <class name="heading"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkFlowBox">
                    <property name="column-spacing">18</property>
                    <property name="homogeneous">True</property>
                    <property name="max-children-per-line">3</property>
                    <property name="min-children-per-line">1</property>
                    <property name="row-spacing">12</property>
                    <property name="selection-mode">none</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="tab_visible_search_toggle">
                            <property name="label" translatable="yes">Search Files</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="tab_visible_downloads_toggle">
                            <property name="label" translatable="yes">Downloads</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="tab_visible_uploads_toggle">
                            <property name="label" translatable="yes">Uploads</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="tab_visible_browse_toggle">
                            <property name="label" translatable="yes">Browse Shares</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="tab_visible_userinfo_toggle">
                            <property name="label" translatable="yes">User Profiles</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="tab_visible_private_chat_toggle">
                            <property name="label" translatable="yes">Private Chat</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="tab_visible_userlist_toggle">
                            <property name="label" translatable="yes">Buddies</property>
                            <property name="visible">True</property>
                            <signal name="toggled" handler="on_buddy_list_tab_toggled"/>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="tab_visible_chatrooms_toggle">
                            <property name="label" translatable="yes">Chat Rooms</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkCheckButton" id="tab_visible_interests_toggle">
                            <property name="label" translatable="yes">Interests</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="view"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkFrame">
            <property name="margin-top">12</property>
            <property name="vexpand">True</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="margin-bottom">12</property>
                <property name="margin-end">12</property>
                <property name="margin-start">12</property>
                <property name="margin-top">12</property>
                <property name="orientation">vertical</property>
                <property name="spacing">12</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="halign">start</property>
                    <property name="label" translatable="yes">Tab bar positions:</property>
                    <property name="opacity">0.6</property>
                    <property name="selectable">True</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="wrap-mode">word-char</property>
                    <property name="xalign">0</property>
                    <style>
                      <class name="heading"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkFlowBox">
                    <property name="column-spacing">18</property>
                    <property name="homogeneous">True</property>
                    <property name="margin-bottom">6</property>
                    <property name="margin-end">6</property>
                    <property name="margin-start">6</property>
                    <property name="max-children-per-line">3</property>
                    <property name="min-children-per-line">1</property>
                    <property name="row-spacing">10</property>
                    <property name="selection-mode">none</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="homogeneous">True</property>
                            <property name="spacing">18</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel" id="tab_position_main_label">
                                <property name="label" translatable="yes">Main tabs</property>
                                <property name="visible">True</property>
                                <property name="wrap">True</property>
                                <property name="wrap-mode">word-char</property>
                                <property name="xalign">1</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="homogeneous">True</property>
                            <property name="spacing">18</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel" id="tab_position_search_label">
                                <property name="label" translatable="yes">Search Files</property>
                                <property name="visible">True</property>
                                <property name="wrap">True</property>
                                <property name="wrap-mode">word-char</property>
                                <property name="xalign">1</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="homogeneous">True</property>
                            <property name="spacing">18</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel" id="tab_position_browse_label">
                                <property name="label" translatable="yes">Browse Shares</property>
                                <property name="visible">True</property>
                                <property name="wrap">True</property>
                                <property name="wrap-mode">word-char</property>
                                <property name="xalign">1</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="homogeneous">True</property>
                            <property name="spacing">18</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel" id="tab_position_userinfo_label">
                                <property name="label" translatable="yes">User Profiles</property>
                                <property name="visible">True</property>
                                <property name="wrap">True</property>
                                <property name="wrap-mode">word-char</property>
                                <property name="xalign">1</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="homogeneous">True</property>
                            <property name="spacing">18</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel" id="tab_position_private_chat_label">
                                <property name="label" translatable="yes">Private Chat</property>
                                <property name="visible">True</property>
                                <property name="wrap">True</property>
                                <property name="wrap-mode">word-char</property>
                                <property name="xalign">1</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkFlowBoxChild">
                        <property name="can-focus">False</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="homogeneous">True</property>
                            <property name="spacing">18</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkLabel" id="tab_position_chatrooms_label">
                                <property name="label" translatable="yes">Chat Rooms</property>
                                <property name="visible">True</property>
                                <property name="wrap">True</property>
                                <property name="wrap-mode">word-char</property>
                                <property name="xalign">1</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="view"/>
            </style>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Lists</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Show reverse file paths (requires a restart)</property>
                <property name="mnemonic-widget">reverse_file_paths_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="reverse_file_paths_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Show exact file sizes (requires a restart)</property>
                <property name="mnemonic-widget">exact_file_sizes_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="exact_file_sizes_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="margin-top">6</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">List text color:</property>
                <property name="mnemonic-widget">color_list_text_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_list_text_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_list_text_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Chats</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Enable colored usernames</property>
                <property name="mnemonic-widget">chat_colored_usernames_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="chat_colored_usernames_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="margin-top">6</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="chat_username_appearance_label">
                <property name="label" translatable="yes">Chat username appearance:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="margin-top">12</property>
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Remote text color:</property>
                <property name="mnemonic-widget">color_chat_remote_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_chat_remote_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_chat_remote_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Local text color:</property>
                <property name="mnemonic-widget">color_chat_local_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_chat_local_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_chat_local_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Command output text color:</property>
                <property name="mnemonic-widget">color_chat_command_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_chat_command_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_chat_command_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">/me action text color:</property>
                <property name="mnemonic-widget">color_chat_action_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_chat_action_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_chat_action_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Highlighted text color:</property>
                <property name="mnemonic-widget">color_chat_highlighted_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_chat_highlighted_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_chat_highlighted_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">URL link text color:</property>
                <property name="mnemonic-widget">color_url_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_url_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_url_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">User Statuses</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Online color:</property>
                <property name="mnemonic-widget">color_status_online_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_status_online_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_status_online_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Away color:</property>
                <property name="mnemonic-widget">color_status_away_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_status_away_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_status_away_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Offline color:</property>
                <property name="mnemonic-widget">color_status_offline_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_status_offline_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_status_offline_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Text Entries</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Text entry background color:</property>
                <property name="mnemonic-widget">color_input_background_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_input_background_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_input_background_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Text entry text color:</property>
                <property name="mnemonic-widget">color_input_text_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkColorButton" id="color_input_text_button">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkEntry" id="color_input_text_entry">
                    <property name="hexpand">True</property>
                    <property name="input-hints">no-emoji</property>
                    <property name="secondary-icon-name">edit-undo-symbolic</property>
                    <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                    <property name="visible">True</property>
                    <property name="width-chars">4</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Fonts</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Global font:</property>
                <property name="mnemonic-widget">font_global_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkFontButton" id="font_global_button">
                    <property name="hexpand">True</property>
                    <property name="use-font">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="font_global_clear_button">
                    <property name="tooltip-text" translatable="yes">Clear</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">edit-clear-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <style>
                      <class name="image-button"/>
                    </style>
                  </object>
                </child>
                <style>
                  <class name="linked"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">List font:</property>
                <property name="mnemonic-widget">font_list_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkFontButton" id="font_list_button">
                    <property name="hexpand">True</property>
                    <property name="use-font">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="font_list_clear_button">
                    <property name="tooltip-text" translatable="yes">Clear</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">edit-clear-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <style>
                      <class name="image-button"/>
                    </style>
                  </object>
                </child>
                <style>
                  <class name="linked"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Text view font:</property>
                <property name="mnemonic-widget">font_text_view_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkFontButton" id="font_text_view_button">
                    <property name="hexpand">True</property>
                    <property name="use-font">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="font_text_view_clear_button">
                    <property name="tooltip-text" translatable="yes">Clear</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">edit-clear-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <style>
                      <class name="image-button"/>
                    </style>
                  </object>
                </child>
                <style>
                  <class name="linked"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Chat font:</property>
                <property name="mnemonic-widget">font_chat_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkFontButton" id="font_chat_button">
                    <property name="hexpand">True</property>
                    <property name="use-font">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="font_chat_clear_button">
                    <property name="tooltip-text" translatable="yes">Clear</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">edit-clear-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <style>
                      <class name="image-button"/>
                    </style>
                  </object>
                </child>
                <style>
                  <class name="linked"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Transfers font:</property>
                <property name="mnemonic-widget">font_transfers_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkFontButton" id="font_transfers_button">
                    <property name="hexpand">True</property>
                    <property name="use-font">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="font_transfers_clear_button">
                    <property name="tooltip-text" translatable="yes">Clear</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">edit-clear-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <style>
                      <class name="image-button"/>
                    </style>
                  </object>
                </child>
                <style>
                  <class name="linked"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Search font:</property>
                <property name="mnemonic-widget">font_search_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkFontButton" id="font_search_button">
                    <property name="hexpand">True</property>
                    <property name="use-font">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="font_search_clear_button">
                    <property name="tooltip-text" translatable="yes">Clear</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">edit-clear-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <style>
                      <class name="image-button"/>
                    </style>
                  </object>
                </child>
                <style>
                  <class name="linked"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Browse font:</property>
                <property name="mnemonic-widget">font_browse_button</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="valign">center</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkFontButton" id="font_browse_button">
                    <property name="hexpand">True</property>
                    <property name="use-font">True</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="font_browse_clear_button">
                    <property name="tooltip-text" translatable="yes">Clear</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">edit-clear-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <style>
                      <class name="image-button"/>
                    </style>
                  </object>
                </child>
                <style>
                  <class name="linked"/>
                </style>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Icons</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="icon_theme_label">
                <property name="label" translatable="yes">Icon theme folder:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkFrame">
            <property name="margin-top">6</property>
            <property name="vexpand">True</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkFlowBox" id="icon_view">
                <property name="column-spacing">24</property>
                <property name="margin-bottom">24</property>
                <property name="margin-end">24</property>
                <property name="margin-start">24</property>
                <property name="margin-top">24</property>
                <property name="min-children-per-line">2</property>
                <property name="row-spacing">24</property>
                <property name="selection-mode">none</property>
                <property name="vexpand">True</property>
                <property name="visible">True</property>
              </object>
            </child>
            <style>
              <class name="view"/>
            </style>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
