<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkButton" id="chatroom_log_folder_default_button">
    <property name="tooltip-text" translatable="yes">Default</property>
    <property name="valign">center</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_default_chatroom_log_folder"/>
    <child>
      <object class="GtkImage">
        <property name="icon-name">edit-undo-symbolic</property>
        <property name="visible">True</property>
      </object>
    </child>
    <style>
      <class name="image-button"/>
    </style>
  </object>
  <object class="GtkButton" id="private_chat_log_folder_default_button">
    <property name="tooltip-text" translatable="yes">Default</property>
    <property name="valign">center</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_default_private_chat_log_folder"/>
    <child>
      <object class="GtkImage">
        <property name="icon-name">edit-undo-symbolic</property>
        <property name="visible">True</property>
      </object>
    </child>
    <style>
      <class name="image-button"/>
    </style>
  </object>
  <object class="GtkButton" id="transfer_log_folder_default_button">
    <property name="tooltip-text" translatable="yes">Default</property>
    <property name="valign">center</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_default_transfer_log_folder"/>
    <child>
      <object class="GtkImage">
        <property name="icon-name">edit-undo-symbolic</property>
        <property name="visible">True</property>
      </object>
    </child>
    <style>
      <class name="image-button"/>
    </style>
  </object>
  <object class="GtkButton" id="debug_log_folder_default_button">
    <property name="tooltip-text" translatable="yes">Default</property>
    <property name="valign">center</property>
    <property name="visible">True</property>
    <signal name="clicked" handler="on_default_debug_log_folder"/>
    <child>
      <object class="GtkImage">
        <property name="icon-name">edit-undo-symbolic</property>
        <property name="visible">True</property>
      </object>
    </child>
    <style>
      <class name="image-button"/>
    </style>
  </object>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="spacing">6</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Logging</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Log chatrooms by default</property>
                <property name="mnemonic-widget">log_chatroom_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="log_chatroom_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Log private chat by default</property>
                <property name="mnemonic-widget">log_private_chat_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="log_private_chat_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Log transfers to file</property>
                <property name="mnemonic-widget">log_transfer_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="log_transfer_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Log debug messages to file</property>
                <property name="mnemonic-widget">log_debug_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="log_debug_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="margin-top">6</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Log timestamp format:</property>
                <property name="mnemonic-widget">log_timestamp_format_entry</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkEntry" id="log_timestamp_format_entry">
                <property name="secondary-icon-name">edit-undo-symbolic</property>
                <property name="secondary-icon-tooltip-text" translatable="yes">Default</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
                <property name="width-chars">8</property>
                <signal name="icon-press" handler="on_default_timestamp"/>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkLabel" id="format_codes_label">
            <property name="halign">end</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox" id="folder_locations_container">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Folder Locations</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="chatroom_log_folder_label">
                <property name="label" translatable="yes">Chatroom logs folder:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="private_chat_log_folder_label">
                <property name="label" translatable="yes">Private chat logs folder:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="transfer_log_folder_label">
                <property name="label" translatable="yes">Transfer logs folder:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="debug_log_folder_label">
                <property name="label" translatable="yes">Debug logs folder:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
