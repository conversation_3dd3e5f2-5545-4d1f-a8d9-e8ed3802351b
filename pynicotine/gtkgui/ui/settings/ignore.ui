<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="spacing">12</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Ignored Users</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkLabel">
            <property name="label" translatable="yes">Ignore chat messages and search results from users, based on username or IP address.</property>
            <property name="margin-bottom">12</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkFlowBox">
        <property name="column-spacing">18</property>
        <property name="homogeneous">True</property>
        <property name="max-children-per-line">2</property>
        <property name="row-spacing">24</property>
        <property name="selection-mode">none</property>
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkFlowBoxChild">
            <property name="can-focus">False</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="orientation">vertical</property>
                <property name="spacing">12</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="halign">start</property>
                    <property name="label" translatable="yes">Users</property>
                    <property name="selectable">True</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="xalign">0</property>
                    <style>
                      <class name="heading"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkFrame">
                    <property name="vexpand">True</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="orientation">vertical</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkScrolledWindow" id="ignored_users_container">
                            <property name="hexpand">True</property>
                            <property name="vexpand">True</property>
                            <property name="visible">True</property>
                            <style>
                              <class name="border-bottom"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkBox">
                            <property name="margin-bottom">6</property>
                            <property name="margin-end">6</property>
                            <property name="margin-start">6</property>
                            <property name="margin-top">6</property>
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkButton" id="_add_ignored_user_button">
                                <property name="tooltip-text" translatable="yes">Add…</property>
                                <property name="visible">True</property>
                                <signal name="clicked" handler="on_add_ignored_user"/>
                                <child>
                                  <object class="GtkBox">
                                    <property name="spacing">6</property>
                                    <property name="visible">True</property>
                                    <child>
                                      <object class="GtkImage">
                                        <property name="icon-name">list-add-symbolic</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                    <child>
                                      <object class="GtkLabel">
                                        <property name="ellipsize">end</property>
                                        <property name="label" translatable="yes">Add…</property>
                                        <property name="mnemonic-widget">_add_ignored_user_button</property>
                                        <property name="use-underline">True</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                  </object>
                                </child>
                                <style>
                                  <class name="flat"/>
                                </style>
                              </object>
                            </child>
                            <child>
                              <object class="GtkButton" id="_remove_ignored_user_button">
                                <property name="tooltip-text" translatable="yes">Remove</property>
                                <property name="visible">True</property>
                                <signal name="clicked" handler="on_remove_ignored_user"/>
                                <child>
                                  <object class="GtkBox">
                                    <property name="spacing">6</property>
                                    <property name="visible">True</property>
                                    <child>
                                      <object class="GtkImage">
                                        <property name="icon-name">list-remove-symbolic</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                    <child>
                                      <object class="GtkLabel">
                                        <property name="ellipsize">end</property>
                                        <property name="label" translatable="yes">Remove</property>
                                        <property name="mnemonic-widget">_remove_ignored_user_button</property>
                                        <property name="use-underline">True</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                  </object>
                                </child>
                                <style>
                                  <class name="flat"/>
                                </style>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkFlowBoxChild">
            <property name="can-focus">False</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="orientation">vertical</property>
                <property name="spacing">12</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="halign">start</property>
                    <property name="label" translatable="yes">IP Addresses</property>
                    <property name="selectable">True</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="xalign">0</property>
                    <style>
                      <class name="heading"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkFrame">
                    <property name="vexpand">True</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="orientation">vertical</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkScrolledWindow" id="ignored_ips_container">
                            <property name="hexpand">True</property>
                            <property name="vexpand">True</property>
                            <property name="visible">True</property>
                            <property name="width-request">260</property>
                            <style>
                              <class name="border-bottom"/>
                            </style>
                          </object>
                        </child>
                        <child>
                          <object class="GtkBox">
                            <property name="margin-bottom">6</property>
                            <property name="margin-end">6</property>
                            <property name="margin-start">6</property>
                            <property name="margin-top">6</property>
                            <property name="spacing">6</property>
                            <property name="visible">True</property>
                            <child>
                              <object class="GtkButton" id="_add_ignored_ip_button">
                                <property name="tooltip-text" translatable="yes">Add…</property>
                                <property name="visible">True</property>
                                <signal name="clicked" handler="on_add_ignored_ip"/>
                                <child>
                                  <object class="GtkBox">
                                    <property name="spacing">6</property>
                                    <property name="visible">True</property>
                                    <child>
                                      <object class="GtkImage">
                                        <property name="icon-name">list-add-symbolic</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                    <child>
                                      <object class="GtkLabel">
                                        <property name="ellipsize">end</property>
                                        <property name="label" translatable="yes">Add…</property>
                                        <property name="mnemonic-widget">_add_ignored_ip_button</property>
                                        <property name="use-underline">True</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                  </object>
                                </child>
                                <style>
                                  <class name="flat"/>
                                </style>
                              </object>
                            </child>
                            <child>
                              <object class="GtkButton" id="_remove_ignored_ip_button">
                                <property name="tooltip-text" translatable="yes">Remove</property>
                                <property name="visible">True</property>
                                <signal name="clicked" handler="on_remove_ignored_ip"/>
                                <child>
                                  <object class="GtkBox">
                                    <property name="spacing">6</property>
                                    <property name="visible">True</property>
                                    <child>
                                      <object class="GtkImage">
                                        <property name="icon-name">list-remove-symbolic</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                    <child>
                                      <object class="GtkLabel">
                                        <property name="ellipsize">end</property>
                                        <property name="label" translatable="yes">Remove</property>
                                        <property name="mnemonic-widget">_remove_ignored_ip_button</property>
                                        <property name="use-underline">True</property>
                                        <property name="visible">True</property>
                                      </object>
                                    </child>
                                  </object>
                                </child>
                                <style>
                                  <class name="flat"/>
                                </style>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
