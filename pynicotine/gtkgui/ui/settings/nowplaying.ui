<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2009-2025 <PERSON><PERSON>+ Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="spacing">30</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Now Playing</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkLabel">
            <property name="label" translatable="yes">Now Playing allows you to display what your media player is playing by using the /now command in chat.</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
          </object>
        </child>
        <child>
          <object class="GtkRadioButton" id="lastfm_radio">
            <property name="label">Last.fm</property>
            <property name="visible">True</property>
            <signal name="toggled" handler="update_now_playing_info"/>
          </object>
        </child>
        <child>
          <object class="GtkRadioButton" id="librefm_radio">
            <property name="group">lastfm_radio</property>
            <property name="label">Libre.fm</property>
            <property name="visible">True</property>
            <signal name="toggled" handler="update_now_playing_info"/>
          </object>
        </child>
        <child>
          <object class="GtkRadioButton" id="listenbrainz_radio">
            <property name="group">lastfm_radio</property>
            <property name="label">ListenBrainz</property>
            <property name="visible">True</property>
            <signal name="toggled" handler="update_now_playing_info"/>
          </object>
        </child>
        <child>
          <object class="GtkRadioButton" id="mpris_radio">
            <property name="group">lastfm_radio</property>
            <property name="label">MPRIS (v2)</property>
            <property name="visible">True</property>
            <signal name="toggled" handler="update_now_playing_info"/>
          </object>
        </child>
        <child>
          <object class="GtkRadioButton" id="other_radio">
            <property name="group">lastfm_radio</property>
            <property name="label" translatable="yes">Other</property>
            <property name="visible">True</property>
            <signal name="toggled" handler="update_now_playing_info"/>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="command_label">
                <property name="mnemonic-widget">command_entry</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkEntry" id="command_entry">
                <property name="valign">center</property>
                <property name="visible">True</property>
                <property name="width-chars">8</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Now Playing Format</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkLabel" id="format_help_label">
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="homogeneous">True</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel" id="format_message_label">
                <property name="label" translatable="yes">Now Playing message format:</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="margin-top">6</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkButton" id="test_configuration_button">
                <property name="halign">start</property>
                <property name="valign">start</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="spacing">6</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage">
                        <property name="icon-name">folder-music-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="label" translatable="yes">Test Configuration</property>
                        <property name="mnemonic-widget">test_configuration_button</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkLabel" id="output_label">
                <property name="hexpand">True</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="xalign">1</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
