<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="spacing">12</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkLabel">
        <property name="halign">start</property>
        <property name="label" translatable="yes">URL Handlers</property>
        <property name="mnemonic-widget">protocol_list_container</property>
        <property name="selectable">True</property>
        <property name="visible">True</property>
        <property name="wrap">True</property>
        <property name="xalign">0</property>
        <style>
          <class name="heading"/>
        </style>
      </object>
    </child>
    <child>
      <object class="GtkLabel">
        <property name="label" translatable="yes">Instances of $ are replaced by the URL. Default system applications are used in cases where a protocol has not been configured.</property>
        <property name="selectable">True</property>
        <property name="visible">True</property>
        <property name="wrap">True</property>
        <property name="xalign">0</property>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="homogeneous">True</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel" id="file_manager_label">
            <property name="label" translatable="yes">File manager command:</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="wrap-mode">word-char</property>
            <property name="xalign">0</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkFrame">
        <property name="margin-top">6</property>
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkBox">
            <property name="orientation">vertical</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkScrolledWindow" id="protocol_list_container">
                <property name="hexpand">True</property>
                <property name="vexpand">True</property>
                <property name="visible">True</property>
                <style>
                  <class name="border-bottom"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkBox">
                <property name="margin-bottom">6</property>
                <property name="margin-end">6</property>
                <property name="margin-start">6</property>
                <property name="margin-top">6</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkButton" id="_add_handler_button">
                    <property name="tooltip-text" translatable="yes">Add…</property>
                    <property name="visible">True</property>
                    <signal name="clicked" handler="on_add_handler"/>
                    <child>
                      <object class="GtkBox">
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">list-add-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkLabel">
                            <property name="ellipsize">end</property>
                            <property name="label" translatable="yes">Add…</property>
                            <property name="mnemonic-widget">_add_handler_button</property>
                            <property name="use-underline">True</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <style>
                      <class name="flat"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="_edit_handler_button">
                    <property name="tooltip-text" translatable="yes">Edit…</property>
                    <property name="visible">True</property>
                    <signal name="clicked" handler="on_edit_handler"/>
                    <child>
                      <object class="GtkBox">
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">document-edit-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkLabel">
                            <property name="ellipsize">end</property>
                            <property name="label" translatable="yes">Edit…</property>
                            <property name="mnemonic-widget">_edit_handler_button</property>
                            <property name="use-underline">True</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <style>
                      <class name="flat"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkButton" id="_remove_handler_button">
                    <property name="tooltip-text" translatable="yes">Remove</property>
                    <property name="visible">True</property>
                    <signal name="clicked" handler="on_remove_handler"/>
                    <child>
                      <object class="GtkBox">
                        <property name="spacing">6</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkImage">
                            <property name="icon-name">list-remove-symbolic</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkLabel">
                            <property name="ellipsize">end</property>
                            <property name="label" translatable="yes">Remove</property>
                            <property name="mnemonic-widget">_remove_handler_button</property>
                            <property name="use-underline">True</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <style>
                      <class name="flat"/>
                    </style>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
