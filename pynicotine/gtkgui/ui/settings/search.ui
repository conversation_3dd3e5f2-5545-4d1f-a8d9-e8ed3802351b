<?xml version="1.0" encoding="UTF-8"?>
<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 Nicotine Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->
<interface>
  <requires lib="gtk+" version="3.0"/>
  <object class="GtkAdjustment" id="_max_displayed_results_adjustment">
    <property name="lower">100</property>
    <property name="page-increment">100</property>
    <property name="step-increment">50</property>
    <property name="upper">25000</property>
  </object>
  <object class="GtkAdjustment" id="_min_search_term_length_adjustment">
    <property name="page-increment">5</property>
    <property name="step-increment">1</property>
    <property name="upper">50</property>
  </object>
  <object class="GtkAdjustment" id="_max_sent_results_adjustment">
    <property name="lower">50</property>
    <property name="page-increment">50</property>
    <property name="step-increment">25</property>
    <property name="upper">10000</property>
  </object>
  <object class="GtkBox" id="container">
    <property name="orientation">vertical</property>
    <property name="spacing">30</property>
    <property name="visible">True</property>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Searches</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Enable search history</property>
                <property name="mnemonic-widget">enable_search_history_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="enable_search_history_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="tooltip-text" translatable="yes">Privately shared files that have been made visible to everyone will be prefixed with '[PRIVATE]', and cannot be downloaded until the uploader gives explicit permission. Ask them kindly.</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="height-request">24</property>
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Show privately shared files in search results</property>
                <property name="mnemonic-widget">show_private_results_toggle</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkImage">
                <property name="icon-name">dialog-question-symbolic</property>
                <property name="visible">True</property>
              </object>
            </child>
            <child>
              <object class="GtkSwitch" id="show_private_results_toggle">
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="margin-top">6</property>
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="hexpand">True</property>
                <property name="label" translatable="yes">Limit number of results per search:</property>
                <property name="mnemonic-widget">max_displayed_results_spinner</property>
                <property name="visible">True</property>
                <property name="wrap">True</property>
                <property name="wrap-mode">word-char</property>
                <property name="xalign">0</property>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton" id="max_displayed_results_spinner">
                <property name="adjustment">_max_displayed_results_adjustment</property>
                <property name="numeric">True</property>
                <property name="valign">center</property>
                <property name="visible">True</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkBox">
            <property name="spacing">12</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkLabel">
                <property name="halign">start</property>
                <property name="label" translatable="yes">Search Result Filters</property>
                <property name="selectable">True</property>
                <property name="visible">True</property>
                <property name="xalign">0</property>
                <style>
                  <class name="heading"/>
                </style>
              </object>
            </child>
            <child>
              <object class="GtkMenuButton" id="filter_help_button">
                <property name="tooltip-text" translatable="yes">Result Filter Help</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkImage">
                    <property name="icon-name">dialog-question-symbolic</property>
                    <property name="visible">True</property>
                  </object>
                </child>
                <style>
                  <class name="circular"/>
                  <class name="image-button"/>
                </style>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="orientation">vertical</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="height-request">24</property>
                    <property name="hexpand">True</property>
                    <property name="label" translatable="yes">Enable search result filters by default</property>
                    <property name="mnemonic-widget">enable_default_filters_toggle</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="wrap-mode">word-char</property>
                    <property name="xalign">0</property>
                  </object>
                </child>
                <child>
                  <object class="GtkSwitch" id="enable_default_filters_toggle">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkRevealer">
                <property name="reveal-child" bind-source="enable_default_filters_toggle" bind-property="active" bind-flags="bidirectional|sync-create"/>
                <property name="transition-type">slide-down</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="homogeneous">True</property>
                        <property name="margin-top">18</property>
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="label" translatable="yes">Include:</property>
                            <property name="mnemonic-widget">filter_include_entry</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="wrap-mode">word-char</property>
                            <property name="xalign">0</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkEntry" id="filter_include_entry">
                            <property name="tooltip-text" translatable="yes">Add a filter to only show results whose file paths have the given text. Use a '|' to specify multiple words or phrases, for example: long trail|till we meet again</property>
                            <property name="valign">center</property>
                            <property name="visible">True</property>
                            <property name="width-chars">8</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="homogeneous">True</property>
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="label" translatable="yes">Exclude:</property>
                            <property name="mnemonic-widget">filter_exclude_entry</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="wrap-mode">word-char</property>
                            <property name="xalign">0</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkEntry" id="filter_exclude_entry">
                            <property name="tooltip-text" translatable="yes">Add a filter to hide results whose file paths have the given text. Use a '|' to specify multiple words or phrases, for example: long trail|till we meet again</property>
                            <property name="valign">center</property>
                            <property name="visible">True</property>
                            <property name="width-chars">8</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="homogeneous">True</property>
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="label" translatable="yes">File Type:</property>
                            <property name="mnemonic-widget">filter_file_type_entry</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="wrap-mode">word-char</property>
                            <property name="xalign">0</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkEntry" id="filter_file_type_entry">
                            <property name="halign">end</property>
                            <property name="input-hints">no-emoji</property>
                            <property name="max-width-chars">26</property>
                            <property name="tooltip-text" translatable="yes">File type, e.g. flac wav or !mp3 !m4a</property>
                            <property name="valign">center</property>
                            <property name="visible">True</property>
                            <property name="width-chars">8</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="homogeneous">True</property>
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="label" translatable="yes">Size:</property>
                            <property name="mnemonic-widget">filter_file_size_entry</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="wrap-mode">word-char</property>
                            <property name="xalign">0</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkEntry" id="filter_file_size_entry">
                            <property name="halign">end</property>
                            <property name="input-hints">no-emoji</property>
                            <property name="max-width-chars">26</property>
                            <property name="tooltip-text" translatable="yes">File size, e.g. &gt;10.5m &lt;1g</property>
                            <property name="valign">center</property>
                            <property name="visible">True</property>
                            <property name="width-chars">8</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="homogeneous">True</property>
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="label" translatable="yes">Bitrate:</property>
                            <property name="mnemonic-widget">filter_bitrate_entry</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="wrap-mode">word-char</property>
                            <property name="xalign">0</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkEntry" id="filter_bitrate_entry">
                            <property name="halign">end</property>
                            <property name="input-hints">no-emoji</property>
                            <property name="max-width-chars">26</property>
                            <property name="tooltip-text" translatable="yes">Bitrate, e.g. 256 &lt;1412</property>
                            <property name="valign">center</property>
                            <property name="visible">True</property>
                            <property name="width-chars">8</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="homogeneous">True</property>
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="label" translatable="yes">Duration:</property>
                            <property name="mnemonic-widget">filter_length_entry</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="wrap-mode">word-char</property>
                            <property name="xalign">0</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkEntry" id="filter_length_entry">
                            <property name="halign">end</property>
                            <property name="input-hints">no-emoji</property>
                            <property name="max-width-chars">26</property>
                            <property name="tooltip-text" translatable="yes">Duration, e.g. &gt;6:00 &lt;12:00 !6:54</property>
                            <property name="valign">center</property>
                            <property name="visible">True</property>
                            <property name="width-chars">8</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="homogeneous">True</property>
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="label" translatable="yes">Country Code:</property>
                            <property name="mnemonic-widget">filter_country_entry</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="wrap-mode">word-char</property>
                            <property name="xalign">0</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkEntry" id="filter_country_entry">
                            <property name="halign">end</property>
                            <property name="input-hints">no-emoji</property>
                            <property name="max-width-chars">26</property>
                            <property name="tooltip-text" translatable="yes">Country code, e.g. US ES or !DE !GB</property>
                            <property name="valign">center</property>
                            <property name="visible">True</property>
                            <property name="width-chars">8</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="height-request">24</property>
                            <property name="hexpand">True</property>
                            <property name="label" translatable="yes">Free Slot</property>
                            <property name="mnemonic-widget">filter_free_slot_toggle</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="wrap-mode">word-char</property>
                            <property name="xalign">0</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkSwitch" id="filter_free_slot_toggle">
                            <property name="tooltip-text" translatable="yes">Only show results from users with an available upload slot.</property>
                            <property name="valign">center</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="height-request">24</property>
                            <property name="hexpand">True</property>
                            <property name="label" translatable="yes">Public Files</property>
                            <property name="mnemonic-widget">filter_public_files_toggle</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="wrap-mode">word-char</property>
                            <property name="xalign">0</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkSwitch" id="filter_public_files_toggle">
                            <property name="tooltip-text" translatable="yes">Only show files that are shared publicly.</property>
                            <property name="valign">center</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkLabel">
            <property name="halign">start</property>
            <property name="label" translatable="yes">Network Searches</property>
            <property name="selectable">True</property>
            <property name="visible">True</property>
            <property name="wrap">True</property>
            <property name="xalign">0</property>
            <style>
              <class name="heading"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkBox">
            <property name="orientation">vertical</property>
            <property name="visible">True</property>
            <child>
              <object class="GtkBox">
                <property name="spacing">12</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkLabel">
                    <property name="height-request">24</property>
                    <property name="hexpand">True</property>
                    <property name="label" translatable="yes">Respond to search requests from other users</property>
                    <property name="mnemonic-widget">repond_search_requests_toggle</property>
                    <property name="visible">True</property>
                    <property name="wrap">True</property>
                    <property name="wrap-mode">word-char</property>
                    <property name="xalign">0</property>
                  </object>
                </child>
                <child>
                  <object class="GtkSwitch" id="repond_search_requests_toggle">
                    <property name="valign">center</property>
                    <property name="visible">True</property>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkRevealer">
                <property name="reveal-child" bind-source="repond_search_requests_toggle" bind-property="active" bind-flags="bidirectional|sync-create"/>
                <property name="transition-type">slide-down</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="spacing">12</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkBox">
                        <property name="margin-top">18</property>
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="hexpand">True</property>
                            <property name="label" translatable="yes">Searches shorter than this number of characters will be ignored:</property>
                            <property name="mnemonic-widget">min_search_term_length_spinner</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="wrap-mode">word-char</property>
                            <property name="xalign">0</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkSpinButton" id="min_search_term_length_spinner">
                            <property name="adjustment">_min_search_term_length_adjustment</property>
                            <property name="numeric">True</property>
                            <property name="valign">center</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                    <child>
                      <object class="GtkBox">
                        <property name="spacing">12</property>
                        <property name="visible">True</property>
                        <child>
                          <object class="GtkLabel">
                            <property name="hexpand">True</property>
                            <property name="label" translatable="yes">Maximum search results to send per search request:</property>
                            <property name="mnemonic-widget">max_sent_results_spinner</property>
                            <property name="visible">True</property>
                            <property name="wrap">True</property>
                            <property name="wrap-mode">word-char</property>
                            <property name="xalign">0</property>
                          </object>
                        </child>
                        <child>
                          <object class="GtkSpinButton" id="max_sent_results_spinner">
                            <property name="adjustment">_max_sent_results_adjustment</property>
                            <property name="numeric">True</property>
                            <property name="valign">center</property>
                            <property name="visible">True</property>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkBox">
        <property name="halign">center</property>
        <property name="homogeneous">True</property>
        <property name="spacing">6</property>
        <property name="valign">end</property>
        <property name="vexpand">True</property>
        <property name="visible">True</property>
        <child>
          <object class="GtkButton" id="_clear_search_history_button">
            <property name="halign">center</property>
            <property name="height-request">30</property>
            <property name="visible">True</property>
            <signal name="clicked" handler="on_clear_search_history"/>
            <child>
              <object class="GtkBox">
                <property name="margin-end">12</property>
                <property name="margin-start">12</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkStack">
                    <property name="transition-type">crossfade</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage" id="clear_search_history_icon">
                        <property name="icon-name">edit-clear-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkImage" id="clear_search_history_success_icon">
                        <property name="icon-name">object-select-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkLabel">
                    <property name="ellipsize">end</property>
                    <property name="label" translatable="yes">Clear Search History</property>
                    <property name="mnemonic-widget">_clear_search_history_button</property>
                    <property name="visible">True</property>
                    <property name="xalign">0</property>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="circular"/>
              <class name="destructive-action"/>
              <class name="image-text-button"/>
            </style>
          </object>
        </child>
        <child>
          <object class="GtkButton" id="_clear_filter_history_button">
            <property name="halign">center</property>
            <property name="height-request">30</property>
            <property name="visible">True</property>
            <signal name="clicked" handler="on_clear_filter_history"/>
            <child>
              <object class="GtkBox">
                <property name="margin-end">12</property>
                <property name="margin-start">12</property>
                <property name="spacing">6</property>
                <property name="visible">True</property>
                <child>
                  <object class="GtkStack">
                    <property name="transition-type">crossfade</property>
                    <property name="visible">True</property>
                    <child>
                      <object class="GtkImage" id="clear_filter_history_icon">
                        <property name="icon-name">edit-clear-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkImage" id="clear_filter_history_success_icon">
                        <property name="icon-name">object-select-symbolic</property>
                        <property name="visible">True</property>
                      </object>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkLabel">
                    <property name="ellipsize">end</property>
                    <property name="label" translatable="yes">Clear Filter History</property>
                    <property name="mnemonic-widget">_clear_filter_history_button</property>
                    <property name="visible">True</property>
                    <property name="xalign">0</property>
                  </object>
                </child>
              </object>
            </child>
            <style>
              <class name="circular"/>
              <class name="destructive-action"/>
              <class name="image-text-button"/>
            </style>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
