<!--
  SPDX-FileCopyrightText: 2004-2025 <PERSON><PERSON>+ Contributors
  SPDX-FileCopyrightText: 2003-2004 <PERSON><PERSON> Contributors
  SPDX-FileCopyrightText: 2002-2003 PySoulSeek Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->

# Authors

## Nico<PERSON>+ Team

### Mat (mathiascode)
 - Maintainer (2020–present)
 - Developer

### <PERSON> (eLvErDe)
 - Maintainer (2013–2016)
 - Domain name administrator
 - Source code migration from SVN to GitHub
 - Developer

### Han Boetes
 - Tester
 - Documentation
 - Bug hunting
 - Translation management

### alekksander
 - Tester
 - Redesign of some graphics

### slook
 - Tester
 - Accessibility improvements

### ketacat
 - Tester

## Nicotine+ Team (Emeritus)

### daelstorm
 - Maintainer (2004–2009)
 - Developer

### quinox
 - Maintainer (2009–2012)
 - Developer

### <PERSON> (gfarmerfr)
 - Maintainer (2016–2017)
 - Developer

### Ki<PERSON>
 - Maintainer (2018–2020)
 - Developer
 - Debianization

### gallows (aka 'burp O')
 - Developer
 - Packager
 - Submitted Slack.Build file

### hedonist (formerly known as alexbk)
 - OS X Nicotine.app maintainer / developer
 - Author of PySoulSeek, used for Nicotine core

### lee8oi
 - Bash commander
 - New and updated /alias

### INMCM
 - Nicotine+ topic maintainer on [ubuntuforums.org](https://ubuntuforums.org/showthread.php?t=196835)

### suser-guru
 - Suse Linux packager
 - Nicotine+ RPM's for Suse 9.1, 9.2, 9.3, 10.0, 10.1

### osiris
 - Handy-man
 - Documentation
 - Some GNU/Linux packaging
 - Nicotine+ on Win32
 - Author of Nicotine+ guide

### OffHand
 - Website and Trac maintenance
 - Tester
 - Nicotine+ dinosaur

### Mutnick
 - Created Nicotine+ GitHub organization
 - Developer

### Lene Preuss
 - Python 3 migration
 - Unit and DEP-8 continuous integration testing


## Nicotine Team (Emeritus)

### Ingmar K. Steen (Hyriand)
 - Maintainer (2003–2004)

### daelstorm
 - Beta tester
 - Designer of most of the settings
 - Made the Nicotine icons

### SmackleFunky
 - Beta tester

### Wretched
 - Beta tester
 - Bringer of great ideas

### (va)\*10^3
 - Beta tester
 - Designer of Nicotine homepage and artwork (logos)

### sierracat
 - MacOSX tester
 - soulseeX developer

### Gustavo J. A. M. Carneiro
 - Created the exception dialog

### SeeSchloss
 - Developer
 - Created 1.0.8 Win32 installer
 - Created [Soulfind](https://github.com/soulfind-dev/soulfind), open source Soulseek server written in D

### vasi
 - Mac developer
 - Packaged Nicotine on OSX PowerPC


## PySoulSeek Team (Emeritus)

### Alexander Kanavin
 - Maintainer (2001–2003)

### Nir Arbel
 - Helped with many protocol questions, and of course he designed and implemented the whole system

### Brett W. Thompson (Zip)
 - His client code was used to get an initial impression of how the system works
 - Supplied the patch for logging chat conversations

### Josselin Mouette
 - Official Debian package maintainer

### blueboy
 - Former unofficial Debian package maintainer

### Christian Swinehart
 - Fink package maintainer

### Ingmar K. Steen (Hyriand)
 - Patches for upload bandwidth management, banning, various UI improvements and more

### Geert Kloosterman
 - A script for importing Windows Soulseek configuration

### Joe Halliwell
 - Submitted a patch for optionally discarding search
   results after closing a search tab

### Alexey Vyskubov
 - Code cleanups

### Jason Green (SmackleFunky)
 - Ignore list and auto-join checkbox, wishlists


## Translators

### Albanian
 - W L (2023–2024)

### Arabic
 - ButterflyOfFire (2024)

### Catalan
 - Aniol (2024–2025)
 - Maite Guix (2022)

### Chinese (Simplified)
 - Ys413 (2024)
 - Bonislaw (2023)
 - hylau (2023)
 - hadwin (2022)

### Czech
 - slrslr (2024–2025)
 - burnmail123 (2021–2023)

### Danish
 - mathsped (2003–2004)

### Dutch
 - Toine Rademacher (toineenzo) (2023–2024)
 - Han Boetes (hboetes) (2021–2024)
 - Kenny Verstraete (2009)
 - nince78 (2007)
 - Ingmar K. Steen (Hyriand) (2003–2004)

### English
 - slook (2021–2024)
 - Han Boetes (hboetes) (2021–2024)
 - Mat (mathiascode) (2020–2024)
 - Michael Labouebe (gfarmerfr) (2016)
 - daelstorm (2004–2009)
 - Ingmar K. Steen (Hyriand) (2003–2004)

### Esperanto
 - phlostically (2021)

### Estonian
 - rimasx (2024)
 - PriitUring (2023)

### Euskara
 - Julen (2006–2007)

### Finnish
 - Kari Viittanen (Kalevi) (2006–2007)

### French
 - Saumon (2023)
 - subu_versus (2023)
 - zniavre (2007–2023)
 - Maxime Leroy (Lisapple) (2021–2022)
 - Mohamed El Morabity (melmorabity) (2021–2024)
 - m-balthazar (2020)
 - Michael Labouebe (gfarmerfr) (2016–2017)
 - Monsieur Poisson (2009–2010)
 - ManWell (2007)
 - systr (2006)
 - Julien Wajsberg (flashfr) (2003–2004)

### German
 - Han Boetes (hboetes) (2021–2024)
 - phelissimo_ (2023)
 - Meokater (2007)
 - (._.) (2007)
 - lippel (2004)
 - Ingmar K. Steen (Hyriand) (2003–2004)

### Hungarian
 - Szia Tomi (2022–2024)
 - Nils (2009)
 - David Balazs (djbaloo) (2006–2020)

### Italian
 - Gabriele (Gabboxl) (2022–2023)
 - ms-afk (2023)
 - Gianluca Boiano (2020–2023)
 - nicola (2007)
 - dbazza (2003–2004)

### Latvian
 - Pagal3 (2022–2025)

### Lithuanian
 - mantas (2020)
 - Žygimantas Beručka (2006–2009)

### Norwegian Bokmål
 - Allan Nordhøy (comradekingu) (2021)

### Polish
 - Mariusz (mariachini) (2017–2024)
 - Amun-Ra (2007)
 - thine (2007)
 - Wojciech Owczarek (owczi) (2003–2004)

### Portuguese (Brazil)
 - Havokdan (2022–2024)
 - Guilherme Santos (2022)
 - b1llso (2022)
 - Nicolas Abril (2021)
 - yyyyyyyan (2020)
 - Felipe Nogaroto Gonzalez (Suicide\|Solution) (2006)

### Portuguese (Portugal)
 - ssantos (2023)
 - Vinícius Soares (2023)

### Romanian
 - Slendi (xslendix) (2023)

### Russian
 - Kirill Feoktistov (SnIPeRSnIPeR) (2022–2024)
 - Mehavoid (2021–2023)
 - AHOHNMYC (2022)

### Slovak
 - Jozef Říha (2006–2008)

### Spanish (Chile)
 - MELERIX (2021–2023)
 - tagomago (2021–2022)
 - Strange (2021)
 - Silvio Orta (2007)
 - Dreslo (2003–2004)

### Spanish (Spain)
 - gallegonovato (2023–2024)
 - MELERIX (2021–2023)
 - tagomago (2021–2022)
 - Strange (2021)
 - Silvio Orta (2007)
 - Dreslo (2003–2004)

### Swedish
 - mitramai (2021)
 - Markus Magnuson (alimony) (2003–2004)

### Tamil
 - தமிழ்நேரம் (2025)

### Turkish
 - Oğuz Ersen (2021–2024)

### Ukrainian
 - Oleg Gritsun (2024–2025)
 - uniss2209 (2022)


## Contributors

 - (._.)
 - Adam Cécile
 - AHOHNMYC
 - Airn Here
 - alekksander
 - Alexander Kanavin
 - Alexey Vyskubov
 - Allan Nordhøy
 - Amun-Ra
 - Aniol
 - Artem
 - AtticFinder65536
 - Attila Fidan
 - Aubin Paul
 - Avery
 - b1llso
 - baloo79
 - bgo-eiu
 - blueboy
 - Bonislaw
 - boredcar
 - Boris Topalov
 - Brett W. Thompson
 - Bugmenot
 - burnmail123
 - ButterflyOfFire
 - Carlos Laviola
 - Cata
 - César Augusto do Nascimento
 - Chris McKenzie
 - Christian Swinehart
 - chz
 - Coda
 - cravings
 - cwpute
 - daelstorm
 - David Balazs
 - dbazza
 - Dreslo
 - dtalens
 - Elias Groß
 - Emily
 - Enr1X
 - Eryk Michalak
 - Everly
 - Felipe Nogaroto Gonzalez
 - Felix Hansen
 - fkobi
 - Gabriele
 - gahag
 - gallegonovato
 - gallows
 - Geert Kloosterman
 - gfarmerfr
 - Gianluca Boiano
 - Guilherme Santos
 - Gustavo J. A. M. Carneiro
 - hadwin
 - Han Boetes
 - Havokdan
 - hednod
 - Héliaz
 - homedirectory
 - hylau
 - ian andrew remsen
 - Ilya
 - infinito
 - Ingmar K. Steen
 - INMCM
 - Inso-m-niaC
 - Jackson Baber
 - Jason Green
 - jat
 - Jean-Baptiste ALLAIN
 - Jimmy
 - J. Lavoie
 - Joe Halliwell
 - Jordan Rodrigues
 - Jörn Weigend
 - jose alberto matos
 - Josep Anguera
 - Josselin Mouette
 - Jozef Říha
 - JP Dillingham
 - Juan Benites
 - Julen
 - Julian
 - Julien Wajsberg
 - Kari Viittanen
 - Kawasumi Ayako
 - Kenny Verstraete
 - ketacat
 - Kian-Meng Ang
 - Kip Warner
 - Kirill Feoktistov
 - Kylie McClain
 - lee8oi
 - Lene Preuss
 - Lev Gorodetskiy
 - lippel
 - Lucas Vieites
 - m-balthazar
 - Maite Guix
 - mantas
 - ManWell
 - marciozomb13
 - Mariusz
 - Markus Magnuson
 - Mat
 - mathsped
 - Matthaiks
 - Maxime Leroy
 - Mehavoid
 - MELERIX
 - MendelGusmao
 - Meokater
 - Michael Labouebe
 - Micke Nilsson
 - Miodrag Milić
 - mitramai
 - Mohamed El Morabity
 - monowii
 - Monsieur Poisson
 - ms-afk
 - Mutnick
 - mzf-guest
 - Nachtalb
 - nicola
 - Nicolas Abril
 - Nick Voronin
 - Nils
 - nince78
 - Nir Arbel
 - nyoooooooooooooooom
 - OffHand
 - Oğuz Ersen
 - Oleg Gritsun
 - Oliver Hattshire
 - osiris
 - Pagal3
 - Patrik
 - phelissimo_
 - phlostically
 - Piotr Strebski
 - Poesty Li
 - PriitUring
 - quinox
 - Ramsey Harrison
 - redactedscribe
 - rimasx
 - Saumon
 - SeeSchloss
 - sierracat
 - Sigrid Davis
 - Silvio Orta
 - Slendi
 - slook
 - slrslr
 - spongy
 - ssantos
 - Ștefan Talpalaru
 - stillbirth
 - Strange
 - subu_versus
 - suser-guru
 - systr
 - Szia Tomi
 - tagomago
 - thine
 - Timo Vanwynsberghe
 - Toine Rademacher
 - tsointsoin
 - (va)\*10^3
 - vasi
 - Vinícius Soares
 - Vladimir Sedach
 - Vladyslav Anisimov
 - wanderer
 - waxed
 - Wojciech Owczarek
 - Wretched
 - Wynfyd
 - W L
 - X Kowalsky
 - Ys413
 - yyyyyyyan
 - Ziabice
 - zniavre
 - ZTetriminos
 - Žygimantas Beručka
 - தமிழ்நேரம்


## License

Nicotine+ is licensed under the [GNU General Public License v3.0 or later](https://www.gnu.org/licenses/gpl-3.0.html),
with the following exceptions:

**[tinytag](https://github.com/tinytag/tinytag) licensed under the MIT License.**  
Copyright (c) 2014-2025 Tom Wallroth, Mat (mathiascode), et al.  
Copyright (c) 2020-2025 Nicotine+ Contributors

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

**[FlagKit](https://github.com/madebybowtie/FlagKit) icons licensed under the MIT
License.**  
Copyright (c) 2016 Bowtie AB  

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

**IP2Location country data licensed under the [CC-BY-SA-4.0 License](https://creativecommons.org/licenses/by-sa/4.0/).**  
Copyright (c) 2001–2024 Hexasoft Development Sdn. Bhd.  
Nicotine+ uses the IP2Location LITE database for [IP geolocation](https://lite.ip2location.com).
