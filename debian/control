Source: nicotine
Section: net
Priority: optional
Maintainer: Nicotine+ Team <<EMAIL>>
Standards-Version: 4.7.0
Build-Depends: debhelper-compat (= 10),
               devscripts,
               dh-python,
               gettext,
               gir1.2-gtk-4.0 (>= 4.6.9) | gir1.2-gtk-3.0 (>= 3.24.24),
               libgtk-4-bin (>= 4.8.0) | python3-pytest-xvfb (>= 2.0.0) | libgtk-3-bin (>= 3.24.24),
               lintian,
               python3-all,
               python3-gi,
               python3-pytest,
               python3-setuptools
Vcs-Git: https://github.com/nicotine-plus/nicotine-plus.git
Vcs-Browser: https://github.com/nicotine-plus/nicotine-plus
X-Python3-Version: >= 3.9
Homepage: https://nicotine-plus.org
Rules-Requires-Root: no

Package: nicotine
Architecture: all
Depends: ${python3:Depends},
         ${misc:Depends},
         gir1.2-gtk-4.0 (>= 4.6.9) | gir1.2-gtk-3.0 (>= 3.24.24),
         python3-gi,
         python3-gi-cairo
Recommends: gir1.2-gspell-1
Description: graphical client for Soulseek peer-to-peer network
 Nicotine+ is a graphical client for the Soulseek peer-to-peer
 network.
 .
 Nicotine+ aims to be a lightweight, pleasant, free and open
 source (FOSS) alternative to the official Soulseek client, while
 also providing a comprehensive set of features.

