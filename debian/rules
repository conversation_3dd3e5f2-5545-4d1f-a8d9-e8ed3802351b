#! /usr/bin/make -f
# SPDX-FileCopyrightText: 2021-2025 <PERSON><PERSON>+ Contributors
# SPDX-FileCopyrightText: 2016 Kip Warner
# SPDX-License-Identifier: GPL-3.0-or-later

# Output every command that modifies files on the build system
export DH_VERBOSE=1
export DH_OPTIONS=-v

# Disable tests requiring internet connection
export PYBUILD_TEST_ARGS=-k "not test_update_check"

%:
	dh $@ --with python3 --buildsystem=pybuild

# Enforce gzip compression format for increased compatibility with older systems
override_dh_builddeb:
	dh_builddeb -- -Zgzip
