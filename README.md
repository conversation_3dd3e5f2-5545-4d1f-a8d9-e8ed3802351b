<!--
  SPDX-FileCopyrightText: 2013-2025 <PERSON><PERSON>+ Contributors
  SPDX-License-Identifier: GPL-3.0-or-later
-->

# <PERSON><PERSON>+

<img src="data/icons/icon.svg" alt="<PERSON><PERSON>+ Logo" align="right"
 width="128" height="128">

<PERSON><PERSON>+ is a graphical client for the [Soulseek](https://www.slsknet.org/news/)
peer-to-peer network.

Nicotine+ aims to be a lightweight, pleasant, free and open source (FOSS)
alternative to the official Soulseek client, while also providing a
comprehensive set of features.

Nicotine+ is written in Python and uses GTK for its graphical user interface.

Check out the [screenshots](data/screenshots/SCREENSHOTS.md)
and [source code](https://github.com/nicotine-plus/nicotine-plus).


## Download

The current stable version of Nicotine+ is 3.3.10, released on March 10, 2025.
See the [release notes](NEWS.md).

Downloads are available for:

 - [GNU/Linux, *BSD, Haiku and Solaris](doc/DOWNLOADS.md#gnulinux-bsd-haiku-solaris)
 - [Windows](doc/DOWNLOADS.md#windows)
 - [macOS](doc/DOWNLOADS.md#macos)


## Get Involved

If you feel like contributing to Nicotine+, there are several ways to get
involved:

 - [Issue Tracker](https://github.com/nicotine-plus/nicotine-plus/issues)
     – Report a problem or suggest improvements
 - [Testing](doc/TESTING.md)
     – Download the latest unstable build and help test Nicotine+
 - [Translations](doc/TRANSLATIONS.md)
     – Translate Nicotine+ into another language with [Weblate](https://hosted.weblate.org/engage/nicotine-plus)
 - [Packaging](doc/PACKAGING.md)
     – Package Nicotine+ for a distribution or operating system
 - [Development](doc/DEVELOPING.md)
     – Implement bug fixes, enhancements or new features
 - [IRC Channel](https://web.libera.chat/?channel=#nicotine+)
     – Chat in the #nicotine+ IRC channel on [Libera.Chat](https://libera.chat/)


## Where did the name Nicotine come from?

> I was in a geeky mood and was browsing bash.org's QDB.  
I stumbled across this quote:  
>> **\<etc>** so tempting to release a product called 'nicotine' and wait for
>> the patches.  
>> **\<etc>** then i would have a reason to only apply one patch a day.
>> otherwise, i'm going against medical advise.  
>
> So I thought what the hell and bluntly stole etc's idea.

— <cite>Hyriand, former Nicotine maintainer, 2003</cite>


## Legal and Privacy

The Nicotine+ Team does not collect any data used or stored by the client.
Different policies may apply for data sent to the default Soulseek server,
which is not operated by the Nicotine+ Team.

When connecting to the default Soulseek server, you agree to abide by the
Soulseek [rules](https://www.slsknet.org/news/node/681) and
[terms of service](https://www.slsknet.org/news/node/682).

Soulseek is an unencrypted protocol not intended for secure communication.


## Authors

Nicotine+ is free and open source software, released under the terms of the
[GNU General Public License v3.0 or later](https://www.gnu.org/licenses/gpl-3.0-standalone.html).
Nicotine+ exists thanks to its [authors](AUTHORS.md).

© 2001–2025 Nicotine+, Nicotine and PySoulSeek Contributors
