#!/bin/bash
cd "Nicotine Plus Plus.app/Contents/Resources/nicotine-plus-plus"
export PKG_CONFIG_PATH="/opt/homebrew/lib/pkgconfig:/opt/homebrew/share/pkgconfig:$PKG_CONFIG_PATH"
export PYTHONPATH="/opt/homebrew/lib/python3.12/site-packages:$PYTHONPATH"
export DYLD_LIBRARY_PATH="/opt/homebrew/lib:$DYLD_LIBRARY_PATH"
export GI_TYPELIB_PATH="/opt/homebrew/lib/girepository-1.0:$GI_TYPELIB_PATH"
export NICOTINE_GTK_VERSION=3
export DISPLAY=:0
python3 -m pynicotine
